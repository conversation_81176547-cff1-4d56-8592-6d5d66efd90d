"""
RAG系统配置文件
"""

from pathlib import Path

# 基础路径配置
BASE_DIR = Path(__file__).parent.parent
MODELS_DIR = BASE_DIR / "models"
PROCESSED_DATA_DIR = BASE_DIR / "ProcressedData"
INNOVATIONS_DIR = PROCESSED_DATA_DIR / "innovations"
CODE_ANALYSIS_DIR = PROCESSED_DATA_DIR / "code_analysis"
RAG_RESULTS_DIR = PROCESSED_DATA_DIR / "rag_results"

# 模型配置
MODEL_CONFIG = {
    # Qwen3嵌入模型配置
    'embedding_model': {
        'model_sizes': ['0.6B', '4B'],
        'default_size': '0.6B',
        'model_path_template': MODELS_DIR / "Qwen3_Embedding" / "Qwen3-Embedding-{size}",
        'batch_size': 32,
        'max_length': 512,
        'normalize_embeddings': True
    },
    
    # 交叉编码器配置
    'cross_encoder': {
        'use_by_default': True,
        'batch_size': 16,
        'max_length': 512,
        'score_threshold': 0.5
    }
}

# 向量数据库配置
VECTOR_DB_CONFIG = {
    'index_types': ['flat', 'ivf', 'hnsw'],
    'default_index_type': 'flat',
    'save_path': PROCESSED_DATA_DIR / "vector_db",
    
    # FAISS索引参数
    'faiss_params': {
        'ivf': {
            'nlist': 100,  # 聚类中心数量
            'nprobe': 10   # 搜索的聚类数量
        },
        'hnsw': {
            'M': 32,           # 连接数
            'efConstruction': 200,  # 构建时的搜索深度
            'efSearch': 100    # 搜索时的深度
        }
    }
}

# 检索配置
RETRIEVAL_CONFIG = {
    # 召回阶段
    'recall': {
        'top_k': 50,
        'similarity_threshold': 0.1
    },
    
    # 重排阶段
    'rerank': {
        'top_k': 10,
        'use_cross_encoder': True,
        'score_combination': {
            'recall_weight': 0.3,
            'rerank_weight': 0.7
        }
    },
    
    # 匹配配置
    'matching': {
        'confidence_threshold': 0.3,
        'max_matches_per_innovation': 20,
        'element_type_weights': {
            'Class': 1.2,
            'Function': 1.0,
            'Module': 0.9
        }
    }
}

# 创新点类型配置
INNOVATION_CONFIG = {
    'type_filters': {
        'architecture': {
            'preferred_elements': ['Class'],
            'boost_keywords': ['network', 'model', 'architecture', 'layer', 'module'],
            'weight_multiplier': 1.2
        },
        'technique': {
            'preferred_elements': ['Class', 'Function'],
            'boost_keywords': ['algorithm', 'method', 'technique', 'approach'],
            'weight_multiplier': 1.1
        },
        'method': {
            'preferred_elements': ['Function'],
            'boost_keywords': ['process', 'procedure', 'strategy', 'optimization'],
            'weight_multiplier': 1.0
        }
    }
}

# 输出配置
OUTPUT_CONFIG = {
    'results_dir': RAG_RESULTS_DIR,
    'file_names': {
        'matches': 'innovation_code_matches.json',
        'report': 'match_report.txt',
        'statistics': 'system_statistics.json',
        'detailed_matches': 'detailed_matches.json'
    },
    'report_format': {
        'max_matches_shown': 5,
        'include_code_snippets': True,
        'max_snippet_length': 200
    }
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'handlers': {
        'console': True,
        'file': True,
        'file_path': BASE_DIR / 'logs' / 'rag_system.log'
    }
}

# 性能配置
PERFORMANCE_CONFIG = {
    'embedding': {
        'batch_size': 32,
        'max_workers': 4,
        'use_gpu': True,
        'gpu_memory_fraction': 0.8
    },
    'retrieval': {
        'cache_embeddings': True,
        'cache_size': 1000,
        'parallel_search': False
    }
}

# 验证配置
VALIDATION_CONFIG = {
    'min_confidence_threshold': 0.1,
    'max_confidence_threshold': 0.9,
    'min_recall_top_k': 10,
    'max_recall_top_k': 200,
    'min_rerank_top_k': 5,
    'max_rerank_top_k': 50
}

def get_model_path(model_size: str = "0.6B") -> Path:
    """获取模型路径"""
    template = MODEL_CONFIG['embedding_model']['model_path_template']
    return Path(str(template).format(size=model_size))

def validate_config():
    """验证配置"""
    errors = []
    
    # 检查模型路径
    for size in MODEL_CONFIG['embedding_model']['model_sizes']:
        model_path = get_model_path(size)
        if not model_path.exists():
            errors.append(f"Model path does not exist: {model_path}")
    
    # 检查数据目录
    required_dirs = [INNOVATIONS_DIR, CODE_ANALYSIS_DIR]
    for dir_path in required_dirs:
        if not dir_path.exists():
            errors.append(f"Required directory does not exist: {dir_path}")
    
    # 检查配置值范围
    recall_top_k = RETRIEVAL_CONFIG['recall']['top_k']
    if not (VALIDATION_CONFIG['min_recall_top_k'] <= recall_top_k <= VALIDATION_CONFIG['max_recall_top_k']):
        errors.append(f"recall_top_k out of range: {recall_top_k}")
    
    rerank_top_k = RETRIEVAL_CONFIG['rerank']['top_k']
    if not (VALIDATION_CONFIG['min_rerank_top_k'] <= rerank_top_k <= VALIDATION_CONFIG['max_rerank_top_k']):
        errors.append(f"rerank_top_k out of range: {rerank_top_k}")
    
    confidence_threshold = RETRIEVAL_CONFIG['matching']['confidence_threshold']
    if not (VALIDATION_CONFIG['min_confidence_threshold'] <= confidence_threshold <= VALIDATION_CONFIG['max_confidence_threshold']):
        errors.append(f"confidence_threshold out of range: {confidence_threshold}")
    
    return errors

def setup_directories():
    """创建必要的目录"""
    dirs_to_create = [
        RAG_RESULTS_DIR,
        VECTOR_DB_CONFIG['save_path'],
        LOGGING_CONFIG['handlers']['file_path'].parent
    ]
    
    for dir_path in dirs_to_create:
        dir_path.mkdir(parents=True, exist_ok=True)

# 初始化时验证配置
if __name__ == "__main__":
    setup_directories()
    errors = validate_config()
    if errors:
        print("Configuration errors found:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("Configuration validation passed")
else:
    setup_directories()
