"""
简化版RAG系统演示
不使用交叉编码器，只使用向量召回
"""

import os
import sys
import json
import logging
from pathlib import Path
import time
from typing import Dict, Any

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from RAGSystem import InnovationRAGSystem
from RAGSystem.embedding_model import CrossEncoder

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def analyze_score_composition(matches: Dict[str, Any]):
    """
    分析匹配结果中分数的组成

    Args:
        matches: 匹配结果字典
    """
    print("分数组成分析 (最终分数 = 0.3 × 向量分数 + 0.7 × 交叉编码器分数):")
    print("-" * 80)

    for _, match_data in matches.items():
        innovation = match_data['innovation']
        matched_code = match_data['matched_code']

        if not matched_code:
            continue

        print(f"\n📋 {innovation.get('title', 'Unknown')}")
        print(f"{'排名':<4} {'元素名称':<25} {'向量分数':<10} {'交叉分数':<10} {'最终分数':<10}")
        print("-" * 70)

        for i, code in enumerate(matched_code[:5], 1):  # 显示前5个
            metadata = code['metadata']
            final_score = code.get('final_score', code.get('score', 0))
            vector_score = code.get('score', 0)
            rerank_score = code.get('rerank_score', None)

            element_name = metadata.get('element_name', 'Unknown')[:24]

            if rerank_score is not None:
                print(f"{i:<4} {element_name:<25} {vector_score:<10.4f} {rerank_score:<10.4f} {final_score:<10.4f}")
            else:
                print(f"{i:<4} {element_name:<25} {vector_score:<10.4f} {'N/A':<10} {final_score:<10.4f}")

        if matched_code:
            # 统计分析
            vector_scores = [code.get('score', 0) for code in matched_code]
            rerank_scores = [code.get('rerank_score', 0) for code in matched_code if code.get('rerank_score') is not None]
            final_scores = [code.get('final_score', code.get('score', 0)) for code in matched_code]

            print(f"\n统计信息:")
            print(f"  向量分数范围: {min(vector_scores):.4f} - {max(vector_scores):.4f}")
            if rerank_scores:
                print(f"  交叉分数范围: {min(rerank_scores):.4f} - {max(rerank_scores):.4f}")
            print(f"  最终分数范围: {min(final_scores):.4f} - {max(final_scores):.4f}")

            # 分析交叉编码器的影响
            if rerank_scores:
                vector_contribution = [0.3 * v for v in vector_scores[:len(rerank_scores)]]
                cross_contribution = [0.7 * r for r in rerank_scores]

                avg_vector_contrib = sum(vector_contribution) / len(vector_contribution)
                avg_cross_contrib = sum(cross_contribution) / len(cross_contribution)

                print(f"  平均向量贡献: {avg_vector_contrib:.4f} (30%)")
                print(f"  平均交叉贡献: {avg_cross_contrib:.4f} (70%)")

                if avg_cross_contrib > avg_vector_contrib:
                    print(f"  ✅ 交叉编码器起主导作用")
                else:
                    print(f"  ⚠️  向量检索起主导作用")


def create_cross_encoder(model_type: str = "qwen3-0.6B") -> CrossEncoder:
    """
    创建交叉编码器

    Args:
        model_type: 模型类型 ("qwen3-0.6B", "qwen3-4B", "ms-marco")

    Returns:
        配置好的CrossEncoder实例
    """
    logger.info(f"创建{model_type}交叉编码器...")
    try:
        cross_encoder = CrossEncoder(model_type=model_type)
        logger.info(f"{model_type}交叉编码器创建成功")
        return cross_encoder
    except Exception as e:
        logger.error(f"创建{model_type}交叉编码器失败: {e}")
        if model_type != "ms-marco":
            logger.info("回退到MS-MARCO交叉编码器...")
            return create_cross_encoder("ms-marco")
        else:
            logger.error("所有交叉编码器都失败了")
            raise e


def run_simple_rag_demo():
    """运行简化版RAG演示"""
    logger.info("=" * 60)
    logger.info("开始运行简化版RAG系统演示")
    logger.info("=" * 60)
    
    start_time = time.time()
    
    try:
        # 创建RAG系统（不使用默认交叉编码器）
        logger.info("创建RAG系统...")
        rag_system = InnovationRAGSystem(
            model_size="4B",
            index_type="flat",
            use_cross_encoder=False,  # 先不使用交叉编码器，避免加载默认模型
            recall_top_k=50,#30
            rerank_top_k=20#15
        )

        # 设置完整系统（不包含交叉编码器）
        logger.info("设置完整系统...")
        rag_system.setup_complete_system("SANDE")

        # 创建指定的Qwen3-Reranker交叉编码器
        logger.info("创建Qwen3-Reranker-4B交叉编码器...")
        cross_encoder = create_cross_encoder("qwen3-4B")

        # 设置交叉编码器到检索器
        if rag_system.retriever:
            rag_system.retriever.cross_encoder = cross_encoder
            rag_system.use_cross_encoder = True  # 启用交叉编码器
            logger.info("已设置Qwen3-Reranker-4B交叉编码器")
        
        # 执行创新点匹配
        logger.info("执行创新点匹配...")
        matches = rag_system.match_innovations_to_code(confidence_threshold=0.15)
        
        # 生成报告
        logger.info("生成匹配报告...")
        report = rag_system.generate_comprehensive_report(matches)
        
        # 显示报告
        print("\n" + "="*80)
        print(report)
        print("="*80)

        # 显示详细的分数分析
        print("\n🔍 详细分数分析:")
        print("="*80)
        analyze_score_composition(matches)
        
        # 保存结果
        logger.info("保存结果...")
        saved_files = rag_system.save_results(matches)
        
        print(f"\n📁 结果文件:")
        for file_type, file_path in saved_files.items():
            print(f"  📄 {file_type}: {file_path}")
        
        # 保存向量数据库
        logger.info("保存向量数据库...")
        rag_system.save_vector_database()
        
        # 显示系统统计
        stats = rag_system.get_system_statistics()
        print(f"\n📊 系统统计:")
        print(f"  模型大小: {stats['system_config']['model_size']}")
        print(f"  索引类型: {stats['system_config']['index_type']}")
        print(f"  使用交叉编码器: {stats['system_config']['use_cross_encoder']}")
        
        if 'retrieval_stats' in stats:
            db_stats = stats['retrieval_stats']['database_stats']
            print(f"  数据库文档数: {db_stats['total_documents']}")
            print(f"  嵌入维度: {db_stats['embedding_dim']}")
        
        # 演示查询功能
        print(f"\n🔍 代码检索演示:")
        print("-" * 50)

        #预定义的演示查询
        # demo_queries = [
        #     "graph convolutional network",
        #     "biaffine attention mechanism",
        #     "sentiment analysis model",
        #     "aspect extraction method",
        #     "opinion mining algorithm",
        #     "multi-channel convolution",
        #     "word pair representation",
        #     "neural network architecture"
        # ]

        # print(f"使用{len(demo_queries)}个预定义查询进行演示\n")

        # for i, query in enumerate(demo_queries, 1):
        #     print(f"\n查询 {i}: {query}")
        #     results = rag_system.query_code(query, top_k=3)
            
        #     if results:
        #         for j, result in enumerate(results, 1):
        #             metadata = result['metadata']
        #             score = result.get('score', 0)
                    
        #             element_name = metadata.get('element_name', 'Unknown')
        #             element_type = metadata.get('element_type', 'Unknown')
        #             file_path = metadata.get('file_path', 'Unknown')
                    
        #             print(f"  {j}. {element_name} ({element_type})")
        #             print(f"     文件: {os.path.basename(file_path) if file_path != 'Unknown' else 'Unknown'}")
        #             print(f"     相似度: {score:.3f}")
        #     else:
        #         print("  未找到相关结果")
        
        # elapsed_time = time.time() - start_time
        # logger.info(f"RAG系统运行完成，耗时: {elapsed_time:.2f}秒")
        
        return True
        
    except Exception as e:
        logger.error(f"RAG系统运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_innovation_matches(matches):
    """分析创新点匹配结果"""
    print(f"\n📈 匹配结果分析:")
    print("-" * 50)
    
    total_innovations = len(matches)
    total_matches = sum(match['match_count'] for match in matches.values())
    
    print(f"总创新点数: {total_innovations}")
    print(f"总匹配数: {total_matches}")
    print(f"平均每个创新点匹配数: {total_matches/total_innovations:.1f}")
    
    # 按匹配数排序
    sorted_matches = sorted(matches.items(), 
                           key=lambda x: x[1]['match_count'], 
                           reverse=True)
    
    print(f"\n匹配数排序:")
    for _, match_data in sorted_matches:
        innovation = match_data['innovation']
        title = innovation.get('title', 'Unknown')
        match_count = match_data['match_count']
        avg_confidence = match_data['avg_confidence']

        print(f"  • {title[:50]}...")
        print(f"    匹配数: {match_count}, 平均置信度: {avg_confidence:.3f}")
    
    # 文件分布分析
    file_matches = {}
    for match_data in matches.values():
        for code_match in match_data['matched_code']:
            file_path = code_match['metadata'].get('file_path', 'Unknown')
            file_name = os.path.basename(file_path) if file_path != 'Unknown' else 'Unknown'
            file_matches[file_name] = file_matches.get(file_name, 0) + 1
    
    print(f"\n📁 文件匹配分布:")
    for file_name, count in sorted(file_matches.items(), key=lambda x: x[1], reverse=True):
        print(f"  {file_name}: {count} 个匹配")


def main():
    """主函数"""
    print("🚀 InnovativeIdentifier 增强版RAG系统演示")
    # print("🤖 使用Qwen3-Reranker-0.6B交叉编码器")
    print("=" * 60)
    
    # 检查前提条件
    base_dir = Path(__file__).parent.parent
    
    # 检查模型
    model_path = base_dir / "models" / "Qwen3_Embedding" / "Qwen3-Embedding-4B"
    if not model_path.exists():
        print(f"❌ 嵌入模型不存在: {model_path}")
        return
    
    # 检查创新点数据
    innovations_file = base_dir / "ProcressedData" / "innovations" / "SANDE_with_descriptions_innovations_innovations.json"
    if not innovations_file.exists():
        print(f"❌ 创新点数据不存在: {innovations_file}")
        return
    
    # 检查代码分析结果
    code_analysis_file = base_dir / "ProcressedData" / "code_analysis" / "SANDE" / "rag_chunks.json"
    if not code_analysis_file.exists():
        print(f"❌ 代码分析结果不存在: {code_analysis_file}")
        print("请先运行CodeProcessor模块生成代码分析结果")
        return
    
    print("✅ 前提条件检查通过")
    
    # 运行演示
    success = run_simple_rag_demo()
    
    if success:
        print("\n🎉 RAG系统演示完成！")
        print("📁 查看结果文件:")
        results_dir = base_dir / "ProcressedData" / "rag_results"
        print(f"   - 结果目录: {results_dir}")
        
        # 如果有匹配结果，进行分析
        matches_file = results_dir / "innovation_code_matches.json"
        if matches_file.exists():
            try:
                with open(matches_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    matches = data.get('matches', {})
                    if matches:
                        analyze_innovation_matches(matches)
            except Exception as e:
                print(f"分析匹配结果时出错: {e}")
    else:
        print("\n😞 RAG系统演示失败")


if __name__ == "__main__":
    main()
