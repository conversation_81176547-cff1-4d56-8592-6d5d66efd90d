"""
向量数据库模块
用于存储和检索代码块的向量表示
"""

import os
import json
import pickle
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
import faiss
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class VectorDatabase:
    """向量数据库类"""
    
    def __init__(self, embedding_dim: int, index_type: str = "flat"):
        """
        初始化向量数据库
        
        Args:
            embedding_dim: 嵌入向量维度
            index_type: 索引类型 ("flat", "ivf", "hnsw")
        """
        self.embedding_dim = embedding_dim
        self.index_type = index_type
        
        # 创建FAISS索引
        self.index = self._create_index()
        
        # 存储文档元数据
        self.documents = []  # 存储原始文档
        self.metadata = []   # 存储元数据
        self.id_to_idx = {}  # ID到索引的映射
        
        logger.info(f"Initialized VectorDatabase with {index_type} index, dim={embedding_dim}")
    
    def _create_index(self) -> faiss.Index:
        """创建FAISS索引"""
        if self.index_type == "flat":
            # 精确搜索，适合小规模数据
            index = faiss.IndexFlatIP(self.embedding_dim)  # 内积相似度
        elif self.index_type == "ivf":
            # 倒排文件索引，适合中等规模数据
            nlist = 100  # 聚类中心数量
            quantizer = faiss.IndexFlatIP(self.embedding_dim)
            index = faiss.IndexIVFFlat(quantizer, self.embedding_dim, nlist)
        elif self.index_type == "hnsw":
            # 分层导航小世界图，适合大规模数据
            index = faiss.IndexHNSWFlat(self.embedding_dim, 32)
            index.hnsw.efConstruction = 200
            index.hnsw.efSearch = 100
        else:
            raise ValueError(f"Unsupported index type: {self.index_type}")
        
        return index
    
    def add_documents(self, documents: List[str], 
                     embeddings: np.ndarray,
                     metadata: List[Dict[str, Any]] = None,
                     doc_ids: List[str] = None):
        """
        添加文档到数据库
        
        Args:
            documents: 文档文本列表
            embeddings: 文档嵌入向量
            metadata: 文档元数据列表
            doc_ids: 文档ID列表
        """
        if len(documents) != len(embeddings):
            raise ValueError("Documents and embeddings must have the same length")
        
        if metadata is None:
            metadata = [{}] * len(documents)
        
        if doc_ids is None:
            doc_ids = [f"doc_{len(self.documents) + i}" for i in range(len(documents))]
        
        # 确保嵌入向量是归一化的（用于内积相似度）
        embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)
        
        # 添加到索引
        start_idx = len(self.documents)
        self.index.add(embeddings.astype(np.float32))
        
        # 存储文档和元数据
        for i, (doc, meta, doc_id) in enumerate(zip(documents, metadata, doc_ids)):
            self.documents.append(doc)
            self.metadata.append(meta)
            self.id_to_idx[doc_id] = start_idx + i
        
        # 如果是IVF索引，需要训练
        if self.index_type == "ivf" and not self.index.is_trained:
            if len(self.documents) >= 100:  # 需要足够的数据进行训练
                all_embeddings = np.vstack([embeddings])
                self.index.train(all_embeddings.astype(np.float32))
        
        logger.info(f"Added {len(documents)} documents to database. Total: {len(self.documents)}")
    
    def search(self, query_embedding: np.ndarray, 
               top_k: int = 10,
               filter_metadata: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        搜索相似文档
        
        Args:
            query_embedding: 查询向量
            top_k: 返回的文档数量
            filter_metadata: 元数据过滤条件
            
        Returns:
            搜索结果列表
        """
        # 归一化查询向量
        query_embedding = query_embedding / np.linalg.norm(query_embedding, keepdims=True)
        query_embedding = query_embedding.astype(np.float32)
        
        # 搜索
        scores, indices = self.index.search(query_embedding.reshape(1, -1), top_k)
        
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx == -1:  # FAISS返回-1表示没有找到足够的结果
                break
            
            doc = self.documents[idx]
            meta = self.metadata[idx].copy()
            
            # 应用元数据过滤
            if filter_metadata:
                if not self._match_filter(meta, filter_metadata):
                    continue
            
            result = {
                'document': doc,
                'score': float(score),
                'metadata': meta,
                'index': idx
            }
            results.append(result)
        
        return results
    
    def _match_filter(self, metadata: Dict[str, Any], 
                     filter_conditions: Dict[str, Any]) -> bool:
        """检查元数据是否匹配过滤条件"""
        for key, value in filter_conditions.items():
            if key not in metadata:
                return False
            if isinstance(value, list):
                if metadata[key] not in value:
                    return False
            else:
                if metadata[key] != value:
                    return False
        return True
    
    def get_document_by_id(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取文档"""
        if doc_id not in self.id_to_idx:
            return None
        
        idx = self.id_to_idx[doc_id]
        return {
            'document': self.documents[idx],
            'metadata': self.metadata[idx],
            'index': idx
        }
    
    def get_all_documents(self) -> List[Dict[str, Any]]:
        """获取所有文档"""
        results = []
        for i, (doc, meta) in enumerate(zip(self.documents, self.metadata)):
            results.append({
                'document': doc,
                'metadata': meta,
                'index': i
            })
        return results
    
    def save(self, save_path: str):
        """保存数据库到文件"""
        save_path = Path(save_path)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # 保存FAISS索引
        faiss.write_index(self.index, str(save_path / "index.faiss"))
        
        # 保存文档和元数据
        with open(save_path / "documents.json", 'w', encoding='utf-8') as f:
            json.dump({
                'documents': self.documents,
                'metadata': self.metadata,
                'id_to_idx': self.id_to_idx,
                'embedding_dim': self.embedding_dim,
                'index_type': self.index_type
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Database saved to {save_path}")
    
    def load(self, load_path: str):
        """从文件加载数据库"""
        load_path = Path(load_path)
        
        if not load_path.exists():
            raise ValueError(f"Database path does not exist: {load_path}")
        
        # 加载FAISS索引
        self.index = faiss.read_index(str(load_path / "index.faiss"))
        
        # 加载文档和元数据
        with open(load_path / "documents.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
            self.documents = data['documents']
            self.metadata = data['metadata']
            self.id_to_idx = data['id_to_idx']
            self.embedding_dim = data['embedding_dim']
            self.index_type = data['index_type']
        
        logger.info(f"Database loaded from {load_path}. Total documents: {len(self.documents)}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        stats = {
            'total_documents': len(self.documents),
            'embedding_dim': self.embedding_dim,
            'index_type': self.index_type,
            'index_size': self.index.ntotal
        }
        
        # 统计元数据类型
        if self.metadata:
            metadata_types = {}
            for meta in self.metadata:
                for key, value in meta.items():
                    if key not in metadata_types:
                        metadata_types[key] = set()
                    metadata_types[key].add(type(value).__name__)
            
            stats['metadata_fields'] = {k: list(v) for k, v in metadata_types.items()}
        
        return stats


class CodeVectorDatabase(VectorDatabase):
    """专门用于代码的向量数据库"""
    
    def __init__(self, embedding_dim: int, index_type: str = "flat"):
        super().__init__(embedding_dim, index_type)
    
    def add_code_chunks(self, code_chunks: List[Dict[str, Any]], 
                       embeddings: np.ndarray):
        """
        添加代码块到数据库
        
        Args:
            code_chunks: 代码块列表，每个包含content和metadata
            embeddings: 代码块嵌入向量
        """
        documents = []
        metadata = []
        doc_ids = []
        
        for chunk in code_chunks:
            documents.append(chunk['content'])
            
            # 增强元数据
            meta = chunk.get('metadata', {}).copy()
            meta.update({
                'chunk_type': chunk.get('chunk_type', 'unknown'),
                'chunk_id': chunk.get('id', ''),
            })
            metadata.append(meta)
            doc_ids.append(chunk.get('id', f"chunk_{len(documents)}"))
        
        self.add_documents(documents, embeddings, metadata, doc_ids)
    
    def search_by_element_type(self, query_embedding: np.ndarray,
                              element_type: str,
                              top_k: int = 10) -> List[Dict[str, Any]]:
        """根据代码元素类型搜索"""
        filter_conditions = {'element_type': element_type}
        return self.search(query_embedding, top_k, filter_conditions)

    def search_exclude_variables(self, query_embedding: np.ndarray,
                                top_k: int = 10) -> List[Dict[str, Any]]:
        """搜索时排除Variable类型的元素"""
        all_results = self.search(query_embedding, top_k * 2)  # 获取更多结果进行过滤

        # 过滤掉Variable类型
        filtered_results = []
        for result in all_results:
            element_type = result['metadata'].get('element_type', '')
            if element_type != 'Variable':
                filtered_results.append(result)
                if len(filtered_results) >= top_k:
                    break

        return filtered_results
    
    def search_by_file(self, query_embedding: np.ndarray,
                      file_path: str,
                      top_k: int = 10) -> List[Dict[str, Any]]:
        """根据文件路径搜索"""
        results = self.search(query_embedding, top_k * 2)  # 获取更多结果进行过滤
        
        # 过滤指定文件的结果
        filtered_results = []
        for result in results:
            if file_path in result['metadata'].get('file_path', ''):
                filtered_results.append(result)
                if len(filtered_results) >= top_k:
                    break
        
        return filtered_results
    
    def get_code_statistics(self) -> Dict[str, Any]:
        """获取代码相关的统计信息"""
        stats = self.get_statistics()
        
        # 统计代码元素类型
        element_types = {}
        chunk_types = {}
        files = set()
        
        for meta in self.metadata:
            # 元素类型统计
            element_type = meta.get('element_type', 'unknown')
            element_types[element_type] = element_types.get(element_type, 0) + 1
            
            # 块类型统计
            chunk_type = meta.get('chunk_type', 'unknown')
            chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
            
            # 文件统计
            file_path = meta.get('file_path', '')
            if file_path:
                files.add(file_path)
        
        stats.update({
            'element_types': element_types,
            'chunk_types': chunk_types,
            'total_files': len(files),
            'files': list(files)
        })
        
        return stats


def test_vector_database():
    """测试向量数据库"""
    # 创建测试数据
    test_docs = [
        "This is a test document about machine learning.",
        "Another document about deep learning and neural networks.",
        "A document about graph convolutional networks.",
        "Text about natural language processing."
    ]
    
    # 创建随机嵌入向量（实际使用中应该用真实的嵌入模型）
    embedding_dim = 768
    embeddings = np.random.randn(len(test_docs), embedding_dim).astype(np.float32)
    
    # 创建数据库
    db = VectorDatabase(embedding_dim)
    
    # 添加文档
    metadata = [
        {'type': 'ml', 'category': 'general'},
        {'type': 'dl', 'category': 'neural'},
        {'type': 'gcn', 'category': 'graph'},
        {'type': 'nlp', 'category': 'language'}
    ]
    
    db.add_documents(test_docs, embeddings, metadata)
    
    # 测试搜索
    query_embedding = np.random.randn(1, embedding_dim).astype(np.float32)
    results = db.search(query_embedding, top_k=2)
    
    print(f"Search results: {len(results)}")
    for i, result in enumerate(results):
        print(f"Result {i+1}: score={result['score']:.4f}")
        print(f"Document: {result['document'][:50]}...")
        print(f"Metadata: {result['metadata']}")
        print()
    
    # 测试统计信息
    stats = db.get_statistics()
    print(f"Database statistics: {stats}")
    
    return True


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_vector_database()
