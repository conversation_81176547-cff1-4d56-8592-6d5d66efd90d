"""
RAG系统主模块
整合嵌入模型、向量数据库和检索器，提供完整的RAG功能
"""

import os
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import numpy as np

try:
    from .embedding_model import Qwen3EmbeddingModel, CrossEncoder, EmbeddingModel
    from .vector_database import CodeVectorDatabase
    from .retriever import TwoStageRetriever, InnovationCodeMatcher
except ImportError:
    # 当作为脚本直接运行时
    from embedding_model import Qwen3EmbeddingModel, CrossEncoder, EmbeddingModel
    from vector_database import CodeVectorDatabase
    from retriever import TwoStageRetriever, InnovationCodeMatcher

logger = logging.getLogger(__name__)


def create_cross_encoder(model_type: str = "qwen3-4B") -> CrossEncoder:
    """
    创建交叉编码器

    Args:
        model_type: 模型类型 ("qwen3-0.6B", "qwen3-4B", "ms-marco")

    Returns:
        配置好的CrossEncoder实例
    """
    logger.info(f"创建{model_type}交叉编码器...")
    try:
        cross_encoder = CrossEncoder(model_type=model_type)
        logger.info(f"{model_type}交叉编码器创建成功")
        return cross_encoder
    except Exception as e:
        logger.error(f"创建{model_type}交叉编码器失败: {e}")
        if model_type != "ms-marco":
            logger.info("回退到MS-MARCO交叉编码器...")
            return create_cross_encoder("ms-marco")
        else:
            logger.error("所有交叉编码器都失败了")
            raise e


class InnovationRAGSystem:
    """创新点RAG系统"""
    
    def __init__(self,
                 model_type: str = "local",
                 model_size: str = "0.6B",
                 dimension: int = 1024,
                 index_type: str = "flat",
                 use_cross_encoder: bool = True,
                 recall_top_k: int = 50,
                 rerank_top_k: int = 10):
        """
        初始化RAG系统

        Args:
            model_type: 模型类型 ("local" 使用本地模型, "api" 使用API)
            model_size: 嵌入模型大小 ("0.6B" 或 "4B")，仅在model_type="local"时使用
            dimension: 向量维度，仅在model_type="api"时使用
            index_type: 向量索引类型
            use_cross_encoder: 是否使用交叉编码器
            recall_top_k: 召回阶段返回数量
            rerank_top_k: 重排阶段返回数量
        """
        self.model_type = model_type
        self.model_size = model_size
        self.dimension = dimension
        self.index_type = index_type
        self.use_cross_encoder = use_cross_encoder
        self.recall_top_k = recall_top_k
        self.rerank_top_k = rerank_top_k
        
        # 初始化组件
        self.embedding_model = None
        self.cross_encoder = None
        self.vector_db = None
        self.retriever = None
        self.matcher = None
        
        # 数据路径
        self.base_dir = Path(__file__).parent.parent
        self.processed_data_dir = self.base_dir / "ProcressedData"
        self.innovations_dir = self.processed_data_dir / "innovations"
        self.code_analysis_dir = self.processed_data_dir / "code_analysis"
        
        logger.info(f"Initialized InnovationRAGSystem with model_type={model_type}, model_size={model_size}, dimension={dimension}")
    
    def initialize_models(self):
        """初始化模型"""
        logger.info(f"Initializing embedding model (type: {self.model_type})...")

        if self.model_type == "api":
            # 使用API模型
            self.embedding_model = EmbeddingModel(
                model_type="api",
                dimension=self.dimension
            )
            logger.info(f"API embedding model initialized with dimension {self.dimension}")
        elif self.model_type == "local":
            # 使用本地模型
            self.embedding_model = EmbeddingModel(
                model_type="local",
                model_size=self.model_size
            )
            logger.info(f"Local embedding model initialized with size {self.model_size}")
        else:
            raise ValueError(f"Unsupported model_type: {self.model_type}")

        if self.use_cross_encoder:
            logger.info("Initializing cross encoder...")
            # 根据模型大小选择交叉编码器
            if self.model_type == "local" and self.model_size == "4B":
                self.cross_encoder = create_cross_encoder("qwen3-4B")
            elif self.model_type == "local" and self.model_size == "0.6B":
                self.cross_encoder = create_cross_encoder("qwen3-0.6B")
            else:
                # API模式默认使用4B交叉编码器
                self.cross_encoder = create_cross_encoder("qwen3-4B")

        logger.info("Models initialized successfully")
    
    def build_vector_database(self, repo_name: str = "EMCGCN-ASTE"):
        """构建向量数据库"""
        logger.info(f"Building vector database for {repo_name}...")
        
        # 加载代码分析结果
        rag_chunks_path = self.code_analysis_dir / repo_name / "rag_chunks.json"
        
        if not rag_chunks_path.exists():
            raise FileNotFoundError(f"RAG chunks not found: {rag_chunks_path}")
        
        with open(rag_chunks_path, 'r', encoding='utf-8') as f:
            rag_data = json.load(f)
        
        chunks = rag_data['chunks']
        logger.info(f"Loaded {len(chunks)} code chunks")
        
        # 创建向量数据库
        embedding_dim = self.embedding_model.get_embedding_dim()
        self.vector_db = CodeVectorDatabase(embedding_dim, self.index_type)
        
        # 批量编码文档
        # API模式使用较小的批处理大小
        batch_size = 8 if self.model_type == "api" else 32
        all_embeddings = []

        for i in range(0, len(chunks), batch_size):
            batch_chunks = chunks[i:i + batch_size]
            batch_texts = [chunk['content'] for chunk in batch_chunks]

            batch_embeddings = self.embedding_model.encode(batch_texts)
            all_embeddings.append(batch_embeddings)

            logger.info(f"Encoded batch {i//batch_size + 1}/{(len(chunks) + batch_size - 1)//batch_size}")
        
        # 合并所有嵌入
        all_embeddings = np.vstack(all_embeddings)
        
        # 添加到向量数据库
        self.vector_db.add_code_chunks(chunks, all_embeddings)
        
        logger.info(f"Vector database built with {len(chunks)} documents")
    
    def initialize_retriever(self):
        """初始化检索器"""
        if self.embedding_model is None or self.vector_db is None:
            raise ValueError("Models and vector database must be initialized first")
        
        logger.info("Initializing retriever...")
        self.retriever = TwoStageRetriever(
            embedding_model=self.embedding_model,
            vector_db=self.vector_db,
            cross_encoder=self.cross_encoder,
            recall_top_k=self.recall_top_k,
            rerank_top_k=self.rerank_top_k
        )
        
        self.matcher = InnovationCodeMatcher(self.retriever)
        logger.info("Retriever initialized successfully")
    
    def load_innovations(self) -> List[Dict[str, Any]]:
        """加载创新点数据"""
        innovations_path = self.innovations_dir / "SANDE_with_descriptions_innovations_innovations.json"
        
        if not innovations_path.exists():
            raise FileNotFoundError(f"Innovations file not found: {innovations_path}")
        
        with open(innovations_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        innovations = data.get('innovations', [])
        logger.info(f"Loaded {len(innovations)} innovations")
        
        return innovations
    
    def setup_complete_system(self, repo_name: str = "EMCGCN-ASTE"):
        """设置完整的RAG系统"""
        logger.info("Setting up complete RAG system...")
        
        # 初始化模型
        self.initialize_models()
        
        # 构建向量数据库
        self.build_vector_database(repo_name)
        
        # 初始化检索器
        self.initialize_retriever()
        
        logger.info("RAG system setup completed successfully")
    
    def match_innovations_to_code(self, 
                                 confidence_threshold: float = 0.3) -> Dict[str, Any]:
        """将创新点匹配到代码"""
        if self.matcher is None:
            raise ValueError("System must be setup first")
        
        # 加载创新点
        innovations = self.load_innovations()
        
        # 执行匹配
        logger.info("Matching innovations to code...")
        matches = self.matcher.match_innovations_to_code(
            innovations, 
            confidence_threshold=confidence_threshold
        )
        
        return matches
    
    def generate_comprehensive_report(self, matches: Dict[str, Any]) -> str:
        """生成综合报告"""
        if self.matcher is None:
            raise ValueError("System must be setup first")
        
        return self.matcher.generate_match_report(matches)
    
    def save_results(self, matches: Dict[str, Any], output_dir: str = None):
        """保存结果"""
        if output_dir is None:
            output_dir = self.processed_data_dir / "rag_results"
        
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存匹配结果
        matches_path = output_dir / "innovation_code_matches.json"
        self.matcher.export_matches_to_json(matches, str(matches_path))
        
        # 保存报告
        report = self.generate_comprehensive_report(matches)
        report_path = output_dir / "match_report.txt"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # 保存系统统计信息
        stats = self.get_system_statistics()
        stats_path = output_dir / "system_statistics.json"
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Results saved to {output_dir}")
        
        return {
            'matches_file': str(matches_path),
            'report_file': str(report_path),
            'stats_file': str(stats_path)
        }
    
    def query_code(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """查询代码"""
        if self.retriever is None:
            raise ValueError("System must be setup first")
        
        return self.retriever.retrieve(query, use_reranking=self.use_cross_encoder)[:top_k]
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        stats = {
            'system_config': {
                'model_type': self.model_type,
                'model_size': self.model_size,
                'dimension': self.dimension,
                'index_type': self.index_type,
                'use_cross_encoder': self.use_cross_encoder,
                'recall_top_k': self.recall_top_k,
                'rerank_top_k': self.rerank_top_k
            }
        }
        
        if self.retriever:
            stats['retrieval_stats'] = self.retriever.get_retrieval_statistics()
        
        return stats
    
    def save_vector_database(self, save_path: str = None):
        """保存向量数据库"""
        if self.vector_db is None:
            raise ValueError("Vector database not initialized")
        
        if save_path is None:
            save_path = self.processed_data_dir / "vector_db"
        
        self.vector_db.save(str(save_path))
        logger.info(f"Vector database saved to {save_path}")
    
    def load_vector_database(self, load_path: str = None):
        """加载向量数据库"""
        if load_path is None:
            load_path = self.processed_data_dir / "vector_db"
        
        if not Path(load_path).exists():
            raise FileNotFoundError(f"Vector database not found: {load_path}")
        
        # 需要先初始化嵌入模型以获取维度
        if self.embedding_model is None:
            self.initialize_models()
        
        embedding_dim = self.embedding_model.get_embedding_dim()
        self.vector_db = CodeVectorDatabase(embedding_dim, self.index_type)
        self.vector_db.load(str(load_path))
        
        logger.info(f"Vector database loaded from {load_path}")


def main():
    """主函数 - 运行完整的RAG系统"""
    import argparse
    
    parser = argparse.ArgumentParser(description="创新点RAG系统")
    parser.add_argument("--model-type", default="local", choices=["local", "api"],
                       help="模型类型 (local: 本地模型, api: API调用)")
    parser.add_argument("--model-size", default="0.6B", choices=["0.6B", "4B"],
                       help="嵌入模型大小 (仅在model-type=local时使用)")
    parser.add_argument("--dimension", type=int, default=1024,
                       help="向量维度 (仅在model-type=api时使用)")
    parser.add_argument("--no-cross-encoder", action="store_true",
                       help="不使用交叉编码器")
    parser.add_argument("--confidence-threshold", type=float, default=0.3,
                       help="匹配置信度阈值")
    parser.add_argument("--repo-name", default="EMCGCN-ASTE",
                       help="代码仓库名称")
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 创建RAG系统
        rag_system = InnovationRAGSystem(
            model_type=args.model_type,
            model_size=args.model_size,
            dimension=args.dimension,
            use_cross_encoder=not args.no_cross_encoder,
            recall_top_k=50,
            rerank_top_k=10
        )
        
        # 设置完整系统
        rag_system.setup_complete_system(args.repo_name)
        
        # 执行创新点匹配
        matches = rag_system.match_innovations_to_code(args.confidence_threshold)
        
        # 生成并显示报告
        report = rag_system.generate_comprehensive_report(matches)
        print(report)
        
        # 保存结果
        saved_files = rag_system.save_results(matches)
        print(f"\n结果已保存:")
        for file_type, file_path in saved_files.items():
            print(f"  {file_type}: {file_path}")
        
        # 保存向量数据库以便后续使用
        rag_system.save_vector_database()
        
    except Exception as e:
        logger.error(f"RAG system execution failed: {e}")
        raise


if __name__ == "__main__":
    main()
