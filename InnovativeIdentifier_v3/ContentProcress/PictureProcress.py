import os
import re
import base64
import mimetypes
from urllib.parse import urlparse
from dotenv import load_dotenv
from openai import OpenAI

# Load environment variables from .env file
load_dotenv()

def encode_image_to_base64(image_path):
    """
    Convert a local image file to base64 encoding
    """
    try:
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')

        # Get the MIME type
        mime_type, _ = mimetypes.guess_type(image_path)
        if mime_type is None:
            # Default to JPEG if we can't determine the type
            mime_type = "image/jpeg"

        return f"data:{mime_type};base64,{encoded_string}"
    except Exception as e:
        print(f"Error encoding image {image_path}: {e}")
        return None

def is_url(string):
    """
    Check if a string is a URL
    """
    try:
        result = urlparse(string)
        return all([result.scheme, result.netloc])
    except:
        return False

def is_local_file(path):
    """
    Check if a path points to a local file that exists
    """
    return os.path.isfile(path)

def describe_images_in_markdown(
    input_md_path: str,
    output_md_path: str,
    model: str = "gpt-4o"
):
    """
    Retrieve all image URLs from the specified Markdown file,
    call gpt-4o to obtain image descriptions (also sending the entire paper content).
    If the model returns "no", indicating that the image cannot be described,
    then do not insert any description.
    Finally, write the modified content to a new file.
    """

    # Initialize OpenAI client with environment variables
    api_key = os.getenv("OPENAI_API_KEY")
    api_base = os.getenv("OPENAI_API_BASE")

    if not api_key:
        raise ValueError("OPENAI_API_KEY not found in environment variables")

    # Create OpenAI client with new API
    client = OpenAI(
        api_key=api_key,
        base_url=api_base
    )

    # Read the markdown file
    with open(input_md_path, "r", encoding="utf-8") as f:
        md_content = f.read()

    # Regular expression to match image markdown syntax
    pattern = re.compile(r'!\[(.*?)\]\((.*?)\)')

    # Cache image descriptions that have already been requested to avoid repeated calls
    url_to_description = {}

    def replace_func(match: re.Match) -> str:
        alt_text = match.group(1)  # Original alt text
        img_path = match.group(2)  # Image path/URL

        # If a description for this image has not been obtained, call the model
        if img_path not in url_to_description:
            # Determine if it's a URL or local file
            image_data = None

            if is_url(img_path):
                # It's a URL, use directly
                image_data = {"url": img_path}
                print(f"Processing URL image: {img_path}")
            elif is_local_file(img_path):
                # It's a local file, encode to base64
                base64_image = encode_image_to_base64(img_path)
                if base64_image:
                    image_data = {"url": base64_image}
                    print(f"Processing local image: {img_path}")
                else:
                    print(f"Failed to encode local image: {img_path}")
                    url_to_description[img_path] = "no"
                    return f'![{alt_text}]({img_path})'
            else:
                # Try to resolve relative path
                # Check if it's relative to the markdown file directory
                md_dir = os.path.dirname(input_md_path)
                full_path = os.path.join(md_dir, img_path)

                if is_local_file(full_path):
                    base64_image = encode_image_to_base64(full_path)
                    if base64_image:
                        image_data = {"url": base64_image}
                        print(f"Processing relative path image: {full_path}")
                    else:
                        print(f"Failed to encode relative path image: {full_path}")
                        url_to_description[img_path] = "no"
                        return f'![{alt_text}]({img_path})'
                else:
                    print(f"Image not found: {img_path} (also tried: {full_path})")
                    url_to_description[img_path] = "no"
                    return f'![{alt_text}]({img_path})'

            # Construct the message to be sent to the model
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": (
                                "Based on the paper content and your visual understanding of the image, please provide a complete description of the image, with an emphasis on numerical details. "
                                "Note that your response should contain a comprehensive description of the image's information without any omissions (especially all details related to the model architecture), and it should not include any analysis of colors.\n"
                                "Below is the full paper content:\n\n"
                                f"{md_content}\n\n"
                            )
                        },
                        {
                            "type": "image_url",
                            "image_url": image_data
                        }
                    ]
                }
            ]

            try:
                # Call OpenAI gpt-4o API using new client
                response = client.chat.completions.create(
                    model=model,
                    messages=messages,
                    max_tokens=1000,
                    temperature=0
                )
                description = response.choices[0].message.content.strip()
                url_to_description[img_path] = description

                # Print token usage information
                usage_info = response.usage
                if usage_info:
                    print(f"Image description - Prompt tokens: {usage_info.prompt_tokens}, "
                          f"Completion tokens: {usage_info.completion_tokens}, "
                          f"Total tokens: {usage_info.total_tokens}")

            except Exception as e:
                print(f"Error processing image {img_path}: {str(e)}")
                url_to_description[img_path] = "no"

        description = url_to_description[img_path]

        # If the model returns "no", keep the original image markdown
        if description.lower() == "no":
            return f'![{alt_text}]({img_path})'
        else:
            # Insert the description below the image
            new_markdown = f'![{alt_text}]({img_path})\n\n> **Picture description**: {description}\n'
            return new_markdown

    # Replace all image markdown with descriptions
    new_md_content = pattern.sub(replace_func, md_content)

    # Write the modified content to the output file
    with open(output_md_path, "w", encoding="utf-8") as f:
        f.write(new_md_content)


def process_markdown_folder(input_folder: str, output_folder: str, model: str = "gpt-4o"):
    """
    Process all markdown files in a folder and generate image descriptions.

    Args:
        input_folder: Path to the folder containing input markdown files
        output_folder: Path to the folder where processed files will be saved
        model: OpenAI model to use for image description (default: gpt-4o)
    """
    # Create output folder if it doesn't exist
    os.makedirs(output_folder, exist_ok=True)

    # Process all .md files in the input folder
    for filename in os.listdir(input_folder):
        if filename.endswith(".md"):
            input_path = os.path.join(input_folder, filename)
            output_path = os.path.join(output_folder, filename)

            print(f"Processing {filename}...")
            describe_images_in_markdown(input_path, output_path, model)
            print(f"Completed processing {filename}")


if __name__ == "__main__":
    # Example usage - single file processing
    # input_path = "../ProcressedData/EMC-GCN/auto/EMC-GCN.md"
    # output_path = "../ProcressedData/results/paper_with_descriptions.md"
    input_path = "../ProcressedData/SANDE/auto/SANDE.md"
    output_path = "../ProcressedData/results/SANDE_with_descriptions.md"

    # Check if input file exists
    if os.path.exists(input_path):
        describe_images_in_markdown(input_path, output_path)
        print(f"Generation completed, the processed content has been written to {output_path}")
    else:
        print(f"Input file {input_path} not found. Please check the path.")

        # Alternative: process a folder
        input_folder = "input_markdown/"
        output_folder = "output_markdown/"

        if os.path.exists(input_folder):
            print(f"Processing folder {input_folder}...")
            process_markdown_folder(input_folder, output_folder)
            print(f"Folder processing completed, results saved to {output_folder}")
        else:
            print(f"Neither single file nor folder found. Please check your paths.")