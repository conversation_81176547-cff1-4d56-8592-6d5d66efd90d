[{"id": "0", "sentence": "Not only was the food outstanding , but the little ' perks ' were great .", "triples": [{"uid": "0-0", "sentiment": "positive", "target_tags": "Not\\O only\\O was\\O the\\O food\\B outstanding\\O ,\\O but\\O the\\O little\\O '\\O perks\\O '\\O were\\O great\\O .\\O", "opinion_tags": "Not\\O only\\O was\\O the\\O food\\O outstanding\\B ,\\O but\\O the\\O little\\O '\\O perks\\O '\\O were\\O great\\O .\\O"}, {"uid": "0-1", "sentiment": "positive", "target_tags": "Not\\O only\\O was\\O the\\O food\\O outstanding\\O ,\\O but\\O the\\O little\\O '\\O perks\\B '\\O were\\O great\\O .\\O", "opinion_tags": "Not\\O only\\O was\\O the\\O food\\O outstanding\\O ,\\O but\\O the\\O little\\O '\\O perks\\O '\\O were\\O great\\B .\\O"}], "postag": ["RB", "RB", "VBD", "DT", "NN", "JJ", ",", "CC", "DT", "JJ", "``", "NNS", "''", "VBD", "JJ", "."], "head": [2, 6, 6, 5, 6, 0, 15, 15, 12, 12, 12, 15, 12, 15, 6, 6], "deprel": ["advmod", "cc:preconj", "cop", "det", "nsubj", "root", "punct", "cc", "det", "amod", "punct", "nsubj", "punct", "cop", "conj", "punct"]}, {"id": "1", "sentence": "The pizza is the best if you like thin crusted pizza .", "triples": [{"uid": "1-0", "sentiment": "positive", "target_tags": "The\\O pizza\\B is\\O the\\O best\\O if\\O you\\O like\\O thin\\O crusted\\O pizza\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O the\\O best\\B if\\O you\\O like\\O thin\\O crusted\\O pizza\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "JJS", "IN", "PRP", "VBP", "JJ", "JJ", "NN", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 11, 11, 8, 5], "deprel": ["det", "nsubj", "cop", "det", "root", "mark", "nsubj", "advcl", "amod", "amod", "obj", "punct"]}, {"id": "2", "sentence": "it 's a perfect place to have a amazing indian food .", "triples": [{"uid": "2-0", "sentiment": "positive", "target_tags": "it\\O 's\\O a\\O perfect\\O place\\O to\\O have\\O a\\O amazing\\O indian\\B food\\I .\\O", "opinion_tags": "it\\O 's\\O a\\O perfect\\O place\\O to\\O have\\O a\\O amazing\\B indian\\O food\\O .\\O"}], "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "DT", "JJ", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 11, 11, 11, 7, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "csubj", "det", "amod", "amod", "obj", "punct"]}, {"id": "3", "sentence": "Fabulous service , fantastic food , and a chilled out atmosphere and environment .", "triples": [{"uid": "3-0", "sentiment": "positive", "target_tags": "Fabulous\\O service\\B ,\\O fantastic\\O food\\O ,\\O and\\O a\\O chilled\\O out\\O atmosphere\\O and\\O environment\\O .\\O", "opinion_tags": "Fabulous\\B service\\O ,\\O fantastic\\O food\\O ,\\O and\\O a\\O chilled\\O out\\O atmosphere\\O and\\O environment\\O .\\O"}, {"uid": "3-1", "sentiment": "positive", "target_tags": "Fabulous\\O service\\O ,\\O fantastic\\O food\\B ,\\O and\\O a\\O chilled\\O out\\O atmosphere\\O and\\O environment\\O .\\O", "opinion_tags": "Fabulous\\O service\\O ,\\O fantastic\\B food\\O ,\\O and\\O a\\O chilled\\O out\\O atmosphere\\O and\\O environment\\O .\\O"}, {"uid": "3-2", "sentiment": "positive", "target_tags": "Fabulous\\O service\\O ,\\O fantastic\\O food\\O ,\\O and\\O a\\O chilled\\O out\\O atmosphere\\B and\\O environment\\O .\\O", "opinion_tags": "Fabulous\\O service\\O ,\\O fantastic\\O food\\O ,\\O and\\O a\\O chilled\\B out\\I atmosphere\\O and\\O environment\\O .\\O"}, {"uid": "3-3", "sentiment": "positive", "target_tags": "Fabulous\\O service\\O ,\\O fantastic\\O food\\O ,\\O and\\O a\\O chilled\\O out\\O atmosphere\\O and\\O environment\\B .\\O", "opinion_tags": "Fabulous\\O service\\O ,\\O fantastic\\O food\\O ,\\O and\\O a\\O chilled\\B out\\I atmosphere\\O and\\O environment\\O .\\O"}], "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "CC", "DT", "VBN", "RP", "NN", "CC", "NN", "."], "head": [2, 0, 5, 5, 2, 11, 11, 11, 11, 9, 2, 13, 11, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "cc", "det", "amod", "compound:prt", "conj", "cc", "conj", "punct"]}, {"id": "4", "sentence": "Go here for a romantic dinner but not for an all out wow dining experience .", "triples": [{"uid": "4-0", "sentiment": "positive", "target_tags": "Go\\O here\\O for\\O a\\O romantic\\O dinner\\B but\\O not\\O for\\O an\\O all\\O out\\O wow\\O dining\\O experience\\O .\\O", "opinion_tags": "Go\\O here\\O for\\O a\\O romantic\\B dinner\\O but\\O not\\O for\\O an\\O all\\O out\\O wow\\O dining\\O experience\\O .\\O"}, {"uid": "4-1", "sentiment": "positive", "target_tags": "Go\\O here\\O for\\O a\\O romantic\\O dinner\\O but\\O not\\O for\\O an\\O all\\O out\\O wow\\O dining\\B experience\\O .\\O", "opinion_tags": "Go\\O here\\O for\\O a\\O romantic\\O dinner\\O but\\O not\\O for\\O an\\O all\\O out\\O wow\\B dining\\O experience\\O .\\O"}], "postag": ["VB", "RB", "IN", "DT", "JJ", "NN", "CC", "RB", "IN", "DT", "DT", "IN", "UH", "NN", "NN", "."], "head": [0, 1, 6, 6, 6, 1, 11, 11, 11, 11, 1, 15, 15, 15, 11, 1], "deprel": ["root", "advmod", "case", "det", "amod", "obl", "cc", "advmod", "case", "det", "conj", "case", "discourse", "compound", "nmod", "punct"]}, {"id": "5", "sentence": "With the great variety on the menu , I eat here often and never get bored .", "triples": [{"uid": "5-0", "sentiment": "positive", "target_tags": "With\\O the\\O great\\O variety\\O on\\O the\\O menu\\B ,\\O I\\O eat\\O here\\O often\\O and\\O never\\O get\\O bored\\O .\\O", "opinion_tags": "With\\O the\\O great\\B variety\\I on\\O the\\O menu\\O ,\\O I\\O eat\\O here\\O often\\O and\\O never\\O get\\O bored\\O .\\O"}], "postag": ["IN", "DT", "JJ", "NN", "IN", "DT", "NN", ",", "PRP", "VBP", "RB", "RB", "CC", "RB", "VB", "JJ", "."], "head": [4, 4, 4, 10, 7, 7, 4, 10, 10, 0, 10, 10, 15, 15, 10, 15, 10], "deprel": ["case", "det", "amod", "obl", "case", "det", "nmod", "punct", "nsubj", "root", "advmod", "advmod", "cc", "advmod", "conj", "xcomp", "punct"]}, {"id": "6", "sentence": "Not too crazy about their sake martini .", "triples": [{"uid": "6-0", "sentiment": "negative", "target_tags": "Not\\O too\\O crazy\\O about\\O their\\O sake\\B martini\\I .\\O", "opinion_tags": "Not\\O too\\O crazy\\B about\\O their\\O sake\\O martini\\O .\\O"}], "postag": ["RB", "RB", "JJ", "IN", "PRP$", "NN", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 3], "deprel": ["advmod", "advmod", "root", "case", "nmod:poss", "compound", "obl", "punct"]}, {"id": "7", "sentence": "All my co-workers were amazed at how small the dish was .", "triples": [{"uid": "7-0", "sentiment": "negative", "target_tags": "All\\O my\\O co-workers\\O were\\O amazed\\O at\\O how\\O small\\O the\\O dish\\B was\\O .\\O", "opinion_tags": "All\\O my\\O co-workers\\O were\\O amazed\\O at\\O how\\O small\\B the\\O dish\\O was\\O .\\O"}], "postag": ["PDT", "PRP$", "NNS", "VBD", "VBN", "IN", "WRB", "JJ", "DT", "NN", "VBD", "."], "head": [3, 3, 5, 5, 0, 8, 8, 5, 10, 8, 8, 5], "deprel": ["det:predet", "nmod:poss", "nsubj:pass", "aux:pass", "root", "mark", "mark", "advcl", "det", "nsubj", "cop", "punct"]}, {"id": "8", "sentence": "The wait staff is friendly , and the food has gotten better and better !", "triples": [{"uid": "8-0", "sentiment": "positive", "target_tags": "The\\O wait\\B staff\\I is\\O friendly\\O ,\\O and\\O the\\O food\\O has\\O gotten\\O better\\O and\\O better\\O !\\O", "opinion_tags": "The\\O wait\\O staff\\O is\\O friendly\\B ,\\O and\\O the\\O food\\O has\\O gotten\\O better\\O and\\O better\\O !\\O"}, {"uid": "8-1", "sentiment": "positive", "target_tags": "The\\O wait\\O staff\\O is\\O friendly\\O ,\\O and\\O the\\O food\\B has\\O gotten\\O better\\O and\\O better\\O !\\O", "opinion_tags": "The\\O wait\\O staff\\O is\\O friendly\\O ,\\O and\\O the\\O food\\O has\\O gotten\\O better\\B and\\O better\\O !\\O"}, {"uid": "8-2", "sentiment": "positive", "target_tags": "The\\O wait\\O staff\\O is\\O friendly\\O ,\\O and\\O the\\O food\\B has\\O gotten\\O better\\O and\\O better\\O !\\O", "opinion_tags": "The\\O wait\\O staff\\O is\\O friendly\\O ,\\O and\\O the\\O food\\O has\\O gotten\\O better\\O and\\O better\\B !\\O"}], "postag": ["DT", "NN", "NN", "VBZ", "JJ", ",", "CC", "DT", "NN", "VBZ", "VBN", "JJR", "CC", "JJR", "."], "head": [3, 3, 5, 5, 0, 11, 11, 9, 11, 11, 5, 11, 14, 12, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "cc", "det", "nsubj", "aux", "conj", "xcomp", "cc", "conj", "punct"]}, {"id": "9", "sentence": "It may be a bit packed on weekends , but the vibe is good and it is the best French food you will find in the area .", "triples": [{"uid": "9-0", "sentiment": "positive", "target_tags": "It\\O may\\O be\\O a\\O bit\\O packed\\O on\\O weekends\\O ,\\O but\\O the\\O vibe\\B is\\O good\\O and\\O it\\O is\\O the\\O best\\O French\\O food\\O you\\O will\\O find\\O in\\O the\\O area\\O .\\O", "opinion_tags": "It\\O may\\O be\\O a\\O bit\\O packed\\O on\\O weekends\\O ,\\O but\\O the\\O vibe\\O is\\O good\\B and\\O it\\O is\\O the\\O best\\O French\\O food\\O you\\O will\\O find\\O in\\O the\\O area\\O .\\O"}, {"uid": "9-1", "sentiment": "positive", "target_tags": "It\\O may\\O be\\O a\\O bit\\O packed\\O on\\O weekends\\O ,\\O but\\O the\\O vibe\\O is\\O good\\O and\\O it\\O is\\O the\\O best\\O French\\B food\\I you\\O will\\O find\\O in\\O the\\O area\\O .\\O", "opinion_tags": "It\\O may\\O be\\O a\\O bit\\O packed\\O on\\O weekends\\O ,\\O but\\O the\\O vibe\\O is\\O good\\O and\\O it\\O is\\O the\\O best\\B French\\O food\\O you\\O will\\O find\\O in\\O the\\O area\\O .\\O"}], "postag": ["PRP", "MD", "VB", "DT", "NN", "VBN", "IN", "NNS", ",", "CC", "DT", "NN", "VBZ", "JJ", "CC", "PRP", "VBZ", "DT", "JJS", "JJ", "NN", "PRP", "MD", "VB", "IN", "DT", "NN", "."], "head": [5, 5, 5, 5, 0, 5, 8, 6, 14, 14, 12, 14, 14, 5, 21, 21, 21, 21, 21, 21, 14, 24, 24, 21, 27, 27, 24, 5], "deprel": ["nsubj", "aux", "cop", "det", "root", "acl", "case", "obl", "punct", "cc", "det", "nsubj", "cop", "conj", "cc", "nsubj", "cop", "det", "amod", "amod", "conj", "nsubj", "aux", "acl:relcl", "case", "det", "obl", "punct"]}, {"id": "10", "sentence": "I also ordered for delivery and the restaurant forgot half the order .", "triples": [{"uid": "10-0", "sentiment": "negative", "target_tags": "I\\O also\\O ordered\\O for\\O delivery\\B and\\O the\\O restaurant\\O forgot\\O half\\O the\\O order\\O .\\O", "opinion_tags": "I\\O also\\O ordered\\O for\\O delivery\\O and\\O the\\O restaurant\\O forgot\\B half\\O the\\O order\\O .\\O"}, {"uid": "10-1", "sentiment": "negative", "target_tags": "I\\O also\\O ordered\\O for\\O delivery\\O and\\O the\\O restaurant\\O forgot\\O half\\O the\\O order\\B .\\O", "opinion_tags": "I\\O also\\O ordered\\O for\\O delivery\\O and\\O the\\O restaurant\\O forgot\\B half\\O the\\O order\\O .\\O"}], "postag": ["PRP", "RB", "VBD", "IN", "NN", "CC", "DT", "NN", "VBD", "PDT", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 9, 8, 9, 3, 12, 12, 9, 3], "deprel": ["nsubj", "advmod", "root", "case", "obl", "cc", "det", "nsubj", "conj", "det:predet", "det", "obj", "punct"]}, {"id": "11", "sentence": "Overall A oh ya even though there is waiting it is deff worth it", "triples": [{"uid": "11-0", "sentiment": "negative", "target_tags": "Overall\\O A\\O oh\\O ya\\O even\\O though\\O there\\O is\\O waiting\\B it\\O is\\O deff\\O worth\\O it\\O", "opinion_tags": "Overall\\O A\\O oh\\O ya\\O even\\O though\\O there\\O is\\O waiting\\O it\\O is\\O deff\\O worth\\B it\\O"}], "postag": ["RB", "DT", "UH", "UH", "RB", "IN", "EX", "VBZ", "VBG", "PRP", "VBZ", "RB", "JJ", "PRP"], "head": [13, 13, 13, 13, 8, 8, 8, 9, 13, 13, 13, 13, 0, 13], "deprel": ["advmod", "mark", "discourse", "discourse", "advmod", "mark", "expl", "aux", "advcl", "nsubj", "cop", "advmod", "root", "obj"]}, {"id": "12", "sentence": "Service is highly refined : our seating was delayed 35 minutes past our reservation and the maitre d ' apologized and regularly kept us apprised of progress .", "triples": [{"uid": "12-0", "sentiment": "positive", "target_tags": "Service\\B is\\O highly\\O refined\\O :\\O our\\O seating\\O was\\O delayed\\O 35\\O minutes\\O past\\O our\\O reservation\\O and\\O the\\O maitre\\O d\\O '\\O apologized\\O and\\O regularly\\O kept\\O us\\O apprised\\O of\\O progress\\O .\\O", "opinion_tags": "Service\\O is\\O highly\\O refined\\B :\\O our\\O seating\\O was\\O delayed\\O 35\\O minutes\\O past\\O our\\O reservation\\O and\\O the\\O maitre\\O d\\O '\\O apologized\\O and\\O regularly\\O kept\\O us\\O apprised\\O of\\O progress\\O .\\O"}, {"uid": "12-1", "sentiment": "positive", "target_tags": "Service\\O is\\O highly\\O refined\\O :\\O our\\O seating\\O was\\O delayed\\O 35\\O minutes\\O past\\O our\\O reservation\\O and\\O the\\O maitre\\B d\\O '\\O apologized\\O and\\O regularly\\O kept\\O us\\O apprised\\O of\\O progress\\O .\\O", "opinion_tags": "Service\\O is\\O highly\\O refined\\O :\\O our\\O seating\\O was\\O delayed\\B 35\\O minutes\\O past\\O our\\O reservation\\O and\\O the\\O maitre\\O d\\O '\\O apologized\\O and\\O regularly\\O kept\\O us\\O apprised\\O of\\O progress\\O .\\O"}, {"uid": "12-2", "sentiment": "negative", "target_tags": "Service\\O is\\O highly\\O refined\\O :\\O our\\O seating\\O was\\O delayed\\O 35\\O minutes\\O past\\O our\\O reservation\\B and\\O the\\O maitre\\O d\\O '\\O apologized\\O and\\O regularly\\O kept\\O us\\O apprised\\O of\\O progress\\O .\\O", "opinion_tags": "Service\\O is\\O highly\\O refined\\O :\\O our\\O seating\\O was\\O delayed\\B 35\\O minutes\\O past\\O our\\O reservation\\O and\\O the\\O maitre\\O d\\O '\\O apologized\\O and\\O regularly\\O kept\\O us\\O apprised\\O of\\O progress\\O .\\O"}], "postag": ["NN", "VBZ", "RB", "VBN", ":", "PRP$", "NN", "VBD", "VBN", "CD", "NNS", "IN", "PRP$", "NN", "CC", "DT", "NN", "NNP", "''", "VBD", "CC", "RB", "VBD", "PRP", "VBN", "IN", "NN", "."], "head": [4, 4, 4, 0, 4, 7, 9, 9, 4, 11, 9, 14, 14, 9, 18, 18, 18, 20, 18, 9, 23, 23, 20, 23, 23, 27, 25, 4], "deprel": ["nsubj:pass", "aux:pass", "advmod", "root", "punct", "nmod:poss", "nsubj", "aux:pass", "parataxis", "nummod", "obl:npmod", "case", "nmod:poss", "obl", "cc", "det", "compound", "nsubj", "punct", "conj", "cc", "advmod", "conj", "obj", "xcomp", "case", "obl", "punct"]}, {"id": "13", "sentence": "Pizza and garlic knots are great as well , I order from them quite often and the delivery is always super quick !", "triples": [{"uid": "13-0", "sentiment": "positive", "target_tags": "Pizza\\B and\\O garlic\\O knots\\O are\\O great\\O as\\O well\\O ,\\O I\\O order\\O from\\O them\\O quite\\O often\\O and\\O the\\O delivery\\O is\\O always\\O super\\O quick\\O !\\O", "opinion_tags": "Pizza\\O and\\O garlic\\O knots\\O are\\O great\\B as\\O well\\O ,\\O I\\O order\\O from\\O them\\O quite\\O often\\O and\\O the\\O delivery\\O is\\O always\\O super\\O quick\\O !\\O"}, {"uid": "13-1", "sentiment": "positive", "target_tags": "Pizza\\O and\\O garlic\\O knots\\O are\\O great\\O as\\O well\\O ,\\O I\\O order\\O from\\O them\\O quite\\O often\\O and\\O the\\O delivery\\B is\\O always\\O super\\O quick\\O !\\O", "opinion_tags": "Pizza\\O and\\O garlic\\O knots\\O are\\O great\\O as\\O well\\O ,\\O I\\O order\\O from\\O them\\O quite\\O often\\O and\\O the\\O delivery\\O is\\O always\\O super\\B quick\\I !\\O"}, {"uid": "13-2", "sentiment": "positive", "target_tags": "Pizza\\O and\\O garlic\\B knots\\I are\\O great\\O as\\O well\\O ,\\O I\\O order\\O from\\O them\\O quite\\O often\\O and\\O the\\O delivery\\O is\\O always\\O super\\O quick\\O !\\O", "opinion_tags": "Pizza\\O and\\O garlic\\O knots\\O are\\O great\\B as\\O well\\O ,\\O I\\O order\\O from\\O them\\O quite\\O often\\O and\\O the\\O delivery\\O is\\O always\\O super\\O quick\\O !\\O"}], "postag": ["NN", "CC", "NN", "NNS", "VBP", "JJ", "RB", "RB", ",", "PRP", "VBP", "IN", "PRP", "RB", "RB", "CC", "DT", "NN", "VBZ", "RB", "RB", "JJ", "."], "head": [6, 4, 4, 1, 6, 0, 6, 7, 6, 11, 6, 13, 11, 15, 11, 22, 18, 22, 22, 22, 22, 6, 6], "deprel": ["nsubj", "cc", "compound", "conj", "cop", "root", "advmod", "fixed", "punct", "nsubj", "parataxis", "case", "obl", "advmod", "advmod", "cc", "det", "nsubj", "cop", "advmod", "advmod", "conj", "punct"]}, {"id": "14", "sentence": "For great chinese food nearby , you have Wu Liang Ye and Grand Sichuan just a block away .", "triples": [{"uid": "14-0", "sentiment": "positive", "target_tags": "For\\O great\\O chinese\\B food\\I nearby\\O ,\\O you\\O have\\O Wu\\O Liang\\O Ye\\O and\\O Grand\\O Sichuan\\O just\\O a\\O block\\O away\\O .\\O", "opinion_tags": "For\\O great\\B chinese\\O food\\O nearby\\O ,\\O you\\O have\\O Wu\\O Liang\\O Ye\\O and\\O Grand\\O Sichuan\\O just\\O a\\O block\\O away\\O .\\O"}], "postag": ["IN", "JJ", "JJ", "NN", "RB", ",", "PRP", "VBP", "NNP", "NNP", "NNP", "CC", "NNP", "NNP", "RB", "DT", "NN", "RB", "."], "head": [4, 4, 4, 8, 4, 8, 8, 0, 11, 9, 8, 14, 14, 11, 18, 17, 18, 8, 8], "deprel": ["case", "amod", "amod", "obl", "advmod", "punct", "nsubj", "root", "compound", "flat", "obj", "cc", "compound", "conj", "advmod", "det", "obl:npmod", "advmod", "punct"]}, {"id": "15", "sentence": "Service could be improved but overall this is a place that understands the importance of little things ( the heavy , black , antique-seeming teapot , for one ) in the restaurant experience .", "triples": [{"uid": "15-0", "sentiment": "negative", "target_tags": "Service\\B could\\O be\\O improved\\O but\\O overall\\O this\\O is\\O a\\O place\\O that\\O understands\\O the\\O importance\\O of\\O little\\O things\\O (\\O the\\O heavy\\O ,\\O black\\O ,\\O antique-seeming\\O teapot\\O ,\\O for\\O one\\O )\\O in\\O the\\O restaurant\\O experience\\O .\\O", "opinion_tags": "Service\\O could\\O be\\O improved\\B but\\O overall\\O this\\O is\\O a\\O place\\O that\\O understands\\O the\\O importance\\O of\\O little\\O things\\O (\\O the\\O heavy\\O ,\\O black\\O ,\\O antique-seeming\\O teapot\\O ,\\O for\\O one\\O )\\O in\\O the\\O restaurant\\O experience\\O .\\O"}, {"uid": "15-1", "sentiment": "positive", "target_tags": "Service\\O could\\O be\\O improved\\O but\\O overall\\O this\\O is\\O a\\O place\\O that\\O understands\\O the\\O importance\\O of\\O little\\O things\\O (\\O the\\O heavy\\O ,\\O black\\O ,\\O antique-seeming\\O teapot\\B ,\\O for\\O one\\O )\\O in\\O the\\O restaurant\\O experience\\O .\\O", "opinion_tags": "Service\\O could\\O be\\O improved\\O but\\O overall\\O this\\O is\\O a\\O place\\O that\\O understands\\O the\\O importance\\O of\\O little\\O things\\O (\\O the\\O heavy\\O ,\\O black\\O ,\\O antique-seeming\\B teapot\\O ,\\O for\\O one\\O )\\O in\\O the\\O restaurant\\O experience\\O .\\O"}], "postag": ["NN", "MD", "VB", "VBN", "CC", "RB", "DT", "VBZ", "DT", "NN", "WDT", "VBZ", "DT", "NN", "IN", "JJ", "NNS", "-LRB-", "DT", "JJ", ",", "JJ", ",", "JJ", "NN", ",", "IN", "CD", "-RRB-", "IN", "DT", "NN", "NN", "."], "head": [4, 4, 4, 0, 10, 10, 10, 10, 10, 4, 12, 10, 14, 12, 17, 17, 14, 25, 25, 25, 22, 20, 25, 25, 17, 25, 28, 25, 25, 33, 33, 33, 12, 4], "deprel": ["nsubj:pass", "aux", "aux:pass", "root", "cc", "advmod", "nsubj", "cop", "det", "conj", "nsubj", "acl:relcl", "det", "obj", "case", "amod", "nmod", "punct", "det", "amod", "punct", "conj", "punct", "amod", "appos", "punct", "case", "nmod", "punct", "case", "det", "compound", "obl", "punct"]}, {"id": "16", "sentence": "And <PERSON><PERSON><PERSON> , the beverage manager is the best bartender I have yet to come across .", "triples": [{"uid": "16-0", "sentiment": "positive", "target_tags": "And\\O Kruno\\O ,\\O the\\O beverage\\O manager\\O is\\O the\\O best\\O bartender\\B I\\O have\\O yet\\O to\\O come\\O across\\O .\\O", "opinion_tags": "And\\O Kruno\\O ,\\O the\\O beverage\\O manager\\O is\\O the\\O best\\B bartender\\O I\\O have\\O yet\\O to\\O come\\O across\\O .\\O"}, {"uid": "16-1", "sentiment": "positive", "target_tags": "And\\O Kruno\\O ,\\O the\\O beverage\\B manager\\I is\\O the\\O best\\O bartender\\O I\\O have\\O yet\\O to\\O come\\O across\\O .\\O", "opinion_tags": "And\\O Kruno\\O ,\\O the\\O beverage\\O manager\\O is\\O the\\O best\\B bartender\\O I\\O have\\O yet\\O to\\O come\\O across\\O .\\O"}], "postag": ["CC", "NNP", ",", "DT", "NN", "NN", "VBZ", "DT", "JJS", "NN", "PRP", "VBP", "RB", "TO", "VB", "RB", "."], "head": [10, 10, 10, 6, 6, 10, 10, 10, 10, 0, 12, 10, 12, 15, 12, 15, 10], "deprel": ["cc", "nsubj", "punct", "det", "compound", "nsubj", "cop", "det", "amod", "root", "nsubj", "acl:relcl", "advmod", "mark", "xcomp", "advmod", "punct"]}, {"id": "17", "sentence": "I understand the area and folks you need not come here for the romantic , alluring ambiance or the five star service featuring a sommlier and a complicated maze of captain and back waiters - you come for the authentic foods , the tastes , the experiance .", "triples": [{"uid": "17-0", "sentiment": "positive", "target_tags": "I\\O understand\\O the\\O area\\O and\\O folks\\O you\\O need\\O not\\O come\\O here\\O for\\O the\\O romantic\\O ,\\O alluring\\O ambiance\\B or\\O the\\O five\\O star\\O service\\O featuring\\O a\\O sommlier\\O and\\O a\\O complicated\\O maze\\O of\\O captain\\O and\\O back\\O waiters\\O -\\O you\\O come\\O for\\O the\\O authentic\\O foods\\O ,\\O the\\O tastes\\O ,\\O the\\O experiance\\O .\\O", "opinion_tags": "I\\O understand\\O the\\O area\\O and\\O folks\\O you\\O need\\O not\\O come\\O here\\O for\\O the\\O romantic\\B ,\\O alluring\\O ambiance\\O or\\O the\\O five\\O star\\O service\\O featuring\\O a\\O sommlier\\O and\\O a\\O complicated\\O maze\\O of\\O captain\\O and\\O back\\O waiters\\O -\\O you\\O come\\O for\\O the\\O authentic\\O foods\\O ,\\O the\\O tastes\\O ,\\O the\\O experiance\\O .\\O"}, {"uid": "17-1", "sentiment": "positive", "target_tags": "I\\O understand\\O the\\O area\\O and\\O folks\\O you\\O need\\O not\\O come\\O here\\O for\\O the\\O romantic\\O ,\\O alluring\\O ambiance\\B or\\O the\\O five\\O star\\O service\\O featuring\\O a\\O sommlier\\O and\\O a\\O complicated\\O maze\\O of\\O captain\\O and\\O back\\O waiters\\O -\\O you\\O come\\O for\\O the\\O authentic\\O foods\\O ,\\O the\\O tastes\\O ,\\O the\\O experiance\\O .\\O", "opinion_tags": "I\\O understand\\O the\\O area\\O and\\O folks\\O you\\O need\\O not\\O come\\O here\\O for\\O the\\O romantic\\O ,\\O alluring\\B ambiance\\O or\\O the\\O five\\O star\\O service\\O featuring\\O a\\O sommlier\\O and\\O a\\O complicated\\O maze\\O of\\O captain\\O and\\O back\\O waiters\\O -\\O you\\O come\\O for\\O the\\O authentic\\O foods\\O ,\\O the\\O tastes\\O ,\\O the\\O experiance\\O .\\O"}, {"uid": "17-2", "sentiment": "positive", "target_tags": "I\\O understand\\O the\\O area\\O and\\O folks\\O you\\O need\\O not\\O come\\O here\\O for\\O the\\O romantic\\O ,\\O alluring\\O ambiance\\O or\\O the\\O five\\O star\\O service\\B featuring\\O a\\O sommlier\\O and\\O a\\O complicated\\O maze\\O of\\O captain\\O and\\O back\\O waiters\\O -\\O you\\O come\\O for\\O the\\O authentic\\O foods\\O ,\\O the\\O tastes\\O ,\\O the\\O experiance\\O .\\O", "opinion_tags": "I\\O understand\\O the\\O area\\O and\\O folks\\O you\\O need\\O not\\O come\\O here\\O for\\O the\\O romantic\\O ,\\O alluring\\O ambiance\\O or\\O the\\O five\\B star\\I service\\O featuring\\O a\\O sommlier\\O and\\O a\\O complicated\\O maze\\O of\\O captain\\O and\\O back\\O waiters\\O -\\O you\\O come\\O for\\O the\\O authentic\\O foods\\O ,\\O the\\O tastes\\O ,\\O the\\O experiance\\O .\\O"}, {"uid": "17-3", "sentiment": "positive", "target_tags": "I\\O understand\\O the\\O area\\O and\\O folks\\O you\\O need\\O not\\O come\\O here\\O for\\O the\\O romantic\\O ,\\O alluring\\O ambiance\\O or\\O the\\O five\\O star\\O service\\O featuring\\O a\\O sommlier\\O and\\O a\\O complicated\\O maze\\O of\\O captain\\O and\\O back\\O waiters\\O -\\O you\\O come\\O for\\O the\\O authentic\\O foods\\B ,\\O the\\O tastes\\O ,\\O the\\O experiance\\O .\\O", "opinion_tags": "I\\O understand\\O the\\O area\\O and\\O folks\\O you\\O need\\O not\\O come\\O here\\O for\\O the\\O romantic\\O ,\\O alluring\\O ambiance\\O or\\O the\\O five\\O star\\O service\\O featuring\\O a\\O sommlier\\O and\\O a\\O complicated\\O maze\\O of\\O captain\\O and\\O back\\O waiters\\O -\\O you\\O come\\O for\\O the\\O authentic\\B foods\\O ,\\O the\\O tastes\\O ,\\O the\\O experiance\\O .\\O"}, {"uid": "17-4", "sentiment": "positive", "target_tags": "I\\O understand\\O the\\O area\\O and\\O folks\\O you\\O need\\O not\\O come\\O here\\O for\\O the\\O romantic\\O ,\\O alluring\\O ambiance\\O or\\O the\\O five\\O star\\O service\\O featuring\\O a\\O sommlier\\O and\\O a\\O complicated\\O maze\\O of\\O captain\\B and\\O back\\O waiters\\O -\\O you\\O come\\O for\\O the\\O authentic\\O foods\\O ,\\O the\\O tastes\\O ,\\O the\\O experiance\\O .\\O", "opinion_tags": "I\\O understand\\O the\\O area\\O and\\O folks\\O you\\O need\\O not\\O come\\O here\\O for\\O the\\O romantic\\O ,\\O alluring\\O ambiance\\O or\\O the\\O five\\O star\\O service\\O featuring\\O a\\O sommlier\\O and\\O a\\O complicated\\B maze\\I of\\O captain\\O and\\O back\\O waiters\\O -\\O you\\O come\\O for\\O the\\O authentic\\O foods\\O ,\\O the\\O tastes\\O ,\\O the\\O experiance\\O .\\O"}, {"uid": "17-5", "sentiment": "positive", "target_tags": "I\\O understand\\O the\\O area\\O and\\O folks\\O you\\O need\\O not\\O come\\O here\\O for\\O the\\O romantic\\O ,\\O alluring\\O ambiance\\O or\\O the\\O five\\O star\\O service\\O featuring\\O a\\O sommlier\\O and\\O a\\O complicated\\O maze\\O of\\O captain\\O and\\O back\\B waiters\\I -\\O you\\O come\\O for\\O the\\O authentic\\O foods\\O ,\\O the\\O tastes\\O ,\\O the\\O experiance\\O .\\O", "opinion_tags": "I\\O understand\\O the\\O area\\O and\\O folks\\O you\\O need\\O not\\O come\\O here\\O for\\O the\\O romantic\\O ,\\O alluring\\O ambiance\\O or\\O the\\O five\\O star\\O service\\O featuring\\O a\\O sommlier\\O and\\O a\\O complicated\\B maze\\I of\\O captain\\O and\\O back\\O waiters\\O -\\O you\\O come\\O for\\O the\\O authentic\\O foods\\O ,\\O the\\O tastes\\O ,\\O the\\O experiance\\O .\\O"}], "postag": ["PRP", "VBP", "DT", "NN", "CC", "NNS", "PRP", "VBP", "RB", "VB", "RB", "IN", "DT", "JJ", ",", "JJ", "NN", "CC", "DT", "CD", "NN", "NN", "VBG", "DT", "NN", "CC", "DT", "JJ", "NN", "IN", "NN", "CC", "JJ", "NNS", ",", "PRP", "VBP", "IN", "DT", "JJ", "NNS", ",", "DT", "NNS", ",", "DT", "NN", "."], "head": [2, 0, 4, 2, 6, 4, 10, 10, 10, 2, 10, 17, 17, 17, 17, 17, 10, 22, 22, 21, 22, 17, 22, 25, 23, 29, 29, 29, 25, 31, 29, 34, 34, 31, 2, 37, 2, 41, 41, 41, 37, 44, 44, 41, 47, 47, 41, 2], "deprel": ["nsubj", "root", "det", "obj", "cc", "conj", "nsubj", "aux", "advmod", "ccomp", "advmod", "case", "det", "amod", "punct", "amod", "obl", "cc", "det", "nummod", "compound", "conj", "acl", "det", "obj", "cc", "det", "amod", "conj", "case", "nmod", "cc", "amod", "conj", "punct", "nsubj", "parataxis", "case", "det", "amod", "obl", "punct", "det", "appos", "punct", "det", "appos", "punct"]}, {"id": "18", "sentence": "Eating in , the atmosphere saves it , but at your desk , it 's a very disappointing experience .", "triples": [{"uid": "18-0", "sentiment": "positive", "target_tags": "Eating\\O in\\O ,\\O the\\O atmosphere\\B saves\\O it\\O ,\\O but\\O at\\O your\\O desk\\O ,\\O it\\O 's\\O a\\O very\\O disappointing\\O experience\\O .\\O", "opinion_tags": "Eating\\O in\\O ,\\O the\\O atmosphere\\O saves\\B it\\O ,\\O but\\O at\\O your\\O desk\\O ,\\O it\\O 's\\O a\\O very\\O disappointing\\O experience\\O .\\O"}], "postag": ["VBG", "RB", ",", "DT", "NN", "VBZ", "PRP", ",", "CC", "IN", "PRP$", "NN", ",", "PRP", "VBZ", "DT", "RB", "JJ", "NN", "."], "head": [6, 1, 6, 5, 6, 0, 6, 19, 19, 12, 12, 19, 19, 19, 19, 19, 18, 19, 6, 6], "deprel": ["advcl", "advmod", "punct", "det", "nsubj", "root", "obj", "punct", "cc", "case", "nmod:poss", "obl", "punct", "nsubj", "cop", "det", "advmod", "amod", "conj", "punct"]}, {"id": "19", "sentence": "Succulent steaks cooked precisely to your desired 'doneness ' accompanied by salads and sides that do n't look like leafy road kill .", "triples": [{"uid": "19-0", "sentiment": "positive", "target_tags": "Succulent\\O steaks\\B cooked\\O precisely\\O to\\O your\\O desired\\O 'doneness\\O '\\O accompanied\\O by\\O salads\\O and\\O sides\\O that\\O do\\O n't\\O look\\O like\\O leafy\\O road\\O kill\\O .\\O", "opinion_tags": "Succulent\\O steaks\\O cooked\\O precisely\\O to\\O your\\O desired\\B 'doneness\\O '\\O accompanied\\O by\\O salads\\O and\\O sides\\O that\\O do\\O n't\\O look\\O like\\O leafy\\O road\\O kill\\O .\\O"}], "postag": ["JJ", "NNS", "VBN", "RB", "IN", "PRP$", "VBN", "NN", "''", "VBN", "IN", "NNS", "CC", "NNS", "WDT", "VBP", "RB", "VB", "IN", "JJ", "NN", "NN", "."], "head": [2, 0, 2, 3, 8, 8, 8, 3, 8, 8, 12, 10, 14, 12, 18, 18, 18, 12, 22, 22, 22, 18, 2], "deprel": ["amod", "root", "acl", "advmod", "case", "nmod:poss", "amod", "obl", "punct", "acl", "case", "obl", "cc", "conj", "nsubj", "aux", "advmod", "acl:relcl", "case", "amod", "compound", "obl", "punct"]}, {"id": "20", "sentence": "Decent wine at reasonable prices .", "triples": [{"uid": "20-0", "sentiment": "positive", "target_tags": "Decent\\O wine\\B at\\O reasonable\\O prices\\O .\\O", "opinion_tags": "Decent\\B wine\\O at\\O reasonable\\O prices\\O .\\O"}, {"uid": "20-1", "sentiment": "positive", "target_tags": "Decent\\O wine\\O at\\O reasonable\\O prices\\B .\\O", "opinion_tags": "Decent\\O wine\\O at\\O reasonable\\B prices\\O .\\O"}], "postag": ["JJ", "NN", "IN", "JJ", "NNS", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["amod", "root", "case", "amod", "nmod", "punct"]}, {"id": "21", "sentence": "even the wine by the glass was good .", "triples": [{"uid": "21-0", "sentiment": "positive", "target_tags": "even\\O the\\O wine\\B by\\I the\\I glass\\I was\\O good\\O .\\O", "opinion_tags": "even\\O the\\O wine\\O by\\O the\\O glass\\O was\\O good\\B .\\O"}], "postag": ["RB", "DT", "NN", "IN", "DT", "NN", "VBD", "JJ", "."], "head": [3, 3, 8, 6, 6, 3, 8, 0, 8], "deprel": ["advmod", "det", "nsubj", "case", "det", "nmod", "cop", "root", "punct"]}, {"id": "22", "sentence": "This dish is my favorite and I always get it when I go there and never get tired of it .", "triples": [{"uid": "22-0", "sentiment": "positive", "target_tags": "This\\O dish\\B is\\O my\\O favorite\\O and\\O I\\O always\\O get\\O it\\O when\\O I\\O go\\O there\\O and\\O never\\O get\\O tired\\O of\\O it\\O .\\O", "opinion_tags": "This\\O dish\\O is\\O my\\O favorite\\B and\\O I\\O always\\O get\\O it\\O when\\O I\\O go\\O there\\O and\\O never\\O get\\O tired\\O of\\O it\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "PRP$", "NN", "CC", "PRP", "RB", "VBP", "PRP", "WRB", "PRP", "VBP", "RB", "CC", "RB", "VBP", "JJ", "IN", "PRP", "."], "head": [2, 5, 5, 5, 0, 9, 9, 9, 5, 9, 13, 13, 9, 13, 17, 17, 9, 17, 20, 18, 5], "deprel": ["det", "nsubj", "cop", "nmod:poss", "root", "cc", "nsubj", "advmod", "conj", "obj", "mark", "nsubj", "advcl", "advmod", "cc", "advmod", "conj", "xcomp", "case", "obl", "punct"]}, {"id": "23", "sentence": "The food was just awful , ATROCIOUS actually .", "triples": [{"uid": "23-0", "sentiment": "negative", "target_tags": "The\\O food\\B was\\O just\\O awful\\O ,\\O ATROCIOUS\\O actually\\O .\\O", "opinion_tags": "The\\O food\\O was\\O just\\O awful\\B ,\\O ATROCIOUS\\O actually\\O .\\O"}, {"uid": "23-1", "sentiment": "negative", "target_tags": "The\\O food\\B was\\O just\\O awful\\O ,\\O ATROCIOUS\\O actually\\O .\\O", "opinion_tags": "The\\O food\\O was\\O just\\O awful\\O ,\\O ATROCIOUS\\B actually\\O .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", ",", "JJ", "RB", "."], "head": [2, 5, 5, 5, 0, 5, 5, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "conj", "advmod", "punct"]}, {"id": "24", "sentence": "Try their plain pizza with fresh garlic or eggplant .", "triples": [{"uid": "24-0", "sentiment": "positive", "target_tags": "Try\\O their\\O plain\\B pizza\\I with\\O fresh\\O garlic\\O or\\O eggplant\\O .\\O", "opinion_tags": "Try\\B their\\O plain\\O pizza\\O with\\O fresh\\O garlic\\O or\\O eggplant\\O .\\O"}, {"uid": "24-1", "sentiment": "positive", "target_tags": "Try\\O their\\O plain\\O pizza\\O with\\O fresh\\O garlic\\B or\\O eggplant\\O .\\O", "opinion_tags": "Try\\O their\\O plain\\O pizza\\O with\\O fresh\\B garlic\\O or\\O eggplant\\O .\\O"}], "postag": ["VB", "PRP$", "JJ", "NN", "IN", "JJ", "NN", "CC", "NN", "."], "head": [0, 4, 4, 1, 7, 7, 1, 9, 7, 1], "deprel": ["root", "nmod:poss", "amod", "obj", "case", "amod", "obl", "cc", "conj", "punct"]}, {"id": "25", "sentence": "The service is awful .", "triples": [{"uid": "25-0", "sentiment": "negative", "target_tags": "The\\O service\\B is\\O awful\\O .\\O", "opinion_tags": "The\\O service\\O is\\O awful\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"]}, {"id": "26", "sentence": "I recently went to this restaurant with some co-workers for lunch and had an amazing time .", "triples": [{"uid": "26-0", "sentiment": "neutral", "target_tags": "I\\O recently\\O went\\O to\\O this\\O restaurant\\O with\\O some\\O co-workers\\O for\\O lunch\\B and\\O had\\O an\\O amazing\\O time\\O .\\O", "opinion_tags": "I\\O recently\\O went\\O to\\O this\\O restaurant\\O with\\O some\\O co-workers\\O for\\O lunch\\O and\\O had\\O an\\O amazing\\B time\\O .\\O"}], "postag": ["PRP", "RB", "VBD", "IN", "DT", "NN", "IN", "DT", "NNS", "IN", "NN", "CC", "VBD", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 3, 11, 9, 13, 3, 16, 16, 13, 3], "deprel": ["nsubj", "advmod", "root", "case", "det", "obl", "case", "det", "obl", "case", "nmod", "cc", "conj", "det", "amod", "obj", "punct"]}, {"id": "27", "sentence": "Ess-A-Bagel ( either by Sty-town or midtown ) is by far the best bagel in NY .", "triples": [{"uid": "27-0", "sentiment": "positive", "target_tags": "Ess-A-Bagel\\O (\\O either\\O by\\O Sty-town\\O or\\O midtown\\O )\\O is\\O by\\O far\\O the\\O best\\O bagel\\B in\\O NY\\O .\\O", "opinion_tags": "Ess-A-Bagel\\O (\\O either\\O by\\O Sty-town\\O or\\O midtown\\O )\\O is\\O by\\O far\\O the\\O best\\B bagel\\O in\\O NY\\O .\\O"}], "postag": ["NNP", "-LRB-", "CC", "IN", "NNP", "CC", "NN", "-RRB-", "VBZ", "IN", "RB", "DT", "JJS", "NN", "IN", "NNP", "."], "head": [14, 5, 5, 5, 1, 7, 5, 5, 14, 11, 14, 14, 14, 0, 16, 14, 14], "deprel": ["nsubj", "punct", "cc:preconj", "case", "nmod", "cc", "conj", "punct", "cop", "case", "obl", "det", "amod", "root", "case", "nmod", "punct"]}, {"id": "28", "sentence": "The sake menu should not be overlooked !", "triples": [{"uid": "28-0", "sentiment": "positive", "target_tags": "The\\O sake\\B menu\\I should\\O not\\O be\\O overlooked\\O !\\O", "opinion_tags": "The\\O sake\\O menu\\O should\\O not\\O be\\O overlooked\\B !\\O"}], "postag": ["DT", "NN", "NN", "MD", "RB", "VB", "VBN", "."], "head": [3, 3, 7, 7, 7, 7, 0, 7], "deprel": ["det", "compound", "nsubj:pass", "aux", "advmod", "aux:pass", "root", "punct"]}, {"id": "29", "sentence": "The service , wine selection , ambiance are all outstanding and deserve recognition .", "triples": [{"uid": "29-0", "sentiment": "positive", "target_tags": "The\\O service\\B ,\\O wine\\O selection\\O ,\\O ambiance\\O are\\O all\\O outstanding\\O and\\O deserve\\O recognition\\O .\\O", "opinion_tags": "The\\O service\\O ,\\O wine\\O selection\\O ,\\O ambiance\\O are\\O all\\O outstanding\\B and\\O deserve\\O recognition\\O .\\O"}, {"uid": "29-1", "sentiment": "positive", "target_tags": "The\\O service\\O ,\\O wine\\B selection\\I ,\\O ambiance\\O are\\O all\\O outstanding\\O and\\O deserve\\O recognition\\O .\\O", "opinion_tags": "The\\O service\\O ,\\O wine\\O selection\\O ,\\O ambiance\\O are\\O all\\O outstanding\\B and\\O deserve\\O recognition\\O .\\O"}, {"uid": "29-2", "sentiment": "positive", "target_tags": "The\\O service\\O ,\\O wine\\O selection\\O ,\\O ambiance\\B are\\O all\\O outstanding\\O and\\O deserve\\O recognition\\O .\\O", "opinion_tags": "The\\O service\\O ,\\O wine\\O selection\\O ,\\O ambiance\\O are\\O all\\O outstanding\\B and\\O deserve\\O recognition\\O .\\O"}], "postag": ["DT", "NN", ",", "NN", "NN", ",", "NN", "VBP", "RB", "JJ", "CC", "VBP", "NN", "."], "head": [2, 10, 5, 5, 2, 7, 2, 10, 10, 0, 12, 10, 12, 10], "deprel": ["det", "nsubj", "punct", "compound", "conj", "punct", "conj", "cop", "advmod", "root", "cc", "conj", "obj", "punct"]}, {"id": "30", "sentence": "It 's really also the service , is good and the waiters are friendly .", "triples": [{"uid": "30-0", "sentiment": "positive", "target_tags": "It\\O 's\\O really\\O also\\O the\\O service\\B ,\\O is\\O good\\O and\\O the\\O waiters\\O are\\O friendly\\O .\\O", "opinion_tags": "It\\O 's\\O really\\O also\\O the\\O service\\O ,\\O is\\O good\\B and\\O the\\O waiters\\O are\\O friendly\\O .\\O"}, {"uid": "30-1", "sentiment": "positive", "target_tags": "It\\O 's\\O really\\O also\\O the\\O service\\O ,\\O is\\O good\\O and\\O the\\O waiters\\B are\\O friendly\\O .\\O", "opinion_tags": "It\\O 's\\O really\\O also\\O the\\O service\\O ,\\O is\\O good\\O and\\O the\\O waiters\\O are\\O friendly\\B .\\O"}], "postag": ["PRP", "VBZ", "RB", "RB", "DT", "NN", ",", "VBZ", "JJ", "CC", "DT", "NNS", "VBP", "JJ", "."], "head": [6, 6, 6, 6, 6, 0, 6, 9, 6, 14, 12, 14, 14, 6, 6], "deprel": ["nsubj", "cop", "advmod", "advmod", "det", "root", "punct", "cop", "parataxis", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "31", "sentence": "I had their eggs benedict for brunch , which were the worst in my entire life , I tried removing the hollondaise sauce completely that was how failed it was .", "triples": [{"uid": "31-0", "sentiment": "negative", "target_tags": "I\\O had\\O their\\O eggs\\B benedict\\I for\\O brunch\\O ,\\O which\\O were\\O the\\O worst\\O in\\O my\\O entire\\O life\\O ,\\O I\\O tried\\O removing\\O the\\O hollondaise\\O sauce\\O completely\\O that\\O was\\O how\\O failed\\O it\\O was\\O .\\O", "opinion_tags": "I\\O had\\O their\\O eggs\\O benedict\\O for\\O brunch\\O ,\\O which\\O were\\O the\\O worst\\B in\\O my\\O entire\\O life\\O ,\\O I\\O tried\\O removing\\O the\\O hollondaise\\O sauce\\O completely\\O that\\O was\\O how\\O failed\\O it\\O was\\O .\\O"}, {"uid": "31-1", "sentiment": "negative", "target_tags": "I\\O had\\O their\\O eggs\\O benedict\\O for\\O brunch\\O ,\\O which\\O were\\O the\\O worst\\O in\\O my\\O entire\\O life\\O ,\\O I\\O tried\\O removing\\O the\\O hollondaise\\B sauce\\I completely\\O that\\O was\\O how\\O failed\\O it\\O was\\O .\\O", "opinion_tags": "I\\O had\\O their\\O eggs\\O benedict\\O for\\O brunch\\O ,\\O which\\O were\\O the\\O worst\\O in\\O my\\O entire\\O life\\O ,\\O I\\O tried\\O removing\\O the\\O hollondaise\\O sauce\\O completely\\O that\\O was\\O how\\O failed\\B it\\O was\\O .\\O"}, {"uid": "31-2", "sentiment": "neutral", "target_tags": "I\\O had\\O their\\O eggs\\O benedict\\O for\\O brunch\\B ,\\O which\\O were\\O the\\O worst\\O in\\O my\\O entire\\O life\\O ,\\O I\\O tried\\O removing\\O the\\O hollondaise\\O sauce\\O completely\\O that\\O was\\O how\\O failed\\O it\\O was\\O .\\O", "opinion_tags": "I\\O had\\O their\\O eggs\\O benedict\\O for\\O brunch\\O ,\\O which\\O were\\O the\\O worst\\B in\\O my\\O entire\\O life\\O ,\\O I\\O tried\\O removing\\O the\\O hollondaise\\O sauce\\O completely\\O that\\O was\\O how\\O failed\\O it\\O was\\O .\\O"}], "postag": ["PRP", "VBD", "PRP$", "NNS", "NN", "IN", "NN", ",", "WDT", "VBD", "DT", "JJS", "IN", "PRP$", "JJ", "NN", ",", "PRP", "VBD", "VBG", "DT", "NN", "NN", "RB", "DT", "VBD", "WRB", "VBD", "PRP", "VBD", "."], "head": [2, 0, 5, 5, 2, 7, 2, 7, 12, 12, 12, 7, 16, 16, 16, 12, 2, 19, 2, 19, 23, 23, 20, 26, 26, 2, 28, 26, 30, 28, 2], "deprel": ["nsubj", "root", "nmod:poss", "compound", "obj", "case", "obl", "punct", "nsubj", "cop", "det", "acl:relcl", "case", "nmod:poss", "amod", "obl", "punct", "nsubj", "parataxis", "xcomp", "det", "compound", "obj", "advmod", "nsubj", "parataxis", "mark", "ccomp", "nsubj", "ccomp", "punct"]}, {"id": "32", "sentence": "The food was really good , I had the onion soup and it was one of the best ever .", "triples": [{"uid": "32-0", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O really\\O good\\O ,\\O I\\O had\\O the\\O onion\\O soup\\O and\\O it\\O was\\O one\\O of\\O the\\O best\\O ever\\O .\\O", "opinion_tags": "The\\O food\\O was\\O really\\O good\\B ,\\O I\\O had\\O the\\O onion\\O soup\\O and\\O it\\O was\\O one\\O of\\O the\\O best\\O ever\\O .\\O"}, {"uid": "32-1", "sentiment": "positive", "target_tags": "The\\O food\\O was\\O really\\O good\\O ,\\O I\\O had\\O the\\O onion\\B soup\\I and\\O it\\O was\\O one\\O of\\O the\\O best\\O ever\\O .\\O", "opinion_tags": "The\\O food\\O was\\O really\\O good\\O ,\\O I\\O had\\O the\\O onion\\O soup\\O and\\O it\\O was\\O one\\O of\\O the\\O best\\B ever\\O .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", ",", "PRP", "VBD", "DT", "NN", "NN", "CC", "PRP", "VBD", "CD", "IN", "DT", "JJS", "RB", "."], "head": [2, 5, 5, 5, 0, 5, 8, 5, 11, 11, 8, 15, 15, 15, 5, 18, 18, 15, 15, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "nsubj", "parataxis", "det", "compound", "obj", "cc", "nsubj", "cop", "conj", "case", "det", "nmod", "advmod", "punct"]}, {"id": "33", "sentence": "The only thing I moderately enjoyed was their Grilled Chicken special with Edamame Puree .", "triples": [{"uid": "33-0", "sentiment": "neutral", "target_tags": "The\\O only\\O thing\\O I\\O moderately\\O enjoyed\\O was\\O their\\O Grilled\\B Chicken\\I special\\I with\\I Edamame\\I Puree\\I .\\O", "opinion_tags": "The\\O only\\O thing\\O I\\O moderately\\O enjoyed\\B was\\O their\\O Grilled\\O Chicken\\O special\\O with\\O Edamame\\O Puree\\O .\\O"}], "postag": ["DT", "JJ", "NN", "PRP", "RB", "VBD", "VBD", "PRP$", "VBN", "NN", "NN", "IN", "NNP", "NNP", "."], "head": [3, 3, 11, 6, 6, 3, 11, 11, 11, 11, 0, 14, 14, 11, 11], "deprel": ["det", "amod", "nsubj", "nsubj", "advmod", "acl:relcl", "cop", "nmod:poss", "amod", "compound", "root", "case", "compound", "nmod", "punct"]}, {"id": "34", "sentence": "They are tasty , but I suggest only eating one with meat because they tend not to mesh that well with the average American digestive system .", "triples": [{"uid": "34-0", "sentiment": "neutral", "target_tags": "They\\O are\\O tasty\\O ,\\O but\\O I\\O suggest\\O only\\O eating\\O one\\O with\\O meat\\B because\\O they\\O tend\\O not\\O to\\O mesh\\O that\\O well\\O with\\O the\\O average\\O American\\O digestive\\O system\\O .\\O", "opinion_tags": "They\\O are\\O tasty\\B ,\\O but\\O I\\O suggest\\O only\\O eating\\O one\\O with\\O meat\\O because\\O they\\O tend\\O not\\O to\\O mesh\\O that\\O well\\O with\\O the\\O average\\O American\\O digestive\\O system\\O .\\O"}, {"uid": "34-1", "sentiment": "neutral", "target_tags": "They\\O are\\O tasty\\O ,\\O but\\O I\\O suggest\\O only\\O eating\\O one\\O with\\O meat\\B because\\O they\\O tend\\O not\\O to\\O mesh\\O that\\O well\\O with\\O the\\O average\\O American\\O digestive\\O system\\O .\\O", "opinion_tags": "They\\O are\\O tasty\\O ,\\O but\\O I\\O suggest\\B only\\O eating\\O one\\O with\\O meat\\O because\\O they\\O tend\\O not\\O to\\O mesh\\O that\\O well\\O with\\O the\\O average\\O American\\O digestive\\O system\\O .\\O"}], "postag": ["PRP", "VBP", "JJ", ",", "CC", "PRP", "VBP", "RB", "VBG", "CD", "IN", "NN", "IN", "PRP", "VBP", "RB", "TO", "VB", "RB", "RB", "IN", "DT", "JJ", "JJ", "JJ", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 9, 7, 9, 12, 9, 15, 15, 9, 18, 18, 15, 20, 18, 26, 26, 26, 26, 26, 18, 3], "deprel": ["nsubj", "cop", "root", "punct", "cc", "nsubj", "conj", "advmod", "xcomp", "obj", "case", "obl", "mark", "nsubj", "advcl", "advmod", "mark", "xcomp", "advmod", "advmod", "case", "det", "amod", "amod", "amod", "obl", "punct"]}, {"id": "35", "sentence": "Delivery is fast too .", "triples": [{"uid": "35-0", "sentiment": "positive", "target_tags": "Delivery\\B is\\O fast\\O too\\O .\\O", "opinion_tags": "Delivery\\O is\\O fast\\B too\\O .\\O"}], "postag": ["NN", "VBZ", "JJ", "RB", "."], "head": [3, 3, 0, 3, 3], "deprel": ["nsubj", "cop", "root", "advmod", "punct"]}, {"id": "36", "sentence": "The atmosphere is great if your looking for a laid back scene and an inexpensive way to spend a weekend afternoon .", "triples": [{"uid": "36-0", "sentiment": "positive", "target_tags": "The\\O atmosphere\\B is\\O great\\O if\\O your\\O looking\\O for\\O a\\O laid\\O back\\O scene\\O and\\O an\\O inexpensive\\O way\\O to\\O spend\\O a\\O weekend\\O afternoon\\O .\\O", "opinion_tags": "The\\O atmosphere\\O is\\O great\\B if\\O your\\O looking\\O for\\O a\\O laid\\O back\\O scene\\O and\\O an\\O inexpensive\\O way\\O to\\O spend\\O a\\O weekend\\O afternoon\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "IN", "PRP$", "VBG", "IN", "DT", "VBN", "NN", "NN", "CC", "DT", "JJ", "NN", "TO", "VB", "DT", "NN", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 12, 12, 12, 12, 7, 16, 16, 16, 12, 18, 16, 21, 21, 18, 4], "deprel": ["det", "nsubj", "cop", "root", "mark", "nsubj", "advcl", "case", "det", "amod", "compound", "obl", "cc", "det", "amod", "conj", "mark", "acl", "det", "compound", "obl:tmod", "punct"]}, {"id": "37", "sentence": "I had the salmon dish and while it was fine , for the price paid , I expected it to have some type of flavor .", "triples": [{"uid": "37-0", "sentiment": "negative", "target_tags": "I\\O had\\O the\\O salmon\\O dish\\O and\\O while\\O it\\O was\\O fine\\O ,\\O for\\O the\\O price\\O paid\\O ,\\O I\\O expected\\O it\\O to\\O have\\O some\\O type\\O of\\O flavor\\B .\\O", "opinion_tags": "I\\O had\\O the\\O salmon\\O dish\\O and\\O while\\O it\\O was\\O fine\\B ,\\O for\\O the\\O price\\O paid\\O ,\\O I\\O expected\\O it\\O to\\O have\\O some\\O type\\O of\\O flavor\\O .\\O"}, {"uid": "37-1", "sentiment": "neutral", "target_tags": "I\\O had\\O the\\O salmon\\O dish\\O and\\O while\\O it\\O was\\O fine\\O ,\\O for\\O the\\O price\\B paid\\O ,\\O I\\O expected\\O it\\O to\\O have\\O some\\O type\\O of\\O flavor\\O .\\O", "opinion_tags": "I\\O had\\O the\\O salmon\\O dish\\O and\\O while\\O it\\O was\\O fine\\B ,\\O for\\O the\\O price\\O paid\\O ,\\O I\\O expected\\O it\\O to\\O have\\O some\\O type\\O of\\O flavor\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "NN", "NN", "CC", "IN", "PRP", "VBD", "JJ", ",", "IN", "DT", "NN", "VBN", ",", "PRP", "VBD", "PRP", "TO", "VB", "DT", "NN", "IN", "NN", "."], "head": [2, 0, 5, 5, 2, 18, 10, 10, 10, 18, 18, 15, 14, 15, 10, 18, 18, 2, 18, 21, 18, 23, 21, 25, 23, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "cc", "mark", "nsubj", "cop", "advcl", "punct", "mark", "det", "nsubj", "advcl", "punct", "nsubj", "conj", "obj", "mark", "xcomp", "det", "obj", "case", "nmod", "punct"]}, {"id": "38", "sentence": "Especially liked chicken tikka and the naan , and the dals .", "triples": [{"uid": "38-0", "sentiment": "positive", "target_tags": "Especially\\O liked\\O chicken\\B tikka\\I and\\O the\\O naan\\O ,\\O and\\O the\\O dals\\O .\\O", "opinion_tags": "Especially\\O liked\\B chicken\\O tikka\\O and\\O the\\O naan\\O ,\\O and\\O the\\O dals\\O .\\O"}, {"uid": "38-1", "sentiment": "positive", "target_tags": "Especially\\O liked\\O chicken\\O tikka\\O and\\O the\\O naan\\B ,\\O and\\O the\\O dals\\O .\\O", "opinion_tags": "Especially\\O liked\\B chicken\\O tikka\\O and\\O the\\O naan\\O ,\\O and\\O the\\O dals\\O .\\O"}, {"uid": "38-2", "sentiment": "positive", "target_tags": "Especially\\O liked\\O chicken\\O tikka\\O and\\O the\\O naan\\O ,\\O and\\O the\\O dals\\B .\\O", "opinion_tags": "Especially\\O liked\\B chicken\\O tikka\\O and\\O the\\O naan\\O ,\\O and\\O the\\O dals\\O .\\O"}], "postag": ["RB", "VBN", "NN", "NN", "CC", "DT", "NN", ",", "CC", "DT", "NNS", "."], "head": [4, 4, 4, 0, 7, 7, 4, 11, 11, 11, 4, 4], "deprel": ["advmod", "amod", "compound", "root", "cc", "det", "conj", "punct", "cc", "det", "conj", "punct"]}, {"id": "39", "sentence": "The restaurant looks out over beautiful green lawns to the Hudson River and the Statue of Liberty .", "triples": [{"uid": "39-0", "sentiment": "positive", "target_tags": "The\\O restaurant\\O looks\\O out\\O over\\O beautiful\\O green\\O lawns\\B to\\O the\\O Hudson\\O River\\O and\\O the\\O Statue\\O of\\O Liberty\\O .\\O", "opinion_tags": "The\\O restaurant\\O looks\\O out\\O over\\O beautiful\\B green\\O lawns\\O to\\O the\\O Hudson\\O River\\O and\\O the\\O Statue\\O of\\O Liberty\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RP", "IN", "JJ", "JJ", "NNS", "IN", "DT", "NNP", "NNP", "CC", "DT", "NNP", "IN", "NNP", "."], "head": [2, 3, 0, 3, 8, 8, 8, 3, 12, 12, 12, 8, 15, 15, 12, 17, 15, 3], "deprel": ["det", "nsubj", "root", "compound:prt", "case", "amod", "amod", "obl", "case", "det", "compound", "nmod", "cc", "det", "conj", "case", "nmod", "punct"]}, {"id": "40", "sentence": "The overall price tag was very very expensive , something I did expect .", "triples": [{"uid": "40-0", "sentiment": "negative", "target_tags": "The\\O overall\\O price\\B tag\\I was\\O very\\O very\\O expensive\\O ,\\O something\\O I\\O did\\O expect\\O .\\O", "opinion_tags": "The\\O overall\\O price\\O tag\\O was\\O very\\O very\\O expensive\\B ,\\O something\\O I\\O did\\O expect\\O .\\O"}], "postag": ["DT", "JJ", "NN", "NN", "VBD", "RB", "RB", "JJ", ",", "NN", "PRP", "VBD", "VB", "."], "head": [4, 4, 4, 8, 8, 8, 8, 0, 8, 8, 13, 13, 10, 8], "deprel": ["det", "amod", "compound", "nsubj", "cop", "advmod", "advmod", "root", "punct", "parataxis", "nsubj", "aux", "acl:relcl", "punct"]}, {"id": "41", "sentence": "Good food .", "triples": [{"uid": "41-0", "sentiment": "positive", "target_tags": "Good\\O food\\B .\\O", "opinion_tags": "Good\\B food\\O .\\O"}], "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"]}, {"id": "42", "sentence": "Its a great place for a casual date or to entertain clients for lunch .", "triples": [{"uid": "42-0", "sentiment": "neutral", "target_tags": "Its\\O a\\O great\\O place\\O for\\O a\\O casual\\O date\\O or\\O to\\O entertain\\O clients\\O for\\O lunch\\B .\\O", "opinion_tags": "Its\\O a\\O great\\B place\\O for\\O a\\O casual\\O date\\O or\\O to\\O entertain\\O clients\\O for\\O lunch\\O .\\O"}], "postag": ["PRP$", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", "CC", "TO", "VB", "NNS", "IN", "NN", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 11, 11, 4, 11, 14, 11, 4], "deprel": ["nmod:poss", "det", "amod", "root", "case", "det", "amod", "nmod", "cc", "mark", "conj", "obj", "case", "obl", "punct"]}, {"id": "43", "sentence": "Plain and simple it 's bad thai food .", "triples": [{"uid": "43-0", "sentiment": "negative", "target_tags": "Plain\\O and\\O simple\\O it\\O 's\\O bad\\O thai\\B food\\I .\\O", "opinion_tags": "Plain\\O and\\O simple\\O it\\O 's\\O bad\\B thai\\O food\\O .\\O"}], "postag": ["JJ", "CC", "JJ", "PRP", "VBZ", "JJ", "JJ", "NN", "."], "head": [8, 3, 1, 8, 8, 8, 8, 0, 8], "deprel": ["amod", "cc", "conj", "nsubj", "cop", "amod", "amod", "root", "punct"]}, {"id": "44", "sentence": "While certain staples are excellent ( the burger , some of the pastas ) , the food is not really the point .", "triples": [{"uid": "44-0", "sentiment": "positive", "target_tags": "While\\O certain\\O staples\\O are\\O excellent\\O (\\O the\\O burger\\B ,\\O some\\O of\\O the\\O pastas\\O )\\O ,\\O the\\O food\\O is\\O not\\O really\\O the\\O point\\O .\\O", "opinion_tags": "While\\O certain\\O staples\\O are\\O excellent\\B (\\O the\\O burger\\O ,\\O some\\O of\\O the\\O pastas\\O )\\O ,\\O the\\O food\\O is\\O not\\O really\\O the\\O point\\O .\\O"}, {"uid": "44-1", "sentiment": "positive", "target_tags": "While\\O certain\\O staples\\O are\\O excellent\\O (\\O the\\O burger\\O ,\\O some\\O of\\O the\\O pastas\\B )\\O ,\\O the\\O food\\O is\\O not\\O really\\O the\\O point\\O .\\O", "opinion_tags": "While\\O certain\\O staples\\O are\\O excellent\\B (\\O the\\O burger\\O ,\\O some\\O of\\O the\\O pastas\\O )\\O ,\\O the\\O food\\O is\\O not\\O really\\O the\\O point\\O .\\O"}, {"uid": "44-2", "sentiment": "neutral", "target_tags": "While\\O certain\\O staples\\O are\\O excellent\\O (\\O the\\O burger\\O ,\\O some\\O of\\O the\\O pastas\\O )\\O ,\\O the\\O food\\B is\\O not\\O really\\O the\\O point\\O .\\O", "opinion_tags": "While\\O certain\\O staples\\O are\\O excellent\\O (\\O the\\O burger\\O ,\\O some\\O of\\O the\\O pastas\\O )\\O ,\\O the\\O food\\O is\\O not\\B really\\I the\\I point\\I .\\O"}, {"uid": "44-3", "sentiment": "positive", "target_tags": "While\\O certain\\O staples\\B are\\O excellent\\O (\\O the\\O burger\\O ,\\O some\\O of\\O the\\O pastas\\O )\\O ,\\O the\\O food\\O is\\O not\\O really\\O the\\O point\\O .\\O", "opinion_tags": "While\\O certain\\O staples\\O are\\O excellent\\B (\\O the\\O burger\\O ,\\O some\\O of\\O the\\O pastas\\O )\\O ,\\O the\\O food\\O is\\O not\\O really\\O the\\O point\\O .\\O"}], "postag": ["IN", "JJ", "NNS", "VBP", "JJ", "-LRB-", "DT", "NN", ",", "DT", "IN", "DT", "NNS", "-RRB-", ",", "DT", "NN", "VBZ", "RB", "RB", "DT", "NN", "."], "head": [5, 3, 5, 5, 22, 8, 8, 5, 10, 8, 13, 13, 10, 8, 22, 17, 22, 22, 22, 22, 22, 0, 22], "deprel": ["mark", "amod", "nsubj", "cop", "advcl", "punct", "det", "parataxis", "punct", "appos", "case", "det", "nmod", "punct", "punct", "det", "nsubj", "cop", "advmod", "advmod", "det", "root", "punct"]}, {"id": "45", "sentence": "good music , great food , speedy service affordable prices .", "triples": [{"uid": "45-0", "sentiment": "positive", "target_tags": "good\\O music\\B ,\\O great\\O food\\O ,\\O speedy\\O service\\O affordable\\O prices\\O .\\O", "opinion_tags": "good\\B music\\O ,\\O great\\O food\\O ,\\O speedy\\O service\\O affordable\\O prices\\O .\\O"}, {"uid": "45-1", "sentiment": "positive", "target_tags": "good\\O music\\O ,\\O great\\O food\\B ,\\O speedy\\O service\\O affordable\\O prices\\O .\\O", "opinion_tags": "good\\O music\\O ,\\O great\\B food\\O ,\\O speedy\\O service\\O affordable\\O prices\\O .\\O"}, {"uid": "45-2", "sentiment": "positive", "target_tags": "good\\O music\\O ,\\O great\\O food\\O ,\\O speedy\\O service\\B affordable\\O prices\\O .\\O", "opinion_tags": "good\\O music\\O ,\\O great\\O food\\O ,\\O speedy\\B service\\O affordable\\O prices\\O .\\O"}, {"uid": "45-3", "sentiment": "positive", "target_tags": "good\\O music\\O ,\\O great\\O food\\O ,\\O speedy\\O service\\O affordable\\O prices\\B .\\O", "opinion_tags": "good\\O music\\O ,\\O great\\O food\\O ,\\O speedy\\O service\\O affordable\\B prices\\O .\\O"}], "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "JJ", "NN", "JJ", "NNS", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 10, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "amod", "conj", "amod", "conj", "punct"]}, {"id": "46", "sentence": "The sides were ok and incredibly salty .", "triples": [{"uid": "46-0", "sentiment": "negative", "target_tags": "The\\O sides\\B were\\O ok\\O and\\O incredibly\\O salty\\O .\\O", "opinion_tags": "The\\O sides\\O were\\O ok\\B and\\O incredibly\\O salty\\O .\\O"}, {"uid": "46-1", "sentiment": "negative", "target_tags": "The\\O sides\\B were\\O ok\\O and\\O incredibly\\O salty\\O .\\O", "opinion_tags": "The\\O sides\\O were\\O ok\\O and\\O incredibly\\O salty\\B .\\O"}], "postag": ["DT", "NNS", "VBD", "JJ", "CC", "RB", "JJ", "."], "head": [2, 4, 4, 0, 7, 7, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "advmod", "conj", "punct"]}, {"id": "47", "sentence": "The hostess and the waitress were incredibly rude and did everything they could to rush us out .", "triples": [{"uid": "47-0", "sentiment": "negative", "target_tags": "The\\O hostess\\B and\\O the\\O waitress\\O were\\O incredibly\\O rude\\O and\\O did\\O everything\\O they\\O could\\O to\\O rush\\O us\\O out\\O .\\O", "opinion_tags": "The\\O hostess\\O and\\O the\\O waitress\\O were\\O incredibly\\O rude\\B and\\O did\\O everything\\O they\\O could\\O to\\O rush\\O us\\O out\\O .\\O"}, {"uid": "47-1", "sentiment": "negative", "target_tags": "The\\O hostess\\O and\\O the\\O waitress\\B were\\O incredibly\\O rude\\O and\\O did\\O everything\\O they\\O could\\O to\\O rush\\O us\\O out\\O .\\O", "opinion_tags": "The\\O hostess\\O and\\O the\\O waitress\\O were\\O incredibly\\O rude\\B and\\O did\\O everything\\O they\\O could\\O to\\O rush\\O us\\O out\\O .\\O"}], "postag": ["DT", "NN", "CC", "DT", "NN", "VBD", "RB", "JJ", "CC", "VBD", "NN", "PRP", "MD", "TO", "VB", "PRP", "RP", "."], "head": [2, 8, 5, 5, 2, 8, 8, 0, 10, 8, 10, 13, 11, 15, 10, 15, 15, 8], "deprel": ["det", "nsubj", "cc", "det", "conj", "cop", "advmod", "root", "cc", "conj", "obj", "nsubj", "acl:relcl", "mark", "advcl", "obj", "compound:prt", "punct"]}, {"id": "48", "sentence": "I must say the view of NYC is so beautiful !", "triples": [{"uid": "48-0", "sentiment": "positive", "target_tags": "I\\O must\\O say\\O the\\O view\\B of\\O NYC\\O is\\O so\\O beautiful\\O !\\O", "opinion_tags": "I\\O must\\O say\\O the\\O view\\O of\\O NYC\\O is\\O so\\O beautiful\\B !\\O"}], "postag": ["PRP", "MD", "VB", "DT", "NN", "IN", "NNP", "VBZ", "RB", "JJ", "."], "head": [3, 3, 0, 5, 10, 7, 5, 10, 10, 3, 3], "deprel": ["nsubj", "aux", "root", "det", "nsubj", "case", "nmod", "cop", "advmod", "ccomp", "punct"]}, {"id": "49", "sentence": "Otherwise , this place has great service and prices and a nice friendly atmosphere .", "triples": [{"uid": "49-0", "sentiment": "positive", "target_tags": "Otherwise\\O ,\\O this\\O place\\O has\\O great\\O service\\B and\\O prices\\O and\\O a\\O nice\\O friendly\\O atmosphere\\O .\\O", "opinion_tags": "Otherwise\\O ,\\O this\\O place\\O has\\O great\\B service\\O and\\O prices\\O and\\O a\\O nice\\O friendly\\O atmosphere\\O .\\O"}, {"uid": "49-1", "sentiment": "positive", "target_tags": "Otherwise\\O ,\\O this\\O place\\O has\\O great\\O service\\O and\\O prices\\B and\\O a\\O nice\\O friendly\\O atmosphere\\O .\\O", "opinion_tags": "Otherwise\\O ,\\O this\\O place\\O has\\O great\\B service\\O and\\O prices\\O and\\O a\\O nice\\O friendly\\O atmosphere\\O .\\O"}, {"uid": "49-2", "sentiment": "positive", "target_tags": "Otherwise\\O ,\\O this\\O place\\O has\\O great\\O service\\O and\\O prices\\O and\\O a\\O nice\\O friendly\\O atmosphere\\B .\\O", "opinion_tags": "Otherwise\\O ,\\O this\\O place\\O has\\O great\\O service\\O and\\O prices\\O and\\O a\\O nice\\B friendly\\I atmosphere\\O .\\O"}], "postag": ["RB", ",", "DT", "NN", "VBZ", "JJ", "NN", "CC", "NNS", "CC", "DT", "JJ", "JJ", "NN", "."], "head": [5, 5, 4, 5, 0, 7, 5, 9, 7, 14, 14, 14, 14, 7, 5], "deprel": ["advmod", "punct", "det", "nsubj", "root", "amod", "obj", "cc", "conj", "cc", "det", "amod", "amod", "conj", "punct"]}, {"id": "50", "sentence": "The desserts are more appealing then stuffy overpriced French restaurants .", "triples": [{"uid": "50-0", "sentiment": "positive", "target_tags": "The\\O desserts\\B are\\O more\\O appealing\\O then\\O stuffy\\O overpriced\\O French\\O restaurants\\O .\\O", "opinion_tags": "The\\O desserts\\O are\\O more\\O appealing\\B then\\O stuffy\\O overpriced\\O French\\O restaurants\\O .\\O"}], "postag": ["DT", "NNS", "VBP", "RBR", "JJ", "RB", "JJ", "JJ", "JJ", "NNS", "."], "head": [2, 5, 5, 5, 0, 10, 10, 10, 10, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "advmod", "amod", "amod", "amod", "obl", "punct"]}, {"id": "51", "sentence": "I found the food to be outstanding , particulary the salmon dish I had .", "triples": [{"uid": "51-0", "sentiment": "positive", "target_tags": "I\\O found\\O the\\O food\\B to\\O be\\O outstanding\\O ,\\O particulary\\O the\\O salmon\\O dish\\O I\\O had\\O .\\O", "opinion_tags": "I\\O found\\O the\\O food\\O to\\O be\\O outstanding\\B ,\\O particulary\\O the\\O salmon\\O dish\\O I\\O had\\O .\\O"}, {"uid": "51-1", "sentiment": "positive", "target_tags": "I\\O found\\O the\\O food\\O to\\O be\\O outstanding\\O ,\\O particulary\\O the\\O salmon\\B dish\\I I\\O had\\O .\\O", "opinion_tags": "I\\O found\\O the\\O food\\O to\\O be\\O outstanding\\B ,\\O particulary\\O the\\O salmon\\O dish\\O I\\O had\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "NN", "TO", "VB", "JJ", ",", "RB", "DT", "NN", "NN", "PRP", "VBD", "."], "head": [2, 0, 4, 2, 7, 7, 2, 12, 12, 12, 12, 2, 14, 12, 2], "deprel": ["nsubj", "root", "det", "obj", "mark", "cop", "xcomp", "punct", "advmod", "det", "compound", "parataxis", "nsubj", "acl:relcl", "punct"]}, {"id": "52", "sentence": "The atmosphere is noisy and the waiters are literally walking around doing things as fast as they can .", "triples": [{"uid": "52-0", "sentiment": "negative", "target_tags": "The\\O atmosphere\\B is\\O noisy\\O and\\O the\\O waiters\\O are\\O literally\\O walking\\O around\\O doing\\O things\\O as\\O fast\\O as\\O they\\O can\\O .\\O", "opinion_tags": "The\\O atmosphere\\O is\\O noisy\\B and\\O the\\O waiters\\O are\\O literally\\O walking\\O around\\O doing\\O things\\O as\\O fast\\O as\\O they\\O can\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NNS", "VBP", "RB", "VBG", "IN", "VBG", "NNS", "RB", "RB", "IN", "PRP", "MD", "."], "head": [2, 4, 4, 0, 10, 7, 10, 10, 10, 4, 12, 10, 12, 15, 12, 18, 18, 15, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "aux", "advmod", "conj", "mark", "advcl", "obj", "advmod", "advmod", "mark", "nsubj", "advcl", "punct"]}, {"id": "53", "sentence": "Pastrami or corned beef are juicy and piled high ( ask for extra rye bread ) .", "triples": [{"uid": "53-0", "sentiment": "positive", "target_tags": "Pastrami\\B or\\I corned\\I beef\\I are\\O juicy\\O and\\O piled\\O high\\O (\\O ask\\O for\\O extra\\O rye\\O bread\\O )\\O .\\O", "opinion_tags": "Pastrami\\O or\\O corned\\O beef\\O are\\O juicy\\B and\\O piled\\O high\\O (\\O ask\\O for\\O extra\\O rye\\O bread\\O )\\O .\\O"}, {"uid": "53-1", "sentiment": "positive", "target_tags": "Pastrami\\B or\\I corned\\I beef\\I are\\O juicy\\O and\\O piled\\O high\\O (\\O ask\\O for\\O extra\\O rye\\O bread\\O )\\O .\\O", "opinion_tags": "Pastrami\\O or\\O corned\\O beef\\O are\\O juicy\\O and\\O piled\\O high\\B (\\O ask\\O for\\O extra\\O rye\\O bread\\O )\\O .\\O"}], "postag": ["NN", "CC", "NN", "NN", "VBP", "JJ", "CC", "VBN", "JJ", "-LRB-", "VB", "IN", "JJ", "NN", "NN", "-RRB-", "."], "head": [4, 3, 1, 6, 6, 0, 8, 6, 6, 11, 6, 15, 15, 15, 11, 11, 6], "deprel": ["compound", "cc", "conj", "nsubj", "cop", "root", "cc", "conj", "conj", "punct", "parataxis", "case", "amod", "compound", "obl", "punct", "punct"]}, {"id": "54", "sentence": "( $ 200 for 2 glasses of champagne , not too expensive bottle of wine and 2 after dinner drinks ) .", "triples": [{"uid": "54-0", "sentiment": "negative", "target_tags": "(\\O $\\O 200\\O for\\O 2\\O glasses\\B of\\I champagne\\I ,\\O not\\O too\\O expensive\\O bottle\\O of\\O wine\\O and\\O 2\\O after\\O dinner\\O drinks\\O )\\O .\\O", "opinion_tags": "(\\O $\\O 200\\O for\\O 2\\O glasses\\O of\\O champagne\\O ,\\O not\\B too\\I expensive\\I bottle\\O of\\O wine\\O and\\O 2\\O after\\O dinner\\O drinks\\O )\\O .\\O"}, {"uid": "54-1", "sentiment": "negative", "target_tags": "(\\O $\\O 200\\O for\\O 2\\O glasses\\O of\\O champagne\\O ,\\O not\\O too\\O expensive\\O bottle\\B of\\I wine\\I and\\O 2\\O after\\O dinner\\O drinks\\O )\\O .\\O", "opinion_tags": "(\\O $\\O 200\\O for\\O 2\\O glasses\\O of\\O champagne\\O ,\\O not\\B too\\I expensive\\I bottle\\O of\\O wine\\O and\\O 2\\O after\\O dinner\\O drinks\\O )\\O .\\O"}], "postag": ["-LRB-", "$", "CD", "IN", "CD", "NNS", "IN", "NN", ",", "RB", "RB", "JJ", "NN", "IN", "NN", "CC", "CD", "IN", "NN", "NNS", "-RRB-", "."], "head": [2, 0, 2, 6, 6, 2, 8, 6, 13, 12, 12, 13, 6, 15, 13, 20, 20, 20, 20, 13, 2, 2], "deprel": ["punct", "root", "nummod", "case", "nummod", "nmod", "case", "nmod", "punct", "advmod", "advmod", "amod", "conj", "case", "nmod", "cc", "nummod", "case", "compound", "conj", "punct", "punct"]}, {"id": "55", "sentence": "otherwise , good stuff for late nite eats .", "triples": [{"uid": "55-0", "sentiment": "positive", "target_tags": "otherwise\\O ,\\O good\\O stuff\\B for\\O late\\O nite\\O eats\\O .\\O", "opinion_tags": "otherwise\\O ,\\O good\\B stuff\\O for\\O late\\O nite\\O eats\\O .\\O"}, {"uid": "55-1", "sentiment": "positive", "target_tags": "otherwise\\O ,\\O good\\O stuff\\O for\\O late\\O nite\\O eats\\B .\\O", "opinion_tags": "otherwise\\O ,\\O good\\O stuff\\O for\\O late\\B nite\\O eats\\O .\\O"}], "postag": ["RB", ",", "JJ", "NN", "IN", "JJ", "NN", "NNS", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 4], "deprel": ["advmod", "punct", "amod", "root", "case", "amod", "compound", "nmod", "punct"]}, {"id": "56", "sentence": "The dinner was ok , nothing I would have again .", "triples": [{"uid": "56-0", "sentiment": "negative", "target_tags": "The\\O dinner\\B was\\O ok\\O ,\\O nothing\\O I\\O would\\O have\\O again\\O .\\O", "opinion_tags": "The\\O dinner\\O was\\O ok\\B ,\\O nothing\\O I\\O would\\O have\\O again\\O .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", ",", "NN", "PRP", "MD", "VB", "RB", "."], "head": [2, 4, 4, 0, 4, 4, 9, 9, 6, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "appos", "nsubj", "aux", "acl:relcl", "advmod", "punct"]}, {"id": "57", "sentence": "All the food was hot tasty .", "triples": [{"uid": "57-0", "sentiment": "positive", "target_tags": "All\\O the\\O food\\B was\\O hot\\O tasty\\O .\\O", "opinion_tags": "All\\O the\\O food\\O was\\O hot\\B tasty\\I .\\O"}], "postag": ["PDT", "DT", "NN", "VBD", "JJ", "JJ", "."], "head": [3, 3, 6, 6, 6, 0, 6], "deprel": ["det:predet", "det", "nsubj", "cop", "advmod", "root", "punct"]}, {"id": "58", "sentence": "Our server was very helpful and friendly .", "triples": [{"uid": "58-0", "sentiment": "positive", "target_tags": "Our\\O server\\B was\\O very\\O helpful\\O and\\O friendly\\O .\\O", "opinion_tags": "Our\\O server\\O was\\O very\\O helpful\\B and\\O friendly\\O .\\O"}, {"uid": "58-1", "sentiment": "positive", "target_tags": "Our\\O server\\B was\\O very\\O helpful\\O and\\O friendly\\O .\\O", "opinion_tags": "Our\\O server\\O was\\O very\\O helpful\\O and\\O friendly\\B .\\O"}], "postag": ["PRP$", "NN", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["nmod:poss", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"]}, {"id": "59", "sentence": "The staff is no nonsense .", "triples": [{"uid": "59-0", "sentiment": "positive", "target_tags": "The\\O staff\\B is\\O no\\O nonsense\\O .\\O", "opinion_tags": "The\\O staff\\O is\\O no\\B nonsense\\I .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "det", "root", "punct"]}, {"id": "60", "sentence": "The menu was impressive with selections ranging from a burger , to steak , to escargot .", "triples": [{"uid": "60-0", "sentiment": "positive", "target_tags": "The\\O menu\\B was\\O impressive\\O with\\O selections\\O ranging\\O from\\O a\\O burger\\O ,\\O to\\O steak\\O ,\\O to\\O escargot\\O .\\O", "opinion_tags": "The\\O menu\\O was\\O impressive\\B with\\O selections\\O ranging\\O from\\O a\\O burger\\O ,\\O to\\O steak\\O ,\\O to\\O escargot\\O .\\O"}, {"uid": "60-1", "sentiment": "neutral", "target_tags": "The\\O menu\\O was\\O impressive\\O with\\O selections\\O ranging\\O from\\O a\\O burger\\B ,\\O to\\O steak\\O ,\\O to\\O escargot\\O .\\O", "opinion_tags": "The\\O menu\\O was\\O impressive\\B with\\O selections\\O ranging\\O from\\O a\\O burger\\O ,\\O to\\O steak\\O ,\\O to\\O escargot\\O .\\O"}, {"uid": "60-2", "sentiment": "neutral", "target_tags": "The\\O menu\\O was\\O impressive\\O with\\O selections\\O ranging\\O from\\O a\\O burger\\O ,\\O to\\O steak\\B ,\\O to\\O escargot\\O .\\O", "opinion_tags": "The\\O menu\\O was\\O impressive\\B with\\O selections\\O ranging\\O from\\O a\\O burger\\O ,\\O to\\O steak\\O ,\\O to\\O escargot\\O .\\O"}, {"uid": "60-3", "sentiment": "neutral", "target_tags": "The\\O menu\\O was\\O impressive\\O with\\O selections\\O ranging\\O from\\O a\\O burger\\O ,\\O to\\O steak\\O ,\\O to\\O escargot\\B .\\O", "opinion_tags": "The\\O menu\\O was\\O impressive\\B with\\O selections\\O ranging\\O from\\O a\\O burger\\O ,\\O to\\O steak\\O ,\\O to\\O escargot\\O .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "IN", "NNS", "VBG", "IN", "DT", "NN", ",", "IN", "NN", ",", "IN", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 6, 10, 10, 7, 10, 13, 10, 16, 16, 7, 4], "deprel": ["det", "nsubj", "cop", "root", "case", "obl", "acl", "case", "det", "obl", "punct", "case", "nmod", "punct", "case", "obl", "punct"]}, {"id": "61", "sentence": "Prices are higher to dine in and their chicken tikka marsala is quite good .", "triples": [{"uid": "61-0", "sentiment": "negative", "target_tags": "Prices\\B are\\O higher\\O to\\O dine\\O in\\O and\\O their\\O chicken\\O tikka\\O marsala\\O is\\O quite\\O good\\O .\\O", "opinion_tags": "Prices\\O are\\O higher\\B to\\O dine\\O in\\O and\\O their\\O chicken\\O tikka\\O marsala\\O is\\O quite\\O good\\O .\\O"}, {"uid": "61-1", "sentiment": "positive", "target_tags": "Prices\\O are\\O higher\\O to\\O dine\\O in\\O and\\O their\\O chicken\\B tikka\\I marsala\\I is\\O quite\\O good\\O .\\O", "opinion_tags": "Prices\\O are\\O higher\\O to\\O dine\\O in\\O and\\O their\\O chicken\\O tikka\\O marsala\\O is\\O quite\\O good\\B .\\O"}], "postag": ["NNS", "VBP", "JJR", "TO", "VB", "IN", "CC", "PRP$", "NN", "NN", "NN", "VBZ", "RB", "JJ", "."], "head": [3, 3, 0, 5, 3, 5, 14, 11, 11, 11, 14, 14, 14, 3, 3], "deprel": ["nsubj", "cop", "root", "mark", "xcomp", "obl", "cc", "nmod:poss", "compound", "compound", "nsubj", "cop", "advmod", "conj", "punct"]}, {"id": "62", "sentence": "The photos of the restaurant in its web site are way better than the real look .", "triples": [{"uid": "62-0", "sentiment": "negative", "target_tags": "The\\O photos\\O of\\O the\\O restaurant\\O in\\O its\\O web\\O site\\O are\\O way\\O better\\O than\\O the\\O real\\O look\\B .\\O", "opinion_tags": "The\\O photos\\O of\\O the\\O restaurant\\O in\\O its\\O web\\O site\\O are\\O way\\O better\\B than\\O the\\O real\\O look\\O .\\O"}], "postag": ["DT", "NNS", "IN", "DT", "NN", "IN", "PRP$", "NN", "NN", "VBP", "RB", "JJR", "IN", "DT", "JJ", "NN", "."], "head": [2, 12, 5, 5, 2, 9, 9, 9, 5, 12, 12, 0, 16, 16, 16, 12, 12], "deprel": ["det", "nsubj", "case", "det", "nmod", "case", "nmod:poss", "compound", "nmod", "cop", "advmod", "root", "case", "det", "amod", "obl", "punct"]}, {"id": "63", "sentence": "The food was lousy - too sweet or too salty and the portions tiny .", "triples": [{"uid": "63-0", "sentiment": "negative", "target_tags": "The\\O food\\B was\\O lousy\\O -\\O too\\O sweet\\O or\\O too\\O salty\\O and\\O the\\O portions\\O tiny\\O .\\O", "opinion_tags": "The\\O food\\O was\\O lousy\\B -\\O too\\O sweet\\O or\\O too\\O salty\\O and\\O the\\O portions\\O tiny\\O .\\O"}, {"uid": "63-1", "sentiment": "negative", "target_tags": "The\\O food\\B was\\O lousy\\O -\\O too\\O sweet\\O or\\O too\\O salty\\O and\\O the\\O portions\\O tiny\\O .\\O", "opinion_tags": "The\\O food\\O was\\O lousy\\O -\\O too\\B sweet\\I or\\O too\\O salty\\O and\\O the\\O portions\\O tiny\\O .\\O"}, {"uid": "63-2", "sentiment": "negative", "target_tags": "The\\O food\\B was\\O lousy\\O -\\O too\\O sweet\\O or\\O too\\O salty\\O and\\O the\\O portions\\O tiny\\O .\\O", "opinion_tags": "The\\O food\\O was\\O lousy\\O -\\O too\\O sweet\\O or\\O too\\B salty\\I and\\O the\\O portions\\O tiny\\O .\\O"}, {"uid": "63-3", "sentiment": "negative", "target_tags": "The\\O food\\O was\\O lousy\\O -\\O too\\O sweet\\O or\\O too\\O salty\\O and\\O the\\O portions\\B tiny\\O .\\O", "opinion_tags": "The\\O food\\O was\\O lousy\\O -\\O too\\O sweet\\O or\\O too\\O salty\\O and\\O the\\O portions\\O tiny\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", ",", "RB", "JJ", "CC", "RB", "JJ", "CC", "DT", "NNS", "JJ", "."], "head": [2, 4, 4, 0, 4, 7, 4, 10, 10, 7, 14, 13, 14, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "advmod", "parataxis", "cc", "advmod", "conj", "cc", "det", "nsubj", "conj", "punct"]}, {"id": "64", "sentence": "But the coconut rice was good .", "triples": [{"uid": "64-0", "sentiment": "positive", "target_tags": "But\\O the\\O coconut\\B rice\\I was\\O good\\O .\\O", "opinion_tags": "But\\O the\\O coconut\\O rice\\O was\\O good\\B .\\O"}], "postag": ["CC", "DT", "NN", "NN", "VBD", "JJ", "."], "head": [6, 4, 4, 6, 6, 0, 6], "deprel": ["cc", "det", "compound", "nsubj", "cop", "root", "punct"]}, {"id": "65", "sentence": "All of my co-workers stated that the food was amazing and wondered why they had n't heard about this place .", "triples": [{"uid": "65-0", "sentiment": "positive", "target_tags": "All\\O of\\O my\\O co-workers\\O stated\\O that\\O the\\O food\\B was\\O amazing\\O and\\O wondered\\O why\\O they\\O had\\O n't\\O heard\\O about\\O this\\O place\\O .\\O", "opinion_tags": "All\\O of\\O my\\O co-workers\\O stated\\O that\\O the\\O food\\O was\\O amazing\\B and\\O wondered\\O why\\O they\\O had\\O n't\\O heard\\O about\\O this\\O place\\O .\\O"}], "postag": ["DT", "IN", "PRP$", "NNS", "VBD", "IN", "DT", "NN", "VBD", "JJ", "CC", "VBD", "WRB", "PRP", "VBD", "RB", "VBN", "IN", "DT", "NN", "."], "head": [5, 4, 4, 1, 0, 10, 8, 10, 10, 5, 12, 10, 17, 17, 17, 17, 12, 20, 20, 17, 5], "deprel": ["nsubj", "case", "nmod:poss", "nmod", "root", "mark", "det", "nsubj", "cop", "ccomp", "cc", "conj", "mark", "nsubj", "aux", "advmod", "ccomp", "case", "det", "obl", "punct"]}, {"id": "66", "sentence": "Friendly staff that actually lets you enjoy your meal and the company you 're with .", "triples": [{"uid": "66-0", "sentiment": "positive", "target_tags": "Friendly\\O staff\\B that\\O actually\\O lets\\O you\\O enjoy\\O your\\O meal\\O and\\O the\\O company\\O you\\O 're\\O with\\O .\\O", "opinion_tags": "Friendly\\B staff\\O that\\O actually\\O lets\\O you\\O enjoy\\O your\\O meal\\O and\\O the\\O company\\O you\\O 're\\O with\\O .\\O"}, {"uid": "66-1", "sentiment": "positive", "target_tags": "Friendly\\O staff\\O that\\O actually\\O lets\\O you\\O enjoy\\O your\\O meal\\B and\\O the\\O company\\O you\\O 're\\O with\\O .\\O", "opinion_tags": "Friendly\\O staff\\O that\\O actually\\O lets\\O you\\O enjoy\\B your\\O meal\\O and\\O the\\O company\\O you\\O 're\\O with\\O .\\O"}], "postag": ["JJ", "NN", "WDT", "RB", "VBZ", "PRP", "VB", "PRP$", "NN", "CC", "DT", "NN", "PRP", "VBP", "IN", "."], "head": [2, 0, 5, 5, 2, 5, 5, 9, 7, 12, 12, 9, 15, 12, 12, 2], "deprel": ["amod", "root", "nsubj", "advmod", "acl:relcl", "obj", "xcomp", "nmod:poss", "obj", "cc", "det", "conj", "nsubj", "acl:relcl", "acl:relcl", "punct"]}, {"id": "67", "sentence": "The appetizing is excellent - just as good as Zabars Barney Greengrass at a reasonable price ( if bought by the pound ) .", "triples": [{"uid": "67-0", "sentiment": "positive", "target_tags": "The\\O appetizing\\B is\\O excellent\\O -\\O just\\O as\\O good\\O as\\O Zabars\\O Barney\\O Greengrass\\O at\\O a\\O reasonable\\O price\\O (\\O if\\O bought\\O by\\O the\\O pound\\O )\\O .\\O", "opinion_tags": "The\\O appetizing\\O is\\O excellent\\B -\\O just\\O as\\O good\\O as\\O Zabars\\O Barney\\O Greengrass\\O at\\O a\\O reasonable\\O price\\O (\\O if\\O bought\\O by\\O the\\O pound\\O )\\O .\\O"}, {"uid": "67-1", "sentiment": "positive", "target_tags": "The\\O appetizing\\O is\\O excellent\\O -\\O just\\O as\\O good\\O as\\O Zabars\\O Barney\\O Greengrass\\O at\\O a\\O reasonable\\O price\\B (\\O if\\O bought\\O by\\O the\\O pound\\O )\\O .\\O", "opinion_tags": "The\\O appetizing\\O is\\O excellent\\O -\\O just\\O as\\O good\\O as\\O Zabars\\O Barney\\O Greengrass\\O at\\O a\\O reasonable\\B price\\O (\\O if\\O bought\\O by\\O the\\O pound\\O )\\O .\\O"}], "postag": ["DT", "JJ", "VBZ", "JJ", ",", "RB", "RB", "JJ", "IN", "NNP", "NNP", "NNP", "IN", "DT", "JJ", "NN", "-LRB-", "IN", "VBN", "IN", "DT", "NN", "-RRB-", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 10, 8, 10, 10, 16, 16, 16, 8, 19, 19, 8, 22, 22, 19, 19, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "advmod", "advmod", "parataxis", "case", "obl", "flat", "flat", "case", "det", "amod", "obl", "punct", "mark", "advcl", "case", "det", "obl", "punct", "punct"]}, {"id": "68", "sentence": "First , the waiter who served us neglected to fill us in on the specials , which I would have chosen had I known about them .", "triples": [{"uid": "68-0", "sentiment": "negative", "target_tags": "First\\O ,\\O the\\O waiter\\B who\\O served\\O us\\O neglected\\O to\\O fill\\O us\\O in\\O on\\O the\\O specials\\O ,\\O which\\O I\\O would\\O have\\O chosen\\O had\\O I\\O known\\O about\\O them\\O .\\O", "opinion_tags": "First\\O ,\\O the\\O waiter\\O who\\O served\\O us\\O neglected\\B to\\O fill\\O us\\O in\\O on\\O the\\O specials\\O ,\\O which\\O I\\O would\\O have\\O chosen\\O had\\O I\\O known\\O about\\O them\\O .\\O"}], "postag": ["RB", ",", "DT", "NN", "WP", "VBD", "PRP", "VBN", "TO", "VB", "PRP", "RB", "IN", "DT", "NNS", ",", "WDT", "PRP", "MD", "VB", "VBN", "VBD", "PRP", "VBN", "IN", "PRP", "."], "head": [24, 1, 4, 24, 6, 4, 6, 6, 10, 8, 10, 10, 15, 15, 10, 15, 21, 21, 21, 21, 4, 24, 24, 0, 26, 24, 24], "deprel": ["advmod", "punct", "det", "nsubj", "nsubj", "acl:relcl", "obj", "xcomp", "mark", "xcomp", "obj", "advmod", "case", "det", "obl", "punct", "obj", "nsubj", "aux", "aux", "acl:relcl", "aux", "nsubj", "root", "case", "obl", "punct"]}, {"id": "69", "sentence": "Authentic food and they have REAL service , not just the rush you get sometimes when they try to push you out the door .", "triples": [{"uid": "69-0", "sentiment": "positive", "target_tags": "Authentic\\O food\\B and\\O they\\O have\\O REAL\\O service\\O ,\\O not\\O just\\O the\\O rush\\O you\\O get\\O sometimes\\O when\\O they\\O try\\O to\\O push\\O you\\O out\\O the\\O door\\O .\\O", "opinion_tags": "Authentic\\B food\\O and\\O they\\O have\\O REAL\\O service\\O ,\\O not\\O just\\O the\\O rush\\O you\\O get\\O sometimes\\O when\\O they\\O try\\O to\\O push\\O you\\O out\\O the\\O door\\O .\\O"}, {"uid": "69-1", "sentiment": "positive", "target_tags": "Authentic\\O food\\O and\\O they\\O have\\O REAL\\O service\\B ,\\O not\\O just\\O the\\O rush\\O you\\O get\\O sometimes\\O when\\O they\\O try\\O to\\O push\\O you\\O out\\O the\\O door\\O .\\O", "opinion_tags": "Authentic\\O food\\O and\\O they\\O have\\O REAL\\B service\\O ,\\O not\\O just\\O the\\O rush\\O you\\O get\\O sometimes\\O when\\O they\\O try\\O to\\O push\\O you\\O out\\O the\\O door\\O .\\O"}], "postag": ["JJ", "NN", "CC", "PRP", "VBP", "JJ", "NN", ",", "RB", "RB", "DT", "NN", "PRP", "VBP", "RB", "WRB", "PRP", "VBP", "TO", "VB", "PRP", "RP", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 7, 5, 12, 12, 12, 12, 7, 14, 12, 18, 18, 18, 14, 20, 18, 20, 20, 24, 20, 2], "deprel": ["amod", "root", "cc", "nsubj", "conj", "amod", "obj", "punct", "advmod", "advmod", "det", "conj", "nsubj", "acl:relcl", "advmod", "mark", "nsubj", "advcl", "mark", "xcomp", "obj", "compound:prt", "det", "obl:npmod", "punct"]}, {"id": "70", "sentence": "I would definitely recommend SEA if you like thai cuisine !", "triples": [{"uid": "70-0", "sentiment": "positive", "target_tags": "I\\O would\\O definitely\\O recommend\\O SEA\\O if\\O you\\O like\\O thai\\B cuisine\\I !\\O", "opinion_tags": "I\\O would\\O definitely\\O recommend\\O SEA\\O if\\O you\\O like\\B thai\\O cuisine\\O !\\O"}], "postag": ["PRP", "MD", "RB", "VB", "NNP", "IN", "PRP", "VBP", "JJ", "NN", "."], "head": [4, 4, 4, 0, 4, 8, 8, 4, 10, 8, 4], "deprel": ["nsubj", "aux", "advmod", "root", "obj", "mark", "nsubj", "advcl", "amod", "obj", "punct"]}, {"id": "71", "sentence": "Prices are in line .", "triples": [{"uid": "71-0", "sentiment": "neutral", "target_tags": "Prices\\B are\\O in\\O line\\O .\\O", "opinion_tags": "Prices\\O are\\O in\\B line\\I .\\O"}], "postag": ["NNS", "VBP", "IN", "NN", "."], "head": [4, 4, 4, 0, 4], "deprel": ["nsubj", "cop", "case", "root", "punct"]}, {"id": "72", "sentence": "The food is prepared quickly and efficiently .", "triples": [{"uid": "72-0", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O prepared\\O quickly\\O and\\O efficiently\\O .\\O", "opinion_tags": "The\\O food\\O is\\O prepared\\O quickly\\B and\\O efficiently\\O .\\O"}, {"uid": "72-1", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O prepared\\O quickly\\O and\\O efficiently\\O .\\O", "opinion_tags": "The\\O food\\O is\\O prepared\\O quickly\\O and\\O efficiently\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "VBN", "RB", "CC", "RB", "."], "head": [2, 4, 4, 0, 4, 7, 5, 4], "deprel": ["det", "nsubj:pass", "aux:pass", "root", "advmod", "cc", "conj", "punct"]}, {"id": "73", "sentence": "While this is n't classical restaurant fare , the chef has given new life to an old cuisine with some really innovative and tasty dishes that are genuinely Indian without being heavy or same old restaurant burn-outs .", "triples": [{"uid": "73-0", "sentiment": "positive", "target_tags": "While\\O this\\O is\\O n't\\O classical\\O restaurant\\O fare\\O ,\\O the\\O chef\\O has\\O given\\O new\\O life\\O to\\O an\\O old\\O cuisine\\O with\\O some\\O really\\O innovative\\O and\\O tasty\\O dishes\\B that\\O are\\O genuinely\\O Indian\\O without\\O being\\O heavy\\O or\\O same\\O old\\O restaurant\\O burn-outs\\O .\\O", "opinion_tags": "While\\O this\\O is\\O n't\\O classical\\O restaurant\\O fare\\O ,\\O the\\O chef\\O has\\O given\\O new\\O life\\O to\\O an\\O old\\O cuisine\\O with\\O some\\O really\\O innovative\\B and\\O tasty\\O dishes\\O that\\O are\\O genuinely\\O Indian\\O without\\O being\\O heavy\\O or\\O same\\O old\\O restaurant\\O burn-outs\\O .\\O"}, {"uid": "73-1", "sentiment": "positive", "target_tags": "While\\O this\\O is\\O n't\\O classical\\O restaurant\\O fare\\O ,\\O the\\O chef\\O has\\O given\\O new\\O life\\O to\\O an\\O old\\O cuisine\\O with\\O some\\O really\\O innovative\\O and\\O tasty\\O dishes\\B that\\O are\\O genuinely\\O Indian\\O without\\O being\\O heavy\\O or\\O same\\O old\\O restaurant\\O burn-outs\\O .\\O", "opinion_tags": "While\\O this\\O is\\O n't\\O classical\\O restaurant\\O fare\\O ,\\O the\\O chef\\O has\\O given\\O new\\O life\\O to\\O an\\O old\\O cuisine\\O with\\O some\\O really\\O innovative\\O and\\O tasty\\B dishes\\O that\\O are\\O genuinely\\O Indian\\O without\\O being\\O heavy\\O or\\O same\\O old\\O restaurant\\O burn-outs\\O .\\O"}, {"uid": "73-2", "sentiment": "neutral", "target_tags": "While\\O this\\O is\\O n't\\O classical\\O restaurant\\O fare\\O ,\\O the\\O chef\\O has\\O given\\O new\\O life\\O to\\O an\\O old\\O cuisine\\B with\\O some\\O really\\O innovative\\O and\\O tasty\\O dishes\\O that\\O are\\O genuinely\\O Indian\\O without\\O being\\O heavy\\O or\\O same\\O old\\O restaurant\\O burn-outs\\O .\\O", "opinion_tags": "While\\O this\\O is\\O n't\\O classical\\O restaurant\\O fare\\O ,\\O the\\O chef\\O has\\O given\\O new\\O life\\O to\\O an\\O old\\B cuisine\\O with\\O some\\O really\\O innovative\\O and\\O tasty\\O dishes\\O that\\O are\\O genuinely\\O Indian\\O without\\O being\\O heavy\\O or\\O same\\O old\\O restaurant\\O burn-outs\\O .\\O"}, {"uid": "73-3", "sentiment": "positive", "target_tags": "While\\O this\\O is\\O n't\\O classical\\O restaurant\\O fare\\O ,\\O the\\O chef\\O has\\O given\\O new\\O life\\O to\\O an\\O old\\O cuisine\\O with\\O some\\O really\\O innovative\\O and\\O tasty\\O dishes\\O that\\O are\\O genuinely\\O Indian\\B without\\O being\\O heavy\\O or\\O same\\O old\\O restaurant\\O burn-outs\\O .\\O", "opinion_tags": "While\\O this\\O is\\O n't\\O classical\\O restaurant\\O fare\\O ,\\O the\\O chef\\O has\\O given\\O new\\O life\\O to\\O an\\O old\\O cuisine\\O with\\O some\\O really\\O innovative\\O and\\O tasty\\O dishes\\O that\\O are\\O genuinely\\B Indian\\O without\\O being\\O heavy\\O or\\O same\\O old\\O restaurant\\O burn-outs\\O .\\O"}], "postag": ["IN", "DT", "VBZ", "RB", "JJ", "NN", "NN", ",", "DT", "NN", "VBZ", "VBN", "JJ", "NN", "IN", "DT", "JJ", "NN", "IN", "DT", "RB", "JJ", "CC", "JJ", "NNS", "WDT", "VBP", "RB", "JJ", "IN", "VBG", "JJ", "CC", "JJ", "JJ", "NN", "NNS", "."], "head": [7, 7, 7, 7, 7, 7, 12, 12, 10, 12, 12, 0, 14, 12, 18, 18, 18, 12, 25, 25, 22, 25, 24, 22, 18, 29, 29, 29, 25, 37, 37, 37, 35, 35, 37, 37, 29, 12], "deprel": ["mark", "nsubj", "cop", "advmod", "amod", "compound", "advcl", "punct", "det", "nsubj", "aux", "root", "amod", "obj", "case", "det", "amod", "obl", "case", "det", "advmod", "amod", "cc", "conj", "nmod", "nsubj", "cop", "advmod", "acl:relcl", "mark", "cop", "amod", "cc", "advmod", "amod", "compound", "advcl", "punct"]}, {"id": "74", "sentence": "Great romantic place for a date ( try to get the corner booth table for a little privacy and to sit close ! ) .", "triples": [{"uid": "74-0", "sentiment": "positive", "target_tags": "Great\\O romantic\\O place\\O for\\O a\\O date\\O (\\O try\\O to\\O get\\O the\\O corner\\B booth\\I table\\I for\\O a\\O little\\O privacy\\O and\\O to\\O sit\\O close\\O !\\O )\\O .\\O", "opinion_tags": "Great\\O romantic\\O place\\O for\\O a\\O date\\O (\\O try\\O to\\O get\\O the\\O corner\\O booth\\O table\\O for\\O a\\O little\\O privacy\\B and\\O to\\O sit\\O close\\O !\\O )\\O .\\O"}, {"uid": "74-1", "sentiment": "positive", "target_tags": "Great\\O romantic\\O place\\B for\\O a\\O date\\O (\\O try\\O to\\O get\\O the\\O corner\\O booth\\O table\\O for\\O a\\O little\\O privacy\\O and\\O to\\O sit\\O close\\O !\\O )\\O .\\O", "opinion_tags": "Great\\B romantic\\I place\\O for\\O a\\O date\\O (\\O try\\O to\\O get\\O the\\O corner\\O booth\\O table\\O for\\O a\\O little\\O privacy\\O and\\O to\\O sit\\O close\\O !\\O )\\O .\\O"}], "postag": ["JJ", "JJ", "NN", "IN", "DT", "NN", "-LRB-", "VB", "TO", "VB", "DT", "NN", "NN", "NN", "IN", "DT", "JJ", "NN", "CC", "TO", "VB", "JJ", ".", "-RRB-", "."], "head": [3, 3, 0, 6, 6, 3, 8, 3, 10, 8, 14, 14, 14, 10, 18, 18, 18, 10, 21, 21, 10, 21, 8, 8, 3], "deprel": ["amod", "amod", "root", "case", "det", "nmod", "punct", "parataxis", "mark", "xcomp", "det", "compound", "compound", "obj", "case", "det", "amod", "obl", "cc", "mark", "conj", "xcomp", "punct", "punct", "punct"]}, {"id": "75", "sentence": "We were fans of the half-price Saturday night option until some inedible squid during a recent visit .", "triples": [{"uid": "75-0", "sentiment": "negative", "target_tags": "We\\O were\\O fans\\O of\\O the\\O half-price\\O Saturday\\O night\\O option\\O until\\O some\\O inedible\\O squid\\B during\\O a\\O recent\\O visit\\O .\\O", "opinion_tags": "We\\O were\\O fans\\O of\\O the\\O half-price\\O Saturday\\O night\\O option\\O until\\O some\\O inedible\\B squid\\O during\\O a\\O recent\\O visit\\O .\\O"}, {"uid": "75-1", "sentiment": "positive", "target_tags": "We\\O were\\O fans\\O of\\O the\\O half-price\\B Saturday\\I night\\I option\\I until\\O some\\O inedible\\O squid\\O during\\O a\\O recent\\O visit\\O .\\O", "opinion_tags": "We\\O were\\O fans\\B of\\O the\\O half-price\\O Saturday\\O night\\O option\\O until\\O some\\O inedible\\O squid\\O during\\O a\\O recent\\O visit\\O .\\O"}], "postag": ["PRP", "VBD", "NNS", "IN", "DT", "JJ", "NNP", "NN", "NN", "IN", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 9, 9, 9, 8, 9, 3, 13, 13, 13, 3, 17, 17, 17, 3, 3], "deprel": ["nsubj", "cop", "root", "case", "det", "amod", "compound", "compound", "nmod", "case", "det", "amod", "nmod", "case", "det", "amod", "nmod", "punct"]}, {"id": "76", "sentence": "Consistently good Japanese Tapas .", "triples": [{"uid": "76-0", "sentiment": "positive", "target_tags": "Consistently\\O good\\O Japanese\\B Tapas\\I .\\O", "opinion_tags": "Consistently\\O good\\B Japanese\\O Tapas\\O .\\O"}], "postag": ["RB", "JJ", "JJ", "NN", "."], "head": [2, 4, 4, 0, 4], "deprel": ["advmod", "amod", "amod", "root", "punct"]}, {"id": "77", "sentence": "The dishes offered were unique , very tasty and fresh from the lamb sausages , sardines with biscuits , large whole shrimp to the amazing pistachio ice cream ( the best and freshest I 've ever had ) .", "triples": [{"uid": "77-0", "sentiment": "positive", "target_tags": "The\\O dishes\\B offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\B ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}, {"uid": "77-1", "sentiment": "positive", "target_tags": "The\\O dishes\\B offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\B and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}, {"uid": "77-2", "sentiment": "positive", "target_tags": "The\\O dishes\\B offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\B from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}, {"uid": "77-3", "sentiment": "positive", "target_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\B sausages\\I ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\B and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}, {"uid": "77-4", "sentiment": "positive", "target_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\B sausages\\I ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\B from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}, {"uid": "77-5", "sentiment": "positive", "target_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\B with\\I biscuits\\I ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\B and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}, {"uid": "77-6", "sentiment": "positive", "target_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\B with\\I biscuits\\I ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\B from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}, {"uid": "77-7", "sentiment": "positive", "target_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\B whole\\I shrimp\\I to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\B whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}, {"uid": "77-8", "sentiment": "positive", "target_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\B ice\\I cream\\I (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\B pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}], "postag": ["DT", "NNS", "VBD", "VBD", "JJ", ",", "RB", "JJ", "CC", "JJ", "IN", "DT", "NN", "NNS", ",", "NNS", "IN", "NNS", ",", "JJ", "JJ", "NNS", "IN", "DT", "JJ", "NN", "NN", "NN", "-LRB-", "DT", "JJS", "CC", "JJS", "PRP", "VBP", "RB", "VBN", "-RRB-", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 10, 5, 14, 14, 14, 10, 16, 14, 18, 14, 22, 22, 22, 14, 28, 28, 28, 28, 28, 14, 31, 31, 14, 33, 31, 37, 37, 37, 31, 31, 5], "deprel": ["det", "nsubj", "csubj", "cop", "root", "punct", "advmod", "conj", "cc", "conj", "case", "det", "compound", "obl", "punct", "conj", "case", "nmod", "punct", "amod", "amod", "conj", "case", "det", "amod", "compound", "compound", "nmod", "punct", "det", "parataxis", "cc", "conj", "nsubj", "aux", "advmod", "acl:relcl", "punct", "punct"]}, {"id": "78", "sentence": "I had to share my table with a loud group of kids and the service was rude an unattentive .", "triples": [{"uid": "78-0", "sentiment": "negative", "target_tags": "I\\O had\\O to\\O share\\O my\\O table\\O with\\O a\\O loud\\O group\\O of\\O kids\\O and\\O the\\O service\\B was\\O rude\\O an\\O unattentive\\O .\\O", "opinion_tags": "I\\O had\\O to\\O share\\O my\\O table\\O with\\O a\\O loud\\O group\\O of\\O kids\\O and\\O the\\O service\\O was\\O rude\\B an\\O unattentive\\O .\\O"}, {"uid": "78-1", "sentiment": "negative", "target_tags": "I\\O had\\O to\\O share\\O my\\O table\\O with\\O a\\O loud\\O group\\O of\\O kids\\O and\\O the\\O service\\B was\\O rude\\O an\\O unattentive\\O .\\O", "opinion_tags": "I\\O had\\O to\\O share\\O my\\O table\\O with\\O a\\O loud\\O group\\O of\\O kids\\O and\\O the\\O service\\O was\\O rude\\O an\\O unattentive\\B .\\O"}, {"uid": "78-2", "sentiment": "negative", "target_tags": "I\\O had\\O to\\O share\\O my\\O table\\B with\\O a\\O loud\\O group\\O of\\O kids\\O and\\O the\\O service\\O was\\O rude\\O an\\O unattentive\\O .\\O", "opinion_tags": "I\\O had\\O to\\O share\\O my\\O table\\O with\\O a\\O loud\\B group\\O of\\O kids\\O and\\O the\\O service\\O was\\O rude\\O an\\O unattentive\\O .\\O"}], "postag": ["PRP", "VBD", "TO", "VB", "PRP$", "NN", "IN", "DT", "JJ", "NN", "IN", "NNS", "CC", "DT", "NN", "VBD", "JJ", "DT", "JJ", "."], "head": [2, 0, 4, 2, 6, 4, 10, 10, 10, 4, 12, 10, 17, 15, 17, 17, 2, 19, 17, 2], "deprel": ["nsubj", "root", "mark", "xcomp", "nmod:poss", "obj", "case", "det", "amod", "obl", "case", "nmod", "cc", "det", "nsubj", "cop", "conj", "det", "advmod", "punct"]}, {"id": "79", "sentence": "i 've been to sapphire twice and both times the food was fine , if not good .", "triples": [{"uid": "79-0", "sentiment": "positive", "target_tags": "i\\O 've\\O been\\O to\\O sapphire\\O twice\\O and\\O both\\O times\\O the\\O food\\B was\\O fine\\O ,\\O if\\O not\\O good\\O .\\O", "opinion_tags": "i\\O 've\\O been\\O to\\O sapphire\\O twice\\O and\\O both\\O times\\O the\\O food\\O was\\O fine\\B ,\\O if\\O not\\O good\\O .\\O"}], "postag": ["PRP", "VBP", "VBN", "IN", "NN", "RB", "CC", "DT", "NNS", "DT", "NN", "VBD", "JJ", ",", "IN", "RB", "JJ", "."], "head": [5, 5, 5, 5, 0, 5, 13, 9, 13, 11, 13, 13, 5, 13, 17, 17, 13, 5], "deprel": ["nsubj", "aux", "cop", "case", "root", "advmod", "cc", "det", "obl:tmod", "det", "nsubj", "cop", "conj", "punct", "mark", "advmod", "advcl", "punct"]}, {"id": "80", "sentence": "It is kinda nosiy and the tables are close together but it 's still a beautiful place to enjoy a nice dinner .", "triples": [{"uid": "80-0", "sentiment": "negative", "target_tags": "It\\O is\\O kinda\\O nosiy\\O and\\O the\\O tables\\B are\\O close\\O together\\O but\\O it\\O 's\\O still\\O a\\O beautiful\\O place\\O to\\O enjoy\\O a\\O nice\\O dinner\\O .\\O", "opinion_tags": "It\\O is\\O kinda\\O nosiy\\O and\\O the\\O tables\\O are\\O close\\B together\\O but\\O it\\O 's\\O still\\O a\\O beautiful\\O place\\O to\\O enjoy\\O a\\O nice\\O dinner\\O .\\O"}, {"uid": "80-1", "sentiment": "positive", "target_tags": "It\\O is\\O kinda\\O nosiy\\O and\\O the\\O tables\\O are\\O close\\O together\\O but\\O it\\O 's\\O still\\O a\\O beautiful\\O place\\O to\\O enjoy\\O a\\O nice\\O dinner\\B .\\O", "opinion_tags": "It\\O is\\O kinda\\O nosiy\\O and\\O the\\O tables\\O are\\O close\\O together\\O but\\O it\\O 's\\O still\\O a\\O beautiful\\O place\\O to\\O enjoy\\O a\\O nice\\B dinner\\O .\\O"}, {"uid": "80-2", "sentiment": "positive", "target_tags": "It\\O is\\O kinda\\O nosiy\\O and\\O the\\O tables\\O are\\O close\\O together\\O but\\O it\\O 's\\O still\\O a\\O beautiful\\O place\\B to\\O enjoy\\O a\\O nice\\O dinner\\O .\\O", "opinion_tags": "It\\O is\\O kinda\\O nosiy\\O and\\O the\\O tables\\O are\\O close\\O together\\O but\\O it\\O 's\\O still\\O a\\O beautiful\\B place\\O to\\O enjoy\\O a\\O nice\\O dinner\\O .\\O"}], "postag": ["PRP", "VBZ", "RB", "JJ", "CC", "DT", "NNS", "VBP", "JJ", "RB", "CC", "PRP", "VBZ", "RB", "DT", "JJ", "NN", "TO", "VB", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 0, 9, 7, 9, 9, 4, 9, 17, 17, 17, 17, 17, 17, 4, 19, 17, 22, 22, 19, 4], "deprel": ["nsubj", "cop", "advmod", "root", "cc", "det", "nsubj", "cop", "conj", "advmod", "cc", "nsubj", "cop", "advmod", "det", "amod", "conj", "mark", "acl", "det", "amod", "obj", "punct"]}, {"id": "81", "sentence": "The Pastrami sandwich was like buttah and with pickles and an icy cold beer to wash it down , it was a pleasurable experience .", "triples": [{"uid": "81-0", "sentiment": "positive", "target_tags": "The\\O Pastrami\\B sandwich\\I was\\O like\\O buttah\\O and\\O with\\O pickles\\O and\\O an\\O icy\\O cold\\O beer\\O to\\O wash\\O it\\O down\\O ,\\O it\\O was\\O a\\O pleasurable\\O experience\\O .\\O", "opinion_tags": "The\\O Pastrami\\O sandwich\\O was\\O like\\O buttah\\O and\\O with\\O pickles\\O and\\O an\\O icy\\O cold\\O beer\\O to\\O wash\\O it\\O down\\O ,\\O it\\O was\\O a\\O pleasurable\\B experience\\O .\\O"}, {"uid": "81-1", "sentiment": "positive", "target_tags": "The\\O Pastrami\\O sandwich\\O was\\O like\\O buttah\\O and\\O with\\O pickles\\O and\\O an\\O icy\\O cold\\O beer\\B to\\O wash\\O it\\O down\\O ,\\O it\\O was\\O a\\O pleasurable\\O experience\\O .\\O", "opinion_tags": "The\\O Pastrami\\O sandwich\\O was\\O like\\O buttah\\O and\\O with\\O pickles\\O and\\O an\\O icy\\B cold\\I beer\\O to\\O wash\\O it\\O down\\O ,\\O it\\O was\\O a\\O pleasurable\\O experience\\O .\\O"}], "postag": ["DT", "NN", "NN", "VBD", "IN", "NN", "CC", "IN", "NNS", "CC", "DT", "JJ", "JJ", "NN", "TO", "VB", "PRP", "RP", ",", "PRP", "VBD", "DT", "JJ", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 24, 9, 24, 14, 14, 14, 14, 9, 16, 14, 16, 16, 24, 24, 24, 24, 24, 6, 6], "deprel": ["det", "compound", "nsubj", "cop", "case", "root", "cc", "case", "obl", "cc", "det", "amod", "amod", "conj", "mark", "acl", "obj", "compound:prt", "punct", "nsubj", "cop", "det", "amod", "conj", "punct"]}, {"id": "82", "sentence": "The scallion pancakes and fried dumplings were nothing out of the ordinary .", "triples": [{"uid": "82-0", "sentiment": "neutral", "target_tags": "The\\O scallion\\B pancakes\\I and\\O fried\\O dumplings\\O were\\O nothing\\O out\\O of\\O the\\O ordinary\\O .\\O", "opinion_tags": "The\\O scallion\\O pancakes\\O and\\O fried\\O dumplings\\O were\\O nothing\\O out\\O of\\O the\\O ordinary\\B .\\O"}, {"uid": "82-1", "sentiment": "neutral", "target_tags": "The\\O scallion\\O pancakes\\O and\\O fried\\B dumplings\\I were\\O nothing\\O out\\O of\\O the\\O ordinary\\O .\\O", "opinion_tags": "The\\O scallion\\O pancakes\\O and\\O fried\\O dumplings\\O were\\O nothing\\O out\\O of\\O the\\O ordinary\\B .\\O"}], "postag": ["DT", "NN", "NNS", "CC", "JJ", "NNS", "VBD", "NN", "IN", "IN", "DT", "JJ", "."], "head": [3, 3, 8, 6, 6, 3, 8, 0, 12, 12, 12, 8, 8], "deprel": ["det", "compound", "nsubj", "cc", "amod", "conj", "cop", "root", "case", "case", "det", "nmod", "punct"]}, {"id": "83", "sentence": "I was pretty much full after our fondue appetizer .", "triples": [{"uid": "83-0", "sentiment": "neutral", "target_tags": "I\\O was\\O pretty\\O much\\O full\\O after\\O our\\O fondue\\B appetizer\\I .\\O", "opinion_tags": "I\\O was\\O pretty\\O much\\O full\\B after\\O our\\O fondue\\O appetizer\\O .\\O"}], "postag": ["PRP", "VBD", "RB", "RB", "JJ", "IN", "PRP$", "NN", "NN", "."], "head": [5, 5, 4, 5, 0, 9, 9, 9, 5, 5], "deprel": ["nsubj", "cop", "advmod", "advmod", "root", "case", "nmod:poss", "compound", "obl", "punct"]}, {"id": "84", "sentence": "Service was quick .", "triples": [{"uid": "84-0", "sentiment": "positive", "target_tags": "Service\\B was\\O quick\\O .\\O", "opinion_tags": "Service\\O was\\O quick\\B .\\O"}], "postag": ["NN", "VBD", "JJ", "."], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"]}, {"id": "85", "sentence": "Drawbacks : service is slow and they do n't toast !", "triples": [{"uid": "85-0", "sentiment": "negative", "target_tags": "Drawbacks\\O :\\O service\\B is\\O slow\\O and\\O they\\O do\\O n't\\O toast\\O !\\O", "opinion_tags": "Drawbacks\\O :\\O service\\O is\\O slow\\B and\\O they\\O do\\O n't\\O toast\\O !\\O"}], "postag": ["NNS", ":", "NN", "VBZ", "JJ", "CC", "PRP", "VBP", "RB", "VB", "."], "head": [0, 1, 5, 5, 1, 10, 10, 10, 10, 5, 1], "deprel": ["root", "punct", "nsubj", "cop", "appos", "cc", "nsubj", "aux", "advmod", "conj", "punct"]}, {"id": "86", "sentence": "The spicy tuna and salmon are the best we 've ever had .", "triples": [{"uid": "86-0", "sentiment": "positive", "target_tags": "The\\O spicy\\B tuna\\I and\\O salmon\\O are\\O the\\O best\\O we\\O 've\\O ever\\O had\\O .\\O", "opinion_tags": "The\\O spicy\\O tuna\\O and\\O salmon\\O are\\O the\\O best\\B we\\O 've\\O ever\\O had\\O .\\O"}, {"uid": "86-1", "sentiment": "positive", "target_tags": "The\\O spicy\\O tuna\\O and\\O salmon\\B are\\O the\\O best\\O we\\O 've\\O ever\\O had\\O .\\O", "opinion_tags": "The\\O spicy\\O tuna\\O and\\O salmon\\O are\\O the\\O best\\B we\\O 've\\O ever\\O had\\O .\\O"}], "postag": ["DT", "JJ", "NN", "CC", "NN", "VBP", "DT", "JJS", "PRP", "VBP", "RB", "VBN", "."], "head": [3, 3, 8, 5, 3, 8, 8, 0, 12, 12, 12, 8, 8], "deprel": ["det", "amod", "nsubj", "cc", "conj", "cop", "det", "root", "nsubj", "aux", "advmod", "acl:relcl", "punct"]}, {"id": "87", "sentence": "Even though its good seafood , the prices are too high .", "triples": [{"uid": "87-0", "sentiment": "positive", "target_tags": "Even\\O though\\O its\\O good\\O seafood\\B ,\\O the\\O prices\\O are\\O too\\O high\\O .\\O", "opinion_tags": "Even\\O though\\O its\\O good\\B seafood\\O ,\\O the\\O prices\\O are\\O too\\O high\\O .\\O"}, {"uid": "87-1", "sentiment": "negative", "target_tags": "Even\\O though\\O its\\O good\\O seafood\\O ,\\O the\\O prices\\B are\\O too\\O high\\O .\\O", "opinion_tags": "Even\\O though\\O its\\O good\\O seafood\\O ,\\O the\\O prices\\O are\\O too\\O high\\B .\\O"}], "postag": ["RB", "IN", "PRP$", "JJ", "NN", ",", "DT", "NNS", "VBP", "RB", "JJ", "."], "head": [5, 5, 5, 5, 11, 11, 8, 11, 11, 11, 0, 11], "deprel": ["advmod", "case", "nmod:poss", "amod", "obl", "punct", "det", "nsubj", "cop", "advmod", "root", "punct"]}, {"id": "88", "sentence": "Meat dishes now adorn the selections , although there 's still a large number of vegetarian-friendly choices .", "triples": [{"uid": "88-0", "sentiment": "positive", "target_tags": "Meat\\O dishes\\O now\\O adorn\\O the\\O selections\\O ,\\O although\\O there\\O 's\\O still\\O a\\O large\\O number\\O of\\O vegetarian-friendly\\B choices\\I .\\O", "opinion_tags": "Meat\\O dishes\\O now\\O adorn\\O the\\O selections\\O ,\\O although\\O there\\O 's\\O still\\O a\\O large\\B number\\O of\\O vegetarian-friendly\\O choices\\O .\\O"}], "postag": ["NN", "NNS", "RB", "VBP", "DT", "NNS", ",", "IN", "EX", "VBZ", "RB", "DT", "JJ", "NN", "IN", "JJ", "NNS", "."], "head": [2, 4, 4, 0, 6, 4, 10, 10, 10, 4, 10, 14, 14, 10, 17, 17, 14, 4], "deprel": ["compound", "nsubj", "advmod", "root", "det", "obj", "punct", "mark", "expl", "advcl", "advmod", "det", "amod", "nsubj", "case", "amod", "nmod", "punct"]}, {"id": "89", "sentence": "Reasonable prices .", "triples": [{"uid": "89-0", "sentiment": "positive", "target_tags": "Reasonable\\O prices\\B .\\O", "opinion_tags": "Reasonable\\B prices\\O .\\O"}], "postag": ["JJ", "NNS", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"]}, {"id": "90", "sentence": "The dim sum servings here are a bit larger than I 'm used to .", "triples": [{"uid": "90-0", "sentiment": "neutral", "target_tags": "The\\O dim\\B sum\\I servings\\I here\\O are\\O a\\O bit\\O larger\\O than\\O I\\O 'm\\O used\\O to\\O .\\O", "opinion_tags": "The\\O dim\\O sum\\O servings\\O here\\O are\\O a\\O bit\\O larger\\B than\\O I\\O 'm\\O used\\O to\\O .\\O"}], "postag": ["DT", "JJ", "NN", "NNS", "RB", "VBP", "DT", "NN", "JJR", "IN", "PRP", "VBP", "VBN", "TO", "."], "head": [4, 4, 4, 9, 4, 9, 8, 9, 0, 13, 13, 13, 9, 13, 9], "deprel": ["det", "amod", "compound", "nsubj", "advmod", "cop", "det", "obl:npmod", "root", "mark", "nsubj:pass", "aux:pass", "advcl", "obl", "punct"]}, {"id": "91", "sentence": "Looking around , I saw a room full of New Yorkers enjoying a real meal in a real restaurant , not a clubhouse of the fabulous trying to be seen .", "triples": [{"uid": "91-0", "sentiment": "positive", "target_tags": "Looking\\O around\\O ,\\O I\\O saw\\O a\\O room\\O full\\O of\\O New\\O Yorkers\\O enjoying\\O a\\O real\\O meal\\B in\\O a\\O real\\O restaurant\\O ,\\O not\\O a\\O clubhouse\\O of\\O the\\O fabulous\\O trying\\O to\\O be\\O seen\\O .\\O", "opinion_tags": "Looking\\O around\\O ,\\O I\\O saw\\O a\\O room\\O full\\O of\\O New\\O Yorkers\\O enjoying\\O a\\O real\\B meal\\O in\\O a\\O real\\O restaurant\\O ,\\O not\\O a\\O clubhouse\\O of\\O the\\O fabulous\\O trying\\O to\\O be\\O seen\\O .\\O"}, {"uid": "91-1", "sentiment": "negative", "target_tags": "Looking\\O around\\O ,\\O I\\O saw\\O a\\O room\\O full\\O of\\O New\\O Yorkers\\O enjoying\\O a\\O real\\O meal\\O in\\O a\\O real\\O restaurant\\O ,\\O not\\O a\\O clubhouse\\B of\\O the\\O fabulous\\O trying\\O to\\O be\\O seen\\O .\\O", "opinion_tags": "Looking\\O around\\O ,\\O I\\O saw\\O a\\O room\\O full\\O of\\O New\\O Yorkers\\O enjoying\\O a\\O real\\O meal\\O in\\O a\\O real\\O restaurant\\O ,\\O not\\O a\\O clubhouse\\O of\\O the\\O fabulous\\B trying\\O to\\O be\\O seen\\O .\\O"}], "postag": ["VBG", "RB", ",", "PRP", "VBD", "DT", "NN", "JJ", "IN", "NNP", "NNPS", "VBG", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", ",", "RB", "DT", "NN", "IN", "DT", "JJ", "VBG", "TO", "VB", "VBN", "."], "head": [5, 1, 1, 5, 0, 7, 5, 7, 11, 11, 8, 7, 15, 15, 12, 19, 19, 19, 12, 23, 23, 23, 7, 26, 26, 23, 23, 30, 30, 27, 5], "deprel": ["advcl", "advmod", "punct", "nsubj", "root", "det", "obj", "amod", "case", "compound", "obl", "acl", "det", "amod", "obj", "case", "det", "amod", "obl", "punct", "advmod", "det", "conj", "case", "det", "nmod", "acl", "mark", "aux:pass", "xcomp", "punct"]}, {"id": "92", "sentence": "We had a great tiem watching the shows and characters and ar food was just what we were looking for .", "triples": [{"uid": "92-0", "sentiment": "positive", "target_tags": "We\\O had\\O a\\O great\\O tiem\\O watching\\O the\\O shows\\B and\\O characters\\O and\\O ar\\O food\\O was\\O just\\O what\\O we\\O were\\O looking\\O for\\O .\\O", "opinion_tags": "We\\O had\\O a\\O great\\B tiem\\O watching\\O the\\O shows\\O and\\O characters\\O and\\O ar\\O food\\O was\\O just\\O what\\O we\\O were\\O looking\\O for\\O .\\O"}, {"uid": "92-1", "sentiment": "positive", "target_tags": "We\\O had\\O a\\O great\\O tiem\\O watching\\O the\\O shows\\O and\\O characters\\O and\\O ar\\O food\\B was\\O just\\O what\\O we\\O were\\O looking\\O for\\O .\\O", "opinion_tags": "We\\O had\\O a\\O great\\O tiem\\O watching\\O the\\O shows\\O and\\O characters\\O and\\O ar\\O food\\O was\\O just\\O what\\O we\\O were\\O looking\\B for\\I .\\O"}, {"uid": "92-2", "sentiment": "positive", "target_tags": "We\\O had\\O a\\O great\\O tiem\\O watching\\O the\\O shows\\O and\\O characters\\B and\\O ar\\O food\\O was\\O just\\O what\\O we\\O were\\O looking\\O for\\O .\\O", "opinion_tags": "We\\O had\\O a\\O great\\B tiem\\O watching\\O the\\O shows\\O and\\O characters\\O and\\O ar\\O food\\O was\\O just\\O what\\O we\\O were\\O looking\\O for\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "JJ", "NN", "VBG", "DT", "NNS", "CC", "NNS", "CC", "NN", "NN", "VBD", "RB", "WP", "PRP", "VBD", "VBG", "IN", "."], "head": [2, 0, 5, 5, 2, 5, 8, 6, 10, 8, 16, 13, 16, 16, 16, 2, 19, 19, 16, 19, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "acl", "det", "obj", "cc", "conj", "cc", "compound", "nsubj", "cop", "advmod", "conj", "nsubj", "aux", "acl:relcl", "obl", "punct"]}, {"id": "93", "sentence": "Not sure where the previous reviewer , lonk , dined , but <PERSON> is in a great neighborhood and has great food !", "triples": [{"uid": "93-0", "sentiment": "positive", "target_tags": "Not\\O sure\\O where\\O the\\O previous\\O reviewer\\O ,\\O lonk\\O ,\\O dined\\O ,\\O but\\O Saul\\O is\\O in\\O a\\O great\\O neighborhood\\B and\\O has\\O great\\O food\\O !\\O", "opinion_tags": "Not\\O sure\\O where\\O the\\O previous\\O reviewer\\O ,\\O lonk\\O ,\\O dined\\O ,\\O but\\O Saul\\O is\\O in\\O a\\O great\\B neighborhood\\O and\\O has\\O great\\O food\\O !\\O"}, {"uid": "93-1", "sentiment": "positive", "target_tags": "Not\\O sure\\O where\\O the\\O previous\\O reviewer\\O ,\\O lonk\\O ,\\O dined\\O ,\\O but\\O Saul\\O is\\O in\\O a\\O great\\O neighborhood\\O and\\O has\\O great\\O food\\B !\\O", "opinion_tags": "Not\\O sure\\O where\\O the\\O previous\\O reviewer\\O ,\\O lonk\\O ,\\O dined\\O ,\\O but\\O Saul\\O is\\O in\\O a\\O great\\O neighborhood\\O and\\O has\\O great\\B food\\O !\\O"}], "postag": ["RB", "JJ", "WRB", "DT", "JJ", "NN", ",", "NNP", ",", "VBD", ",", "CC", "NNP", "VBZ", "IN", "DT", "JJ", "NN", "CC", "VBZ", "JJ", "NN", "."], "head": [2, 0, 10, 6, 6, 10, 8, 6, 10, 2, 14, 18, 18, 18, 18, 18, 18, 10, 20, 18, 22, 20, 2], "deprel": ["advmod", "root", "mark", "det", "amod", "nsubj", "punct", "conj", "punct", "ccomp", "punct", "cc", "nsubj", "cop", "case", "det", "amod", "conj", "cc", "conj", "amod", "obj", "punct"]}, {"id": "94", "sentence": "The noodle and rices dishes taste great .", "triples": [{"uid": "94-0", "sentiment": "positive", "target_tags": "The\\O noodle\\B and\\I rices\\I dishes\\I taste\\O great\\O .\\O", "opinion_tags": "The\\O noodle\\O and\\O rices\\O dishes\\O taste\\O great\\B .\\O"}], "postag": ["DT", "NN", "CC", "NNS", "NNS", "VBP", "JJ", "."], "head": [5, 5, 4, 2, 6, 0, 6, 6], "deprel": ["det", "compound", "cc", "conj", "nsubj", "root", "xcomp", "punct"]}, {"id": "95", "sentence": "The food 's as good as ever .", "triples": [{"uid": "95-0", "sentiment": "positive", "target_tags": "The\\O food\\B 's\\O as\\O good\\O as\\O ever\\O .\\O", "opinion_tags": "The\\O food\\O 's\\O as\\O good\\B as\\O ever\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "IN", "RB", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "obl", "punct"]}, {"id": "96", "sentence": "The rice was poor quality and was cooked so badly it was hard .", "triples": [{"uid": "96-0", "sentiment": "negative", "target_tags": "The\\O rice\\B was\\O poor\\O quality\\O and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\O .\\O", "opinion_tags": "The\\O rice\\O was\\O poor\\B quality\\I and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\O .\\O"}, {"uid": "96-1", "sentiment": "negative", "target_tags": "The\\O rice\\B was\\O poor\\O quality\\O and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\O .\\O", "opinion_tags": "The\\O rice\\O was\\O poor\\O quality\\O and\\O was\\O cooked\\B so\\I badly\\I it\\O was\\O hard\\O .\\O"}, {"uid": "96-2", "sentiment": "negative", "target_tags": "The\\O rice\\B was\\O poor\\O quality\\O and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\O .\\O", "opinion_tags": "The\\O rice\\O was\\O poor\\O quality\\O and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\B .\\O"}, {"uid": "96-3", "sentiment": "negative", "target_tags": "The\\O rice\\O was\\O poor\\O quality\\B and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\O .\\O", "opinion_tags": "The\\O rice\\O was\\O poor\\B quality\\O and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\O .\\O"}, {"uid": "96-4", "sentiment": "negative", "target_tags": "The\\O rice\\O was\\O poor\\O quality\\O and\\O was\\O cooked\\B so\\O badly\\O it\\O was\\O hard\\O .\\O", "opinion_tags": "The\\O rice\\O was\\O poor\\O quality\\O and\\O was\\O cooked\\O so\\O badly\\B it\\O was\\O hard\\O .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "NN", "CC", "VBD", "VBN", "RB", "RB", "PRP", "VBD", "JJ", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 10, 8, 13, 13, 5, 5], "deprel": ["det", "nsubj", "cop", "amod", "root", "cc", "aux:pass", "conj", "advmod", "advmod", "nsubj", "cop", "conj", "punct"]}, {"id": "97", "sentence": "While they keep the capex to a minimum , they do put some cash into the bagels , because they among the best in the city .", "triples": [{"uid": "97-0", "sentiment": "neutral", "target_tags": "While\\O they\\O keep\\O the\\O capex\\O to\\O a\\O minimum\\O ,\\O they\\O do\\O put\\O some\\O cash\\O into\\O the\\O bagels\\B ,\\O because\\O they\\O among\\O the\\O best\\O in\\O the\\O city\\O .\\O", "opinion_tags": "While\\O they\\O keep\\O the\\O capex\\O to\\O a\\O minimum\\O ,\\O they\\O do\\O put\\O some\\O cash\\O into\\O the\\O bagels\\O ,\\O because\\O they\\O among\\O the\\O best\\B in\\O the\\O city\\O .\\O"}], "postag": ["IN", "PRP", "VBP", "DT", "NN", "IN", "DT", "NN", ",", "PRP", "VBP", "VB", "DT", "NN", "IN", "DT", "NNS", ",", "IN", "PRP", "IN", "DT", "JJS", "IN", "DT", "NN", "."], "head": [3, 3, 12, 5, 3, 8, 8, 3, 12, 12, 12, 0, 14, 12, 17, 17, 12, 12, 23, 23, 23, 23, 12, 26, 26, 23, 12], "deprel": ["mark", "nsubj", "advcl", "det", "obj", "case", "det", "obl", "punct", "nsubj", "aux", "root", "det", "obj", "case", "det", "obl", "punct", "mark", "nsubj", "case", "det", "advcl", "case", "det", "obl", "punct"]}, {"id": "98", "sentence": "Have frequented 'ino for several years and the food remains excellent .", "triples": [{"uid": "98-0", "sentiment": "positive", "target_tags": "Have\\O frequented\\O 'ino\\O for\\O several\\O years\\O and\\O the\\O food\\B remains\\O excellent\\O .\\O", "opinion_tags": "Have\\O frequented\\O 'ino\\O for\\O several\\O years\\O and\\O the\\O food\\O remains\\O excellent\\B .\\O"}], "postag": ["VBP", "VBN", "NN", "IN", "JJ", "NNS", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 0, 2, 6, 6, 2, 10, 9, 10, 2, 10, 2], "deprel": ["aux", "root", "obj", "case", "amod", "obl", "cc", "det", "nsubj", "conj", "xcomp", "punct"]}, {"id": "99", "sentence": "My friend got the mushroom pizza which tasted better .", "triples": [{"uid": "99-0", "sentiment": "positive", "target_tags": "My\\O friend\\O got\\O the\\O mushroom\\B pizza\\I which\\O tasted\\O better\\O .\\O", "opinion_tags": "My\\O friend\\O got\\O the\\O mushroom\\O pizza\\O which\\O tasted\\O better\\B .\\O"}], "postag": ["PRP$", "NN", "VBD", "DT", "NN", "NN", "WDT", "VBD", "JJR", "."], "head": [2, 3, 0, 6, 6, 3, 8, 6, 8, 3], "deprel": ["nmod:poss", "nsubj", "root", "det", "compound", "obj", "nsubj", "acl:relcl", "xcomp", "punct"]}, {"id": "100", "sentence": "The porcini mushroom pasta special was tasteless , so was the seafood tagliatelle .", "triples": [{"uid": "100-0", "sentiment": "negative", "target_tags": "The\\O porcini\\B mushroom\\I pasta\\I special\\I was\\O tasteless\\O ,\\O so\\O was\\O the\\O seafood\\O tagliatelle\\O .\\O", "opinion_tags": "The\\O porcini\\O mushroom\\O pasta\\O special\\O was\\O tasteless\\B ,\\O so\\O was\\O the\\O seafood\\O tagliatelle\\O .\\O"}, {"uid": "100-1", "sentiment": "negative", "target_tags": "The\\O porcini\\O mushroom\\O pasta\\O special\\O was\\O tasteless\\O ,\\O so\\O was\\O the\\O seafood\\B tagliatelle\\I .\\O", "opinion_tags": "The\\O porcini\\O mushroom\\O pasta\\O special\\O was\\O tasteless\\B ,\\O so\\O was\\O the\\O seafood\\O tagliatelle\\O .\\O"}], "postag": ["DT", "NN", "NN", "NN", "NN", "VBD", "JJ", ",", "RB", "VBD", "DT", "NN", "NN", "."], "head": [5, 5, 5, 5, 7, 7, 0, 7, 13, 13, 13, 13, 7, 7], "deprel": ["det", "compound", "compound", "compound", "nsubj", "cop", "root", "punct", "advmod", "cop", "det", "compound", "parataxis", "punct"]}, {"id": "101", "sentence": "THE FOOD PORTIONS ARE REALLY LARGE .", "triples": [{"uid": "101-0", "sentiment": "positive", "target_tags": "THE\\O FOOD\\B PORTIONS\\I ARE\\O REALLY\\O LARGE\\O .\\O", "opinion_tags": "THE\\O FOOD\\O PORTIONS\\O ARE\\O REALLY\\O LARGE\\B .\\I"}], "postag": ["DT", "NN", "NNS", "VBP", "RB", "JJ", "."], "head": [3, 3, 6, 6, 6, 0, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "punct"]}, {"id": "102", "sentence": "It looked like shredded cheese partly done - still in strips .", "triples": [{"uid": "102-0", "sentiment": "negative", "target_tags": "It\\O looked\\O like\\O shredded\\B cheese\\I partly\\O done\\O -\\O still\\O in\\O strips\\O .\\O", "opinion_tags": "It\\O looked\\O like\\O shredded\\O cheese\\O partly\\B done\\I -\\O still\\O in\\O strips\\O .\\O"}], "postag": ["PRP", "VBD", "IN", "JJ", "NN", "RB", "VBN", ",", "RB", "IN", "NNS", "."], "head": [2, 0, 5, 5, 2, 7, 5, 2, 11, 11, 2, 2], "deprel": ["nsubj", "root", "case", "amod", "obl", "advmod", "acl", "punct", "advmod", "case", "obl", "punct"]}, {"id": "103", "sentence": "I have never better enjoyed humble root vegetables or a mushroom consomme - and this chef accomplishes without fats .", "triples": [{"uid": "103-0", "sentiment": "positive", "target_tags": "I\\O have\\O never\\O better\\O enjoyed\\O humble\\O root\\B vegetables\\I or\\O a\\O mushroom\\O consomme\\O -\\O and\\O this\\O chef\\O accomplishes\\O without\\O fats\\O .\\O", "opinion_tags": "I\\O have\\O never\\O better\\B enjoyed\\I humble\\O root\\O vegetables\\O or\\O a\\O mushroom\\O consomme\\O -\\O and\\O this\\O chef\\O accomplishes\\O without\\O fats\\O .\\O"}, {"uid": "103-1", "sentiment": "positive", "target_tags": "I\\O have\\O never\\O better\\O enjoyed\\O humble\\O root\\O vegetables\\O or\\O a\\O mushroom\\B consomme\\I -\\O and\\O this\\O chef\\O accomplishes\\O without\\O fats\\O .\\O", "opinion_tags": "I\\O have\\O never\\O better\\B enjoyed\\I humble\\O root\\O vegetables\\O or\\O a\\O mushroom\\O consomme\\O -\\O and\\O this\\O chef\\O accomplishes\\O without\\O fats\\O .\\O"}], "postag": ["PRP", "VBP", "RB", "RBR", "VBN", "JJ", "NN", "NNS", "CC", "DT", "NN", "NN", ",", "CC", "DT", "NN", "VBZ", "IN", "NNS", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 12, 12, 12, 8, 5, 17, 16, 17, 5, 19, 17, 5], "deprel": ["nsubj", "aux", "advmod", "advmod", "root", "amod", "compound", "obj", "cc", "det", "compound", "conj", "punct", "cc", "det", "nsubj", "conj", "case", "obl", "punct"]}, {"id": "104", "sentence": "If you go to <PERSON> 's try to be served by <PERSON> , he is GREAT ! !", "triples": [{"uid": "104-0", "sentiment": "positive", "target_tags": "If\\O you\\O go\\O to\\O Roth\\O 's\\O try\\O to\\O be\\O served\\B by\\O Mike\\O ,\\O he\\O is\\O GREAT\\O !\\O !\\O", "opinion_tags": "If\\O you\\O go\\O to\\O Roth\\O 's\\O try\\O to\\O be\\O served\\O by\\O Mike\\O ,\\O he\\O is\\O GREAT\\B !\\I !\\O"}], "postag": ["IN", "PRP", "VBP", "IN", "NNP", "POS", "NN", "TO", "VB", "VBN", "IN", "NNP", ",", "PRP", "VBZ", "JJ", ".", "."], "head": [3, 3, 16, 7, 7, 5, 3, 10, 10, 3, 12, 10, 16, 16, 16, 0, 16, 16], "deprel": ["mark", "nsubj", "advcl", "case", "nmod:poss", "case", "obl", "mark", "aux:pass", "advcl", "case", "obl", "punct", "nsubj", "cop", "root", "punct", "punct"]}, {"id": "105", "sentence": "The food is amazing ! ! ! !", "triples": [{"uid": "105-0", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O amazing\\O !\\O !\\O !\\O !\\O", "opinion_tags": "The\\O food\\O is\\O amazing\\B !\\O !\\O !\\O !\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ".", ".", ".", "."], "head": [2, 4, 4, 0, 4, 4, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "punct", "punct", "punct"]}, {"id": "106", "sentence": "Interesting selection , good wines , service fine , fun decor .", "triples": [{"uid": "106-0", "sentiment": "positive", "target_tags": "Interesting\\O selection\\O ,\\O good\\O wines\\B ,\\O service\\O fine\\O ,\\O fun\\O decor\\O .\\O", "opinion_tags": "Interesting\\O selection\\O ,\\O good\\B wines\\O ,\\O service\\O fine\\O ,\\O fun\\O decor\\O .\\O"}, {"uid": "106-1", "sentiment": "positive", "target_tags": "Interesting\\O selection\\O ,\\O good\\O wines\\O ,\\O service\\B fine\\O ,\\O fun\\O decor\\O .\\O", "opinion_tags": "Interesting\\O selection\\O ,\\O good\\O wines\\O ,\\O service\\O fine\\B ,\\O fun\\O decor\\O .\\O"}, {"uid": "106-2", "sentiment": "positive", "target_tags": "Interesting\\O selection\\O ,\\O good\\O wines\\O ,\\O service\\O fine\\O ,\\O fun\\O decor\\B .\\O", "opinion_tags": "Interesting\\O selection\\O ,\\O good\\O wines\\O ,\\O service\\O fine\\O ,\\O fun\\B decor\\O .\\O"}], "postag": ["JJ", "NN", ",", "JJ", "NNS", ",", "NN", "JJ", ",", "JJ", "NN", "."], "head": [2, 0, 2, 5, 2, 2, 8, 2, 2, 11, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "compound", "list", "punct", "amod", "list", "punct"]}, {"id": "107", "sentence": "The rice to fish ration was also good -- they did n't try to overpack the rice .", "triples": [{"uid": "107-0", "sentiment": "positive", "target_tags": "The\\O rice\\B to\\I fish\\I ration\\I was\\O also\\O good\\O --\\O they\\O did\\O n't\\O try\\O to\\O overpack\\O the\\O rice\\O .\\O", "opinion_tags": "The\\O rice\\O to\\O fish\\O ration\\O was\\O also\\O good\\B --\\O they\\O did\\O n't\\O try\\O to\\O overpack\\O the\\O rice\\O .\\O"}], "postag": ["DT", "NN", "IN", "NN", "NN", "VBD", "RB", "JJ", ",", "PRP", "VBD", "RB", "VB", "TO", "VB", "DT", "NN", "."], "head": [2, 8, 5, 5, 2, 8, 8, 0, 8, 13, 13, 13, 8, 15, 13, 17, 15, 8], "deprel": ["det", "nsubj", "case", "compound", "nmod", "cop", "advmod", "root", "punct", "nsubj", "aux", "advmod", "parataxis", "mark", "xcomp", "det", "obj", "punct"]}, {"id": "108", "sentence": "The food is great and they have a good selecion of wines at reasonable prices .", "triples": [{"uid": "108-0", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O great\\O and\\O they\\O have\\O a\\O good\\O selecion\\O of\\O wines\\O at\\O reasonable\\O prices\\O .\\O", "opinion_tags": "The\\O food\\O is\\O great\\B and\\O they\\O have\\O a\\O good\\O selecion\\O of\\O wines\\O at\\O reasonable\\O prices\\O .\\O"}, {"uid": "108-1", "sentiment": "positive", "target_tags": "The\\O food\\O is\\O great\\O and\\O they\\O have\\O a\\O good\\O selecion\\B of\\I wines\\I at\\O reasonable\\O prices\\O .\\O", "opinion_tags": "The\\O food\\O is\\O great\\O and\\O they\\O have\\O a\\O good\\B selecion\\O of\\O wines\\O at\\O reasonable\\O prices\\O .\\O"}, {"uid": "108-2", "sentiment": "positive", "target_tags": "The\\O food\\O is\\O great\\O and\\O they\\O have\\O a\\O good\\O selecion\\O of\\O wines\\O at\\O reasonable\\O prices\\B .\\O", "opinion_tags": "The\\O food\\O is\\O great\\O and\\O they\\O have\\O a\\O good\\O selecion\\O of\\O wines\\O at\\O reasonable\\B prices\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "PRP", "VBP", "DT", "JJ", "NN", "IN", "NNS", "IN", "JJ", "NNS", "."], "head": [2, 4, 4, 0, 7, 7, 4, 10, 10, 7, 12, 10, 15, 15, 7, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "nsubj", "conj", "det", "amod", "obj", "case", "nmod", "case", "amod", "obl", "punct"]}, {"id": "109", "sentence": "I could n't reccommend their God<PERSON> pizza any higher .", "triples": [{"uid": "109-0", "sentiment": "positive", "target_tags": "I\\O could\\O n't\\O reccommend\\O their\\O Godmother\\B pizza\\I any\\O higher\\O .\\O", "opinion_tags": "I\\O could\\O n't\\O reccommend\\B their\\O Godmother\\O pizza\\O any\\O higher\\O .\\O"}], "postag": ["PRP", "MD", "RB", "VB", "PRP$", "NN", "NN", "DT", "JJR", "."], "head": [4, 4, 4, 0, 7, 7, 4, 9, 4, 4], "deprel": ["nsubj", "aux", "advmod", "root", "nmod:poss", "compound", "obj", "advmod", "advmod", "punct"]}, {"id": "110", "sentence": "Always a nice crowd , but never loud .", "triples": [{"uid": "110-0", "sentiment": "positive", "target_tags": "Always\\O a\\O nice\\O crowd\\B ,\\O but\\O never\\O loud\\O .\\O", "opinion_tags": "Always\\O a\\O nice\\B crowd\\O ,\\O but\\O never\\O loud\\O .\\O"}, {"uid": "110-1", "sentiment": "positive", "target_tags": "Always\\O a\\O nice\\O crowd\\B ,\\O but\\O never\\O loud\\O .\\O", "opinion_tags": "Always\\O a\\O nice\\O crowd\\O ,\\O but\\O never\\B loud\\I .\\O"}], "postag": ["RB", "DT", "JJ", "NN", ",", "CC", "RB", "JJ", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 4], "deprel": ["advmod", "det", "amod", "root", "punct", "cc", "advmod", "conj", "punct"]}, {"id": "111", "sentence": "Decor is minimalist and clean - nothing to distract or commend .", "triples": [{"uid": "111-0", "sentiment": "neutral", "target_tags": "Decor\\B is\\O minimalist\\O and\\O clean\\O -\\O nothing\\O to\\O distract\\O or\\O commend\\O .\\O", "opinion_tags": "Decor\\O is\\O minimalist\\B and\\O clean\\O -\\O nothing\\O to\\O distract\\O or\\O commend\\O .\\O"}, {"uid": "111-1", "sentiment": "neutral", "target_tags": "Decor\\B is\\O minimalist\\O and\\O clean\\O -\\O nothing\\O to\\O distract\\O or\\O commend\\O .\\O", "opinion_tags": "Decor\\O is\\O minimalist\\O and\\O clean\\B -\\O nothing\\O to\\O distract\\O or\\O commend\\O .\\O"}], "postag": ["NNP", "VBZ", "JJ", "CC", "JJ", ",", "NN", "TO", "VB", "CC", "VB", "."], "head": [3, 3, 0, 5, 3, 7, 3, 9, 7, 11, 9, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "punct", "parataxis", "mark", "acl", "cc", "conj", "punct"]}, {"id": "112", "sentence": "The people in the restaurant were pretty obnoxious and loud .", "triples": [{"uid": "112-0", "sentiment": "negative", "target_tags": "The\\O people\\B in\\O the\\O restaurant\\O were\\O pretty\\O obnoxious\\O and\\O loud\\O .\\O", "opinion_tags": "The\\O people\\O in\\O the\\O restaurant\\O were\\O pretty\\O obnoxious\\B and\\O loud\\O .\\O"}, {"uid": "112-1", "sentiment": "negative", "target_tags": "The\\O people\\B in\\O the\\O restaurant\\O were\\O pretty\\O obnoxious\\O and\\O loud\\O .\\O", "opinion_tags": "The\\O people\\O in\\O the\\O restaurant\\O were\\O pretty\\O obnoxious\\O and\\O loud\\B .\\O"}], "postag": ["DT", "NNS", "IN", "DT", "NN", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [2, 8, 5, 5, 2, 8, 8, 0, 10, 8, 8], "deprel": ["det", "nsubj", "case", "det", "nmod", "cop", "advmod", "root", "cc", "conj", "punct"]}, {"id": "113", "sentence": "Price no more than a Jersey deli but way better .", "triples": [{"uid": "113-0", "sentiment": "positive", "target_tags": "Price\\B no\\O more\\O than\\O a\\O Jersey\\O deli\\O but\\O way\\O better\\O .\\O", "opinion_tags": "Price\\O no\\O more\\O than\\O a\\O Jersey\\O deli\\O but\\O way\\O better\\B .\\O"}], "postag": ["NN", "RB", "JJR", "IN", "DT", "NNP", "NN", "CC", "RB", "JJR", "."], "head": [0, 3, 1, 7, 7, 7, 3, 10, 10, 3, 1], "deprel": ["root", "advmod", "advmod", "case", "det", "compound", "obl", "cc", "advmod", "conj", "punct"]}, {"id": "114", "sentence": "THe back garden sitting area is very pleasant , where you can see their personal herb garden .", "triples": [{"uid": "114-0", "sentiment": "positive", "target_tags": "THe\\O back\\B garden\\I sitting\\I area\\I is\\O very\\O pleasant\\O ,\\O where\\O you\\O can\\O see\\O their\\O personal\\O herb\\O garden\\O .\\O", "opinion_tags": "THe\\O back\\O garden\\O sitting\\O area\\O is\\O very\\O pleasant\\B ,\\O where\\O you\\O can\\O see\\O their\\O personal\\O herb\\O garden\\O .\\O"}], "postag": ["DT", "JJ", "NN", "NN", "NN", "VBZ", "RB", "JJ", ",", "WRB", "PRP", "MD", "VB", "PRP$", "JJ", "NN", "NN", "."], "head": [5, 3, 5, 5, 8, 8, 8, 0, 8, 13, 13, 13, 8, 17, 17, 17, 13, 8], "deprel": ["det", "amod", "compound", "compound", "nsubj", "cop", "advmod", "root", "punct", "mark", "nsubj", "aux", "advcl", "nmod:poss", "amod", "compound", "obj", "punct"]}, {"id": "115", "sentence": "The drinks are always well made and wine selection is fairly priced .", "triples": [{"uid": "115-0", "sentiment": "positive", "target_tags": "The\\O drinks\\B are\\O always\\O well\\O made\\O and\\O wine\\O selection\\O is\\O fairly\\O priced\\O .\\O", "opinion_tags": "The\\O drinks\\O are\\O always\\O well\\B made\\I and\\O wine\\O selection\\O is\\O fairly\\O priced\\O .\\O"}, {"uid": "115-1", "sentiment": "neutral", "target_tags": "The\\O drinks\\O are\\O always\\O well\\O made\\O and\\O wine\\B selection\\I is\\O fairly\\O priced\\O .\\O", "opinion_tags": "The\\O drinks\\O are\\O always\\O well\\O made\\O and\\O wine\\O selection\\O is\\O fairly\\B priced\\I .\\O"}, {"uid": "115-2", "sentiment": "positive", "target_tags": "The\\O drinks\\O are\\O always\\O well\\O made\\O and\\O wine\\O selection\\O is\\O fairly\\O priced\\B .\\O", "opinion_tags": "The\\O drinks\\O are\\O always\\O well\\O made\\O and\\O wine\\O selection\\O is\\O fairly\\B priced\\O .\\O"}], "postag": ["DT", "NNS", "VBP", "RB", "RB", "VBN", "CC", "NN", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 6, 6, 6, 6, 0, 12, 9, 12, 12, 12, 6, 6], "deprel": ["det", "nsubj:pass", "aux:pass", "advmod", "advmod", "root", "cc", "compound", "nsubj", "cop", "advmod", "conj", "punct"]}, {"id": "116", "sentence": "Food was average but tasty .", "triples": [{"uid": "116-0", "sentiment": "positive", "target_tags": "Food\\B was\\O average\\O but\\O tasty\\O .\\O", "opinion_tags": "Food\\O was\\O average\\B but\\O tasty\\O .\\O"}, {"uid": "116-1", "sentiment": "positive", "target_tags": "Food\\B was\\O average\\O but\\O tasty\\O .\\O", "opinion_tags": "Food\\O was\\O average\\O but\\O tasty\\B .\\O"}], "postag": ["NN", "VBD", "JJ", "CC", "JJ", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "punct"]}, {"id": "117", "sentence": "Admittedly some nights inside the restaurant were rather warm , but the open kitchen is part of the charm .", "triples": [{"uid": "117-0", "sentiment": "positive", "target_tags": "Admittedly\\O some\\O nights\\O inside\\O the\\O restaurant\\O were\\O rather\\O warm\\O ,\\O but\\O the\\O open\\B kitchen\\I is\\O part\\O of\\O the\\O charm\\O .\\O", "opinion_tags": "Admittedly\\O some\\O nights\\O inside\\O the\\O restaurant\\O were\\O rather\\O warm\\O ,\\O but\\O the\\O open\\O kitchen\\O is\\O part\\O of\\O the\\O charm\\B .\\O"}], "postag": ["RB", "DT", "NNS", "IN", "DT", "NN", "VBD", "RB", "JJ", ",", "CC", "DT", "JJ", "NN", "VBZ", "NN", "IN", "DT", "NN", "."], "head": [9, 3, 9, 6, 6, 3, 9, 9, 0, 16, 16, 14, 14, 16, 16, 9, 19, 19, 16, 9], "deprel": ["advmod", "det", "nsubj", "case", "det", "nmod", "cop", "advmod", "root", "punct", "cc", "det", "amod", "nsubj", "cop", "conj", "case", "det", "nmod", "punct"]}, {"id": "118", "sentence": "I 'm still mad that i had to pay for lousy food .", "triples": [{"uid": "118-0", "sentiment": "negative", "target_tags": "I\\O 'm\\O still\\O mad\\O that\\O i\\O had\\O to\\O pay\\O for\\O lousy\\O food\\B .\\O", "opinion_tags": "I\\O 'm\\O still\\O mad\\O that\\O i\\O had\\O to\\O pay\\O for\\O lousy\\B food\\O .\\O"}], "postag": ["PRP", "VBP", "RB", "JJ", "IN", "PRP", "VBD", "TO", "VB", "IN", "JJ", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 9, 7, 12, 12, 9, 4], "deprel": ["nsubj", "cop", "advmod", "root", "mark", "nsubj", "ccomp", "mark", "xcomp", "case", "amod", "obl", "punct"]}, {"id": "119", "sentence": "Worse of all , $ 60 was erroneously added to our $ 80 bill .", "triples": [{"uid": "119-0", "sentiment": "negative", "target_tags": "Worse\\O of\\O all\\O ,\\O $\\O 60\\O was\\O erroneously\\O added\\O to\\O our\\O $\\O 80\\O bill\\B .\\O", "opinion_tags": "Worse\\O of\\O all\\O ,\\O $\\O 60\\O was\\O erroneously\\B added\\O to\\O our\\O $\\O 80\\O bill\\O .\\O"}], "postag": ["JJR", "IN", "DT", ",", "$", "CD", "VBD", "RB", "VBN", "IN", "PRP$", "$", "CD", "NN", "."], "head": [9, 3, 1, 9, 9, 5, 9, 9, 0, 14, 14, 14, 12, 9, 9], "deprel": ["advmod", "case", "obl", "punct", "nsubj:pass", "nummod", "aux:pass", "advmod", "root", "case", "nmod:poss", "compound", "nummod", "obl", "punct"]}, {"id": "120", "sentence": "Someone else recommended the dessert - we also left that .", "triples": [{"uid": "120-0", "sentiment": "negative", "target_tags": "Someone\\O else\\O recommended\\O the\\O dessert\\B -\\O we\\O also\\O left\\O that\\O .\\O", "opinion_tags": "Someone\\O else\\O recommended\\B the\\O dessert\\O -\\O we\\O also\\O left\\O that\\O .\\O"}], "postag": ["NN", "JJ", "VBD", "DT", "NN", ",", "PRP", "RB", "VBD", "DT", "."], "head": [3, 1, 0, 5, 3, 3, 9, 9, 3, 9, 3], "deprel": ["nsubj", "amod", "root", "det", "obj", "punct", "nsubj", "advmod", "parataxis", "obj", "punct"]}, {"id": "121", "sentence": "It was nice and fresh , but I ca n't give it high scores on being authentic thai .", "triples": [{"uid": "121-0", "sentiment": "negative", "target_tags": "It\\O was\\O nice\\O and\\O fresh\\O ,\\O but\\O I\\O ca\\O n't\\O give\\O it\\O high\\O scores\\O on\\O being\\O authentic\\O thai\\B .\\O", "opinion_tags": "It\\O was\\O nice\\O and\\O fresh\\O ,\\O but\\O I\\O ca\\O n't\\O give\\O it\\O high\\O scores\\O on\\O being\\O authentic\\B thai\\O .\\O"}], "postag": ["PRP", "VBD", "JJ", "CC", "JJ", ",", "CC", "PRP", "MD", "RB", "VB", "PRP", "JJ", "NNS", "IN", "VBG", "JJ", "NN", "."], "head": [3, 3, 0, 5, 3, 11, 11, 11, 11, 11, 3, 11, 14, 11, 18, 18, 18, 11, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "punct", "cc", "nsubj", "aux", "advmod", "conj", "i<PERSON><PERSON>", "amod", "obj", "mark", "cop", "amod", "advcl", "punct"]}, {"id": "122", "sentence": "Oh , and the complimentary pudding dessert was just enough - yummy !", "triples": [{"uid": "122-0", "sentiment": "positive", "target_tags": "Oh\\O ,\\O and\\O the\\O complimentary\\O pudding\\B dessert\\I was\\O just\\O enough\\O -\\O yummy\\O !\\O", "opinion_tags": "Oh\\O ,\\O and\\O the\\O complimentary\\O pudding\\O dessert\\O was\\O just\\O enough\\B -\\O yummy\\O !\\O"}, {"uid": "122-1", "sentiment": "positive", "target_tags": "Oh\\O ,\\O and\\O the\\O complimentary\\O pudding\\B dessert\\I was\\O just\\O enough\\O -\\O yummy\\O !\\O", "opinion_tags": "Oh\\O ,\\O and\\O the\\O complimentary\\O pudding\\O dessert\\O was\\O just\\O enough\\O -\\O yummy\\B !\\O"}], "postag": ["UH", ",", "CC", "DT", "JJ", "NN", "NN", "VBD", "RB", "JJ", "HYPH", "JJ", "."], "head": [12, 12, 12, 7, 7, 7, 12, 12, 12, 12, 12, 0, 12], "deprel": ["discourse", "punct", "cc", "det", "amod", "compound", "nsubj", "cop", "advmod", "amod", "punct", "root", "punct"]}, {"id": "123", "sentence": "It saves walking in and waiting for a table in the often noisy , crowded bar at dinnertime .", "triples": [{"uid": "123-0", "sentiment": "negative", "target_tags": "It\\O saves\\O walking\\O in\\O and\\O waiting\\O for\\O a\\O table\\O in\\O the\\O often\\O noisy\\O ,\\O crowded\\O bar\\B at\\O dinnertime\\O .\\O", "opinion_tags": "It\\O saves\\O walking\\O in\\O and\\O waiting\\O for\\O a\\O table\\O in\\O the\\O often\\O noisy\\B ,\\O crowded\\O bar\\O at\\O dinnertime\\O .\\O"}, {"uid": "123-1", "sentiment": "negative", "target_tags": "It\\O saves\\O walking\\O in\\O and\\O waiting\\O for\\O a\\O table\\O in\\O the\\O often\\O noisy\\O ,\\O crowded\\O bar\\B at\\O dinnertime\\O .\\O", "opinion_tags": "It\\O saves\\O walking\\O in\\O and\\O waiting\\O for\\O a\\O table\\O in\\O the\\O often\\O noisy\\O ,\\O crowded\\B bar\\O at\\O dinnertime\\O .\\O"}, {"uid": "123-2", "sentiment": "negative", "target_tags": "It\\O saves\\O walking\\O in\\O and\\O waiting\\B for\\O a\\O table\\O in\\O the\\O often\\O noisy\\O ,\\O crowded\\O bar\\O at\\O dinnertime\\O .\\O", "opinion_tags": "It\\O saves\\B walking\\O in\\O and\\O waiting\\O for\\O a\\O table\\O in\\O the\\O often\\O noisy\\O ,\\O crowded\\O bar\\O at\\O dinnertime\\O .\\O"}], "postag": ["PRP", "VBZ", "VBG", "RB", "CC", "VBG", "IN", "DT", "NN", "IN", "DT", "RB", "JJ", ",", "JJ", "NN", "IN", "NN", "."], "head": [2, 0, 2, 3, 6, 3, 9, 9, 6, 16, 16, 13, 16, 16, 16, 9, 18, 16, 2], "deprel": ["nsubj", "root", "xcomp", "advmod", "cc", "conj", "case", "det", "obl", "case", "det", "advmod", "amod", "punct", "amod", "nmod", "case", "nmod", "punct"]}, {"id": "124", "sentence": "Mine was a little burnt but still delicious with goat cheese and panchetta ( rad<PERSON>hio was kind of bitter though ) .", "triples": [{"uid": "124-0", "sentiment": "positive", "target_tags": "Mine\\O was\\O a\\O little\\O burnt\\O but\\O still\\O delicious\\O with\\O goat\\B cheese\\I and\\O panchetta\\O (\\O raddichio\\O was\\O kind\\O of\\O bitter\\O though\\O )\\O .\\O", "opinion_tags": "Mine\\O was\\O a\\O little\\O burnt\\O but\\O still\\O delicious\\B with\\O goat\\O cheese\\O and\\O panchetta\\O (\\O raddichio\\O was\\O kind\\O of\\O bitter\\O though\\O )\\O .\\O"}, {"uid": "124-1", "sentiment": "positive", "target_tags": "Mine\\O was\\O a\\O little\\O burnt\\O but\\O still\\O delicious\\O with\\O goat\\O cheese\\O and\\O panchetta\\B (\\O raddichio\\O was\\O kind\\O of\\O bitter\\O though\\O )\\O .\\O", "opinion_tags": "Mine\\O was\\O a\\O little\\O burnt\\O but\\O still\\O delicious\\B with\\O goat\\O cheese\\O and\\O panchetta\\O (\\O raddichio\\O was\\O kind\\O of\\O bitter\\O though\\O )\\O .\\O"}, {"uid": "124-2", "sentiment": "negative", "target_tags": "Mine\\O was\\O a\\O little\\O burnt\\O but\\O still\\O delicious\\O with\\O goat\\O cheese\\O and\\O panchetta\\O (\\O raddichio\\B was\\O kind\\O of\\O bitter\\O though\\O )\\O .\\O", "opinion_tags": "Mine\\O was\\O a\\O little\\O burnt\\O but\\O still\\O delicious\\O with\\O goat\\O cheese\\O and\\O panchetta\\O (\\O raddichio\\O was\\O kind\\O of\\O bitter\\B though\\O )\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "JJ", "JJ", "CC", "RB", "JJ", "IN", "NN", "NN", "CC", "NN", "-LRB-", "NN", "VBD", "RB", "RB", "JJ", "RB", "-RRB-", "."], "head": [5, 5, 4, 5, 0, 8, 8, 5, 11, 11, 8, 13, 11, 19, 19, 19, 19, 17, 5, 19, 19, 5], "deprel": ["nsubj", "cop", "det", "obl:npmod", "root", "cc", "advmod", "conj", "case", "compound", "obl", "cc", "conj", "punct", "nsubj", "cop", "advmod", "fixed", "parataxis", "advmod", "punct", "punct"]}, {"id": "125", "sentence": "Disappointing food , lousy service .", "triples": [{"uid": "125-0", "sentiment": "negative", "target_tags": "Disappointing\\O food\\B ,\\O lousy\\O service\\O .\\O", "opinion_tags": "Disappointing\\B food\\O ,\\O lousy\\O service\\O .\\O"}, {"uid": "125-1", "sentiment": "negative", "target_tags": "Disappointing\\O food\\O ,\\O lousy\\O service\\B .\\O", "opinion_tags": "Disappointing\\O food\\O ,\\O lousy\\B service\\O .\\O"}], "postag": ["JJ", "NN", ",", "JJ", "NN", "."], "head": [2, 0, 2, 5, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct"]}, {"id": "126", "sentence": "Over price , and small portions .", "triples": [{"uid": "126-0", "sentiment": "negative", "target_tags": "Over\\O price\\B ,\\O and\\O small\\O portions\\O .\\O", "opinion_tags": "Over\\B price\\O ,\\O and\\O small\\O portions\\O .\\O"}, {"uid": "126-1", "sentiment": "negative", "target_tags": "Over\\O price\\O ,\\O and\\O small\\O portions\\B .\\O", "opinion_tags": "Over\\O price\\O ,\\O and\\O small\\B portions\\O .\\O"}], "postag": ["IN", "NN", ",", "CC", "JJ", "NNS", "."], "head": [2, 0, 6, 6, 6, 2, 2], "deprel": ["case", "root", "punct", "cc", "amod", "conj", "punct"]}, {"id": "127", "sentence": "My husband said the portions were very small , but if my main course was good to eat the portion would 've been fine for me .", "triples": [{"uid": "127-0", "sentiment": "negative", "target_tags": "My\\O husband\\O said\\O the\\O portions\\B were\\O very\\O small\\O ,\\O but\\O if\\O my\\O main\\O course\\O was\\O good\\O to\\O eat\\O the\\O portion\\O would\\O 've\\O been\\O fine\\O for\\O me\\O .\\O", "opinion_tags": "My\\O husband\\O said\\O the\\O portions\\O were\\O very\\O small\\B ,\\O but\\O if\\O my\\O main\\O course\\O was\\O good\\O to\\O eat\\O the\\O portion\\O would\\O 've\\O been\\O fine\\O for\\O me\\O .\\O"}, {"uid": "127-1", "sentiment": "negative", "target_tags": "My\\O husband\\O said\\O the\\O portions\\O were\\O very\\O small\\O ,\\O but\\O if\\O my\\O main\\B course\\I was\\O good\\O to\\O eat\\O the\\O portion\\O would\\O 've\\O been\\O fine\\O for\\O me\\O .\\O", "opinion_tags": "My\\O husband\\O said\\O the\\O portions\\O were\\O very\\O small\\O ,\\O but\\O if\\O my\\O main\\O course\\O was\\O good\\B to\\O eat\\O the\\O portion\\O would\\O 've\\O been\\O fine\\O for\\O me\\O .\\O"}], "postag": ["PRP$", "NN", "VBD", "DT", "NNS", "VBD", "RB", "JJ", ",", "CC", "IN", "PRP$", "JJ", "NN", "VBD", "JJ", "TO", "VB", "DT", "NN", "MD", "VB", "VBN", "JJ", "IN", "PRP", "."], "head": [2, 3, 0, 5, 8, 8, 8, 3, 24, 24, 16, 14, 14, 16, 16, 24, 18, 16, 20, 18, 24, 24, 24, 3, 26, 24, 3], "deprel": ["nmod:poss", "nsubj", "root", "det", "nsubj", "cop", "advmod", "ccomp", "punct", "cc", "mark", "nmod:poss", "amod", "nsubj", "cop", "advcl", "mark", "xcomp", "det", "obj", "aux", "aux", "cop", "conj", "case", "obl", "punct"]}, {"id": "128", "sentence": "Because we did n't have a reservation , we could only sit in the back garden , but it was great , secluded and perfect in nice weather .", "triples": [{"uid": "128-0", "sentiment": "positive", "target_tags": "Because\\O we\\O did\\O n't\\O have\\O a\\O reservation\\O ,\\O we\\O could\\O only\\O sit\\O in\\O the\\O back\\B garden\\I ,\\O but\\O it\\O was\\O great\\O ,\\O secluded\\O and\\O perfect\\O in\\O nice\\O weather\\O .\\O", "opinion_tags": "Because\\O we\\O did\\O n't\\O have\\O a\\O reservation\\O ,\\O we\\O could\\O only\\O sit\\O in\\O the\\O back\\O garden\\O ,\\O but\\O it\\O was\\O great\\B ,\\O secluded\\O and\\O perfect\\O in\\O nice\\O weather\\O .\\O"}, {"uid": "128-1", "sentiment": "positive", "target_tags": "Because\\O we\\O did\\O n't\\O have\\O a\\O reservation\\O ,\\O we\\O could\\O only\\O sit\\O in\\O the\\O back\\B garden\\I ,\\O but\\O it\\O was\\O great\\O ,\\O secluded\\O and\\O perfect\\O in\\O nice\\O weather\\O .\\O", "opinion_tags": "Because\\O we\\O did\\O n't\\O have\\O a\\O reservation\\O ,\\O we\\O could\\O only\\O sit\\O in\\O the\\O back\\O garden\\O ,\\O but\\O it\\O was\\O great\\O ,\\O secluded\\B and\\O perfect\\O in\\O nice\\O weather\\O .\\O"}, {"uid": "128-2", "sentiment": "positive", "target_tags": "Because\\O we\\O did\\O n't\\O have\\O a\\O reservation\\O ,\\O we\\O could\\O only\\O sit\\O in\\O the\\O back\\B garden\\I ,\\O but\\O it\\O was\\O great\\O ,\\O secluded\\O and\\O perfect\\O in\\O nice\\O weather\\O .\\O", "opinion_tags": "Because\\O we\\O did\\O n't\\O have\\O a\\O reservation\\O ,\\O we\\O could\\O only\\O sit\\O in\\O the\\O back\\O garden\\O ,\\O but\\O it\\O was\\O great\\O ,\\O secluded\\O and\\O perfect\\B in\\O nice\\O weather\\O .\\O"}], "postag": ["IN", "PRP", "VBD", "RB", "VB", "DT", "NN", ",", "PRP", "MD", "RB", "VB", "IN", "DT", "JJ", "NN", ",", "CC", "PRP", "VBD", "JJ", ",", "JJ", "CC", "JJ", "IN", "JJ", "NN", "."], "head": [5, 5, 5, 5, 12, 7, 5, 12, 12, 12, 12, 0, 16, 16, 16, 12, 21, 21, 21, 21, 12, 23, 21, 25, 21, 28, 28, 21, 12], "deprel": ["mark", "nsubj", "aux", "advmod", "advcl", "det", "obj", "punct", "nsubj", "aux", "advmod", "root", "case", "det", "amod", "obl", "punct", "cc", "nsubj", "cop", "conj", "punct", "conj", "cc", "conj", "case", "amod", "obl", "punct"]}, {"id": "129", "sentence": "Stuffing yourself with Japanese food is a rare thing .", "triples": [{"uid": "129-0", "sentiment": "neutral", "target_tags": "Stuffing\\O yourself\\O with\\O Japanese\\B food\\I is\\O a\\O rare\\O thing\\O .\\O", "opinion_tags": "Stuffing\\O yourself\\O with\\O Japanese\\O food\\O is\\O a\\O rare\\B thing\\O .\\O"}], "postag": ["VBG", "PRP", "IN", "JJ", "NN", "VBZ", "DT", "JJ", "NN", "."], "head": [9, 1, 5, 5, 1, 9, 9, 9, 0, 9], "deprel": ["csubj", "obj", "case", "amod", "obl", "cop", "det", "amod", "root", "punct"]}, {"id": "130", "sentence": "Our family never expected such incredible entertainment in a restaurant .", "triples": [{"uid": "130-0", "sentiment": "positive", "target_tags": "Our\\O family\\O never\\O expected\\O such\\O incredible\\O entertainment\\B in\\O a\\O restaurant\\O .\\O", "opinion_tags": "Our\\O family\\O never\\O expected\\O such\\O incredible\\B entertainment\\O in\\O a\\O restaurant\\O .\\O"}], "postag": ["PRP$", "NN", "RB", "VBD", "JJ", "JJ", "NN", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 10, 10, 4, 4], "deprel": ["nmod:poss", "nsubj", "advmod", "root", "amod", "amod", "obj", "case", "det", "obl", "punct"]}, {"id": "131", "sentence": "The all you can eat deal is truly amazing here .", "triples": [{"uid": "131-0", "sentiment": "positive", "target_tags": "The\\O all\\B you\\I can\\I eat\\I deal\\I is\\O truly\\O amazing\\O here\\O .\\O", "opinion_tags": "The\\O all\\O you\\O can\\O eat\\O deal\\O is\\O truly\\O amazing\\B here\\O .\\O"}], "postag": ["DT", "DT", "PRP", "MD", "VB", "NN", "VBZ", "RB", "JJ", "RB", "."], "head": [2, 9, 5, 5, 2, 5, 9, 9, 0, 9, 9], "deprel": ["det", "nsubj", "nsubj", "aux", "acl:relcl", "obj", "cop", "advmod", "root", "advmod", "punct"]}, {"id": "132", "sentence": "The dining room is quietly elegant with no music to shout over -- how refreshing !", "triples": [{"uid": "132-0", "sentiment": "positive", "target_tags": "The\\O dining\\B room\\I is\\O quietly\\O elegant\\O with\\O no\\O music\\O to\\O shout\\O over\\O --\\O how\\O refreshing\\O !\\O", "opinion_tags": "The\\O dining\\O room\\O is\\O quietly\\O elegant\\B with\\O no\\O music\\O to\\O shout\\O over\\O --\\O how\\O refreshing\\O !\\O"}, {"uid": "132-1", "sentiment": "positive", "target_tags": "The\\O dining\\B room\\I is\\O quietly\\O elegant\\O with\\O no\\O music\\O to\\O shout\\O over\\O --\\O how\\O refreshing\\O !\\O", "opinion_tags": "The\\O dining\\O room\\O is\\O quietly\\O elegant\\O with\\O no\\O music\\O to\\O shout\\O over\\O --\\O how\\O refreshing\\B !\\O"}], "postag": ["DT", "NN", "NN", "VBZ", "RB", "JJ", "IN", "DT", "NN", "TO", "VB", "RP", ",", "WRB", "JJ", "."], "head": [3, 3, 6, 6, 6, 0, 9, 9, 6, 11, 6, 11, 6, 15, 6, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "case", "det", "obl", "mark", "advcl", "compound:prt", "punct", "mark", "parataxis", "punct"]}, {"id": "133", "sentence": "They have it all -- great price , food , and service .", "triples": [{"uid": "133-0", "sentiment": "positive", "target_tags": "They\\O have\\O it\\O all\\O --\\O great\\O price\\B ,\\O food\\O ,\\O and\\O service\\O .\\O", "opinion_tags": "They\\O have\\O it\\O all\\O --\\O great\\B price\\O ,\\O food\\O ,\\O and\\O service\\O .\\O"}, {"uid": "133-1", "sentiment": "positive", "target_tags": "They\\O have\\O it\\O all\\O --\\O great\\O price\\O ,\\O food\\B ,\\O and\\O service\\O .\\O", "opinion_tags": "They\\O have\\O it\\O all\\O --\\O great\\B price\\O ,\\O food\\O ,\\O and\\O service\\O .\\O"}, {"uid": "133-2", "sentiment": "positive", "target_tags": "They\\O have\\O it\\O all\\O --\\O great\\O price\\O ,\\O food\\O ,\\O and\\O service\\B .\\O", "opinion_tags": "They\\O have\\O it\\O all\\O --\\O great\\B price\\O ,\\O food\\O ,\\O and\\O service\\O .\\O"}], "postag": ["PRP", "VBP", "PRP", "DT", ",", "JJ", "NN", ",", "NN", ",", "CC", "NN", "."], "head": [2, 0, 2, 3, 7, 7, 2, 9, 7, 12, 12, 7, 2], "deprel": ["nsubj", "root", "obj", "det", "punct", "amod", "parataxis", "punct", "conj", "punct", "cc", "conj", "punct"]}, {"id": "134", "sentence": "The food was amazing , the service was so attentive and personable , and how about that ambience !", "triples": [{"uid": "134-0", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O amazing\\O ,\\O the\\O service\\O was\\O so\\O attentive\\O and\\O personable\\O ,\\O and\\O how\\O about\\O that\\O ambience\\O !\\O", "opinion_tags": "The\\O food\\O was\\O amazing\\B ,\\O the\\O service\\O was\\O so\\O attentive\\O and\\O personable\\O ,\\O and\\O how\\O about\\O that\\O ambience\\O !\\O"}, {"uid": "134-1", "sentiment": "positive", "target_tags": "The\\O food\\O was\\O amazing\\O ,\\O the\\O service\\B was\\O so\\O attentive\\O and\\O personable\\O ,\\O and\\O how\\O about\\O that\\O ambience\\O !\\O", "opinion_tags": "The\\O food\\O was\\O amazing\\O ,\\O the\\O service\\O was\\O so\\O attentive\\B and\\O personable\\O ,\\O and\\O how\\O about\\O that\\O ambience\\O !\\O"}, {"uid": "134-2", "sentiment": "positive", "target_tags": "The\\O food\\O was\\O amazing\\O ,\\O the\\O service\\B was\\O so\\O attentive\\O and\\O personable\\O ,\\O and\\O how\\O about\\O that\\O ambience\\O !\\O", "opinion_tags": "The\\O food\\O was\\O amazing\\O ,\\O the\\O service\\O was\\O so\\O attentive\\O and\\O personable\\B ,\\O and\\O how\\O about\\O that\\O ambience\\O !\\O"}], "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "RB", "JJ", "CC", "JJ", ",", "CC", "WRB", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 4, 7, 10, 10, 10, 4, 12, 10, 18, 18, 18, 18, 18, 10, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "advmod", "parataxis", "cc", "conj", "punct", "cc", "mark", "case", "det", "conj", "punct"]}, {"id": "135", "sentence": "This place has the best interior I have seen anywhere in the northside of W'burg , and will impress whoever you bring there .", "triples": [{"uid": "135-0", "sentiment": "positive", "target_tags": "This\\O place\\O has\\O the\\O best\\O interior\\B I\\O have\\O seen\\O anywhere\\O in\\O the\\O northside\\O of\\O W'burg\\O ,\\O and\\O will\\O impress\\O whoever\\O you\\O bring\\O there\\O .\\O", "opinion_tags": "This\\O place\\O has\\O the\\O best\\B interior\\O I\\O have\\O seen\\O anywhere\\O in\\O the\\O northside\\O of\\O W'burg\\O ,\\O and\\O will\\O impress\\O whoever\\O you\\O bring\\O there\\O .\\O"}, {"uid": "135-1", "sentiment": "positive", "target_tags": "This\\O place\\B has\\O the\\O best\\O interior\\O I\\O have\\O seen\\O anywhere\\O in\\O the\\O northside\\O of\\O W'burg\\O ,\\O and\\O will\\O impress\\O whoever\\O you\\O bring\\O there\\O .\\O", "opinion_tags": "This\\O place\\O has\\O the\\O best\\O interior\\O I\\O have\\O seen\\O anywhere\\O in\\O the\\O northside\\O of\\O W'burg\\O ,\\O and\\O will\\O impress\\B whoever\\O you\\O bring\\O there\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "JJS", "NN", "PRP", "VBP", "VBN", "RB", "IN", "DT", "NN", "IN", "NNP", ",", "CC", "MD", "VB", "WP", "PRP", "VBP", "RB", "."], "head": [2, 3, 0, 6, 6, 3, 9, 9, 6, 9, 13, 13, 9, 15, 13, 19, 19, 19, 3, 22, 22, 19, 22, 3], "deprel": ["det", "nsubj", "root", "det", "amod", "obj", "nsubj", "aux", "acl:relcl", "advmod", "case", "det", "obl", "case", "nmod", "punct", "cc", "aux", "conj", "obj", "nsubj", "ccomp", "advmod", "punct"]}, {"id": "136", "sentence": "I found the variety of the sashimi plate to be satisfying - fresh and yummy .", "triples": [{"uid": "136-0", "sentiment": "positive", "target_tags": "I\\O found\\O the\\O variety\\O of\\O the\\O sashimi\\B plate\\I to\\O be\\O satisfying\\O -\\O fresh\\O and\\O yummy\\O .\\O", "opinion_tags": "I\\O found\\O the\\O variety\\O of\\O the\\O sashimi\\O plate\\O to\\O be\\O satisfying\\B -\\O fresh\\O and\\O yummy\\O .\\O"}, {"uid": "136-1", "sentiment": "positive", "target_tags": "I\\O found\\O the\\O variety\\O of\\O the\\O sashimi\\B plate\\I to\\O be\\O satisfying\\O -\\O fresh\\O and\\O yummy\\O .\\O", "opinion_tags": "I\\O found\\O the\\O variety\\O of\\O the\\O sashimi\\O plate\\O to\\O be\\O satisfying\\O -\\O fresh\\B and\\O yummy\\O .\\O"}, {"uid": "136-2", "sentiment": "positive", "target_tags": "I\\O found\\O the\\O variety\\O of\\O the\\O sashimi\\B plate\\I to\\O be\\O satisfying\\O -\\O fresh\\O and\\O yummy\\O .\\O", "opinion_tags": "I\\O found\\O the\\O variety\\O of\\O the\\O sashimi\\O plate\\O to\\O be\\O satisfying\\O -\\O fresh\\O and\\O yummy\\B .\\O"}], "postag": ["PRP", "VBD", "DT", "NN", "IN", "DT", "NN", "NN", "TO", "VB", "JJ", ",", "JJ", "CC", "JJ", "."], "head": [2, 0, 4, 2, 8, 8, 8, 4, 11, 11, 2, 13, 11, 15, 13, 2], "deprel": ["nsubj", "root", "det", "obj", "case", "det", "compound", "nmod", "mark", "cop", "xcomp", "punct", "appos", "cc", "conj", "punct"]}, {"id": "137", "sentence": "<PERSON><PERSON><PERSON> also offers prix fixe lunch and buffet .", "triples": [{"uid": "137-0", "sentiment": "positive", "target_tags": "<PERSON><PERSON><PERSON>\\O also\\O offers\\O prix\\B fixe\\I lunch\\I and\\O buffet\\O .\\O", "opinion_tags": "<PERSON><PERSON><PERSON>\\O also\\O offers\\O prix\\B fixe\\O lunch\\O and\\O buffet\\O .\\O"}, {"uid": "137-1", "sentiment": "positive", "target_tags": "<PERSON><PERSON><PERSON>\\O also\\O offers\\O prix\\O fixe\\O lunch\\O and\\O buffet\\B .\\O", "opinion_tags": "<PERSON><PERSON><PERSON>\\O also\\O offers\\O prix\\B fixe\\O lunch\\O and\\O buffet\\O .\\O"}], "postag": ["NNP", "RB", "VBZ", "NN", "NN", "NN", "CC", "NN", "."], "head": [3, 3, 0, 5, 6, 3, 8, 6, 3], "deprel": ["nsubj", "advmod", "root", "compound", "compound", "obj", "cc", "conj", "punct"]}, {"id": "138", "sentence": "The service , however , was a bright flower in a garden .", "triples": [{"uid": "138-0", "sentiment": "positive", "target_tags": "The\\O service\\B ,\\O however\\O ,\\O was\\O a\\O bright\\O flower\\O in\\O a\\O garden\\O .\\O", "opinion_tags": "The\\O service\\O ,\\O however\\O ,\\O was\\O a\\O bright\\B flower\\O in\\O a\\O garden\\O .\\O"}], "postag": ["DT", "NN", ",", "RB", ",", "VBD", "DT", "JJ", "NN", "IN", "DT", "NN", "."], "head": [2, 9, 9, 9, 9, 9, 9, 9, 0, 12, 12, 9, 9], "deprel": ["det", "nsubj", "punct", "advmod", "punct", "cop", "det", "amod", "root", "case", "det", "nmod", "punct"]}, {"id": "139", "sentence": "Tried the pad see ew on the recommendation of the last reviewer since it 's one of my favorite dishes .", "triples": [{"uid": "139-0", "sentiment": "neutral", "target_tags": "Tried\\O the\\O pad\\B see\\I ew\\I on\\O the\\O recommendation\\O of\\O the\\O last\\O reviewer\\O since\\O it\\O 's\\O one\\O of\\O my\\O favorite\\O dishes\\O .\\O", "opinion_tags": "Tried\\B the\\O pad\\O see\\O ew\\O on\\O the\\O recommendation\\O of\\O the\\O last\\O reviewer\\O since\\O it\\O 's\\O one\\O of\\O my\\O favorite\\O dishes\\O .\\O"}, {"uid": "139-1", "sentiment": "neutral", "target_tags": "Tried\\O the\\O pad\\B see\\I ew\\I on\\O the\\O recommendation\\O of\\O the\\O last\\O reviewer\\O since\\O it\\O 's\\O one\\O of\\O my\\O favorite\\O dishes\\O .\\O", "opinion_tags": "Tried\\O the\\O pad\\O see\\O ew\\O on\\O the\\O recommendation\\O of\\O the\\O last\\O reviewer\\O since\\O it\\O 's\\O one\\O of\\O my\\O favorite\\B dishes\\O .\\O"}, {"uid": "139-2", "sentiment": "positive", "target_tags": "Tried\\O the\\O pad\\O see\\O ew\\O on\\O the\\O recommendation\\O of\\O the\\O last\\O reviewer\\O since\\O it\\O 's\\O one\\O of\\O my\\O favorite\\O dishes\\B .\\O", "opinion_tags": "Tried\\O the\\O pad\\O see\\O ew\\O on\\O the\\O recommendation\\O of\\O the\\O last\\O reviewer\\O since\\O it\\O 's\\O one\\O of\\O my\\O favorite\\B dishes\\O .\\O"}], "postag": ["VBD", "DT", "NN", "VB", "NN", "IN", "DT", "NN", "IN", "DT", "JJ", "NN", "IN", "PRP", "VBZ", "CD", "IN", "PRP$", "JJ", "NNS", "."], "head": [0, 3, 4, 1, 4, 8, 8, 4, 12, 12, 12, 8, 16, 16, 16, 4, 20, 20, 20, 16, 1], "deprel": ["root", "det", "nsubj", "ccomp", "obj", "case", "det", "obl", "case", "det", "amod", "nmod", "mark", "nsubj", "cop", "advcl", "case", "nmod:poss", "amod", "nmod", "punct"]}, {"id": "140", "sentence": "The location is perfect .", "triples": [{"uid": "140-0", "sentiment": "positive", "target_tags": "The\\O location\\B is\\O perfect\\O .\\O", "opinion_tags": "The\\O location\\O is\\O perfect\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"]}, {"id": "141", "sentence": "The lunch special is an absolute steal .", "triples": [{"uid": "141-0", "sentiment": "positive", "target_tags": "The\\O lunch\\B special\\I is\\O an\\O absolute\\O steal\\O .\\O", "opinion_tags": "The\\O lunch\\O special\\O is\\O an\\O absolute\\O steal\\B .\\O"}], "postag": ["DT", "NN", "NN", "VBZ", "DT", "JJ", "NN", "."], "head": [3, 3, 7, 7, 7, 7, 0, 7], "deprel": ["det", "compound", "nsubj", "cop", "det", "amod", "root", "punct"]}, {"id": "142", "sentence": "The chicken pot pie is exceptional , the cheeseburger huge and delictable , and the service professional wan warm .", "triples": [{"uid": "142-0", "sentiment": "positive", "target_tags": "The\\B chicken\\I pot\\I pie\\I is\\O exceptional\\O ,\\O the\\O cheeseburger\\O huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\O professional\\O wan\\O warm\\O .\\O", "opinion_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\B ,\\O the\\O cheeseburger\\O huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\O professional\\O wan\\O warm\\O .\\O"}, {"uid": "142-1", "sentiment": "positive", "target_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\O ,\\O the\\O cheeseburger\\B huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\O professional\\O wan\\O warm\\O .\\O", "opinion_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\O ,\\O the\\O cheeseburger\\O huge\\B and\\O delictable\\O ,\\O and\\O the\\O service\\O professional\\O wan\\O warm\\O .\\O"}, {"uid": "142-2", "sentiment": "positive", "target_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\O ,\\O the\\O cheeseburger\\B huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\O professional\\O wan\\O warm\\O .\\O", "opinion_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\O ,\\O the\\O cheeseburger\\O huge\\O and\\O delictable\\B ,\\O and\\O the\\O service\\O professional\\O wan\\O warm\\O .\\O"}, {"uid": "142-3", "sentiment": "positive", "target_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\O ,\\O the\\O cheeseburger\\O huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\B professional\\O wan\\O warm\\O .\\O", "opinion_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\O ,\\O the\\O cheeseburger\\O huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\O professional\\B wan\\O warm\\O .\\O"}, {"uid": "142-4", "sentiment": "positive", "target_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\O ,\\O the\\O cheeseburger\\O huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\B professional\\O wan\\O warm\\O .\\O", "opinion_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\O ,\\O the\\O cheeseburger\\O huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\O professional\\O wan\\O warm\\B .\\O"}], "postag": ["DT", "NN", "NN", "NN", "VBZ", "JJ", ",", "DT", "NN", "JJ", "CC", "JJ", ",", "CC", "DT", "NN", "JJ", "NN", "JJ", "."], "head": [4, 3, 4, 6, 6, 0, 10, 9, 10, 6, 12, 10, 19, 19, 16, 19, 18, 19, 6, 6], "deprel": ["det", "compound", "compound", "nsubj", "cop", "root", "punct", "det", "nsubj", "conj", "cc", "conj", "punct", "cc", "det", "nsubj", "amod", "nsubj", "conj", "punct"]}, {"id": "143", "sentence": "She gets 10 for her excellent service and advice .", "triples": [{"uid": "143-0", "sentiment": "positive", "target_tags": "She\\O gets\\O 10\\O for\\O her\\O excellent\\O service\\B and\\O advice\\O .\\O", "opinion_tags": "She\\O gets\\O 10\\O for\\O her\\O excellent\\B service\\O and\\O advice\\O .\\O"}], "postag": ["PRP", "VBZ", "CD", "IN", "PRP$", "JJ", "NN", "CC", "NN", "."], "head": [2, 0, 2, 7, 7, 7, 2, 9, 7, 2], "deprel": ["nsubj", "root", "obj", "case", "nmod:poss", "amod", "obl", "cc", "conj", "punct"]}, {"id": "144", "sentence": "The restaurant is a bit noisy but that is something that can be overlooked once you sit down and enjoy a great meal", "triples": [{"uid": "144-0", "sentiment": "positive", "target_tags": "The\\O restaurant\\O is\\O a\\O bit\\O noisy\\O but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\O a\\O great\\O meal\\B", "opinion_tags": "The\\O restaurant\\O is\\O a\\O bit\\O noisy\\O but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\B a\\O great\\O meal\\O"}, {"uid": "144-1", "sentiment": "positive", "target_tags": "The\\O restaurant\\O is\\O a\\O bit\\O noisy\\O but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\O a\\O great\\O meal\\B", "opinion_tags": "The\\O restaurant\\O is\\O a\\O bit\\O noisy\\O but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\O a\\O great\\B meal\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "NN", "JJ", "CC", "DT", "VBZ", "NN", "WDT", "MD", "VB", "VBN", "IN", "PRP", "VBP", "RP", "CC", "VB", "DT", "JJ", "NN"], "head": [2, 6, 6, 5, 6, 0, 10, 10, 10, 6, 14, 14, 14, 10, 17, 17, 14, 17, 20, 17, 23, 23, 20], "deprel": ["det", "nsubj", "cop", "det", "obl:npmod", "root", "cc", "nsubj", "cop", "conj", "nsubj:pass", "aux", "aux:pass", "acl:relcl", "mark", "nsubj", "advcl", "compound:prt", "cc", "conj", "det", "amod", "obj"]}, {"id": "145", "sentence": "I have eaten at Saul , many times , the food is always consistently , outrageously good .", "triples": [{"uid": "145-0", "sentiment": "positive", "target_tags": "I\\O have\\O eaten\\O at\\O Saul\\O ,\\O many\\O times\\O ,\\O the\\O food\\B is\\O always\\O consistently\\O ,\\O outrageously\\O good\\O .\\O", "opinion_tags": "I\\O have\\O eaten\\O at\\O Saul\\O ,\\O many\\O times\\O ,\\O the\\O food\\O is\\O always\\O consistently\\O ,\\O outrageously\\O good\\B .\\O"}], "postag": ["PRP", "VBP", "VBN", "IN", "NNP", ",", "JJ", "NNS", ",", "DT", "NN", "VBZ", "RB", "RB", ",", "RB", "JJ", "."], "head": [3, 3, 0, 5, 3, 3, 8, 3, 3, 11, 17, 17, 14, 17, 17, 17, 3, 3], "deprel": ["nsubj", "aux", "root", "case", "obl", "punct", "amod", "obl:tmod", "punct", "det", "nsubj", "cop", "advmod", "advmod", "punct", "advmod", "parataxis", "punct"]}, {"id": "146", "sentence": "Apparently , the good cook works then .", "triples": [{"uid": "146-0", "sentiment": "positive", "target_tags": "Apparently\\O ,\\O the\\O good\\O cook\\B works\\O then\\O .\\O", "opinion_tags": "Apparently\\O ,\\O the\\O good\\B cook\\O works\\O then\\O .\\O"}], "postag": ["RB", ",", "DT", "JJ", "NN", "VBZ", "RB", "."], "head": [6, 6, 5, 5, 6, 0, 6, 6], "deprel": ["advmod", "punct", "det", "amod", "nsubj", "root", "advmod", "punct"]}, {"id": "147", "sentence": "My steak au poivre was one of the worst I 've had .", "triples": [{"uid": "147-0", "sentiment": "negative", "target_tags": "My\\O steak\\B au\\I poivre\\I was\\O one\\O of\\O the\\O worst\\O I\\O 've\\O had\\O .\\O", "opinion_tags": "My\\O steak\\O au\\O poivre\\O was\\O one\\O of\\O the\\O worst\\B I\\O 've\\O had\\O .\\O"}], "postag": ["PRP$", "NN", "NN", "NN", "VBD", "CD", "IN", "DT", "JJS", "PRP", "VBP", "VBN", "."], "head": [4, 3, 4, 6, 6, 0, 9, 9, 6, 12, 12, 9, 6], "deprel": ["nmod:poss", "compound", "compound", "nsubj", "cop", "root", "case", "det", "nmod", "nsubj", "aux", "acl:relcl", "punct"]}, {"id": "148", "sentence": "Although the tables may be closely situated , the candle-light , food-quality and service overcompensate .", "triples": [{"uid": "148-0", "sentiment": "positive", "target_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\O situated\\O ,\\O the\\O candle-light\\B ,\\O food-quality\\O and\\O service\\O overcompensate\\O .\\O", "opinion_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\B situated\\I ,\\O the\\O candle-light\\O ,\\O food-quality\\O and\\O service\\O overcompensate\\O .\\O"}, {"uid": "148-1", "sentiment": "positive", "target_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\O situated\\O ,\\O the\\O candle-light\\O ,\\O food-quality\\B and\\O service\\O overcompensate\\O .\\O", "opinion_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\O situated\\O ,\\O the\\O candle-light\\O ,\\O food-quality\\O and\\O service\\O overcompensate\\B .\\O"}, {"uid": "148-2", "sentiment": "positive", "target_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\O situated\\O ,\\O the\\O candle-light\\O ,\\O food-quality\\O and\\O service\\B overcompensate\\O .\\O", "opinion_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\O situated\\O ,\\O the\\O candle-light\\O ,\\O food-quality\\O and\\O service\\O overcompensate\\B .\\O"}, {"uid": "148-3", "sentiment": "negative", "target_tags": "Although\\O the\\O tables\\B may\\O be\\O closely\\O situated\\O ,\\O the\\O candle-light\\O ,\\O food-quality\\O and\\O service\\O overcompensate\\O .\\O", "opinion_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\B situated\\I ,\\O the\\O candle-light\\O ,\\O food-quality\\O and\\O service\\O overcompensate\\O .\\O"}], "postag": ["IN", "DT", "NNS", "MD", "VB", "RB", "VBN", ",", "DT", "NN", ",", "NN", "CC", "NN", "NN", "."], "head": [7, 3, 7, 7, 7, 7, 15, 15, 10, 15, 12, 10, 14, 10, 0, 15], "deprel": ["mark", "det", "nsubj:pass", "aux", "aux:pass", "advmod", "advcl", "punct", "det", "nsubj", "punct", "conj", "cc", "conj", "root", "punct"]}, {"id": "149", "sentence": "From the spectacular caviar to the hospitable waitstaff , I felt like royalty and enjoyed every second of it .", "triples": [{"uid": "149-0", "sentiment": "positive", "target_tags": "From\\O the\\O spectacular\\O caviar\\B to\\O the\\O hospitable\\O waitstaff\\O ,\\O I\\O felt\\O like\\O royalty\\O and\\O enjoyed\\O every\\O second\\O of\\O it\\O .\\O", "opinion_tags": "From\\O the\\O spectacular\\B caviar\\O to\\O the\\O hospitable\\O waitstaff\\O ,\\O I\\O felt\\O like\\O royalty\\O and\\O enjoyed\\O every\\O second\\O of\\O it\\O .\\O"}, {"uid": "149-1", "sentiment": "positive", "target_tags": "From\\O the\\O spectacular\\O caviar\\B to\\O the\\O hospitable\\O waitstaff\\O ,\\O I\\O felt\\O like\\O royalty\\O and\\O enjoyed\\O every\\O second\\O of\\O it\\O .\\O", "opinion_tags": "From\\O the\\O spectacular\\O caviar\\O to\\O the\\O hospitable\\O waitstaff\\O ,\\O I\\O felt\\O like\\O royalty\\O and\\O enjoyed\\B every\\O second\\O of\\O it\\O .\\O"}, {"uid": "149-2", "sentiment": "positive", "target_tags": "From\\O the\\O spectacular\\O caviar\\O to\\O the\\O hospitable\\O waitstaff\\B ,\\O I\\O felt\\O like\\O royalty\\O and\\O enjoyed\\O every\\O second\\O of\\O it\\O .\\O", "opinion_tags": "From\\O the\\O spectacular\\O caviar\\O to\\O the\\O hospitable\\B waitstaff\\O ,\\O I\\O felt\\O like\\O royalty\\O and\\O enjoyed\\O every\\O second\\O of\\O it\\O .\\O"}, {"uid": "149-3", "sentiment": "positive", "target_tags": "From\\O the\\O spectacular\\O caviar\\O to\\O the\\O hospitable\\O waitstaff\\B ,\\O I\\O felt\\O like\\O royalty\\O and\\O enjoyed\\O every\\O second\\O of\\O it\\O .\\O", "opinion_tags": "From\\O the\\O spectacular\\O caviar\\O to\\O the\\O hospitable\\O waitstaff\\O ,\\O I\\O felt\\O like\\O royalty\\O and\\O enjoyed\\B every\\O second\\O of\\O it\\O .\\O"}], "postag": ["IN", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", ",", "PRP", "VBD", "IN", "NN", "CC", "VBD", "DT", "NN", "IN", "PRP", "."], "head": [4, 4, 4, 11, 8, 8, 8, 4, 11, 11, 0, 13, 11, 15, 11, 17, 15, 19, 17, 11], "deprel": ["case", "det", "amod", "obl", "case", "det", "amod", "nmod", "punct", "nsubj", "root", "case", "obl", "cc", "conj", "det", "obj", "case", "nmod", "punct"]}, {"id": "150", "sentence": "Service was excellent , and the AC worked very well too ( thank God , it was hot ! ) .", "triples": [{"uid": "150-0", "sentiment": "positive", "target_tags": "Service\\B was\\O excellent\\O ,\\O and\\O the\\O AC\\O worked\\O very\\O well\\O too\\O (\\O thank\\O God\\O ,\\O it\\O was\\O hot\\O !\\O )\\O .\\O", "opinion_tags": "Service\\O was\\O excellent\\B ,\\O and\\O the\\O AC\\O worked\\O very\\O well\\O too\\O (\\O thank\\O God\\O ,\\O it\\O was\\O hot\\O !\\O )\\O .\\O"}, {"uid": "150-1", "sentiment": "positive", "target_tags": "Service\\O was\\O excellent\\O ,\\O and\\O the\\O AC\\B worked\\O very\\O well\\O too\\O (\\O thank\\O God\\O ,\\O it\\O was\\O hot\\O !\\O )\\O .\\O", "opinion_tags": "Service\\O was\\O excellent\\O ,\\O and\\O the\\O AC\\O worked\\O very\\O well\\B too\\O (\\O thank\\O God\\O ,\\O it\\O was\\O hot\\O !\\O )\\O .\\O"}], "postag": ["NN", "VBD", "JJ", ",", "CC", "DT", "NNP", "VBD", "RB", "RB", "RB", "-LRB-", "VBP", "NNP", ",", "PRP", "VBD", "JJ", ".", "-RRB-", "."], "head": [3, 3, 0, 8, 8, 7, 8, 3, 10, 8, 8, 13, 3, 13, 13, 18, 18, 13, 13, 13, 3], "deprel": ["nsubj", "cop", "root", "punct", "cc", "det", "nsubj", "conj", "advmod", "advmod", "advmod", "punct", "parataxis", "obj", "punct", "nsubj", "cop", "parataxis", "punct", "punct", "punct"]}, {"id": "151", "sentence": "My friend ordered some of their special sushi rolls which had excellent presentation and tasted great !", "triples": [{"uid": "151-0", "sentiment": "positive", "target_tags": "My\\O friend\\O ordered\\O some\\O of\\O their\\O special\\O sushi\\B rolls\\I which\\O had\\O excellent\\O presentation\\O and\\O tasted\\O great\\O !\\O", "opinion_tags": "My\\O friend\\O ordered\\O some\\O of\\O their\\O special\\B sushi\\O rolls\\O which\\O had\\O excellent\\O presentation\\O and\\O tasted\\O great\\O !\\O"}, {"uid": "151-1", "sentiment": "positive", "target_tags": "My\\O friend\\O ordered\\O some\\O of\\O their\\O special\\O sushi\\B rolls\\I which\\O had\\O excellent\\O presentation\\O and\\O tasted\\O great\\O !\\O", "opinion_tags": "My\\O friend\\O ordered\\O some\\O of\\O their\\O special\\O sushi\\O rolls\\O which\\O had\\O excellent\\B presentation\\O and\\O tasted\\O great\\O !\\O"}, {"uid": "151-2", "sentiment": "positive", "target_tags": "My\\O friend\\O ordered\\O some\\O of\\O their\\O special\\O sushi\\B rolls\\I which\\O had\\O excellent\\O presentation\\O and\\O tasted\\O great\\O !\\O", "opinion_tags": "My\\O friend\\O ordered\\O some\\O of\\O their\\O special\\O sushi\\O rolls\\O which\\O had\\O excellent\\O presentation\\O and\\O tasted\\O great\\B !\\O"}], "postag": ["PRP$", "NN", "VBD", "DT", "IN", "PRP$", "JJ", "NN", "NNS", "WDT", "VBD", "JJ", "NN", "CC", "VBD", "JJ", "."], "head": [2, 3, 0, 3, 9, 9, 9, 9, 4, 11, 9, 13, 11, 15, 11, 15, 3], "deprel": ["nmod:poss", "nsubj", "root", "obj", "case", "nmod:poss", "amod", "compound", "nmod", "nsubj", "acl:relcl", "amod", "obj", "cc", "conj", "xcomp", "punct"]}, {"id": "152", "sentence": "Although be warned their dinner menu to sit and take out prices are different .", "triples": [{"uid": "152-0", "sentiment": "neutral", "target_tags": "Although\\O be\\O warned\\O their\\O dinner\\O menu\\O to\\O sit\\O and\\O take\\O out\\O prices\\B are\\O different\\O .\\O", "opinion_tags": "Although\\O be\\O warned\\O their\\O dinner\\O menu\\O to\\O sit\\O and\\O take\\O out\\O prices\\O are\\O different\\B .\\O"}, {"uid": "152-1", "sentiment": "neutral", "target_tags": "Although\\O be\\O warned\\O their\\O dinner\\B menu\\I to\\I sit\\I and\\O take\\O out\\O prices\\O are\\O different\\O .\\O", "opinion_tags": "Although\\O be\\O warned\\B their\\O dinner\\O menu\\O to\\O sit\\O and\\O take\\O out\\O prices\\O are\\O different\\O .\\O"}], "postag": ["IN", "VB", "VBN", "PRP$", "NN", "NN", "TO", "VB", "CC", "VB", "RP", "NNS", "VBP", "JJ", "."], "head": [3, 3, 14, 6, 6, 3, 8, 3, 10, 8, 10, 10, 14, 0, 14], "deprel": ["mark", "aux:pass", "advcl", "nmod:poss", "compound", "obj", "mark", "xcomp", "cc", "conj", "compound:prt", "obj", "cop", "root", "punct"]}, {"id": "153", "sentence": "To celebrate a birthday , three of us went to Mare anticipating great food .", "triples": [{"uid": "153-0", "sentiment": "neutral", "target_tags": "To\\O celebrate\\O a\\O birthday\\O ,\\O three\\O of\\O us\\O went\\O to\\O Mare\\O anticipating\\O great\\O food\\B .\\O", "opinion_tags": "To\\O celebrate\\O a\\O birthday\\O ,\\O three\\O of\\O us\\O went\\O to\\O Mare\\O anticipating\\O great\\B food\\O .\\O"}], "postag": ["TO", "VB", "DT", "NN", ",", "CD", "IN", "PRP", "VBD", "IN", "NNP", "VBG", "JJ", "NN", "."], "head": [2, 9, 4, 2, 9, 9, 8, 6, 0, 11, 9, 9, 14, 12, 9], "deprel": ["mark", "advcl", "det", "obj", "punct", "nsubj", "case", "nmod", "root", "case", "obl", "advcl", "amod", "obj", "punct"]}, {"id": "154", "sentence": "The spicy tuna roll was unusually good and the rock shrimp tempura was awesome , great appetizer to share !", "triples": [{"uid": "154-0", "sentiment": "positive", "target_tags": "The\\O spicy\\B tuna\\I roll\\I was\\O unusually\\O good\\O and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\O ,\\O great\\O appetizer\\O to\\O share\\O !\\O", "opinion_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\B and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\O ,\\O great\\O appetizer\\O to\\O share\\O !\\O"}, {"uid": "154-1", "sentiment": "positive", "target_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\O and\\O the\\O rock\\B shrimp\\I tempura\\I was\\O awesome\\O ,\\O great\\O appetizer\\O to\\O share\\O !\\O", "opinion_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\O and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\B ,\\O great\\O appetizer\\O to\\O share\\O !\\O"}, {"uid": "154-2", "sentiment": "positive", "target_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\O and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\O ,\\O great\\O appetizer\\B to\\O share\\O !\\O", "opinion_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\O and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\O ,\\O great\\B appetizer\\O to\\O share\\O !\\O"}], "postag": ["DT", "JJ", "NN", "NN", "VBD", "RB", "JJ", "CC", "DT", "NN", "NN", "NN", "VBD", "JJ", ",", "JJ", "NN", "TO", "VB", "."], "head": [4, 4, 4, 7, 7, 7, 0, 14, 12, 12, 12, 14, 14, 7, 17, 17, 14, 19, 17, 7], "deprel": ["det", "amod", "compound", "nsubj", "cop", "advmod", "root", "cc", "det", "compound", "compound", "nsubj", "cop", "conj", "punct", "amod", "conj", "mark", "acl", "punct"]}, {"id": "155", "sentence": "Great staff .", "triples": [{"uid": "155-0", "sentiment": "positive", "target_tags": "Great\\O staff\\B .\\O", "opinion_tags": "Great\\B staff\\O .\\O"}], "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"]}, {"id": "156", "sentence": "To finish off such a delightful dinner experience you must have dessert , especially the White Chocolate Bread Pudding with Gelato and hot chocolate .", "triples": [{"uid": "156-0", "sentiment": "positive", "target_tags": "To\\O finish\\O off\\O such\\O a\\O delightful\\O dinner\\B experience\\O you\\O must\\O have\\O dessert\\O ,\\O especially\\O the\\O White\\O Chocolate\\O Bread\\O Pudding\\O with\\O Gelato\\O and\\O hot\\O chocolate\\O .\\O", "opinion_tags": "To\\O finish\\O off\\O such\\O a\\O delightful\\B dinner\\O experience\\O you\\O must\\O have\\O dessert\\O ,\\O especially\\O the\\O White\\O Chocolate\\O Bread\\O Pudding\\O with\\O Gelato\\O and\\O hot\\O chocolate\\O .\\O"}], "postag": ["TO", "VB", "RP", "PDT", "DT", "JJ", "NN", "NN", "PRP", "MD", "VB", "NN", ",", "RB", "DT", "NNP", "NN", "NNP", "NN", "IN", "NN", "CC", "JJ", "NN", "."], "head": [2, 11, 2, 8, 8, 8, 8, 2, 11, 11, 0, 11, 19, 19, 19, 17, 18, 19, 12, 21, 19, 24, 24, 21, 11], "deprel": ["mark", "advcl", "compound:prt", "det:predet", "det", "amod", "compound", "obj", "nsubj", "aux", "root", "obj", "punct", "advmod", "det", "compound", "compound", "compound", "appos", "case", "nmod", "cc", "amod", "conj", "punct"]}, {"id": "157", "sentence": "Great vibe , lots of people .", "triples": [{"uid": "157-0", "sentiment": "positive", "target_tags": "Great\\O vibe\\B ,\\O lots\\O of\\O people\\O .\\O", "opinion_tags": "Great\\B vibe\\O ,\\O lots\\O of\\O people\\O .\\O"}], "postag": ["JJ", "NN", ",", "NNS", "IN", "NNS", "."], "head": [2, 0, 4, 2, 6, 4, 2], "deprel": ["amod", "root", "punct", "conj", "case", "nmod", "punct"]}, {"id": "158", "sentence": "Great food and the prices are very reasonable .", "triples": [{"uid": "158-0", "sentiment": "positive", "target_tags": "Great\\O food\\B and\\O the\\O prices\\O are\\O very\\O reasonable\\O .\\O", "opinion_tags": "Great\\B food\\O and\\O the\\O prices\\O are\\O very\\O reasonable\\O .\\O"}, {"uid": "158-1", "sentiment": "positive", "target_tags": "Great\\O food\\O and\\O the\\O prices\\B are\\O very\\O reasonable\\O .\\O", "opinion_tags": "Great\\O food\\O and\\O the\\O prices\\O are\\O very\\O reasonable\\B .\\O"}], "postag": ["JJ", "NN", "CC", "DT", "NNS", "VBP", "RB", "JJ", "."], "head": [2, 0, 8, 5, 8, 8, 8, 2, 2], "deprel": ["amod", "root", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct"]}, {"id": "159", "sentence": "Great food ( spinach and corn dumplings and massamman curry ) , very friendly and no nonsense service and a clean and funky bathroom .", "triples": [{"uid": "159-0", "sentiment": "positive", "target_tags": "Great\\O food\\B (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\O curry\\O )\\O ,\\O very\\O friendly\\O and\\O no\\O nonsense\\O service\\O and\\O a\\O clean\\O and\\O funky\\O bathroom\\O .\\O", "opinion_tags": "Great\\B food\\O (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\O curry\\O )\\O ,\\O very\\O friendly\\O and\\O no\\O nonsense\\O service\\O and\\O a\\O clean\\O and\\O funky\\O bathroom\\O .\\O"}, {"uid": "159-1", "sentiment": "positive", "target_tags": "Great\\O food\\O (\\O spinach\\B and\\I corn\\I dumplings\\I and\\<PERSON>am<PERSON>\\O curry\\O )\\O ,\\O very\\O friendly\\O and\\O no\\O nonsense\\O service\\O and\\O a\\O clean\\O and\\O funky\\O bathroom\\O .\\O", "opinion_tags": "Great\\B food\\O (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\O curry\\O )\\O ,\\O very\\O friendly\\O and\\O no\\O nonsense\\O service\\O and\\O a\\O clean\\O and\\O funky\\O bathroom\\O .\\O"}, {"uid": "159-2", "sentiment": "positive", "target_tags": "Great\\O food\\O (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\O curry\\O )\\O ,\\O very\\O friendly\\O and\\O no\\O nonsense\\O service\\B and\\O a\\O clean\\O and\\O funky\\O bathroom\\O .\\O", "opinion_tags": "Great\\O food\\O (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\O curry\\O )\\O ,\\O very\\O friendly\\B and\\O no\\O nonsense\\O service\\O and\\O a\\O clean\\O and\\O funky\\O bathroom\\O .\\O"}, {"uid": "159-3", "sentiment": "positive", "target_tags": "Great\\O food\\O (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\O curry\\O )\\O ,\\O very\\O friendly\\O and\\O no\\O nonsense\\O service\\B and\\O a\\O clean\\O and\\O funky\\O bathroom\\O .\\O", "opinion_tags": "Great\\O food\\O (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\O curry\\O )\\O ,\\O very\\O friendly\\O and\\O no\\O nonsense\\B service\\O and\\O a\\O clean\\O and\\O funky\\O bathroom\\O .\\O"}, {"uid": "159-4", "sentiment": "positive", "target_tags": "Great\\O food\\O (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\O curry\\O )\\O ,\\O very\\O friendly\\O and\\O no\\O nonsense\\O service\\O and\\O a\\O clean\\O and\\O funky\\O bathroom\\B .\\O", "opinion_tags": "Great\\O food\\O (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\O curry\\O )\\O ,\\O very\\O friendly\\O and\\O no\\O nonsense\\O service\\O and\\O a\\O clean\\B and\\O funky\\O bathroom\\O .\\O"}, {"uid": "159-5", "sentiment": "positive", "target_tags": "Great\\O food\\O (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\O curry\\O )\\O ,\\O very\\O friendly\\O and\\O no\\O nonsense\\O service\\O and\\O a\\O clean\\O and\\O funky\\O bathroom\\B .\\O", "opinion_tags": "Great\\O food\\O (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\O curry\\O )\\O ,\\O very\\O friendly\\O and\\O no\\O nonsense\\O service\\O and\\O a\\O clean\\O and\\O funky\\B bathroom\\O .\\O"}, {"uid": "159-6", "sentiment": "positive", "target_tags": "Great\\O food\\O (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\B curry\\I )\\O ,\\O very\\O friendly\\O and\\O no\\O nonsense\\O service\\O and\\O a\\O clean\\O and\\O funky\\O bathroom\\O .\\O", "opinion_tags": "Great\\O food\\O (\\O spinach\\O and\\O corn\\O dumplings\\O and\\<PERSON>amman\\O curry\\O )\\O ,\\O very\\O friendly\\B and\\O no\\O nonsense\\O service\\O and\\O a\\O clean\\O and\\O funky\\O bathroom\\O .\\O"}], "postag": ["JJ", "NN", "-LRB-", "NN", "CC", "NN", "NNS", "CC", "JJ", "NN", "-RRB-", ",", "RB", "JJ", "CC", "DT", "JJ", "NN", "CC", "DT", "JJ", "CC", "JJ", "NN", "."], "head": [2, 0, 4, 2, 7, 7, 4, 10, 10, 4, 4, 14, 14, 2, 18, 18, 18, 2, 24, 24, 24, 23, 21, 2, 2], "deprel": ["amod", "root", "punct", "appos", "cc", "compound", "conj", "cc", "amod", "conj", "punct", "punct", "advmod", "parataxis", "cc", "det", "amod", "conj", "cc", "det", "amod", "cc", "conj", "conj", "punct"]}, {"id": "160", "sentence": "My chow fun and chow see was really bland and oily .", "triples": [{"uid": "160-0", "sentiment": "negative", "target_tags": "My\\O chow\\B fun\\I and\\I chow\\I see\\I was\\O really\\O bland\\O and\\O oily\\O .\\O", "opinion_tags": "My\\O chow\\O fun\\O and\\O chow\\O see\\O was\\O really\\O bland\\B and\\O oily\\O .\\O"}, {"uid": "160-1", "sentiment": "negative", "target_tags": "My\\O chow\\B fun\\I and\\I chow\\I see\\I was\\O really\\O bland\\O and\\O oily\\O .\\O", "opinion_tags": "My\\O chow\\O fun\\O and\\O chow\\O see\\O was\\O really\\O bland\\O and\\O oily\\B .\\O"}], "postag": ["PRP$", "NN", "NN", "CC", "NN", "VBP", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [3, 3, 9, 5, 3, 3, 9, 9, 0, 11, 9, 9], "deprel": ["nmod:poss", "compound", "nsubj", "cc", "conj", "acl", "cop", "advmod", "root", "cc", "conj", "punct"]}, {"id": "161", "sentence": "If you love seafood , you would love this place !", "triples": [{"uid": "161-0", "sentiment": "positive", "target_tags": "If\\O you\\O love\\O seafood\\B ,\\O you\\O would\\O love\\O this\\O place\\O !\\O", "opinion_tags": "If\\O you\\O love\\B seafood\\O ,\\O you\\O would\\O love\\O this\\O place\\O !\\O"}, {"uid": "161-1", "sentiment": "positive", "target_tags": "If\\O you\\O love\\O seafood\\O ,\\O you\\O would\\O love\\O this\\O place\\B !\\O", "opinion_tags": "If\\O you\\O love\\O seafood\\O ,\\O you\\O would\\O love\\B this\\O place\\O !\\O"}], "postag": ["IN", "PRP", "VBP", "NN", ",", "PRP", "MD", "VB", "DT", "NN", "."], "head": [3, 3, 8, 3, 8, 8, 8, 0, 10, 8, 8], "deprel": ["mark", "nsubj", "advcl", "obj", "punct", "nsubj", "aux", "root", "det", "obj", "punct"]}, {"id": "162", "sentence": "They charge different prices all the time .", "triples": [{"uid": "162-0", "sentiment": "negative", "target_tags": "They\\O charge\\O different\\O prices\\B all\\O the\\O time\\O .\\O", "opinion_tags": "They\\O charge\\O different\\B prices\\O all\\O the\\O time\\O .\\O"}], "postag": ["PRP", "VBP", "JJ", "NNS", "PDT", "DT", "NN", "."], "head": [2, 0, 4, 2, 7, 7, 2, 2], "deprel": ["nsubj", "root", "amod", "obj", "det:predet", "det", "obl:tmod", "punct"]}, {"id": "163", "sentence": "In terms of the food itself -- nothing special , we limited ourselves to several appetizers .", "triples": [{"uid": "163-0", "sentiment": "neutral", "target_tags": "In\\O terms\\O of\\O the\\O food\\B itself\\O --\\O nothing\\O special\\O ,\\O we\\O limited\\O ourselves\\O to\\O several\\O appetizers\\O .\\O", "opinion_tags": "In\\O terms\\O of\\O the\\O food\\O itself\\O --\\O nothing\\B special\\I ,\\O we\\O limited\\O ourselves\\O to\\O several\\O appetizers\\O .\\O"}, {"uid": "163-1", "sentiment": "neutral", "target_tags": "In\\O terms\\O of\\O the\\O food\\O itself\\O --\\O nothing\\O special\\O ,\\O we\\O limited\\O ourselves\\O to\\O several\\O appetizers\\B .\\O", "opinion_tags": "In\\O terms\\O of\\O the\\O food\\O itself\\O --\\O nothing\\O special\\O ,\\O we\\O limited\\B ourselves\\O to\\O several\\O appetizers\\O .\\O"}], "postag": ["IN", "NNS", "IN", "DT", "NN", "PRP", ",", "NN", "JJ", ",", "PRP", "VBD", "PRP", "IN", "JJ", "NNS", "."], "head": [2, 12, 5, 5, 2, 5, 8, 0, 8, 12, 12, 8, 12, 16, 16, 12, 8], "deprel": ["case", "obl", "case", "det", "nmod", "nmod:npmod", "punct", "root", "amod", "punct", "nsubj", "parataxis", "obj", "case", "amod", "obl", "punct"]}, {"id": "164", "sentence": "No dress codes , no attitudes , plenty of comfort companionship , a great place to relax in an always busy Midtown .", "triples": [{"uid": "164-0", "sentiment": "positive", "target_tags": "No\\O dress\\B codes\\I ,\\O no\\O attitudes\\O ,\\O plenty\\O of\\O comfort\\O companionship\\O ,\\O a\\O great\\O place\\O to\\O relax\\O in\\O an\\O always\\O busy\\O Midtown\\O .\\O", "opinion_tags": "No\\B dress\\O codes\\O ,\\O no\\O attitudes\\O ,\\O plenty\\O of\\O comfort\\O companionship\\O ,\\O a\\O great\\O place\\O to\\O relax\\O in\\O an\\O always\\O busy\\O Midtown\\O .\\O"}, {"uid": "164-1", "sentiment": "positive", "target_tags": "No\\O dress\\O codes\\O ,\\O no\\O attitudes\\B ,\\O plenty\\O of\\O comfort\\O companionship\\O ,\\O a\\O great\\O place\\O to\\O relax\\O in\\O an\\O always\\O busy\\O Midtown\\O .\\O", "opinion_tags": "No\\O dress\\O codes\\O ,\\O no\\B attitudes\\O ,\\O plenty\\O of\\O comfort\\O companionship\\O ,\\O a\\O great\\O place\\O to\\O relax\\O in\\O an\\O always\\O busy\\O Midtown\\O .\\O"}, {"uid": "164-2", "sentiment": "positive", "target_tags": "No\\O dress\\O codes\\O ,\\O no\\O attitudes\\O ,\\O plenty\\O of\\O comfort\\O companionship\\O ,\\O a\\O great\\O place\\B to\\O relax\\O in\\O an\\O always\\O busy\\O Midtown\\O .\\O", "opinion_tags": "No\\O dress\\O codes\\O ,\\O no\\O attitudes\\O ,\\O plenty\\O of\\O comfort\\O companionship\\O ,\\O a\\O great\\B place\\O to\\O relax\\O in\\O an\\O always\\O busy\\O Midtown\\O .\\O"}], "postag": ["DT", "NN", "NNS", ",", "DT", "NNS", ",", "NN", "IN", "NN", "NN", ",", "DT", "JJ", "NN", "TO", "VB", "IN", "DT", "RB", "JJ", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 8, 3, 11, 11, 8, 15, 15, 15, 3, 17, 15, 22, 22, 21, 22, 17, 3], "deprel": ["det", "compound", "root", "punct", "det", "conj", "punct", "conj", "case", "compound", "nmod", "punct", "det", "amod", "conj", "mark", "acl", "case", "det", "advmod", "amod", "obl", "punct"]}, {"id": "165", "sentence": "They have a huge selection of different cream cheeses and all of their salads are great .", "triples": [{"uid": "165-0", "sentiment": "positive", "target_tags": "They\\O have\\O a\\O huge\\O selection\\O of\\O different\\O cream\\B cheeses\\I and\\O all\\O of\\O their\\O salads\\O are\\O great\\O .\\O", "opinion_tags": "They\\O have\\O a\\O huge\\B selection\\O of\\O different\\O cream\\O cheeses\\O and\\O all\\O of\\O their\\O salads\\O are\\O great\\O .\\O"}, {"uid": "165-1", "sentiment": "positive", "target_tags": "They\\O have\\O a\\O huge\\O selection\\O of\\O different\\O cream\\B cheeses\\I and\\O all\\O of\\O their\\O salads\\O are\\O great\\O .\\O", "opinion_tags": "They\\O have\\O a\\O huge\\O selection\\O of\\O different\\B cream\\O cheeses\\O and\\O all\\O of\\O their\\O salads\\O are\\O great\\O .\\O"}, {"uid": "165-2", "sentiment": "positive", "target_tags": "They\\O have\\O a\\O huge\\O selection\\O of\\O different\\O cream\\O cheeses\\O and\\O all\\O of\\O their\\O salads\\B are\\O great\\O .\\O", "opinion_tags": "They\\O have\\O a\\O huge\\O selection\\O of\\O different\\O cream\\O cheeses\\O and\\O all\\O of\\O their\\O salads\\O are\\O great\\B .\\O"}], "postag": ["PRP", "VBP", "DT", "JJ", "NN", "IN", "JJ", "NN", "NNS", "CC", "DT", "IN", "PRP$", "NNS", "VBP", "JJ", "."], "head": [2, 0, 5, 5, 2, 9, 9, 9, 5, 16, 16, 14, 14, 11, 16, 2, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "case", "amod", "compound", "nmod", "cc", "nsubj", "case", "nmod:poss", "nmod", "cop", "conj", "punct"]}, {"id": "166", "sentence": "I ordered a Chicken Teriyaki dish and found that the chicken was extremely dry .", "triples": [{"uid": "166-0", "sentiment": "negative", "target_tags": "I\\O ordered\\O a\\O Chicken\\B Teriyaki\\I dish\\I and\\O found\\O that\\O the\\O chicken\\O was\\O extremely\\O dry\\O .\\O", "opinion_tags": "I\\O ordered\\O a\\O Chicken\\O Teriyaki\\O dish\\O and\\O found\\O that\\O the\\O chicken\\O was\\O extremely\\O dry\\B .\\O"}, {"uid": "166-1", "sentiment": "negative", "target_tags": "I\\O ordered\\O a\\O Chicken\\O Teriyaki\\O dish\\O and\\O found\\O that\\O the\\O chicken\\B was\\O extremely\\O dry\\O .\\O", "opinion_tags": "I\\O ordered\\O a\\O Chicken\\O Teriyaki\\O dish\\O and\\O found\\O that\\O the\\O chicken\\O was\\O extremely\\O dry\\B .\\O"}], "postag": ["PRP", "VBD", "DT", "NNP", "NN", "NN", "CC", "VBD", "IN", "DT", "NN", "VBD", "RB", "JJ", "."], "head": [2, 0, 6, 5, 6, 2, 8, 2, 14, 11, 14, 14, 14, 8, 2], "deprel": ["nsubj", "root", "det", "compound", "compound", "obj", "cc", "conj", "mark", "det", "nsubj", "cop", "advmod", "ccomp", "punct"]}, {"id": "167", "sentence": "There is no excuse for such lousy service !", "triples": [{"uid": "167-0", "sentiment": "negative", "target_tags": "There\\O is\\O no\\O excuse\\O for\\O such\\O lousy\\O service\\B !\\O", "opinion_tags": "There\\O is\\O no\\O excuse\\O for\\O such\\O lousy\\B service\\O !\\O"}], "postag": ["EX", "VBZ", "DT", "NN", "IN", "JJ", "JJ", "NN", "."], "head": [2, 0, 4, 2, 8, 8, 8, 4, 2], "deprel": ["expl", "root", "det", "nsubj", "case", "amod", "amod", "nmod", "punct"]}, {"id": "168", "sentence": "So , a little inconsistency there but either way , both pizzas were really good .", "triples": [{"uid": "168-0", "sentiment": "positive", "target_tags": "So\\O ,\\O a\\O little\\O inconsistency\\O there\\O but\\O either\\O way\\O ,\\O both\\O pizzas\\B were\\O really\\O good\\O .\\O", "opinion_tags": "So\\O ,\\O a\\O little\\O inconsistency\\O there\\O but\\O either\\O way\\O ,\\O both\\O pizzas\\O were\\O really\\O good\\B .\\O"}], "postag": ["RB", ",", "DT", "JJ", "NN", "RB", "CC", "DT", "NN", ",", "DT", "NNS", "VBD", "RB", "JJ", "."], "head": [5, 5, 5, 5, 0, 5, 15, 9, 15, 15, 12, 15, 15, 15, 5, 5], "deprel": ["advmod", "punct", "det", "amod", "root", "advmod", "cc", "det", "obl:tmod", "punct", "det", "nsubj", "cop", "advmod", "conj", "punct"]}, {"id": "169", "sentence": "I had the duck breast special on my last visit and it was incredible .", "triples": [{"uid": "169-0", "sentiment": "positive", "target_tags": "I\\O had\\O the\\O duck\\B breast\\I special\\I on\\O my\\O last\\O visit\\O and\\O it\\O was\\O incredible\\O .\\O", "opinion_tags": "I\\O had\\O the\\O duck\\O breast\\O special\\O on\\O my\\O last\\O visit\\O and\\O it\\O was\\O incredible\\B .\\O"}], "postag": ["PRP", "VBD", "DT", "NN", "NN", "JJ", "IN", "PRP$", "JJ", "NN", "CC", "PRP", "VBD", "JJ", "."], "head": [2, 0, 5, 5, 6, 2, 10, 10, 10, 2, 14, 14, 14, 2, 2], "deprel": ["nsubj", "root", "det", "compound", "compound", "obj", "case", "nmod:poss", "amod", "obl", "cc", "nsubj", "cop", "conj", "punct"]}, {"id": "170", "sentence": "These innovators of french indian fusion do a great job of making dishes as interesting as possible while still being accessible .", "triples": [{"uid": "170-0", "sentiment": "positive", "target_tags": "These\\O innovators\\O of\\O french\\B indian\\I fusion\\I do\\O a\\O great\\O job\\O of\\O making\\O dishes\\O as\\O interesting\\O as\\O possible\\O while\\O still\\O being\\O accessible\\O .\\O", "opinion_tags": "These\\O innovators\\O of\\O french\\O indian\\O fusion\\O do\\O a\\O great\\B job\\O of\\O making\\O dishes\\O as\\O interesting\\O as\\O possible\\O while\\O still\\O being\\O accessible\\O .\\O"}, {"uid": "170-1", "sentiment": "positive", "target_tags": "These\\O innovators\\O of\\O french\\O indian\\O fusion\\O do\\O a\\O great\\O job\\O of\\O making\\O dishes\\B as\\O interesting\\O as\\O possible\\O while\\O still\\O being\\O accessible\\O .\\O", "opinion_tags": "These\\O innovators\\O of\\O french\\O indian\\O fusion\\O do\\O a\\O great\\O job\\O of\\O making\\O dishes\\O as\\O interesting\\B as\\O possible\\O while\\O still\\O being\\O accessible\\O .\\O"}, {"uid": "170-2", "sentiment": "positive", "target_tags": "These\\O innovators\\O of\\O french\\O indian\\O fusion\\O do\\O a\\O great\\O job\\O of\\O making\\O dishes\\B as\\O interesting\\O as\\O possible\\O while\\O still\\O being\\O accessible\\O .\\O", "opinion_tags": "These\\O innovators\\O of\\O french\\O indian\\O fusion\\O do\\O a\\O great\\O job\\O of\\O making\\O dishes\\O as\\O interesting\\O as\\O possible\\O while\\O still\\O being\\O accessible\\B .\\O"}], "postag": ["DT", "NNS", "IN", "JJ", "JJ", "NN", "VBP", "DT", "JJ", "NN", "IN", "VBG", "NNS", "RB", "JJ", "IN", "JJ", "IN", "RB", "VBG", "JJ", "."], "head": [2, 7, 6, 6, 6, 2, 0, 10, 10, 7, 12, 10, 12, 15, 12, 17, 15, 21, 21, 21, 14, 7], "deprel": ["det", "nsubj", "case", "amod", "amod", "nmod", "root", "det", "amod", "obj", "mark", "acl", "obj", "advmod", "xcomp", "mark", "advcl", "mark", "advmod", "cop", "advcl", "punct"]}, {"id": "171", "sentence": "night without a reservation , we had to wait at the bar for a little while , but the manager was so nice and made our wait a great experience .", "triples": [{"uid": "171-0", "sentiment": "positive", "target_tags": "night\\O without\\O a\\O reservation\\O ,\\O we\\O had\\O to\\O wait\\O at\\O the\\O bar\\O for\\O a\\O little\\O while\\O ,\\O but\\O the\\O manager\\B was\\O so\\O nice\\O and\\O made\\O our\\O wait\\O a\\O great\\O experience\\O .\\O", "opinion_tags": "night\\O without\\O a\\O reservation\\O ,\\O we\\O had\\O to\\O wait\\O at\\O the\\O bar\\O for\\O a\\O little\\O while\\O ,\\O but\\O the\\O manager\\O was\\O so\\O nice\\B and\\O made\\O our\\O wait\\O a\\O great\\O experience\\O .\\O"}], "postag": ["NN", "IN", "DT", "NN", ",", "PRP", "VBD", "TO", "VB", "IN", "DT", "NN", "IN", "DT", "JJ", "NN", ",", "CC", "DT", "NN", "VBD", "RB", "JJ", "CC", "VBD", "PRP$", "NN", "DT", "JJ", "NN", "."], "head": [7, 4, 4, 1, 7, 7, 0, 9, 7, 12, 12, 9, 16, 16, 16, 9, 23, 23, 20, 23, 23, 23, 7, 25, 23, 27, 25, 30, 30, 25, 7], "deprel": ["obl:tmod", "case", "det", "nmod", "punct", "nsubj", "root", "mark", "xcomp", "case", "det", "obl", "case", "det", "amod", "obl", "punct", "cc", "det", "nsubj", "cop", "advmod", "conj", "cc", "conj", "nmod:poss", "obj", "det", "amod", "xcomp", "punct"]}, {"id": "172", "sentence": "You rarely have to wait for a seat and the currys ( masaman , green , red ) are full of flavor and come super spicy if you ask for it .", "triples": [{"uid": "172-0", "sentiment": "positive", "target_tags": "You\\O rarely\\O have\\O to\\O wait\\O for\\O a\\O seat\\B and\\O the\\O currys\\O (\\O masaman\\O ,\\O green\\O ,\\O red\\O )\\O are\\O full\\O of\\O flavor\\O and\\O come\\O super\\O spicy\\O if\\O you\\O ask\\O for\\O it\\O .\\O", "opinion_tags": "You\\O rarely\\B have\\O to\\O wait\\O for\\O a\\O seat\\O and\\O the\\O currys\\O (\\O masaman\\O ,\\O green\\O ,\\O red\\O )\\O are\\O full\\O of\\O flavor\\O and\\O come\\O super\\O spicy\\O if\\O you\\O ask\\O for\\O it\\O .\\O"}, {"uid": "172-1", "sentiment": "positive", "target_tags": "You\\O rarely\\O have\\O to\\O wait\\O for\\O a\\O seat\\O and\\O the\\O currys\\B (\\I masaman\\I ,\\I green\\I ,\\I red\\I )\\I are\\O full\\O of\\O flavor\\O and\\O come\\O super\\O spicy\\O if\\O you\\O ask\\O for\\O it\\O .\\O", "opinion_tags": "You\\O rarely\\O have\\O to\\O wait\\O for\\O a\\O seat\\O and\\O the\\O currys\\O (\\O masaman\\O ,\\O green\\O ,\\O red\\O )\\O are\\O full\\B of\\O flavor\\O and\\O come\\O super\\O spicy\\O if\\O you\\O ask\\O for\\O it\\O .\\O"}, {"uid": "172-2", "sentiment": "positive", "target_tags": "You\\O rarely\\O have\\O to\\O wait\\O for\\O a\\O seat\\O and\\O the\\O currys\\O (\\O masaman\\O ,\\O green\\O ,\\O red\\O )\\O are\\O full\\O of\\O flavor\\B and\\O come\\O super\\O spicy\\O if\\O you\\O ask\\O for\\O it\\O .\\O", "opinion_tags": "You\\O rarely\\O have\\O to\\O wait\\O for\\O a\\O seat\\O and\\O the\\O currys\\O (\\O masaman\\O ,\\O green\\O ,\\O red\\O )\\O are\\O full\\B of\\O flavor\\O and\\O come\\O super\\O spicy\\O if\\O you\\O ask\\O for\\O it\\O .\\O"}], "postag": ["PRP", "RB", "VBP", "TO", "VB", "IN", "DT", "NN", "CC", "DT", "NNS", "-LRB-", "JJ", ",", "JJ", ",", "JJ", "-RRB-", "VBP", "JJ", "IN", "NN", "CC", "VBP", "RB", "JJ", "IN", "PRP", "VBP", "IN", "PRP", "."], "head": [3, 3, 0, 5, 3, 8, 8, 5, 20, 11, 20, 13, 11, 15, 13, 17, 13, 13, 20, 3, 22, 20, 24, 20, 26, 24, 29, 29, 24, 31, 29, 3], "deprel": ["nsubj", "advmod", "root", "mark", "xcomp", "case", "det", "obl", "cc", "det", "nsubj", "punct", "amod", "punct", "conj", "punct", "conj", "punct", "cop", "conj", "case", "obl", "cc", "conj", "advmod", "xcomp", "mark", "nsubj", "advcl", "case", "obl", "punct"]}, {"id": "173", "sentence": "The bar is very well stocked with interesting beers and well priced wines .", "triples": [{"uid": "173-0", "sentiment": "positive", "target_tags": "The\\O bar\\B is\\O very\\O well\\O stocked\\O with\\O interesting\\O beers\\O and\\O well\\O priced\\O wines\\O .\\O", "opinion_tags": "The\\O bar\\O is\\O very\\O well\\B stocked\\I with\\O interesting\\O beers\\O and\\O well\\O priced\\O wines\\O .\\O"}, {"uid": "173-1", "sentiment": "positive", "target_tags": "The\\O bar\\O is\\O very\\O well\\O stocked\\O with\\O interesting\\O beers\\B and\\O well\\O priced\\O wines\\O .\\O", "opinion_tags": "The\\O bar\\O is\\O very\\O well\\O stocked\\O with\\O interesting\\B beers\\O and\\O well\\O priced\\O wines\\O .\\O"}, {"uid": "173-2", "sentiment": "positive", "target_tags": "The\\O bar\\O is\\O very\\O well\\O stocked\\O with\\O interesting\\O beers\\O and\\O well\\O priced\\O wines\\B .\\O", "opinion_tags": "The\\O bar\\O is\\O very\\O well\\O stocked\\O with\\O interesting\\O beers\\O and\\O well\\B priced\\I wines\\O .\\O"}, {"uid": "173-3", "sentiment": "positive", "target_tags": "The\\O bar\\O is\\O very\\O well\\O stocked\\O with\\O interesting\\O beers\\O and\\O well\\O priced\\B wines\\O .\\O", "opinion_tags": "The\\O bar\\O is\\O very\\O well\\O stocked\\O with\\O interesting\\O beers\\O and\\O well\\B priced\\O wines\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "RB", "VBN", "IN", "JJ", "NNS", "CC", "RB", "VBN", "NNS", "."], "head": [2, 6, 6, 5, 6, 0, 9, 9, 6, 13, 12, 13, 9, 6], "deprel": ["det", "nsubj:pass", "aux:pass", "advmod", "advmod", "root", "case", "amod", "obl", "cc", "advmod", "amod", "conj", "punct"]}, {"id": "174", "sentence": "The food was good .", "triples": [{"uid": "174-0", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O good\\O .\\O", "opinion_tags": "The\\O food\\O was\\O good\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"]}, {"id": "175", "sentence": "I have enjoyed everything I have ever gotten and the fish is so fresh and always prepared in a great way .", "triples": [{"uid": "175-0", "sentiment": "positive", "target_tags": "I\\O have\\O enjoyed\\O everything\\O I\\O have\\O ever\\O gotten\\O and\\O the\\O fish\\B is\\O so\\O fresh\\O and\\O always\\O prepared\\O in\\O a\\O great\\O way\\O .\\O", "opinion_tags": "I\\O have\\O enjoyed\\O everything\\O I\\O have\\O ever\\O gotten\\O and\\O the\\O fish\\O is\\O so\\O fresh\\B and\\O always\\O prepared\\O in\\O a\\O great\\O way\\O .\\O"}, {"uid": "175-1", "sentiment": "positive", "target_tags": "I\\O have\\O enjoyed\\O everything\\O I\\O have\\O ever\\O gotten\\O and\\O the\\O fish\\B is\\O so\\O fresh\\O and\\O always\\O prepared\\O in\\O a\\O great\\O way\\O .\\O", "opinion_tags": "I\\O have\\O enjoyed\\O everything\\O I\\O have\\O ever\\O gotten\\O and\\O the\\O fish\\O is\\O so\\O fresh\\O and\\O always\\O prepared\\O in\\O a\\O great\\B way\\O .\\O"}], "postag": ["PRP", "VBP", "VBN", "NN", "PRP", "VBP", "RB", "VBN", "CC", "DT", "NN", "VBZ", "RB", "JJ", "CC", "RB", "JJ", "IN", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 3, 8, 8, 8, 4, 14, 11, 14, 14, 14, 3, 17, 17, 14, 21, 21, 21, 17, 3], "deprel": ["nsubj", "aux", "root", "obj", "nsubj", "aux", "advmod", "acl:relcl", "cc", "det", "nsubj", "cop", "advmod", "conj", "cc", "advmod", "conj", "case", "det", "amod", "obl", "punct"]}, {"id": "176", "sentence": "The place was nice and calm .", "triples": [{"uid": "176-0", "sentiment": "positive", "target_tags": "The\\O place\\B was\\O nice\\O and\\O calm\\O .\\O", "opinion_tags": "The\\O place\\O was\\O nice\\B and\\O calm\\O .\\O"}, {"uid": "176-1", "sentiment": "positive", "target_tags": "The\\O place\\B was\\O nice\\O and\\O calm\\O .\\O", "opinion_tags": "The\\O place\\O was\\O nice\\O and\\O calm\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct"]}, {"id": "177", "sentence": "I could have drank 4 glasses of water and still been parched - so watch out .", "triples": [{"uid": "177-0", "sentiment": "neutral", "target_tags": "I\\O could\\O have\\O drank\\O 4\\O glasses\\B of\\I water\\I and\\O still\\O been\\O parched\\O -\\O so\\O watch\\O out\\O .\\O", "opinion_tags": "I\\O could\\O have\\O drank\\O 4\\O glasses\\O of\\O water\\O and\\O still\\O been\\O parched\\B -\\O so\\O watch\\O out\\O .\\O"}], "postag": ["PRP", "MD", "VB", "VBN", "CD", "NNS", "IN", "NN", "CC", "RB", "VBN", "JJ", ",", "RB", "VB", "RP", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 12, 12, 12, 4, 4, 15, 4, 15, 4], "deprel": ["nsubj", "aux", "aux", "root", "nummod", "obj", "case", "nmod", "cc", "advmod", "cop", "conj", "punct", "advmod", "parataxis", "compound:prt", "punct"]}, {"id": "178", "sentence": "When we sat , we got great and fast service .", "triples": [{"uid": "178-0", "sentiment": "positive", "target_tags": "When\\O we\\O sat\\O ,\\O we\\O got\\O great\\O and\\O fast\\O service\\B .\\O", "opinion_tags": "When\\O we\\O sat\\O ,\\O we\\O got\\O great\\B and\\O fast\\O service\\O .\\O"}, {"uid": "178-1", "sentiment": "positive", "target_tags": "When\\O we\\O sat\\O ,\\O we\\O got\\O great\\O and\\O fast\\O service\\B .\\O", "opinion_tags": "When\\O we\\O sat\\O ,\\O we\\O got\\O great\\O and\\O fast\\B service\\O .\\O"}], "postag": ["WRB", "PRP", "VBD", ",", "PRP", "VBD", "JJ", "CC", "JJ", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 10, 9, 7, 6, 6], "deprel": ["mark", "nsubj", "advcl", "punct", "nsubj", "root", "amod", "cc", "conj", "obj", "punct"]}, {"id": "179", "sentence": "They forgot a sandwich , did n't include plastic forks , and did n't include pita with the hummus platter .", "triples": [{"uid": "179-0", "sentiment": "neutral", "target_tags": "They\\O forgot\\O a\\O sandwich\\B ,\\O did\\O n't\\O include\\O plastic\\O forks\\O ,\\O and\\O did\\O n't\\O include\\O pita\\O with\\O the\\O hummus\\O platter\\O .\\O", "opinion_tags": "They\\O forgot\\B a\\O sandwich\\O ,\\O did\\O n't\\O include\\O plastic\\O forks\\O ,\\O and\\O did\\O n't\\O include\\O pita\\O with\\O the\\O hummus\\O platter\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "NN", ",", "VBD", "RB", "VB", "JJ", "NNS", ",", "CC", "VBD", "RB", "VB", "NN", "IN", "DT", "NN", "NN", "."], "head": [2, 0, 4, 2, 8, 8, 8, 2, 10, 8, 15, 15, 15, 15, 2, 15, 20, 20, 20, 15, 2], "deprel": ["nsubj", "root", "det", "obj", "punct", "aux", "advmod", "conj", "amod", "obj", "punct", "cc", "aux", "advmod", "conj", "obj", "case", "det", "compound", "obl", "punct"]}, {"id": "180", "sentence": "The menu is Prix Fixe , so be prepared to spend at least $ 60 per person , but it is Well worth it superb food .", "triples": [{"uid": "180-0", "sentiment": "negative", "target_tags": "The\\O menu\\B is\\O Prix\\O Fixe\\O ,\\O so\\O be\\O prepared\\O to\\O spend\\O at\\O least\\O $\\O 60\\O per\\O person\\O ,\\O but\\O it\\O is\\O Well\\O worth\\O it\\O superb\\O food\\O .\\O", "opinion_tags": "The\\O menu\\O is\\O Prix\\O Fixe\\O ,\\O so\\O be\\O prepared\\O to\\O spend\\O at\\O least\\O $\\O 60\\O per\\O person\\O ,\\O but\\O it\\O is\\O Well\\B worth\\I it\\O superb\\O food\\O .\\O"}, {"uid": "180-1", "sentiment": "positive", "target_tags": "The\\O menu\\O is\\O Prix\\O Fixe\\O ,\\O so\\O be\\O prepared\\O to\\O spend\\O at\\O least\\O $\\O 60\\O per\\O person\\O ,\\O but\\O it\\O is\\O Well\\O worth\\O it\\O superb\\O food\\B .\\O", "opinion_tags": "The\\O menu\\O is\\O Prix\\O Fixe\\O ,\\O so\\O be\\O prepared\\O to\\O spend\\O at\\O least\\O $\\O 60\\O per\\O person\\O ,\\O but\\O it\\O is\\O Well\\O worth\\O it\\O superb\\B food\\O .\\O"}, {"uid": "180-2", "sentiment": "neutral", "target_tags": "The\\O menu\\O is\\O Prix\\B Fixe\\I ,\\O so\\O be\\O prepared\\O to\\O spend\\O at\\O least\\O $\\O 60\\O per\\O person\\O ,\\O but\\O it\\O is\\O Well\\O worth\\O it\\O superb\\O food\\O .\\O", "opinion_tags": "The\\O menu\\O is\\O Prix\\O Fixe\\O ,\\O so\\O be\\O prepared\\O to\\O spend\\O at\\O least\\O $\\O 60\\O per\\O person\\O ,\\O but\\O it\\O is\\O Well\\B worth\\I it\\O superb\\O food\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "NN", "NN", ",", "RB", "VB", "VBN", "TO", "VB", "RB", "RBS", "$", "CD", "IN", "NN", ",", "CC", "PRP", "VBZ", "RB", "JJ", "PRP", "JJ", "NN", "."], "head": [2, 5, 5, 5, 0, 5, 9, 9, 5, 11, 9, 13, 14, 11, 14, 17, 14, 23, 23, 23, 23, 23, 5, 23, 26, 23, 5], "deprel": ["det", "nsubj", "cop", "compound", "root", "punct", "advmod", "aux:pass", "parataxis", "mark", "xcomp", "case", "nmod", "obj", "nummod", "case", "nmod", "punct", "cc", "nsubj", "cop", "advmod", "conj", "obj", "amod", "obj", "punct"]}, {"id": "181", "sentence": "We had the most wonderful waitress .", "triples": [{"uid": "181-0", "sentiment": "positive", "target_tags": "We\\O had\\O the\\O most\\O wonderful\\O waitress\\B .\\O", "opinion_tags": "We\\O had\\O the\\O most\\O wonderful\\B waitress\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "RBS", "JJ", "NN", "."], "head": [2, 0, 6, 5, 6, 2, 2], "deprel": ["nsubj", "root", "det", "advmod", "amod", "obj", "punct"]}, {"id": "182", "sentence": "Decor is nice though service can be spotty .", "triples": [{"uid": "182-0", "sentiment": "positive", "target_tags": "Decor\\B is\\O nice\\O though\\O service\\O can\\O be\\O spotty\\O .\\O", "opinion_tags": "Decor\\O is\\O nice\\B though\\O service\\O can\\O be\\O spotty\\O .\\O"}, {"uid": "182-1", "sentiment": "negative", "target_tags": "Decor\\O is\\O nice\\O though\\O service\\B can\\O be\\O spotty\\O .\\O", "opinion_tags": "Decor\\O is\\O nice\\O though\\O service\\O can\\O be\\O spotty\\B .\\O"}], "postag": ["NN", "VBZ", "JJ", "IN", "NN", "MD", "VB", "JJ", "."], "head": [3, 3, 0, 8, 8, 8, 8, 3, 3], "deprel": ["nsubj", "cop", "root", "mark", "nsubj", "aux", "cop", "advcl", "punct"]}, {"id": "183", "sentence": "The blond wood decor is very soothing , the premium sake is excellent and the service is great .", "triples": [{"uid": "183-0", "sentiment": "positive", "target_tags": "The\\O blond\\B wood\\I decor\\I is\\O very\\O soothing\\O ,\\O the\\O premium\\O sake\\O is\\O excellent\\O and\\O the\\O service\\O is\\O great\\O .\\O", "opinion_tags": "The\\O blond\\O wood\\O decor\\O is\\O very\\O soothing\\B ,\\O the\\O premium\\O sake\\O is\\O excellent\\O and\\O the\\O service\\O is\\O great\\O .\\O"}, {"uid": "183-1", "sentiment": "positive", "target_tags": "The\\O blond\\O wood\\O decor\\O is\\O very\\O soothing\\O ,\\O the\\O premium\\O sake\\B is\\O excellent\\O and\\O the\\O service\\O is\\O great\\O .\\O", "opinion_tags": "The\\O blond\\O wood\\O decor\\O is\\O very\\O soothing\\O ,\\O the\\O premium\\O sake\\O is\\O excellent\\B and\\O the\\O service\\O is\\O great\\O .\\O"}, {"uid": "183-2", "sentiment": "positive", "target_tags": "The\\O blond\\O wood\\O decor\\O is\\O very\\O soothing\\O ,\\O the\\O premium\\O sake\\O is\\O excellent\\O and\\O the\\O service\\B is\\O great\\O .\\O", "opinion_tags": "The\\O blond\\O wood\\O decor\\O is\\O very\\O soothing\\O ,\\O the\\O premium\\O sake\\O is\\O excellent\\O and\\O the\\O service\\O is\\O great\\B .\\O"}], "postag": ["DT", "JJ", "NN", "NN", "VBZ", "RB", "JJ", ",", "DT", "NN", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [4, 4, 4, 7, 7, 7, 0, 13, 11, 11, 13, 13, 7, 18, 16, 18, 18, 7, 7], "deprel": ["det", "amod", "compound", "nsubj", "cop", "advmod", "root", "punct", "det", "compound", "nsubj", "cop", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "184", "sentence": "Your money could easily be better spent elsewhere ( Anywhere ) .", "triples": [{"uid": "184-0", "sentiment": "neutral", "target_tags": "Your\\O money\\B could\\O easily\\O be\\O better\\O spent\\O elsewhere\\O (\\O Anywhere\\O )\\O .\\O", "opinion_tags": "Your\\O money\\O could\\O easily\\O be\\O better\\B spent\\O elsewhere\\O (\\O Anywhere\\O )\\O .\\O"}], "postag": ["PRP$", "NN", "MD", "RB", "VB", "RBR", "VBN", "RB", "-LRB-", "RB", "-RRB-", "."], "head": [2, 7, 7, 6, 7, 7, 0, 7, 10, 7, 10, 7], "deprel": ["nmod:poss", "nsubj:pass", "aux", "advmod", "aux:pass", "advmod", "root", "advmod", "punct", "advmod", "punct", "punct"]}, {"id": "185", "sentence": "Try the Pad Se-Ew or Chicken with Cashew Nuts for a memorable and repeatable experience .", "triples": [{"uid": "185-0", "sentiment": "positive", "target_tags": "Try\\O the\\O Pad\\B Se-Ew\\I or\\O Chicken\\O with\\O Cashew\\O Nuts\\O for\\O a\\O memorable\\O and\\O repeatable\\O experience\\O .\\O", "opinion_tags": "Try\\B the\\O Pad\\O Se-Ew\\O or\\O Chicken\\O with\\O Cashew\\O Nuts\\O for\\O a\\O memorable\\O and\\O repeatable\\O experience\\O .\\O"}, {"uid": "185-1", "sentiment": "positive", "target_tags": "Try\\O the\\O Pad\\B Se-Ew\\I or\\O Chicken\\O with\\O Cashew\\O Nuts\\O for\\O a\\O memorable\\O and\\O repeatable\\O experience\\O .\\O", "opinion_tags": "Try\\O the\\O Pad\\O Se-Ew\\O or\\O Chicken\\O with\\O Cashew\\O Nuts\\O for\\O a\\O memorable\\B and\\O repeatable\\O experience\\O .\\O"}, {"uid": "185-2", "sentiment": "positive", "target_tags": "Try\\O the\\O Pad\\B Se-Ew\\I or\\O Chicken\\O with\\O Cashew\\O Nuts\\O for\\O a\\O memorable\\O and\\O repeatable\\O experience\\O .\\O", "opinion_tags": "Try\\O the\\O Pad\\O Se-Ew\\O or\\O Chicken\\O with\\O Cashew\\O Nuts\\O for\\O a\\O memorable\\O and\\O repeatable\\B experience\\O .\\O"}, {"uid": "185-3", "sentiment": "positive", "target_tags": "Try\\O the\\O Pad\\O Se-Ew\\O or\\O Chicken\\B with\\I Cashew\\I Nuts\\I for\\O a\\O memorable\\O and\\O repeatable\\O experience\\O .\\O", "opinion_tags": "Try\\B the\\O Pad\\O Se-Ew\\O or\\O Chicken\\O with\\O Cashew\\O Nuts\\O for\\O a\\O memorable\\O and\\O repeatable\\O experience\\O .\\O"}, {"uid": "185-4", "sentiment": "positive", "target_tags": "Try\\O the\\O Pad\\O Se-Ew\\O or\\O Chicken\\B with\\I Cashew\\I Nuts\\I for\\O a\\O memorable\\O and\\O repeatable\\O experience\\O .\\O", "opinion_tags": "Try\\O the\\O Pad\\O Se-Ew\\O or\\O Chicken\\O with\\O Cashew\\O Nuts\\O for\\O a\\O memorable\\B and\\O repeatable\\O experience\\O .\\O"}, {"uid": "185-5", "sentiment": "positive", "target_tags": "Try\\O the\\O Pad\\O Se-Ew\\O or\\O Chicken\\B with\\I Cashew\\I Nuts\\I for\\O a\\O memorable\\O and\\O repeatable\\O experience\\O .\\O", "opinion_tags": "Try\\O the\\O Pad\\O Se-Ew\\O or\\O Chicken\\O with\\O Cashew\\O Nuts\\O for\\O a\\O memorable\\O and\\O repeatable\\B experience\\O .\\O"}], "postag": ["VB", "DT", "NN", "NNP", "CC", "NN", "IN", "NNP", "NNP", "IN", "DT", "JJ", "CC", "JJ", "NN", "."], "head": [0, 4, 4, 1, 6, 4, 9, 9, 4, 15, 15, 15, 14, 12, 1, 1], "deprel": ["root", "det", "compound", "obj", "cc", "conj", "case", "compound", "nmod", "case", "det", "amod", "cc", "conj", "obl", "punct"]}, {"id": "186", "sentence": "It 's just O.K . pizza .", "triples": [{"uid": "186-0", "sentiment": "neutral", "target_tags": "It\\O 's\\O just\\O O.K\\O .\\O pizza\\B .\\O", "opinion_tags": "It\\O 's\\O just\\O O.K\\B .\\I pizza\\O .\\O"}], "postag": ["PRP", "VBZ", "RB", "JJ", ".", "NN", "."], "head": [4, 4, 4, 0, 4, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct", "parataxis", "punct"]}, {"id": "187", "sentence": "What I did n't like was how the food came right after it was ordered .", "triples": [{"uid": "187-0", "sentiment": "negative", "target_tags": "What\\O I\\O did\\O n't\\O like\\O was\\O how\\O the\\O food\\B came\\O right\\O after\\O it\\O was\\O ordered\\O .\\O", "opinion_tags": "What\\O I\\O did\\B n't\\I like\\I was\\O how\\O the\\O food\\O came\\O right\\O after\\O it\\O was\\O ordered\\O .\\O"}], "postag": ["WP", "PRP", "VBD", "RB", "VB", "VBD", "WRB", "DT", "NN", "VBD", "RB", "IN", "PRP", "VBD", "VBN", "."], "head": [6, 5, 5, 5, 1, 0, 10, 9, 10, 6, 15, 15, 15, 15, 10, 6], "deprel": ["nsubj", "nsubj", "aux", "advmod", "acl:relcl", "root", "mark", "det", "nsubj", "ccomp", "advmod", "mark", "nsubj:pass", "aux:pass", "advcl", "punct"]}, {"id": "188", "sentence": "The best part of the experience was knowing that the manager ( a bubbly , friendly young woman with a great smile ) truly cared about how we were doing .", "triples": [{"uid": "188-0", "sentiment": "positive", "target_tags": "The\\O best\\O part\\O of\\O the\\O experience\\O was\\O knowing\\O that\\O the\\O manager\\B (\\O a\\O bubbly\\O ,\\O friendly\\O young\\O woman\\O with\\O a\\O great\\O smile\\O )\\O truly\\O cared\\O about\\O how\\O we\\O were\\O doing\\O .\\O", "opinion_tags": "The\\O best\\O part\\O of\\O the\\O experience\\O was\\O knowing\\O that\\O the\\O manager\\O (\\O a\\O bubbly\\B ,\\O friendly\\O young\\O woman\\O with\\O a\\O great\\O smile\\O )\\O truly\\O cared\\O about\\O how\\O we\\O were\\O doing\\O .\\O"}, {"uid": "188-1", "sentiment": "positive", "target_tags": "The\\O best\\O part\\O of\\O the\\O experience\\O was\\O knowing\\O that\\O the\\O manager\\B (\\O a\\O bubbly\\O ,\\O friendly\\O young\\O woman\\O with\\O a\\O great\\O smile\\O )\\O truly\\O cared\\O about\\O how\\O we\\O were\\O doing\\O .\\O", "opinion_tags": "The\\O best\\O part\\O of\\O the\\O experience\\O was\\O knowing\\O that\\O the\\O manager\\O (\\O a\\O bubbly\\O ,\\O friendly\\B young\\I woman\\O with\\O a\\O great\\O smile\\O )\\O truly\\O cared\\O about\\O how\\O we\\O were\\O doing\\O .\\O"}], "postag": ["DT", "JJS", "NN", "IN", "DT", "NN", "VBD", "VBG", "IN", "DT", "NN", "-LRB-", "DT", "JJ", ",", "JJ", "JJ", "NN", "IN", "DT", "JJ", "NN", "-RRB-", "RB", "VBD", "IN", "WRB", "PRP", "VBD", "VBG", "."], "head": [3, 3, 8, 6, 6, 3, 8, 0, 25, 11, 25, 18, 18, 18, 14, 18, 18, 11, 22, 22, 22, 18, 18, 25, 8, 30, 30, 30, 30, 25, 8], "deprel": ["det", "amod", "nsubj", "case", "det", "nmod", "aux", "root", "mark", "det", "nsubj", "punct", "det", "amod", "punct", "amod", "amod", "appos", "case", "det", "amod", "nmod", "punct", "advmod", "ccomp", "mark", "mark", "nsubj", "aux", "advcl", "punct"]}, {"id": "189", "sentence": "one of the best Chicken Tikka Masala .", "triples": [{"uid": "189-0", "sentiment": "positive", "target_tags": "one\\O of\\O the\\O best\\O Chicken\\B Tikka\\I Masala\\I .\\O", "opinion_tags": "one\\O of\\O the\\O best\\B Chicken\\O Tikka\\O Masala\\O .\\O"}], "postag": ["CD", "IN", "DT", "JJS", "NNP", "NNP", "NNP", "."], "head": [0, 7, 7, 7, 7, 7, 1, 1], "deprel": ["root", "case", "det", "amod", "compound", "compound", "nmod", "punct"]}, {"id": "190", "sentence": "The corned beef and pastrami are excellent , much less fatty than those big tourist places around Times Square .", "triples": [{"uid": "190-0", "sentiment": "positive", "target_tags": "The\\O corned\\B beef\\I and\\O pastrami\\O are\\O excellent\\O ,\\O much\\O less\\O fatty\\O than\\O those\\O big\\O tourist\\O places\\O around\\O Times\\O Square\\O .\\O", "opinion_tags": "The\\O corned\\O beef\\O and\\O pastrami\\O are\\O excellent\\B ,\\O much\\O less\\O fatty\\O than\\O those\\O big\\O tourist\\O places\\O around\\O Times\\O Square\\O .\\O"}, {"uid": "190-1", "sentiment": "positive", "target_tags": "The\\O corned\\B beef\\I and\\O pastrami\\O are\\O excellent\\O ,\\O much\\O less\\O fatty\\O than\\O those\\O big\\O tourist\\O places\\O around\\O Times\\O Square\\O .\\O", "opinion_tags": "The\\O corned\\O beef\\O and\\O pastrami\\O are\\O excellent\\O ,\\O much\\O less\\B fatty\\I than\\O those\\O big\\O tourist\\O places\\O around\\O Times\\O Square\\O .\\O"}, {"uid": "190-2", "sentiment": "positive", "target_tags": "The\\O corned\\O beef\\O and\\O pastrami\\B are\\O excellent\\O ,\\O much\\O less\\O fatty\\O than\\O those\\O big\\O tourist\\O places\\O around\\O Times\\O Square\\O .\\O", "opinion_tags": "The\\O corned\\O beef\\O and\\O pastrami\\O are\\O excellent\\B ,\\O much\\O less\\O fatty\\O than\\O those\\O big\\O tourist\\O places\\O around\\O Times\\O Square\\O .\\O"}, {"uid": "190-3", "sentiment": "positive", "target_tags": "The\\O corned\\O beef\\O and\\O pastrami\\B are\\O excellent\\O ,\\O much\\O less\\O fatty\\O than\\O those\\O big\\O tourist\\O places\\O around\\O Times\\O Square\\O .\\O", "opinion_tags": "The\\O corned\\O beef\\O and\\O pastrami\\O are\\O excellent\\O ,\\O much\\O less\\B fatty\\I than\\O those\\O big\\O tourist\\O places\\O around\\O Times\\O Square\\O .\\O"}], "postag": ["DT", "NN", "NN", "CC", "NN", "VBP", "JJ", ",", "RB", "RBR", "JJ", "IN", "DT", "JJ", "NN", "NNS", "IN", "NNP", "NNP", "."], "head": [3, 3, 7, 5, 3, 7, 0, 11, 10, 11, 7, 16, 16, 16, 16, 11, 19, 19, 16, 7], "deprel": ["det", "compound", "nsubj", "cc", "conj", "cop", "root", "punct", "advmod", "advmod", "conj", "case", "det", "amod", "compound", "obl", "case", "compound", "nmod", "punct"]}, {"id": "191", "sentence": "The pizza is overpriced and soggy .", "triples": [{"uid": "191-0", "sentiment": "negative", "target_tags": "The\\O pizza\\B is\\O overpriced\\O and\\O soggy\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O overpriced\\B and\\O soggy\\O .\\O"}, {"uid": "191-1", "sentiment": "negative", "target_tags": "The\\O pizza\\B is\\O overpriced\\O and\\O soggy\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O overpriced\\O and\\O soggy\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct"]}, {"id": "192", "sentence": "If you 've ever been along the river in Weehawken you have an idea of the top of view the chart house has to offer .", "triples": [{"uid": "192-0", "sentiment": "positive", "target_tags": "If\\O you\\O 've\\O ever\\O been\\O along\\O the\\O river\\O in\\O Weehawken\\O you\\O have\\O an\\O idea\\O of\\O the\\O top\\O of\\O view\\B the\\O chart\\O house\\O has\\O to\\O offer\\O .\\O", "opinion_tags": "If\\O you\\O 've\\O ever\\O been\\O along\\O the\\O river\\O in\\O Weehawken\\O you\\O have\\O an\\O idea\\O of\\O the\\O top\\B of\\O view\\O the\\O chart\\O house\\O has\\O to\\O offer\\O .\\O"}], "postag": ["IN", "PRP", "VBP", "RB", "VBN", "IN", "DT", "NN", "IN", "NNP", "PRP", "VBP", "DT", "NN", "IN", "DT", "NN", "IN", "NN", "DT", "NN", "NN", "VBZ", "TO", "VB", "."], "head": [8, 8, 8, 8, 8, 8, 8, 12, 10, 8, 12, 0, 14, 12, 17, 17, 14, 19, 17, 22, 22, 23, 17, 25, 23, 12], "deprel": ["mark", "nsubj", "aux", "advmod", "cop", "case", "det", "advcl", "case", "nmod", "nsubj", "root", "det", "obj", "case", "det", "nmod", "case", "nmod", "det", "compound", "nsubj", "acl:relcl", "mark", "xcomp", "punct"]}, {"id": "193", "sentence": "Downstairs lounge is always a good attraction", "triples": [{"uid": "193-0", "sentiment": "positive", "target_tags": "Downstairs\\B lounge\\I is\\O always\\O a\\O good\\O attraction\\O", "opinion_tags": "Downstairs\\O lounge\\O is\\O always\\O a\\O good\\B attraction\\I"}], "postag": ["JJ", "NN", "VBZ", "RB", "DT", "JJ", "NN"], "head": [2, 7, 7, 7, 7, 7, 0], "deprel": ["amod", "nsubj", "cop", "advmod", "det", "amod", "root"]}, {"id": "194", "sentence": "One of us actually liked the expresso - that 's it .", "triples": [{"uid": "194-0", "sentiment": "positive", "target_tags": "One\\O of\\O us\\O actually\\O liked\\O the\\O expresso\\B -\\O that\\O 's\\O it\\O .\\O", "opinion_tags": "One\\O of\\O us\\O actually\\O liked\\B the\\O expresso\\O -\\O that\\O 's\\O it\\O .\\O"}], "postag": ["CD", "IN", "PRP", "RB", "VBD", "DT", "NN", ",", "DT", "VBZ", "PRP", "."], "head": [5, 3, 1, 5, 0, 7, 5, 5, 11, 11, 5, 5], "deprel": ["nsubj", "case", "nmod", "advmod", "root", "det", "obj", "punct", "nsubj", "cop", "parataxis", "punct"]}, {"id": "195", "sentence": "The food was just OK , I would never go back .", "triples": [{"uid": "195-0", "sentiment": "neutral", "target_tags": "The\\O food\\B was\\O just\\O OK\\O ,\\O I\\O would\\O never\\O go\\O back\\O .\\O", "opinion_tags": "The\\O food\\O was\\O just\\O OK\\B ,\\O I\\O would\\O never\\O go\\O back\\O .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", ",", "PRP", "MD", "RB", "VB", "RB", "."], "head": [2, 5, 5, 5, 0, 5, 10, 10, 10, 5, 10, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "nsubj", "aux", "advmod", "parataxis", "advmod", "punct"]}, {"id": "196", "sentence": "Ummm ... the beer was cold .", "triples": [{"uid": "196-0", "sentiment": "positive", "target_tags": "Ummm\\O ...\\O the\\O beer\\B was\\O cold\\O .\\O", "opinion_tags": "Ummm\\O ...\\O the\\O beer\\O was\\O cold\\B .\\O"}], "postag": ["UH", ",", "DT", "NN", "VBD", "JJ", "."], "head": [6, 6, 4, 6, 6, 0, 6], "deprel": ["discourse", "punct", "det", "nsubj", "cop", "root", "punct"]}, {"id": "197", "sentence": "I am happy i did the food was awesome .", "triples": [{"uid": "197-0", "sentiment": "positive", "target_tags": "I\\O am\\O happy\\O i\\O did\\O the\\O food\\B was\\O awesome\\O .\\O", "opinion_tags": "I\\O am\\O happy\\O i\\O did\\O the\\O food\\O was\\O awesome\\B .\\O"}], "postag": ["PRP", "VBP", "JJ", "PRP", "VBD", "DT", "NN", "VBD", "JJ", "."], "head": [3, 3, 0, 9, 9, 7, 9, 9, 3, 3], "deprel": ["nsubj", "cop", "root", "nsubj", "aux", "det", "nsubj", "cop", "ccomp", "punct"]}, {"id": "198", "sentence": "We ordered the chicken casserole , but what we got were a few small pieces of chicken , all dark meat and on the bone .", "triples": [{"uid": "198-0", "sentiment": "negative", "target_tags": "We\\O ordered\\O the\\O chicken\\O casserole\\O ,\\O but\\O what\\O we\\O got\\O were\\O a\\O few\\O small\\O pieces\\O of\\O chicken\\B ,\\O all\\O dark\\O meat\\O and\\O on\\O the\\O bone\\O .\\O", "opinion_tags": "We\\O ordered\\O the\\O chicken\\O casserole\\O ,\\O but\\O what\\O we\\O got\\O were\\O a\\O few\\B small\\O pieces\\O of\\O chicken\\O ,\\O all\\O dark\\O meat\\O and\\O on\\O the\\O bone\\O .\\O"}, {"uid": "198-1", "sentiment": "negative", "target_tags": "We\\O ordered\\O the\\O chicken\\O casserole\\O ,\\O but\\O what\\O we\\O got\\O were\\O a\\O few\\O small\\O pieces\\O of\\O chicken\\O ,\\O all\\O dark\\O meat\\B and\\O on\\O the\\O bone\\O .\\O", "opinion_tags": "We\\O ordered\\O the\\O chicken\\O casserole\\O ,\\O but\\O what\\O we\\O got\\O were\\O a\\O few\\O small\\O pieces\\O of\\O chicken\\O ,\\O all\\O dark\\B meat\\O and\\O on\\O the\\O bone\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "NN", "NN", ",", "CC", "WP", "PRP", "VBD", "VBD", "DT", "JJ", "JJ", "NNS", "IN", "NN", ",", "DT", "JJ", "NN", "CC", "IN", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 15, 15, 15, 10, 8, 15, 15, 15, 15, 2, 17, 15, 21, 21, 21, 17, 25, 25, 25, 17, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "punct", "cc", "nsubj", "nsubj", "acl:relcl", "cop", "det", "amod", "amod", "conj", "case", "nmod", "punct", "det", "amod", "conj", "cc", "case", "det", "conj", "punct"]}, {"id": "199", "sentence": "I recommend the jelly fish , drunken chicken and the soupy dumplings , certainly the stir fry blue crab .", "triples": [{"uid": "199-0", "sentiment": "positive", "target_tags": "I\\O recommend\\O the\\O jelly\\B fish\\I ,\\O drunken\\O chicken\\O and\\O the\\O soupy\\O dumplings\\O ,\\O certainly\\O the\\O stir\\O fry\\O blue\\O crab\\O .\\O", "opinion_tags": "I\\O recommend\\B the\\O jelly\\O fish\\O ,\\O drunken\\O chicken\\O and\\O the\\O soupy\\O dumplings\\O ,\\O certainly\\O the\\O stir\\O fry\\O blue\\O crab\\O .\\O"}, {"uid": "199-1", "sentiment": "positive", "target_tags": "I\\O recommend\\O the\\O jelly\\O fish\\O ,\\O drunken\\B chicken\\I and\\O the\\O soupy\\O dumplings\\O ,\\O certainly\\O the\\O stir\\O fry\\O blue\\O crab\\O .\\O", "opinion_tags": "I\\O recommend\\B the\\O jelly\\O fish\\O ,\\O drunken\\O chicken\\O and\\O the\\O soupy\\O dumplings\\O ,\\O certainly\\O the\\O stir\\O fry\\O blue\\O crab\\O .\\O"}, {"uid": "199-2", "sentiment": "positive", "target_tags": "I\\O recommend\\O the\\O jelly\\O fish\\O ,\\O drunken\\O chicken\\O and\\O the\\O soupy\\B dumplings\\I ,\\O certainly\\O the\\O stir\\O fry\\O blue\\O crab\\O .\\O", "opinion_tags": "I\\O recommend\\B the\\O jelly\\O fish\\O ,\\O drunken\\O chicken\\O and\\O the\\O soupy\\O dumplings\\O ,\\O certainly\\O the\\O stir\\O fry\\O blue\\O crab\\O .\\O"}, {"uid": "199-3", "sentiment": "positive", "target_tags": "I\\O recommend\\O the\\O jelly\\O fish\\O ,\\O drunken\\O chicken\\O and\\O the\\O soupy\\O dumplings\\O ,\\O certainly\\O the\\O stir\\B fry\\I blue\\I crab\\I .\\O", "opinion_tags": "I\\O recommend\\B the\\O jelly\\O fish\\O ,\\O drunken\\O chicken\\O and\\O the\\O soupy\\O dumplings\\O ,\\O certainly\\O the\\O stir\\O fry\\O blue\\O crab\\O .\\O"}], "postag": ["PRP", "VBP", "DT", "NN", "NN", ",", "JJ", "NN", "CC", "DT", "JJ", "NNS", ",", "RB", "DT", "NN", "VBP", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 12, 12, 12, 5, 2, 17, 16, 17, 2, 19, 17, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "punct", "amod", "conj", "cc", "det", "amod", "conj", "punct", "advmod", "det", "nsubj", "parataxis", "amod", "obj", "punct"]}, {"id": "200", "sentence": "If you do n't mind pre-sliced low quality fish , unfriendly staff and a sushi chef that looks like he is miserable then this is your place .", "triples": [{"uid": "200-0", "sentiment": "negative", "target_tags": "If\\O you\\O do\\O n't\\O mind\\O pre-sliced\\O low\\O quality\\O fish\\B ,\\O unfriendly\\O staff\\O and\\O a\\O sushi\\O chef\\O that\\O looks\\O like\\O he\\O is\\O miserable\\O then\\O this\\O is\\O your\\O place\\O .\\O", "opinion_tags": "If\\O you\\O do\\O n't\\O mind\\O pre-sliced\\O low\\B quality\\I fish\\O ,\\O unfriendly\\O staff\\O and\\O a\\O sushi\\O chef\\O that\\O looks\\O like\\O he\\O is\\O miserable\\O then\\O this\\O is\\O your\\O place\\O .\\O"}, {"uid": "200-1", "sentiment": "negative", "target_tags": "If\\O you\\O do\\O n't\\O mind\\O pre-sliced\\O low\\O quality\\O fish\\O ,\\O unfriendly\\O staff\\B and\\O a\\O sushi\\O chef\\O that\\O looks\\O like\\O he\\O is\\O miserable\\O then\\O this\\O is\\O your\\O place\\O .\\O", "opinion_tags": "If\\O you\\O do\\O n't\\O mind\\O pre-sliced\\O low\\O quality\\O fish\\O ,\\O unfriendly\\B staff\\O and\\O a\\O sushi\\O chef\\O that\\O looks\\O like\\O he\\O is\\O miserable\\O then\\O this\\O is\\O your\\O place\\O .\\O"}, {"uid": "200-2", "sentiment": "negative", "target_tags": "If\\O you\\O do\\O n't\\O mind\\O pre-sliced\\O low\\O quality\\O fish\\O ,\\O unfriendly\\O staff\\O and\\O a\\O sushi\\B chef\\I that\\O looks\\O like\\O he\\O is\\O miserable\\O then\\O this\\O is\\O your\\O place\\O .\\O", "opinion_tags": "If\\O you\\O do\\O n't\\O mind\\O pre-sliced\\O low\\O quality\\O fish\\O ,\\O unfriendly\\O staff\\O and\\O a\\O sushi\\O chef\\O that\\O looks\\O like\\O he\\O is\\O miserable\\B then\\O this\\O is\\O your\\O place\\O .\\O"}], "postag": ["IN", "PRP", "VBP", "RB", "VB", "JJ", "JJ", "JJ", "NN", ",", "JJ", "NN", "CC", "DT", "NN", "NN", "WDT", "VBZ", "IN", "PRP", "VBZ", "JJ", "RB", "DT", "VBZ", "PRP$", "NN", "."], "head": [5, 5, 5, 5, 27, 9, 9, 9, 5, 12, 12, 9, 16, 16, 16, 9, 18, 16, 22, 22, 22, 18, 27, 27, 27, 27, 0, 27], "deprel": ["mark", "nsubj", "aux", "advmod", "advcl", "amod", "amod", "amod", "obj", "punct", "amod", "conj", "cc", "det", "compound", "conj", "nsubj", "acl:relcl", "mark", "nsubj", "cop", "advcl", "advmod", "nsubj", "cop", "nmod:poss", "root", "punct"]}, {"id": "201", "sentence": "The pesto pizza was excellent , thin-crust pizza with a nice amount of spicy Italian cheese that I 'd never heard of before .", "triples": [{"uid": "201-0", "sentiment": "positive", "target_tags": "The\\O pesto\\B pizza\\I was\\O excellent\\O ,\\O thin-crust\\O pizza\\O with\\O a\\O nice\\O amount\\O of\\O spicy\\O Italian\\O cheese\\O that\\O I\\O 'd\\O never\\O heard\\O of\\O before\\O .\\O", "opinion_tags": "The\\O pesto\\O pizza\\O was\\O excellent\\B ,\\O thin-crust\\O pizza\\O with\\O a\\O nice\\O amount\\O of\\O spicy\\O Italian\\O cheese\\O that\\O I\\O 'd\\O never\\O heard\\O of\\O before\\O .\\O"}, {"uid": "201-1", "sentiment": "positive", "target_tags": "The\\O pesto\\O pizza\\O was\\O excellent\\O ,\\O thin-crust\\O pizza\\O with\\O a\\O nice\\O amount\\O of\\O spicy\\O Italian\\B cheese\\I that\\O I\\O 'd\\O never\\O heard\\O of\\O before\\O .\\O", "opinion_tags": "The\\O pesto\\O pizza\\O was\\O excellent\\O ,\\O thin-crust\\O pizza\\O with\\O a\\O nice\\B amount\\O of\\O spicy\\O Italian\\O cheese\\O that\\O I\\O 'd\\O never\\O heard\\O of\\O before\\O .\\O"}, {"uid": "201-2", "sentiment": "positive", "target_tags": "The\\O pesto\\O pizza\\O was\\O excellent\\O ,\\O thin-crust\\O pizza\\O with\\O a\\O nice\\O amount\\O of\\O spicy\\O Italian\\B cheese\\I that\\O I\\O 'd\\O never\\O heard\\O of\\O before\\O .\\O", "opinion_tags": "The\\O pesto\\O pizza\\O was\\O excellent\\O ,\\O thin-crust\\O pizza\\O with\\O a\\O nice\\O amount\\O of\\O spicy\\B Italian\\O cheese\\O that\\O I\\O 'd\\O never\\O heard\\O of\\O before\\O .\\O"}], "postag": ["DT", "NN", "NN", "VBD", "JJ", ",", "JJ", "NN", "IN", "DT", "JJ", "NN", "IN", "JJ", "JJ", "NN", "WDT", "PRP", "VBD", "RB", "VBN", "IN", "RB", "."], "head": [3, 3, 5, 5, 0, 8, 8, 5, 12, 12, 12, 8, 16, 16, 16, 12, 21, 21, 21, 21, 16, 17, 21, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "amod", "conj", "case", "det", "amod", "nmod", "case", "amod", "amod", "nmod", "obj", "nsubj", "aux", "advmod", "acl:relcl", "case", "advmod", "punct"]}, {"id": "202", "sentence": "Delicate spices , onions , eggs and a kick-ass roti .", "triples": [{"uid": "202-0", "sentiment": "positive", "target_tags": "Delicate\\O spices\\B ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O", "opinion_tags": "Delicate\\B spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O"}, {"uid": "202-1", "sentiment": "positive", "target_tags": "Delicate\\O spices\\O ,\\O onions\\B ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O", "opinion_tags": "Delicate\\B spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O"}, {"uid": "202-2", "sentiment": "positive", "target_tags": "Delicate\\O spices\\O ,\\O onions\\O ,\\O eggs\\B and\\O a\\O kick-ass\\O roti\\O .\\O", "opinion_tags": "Delicate\\B spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O"}, {"uid": "202-3", "sentiment": "positive", "target_tags": "Delicate\\O spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\B .\\O", "opinion_tags": "Delicate\\O spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\B roti\\O .\\O"}], "postag": ["JJ", "NNS", ",", "NNS", ",", "NNS", "CC", "DT", "JJ", "NN", "."], "head": [2, 0, 4, 2, 6, 2, 10, 10, 10, 2, 2], "deprel": ["amod", "root", "punct", "conj", "punct", "conj", "cc", "det", "amod", "conj", "punct"]}, {"id": "203", "sentence": "Incredible food at a very agreeable price brings me back just about every other day to this authentic Thai restaurant .", "triples": [{"uid": "203-0", "sentiment": "positive", "target_tags": "Incredible\\O food\\B at\\O a\\O very\\O agreeable\\O price\\O brings\\O me\\O back\\O just\\O about\\O every\\O other\\O day\\O to\\O this\\O authentic\\O Thai\\O restaurant\\O .\\O", "opinion_tags": "Incredible\\B food\\O at\\O a\\O very\\O agreeable\\O price\\O brings\\O me\\O back\\O just\\O about\\O every\\O other\\O day\\O to\\O this\\O authentic\\O Thai\\O restaurant\\O .\\O"}, {"uid": "203-1", "sentiment": "positive", "target_tags": "Incredible\\O food\\O at\\O a\\O very\\O agreeable\\O price\\B brings\\O me\\O back\\O just\\O about\\O every\\O other\\O day\\O to\\O this\\O authentic\\O Thai\\O restaurant\\O .\\O", "opinion_tags": "Incredible\\O food\\O at\\O a\\O very\\O agreeable\\B price\\O brings\\O me\\O back\\O just\\O about\\O every\\O other\\O day\\O to\\O this\\O authentic\\O Thai\\O restaurant\\O .\\O"}], "postag": ["JJ", "NN", "IN", "DT", "RB", "JJ", "NN", "VBZ", "PRP", "RB", "RB", "IN", "DT", "JJ", "NN", "IN", "DT", "JJ", "JJ", "NN", "."], "head": [2, 8, 7, 7, 6, 7, 2, 0, 8, 8, 15, 15, 15, 15, 8, 20, 20, 20, 20, 15, 8], "deprel": ["amod", "nsubj", "case", "det", "advmod", "amod", "nmod", "root", "obj", "advmod", "advmod", "case", "det", "amod", "obl", "case", "det", "amod", "amod", "nmod", "punct"]}, {"id": "204", "sentence": "This was the worst dining experience I 've ever had .", "triples": [{"uid": "204-0", "sentiment": "negative", "target_tags": "This\\O was\\O the\\O worst\\O dining\\B experience\\I I\\O 've\\O ever\\O had\\O .\\O", "opinion_tags": "This\\O was\\O the\\O worst\\B dining\\O experience\\O I\\O 've\\O ever\\O had\\O .\\O"}], "postag": ["DT", "VBD", "DT", "JJS", "NN", "NN", "PRP", "VBP", "RB", "VBN", "."], "head": [6, 6, 6, 6, 6, 0, 10, 10, 10, 6, 6], "deprel": ["nsubj", "cop", "det", "amod", "compound", "root", "nsubj", "aux", "advmod", "acl:relcl", "punct"]}, {"id": "205", "sentence": "Food was okay , nothing great .", "triples": [{"uid": "205-0", "sentiment": "neutral", "target_tags": "Food\\B was\\O okay\\O ,\\O nothing\\O great\\O .\\O", "opinion_tags": "Food\\O was\\O okay\\B ,\\O nothing\\O great\\O .\\O"}, {"uid": "205-1", "sentiment": "neutral", "target_tags": "Food\\B was\\O okay\\O ,\\O nothing\\O great\\O .\\O", "opinion_tags": "Food\\O was\\O okay\\O ,\\O nothing\\B great\\I .\\O"}], "postag": ["NN", "VBD", "JJ", ",", "NN", "JJ", "."], "head": [3, 3, 0, 3, 3, 5, 3], "deprel": ["nsubj", "cop", "root", "punct", "parataxis", "amod", "punct"]}, {"id": "206", "sentence": "The bread is the soft paratha bread ( unlike the plain bread they use in Calcutta ) , and the stuffing is tandoori styled and very flavorful .", "triples": [{"uid": "206-0", "sentiment": "positive", "target_tags": "The\\O bread\\O is\\O the\\O soft\\O paratha\\B bread\\I (\\O unlike\\O the\\O plain\\O bread\\O they\\O use\\O in\\O Calcutta\\O )\\O ,\\O and\\O the\\O stuffing\\O is\\O tandoori\\O styled\\O and\\O very\\O flavorful\\O .\\O", "opinion_tags": "The\\O bread\\O is\\O the\\O soft\\B paratha\\O bread\\O (\\O unlike\\O the\\O plain\\O bread\\O they\\O use\\O in\\O Calcutta\\O )\\O ,\\O and\\O the\\O stuffing\\O is\\O tandoori\\O styled\\O and\\O very\\O flavorful\\O .\\O"}, {"uid": "206-1", "sentiment": "negative", "target_tags": "The\\O bread\\O is\\O the\\O soft\\O paratha\\O bread\\O (\\O unlike\\O the\\O plain\\O bread\\B they\\O use\\O in\\O Calcutta\\O )\\O ,\\O and\\O the\\O stuffing\\O is\\O tandoori\\O styled\\O and\\O very\\O flavorful\\O .\\O", "opinion_tags": "The\\O bread\\O is\\O the\\O soft\\O paratha\\O bread\\O (\\O unlike\\O the\\O plain\\B bread\\O they\\O use\\O in\\O Calcutta\\O )\\O ,\\O and\\O the\\O stuffing\\O is\\O tandoori\\O styled\\O and\\O very\\O flavorful\\O .\\O"}, {"uid": "206-2", "sentiment": "positive", "target_tags": "The\\O bread\\O is\\O the\\O soft\\O paratha\\O bread\\O (\\O unlike\\O the\\O plain\\O bread\\O they\\O use\\O in\\O Calcutta\\O )\\O ,\\O and\\O the\\O stuffing\\B is\\O tandoori\\O styled\\O and\\O very\\O flavorful\\O .\\O", "opinion_tags": "The\\O bread\\O is\\O the\\O soft\\O paratha\\O bread\\O (\\O unlike\\O the\\O plain\\O bread\\O they\\O use\\O in\\O Calcutta\\O )\\O ,\\O and\\O the\\O stuffing\\O is\\O tandoori\\O styled\\B and\\O very\\O flavorful\\O .\\O"}, {"uid": "206-3", "sentiment": "positive", "target_tags": "The\\O bread\\O is\\O the\\O soft\\O paratha\\O bread\\O (\\O unlike\\O the\\O plain\\O bread\\O they\\O use\\O in\\O Calcutta\\O )\\O ,\\O and\\O the\\O stuffing\\B is\\O tandoori\\O styled\\O and\\O very\\O flavorful\\O .\\O", "opinion_tags": "The\\O bread\\O is\\O the\\O soft\\O paratha\\O bread\\O (\\O unlike\\O the\\O plain\\O bread\\O they\\O use\\O in\\O Calcutta\\O )\\O ,\\O and\\O the\\O stuffing\\O is\\O tandoori\\O styled\\O and\\O very\\O flavorful\\B .\\O"}, {"uid": "206-4", "sentiment": "positive", "target_tags": "The\\O bread\\O is\\O the\\O soft\\O paratha\\O bread\\O (\\O unlike\\O the\\O plain\\O bread\\O they\\O use\\O in\\O Calcutta\\O )\\O ,\\O and\\O the\\O stuffing\\O is\\O tandoori\\B styled\\O and\\O very\\O flavorful\\O .\\O", "opinion_tags": "The\\O bread\\O is\\O the\\O soft\\O paratha\\O bread\\O (\\O unlike\\O the\\O plain\\O bread\\O they\\O use\\O in\\O Calcutta\\O )\\O ,\\O and\\O the\\O stuffing\\O is\\O tandoori\\O styled\\B and\\O very\\O flavorful\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "JJ", "NN", "NN", "-LRB-", "IN", "DT", "JJ", "NN", "PRP", "VBP", "IN", "NNP", "-RRB-", ",", "CC", "DT", "NN", "VBZ", "NN", "VBN", "CC", "RB", "JJ", "."], "head": [2, 7, 7, 7, 7, 7, 0, 12, 12, 12, 12, 7, 14, 12, 16, 14, 12, 24, 24, 21, 24, 24, 7, 7, 27, 27, 24, 7], "deprel": ["det", "nsubj", "cop", "det", "amod", "compound", "root", "punct", "case", "det", "amod", "nmod", "nsubj", "acl:relcl", "case", "obl", "punct", "punct", "cc", "det", "nsubj:pass", "aux:pass", "conj", "conj", "cc", "advmod", "conj", "punct"]}, {"id": "207", "sentence": "The food however , is what one might expect .", "triples": [{"uid": "207-0", "sentiment": "neutral", "target_tags": "The\\O food\\B however\\O ,\\O is\\O what\\O one\\O might\\O expect\\O .\\O", "opinion_tags": "The\\O food\\O however\\O ,\\O is\\O what\\O one\\O might\\O expect\\B .\\O"}], "postag": ["DT", "NN", "RB", ",", "VBZ", "WP", "PRP", "MD", "VB", "."], "head": [2, 6, 6, 6, 6, 0, 9, 9, 6, 6], "deprel": ["det", "nsubj", "advmod", "punct", "cop", "root", "nsubj", "aux", "acl:relcl", "punct"]}, {"id": "208", "sentence": "Food is average , and I would say even the chain restaurant Baluchi 's tastes better .", "triples": [{"uid": "208-0", "sentiment": "neutral", "target_tags": "Food\\B is\\O average\\O ,\\O and\\O I\\O would\\O say\\O even\\O the\\O chain\\O restaurant\\O Baluchi\\O 's\\O tastes\\O better\\O .\\O", "opinion_tags": "Food\\O is\\O average\\B ,\\O and\\O I\\O would\\O say\\O even\\O the\\O chain\\O restaurant\\O Baluchi\\O 's\\O tastes\\O better\\O .\\O"}], "postag": ["NN", "VBZ", "JJ", ",", "CC", "PRP", "MD", "VB", "RB", "DT", "NN", "NN", "NNP", "POS", "NNS", "JJR", "."], "head": [3, 3, 0, 8, 8, 8, 8, 3, 12, 12, 12, 13, 15, 13, 16, 8, 3], "deprel": ["nsubj", "cop", "root", "punct", "cc", "nsubj", "aux", "conj", "advmod", "det", "compound", "compound", "nmod:poss", "case", "nsubj", "ccomp", "punct"]}, {"id": "209", "sentence": "The place is small and cramped but the food is fantastic .", "triples": [{"uid": "209-0", "sentiment": "negative", "target_tags": "The\\O place\\B is\\O small\\O and\\O cramped\\O but\\O the\\O food\\O is\\O fantastic\\O .\\O", "opinion_tags": "The\\O place\\O is\\O small\\B and\\O cramped\\O but\\O the\\O food\\O is\\O fantastic\\O .\\O"}, {"uid": "209-1", "sentiment": "negative", "target_tags": "The\\O place\\B is\\O small\\O and\\O cramped\\O but\\O the\\O food\\O is\\O fantastic\\O .\\O", "opinion_tags": "The\\O place\\O is\\O small\\O and\\O cramped\\B but\\O the\\O food\\O is\\O fantastic\\O .\\O"}, {"uid": "209-2", "sentiment": "positive", "target_tags": "The\\O place\\O is\\O small\\O and\\O cramped\\O but\\O the\\O food\\B is\\O fantastic\\O .\\O", "opinion_tags": "The\\O place\\O is\\O small\\O and\\O cramped\\O but\\O the\\O food\\O is\\O fantastic\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 11, 9, 11, 11, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "210", "sentence": "The food is tasty and portion sizes are appropriate .", "triples": [{"uid": "210-0", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O tasty\\O and\\O portion\\O sizes\\O are\\O appropriate\\O .\\O", "opinion_tags": "The\\O food\\O is\\O tasty\\B and\\O portion\\O sizes\\O are\\O appropriate\\O .\\O"}, {"uid": "210-1", "sentiment": "positive", "target_tags": "The\\O food\\O is\\O tasty\\O and\\O portion\\B sizes\\I are\\O appropriate\\O .\\O", "opinion_tags": "The\\O food\\O is\\O tasty\\O and\\O portion\\O sizes\\O are\\O appropriate\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "NN", "NNS", "VBP", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "compound", "nsubj", "cop", "conj", "punct"]}, {"id": "211", "sentence": "The bartender on my most recent visit was so incredibly rude that I will never go back .", "triples": [{"uid": "211-0", "sentiment": "negative", "target_tags": "The\\O bartender\\B on\\O my\\O most\\O recent\\O visit\\O was\\O so\\O incredibly\\O rude\\O that\\O I\\O will\\O never\\O go\\O back\\O .\\O", "opinion_tags": "The\\O bartender\\O on\\O my\\O most\\O recent\\O visit\\O was\\O so\\O incredibly\\O rude\\B that\\O I\\O will\\O never\\O go\\O back\\O .\\O"}], "postag": ["DT", "NN", "IN", "PRP$", "RBS", "JJ", "NN", "VBD", "RB", "RB", "JJ", "IN", "PRP", "MD", "RB", "VB", "RB", "."], "head": [2, 11, 7, 7, 6, 7, 2, 11, 10, 11, 0, 16, 16, 16, 16, 11, 16, 11], "deprel": ["det", "nsubj", "case", "nmod:poss", "advmod", "amod", "nmod", "cop", "advmod", "advmod", "root", "mark", "nsubj", "aux", "advmod", "ccomp", "advmod", "punct"]}, {"id": "212", "sentence": "you can actually get 2 salads worth if u take it home and add it to some lettuce !", "triples": [{"uid": "212-0", "sentiment": "negative", "target_tags": "you\\O can\\O actually\\O get\\O 2\\O salads\\B worth\\O if\\O u\\O take\\O it\\O home\\O and\\O add\\O it\\O to\\O some\\O lettuce\\O !\\O", "opinion_tags": "you\\O can\\O actually\\O get\\O 2\\O salads\\O worth\\B if\\O u\\O take\\O it\\O home\\O and\\O add\\O it\\O to\\O some\\O lettuce\\O !\\O"}], "postag": ["PRP", "MD", "RB", "VB", "CD", "NNS", "JJ", "IN", "PRP", "VBP", "PRP", "RB", "CC", "VB", "PRP", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 4, 10, 10, 4, 10, 10, 14, 10, 14, 18, 18, 14, 4], "deprel": ["nsubj", "aux", "advmod", "root", "nummod", "obj", "xcomp", "mark", "nsubj", "advcl", "obj", "advmod", "cc", "conj", "obj", "case", "det", "obl", "punct"]}, {"id": "213", "sentence": "The ambience is very romantic and definitely a good place to bring a date .", "triples": [{"uid": "213-0", "sentiment": "positive", "target_tags": "The\\O ambience\\B is\\O very\\O romantic\\O and\\O definitely\\O a\\O good\\O place\\O to\\O bring\\O a\\O date\\O .\\O", "opinion_tags": "The\\O ambience\\O is\\O very\\O romantic\\B and\\O definitely\\O a\\O good\\O place\\O to\\O bring\\O a\\O date\\O .\\O"}, {"uid": "213-1", "sentiment": "positive", "target_tags": "The\\O ambience\\O is\\O very\\O romantic\\O and\\O definitely\\O a\\O good\\O place\\B to\\O bring\\O a\\O date\\O .\\O", "opinion_tags": "The\\O ambience\\O is\\O very\\O romantic\\O and\\O definitely\\O a\\O good\\B place\\O to\\O bring\\O a\\O date\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "RB", "DT", "JJ", "NN", "TO", "VB", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 10, 10, 10, 10, 5, 12, 10, 14, 12, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "advmod", "det", "amod", "conj", "mark", "acl", "det", "obj", "punct"]}, {"id": "214", "sentence": "It is far more popular as a bar than as a restaurant , with only a few tables and the waiter being the bartender , but we greatly enjoyed the unobtrusive atmosphere .", "triples": [{"uid": "214-0", "sentiment": "positive", "target_tags": "It\\O is\\O far\\O more\\O popular\\O as\\O a\\O bar\\B than\\O as\\O a\\O restaurant\\O ,\\O with\\O only\\O a\\O few\\O tables\\O and\\O the\\O waiter\\O being\\O the\\O bartender\\O ,\\O but\\O we\\O greatly\\O enjoyed\\O the\\O unobtrusive\\O atmosphere\\O .\\O", "opinion_tags": "It\\O is\\O far\\O more\\O popular\\B as\\O a\\O bar\\O than\\O as\\O a\\O restaurant\\O ,\\O with\\O only\\O a\\O few\\O tables\\O and\\O the\\O waiter\\O being\\O the\\O bartender\\O ,\\O but\\O we\\O greatly\\O enjoyed\\O the\\O unobtrusive\\O atmosphere\\O .\\O"}, {"uid": "214-1", "sentiment": "negative", "target_tags": "It\\O is\\O far\\O more\\O popular\\O as\\O a\\O bar\\O than\\O as\\O a\\O restaurant\\O ,\\O with\\O only\\O a\\O few\\O tables\\B and\\O the\\O waiter\\O being\\O the\\O bartender\\O ,\\O but\\O we\\O greatly\\O enjoyed\\O the\\O unobtrusive\\O atmosphere\\O .\\O", "opinion_tags": "It\\O is\\O far\\O more\\O popular\\O as\\O a\\O bar\\O than\\O as\\O a\\O restaurant\\O ,\\O with\\O only\\O a\\O few\\B tables\\O and\\O the\\O waiter\\O being\\O the\\O bartender\\O ,\\O but\\O we\\O greatly\\O enjoyed\\O the\\O unobtrusive\\O atmosphere\\O .\\O"}, {"uid": "214-2", "sentiment": "positive", "target_tags": "It\\O is\\O far\\O more\\O popular\\O as\\O a\\O bar\\O than\\O as\\O a\\O restaurant\\O ,\\O with\\O only\\O a\\O few\\O tables\\O and\\O the\\O waiter\\O being\\O the\\O bartender\\O ,\\O but\\O we\\O greatly\\O enjoyed\\O the\\O unobtrusive\\O atmosphere\\B .\\O", "opinion_tags": "It\\O is\\O far\\O more\\O popular\\O as\\O a\\O bar\\O than\\O as\\O a\\O restaurant\\O ,\\O with\\O only\\O a\\O few\\O tables\\O and\\O the\\O waiter\\O being\\O the\\O bartender\\O ,\\O but\\O we\\O greatly\\O enjoyed\\B the\\O unobtrusive\\O atmosphere\\O .\\O"}, {"uid": "214-3", "sentiment": "positive", "target_tags": "It\\O is\\O far\\O more\\O popular\\O as\\O a\\O bar\\O than\\O as\\O a\\O restaurant\\O ,\\O with\\O only\\O a\\O few\\O tables\\O and\\O the\\O waiter\\O being\\O the\\O bartender\\O ,\\O but\\O we\\O greatly\\O enjoyed\\O the\\O unobtrusive\\O atmosphere\\B .\\O", "opinion_tags": "It\\O is\\O far\\O more\\O popular\\O as\\O a\\O bar\\O than\\O as\\O a\\O restaurant\\O ,\\O with\\O only\\O a\\O few\\O tables\\O and\\O the\\O waiter\\O being\\O the\\O bartender\\O ,\\O but\\O we\\O greatly\\O enjoyed\\O the\\O unobtrusive\\B atmosphere\\O .\\O"}], "postag": ["PRP", "VBZ", "RB", "RBR", "JJ", "IN", "DT", "NN", "IN", "IN", "DT", "NN", ",", "IN", "RB", "DT", "JJ", "NNS", "CC", "DT", "NN", "VBG", "DT", "NN", ",", "CC", "PRP", "RB", "VBD", "DT", "JJ", "NN", "."], "head": [5, 5, 4, 5, 0, 8, 8, 5, 12, 12, 12, 8, 18, 18, 18, 18, 18, 12, 21, 21, 18, 24, 24, 18, 29, 29, 29, 29, 5, 32, 32, 29, 5], "deprel": ["nsubj", "cop", "advmod", "advmod", "root", "case", "det", "obl", "case", "case", "det", "nmod", "punct", "case", "cc:preconj", "det", "amod", "nmod", "cc", "det", "conj", "cop", "det", "conj", "punct", "cc", "nsubj", "advmod", "conj", "det", "amod", "obj", "punct"]}, {"id": "215", "sentence": "There are much better places in NY with better prices .", "triples": [{"uid": "215-0", "sentiment": "negative", "target_tags": "There\\O are\\O much\\O better\\O places\\O in\\O NY\\O with\\O better\\O prices\\B .\\O", "opinion_tags": "There\\O are\\O much\\O better\\O places\\O in\\O NY\\O with\\O better\\B prices\\O .\\O"}], "postag": ["EX", "VBP", "RB", "JJR", "NNS", "IN", "NNP", "IN", "JJR", "NNS", "."], "head": [2, 0, 4, 5, 2, 7, 5, 10, 10, 2, 2], "deprel": ["expl", "root", "advmod", "amod", "nsubj", "case", "nmod", "case", "amod", "obl", "punct"]}, {"id": "216", "sentence": "The ambience is very calm and quiet .", "triples": [{"uid": "216-0", "sentiment": "positive", "target_tags": "The\\O ambience\\B is\\O very\\O calm\\O and\\O quiet\\O .\\O", "opinion_tags": "The\\O ambience\\O is\\O very\\O calm\\B and\\O quiet\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"]}, {"id": "217", "sentence": "Both times I was extremely dissappointed by the service , which was boarderline rude .", "triples": [{"uid": "217-0", "sentiment": "negative", "target_tags": "Both\\O times\\O I\\O was\\O extremely\\O dissappointed\\O by\\O the\\O service\\B ,\\O which\\O was\\O boarderline\\O rude\\O .\\O", "opinion_tags": "Both\\O times\\O I\\O was\\O extremely\\O dissappointed\\B by\\O the\\O service\\O ,\\O which\\O was\\O boarderline\\O rude\\O .\\O"}, {"uid": "217-1", "sentiment": "negative", "target_tags": "Both\\O times\\O I\\O was\\O extremely\\O dissappointed\\O by\\O the\\O service\\B ,\\O which\\O was\\O boarderline\\O rude\\O .\\O", "opinion_tags": "Both\\O times\\O I\\O was\\O extremely\\O dissappointed\\O by\\O the\\O service\\O ,\\O which\\O was\\O boarderline\\O rude\\B .\\O"}], "postag": ["DT", "NNS", "PRP", "VBD", "RB", "JJ", "IN", "DT", "NN", ",", "WDT", "VBD", "RBR", "JJ", "."], "head": [2, 6, 6, 6, 6, 0, 9, 9, 6, 9, 14, 14, 14, 9, 6], "deprel": ["det", "obl:tmod", "nsubj", "cop", "advmod", "root", "case", "det", "obl", "punct", "nsubj", "cop", "advmod", "acl:relcl", "punct"]}, {"id": "218", "sentence": "Considering their price of $ 6.25 for lunch special , the dish was ridiculously small .", "triples": [{"uid": "218-0", "sentiment": "negative", "target_tags": "Considering\\O their\\O price\\O of\\O $\\O 6.25\\O for\\O lunch\\O special\\O ,\\O the\\O dish\\B was\\O ridiculously\\O small\\O .\\O", "opinion_tags": "Considering\\O their\\O price\\O of\\O $\\O 6.25\\O for\\O lunch\\O special\\O ,\\O the\\O dish\\O was\\O ridiculously\\B small\\I .\\O"}], "postag": ["VBG", "PRP$", "NN", "IN", "$", "CD", "IN", "NN", "JJ", ",", "DT", "NN", "VBD", "RB", "JJ", "."], "head": [15, 3, 1, 5, 3, 5, 8, 9, 1, 15, 12, 15, 15, 15, 0, 15], "deprel": ["advcl", "nmod:poss", "obj", "case", "nmod", "nummod", "case", "compound", "obl", "punct", "det", "nsubj", "cop", "advmod", "root", "punct"]}, {"id": "219", "sentence": "The food was pretty traditional but it was hot and good with large portions .", "triples": [{"uid": "219-0", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O pretty\\O traditional\\O but\\O it\\O was\\O hot\\O and\\O good\\O with\\O large\\O portions\\O .\\O", "opinion_tags": "The\\O food\\O was\\O pretty\\O traditional\\B but\\O it\\O was\\O hot\\O and\\O good\\O with\\O large\\O portions\\O .\\O"}, {"uid": "219-1", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O pretty\\O traditional\\O but\\O it\\O was\\O hot\\O and\\O good\\O with\\O large\\O portions\\O .\\O", "opinion_tags": "The\\O food\\O was\\O pretty\\O traditional\\O but\\O it\\O was\\O hot\\B and\\O good\\O with\\O large\\O portions\\O .\\O"}, {"uid": "219-2", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O pretty\\O traditional\\O but\\O it\\O was\\O hot\\O and\\O good\\O with\\O large\\O portions\\O .\\O", "opinion_tags": "The\\O food\\O was\\O pretty\\O traditional\\O but\\O it\\O was\\O hot\\O and\\O good\\B with\\O large\\O portions\\O .\\O"}, {"uid": "219-3", "sentiment": "positive", "target_tags": "The\\O food\\O was\\O pretty\\O traditional\\O but\\O it\\O was\\O hot\\O and\\O good\\O with\\O large\\O portions\\B .\\O", "opinion_tags": "The\\O food\\O was\\O pretty\\O traditional\\O but\\O it\\O was\\O hot\\O and\\O good\\O with\\O large\\B portions\\O .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "PRP", "VBD", "JJ", "CC", "JJ", "IN", "JJ", "NNS", "."], "head": [2, 5, 5, 5, 0, 9, 9, 9, 5, 11, 9, 14, 14, 9, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "nsubj", "cop", "conj", "cc", "conj", "case", "amod", "obl", "punct"]}, {"id": "220", "sentence": "They were very abrupt with me when I called and actually claimed the food was late because they were out of rice .", "triples": [{"uid": "220-0", "sentiment": "negative", "target_tags": "They\\O were\\O very\\O abrupt\\O with\\O me\\O when\\O I\\O called\\O and\\O actually\\O claimed\\O the\\O food\\B was\\O late\\O because\\O they\\O were\\O out\\O of\\O rice\\O .\\O", "opinion_tags": "They\\O were\\O very\\O abrupt\\O with\\O me\\O when\\O I\\O called\\O and\\O actually\\O claimed\\O the\\O food\\O was\\O late\\B because\\O they\\O were\\O out\\O of\\O rice\\O .\\O"}, {"uid": "220-1", "sentiment": "neutral", "target_tags": "They\\O were\\O very\\O abrupt\\O with\\O me\\O when\\O I\\O called\\O and\\O actually\\O claimed\\O the\\O food\\O was\\O late\\O because\\O they\\O were\\O out\\O of\\O rice\\B .\\O", "opinion_tags": "They\\O were\\O very\\O abrupt\\O with\\O me\\O when\\O I\\O called\\O and\\O actually\\O claimed\\O the\\O food\\O was\\O late\\O because\\O they\\O were\\O out\\B of\\I rice\\O .\\O"}], "postag": ["PRP", "VBD", "RB", "JJ", "IN", "PRP", "WRB", "PRP", "VBD", "CC", "RB", "VBD", "DT", "NN", "VBD", "JJ", "IN", "PRP", "VBD", "IN", "IN", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 9, 9, 4, 12, 12, 4, 14, 16, 16, 12, 22, 22, 22, 22, 22, 16, 4], "deprel": ["nsubj", "cop", "advmod", "root", "case", "obl", "mark", "nsubj", "advcl", "cc", "advmod", "conj", "det", "nsubj", "cop", "ccomp", "mark", "nsubj", "cop", "case", "case", "advcl", "punct"]}, {"id": "221", "sentence": "Service is extraordinary , yet not overbearing , and the decor brings a taste of trendy SoHo into Queens .", "triples": [{"uid": "221-0", "sentiment": "positive", "target_tags": "Service\\B is\\O extraordinary\\O ,\\O yet\\O not\\O overbearing\\O ,\\O and\\O the\\O decor\\O brings\\O a\\O taste\\O of\\O trendy\\O SoHo\\O into\\O Queens\\O .\\O", "opinion_tags": "Service\\O is\\O extraordinary\\B ,\\O yet\\O not\\O overbearing\\O ,\\O and\\O the\\O decor\\O brings\\O a\\O taste\\O of\\O trendy\\O SoHo\\O into\\O Queens\\O .\\O"}, {"uid": "221-1", "sentiment": "positive", "target_tags": "Service\\B is\\O extraordinary\\O ,\\O yet\\O not\\O overbearing\\O ,\\O and\\O the\\O decor\\O brings\\O a\\O taste\\O of\\O trendy\\O SoHo\\O into\\O Queens\\O .\\O", "opinion_tags": "Service\\O is\\O extraordinary\\O ,\\O yet\\O not\\B overbearing\\I ,\\O and\\O the\\O decor\\O brings\\O a\\O taste\\O of\\O trendy\\O SoHo\\O into\\O Queens\\O .\\O"}, {"uid": "221-2", "sentiment": "positive", "target_tags": "Service\\O is\\O extraordinary\\O ,\\O yet\\O not\\O overbearing\\O ,\\O and\\O the\\O decor\\B brings\\O a\\O taste\\O of\\O trendy\\O SoHo\\O into\\O Queens\\O .\\O", "opinion_tags": "Service\\O is\\O extraordinary\\O ,\\O yet\\O not\\O overbearing\\O ,\\O and\\O the\\O decor\\O brings\\O a\\O taste\\O of\\O trendy\\B SoHo\\O into\\O Queens\\O .\\O"}], "postag": ["NN", "VBZ", "JJ", ",", "CC", "RB", "JJ", ",", "CC", "DT", "NN", "VBZ", "DT", "NN", "IN", "JJ", "NNP", "IN", "NNPS", "."], "head": [3, 3, 0, 7, 7, 7, 3, 12, 12, 11, 12, 3, 14, 12, 17, 17, 14, 19, 12, 3], "deprel": ["nsubj", "cop", "root", "punct", "cc", "advmod", "conj", "punct", "cc", "det", "nsubj", "conj", "det", "obj", "case", "amod", "nmod", "case", "obl", "punct"]}, {"id": "222", "sentence": "A restaurant that does n't try to do anything except serve great food with great service in a pleasant atmosphere .", "triples": [{"uid": "222-0", "sentiment": "positive", "target_tags": "A\\O restaurant\\O that\\O does\\O n't\\O try\\O to\\O do\\O anything\\O except\\O serve\\O great\\O food\\B with\\O great\\O service\\O in\\O a\\O pleasant\\O atmosphere\\O .\\O", "opinion_tags": "A\\O restaurant\\O that\\O does\\O n't\\O try\\O to\\O do\\O anything\\O except\\O serve\\O great\\B food\\O with\\O great\\O service\\O in\\O a\\O pleasant\\O atmosphere\\O .\\O"}, {"uid": "222-1", "sentiment": "positive", "target_tags": "A\\O restaurant\\O that\\O does\\O n't\\O try\\O to\\O do\\O anything\\O except\\O serve\\O great\\O food\\O with\\O great\\O service\\B in\\O a\\O pleasant\\O atmosphere\\O .\\O", "opinion_tags": "A\\O restaurant\\O that\\O does\\O n't\\O try\\O to\\O do\\O anything\\O except\\O serve\\O great\\O food\\O with\\O great\\B service\\O in\\O a\\O pleasant\\O atmosphere\\O .\\O"}, {"uid": "222-2", "sentiment": "positive", "target_tags": "A\\O restaurant\\O that\\O does\\O n't\\O try\\O to\\O do\\O anything\\O except\\O serve\\O great\\O food\\O with\\O great\\O service\\O in\\O a\\O pleasant\\O atmosphere\\B .\\O", "opinion_tags": "A\\O restaurant\\O that\\O does\\O n't\\O try\\O to\\O do\\O anything\\O except\\O serve\\O great\\O food\\O with\\O great\\O service\\O in\\O a\\O pleasant\\B atmosphere\\O .\\O"}], "postag": ["DT", "NN", "WDT", "VBZ", "RB", "VB", "TO", "VB", "NN", "IN", "VB", "JJ", "NN", "IN", "JJ", "NN", "IN", "DT", "JJ", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 8, 6, 8, 11, 8, 13, 11, 16, 16, 11, 20, 20, 20, 11, 2], "deprel": ["det", "root", "nsubj", "aux", "advmod", "acl:relcl", "mark", "xcomp", "obj", "mark", "advcl", "amod", "obj", "case", "amod", "obl", "case", "det", "amod", "obl", "punct"]}, {"id": "223", "sentence": "Their sake martini is wonderful .", "triples": [{"uid": "223-0", "sentiment": "positive", "target_tags": "Their\\O sake\\B martini\\I is\\O wonderful\\O .\\O", "opinion_tags": "Their\\O sake\\O martini\\O is\\O wonderful\\B .\\O"}], "postag": ["PRP$", "NN", "NN", "VBZ", "JJ", "."], "head": [3, 3, 5, 5, 0, 5], "deprel": ["nmod:poss", "compound", "nsubj", "cop", "root", "punct"]}, {"id": "224", "sentence": "The service is friendly , if not the most prompt in the world , the food is great , and the prices , while not cheap , wo n't put your wallet out of commission .", "triples": [{"uid": "224-0", "sentiment": "positive", "target_tags": "The\\O service\\B is\\O friendly\\O ,\\O if\\O not\\O the\\O most\\O prompt\\O in\\O the\\O world\\O ,\\O the\\O food\\O is\\O great\\O ,\\O and\\O the\\O prices\\O ,\\O while\\O not\\O cheap\\O ,\\O wo\\O n't\\O put\\O your\\O wallet\\O out\\O of\\O commission\\O .\\O", "opinion_tags": "The\\O service\\O is\\O friendly\\B ,\\O if\\O not\\O the\\O most\\O prompt\\O in\\O the\\O world\\O ,\\O the\\O food\\O is\\O great\\O ,\\O and\\O the\\O prices\\O ,\\O while\\O not\\O cheap\\O ,\\O wo\\O n't\\O put\\O your\\O wallet\\O out\\O of\\O commission\\O .\\O"}, {"uid": "224-1", "sentiment": "positive", "target_tags": "The\\O service\\O is\\O friendly\\O ,\\O if\\O not\\O the\\O most\\O prompt\\O in\\O the\\O world\\O ,\\O the\\O food\\B is\\O great\\O ,\\O and\\O the\\O prices\\O ,\\O while\\O not\\O cheap\\O ,\\O wo\\O n't\\O put\\O your\\O wallet\\O out\\O of\\O commission\\O .\\O", "opinion_tags": "The\\O service\\O is\\O friendly\\O ,\\O if\\O not\\O the\\O most\\O prompt\\O in\\O the\\O world\\O ,\\O the\\O food\\O is\\O great\\B ,\\O and\\O the\\O prices\\O ,\\O while\\O not\\O cheap\\O ,\\O wo\\O n't\\O put\\O your\\O wallet\\O out\\O of\\O commission\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ",", "IN", "RB", "DT", "RBS", "JJ", "IN", "DT", "NN", ",", "DT", "NN", "VBZ", "JJ", ",", "CC", "DT", "NNS", ",", "IN", "RB", "JJ", ",", "MD", "RB", "VB", "PRP$", "NN", "IN", "IN", "NN", "."], "head": [2, 4, 4, 0, 4, 10, 10, 10, 10, 4, 13, 13, 10, 18, 16, 18, 18, 4, 30, 30, 22, 30, 22, 26, 26, 30, 30, 30, 30, 4, 32, 30, 35, 35, 30, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "mark", "advmod", "det", "advmod", "advcl", "case", "det", "obl", "punct", "det", "nsubj", "cop", "parataxis", "punct", "cc", "det", "nsubj", "punct", "mark", "advmod", "advcl", "punct", "aux", "advmod", "conj", "nmod:poss", "obj", "case", "case", "obl", "punct"]}, {"id": "225", "sentence": "Best drumsticks over rice and sour spicy soup in town !", "triples": [{"uid": "225-0", "sentiment": "positive", "target_tags": "Best\\O drumsticks\\B over\\I rice\\I and\\O sour\\O spicy\\O soup\\O in\\O town\\O !\\O", "opinion_tags": "Best\\B drumsticks\\O over\\O rice\\O and\\O sour\\O spicy\\O soup\\O in\\O town\\O !\\O"}, {"uid": "225-1", "sentiment": "positive", "target_tags": "Best\\O drumsticks\\O over\\O rice\\O and\\O sour\\B spicy\\I soup\\I in\\O town\\O !\\O", "opinion_tags": "Best\\B drumsticks\\O over\\O rice\\O and\\O sour\\O spicy\\O soup\\O in\\O town\\O !\\O"}], "postag": ["JJS", "NNS", "IN", "NN", "CC", "JJ", "JJ", "NN", "IN", "NN", "."], "head": [2, 0, 4, 2, 8, 8, 8, 4, 10, 2, 2], "deprel": ["amod", "root", "case", "nmod", "cc", "amod", "amod", "conj", "case", "nmod", "punct"]}, {"id": "226", "sentence": "And it all comes at a very reasonable price ( congee , noodles , and rice dishes are no more than $ 3-6 each ) .", "triples": [{"uid": "226-0", "sentiment": "positive", "target_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\O price\\B (\\O congee\\O ,\\O noodles\\O ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O", "opinion_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\B price\\O (\\O congee\\O ,\\O noodles\\O ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O"}, {"uid": "226-1", "sentiment": "neutral", "target_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\O price\\O (\\O congee\\B ,\\O noodles\\O ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O", "opinion_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\B price\\O (\\O congee\\O ,\\O noodles\\O ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O"}, {"uid": "226-2", "sentiment": "neutral", "target_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\O price\\O (\\O congee\\O ,\\O noodles\\B ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O", "opinion_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\B price\\O (\\O congee\\O ,\\O noodles\\O ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O"}, {"uid": "226-3", "sentiment": "neutral", "target_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\O price\\O (\\O congee\\O ,\\O noodles\\O ,\\O and\\O rice\\B dishes\\I are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O", "opinion_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\B price\\O (\\O congee\\O ,\\O noodles\\O ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O"}], "postag": ["CC", "PRP", "DT", "VBZ", "IN", "DT", "RB", "JJ", "NN", "-LRB-", "NN", ",", "NNS", ",", "CC", "NN", "NNS", "VBP", "RB", "JJR", "IN", "$", "CD", "DT", "-RRB-", "."], "head": [4, 4, 4, 0, 9, 9, 8, 9, 4, 11, 9, 13, 11, 17, 17, 17, 11, 22, 20, 22, 20, 4, 22, 22, 22, 4], "deprel": ["cc", "nsubj", "advmod", "root", "case", "det", "advmod", "amod", "obl", "punct", "appos", "punct", "conj", "punct", "cc", "compound", "conj", "cop", "advmod", "advmod", "fixed", "parataxis", "compound", "advmod", "punct", "punct"]}, {"id": "227", "sentence": "Overall I was impressed and will return , it 's a great QPR ( Quality to Price Ratio ) .", "triples": [{"uid": "227-0", "sentiment": "positive", "target_tags": "Overall\\O I\\O was\\O impressed\\O and\\O will\\O return\\O ,\\O it\\O 's\\O a\\O great\\O QPR\\O (\\O Quality\\O to\\O Price\\B Ratio\\O )\\O .\\O", "opinion_tags": "Overall\\O I\\O was\\O impressed\\O and\\O will\\O return\\O ,\\O it\\O 's\\O a\\O great\\B QPR\\O (\\O Quality\\O to\\O Price\\O Ratio\\O )\\O .\\O"}, {"uid": "227-1", "sentiment": "positive", "target_tags": "Overall\\O I\\O was\\O impressed\\O and\\O will\\O return\\O ,\\O it\\O 's\\O a\\O great\\O QPR\\O (\\O Quality\\B to\\O Price\\O Ratio\\O )\\O .\\O", "opinion_tags": "Overall\\O I\\O was\\O impressed\\O and\\O will\\O return\\O ,\\O it\\O 's\\O a\\O great\\B QPR\\O (\\O Quality\\O to\\O Price\\O Ratio\\O )\\O .\\O"}], "postag": ["RB", "PRP", "VBD", "JJ", "CC", "MD", "VB", ",", "PRP", "VBZ", "DT", "JJ", "NN", "-LRB-", "NN", "IN", "NN", "NN", "-RRB-", "."], "head": [4, 4, 4, 0, 7, 7, 4, 4, 13, 13, 13, 13, 4, 15, 13, 18, 18, 15, 15, 4], "deprel": ["advmod", "nsubj", "cop", "root", "cc", "aux", "conj", "punct", "nsubj", "cop", "det", "amod", "parataxis", "punct", "appos", "case", "compound", "nmod", "punct", "punct"]}, {"id": "228", "sentence": "By far the best salad I have had in a fast food restaurant .", "triples": [{"uid": "228-0", "sentiment": "positive", "target_tags": "By\\O far\\O the\\O best\\O salad\\B I\\O have\\O had\\O in\\O a\\O fast\\O food\\O restaurant\\O .\\O", "opinion_tags": "By\\O far\\O the\\O best\\B salad\\O I\\O have\\O had\\O in\\O a\\O fast\\O food\\O restaurant\\O .\\O"}], "postag": ["IN", "RB", "DT", "JJS", "NN", "PRP", "VBP", "VBN", "IN", "DT", "JJ", "NN", "NN", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 13, 13, 13, 13, 8, 5], "deprel": ["case", "obl", "det", "amod", "root", "nsubj", "aux", "acl:relcl", "case", "det", "amod", "compound", "obl", "punct"]}, {"id": "229", "sentence": "Unlike HH up the block , this place actually gives you hearty and hot bagels this town is known for .", "triples": [{"uid": "229-0", "sentiment": "positive", "target_tags": "Unlike\\O HH\\O up\\O the\\O block\\O ,\\O this\\O place\\O actually\\O gives\\O you\\O hearty\\O and\\O hot\\O bagels\\B this\\O town\\O is\\O known\\O for\\O .\\O", "opinion_tags": "Unlike\\O HH\\O up\\O the\\O block\\O ,\\O this\\O place\\O actually\\O gives\\O you\\O hearty\\B and\\O hot\\O bagels\\O this\\O town\\O is\\O known\\O for\\O .\\O"}, {"uid": "229-1", "sentiment": "positive", "target_tags": "Unlike\\O HH\\O up\\O the\\O block\\O ,\\O this\\O place\\O actually\\O gives\\O you\\O hearty\\O and\\O hot\\O bagels\\B this\\O town\\O is\\O known\\O for\\O .\\O", "opinion_tags": "Unlike\\O HH\\O up\\O the\\O block\\O ,\\O this\\O place\\O actually\\O gives\\O you\\O hearty\\O and\\O hot\\B bagels\\O this\\O town\\O is\\O known\\O for\\O .\\O"}], "postag": ["IN", "NN", "IN", "DT", "NN", ",", "DT", "NN", "RB", "VBZ", "PRP", "JJ", "CC", "JJ", "NNS", "DT", "NN", "VBZ", "VBN", "IN", "."], "head": [2, 10, 5, 5, 2, 10, 8, 10, 10, 0, 10, 15, 14, 12, 10, 17, 19, 19, 15, 19, 10], "deprel": ["case", "obl", "case", "det", "nmod", "punct", "det", "nsubj", "advmod", "root", "i<PERSON><PERSON>", "amod", "cc", "conj", "obj", "det", "nsubj:pass", "aux:pass", "acl:relcl", "obl", "punct"]}, {"id": "230", "sentence": "What generous portions !", "triples": [{"uid": "230-0", "sentiment": "positive", "target_tags": "What\\O generous\\O portions\\B !\\O", "opinion_tags": "What\\O generous\\B portions\\O !\\O"}], "postag": ["WDT", "JJ", "NNS", "."], "head": [3, 3, 0, 3], "deprel": ["det", "amod", "root", "punct"]}, {"id": "231", "sentence": "The wine the service was very good too .", "triples": [{"uid": "231-0", "sentiment": "positive", "target_tags": "The\\O wine\\B the\\O service\\O was\\O very\\O good\\O too\\O .\\O", "opinion_tags": "The\\O wine\\O the\\O service\\O was\\O very\\O good\\B too\\O .\\O"}, {"uid": "231-1", "sentiment": "positive", "target_tags": "The\\O wine\\O the\\O service\\B was\\O very\\O good\\O too\\O .\\O", "opinion_tags": "The\\O wine\\O the\\O service\\O was\\O very\\O good\\B too\\O .\\O"}], "postag": ["DT", "NN", "DT", "NN", "VBD", "RB", "JJ", "RB", "."], "head": [2, 7, 4, 7, 7, 7, 0, 7, 7], "deprel": ["det", "nsubj", "det", "nsubj", "cop", "advmod", "root", "advmod", "punct"]}, {"id": "232", "sentence": "So , the menu is written in chalk above your head and it all sounds delicious .", "triples": [{"uid": "232-0", "sentiment": "neutral", "target_tags": "So\\O ,\\O the\\O menu\\B is\\O written\\O in\\O chalk\\O above\\O your\\O head\\O and\\O it\\O all\\O sounds\\O delicious\\O .\\O", "opinion_tags": "So\\O ,\\O the\\O menu\\O is\\O written\\O in\\O chalk\\O above\\O your\\O head\\O and\\O it\\O all\\O sounds\\O delicious\\B .\\O"}], "postag": ["RB", ",", "DT", "NN", "VBZ", "VBN", "IN", "NN", "IN", "PRP$", "NN", "CC", "PRP", "DT", "VBZ", "JJ", "."], "head": [6, 6, 4, 6, 6, 0, 8, 6, 11, 11, 6, 15, 15, 13, 6, 15, 6], "deprel": ["advmod", "punct", "det", "nsubj:pass", "aux:pass", "root", "case", "obl", "case", "nmod:poss", "obl", "cc", "nsubj", "det", "conj", "xcomp", "punct"]}, {"id": "233", "sentence": "I have been there many times , and food is good and consistent .", "triples": [{"uid": "233-0", "sentiment": "positive", "target_tags": "I\\O have\\O been\\O there\\O many\\O times\\O ,\\O and\\O food\\B is\\O good\\O and\\O consistent\\O .\\O", "opinion_tags": "I\\O have\\O been\\O there\\O many\\O times\\O ,\\O and\\O food\\O is\\O good\\B and\\O consistent\\O .\\O"}, {"uid": "233-1", "sentiment": "positive", "target_tags": "I\\O have\\O been\\O there\\O many\\O times\\O ,\\O and\\O food\\B is\\O good\\O and\\O consistent\\O .\\O", "opinion_tags": "I\\O have\\O been\\O there\\O many\\O times\\O ,\\O and\\O food\\O is\\O good\\O and\\O consistent\\B .\\O"}], "postag": ["PRP", "VBP", "VBN", "RB", "JJ", "NNS", ",", "CC", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 11, 11, 11, 11, 4, 13, 11, 4], "deprel": ["nsubj", "aux", "cop", "root", "amod", "obl:tmod", "punct", "cc", "nsubj", "cop", "conj", "cc", "conj", "punct"]}, {"id": "234", "sentence": "The meat dishes were only so-so .", "triples": [{"uid": "234-0", "sentiment": "neutral", "target_tags": "The\\O meat\\B dishes\\I were\\O only\\O so-so\\O .\\O", "opinion_tags": "The\\O meat\\O dishes\\O were\\O only\\O so-so\\B .\\O"}], "postag": ["DT", "NN", "NNS", "VBD", "RB", "JJ", "."], "head": [3, 3, 6, 6, 6, 0, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "punct"]}, {"id": "235", "sentence": "The menu consisted of standard brassiere food , better then places like Balthazar etc .", "triples": [{"uid": "235-0", "sentiment": "positive", "target_tags": "The\\O menu\\O consisted\\O of\\O standard\\O brassiere\\B food\\I ,\\O better\\O then\\O places\\O like\\O Balthazar\\O etc\\O .\\O", "opinion_tags": "The\\O menu\\O consisted\\O of\\O standard\\B brassiere\\O food\\O ,\\O better\\O then\\O places\\O like\\O Balthazar\\O etc\\O .\\O"}, {"uid": "235-1", "sentiment": "positive", "target_tags": "The\\O menu\\O consisted\\O of\\O standard\\O brassiere\\B food\\I ,\\O better\\O then\\O places\\O like\\O Balthazar\\O etc\\O .\\O", "opinion_tags": "The\\O menu\\O consisted\\O of\\O standard\\O brassiere\\O food\\O ,\\O better\\B then\\O places\\O like\\O Balthazar\\O etc\\O .\\O"}], "postag": ["DT", "NN", "VBD", "IN", "JJ", "NN", "NN", ",", "JJR", "RB", "NNS", "IN", "NNP", "FW", "."], "head": [2, 3, 0, 7, 7, 7, 3, 11, 11, 11, 7, 13, 11, 13, 3], "deprel": ["det", "nsubj", "root", "case", "amod", "compound", "obl", "punct", "amod", "advmod", "conj", "case", "nmod", "advmod", "punct"]}, {"id": "236", "sentence": "The service is always bad though , do n't expect much of anything from your server , and I would not recommend bringing a date here either .", "triples": [{"uid": "236-0", "sentiment": "negative", "target_tags": "The\\O service\\B is\\O always\\O bad\\O though\\O ,\\O do\\O n't\\O expect\\O much\\O of\\O anything\\O from\\O your\\O server\\O ,\\O and\\O I\\O would\\O not\\O recommend\\O bringing\\O a\\O date\\O here\\O either\\O .\\O", "opinion_tags": "The\\O service\\O is\\O always\\O bad\\B though\\O ,\\O do\\O n't\\O expect\\O much\\O of\\O anything\\O from\\O your\\O server\\O ,\\O and\\O I\\O would\\O not\\O recommend\\O bringing\\O a\\O date\\O here\\O either\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "RB", ",", "VB", "RB", "VB", "JJ", "IN", "NN", "IN", "PRP$", "NN", ",", "CC", "PRP", "MD", "RB", "VB", "VBG", "DT", "NN", "RB", "RB", "."], "head": [2, 5, 5, 5, 0, 5, 5, 10, 10, 5, 10, 13, 11, 16, 16, 13, 22, 22, 22, 22, 22, 5, 22, 25, 23, 23, 23, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "advmod", "punct", "aux", "advmod", "parataxis", "obj", "case", "nmod", "case", "nmod:poss", "nmod", "punct", "cc", "nsubj", "aux", "advmod", "conj", "xcomp", "det", "obj", "advmod", "advmod", "punct"]}, {"id": "237", "sentence": "The sushi is also great !", "triples": [{"uid": "237-0", "sentiment": "positive", "target_tags": "The\\O sushi\\B is\\O also\\O great\\O !\\O", "opinion_tags": "The\\O sushi\\O is\\O also\\O great\\B !\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"]}, {"id": "238", "sentence": "From the complimentary chef app of a delicate butternut squash ravioli in a delicious truffle sauce to an amazing buttery and tender langostine entree to a dessert that I ca n't remember because of the fabulous Cakebread Cabernet we were drinking -- the whole evening was amazing .", "triples": [{"uid": "238-0", "sentiment": "positive", "target_tags": "From\\O the\\O complimentary\\O chef\\B app\\I of\\O a\\O delicate\\O butternut\\O squash\\O ravioli\\O in\\O a\\O delicious\\O truffle\\O sauce\\O to\\O an\\O amazing\\O buttery\\O and\\O tender\\O langostine\\O entree\\O to\\O a\\O dessert\\O that\\O I\\O ca\\O n't\\O remember\\O because\\O of\\O the\\O fabulous\\O Cakebread\\O Cabernet\\O we\\O were\\O drinking\\O --\\O the\\O whole\\O evening\\O was\\O amazing\\O .\\O", "opinion_tags": "From\\O the\\O complimentary\\B chef\\O app\\O of\\O a\\O delicate\\O butternut\\O squash\\O ravioli\\O in\\O a\\O delicious\\O truffle\\O sauce\\O to\\O an\\O amazing\\O buttery\\O and\\O tender\\O langostine\\O entree\\O to\\O a\\O dessert\\O that\\O I\\O ca\\O n't\\O remember\\O because\\O of\\O the\\O fabulous\\O Cakebread\\O Cabernet\\O we\\O were\\O drinking\\O --\\O the\\O whole\\O evening\\O was\\O amazing\\O .\\O"}, {"uid": "238-1", "sentiment": "positive", "target_tags": "From\\O the\\O complimentary\\O chef\\O app\\O of\\O a\\O delicate\\B butternut\\I squash\\I ravioli\\I in\\I a\\I delicious\\I truffle\\I sauce\\I to\\O an\\O amazing\\O buttery\\O and\\O tender\\O langostine\\O entree\\O to\\O a\\O dessert\\O that\\O I\\O ca\\O n't\\O remember\\O because\\O of\\O the\\O fabulous\\O Cakebread\\O Cabernet\\O we\\O were\\O drinking\\O --\\O the\\O whole\\O evening\\O was\\O amazing\\O .\\O", "opinion_tags": "From\\O the\\O complimentary\\O chef\\O app\\O of\\O a\\O delicate\\B butternut\\O squash\\O ravioli\\O in\\O a\\O delicious\\O truffle\\O sauce\\O to\\O an\\O amazing\\O buttery\\O and\\O tender\\O langostine\\O entree\\O to\\O a\\O dessert\\O that\\O I\\O ca\\O n't\\O remember\\O because\\O of\\O the\\O fabulous\\O Cakebread\\O Cabernet\\O we\\O were\\O drinking\\O --\\O the\\O whole\\O evening\\O was\\O amazing\\O .\\O"}, {"uid": "238-2", "sentiment": "positive", "target_tags": "From\\O the\\O complimentary\\O chef\\O app\\O of\\O a\\O delicate\\O butternut\\O squash\\O ravioli\\O in\\O a\\O delicious\\O truffle\\O sauce\\O to\\O an\\O amazing\\O buttery\\B and\\I tender\\I langostine\\I entree\\I to\\O a\\O dessert\\O that\\O I\\O ca\\O n't\\O remember\\O because\\O of\\O the\\O fabulous\\O Cakebread\\O Cabernet\\O we\\O were\\O drinking\\O --\\O the\\O whole\\O evening\\O was\\O amazing\\O .\\O", "opinion_tags": "From\\O the\\O complimentary\\O chef\\O app\\O of\\O a\\O delicate\\O butternut\\O squash\\O ravioli\\O in\\O a\\O delicious\\O truffle\\O sauce\\O to\\O an\\O amazing\\B buttery\\O and\\O tender\\O langostine\\O entree\\O to\\O a\\O dessert\\O that\\O I\\O ca\\O n't\\O remember\\O because\\O of\\O the\\O fabulous\\O Cakebread\\O Cabernet\\O we\\O were\\O drinking\\O --\\O the\\O whole\\O evening\\O was\\O amazing\\O .\\O"}, {"uid": "238-3", "sentiment": "positive", "target_tags": "From\\O the\\O complimentary\\O chef\\O app\\O of\\O a\\O delicate\\O butternut\\O squash\\O ravioli\\O in\\O a\\O delicious\\O truffle\\O sauce\\O to\\O an\\O amazing\\O buttery\\O and\\O tender\\O langostine\\O entree\\O to\\O a\\O dessert\\O that\\O I\\O ca\\O n't\\O remember\\O because\\O of\\O the\\O fabulous\\O Cakebread\\B Cabernet\\I we\\O were\\O drinking\\O --\\O the\\O whole\\O evening\\O was\\O amazing\\O .\\O", "opinion_tags": "From\\O the\\O complimentary\\O chef\\O app\\O of\\O a\\O delicate\\O butternut\\O squash\\O ravioli\\O in\\O a\\O delicious\\O truffle\\O sauce\\O to\\O an\\O amazing\\O buttery\\O and\\O tender\\O langostine\\O entree\\O to\\O a\\O dessert\\O that\\O I\\O ca\\O n't\\O remember\\O because\\O of\\O the\\O fabulous\\B Cakebread\\O Cabernet\\O we\\O were\\O drinking\\O --\\O the\\O whole\\O evening\\O was\\O amazing\\O .\\O"}], "postag": ["IN", "DT", "JJ", "NN", "NN", "IN", "DT", "JJ", "NN", "NN", "NN", "IN", "DT", "JJ", "NN", "NN", "IN", "DT", "JJ", "JJ", "CC", "JJ", "NN", "NN", "IN", "DT", "NN", "WDT", "PRP", "MD", "RB", "VB", "IN", "IN", "DT", "JJ", "NNP", "NNP", "PRP", "VBD", "VBG", ",", "DT", "JJ", "NN", "VBD", "JJ", "."], "head": [5, 5, 4, 5, 0, 11, 11, 11, 11, 11, 5, 16, 16, 16, 16, 11, 24, 24, 24, 24, 22, 20, 24, 5, 27, 27, 24, 32, 32, 32, 32, 27, 38, 33, 38, 38, 38, 32, 41, 41, 38, 47, 45, 45, 47, 47, 41, 47], "deprel": ["case", "det", "amod", "compound", "root", "case", "det", "amod", "compound", "compound", "nmod", "case", "det", "amod", "compound", "nmod", "case", "det", "amod", "amod", "cc", "conj", "compound", "nmod", "case", "det", "nmod", "obj", "nsubj", "aux", "advmod", "acl:relcl", "case", "fixed", "det", "amod", "compound", "obl", "nsubj", "aux", "acl:relcl", "punct", "det", "amod", "nsubj", "cop", "parataxis", "punct"]}, {"id": "239", "sentence": "The makhani was OK -- the korma was bland .", "triples": [{"uid": "239-0", "sentiment": "neutral", "target_tags": "The\\O makhani\\B was\\O OK\\O --\\O the\\O korma\\O was\\O bland\\O .\\O", "opinion_tags": "The\\O makhani\\O was\\O OK\\B --\\O the\\O korma\\O was\\O bland\\O .\\O"}, {"uid": "239-1", "sentiment": "negative", "target_tags": "The\\O makhani\\O was\\O OK\\O --\\O the\\O korma\\B was\\O bland\\O .\\O", "opinion_tags": "The\\O makhani\\O was\\O OK\\O --\\O the\\O korma\\O was\\O bland\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4, 7, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "parataxis", "punct"]}, {"id": "240", "sentence": "Service was very friendly .", "triples": [{"uid": "240-0", "sentiment": "positive", "target_tags": "Service\\B was\\O very\\O friendly\\O .\\O", "opinion_tags": "Service\\O was\\O very\\O friendly\\B .\\O"}], "postag": ["NN", "VBD", "RB", "JJ", "."], "head": [4, 4, 4, 0, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct"]}, {"id": "241", "sentence": "We actually left hungry and went across the street to Wo Hop at 15 Mott street for some good chinese food .", "triples": [{"uid": "241-0", "sentiment": "positive", "target_tags": "We\\O actually\\O left\\O hungry\\O and\\O went\\O across\\O the\\O street\\O to\\O Wo\\O Hop\\O at\\O 15\\O Mott\\O street\\O for\\O some\\O good\\O chinese\\B food\\I .\\O", "opinion_tags": "We\\O actually\\O left\\O hungry\\O and\\O went\\O across\\O the\\O street\\O to\\O Wo\\O Hop\\O at\\O 15\\O Mott\\O street\\O for\\O some\\O good\\B chinese\\O food\\O .\\O"}], "postag": ["PRP", "RB", "VBD", "JJ", "CC", "VBD", "IN", "DT", "NN", "IN", "NNP", "NNP", "IN", "CD", "NNP", "NN", "IN", "DT", "JJ", "JJ", "NN", "."], "head": [3, 3, 0, 3, 6, 3, 9, 9, 6, 11, 6, 11, 16, 16, 16, 6, 21, 21, 21, 21, 6, 3], "deprel": ["nsubj", "advmod", "root", "xcomp", "cc", "conj", "case", "det", "obl", "case", "obl", "flat", "case", "nummod", "compound", "obl", "case", "det", "amod", "amod", "obl", "punct"]}, {"id": "242", "sentence": "I have known about this secret for the last 13 years , <PERSON> ( the Godfather ) has continued to serve food and wine for the gods at mortal prices .", "triples": [{"uid": "242-0", "sentiment": "positive", "target_tags": "I\\O have\\O known\\O about\\O this\\O secret\\O for\\O the\\O last\\O 13\\O years\\O ,\\O Emilio\\O (\\O the\\O Godfather\\O )\\O has\\O continued\\O to\\O serve\\O food\\O and\\O wine\\O for\\O the\\O gods\\O at\\O mortal\\O prices\\B .\\O", "opinion_tags": "I\\O have\\O known\\O about\\O this\\O secret\\O for\\O the\\O last\\O 13\\O years\\O ,\\O Emilio\\O (\\O the\\O Godfather\\O )\\O has\\O continued\\O to\\O serve\\O food\\O and\\O wine\\O for\\O the\\O gods\\O at\\O mortal\\B prices\\O .\\O"}], "postag": ["PRP", "VBP", "VBN", "IN", "DT", "NN", "IN", "DT", "JJ", "CD", "NNS", ",", "NNP", "-LRB-", "DT", "NNP", "-RRB-", "VBZ", "VBN", "TO", "VB", "NN", "CC", "NN", "IN", "DT", "NNS", "IN", "JJ", "NNS", "."], "head": [3, 3, 0, 6, 6, 3, 11, 11, 11, 11, 3, 19, 19, 16, 16, 13, 16, 19, 3, 21, 19, 21, 24, 22, 27, 27, 21, 30, 30, 27, 3], "deprel": ["nsubj", "aux", "root", "case", "det", "obl", "case", "det", "amod", "nummod", "obl", "punct", "nsubj", "punct", "det", "appos", "punct", "aux", "parataxis", "mark", "xcomp", "obj", "cc", "conj", "case", "det", "obl", "case", "amod", "nmod", "punct"]}, {"id": "243", "sentence": "And the bill was outrageous .", "triples": [{"uid": "243-0", "sentiment": "negative", "target_tags": "And\\O the\\O bill\\B was\\O outrageous\\O .\\O", "opinion_tags": "And\\O the\\O bill\\O was\\O outrageous\\B .\\O"}], "postag": ["CC", "DT", "NN", "VBD", "JJ", "."], "head": [5, 3, 5, 5, 0, 5], "deprel": ["cc", "det", "nsubj", "cop", "root", "punct"]}, {"id": "244", "sentence": "Kind , attentive wait staff .", "triples": [{"uid": "244-0", "sentiment": "positive", "target_tags": "Kind\\O ,\\O attentive\\O wait\\B staff\\I .\\O", "opinion_tags": "Kind\\B ,\\O attentive\\O wait\\O staff\\O .\\O"}, {"uid": "244-1", "sentiment": "positive", "target_tags": "Kind\\O ,\\O attentive\\O wait\\B staff\\I .\\O", "opinion_tags": "Kind\\O ,\\O attentive\\B wait\\O staff\\O .\\O"}], "postag": ["JJ", ",", "JJ", "NN", "NN", "."], "head": [5, 5, 5, 5, 0, 5], "deprel": ["amod", "punct", "amod", "compound", "root", "punct"]}, {"id": "245", "sentence": "The food is authentic Italian - delicious !", "triples": [{"uid": "245-0", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O authentic\\O Italian\\O -\\O delicious\\O !\\O", "opinion_tags": "The\\O food\\O is\\O authentic\\B Italian\\I -\\O delicious\\O !\\O"}, {"uid": "245-1", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O authentic\\O Italian\\O -\\O delicious\\O !\\O", "opinion_tags": "The\\O food\\O is\\O authentic\\O Italian\\O -\\O delicious\\B !\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "JJ", ",", "JJ", "."], "head": [2, 7, 7, 5, 7, 7, 0, 7], "deprel": ["det", "nsubj", "cop", "amod", "amod", "punct", "root", "punct"]}, {"id": "246", "sentence": "It was like the servers forgot that they actually worked there and instead wanted to hang out and be cool .", "triples": [{"uid": "246-0", "sentiment": "negative", "target_tags": "It\\O was\\O like\\O the\\O servers\\B forgot\\O that\\O they\\O actually\\O worked\\O there\\O and\\O instead\\O wanted\\O to\\O hang\\O out\\O and\\O be\\O cool\\O .\\O", "opinion_tags": "It\\O was\\O like\\O the\\O servers\\O forgot\\B that\\O they\\O actually\\O worked\\O there\\O and\\O instead\\O wanted\\O to\\O hang\\O out\\O and\\O be\\O cool\\O .\\O"}], "postag": ["PRP", "VBD", "IN", "DT", "NNS", "VBD", "IN", "PRP", "RB", "VBD", "RB", "CC", "RB", "VBD", "TO", "VB", "RP", "CC", "VB", "JJ", "."], "head": [2, 0, 6, 5, 6, 2, 10, 10, 10, 6, 10, 14, 14, 10, 16, 14, 16, 20, 20, 16, 2], "deprel": ["nsubj", "root", "mark", "det", "nsubj", "advcl", "mark", "nsubj", "advmod", "ccomp", "advmod", "cc", "advmod", "conj", "mark", "xcomp", "compound:prt", "cc", "cop", "conj", "punct"]}, {"id": "247", "sentence": "The service is good and ambience is good for a date or group outing .", "triples": [{"uid": "247-0", "sentiment": "positive", "target_tags": "The\\O service\\B is\\O good\\O and\\O ambience\\O is\\O good\\O for\\O a\\O date\\O or\\O group\\O outing\\O .\\O", "opinion_tags": "The\\O service\\O is\\O good\\B and\\O ambience\\O is\\O good\\O for\\O a\\O date\\O or\\O group\\O outing\\O .\\O"}, {"uid": "247-1", "sentiment": "positive", "target_tags": "The\\O service\\O is\\O good\\O and\\O ambience\\B is\\O good\\O for\\O a\\O date\\O or\\O group\\O outing\\O .\\O", "opinion_tags": "The\\O service\\O is\\O good\\O and\\O ambience\\O is\\O good\\B for\\O a\\O date\\O or\\O group\\O outing\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "NN", "VBZ", "JJ", "IN", "DT", "NN", "CC", "NN", "NN", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 11, 11, 8, 14, 14, 11, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "nsubj", "cop", "conj", "case", "det", "obl", "cc", "compound", "conj", "punct"]}, {"id": "248", "sentence": "This restaurant is a wonderful place to go many times and it is reasonably priced .", "triples": [{"uid": "248-0", "sentiment": "positive", "target_tags": "This\\O restaurant\\O is\\O a\\O wonderful\\O place\\O to\\O go\\O many\\O times\\O and\\O it\\O is\\O reasonably\\O priced\\B .\\O", "opinion_tags": "This\\O restaurant\\O is\\O a\\O wonderful\\O place\\O to\\O go\\O many\\O times\\O and\\O it\\O is\\O reasonably\\B priced\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "JJ", "NN", "TO", "VB", "JJ", "NNS", "CC", "PRP", "VBZ", "RB", "VBN", "."], "head": [2, 6, 6, 6, 6, 0, 8, 6, 10, 8, 15, 15, 15, 15, 6, 6], "deprel": ["det", "nsubj", "cop", "det", "amod", "root", "mark", "acl", "amod", "obl:tmod", "cc", "nsubj:pass", "aux:pass", "advmod", "conj", "punct"]}, {"id": "249", "sentence": "Went on a 3 day oyster binge , with <PERSON> bringing up the closing , and I am so glad this was the place it O trip ended , because it was so great !", "triples": [{"uid": "249-0", "sentiment": "positive", "target_tags": "Went\\O on\\O a\\O 3\\O day\\O oyster\\O binge\\O ,\\O with\\O Fish\\O bringing\\O up\\O the\\O closing\\O ,\\O and\\O I\\O am\\O so\\O glad\\O this\\O was\\O the\\O place\\B it\\O O\\O trip\\O ended\\O ,\\O because\\O it\\O was\\O so\\O great\\O !\\O", "opinion_tags": "Went\\O on\\O a\\O 3\\O day\\O oyster\\O binge\\O ,\\O with\\O Fish\\O bringing\\O up\\O the\\O closing\\O ,\\O and\\O I\\O am\\O so\\O glad\\O this\\O was\\O the\\O place\\O it\\O O\\O trip\\O ended\\O ,\\O because\\O it\\O was\\O so\\O great\\B !\\O"}], "postag": ["VBD", "IN", "DT", "CD", "NN", "NN", "NN", ",", "IN", "NNP", "VBG", "RP", "DT", "NN", ",", "CC", "PRP", "VBP", "RB", "JJ", "DT", "VBD", "DT", "NN", "PRP", "IN", "NN", "VBN", ",", "IN", "PRP", "VBD", "RB", "JJ", "."], "head": [0, 7, 7, 5, 7, 7, 1, 1, 11, 11, 1, 11, 14, 11, 20, 20, 20, 20, 20, 1, 24, 24, 24, 20, 28, 27, 24, 24, 34, 34, 34, 34, 34, 24, 1], "deprel": ["root", "case", "det", "nummod", "compound", "compound", "obl", "punct", "mark", "nsubj", "advcl", "compound:prt", "det", "obj", "punct", "cc", "nsubj", "cop", "advmod", "conj", "nsubj", "cop", "det", "ccomp", "nsubj", "case", "nmod", "acl:relcl", "punct", "mark", "nsubj", "cop", "advmod", "advcl", "punct"]}, {"id": "250", "sentence": "If you are looking for a good quality , cheap eats - this is the place .", "triples": [{"uid": "250-0", "sentiment": "positive", "target_tags": "If\\O you\\O are\\O looking\\O for\\O a\\O good\\O quality\\B ,\\O cheap\\O eats\\O -\\O this\\O is\\O the\\O place\\O .\\O", "opinion_tags": "If\\O you\\O are\\O looking\\O for\\O a\\O good\\B quality\\O ,\\O cheap\\O eats\\O -\\O this\\O is\\O the\\O place\\O .\\O"}], "postag": ["IN", "PRP", "VBP", "VBG", "IN", "DT", "JJ", "NN", ",", "JJ", "NNS", ",", "DT", "VBZ", "DT", "NN", "."], "head": [4, 4, 4, 16, 8, 8, 8, 4, 11, 11, 8, 16, 16, 16, 16, 0, 16], "deprel": ["mark", "nsubj", "aux", "advcl", "case", "det", "amod", "obl", "punct", "amod", "appos", "punct", "nsubj", "cop", "det", "root", "punct"]}, {"id": "251", "sentence": "Never in my life did I think that I could be satisfied both in taste and in quantity for $ 3.00 in NYC .", "triples": [{"uid": "251-0", "sentiment": "positive", "target_tags": "Never\\O in\\O my\\O life\\O did\\O I\\O think\\O that\\O I\\O could\\O be\\O satisfied\\O both\\O in\\O taste\\B and\\O in\\O quantity\\O for\\O $\\O 3.00\\O in\\O NYC\\O .\\O", "opinion_tags": "Never\\O in\\O my\\O life\\O did\\O I\\O think\\O that\\O I\\O could\\O be\\O satisfied\\B both\\O in\\O taste\\O and\\O in\\O quantity\\O for\\O $\\O 3.00\\O in\\O NYC\\O .\\O"}, {"uid": "251-1", "sentiment": "positive", "target_tags": "Never\\O in\\O my\\O life\\O did\\O I\\O think\\O that\\O I\\O could\\O be\\O satisfied\\O both\\O in\\O taste\\O and\\O in\\O quantity\\B for\\O $\\O 3.00\\O in\\O NYC\\O .\\O", "opinion_tags": "Never\\O in\\O my\\O life\\O did\\O I\\O think\\O that\\O I\\O could\\O be\\O satisfied\\B both\\O in\\O taste\\O and\\O in\\O quantity\\O for\\O $\\O 3.00\\O in\\O NYC\\O .\\O"}], "postag": ["RB", "IN", "PRP$", "NN", "VBD", "PRP", "VB", "IN", "PRP", "MD", "VB", "JJ", "CC", "IN", "NN", "CC", "IN", "NN", "IN", "$", "CD", "IN", "NNP", "."], "head": [7, 4, 4, 7, 7, 7, 0, 12, 12, 12, 12, 7, 15, 15, 12, 18, 18, 12, 20, 18, 20, 23, 20, 7], "deprel": ["advmod", "case", "nmod:poss", "obl", "aux", "nsubj", "root", "mark", "nsubj", "aux", "cop", "ccomp", "cc:preconj", "case", "obl", "cc", "case", "conj", "case", "nmod", "nummod", "case", "nmod", "punct"]}, {"id": "252", "sentence": "The main course had an average portion , and was decent overall .", "triples": [{"uid": "252-0", "sentiment": "positive", "target_tags": "The\\O main\\B course\\I had\\O an\\O average\\O portion\\O ,\\O and\\O was\\O decent\\O overall\\O .\\O", "opinion_tags": "The\\O main\\O course\\O had\\O an\\O average\\O portion\\O ,\\O and\\O was\\O decent\\B overall\\O .\\O"}, {"uid": "252-1", "sentiment": "neutral", "target_tags": "The\\O main\\O course\\O had\\O an\\O average\\O portion\\B ,\\O and\\O was\\O decent\\O overall\\O .\\O", "opinion_tags": "The\\O main\\O course\\O had\\O an\\O average\\B portion\\O ,\\O and\\O was\\O decent\\O overall\\O .\\O"}], "postag": ["DT", "JJ", "NN", "VBD", "DT", "JJ", "NN", ",", "CC", "VBD", "JJ", "RB", "."], "head": [3, 3, 4, 0, 7, 7, 4, 11, 11, 11, 4, 11, 4], "deprel": ["det", "amod", "nsubj", "root", "det", "amod", "obj", "punct", "cc", "cop", "conj", "advmod", "punct"]}, {"id": "253", "sentence": "20 minutes for our reservation but it gave us time to have a few cocktails and enjoy our surroundings and each other .", "triples": [{"uid": "253-0", "sentiment": "positive", "target_tags": "20\\O minutes\\O for\\O our\\O reservation\\O but\\O it\\O gave\\O us\\O time\\O to\\O have\\O a\\O few\\O cocktails\\O and\\O enjoy\\O our\\O surroundings\\B and\\O each\\O other\\O .\\O", "opinion_tags": "20\\O minutes\\O for\\O our\\O reservation\\O but\\O it\\O gave\\O us\\O time\\O to\\O have\\O a\\O few\\O cocktails\\O and\\O enjoy\\B our\\O surroundings\\O and\\O each\\O other\\O .\\O"}], "postag": ["CD", "NNS", "IN", "PRP$", "NN", "CC", "PRP", "VBD", "PRP", "NN", "TO", "VB", "DT", "JJ", "NNS", "CC", "VB", "PRP$", "NNS", "CC", "DT", "JJ", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 8, 8, 12, 10, 15, 15, 12, 17, 12, 19, 17, 22, 22, 19, 2], "deprel": ["nummod", "root", "case", "nmod:poss", "nmod", "cc", "nsubj", "conj", "i<PERSON><PERSON>", "obj", "mark", "acl", "det", "amod", "obj", "cc", "conj", "nmod:poss", "obj", "cc", "det", "conj", "punct"]}, {"id": "254", "sentence": "<PERSON><PERSON><PERSON> is great , very attentive .", "triples": [{"uid": "254-0", "sentiment": "positive", "target_tags": "Waitstaff\\B is\\O great\\O ,\\O very\\O attentive\\O .\\O", "opinion_tags": "Waitstaff\\O is\\O great\\B ,\\O very\\O attentive\\O .\\O"}, {"uid": "254-1", "sentiment": "positive", "target_tags": "Waitstaff\\B is\\O great\\O ,\\O very\\O attentive\\O .\\O", "opinion_tags": "Waitstaff\\O is\\O great\\O ,\\O very\\O attentive\\B .\\O"}], "postag": ["NN", "VBZ", "JJ", ",", "RB", "JJ", "."], "head": [3, 3, 0, 3, 6, 3, 3], "deprel": ["nsubj", "cop", "root", "punct", "advmod", "conj", "punct"]}, {"id": "255", "sentence": "Coming from Boston this place is like <PERSON> 's Pizza in Kendall Square in Cambridge ( although they have more funky toppings ! )", "triples": [{"uid": "255-0", "sentiment": "positive", "target_tags": "Coming\\O from\\O Boston\\O this\\O place\\O is\\O like\\O Emma\\O 's\\O Pizza\\O in\\O Kendall\\O Square\\O in\\O Cambridge\\O (\\O although\\O they\\O have\\O more\\O funky\\O toppings\\B !\\O )\\O", "opinion_tags": "Coming\\O from\\O Boston\\O this\\O place\\O is\\O like\\O Emma\\O 's\\O Pizza\\O in\\O Kendall\\O Square\\O in\\O Cambridge\\O (\\O although\\O they\\O have\\O more\\O funky\\B toppings\\O !\\O )\\O"}], "postag": ["VBG", "IN", "NNP", "DT", "NN", "VBZ", "IN", "NNP", "POS", "NNP", "IN", "NNP", "NNP", "IN", "NNP", "-LRB-", "IN", "PRP", "VBP", "JJR", "JJ", "NNS", ".", "-RRB-"], "head": [10, 3, 1, 5, 10, 10, 10, 10, 8, 0, 13, 13, 10, 15, 10, 19, 19, 19, 10, 22, 22, 19, 10, 19], "deprel": ["csubj", "case", "obl", "det", "nsubj", "cop", "case", "nmod:poss", "case", "root", "case", "compound", "nmod", "case", "nmod", "punct", "mark", "nsubj", "advcl", "amod", "amod", "obj", "punct", "punct"]}, {"id": "256", "sentence": "The space is nice but when we order our drink we were in for a surprise .", "triples": [{"uid": "256-0", "sentiment": "positive", "target_tags": "The\\O space\\B is\\O nice\\O but\\O when\\O we\\O order\\O our\\O drink\\O we\\O were\\O in\\O for\\O a\\O surprise\\O .\\O", "opinion_tags": "The\\O space\\O is\\O nice\\B but\\O when\\O we\\O order\\O our\\O drink\\O we\\O were\\O in\\O for\\O a\\O surprise\\O .\\O"}, {"uid": "256-1", "sentiment": "negative", "target_tags": "The\\O space\\O is\\O nice\\O but\\O when\\O we\\O order\\O our\\O drink\\B we\\O were\\O in\\O for\\O a\\O surprise\\O .\\O", "opinion_tags": "The\\O space\\O is\\O nice\\O but\\O when\\O we\\O order\\O our\\O drink\\O we\\O were\\O in\\O for\\O a\\O surprise\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "WRB", "PRP", "VBP", "PRP$", "NN", "PRP", "VBD", "RB", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 16, 8, 8, 16, 10, 8, 16, 16, 16, 16, 16, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "mark", "nsubj", "advcl", "nmod:poss", "obj", "nsubj", "cop", "case", "case", "det", "conj", "punct"]}, {"id": "257", "sentence": "The price very reasonable .", "triples": [{"uid": "257-0", "sentiment": "positive", "target_tags": "The\\O price\\B very\\O reasonable\\O .\\O", "opinion_tags": "The\\O price\\O very\\O reasonable\\B .\\O"}], "postag": ["DT", "NN", "RB", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "advmod", "root", "punct"]}, {"id": "258", "sentence": "The waiters are sweet , the food is tasty and the bill is never too large .", "triples": [{"uid": "258-0", "sentiment": "positive", "target_tags": "The\\O waiters\\B are\\O sweet\\O ,\\O the\\O food\\O is\\O tasty\\O and\\O the\\O bill\\O is\\O never\\O too\\O large\\O .\\O", "opinion_tags": "The\\O waiters\\O are\\O sweet\\B ,\\O the\\O food\\O is\\O tasty\\O and\\O the\\O bill\\O is\\O never\\O too\\O large\\O .\\O"}, {"uid": "258-1", "sentiment": "positive", "target_tags": "The\\O waiters\\O are\\O sweet\\O ,\\O the\\O food\\B is\\O tasty\\O and\\O the\\O bill\\O is\\O never\\O too\\O large\\O .\\O", "opinion_tags": "The\\O waiters\\O are\\O sweet\\O ,\\O the\\O food\\O is\\O tasty\\B and\\O the\\O bill\\O is\\O never\\O too\\O large\\O .\\O"}, {"uid": "258-2", "sentiment": "positive", "target_tags": "The\\O waiters\\O are\\O sweet\\O ,\\O the\\O food\\O is\\O tasty\\O and\\O the\\O bill\\B is\\O never\\O too\\O large\\O .\\O", "opinion_tags": "The\\O waiters\\O are\\O sweet\\O ,\\O the\\O food\\O is\\O tasty\\O and\\O the\\O bill\\O is\\O never\\B too\\I large\\I .\\O"}], "postag": ["DT", "NNS", "VBP", "JJ", ",", "DT", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "RB", "RB", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 16, 12, 16, 16, 16, 16, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "conj", "cc", "det", "nsubj", "cop", "advmod", "advmod", "conj", "punct"]}, {"id": "259", "sentence": "The flavors robust and subtle .", "triples": [{"uid": "259-0", "sentiment": "positive", "target_tags": "The\\O flavors\\B robust\\O and\\O subtle\\O .\\O", "opinion_tags": "The\\O flavors\\O robust\\B and\\O subtle\\O .\\O"}, {"uid": "259-1", "sentiment": "positive", "target_tags": "The\\O flavors\\B robust\\O and\\O subtle\\O .\\O", "opinion_tags": "The\\O flavors\\O robust\\O and\\O subtle\\B .\\O"}], "postag": ["DT", "NNS", "JJ", "CC", "JJ", "."], "head": [2, 0, 2, 5, 3, 3], "deprel": ["det", "root", "amod", "cc", "conj", "punct"]}, {"id": "260", "sentence": "This is such a lovely , peaceful place to eat outside .", "triples": [{"uid": "260-0", "sentiment": "positive", "target_tags": "This\\O is\\O such\\O a\\O lovely\\O ,\\O peaceful\\O place\\B to\\O eat\\O outside\\O .\\O", "opinion_tags": "This\\O is\\O such\\O a\\O lovely\\B ,\\O peaceful\\O place\\O to\\O eat\\O outside\\O .\\O"}, {"uid": "260-1", "sentiment": "positive", "target_tags": "This\\O is\\O such\\O a\\O lovely\\O ,\\O peaceful\\O place\\B to\\O eat\\O outside\\O .\\O", "opinion_tags": "This\\O is\\O such\\O a\\O lovely\\O ,\\O peaceful\\B place\\O to\\O eat\\O outside\\O .\\O"}], "postag": ["DT", "VBZ", "PDT", "DT", "JJ", ",", "JJ", "NN", "TO", "VB", "RB", "."], "head": [8, 8, 8, 8, 8, 8, 8, 0, 10, 8, 10, 8], "deprel": ["nsubj", "cop", "det:predet", "det", "amod", "punct", "amod", "root", "mark", "acl", "advmod", "punct"]}, {"id": "261", "sentence": "Oh , but wait , we were out of drinks ( which were also delightfully overpriced ) .", "triples": [{"uid": "261-0", "sentiment": "negative", "target_tags": "Oh\\O ,\\O but\\O wait\\O ,\\O we\\O were\\O out\\O of\\O drinks\\B (\\O which\\O were\\O also\\O delightfully\\O overpriced\\O )\\O .\\O", "opinion_tags": "Oh\\O ,\\O but\\O wait\\O ,\\O we\\O were\\O out\\O of\\O drinks\\O (\\O which\\O were\\O also\\O delightfully\\O overpriced\\B )\\O .\\O"}], "postag": ["UH", ",", "CC", "VB", ",", "PRP", "VBD", "IN", "IN", "NNS", "-LRB-", "WDT", "VBD", "RB", "RB", "JJ", "-RRB-", "."], "head": [4, 4, 4, 0, 10, 10, 10, 10, 10, 4, 16, 16, 16, 16, 16, 10, 16, 4], "deprel": ["discourse", "punct", "cc", "root", "punct", "nsubj", "cop", "case", "case", "ccomp", "punct", "nsubj", "cop", "advmod", "advmod", "parataxis", "punct", "punct"]}, {"id": "262", "sentence": "The service is fantastic .", "triples": [{"uid": "262-0", "sentiment": "positive", "target_tags": "The\\O service\\B is\\O fantastic\\O .\\O", "opinion_tags": "The\\O service\\O is\\O fantastic\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"]}, {"id": "263", "sentence": "The garlic mashed potatoes are hands down the best in the city !", "triples": [{"uid": "263-0", "sentiment": "positive", "target_tags": "The\\O garlic\\B mashed\\I potatoes\\I are\\O hands\\O down\\O the\\O best\\O in\\O the\\O city\\O !\\O", "opinion_tags": "The\\O garlic\\O mashed\\O potatoes\\O are\\O hands\\O down\\O the\\O best\\B in\\O the\\O city\\O !\\O"}], "postag": ["DT", "NN", "VBD", "NNS", "VBP", "NNS", "IN", "DT", "JJS", "IN", "DT", "NN", "."], "head": [4, 4, 4, 9, 9, 9, 9, 9, 0, 12, 12, 9, 9], "deprel": ["det", "compound", "amod", "nsubj", "cop", "obl:npmod", "case", "det", "root", "case", "det", "obl", "punct"]}, {"id": "264", "sentence": "The lamb meat was under-cooked and EXTRMELY CHEWY .", "triples": [{"uid": "264-0", "sentiment": "negative", "target_tags": "The\\O lamb\\B meat\\I was\\O under-cooked\\O and\\O EXTRMELY\\O CHEWY\\O .\\O", "opinion_tags": "The\\O lamb\\O meat\\O was\\O under-cooked\\B and\\O EXTRMELY\\O CHEWY\\O .\\O"}], "postag": ["DT", "NN", "NN", "VBD", "JJ", "CC", "RB", "JJ", "."], "head": [3, 3, 5, 5, 0, 8, 8, 5, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "cc", "advmod", "conj", "punct"]}, {"id": "265", "sentence": "Over time , the food quality has decreased substantially , it is a lot less crowded than it used to , and the service must definitely be part of the reason .", "triples": [{"uid": "265-0", "sentiment": "negative", "target_tags": "Over\\O time\\O ,\\O the\\O food\\B quality\\I has\\O decreased\\O substantially\\O ,\\O it\\O is\\O a\\O lot\\O less\\O crowded\\O than\\O it\\O used\\O to\\O ,\\O and\\O the\\O service\\O must\\O definitely\\O be\\O part\\O of\\O the\\O reason\\O .\\O", "opinion_tags": "Over\\O time\\O ,\\O the\\O food\\O quality\\O has\\O decreased\\B substantially\\O ,\\O it\\O is\\O a\\O lot\\O less\\O crowded\\O than\\O it\\O used\\O to\\O ,\\O and\\O the\\O service\\O must\\O definitely\\O be\\O part\\O of\\O the\\O reason\\O .\\O"}], "postag": ["IN", "NN", ",", "DT", "NN", "NN", "VBZ", "VBN", "RB", ",", "PRP", "VBZ", "DT", "NN", "RBR", "JJ", "IN", "PRP", "VBD", "IN", ",", "CC", "DT", "NN", "MD", "RB", "VB", "NN", "IN", "DT", "NN", "."], "head": [2, 8, 8, 6, 6, 8, 8, 0, 8, 8, 16, 16, 14, 15, 16, 8, 19, 19, 16, 19, 28, 28, 24, 28, 28, 28, 28, 8, 31, 31, 28, 8], "deprel": ["case", "obl", "punct", "det", "compound", "nsubj", "aux", "root", "advmod", "punct", "nsubj", "cop", "det", "obl:npmod", "advmod", "parataxis", "mark", "nsubj", "advcl", "obl", "punct", "cc", "det", "nsubj", "aux", "advmod", "cop", "conj", "case", "det", "nmod", "punct"]}, {"id": "266", "sentence": "Killer Sushi !", "triples": [{"uid": "266-0", "sentiment": "positive", "target_tags": "Killer\\O Sushi\\B !\\O", "opinion_tags": "Killer\\B Sushi\\O !\\O"}], "postag": ["NNP", "NN", "."], "head": [2, 0, 2], "deprel": ["compound", "root", "punct"]}, {"id": "267", "sentence": "Plus they made a perfect martini .", "triples": [{"uid": "267-0", "sentiment": "positive", "target_tags": "Plus\\O they\\O made\\O a\\O perfect\\O martini\\B .\\O", "opinion_tags": "Plus\\O they\\O made\\O a\\O perfect\\B martini\\O .\\O"}], "postag": ["CC", "PRP", "VBD", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 3], "deprel": ["cc", "nsubj", "root", "det", "amod", "obj", "punct"]}, {"id": "268", "sentence": "Furthermore , the rice had no seasoning , so the sushi was bland and disgusting .", "triples": [{"uid": "268-0", "sentiment": "negative", "target_tags": "Furthermore\\O ,\\O the\\O rice\\B had\\O no\\O seasoning\\O ,\\O so\\O the\\O sushi\\O was\\O bland\\O and\\O disgusting\\O .\\O", "opinion_tags": "Furthermore\\O ,\\O the\\O rice\\O had\\O no\\B seasoning\\I ,\\O so\\O the\\O sushi\\O was\\O bland\\O and\\O disgusting\\O .\\O"}, {"uid": "268-1", "sentiment": "negative", "target_tags": "Furthermore\\O ,\\O the\\O rice\\O had\\O no\\O seasoning\\O ,\\O so\\O the\\O sushi\\B was\\O bland\\O and\\O disgusting\\O .\\O", "opinion_tags": "Furthermore\\O ,\\O the\\O rice\\O had\\O no\\O seasoning\\O ,\\O so\\O the\\O sushi\\O was\\O bland\\B and\\O disgusting\\O .\\O"}, {"uid": "268-2", "sentiment": "negative", "target_tags": "Furthermore\\O ,\\O the\\O rice\\O had\\O no\\O seasoning\\O ,\\O so\\O the\\O sushi\\B was\\O bland\\O and\\O disgusting\\O .\\O", "opinion_tags": "Furthermore\\O ,\\O the\\O rice\\O had\\O no\\O seasoning\\O ,\\O so\\O the\\O sushi\\O was\\O bland\\O and\\O disgusting\\B .\\O"}, {"uid": "268-3", "sentiment": "negative", "target_tags": "Furthermore\\O ,\\O the\\O rice\\O had\\O no\\O seasoning\\B ,\\O so\\O the\\O sushi\\O was\\O bland\\O and\\O disgusting\\O .\\O", "opinion_tags": "Furthermore\\O ,\\O the\\O rice\\O had\\O no\\B seasoning\\O ,\\O so\\O the\\O sushi\\O was\\O bland\\O and\\O disgusting\\O .\\O"}], "postag": ["RB", ",", "DT", "NN", "VBD", "DT", "NN", ",", "RB", "DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [5, 5, 4, 5, 0, 7, 5, 13, 13, 11, 13, 13, 5, 15, 13, 5], "deprel": ["advmod", "punct", "det", "nsubj", "root", "det", "obj", "punct", "advmod", "det", "nsubj", "cop", "parataxis", "cc", "conj", "punct"]}, {"id": "269", "sentence": "<PERSON><PERSON> and her staff are the best crew you can find serving you .", "triples": [{"uid": "269-0", "sentiment": "positive", "target_tags": "Winnie\\O and\\O her\\O staff\\O are\\O the\\O best\\O crew\\B you\\O can\\O find\\O serving\\O you\\O .\\O", "opinion_tags": "Winnie\\O and\\O her\\O staff\\O are\\O the\\O best\\B crew\\O you\\O can\\O find\\O serving\\O you\\O .\\O"}, {"uid": "269-1", "sentiment": "positive", "target_tags": "Winnie\\O and\\O her\\O staff\\B are\\O the\\O best\\O crew\\O you\\O can\\O find\\O serving\\O you\\O .\\O", "opinion_tags": "Winnie\\O and\\O her\\O staff\\O are\\O the\\O best\\B crew\\O you\\O can\\O find\\O serving\\O you\\O .\\O"}], "postag": ["NNP", "CC", "PRP$", "NN", "VBP", "DT", "JJS", "NN", "PRP", "MD", "VB", "VBG", "PRP", "."], "head": [8, 4, 4, 1, 8, 8, 8, 0, 11, 11, 8, 11, 12, 8], "deprel": ["nsubj", "cc", "nmod:poss", "conj", "cop", "det", "amod", "root", "nsubj", "aux", "acl:relcl", "xcomp", "obj", "punct"]}, {"id": "270", "sentence": "If it is n't for the food ( A+++ ) , it must be the service or the ambience .", "triples": [{"uid": "270-0", "sentiment": "positive", "target_tags": "If\\O it\\O is\\O n't\\O for\\O the\\O food\\B (\\O A+++\\O )\\O ,\\O it\\O must\\O be\\O the\\O service\\O or\\O the\\O ambience\\O .\\O", "opinion_tags": "If\\O it\\O is\\O n't\\O for\\O the\\O food\\O (\\B A+++\\I )\\I ,\\O it\\O must\\O be\\O the\\O service\\O or\\O the\\O ambience\\O .\\O"}], "postag": ["IN", "PRP", "VBZ", "RB", "IN", "DT", "NN", "-LRB-", "NNP", "-RRB-", ",", "PRP", "MD", "VB", "DT", "NN", "CC", "DT", "NN", "."], "head": [7, 7, 7, 7, 7, 7, 16, 9, 7, 9, 16, 16, 16, 16, 16, 0, 19, 19, 16, 16], "deprel": ["mark", "nsubj", "cop", "advmod", "case", "det", "advcl", "punct", "appos", "punct", "punct", "nsubj", "aux", "cop", "det", "root", "cc", "det", "conj", "punct"]}, {"id": "271", "sentence": "This place has the best Chinese style BBQ ribs in the city .", "triples": [{"uid": "271-0", "sentiment": "positive", "target_tags": "This\\O place\\O has\\O the\\O best\\O Chinese\\O style\\O BBQ\\B ribs\\I in\\O the\\O city\\O .\\O", "opinion_tags": "This\\O place\\O has\\O the\\O best\\B Chinese\\O style\\O BBQ\\O ribs\\O in\\O the\\O city\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "JJS", "JJ", "NN", "NN", "NNS", "IN", "DT", "NN", "."], "head": [2, 3, 0, 9, 9, 9, 9, 9, 3, 12, 12, 9, 3], "deprel": ["det", "nsubj", "root", "det", "amod", "amod", "compound", "compound", "obj", "case", "det", "nmod", "punct"]}, {"id": "272", "sentence": "$ 6 and there is much tasty food , all of it fresh and continually refilled .", "triples": [{"uid": "272-0", "sentiment": "positive", "target_tags": "$\\O 6\\O and\\O there\\O is\\O much\\O tasty\\O food\\B ,\\O all\\O of\\O it\\O fresh\\O and\\O continually\\O refilled\\O .\\O", "opinion_tags": "$\\O 6\\O and\\O there\\O is\\O much\\O tasty\\B food\\O ,\\O all\\O of\\O it\\O fresh\\O and\\O continually\\O refilled\\O .\\O"}, {"uid": "272-1", "sentiment": "positive", "target_tags": "$\\O 6\\O and\\O there\\O is\\O much\\O tasty\\O food\\B ,\\O all\\O of\\O it\\O fresh\\O and\\O continually\\O refilled\\O .\\O", "opinion_tags": "$\\O 6\\O and\\O there\\O is\\O much\\O tasty\\O food\\O ,\\O all\\O of\\O it\\O fresh\\B and\\O continually\\O refilled\\O .\\O"}, {"uid": "272-2", "sentiment": "positive", "target_tags": "$\\O 6\\O and\\O there\\O is\\O much\\O tasty\\O food\\B ,\\O all\\O of\\O it\\O fresh\\O and\\O continually\\O refilled\\O .\\O", "opinion_tags": "$\\O 6\\O and\\O there\\O is\\O much\\O tasty\\O food\\O ,\\O all\\O of\\O it\\O fresh\\O and\\O continually\\O refilled\\B .\\O"}], "postag": ["$", "CD", "CC", "EX", "VBZ", "RB", "JJ", "NN", ",", "DT", "IN", "PRP", "JJ", "CC", "RB", "VBN", "."], "head": [0, 1, 5, 5, 1, 7, 8, 5, 8, 13, 12, 10, 8, 16, 16, 13, 1], "deprel": ["root", "nummod", "cc", "expl", "conj", "advmod", "amod", "nsubj", "punct", "nsubj", "case", "nmod", "amod", "cc", "advmod", "conj", "punct"]}, {"id": "273", "sentence": "Just straight up cheap , good food .", "triples": [{"uid": "273-0", "sentiment": "positive", "target_tags": "Just\\O straight\\O up\\O cheap\\O ,\\O good\\O food\\B .\\O", "opinion_tags": "Just\\O straight\\O up\\O cheap\\B ,\\O good\\O food\\O .\\O"}, {"uid": "273-1", "sentiment": "positive", "target_tags": "Just\\O straight\\O up\\O cheap\\O ,\\O good\\O food\\B .\\O", "opinion_tags": "Just\\O straight\\O up\\O cheap\\O ,\\O good\\B food\\O .\\O"}], "postag": ["RB", "RB", "RP", "JJ", ",", "JJ", "NN", "."], "head": [7, 7, 7, 7, 7, 7, 0, 7], "deprel": ["advmod", "advmod", "advmod", "amod", "punct", "amod", "root", "punct"]}, {"id": "274", "sentence": "Seating is ok even though sometimes there 's alot of people .", "triples": [{"uid": "274-0", "sentiment": "neutral", "target_tags": "Seating\\B is\\O ok\\O even\\O though\\O sometimes\\O there\\O 's\\O alot\\O of\\O people\\O .\\O", "opinion_tags": "Seating\\O is\\O ok\\B even\\O though\\O sometimes\\O there\\O 's\\O alot\\O of\\O people\\O .\\O"}], "postag": ["NN", "VBZ", "JJ", "RB", "IN", "RB", "EX", "VBZ", "NN", "IN", "NNS", "."], "head": [3, 3, 0, 8, 8, 8, 8, 3, 8, 11, 9, 3], "deprel": ["nsubj", "cop", "root", "advmod", "mark", "advmod", "expl", "advcl", "nsubj", "case", "nmod", "punct"]}, {"id": "275", "sentence": "As a long-time patron of <PERSON><PERSON><PERSON> 's , I always figured that I had found the best late night food spot in the city .", "triples": [{"uid": "275-0", "sentiment": "positive", "target_tags": "As\\O a\\O long-time\\O patron\\O of\\O Mamoun\\O 's\\O ,\\O I\\O always\\O figured\\O that\\O I\\O had\\O found\\O the\\O best\\O late\\O night\\O food\\B spot\\I in\\O the\\O city\\O .\\O", "opinion_tags": "As\\O a\\O long-time\\O patron\\O of\\O Mamoun\\O 's\\O ,\\O I\\O always\\O figured\\O that\\O I\\O had\\O found\\O the\\O best\\B late\\O night\\O food\\O spot\\O in\\O the\\O city\\O .\\O"}], "postag": ["IN", "DT", "JJ", "NN", "IN", "NNP", "POS", ",", "PRP", "RB", "VBD", "IN", "PRP", "VBD", "VBN", "DT", "JJS", "JJ", "NN", "NN", "NN", "IN", "DT", "NN", "."], "head": [4, 4, 4, 11, 6, 4, 6, 11, 11, 11, 0, 15, 15, 15, 11, 21, 21, 19, 21, 21, 15, 24, 24, 15, 11], "deprel": ["case", "det", "amod", "obl", "case", "nmod", "case", "punct", "nsubj", "advmod", "root", "mark", "nsubj", "aux", "ccomp", "det", "amod", "amod", "compound", "compound", "obj", "case", "det", "obl", "punct"]}, {"id": "276", "sentence": "I like the somosas , chai , and the chole , but the dhosas and dhal were kinda disappointing .", "triples": [{"uid": "276-0", "sentiment": "positive", "target_tags": "I\\O like\\O the\\O somosas\\B ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O", "opinion_tags": "I\\O like\\B the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O"}, {"uid": "276-1", "sentiment": "positive", "target_tags": "I\\O like\\O the\\O somosas\\O ,\\O chai\\B ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O", "opinion_tags": "I\\O like\\B the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O"}, {"uid": "276-2", "sentiment": "positive", "target_tags": "I\\O like\\O the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\B ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O", "opinion_tags": "I\\O like\\B the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O"}, {"uid": "276-3", "sentiment": "negative", "target_tags": "I\\O like\\O the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\B and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O", "opinion_tags": "I\\O like\\O the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\B .\\O"}, {"uid": "276-4", "sentiment": "negative", "target_tags": "I\\O like\\O the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\B were\\O kinda\\O disappointing\\O .\\O", "opinion_tags": "I\\O like\\O the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\B .\\O"}], "postag": ["PRP", "VBP", "DT", "NNS", ",", "NN", ",", "CC", "DT", "NN", ",", "CC", "DT", "NN", "CC", "NN", "VBD", "RB", "JJ", "."], "head": [2, 0, 4, 2, 6, 4, 10, 10, 10, 4, 19, 19, 14, 19, 16, 14, 19, 19, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "punct", "conj", "punct", "cc", "det", "conj", "punct", "cc", "det", "nsubj", "cc", "conj", "cop", "advmod", "conj", "punct"]}, {"id": "277", "sentence": "Yeah , sometimes the service can be slow .", "triples": [{"uid": "277-0", "sentiment": "negative", "target_tags": "Yeah\\O ,\\O sometimes\\O the\\O service\\B can\\O be\\O slow\\O .\\O", "opinion_tags": "Yeah\\O ,\\O sometimes\\O the\\O service\\O can\\O be\\O slow\\B .\\O"}], "postag": ["UH", ",", "RB", "DT", "NN", "MD", "VB", "JJ", "."], "head": [8, 8, 8, 5, 8, 8, 8, 0, 8], "deprel": ["discourse", "punct", "advmod", "det", "nsubj", "aux", "cop", "root", "punct"]}, {"id": "278", "sentence": "The service was attentive without being overbearing and each dish we tried was wonderful from the spring rolls to the cod with pineapple tempura .", "triples": [{"uid": "278-0", "sentiment": "positive", "target_tags": "The\\O service\\B was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\O from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O", "opinion_tags": "The\\O service\\O was\\O attentive\\B without\\I being\\I overbearing\\I and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\O from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O"}, {"uid": "278-1", "sentiment": "positive", "target_tags": "The\\O service\\O was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\B we\\O tried\\O was\\O wonderful\\O from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O", "opinion_tags": "The\\O service\\O was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\B from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O"}, {"uid": "278-2", "sentiment": "positive", "target_tags": "The\\O service\\O was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\O from\\O the\\O spring\\B rolls\\I to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O", "opinion_tags": "The\\O service\\O was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\B from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O"}, {"uid": "278-3", "sentiment": "positive", "target_tags": "The\\O service\\O was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\O from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\B with\\I pineapple\\I tempura\\I .\\O", "opinion_tags": "The\\O service\\O was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\B from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "IN", "VBG", "JJ", "CC", "DT", "NN", "PRP", "VBD", "VBD", "JJ", "IN", "DT", "NN", "NNS", "IN", "DT", "NN", "IN", "NN", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 14, 10, 14, 12, 10, 14, 4, 18, 18, 18, 14, 21, 21, 14, 24, 24, 21, 4], "deprel": ["det", "nsubj", "cop", "root", "mark", "cop", "advcl", "cc", "det", "nsubj", "nsubj", "acl:relcl", "cop", "conj", "case", "det", "compound", "obl", "case", "det", "obl", "case", "compound", "nmod", "punct"]}, {"id": "279", "sentence": "I would not have been so disappointed with the portions if the qualities were good enough to make up for it , but they were not !", "triples": [{"uid": "279-0", "sentiment": "negative", "target_tags": "I\\O would\\O not\\O have\\O been\\O so\\O disappointed\\O with\\O the\\O portions\\B if\\O the\\O qualities\\O were\\O good\\O enough\\O to\\O make\\O up\\O for\\O it\\O ,\\O but\\O they\\O were\\O not\\O !\\O", "opinion_tags": "I\\O would\\O not\\O have\\O been\\O so\\O disappointed\\B with\\O the\\O portions\\O if\\O the\\O qualities\\O were\\O good\\O enough\\O to\\O make\\O up\\O for\\O it\\O ,\\O but\\O they\\O were\\O not\\O !\\O"}, {"uid": "279-1", "sentiment": "negative", "target_tags": "I\\O would\\O not\\O have\\O been\\O so\\O disappointed\\O with\\O the\\O portions\\O if\\O the\\O qualities\\B were\\O good\\O enough\\O to\\O make\\O up\\O for\\O it\\O ,\\O but\\O they\\O were\\O not\\O !\\O", "opinion_tags": "I\\O would\\O not\\O have\\O been\\O so\\O disappointed\\O with\\O the\\O portions\\O if\\O the\\O qualities\\O were\\O good\\B enough\\O to\\O make\\O up\\O for\\O it\\O ,\\O but\\O they\\O were\\O not\\O !\\O"}], "postag": ["PRP", "MD", "RB", "VB", "VBN", "RB", "JJ", "IN", "DT", "NNS", "IN", "DT", "NNS", "VBD", "JJ", "RB", "TO", "VB", "RP", "IN", "PRP", ",", "CC", "PRP", "VBD", "RB", "."], "head": [7, 7, 7, 7, 7, 7, 0, 10, 10, 7, 15, 13, 15, 15, 7, 15, 18, 15, 18, 21, 18, 25, 25, 25, 7, 25, 7], "deprel": ["nsubj", "aux", "advmod", "aux", "cop", "advmod", "root", "case", "det", "obl", "mark", "det", "nsubj", "cop", "advcl", "advmod", "mark", "advcl", "compound:prt", "case", "obl", "punct", "cc", "nsubj", "conj", "advmod", "punct"]}, {"id": "280", "sentence": "All the desserts the group tried got favorable reviews .", "triples": [{"uid": "280-0", "sentiment": "positive", "target_tags": "All\\O the\\O desserts\\B the\\O group\\O tried\\O got\\O favorable\\O reviews\\O .\\O", "opinion_tags": "All\\O the\\O desserts\\O the\\O group\\O tried\\O got\\O favorable\\B reviews\\O .\\O"}], "postag": ["PDT", "DT", "NNS", "DT", "NN", "VBD", "VBD", "JJ", "NNS", "."], "head": [3, 3, 7, 5, 6, 3, 0, 9, 7, 7], "deprel": ["det:predet", "det", "nsubj", "det", "nsubj", "acl:relcl", "root", "amod", "obj", "punct"]}, {"id": "281", "sentence": "Wonderful strawberry daiquiries as well !", "triples": [{"uid": "281-0", "sentiment": "positive", "target_tags": "Wonderful\\O strawberry\\B daiquiries\\I as\\O well\\O !\\O", "opinion_tags": "Wonderful\\B strawberry\\O daiquiries\\O as\\O well\\O !\\O"}], "postag": ["JJ", "NN", "NNS", "RB", "RB", "."], "head": [3, 3, 0, 3, 4, 3], "deprel": ["amod", "compound", "root", "advmod", "fixed", "punct"]}, {"id": "282", "sentence": "When I saw that their website had a link to da Ciro in Napoli , I knew there was going to be good pizza !", "triples": [{"uid": "282-0", "sentiment": "positive", "target_tags": "When\\O I\\O saw\\O that\\O their\\O website\\O had\\O a\\O link\\O to\\O da\\O Ciro\\O in\\O Napoli\\O ,\\O I\\O knew\\O there\\O was\\O going\\O to\\O be\\O good\\O pizza\\B !\\O", "opinion_tags": "When\\O I\\O saw\\O that\\O their\\O website\\O had\\O a\\O link\\O to\\O da\\O Ciro\\O in\\O Napoli\\O ,\\O I\\O knew\\O there\\O was\\O going\\O to\\O be\\O good\\B pizza\\O !\\O"}], "postag": ["WRB", "PRP", "VBD", "IN", "PRP$", "NN", "VBD", "DT", "NN", "IN", "NNP", "NNP", "IN", "NNP", ",", "PRP", "VBD", "EX", "VBD", "VBG", "TO", "VB", "JJ", "NN", "."], "head": [3, 3, 17, 7, 6, 7, 3, 9, 7, 12, 9, 11, 14, 7, 17, 17, 0, 20, 20, 17, 24, 24, 24, 20, 17], "deprel": ["mark", "nsubj", "advcl", "mark", "nmod:poss", "nsubj", "ccomp", "det", "obj", "case", "nmod", "flat", "case", "obl", "punct", "nsubj", "root", "expl", "aux", "ccomp", "mark", "cop", "amod", "xcomp", "punct"]}, {"id": "283", "sentence": "And their prices are very high - they actually think that they can get away with charging such prices for such terrible food and service !", "triples": [{"uid": "283-0", "sentiment": "negative", "target_tags": "And\\O their\\O prices\\B are\\O very\\O high\\O -\\O they\\O actually\\O think\\O that\\O they\\O can\\O get\\O away\\O with\\O charging\\O such\\O prices\\O for\\O such\\O terrible\\O food\\O and\\O service\\O !\\O", "opinion_tags": "And\\O their\\O prices\\O are\\O very\\O high\\B -\\O they\\O actually\\O think\\O that\\O they\\O can\\O get\\O away\\O with\\O charging\\O such\\O prices\\O for\\O such\\O terrible\\O food\\O and\\O service\\O !\\O"}, {"uid": "283-1", "sentiment": "negative", "target_tags": "And\\O their\\O prices\\O are\\O very\\O high\\O -\\O they\\O actually\\O think\\O that\\O they\\O can\\O get\\O away\\O with\\O charging\\O such\\O prices\\O for\\O such\\O terrible\\O food\\B and\\O service\\O !\\O", "opinion_tags": "And\\O their\\O prices\\O are\\O very\\O high\\O -\\O they\\O actually\\O think\\O that\\O they\\O can\\O get\\O away\\O with\\O charging\\O such\\O prices\\O for\\O such\\O terrible\\B food\\O and\\O service\\O !\\O"}, {"uid": "283-2", "sentiment": "negative", "target_tags": "And\\O their\\O prices\\O are\\O very\\O high\\O -\\O they\\O actually\\O think\\O that\\O they\\O can\\O get\\O away\\O with\\O charging\\O such\\O prices\\O for\\O such\\O terrible\\O food\\O and\\O service\\B !\\O", "opinion_tags": "And\\O their\\O prices\\O are\\O very\\O high\\O -\\O they\\O actually\\O think\\O that\\O they\\O can\\O get\\O away\\O with\\O charging\\O such\\O prices\\O for\\O such\\O terrible\\B food\\O and\\O service\\O !\\O"}], "postag": ["CC", "PRP$", "NNS", "VBP", "RB", "JJ", ",", "PRP", "RB", "VBP", "IN", "PRP", "MD", "VB", "RB", "IN", "VBG", "JJ", "NNS", "IN", "JJ", "JJ", "NN", "CC", "NN", "."], "head": [6, 3, 6, 6, 6, 0, 6, 10, 10, 6, 14, 14, 14, 10, 14, 17, 14, 19, 17, 23, 23, 23, 19, 25, 23, 6], "deprel": ["cc", "nmod:poss", "nsubj", "cop", "advmod", "root", "punct", "nsubj", "advmod", "parataxis", "mark", "nsubj", "aux", "ccomp", "advmod", "mark", "advcl", "amod", "obj", "case", "amod", "amod", "nmod", "cc", "conj", "punct"]}, {"id": "284", "sentence": "Great spot , whether looking for a couple of drinks or quiet dinner .", "triples": [{"uid": "284-0", "sentiment": "positive", "target_tags": "Great\\O spot\\O ,\\O whether\\O looking\\O for\\O a\\O couple\\O of\\O drinks\\O or\\O quiet\\O dinner\\B .\\O", "opinion_tags": "Great\\O spot\\O ,\\O whether\\O looking\\O for\\O a\\O couple\\O of\\O drinks\\O or\\O quiet\\B dinner\\O .\\O"}, {"uid": "284-1", "sentiment": "positive", "target_tags": "Great\\O spot\\B ,\\O whether\\O looking\\O for\\O a\\O couple\\O of\\O drinks\\O or\\O quiet\\O dinner\\O .\\O", "opinion_tags": "Great\\B spot\\O ,\\O whether\\O looking\\O for\\O a\\O couple\\O of\\O drinks\\O or\\O quiet\\O dinner\\O .\\O"}], "postag": ["JJ", "NN", ",", "IN", "VBG", "IN", "DT", "NN", "IN", "NNS", "CC", "JJ", "NN", "."], "head": [2, 0, 2, 5, 2, 8, 8, 5, 10, 8, 13, 13, 10, 2], "deprel": ["amod", "root", "punct", "mark", "acl", "case", "det", "obl", "case", "nmod", "cc", "amod", "conj", "punct"]}, {"id": "285", "sentence": "Just stick with the small dishes !", "triples": [{"uid": "285-0", "sentiment": "negative", "target_tags": "Just\\O stick\\O with\\O the\\O small\\O dishes\\B !\\O", "opinion_tags": "Just\\O stick\\O with\\O the\\O small\\B dishes\\O !\\O"}], "postag": ["RB", "VB", "IN", "DT", "JJ", "NNS", "."], "head": [2, 0, 6, 6, 6, 2, 2], "deprel": ["advmod", "root", "case", "det", "amod", "obl", "punct"]}, {"id": "286", "sentence": "both are very reasonably priced ( around $ 8 for dinner and $ 5 for lunch ) , and are delicious and filling .", "triples": [{"uid": "286-0", "sentiment": "positive", "target_tags": "both\\O are\\O very\\O reasonably\\O priced\\B (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\O and\\O filling\\O .\\O", "opinion_tags": "both\\O are\\O very\\O reasonably\\B priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\O and\\O filling\\O .\\O"}, {"uid": "286-1", "sentiment": "positive", "target_tags": "both\\O are\\O very\\O reasonably\\O priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\B and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\O and\\O filling\\O .\\O", "opinion_tags": "both\\O are\\O very\\O reasonably\\O priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\B and\\O filling\\O .\\O"}, {"uid": "286-2", "sentiment": "positive", "target_tags": "both\\O are\\O very\\O reasonably\\O priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\B and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\O and\\O filling\\O .\\O", "opinion_tags": "both\\O are\\O very\\O reasonably\\O priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\O and\\O filling\\B .\\O"}, {"uid": "286-3", "sentiment": "positive", "target_tags": "both\\O are\\O very\\O reasonably\\O priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\B )\\O ,\\O and\\O are\\O delicious\\O and\\O filling\\O .\\O", "opinion_tags": "both\\O are\\O very\\O reasonably\\O priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\B and\\O filling\\O .\\O"}, {"uid": "286-4", "sentiment": "positive", "target_tags": "both\\O are\\O very\\O reasonably\\O priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\B )\\O ,\\O and\\O are\\O delicious\\O and\\O filling\\O .\\O", "opinion_tags": "both\\O are\\O very\\O reasonably\\O priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\O and\\O filling\\B .\\O"}], "postag": ["DT", "VBP", "RB", "RB", "VBN", "-LRB-", "RB", "$", "CD", "IN", "NN", "CC", "$", "CD", "IN", "NN", "-RRB-", ",", "CC", "VBP", "JJ", "CC", "VBG", "."], "head": [5, 5, 4, 5, 0, 8, 8, 5, 8, 11, 8, 13, 8, 13, 16, 13, 8, 21, 21, 21, 5, 23, 21, 5], "deprel": ["nsubj:pass", "aux:pass", "advmod", "advmod", "root", "punct", "advmod", "parataxis", "nummod", "case", "nmod", "cc", "conj", "nummod", "case", "nmod", "punct", "punct", "cc", "cop", "conj", "cc", "conj", "punct"]}, {"id": "287", "sentence": "My boyfriend ate tuna and it was cooked perfectly !", "triples": [{"uid": "287-0", "sentiment": "positive", "target_tags": "My\\O boyfriend\\O ate\\O tuna\\B and\\O it\\O was\\O cooked\\O perfectly\\O !\\O", "opinion_tags": "My\\O boyfriend\\O ate\\O tuna\\O and\\O it\\O was\\O cooked\\O perfectly\\B !\\O"}], "postag": ["PRP$", "NN", "VBD", "NN", "CC", "PRP", "VBD", "VBN", "RB", "."], "head": [2, 3, 0, 3, 8, 8, 8, 3, 8, 3], "deprel": ["nmod:poss", "nsubj", "root", "obj", "cc", "nsubj:pass", "aux:pass", "conj", "advmod", "punct"]}, {"id": "288", "sentence": "great eats , good times .", "triples": [{"uid": "288-0", "sentiment": "positive", "target_tags": "great\\O eats\\B ,\\O good\\O times\\O .\\O", "opinion_tags": "great\\B eats\\O ,\\O good\\O times\\O .\\O"}], "postag": ["JJ", "NNS", ",", "JJ", "NNS", "."], "head": [2, 0, 2, 5, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct"]}, {"id": "289", "sentence": "The food was delicious , the atmosphere was relaxed , and we have now adopted Plate 347 as our Secret on Second !", "triples": [{"uid": "289-0", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O delicious\\O ,\\O the\\O atmosphere\\O was\\O relaxed\\O ,\\O and\\O we\\O have\\O now\\O adopted\\O Plate\\O 347\\O as\\O our\\O Secret\\O on\\O Second\\O !\\O", "opinion_tags": "The\\O food\\O was\\O delicious\\B ,\\O the\\O atmosphere\\O was\\O relaxed\\O ,\\O and\\O we\\O have\\O now\\O adopted\\O Plate\\O 347\\O as\\O our\\O Secret\\O on\\O Second\\O !\\O"}, {"uid": "289-1", "sentiment": "positive", "target_tags": "The\\O food\\O was\\O delicious\\O ,\\O the\\O atmosphere\\B was\\O relaxed\\O ,\\O and\\O we\\O have\\O now\\O adopted\\O Plate\\O 347\\O as\\O our\\O Secret\\O on\\O Second\\O !\\O", "opinion_tags": "The\\O food\\O was\\O delicious\\O ,\\O the\\O atmosphere\\O was\\O relaxed\\B ,\\O and\\O we\\O have\\O now\\O adopted\\O Plate\\O 347\\O as\\O our\\O Secret\\O on\\O Second\\O !\\O"}], "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "JJ", ",", "CC", "PRP", "VBP", "RB", "VBN", "NNP", "CD", "IN", "PRP$", "NN", "IN", "NNP", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 15, 15, 15, 15, 15, 4, 15, 16, 20, 20, 15, 22, 20, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "conj", "punct", "cc", "nsubj", "aux", "advmod", "conj", "obj", "nummod", "case", "nmod:poss", "obl", "case", "nmod", "punct"]}, {"id": "290", "sentence": "The food is good , I ca n't lie .", "triples": [{"uid": "290-0", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O good\\O ,\\O I\\O ca\\O n't\\O lie\\O .\\O", "opinion_tags": "The\\O food\\O is\\O good\\B ,\\O I\\O ca\\O n't\\O lie\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ",", "PRP", "MD", "RB", "VB", "."], "head": [2, 4, 4, 0, 4, 9, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "aux", "advmod", "parataxis", "punct"]}, {"id": "291", "sentence": "great food , lt 's of it , more then one person can eat !", "triples": [{"uid": "291-0", "sentiment": "positive", "target_tags": "great\\O food\\B ,\\O lt\\O 's\\O of\\O it\\O ,\\O more\\O then\\O one\\O person\\O can\\O eat\\O !\\O", "opinion_tags": "great\\B food\\O ,\\O lt\\O 's\\O of\\O it\\O ,\\O more\\O then\\O one\\O person\\O can\\O eat\\O !\\O"}], "postag": ["JJ", "NN", ",", "PRP", "VBZ", "IN", "PRP", ",", "JJR", "RB", "CD", "NN", "MD", "VB", "."], "head": [2, 0, 2, 7, 7, 7, 2, 2, 14, 14, 12, 14, 14, 2, 2], "deprel": ["amod", "root", "punct", "nsubj", "cop", "case", "parataxis", "punct", "advmod", "advmod", "nummod", "nsubj", "aux", "parataxis", "punct"]}, {"id": "292", "sentence": "Really tasty spring rolls and noodles for a good price though .", "triples": [{"uid": "292-0", "sentiment": "positive", "target_tags": "Really\\O tasty\\O spring\\B rolls\\I and\\O noodles\\O for\\O a\\O good\\O price\\O though\\O .\\O", "opinion_tags": "Really\\O tasty\\B spring\\O rolls\\O and\\O noodles\\O for\\O a\\O good\\O price\\O though\\O .\\O"}, {"uid": "292-1", "sentiment": "positive", "target_tags": "Really\\O tasty\\O spring\\O rolls\\O and\\O noodles\\B for\\O a\\O good\\O price\\O though\\O .\\O", "opinion_tags": "Really\\O tasty\\B spring\\O rolls\\O and\\O noodles\\O for\\O a\\O good\\O price\\O though\\O .\\O"}, {"uid": "292-2", "sentiment": "positive", "target_tags": "Really\\O tasty\\O spring\\O rolls\\O and\\O noodles\\O for\\O a\\O good\\O price\\B though\\O .\\O", "opinion_tags": "Really\\O tasty\\O spring\\O rolls\\O and\\O noodles\\O for\\O a\\O good\\B price\\O though\\O .\\O"}], "postag": ["RB", "JJ", "NN", "NNS", "CC", "NNS", "IN", "DT", "JJ", "NN", "RB", "."], "head": [2, 4, 4, 0, 6, 4, 10, 10, 10, 4, 4, 4], "deprel": ["advmod", "amod", "compound", "root", "cc", "conj", "case", "det", "amod", "nmod", "advmod", "punct"]}, {"id": "293", "sentence": "Little <PERSON><PERSON> 's is just awesome , our favorite delivery place in Kennsington , honestly the best Gnochi I have ever had !", "triples": [{"uid": "293-0", "sentiment": "positive", "target_tags": "Little\\O Tonino\\O 's\\O is\\O just\\O awesome\\O ,\\O our\\O favorite\\O delivery\\O place\\O in\\O Kennsington\\O ,\\O honestly\\O the\\O best\\O Gnochi\\B I\\O have\\O ever\\O had\\O !\\O", "opinion_tags": "Little\\O Tonino\\O 's\\O is\\O just\\O awesome\\O ,\\O our\\O favorite\\O delivery\\O place\\O in\\O Kennsington\\O ,\\O honestly\\O the\\O best\\B Gnochi\\O I\\O have\\O ever\\O had\\O !\\O"}], "postag": ["JJ", "NNP", "POS", "VBZ", "RB", "JJ", ",", "PRP$", "JJ", "NN", "NN", "IN", "NNP", ",", "RB", "DT", "JJS", "NNP", "PRP", "VBP", "RB", "VBN", "."], "head": [2, 6, 2, 6, 6, 0, 6, 11, 11, 11, 6, 13, 11, 18, 18, 18, 18, 6, 22, 22, 22, 18, 6], "deprel": ["amod", "nsubj", "case", "cop", "advmod", "root", "punct", "nmod:poss", "amod", "compound", "parataxis", "case", "nmod", "punct", "advmod", "det", "amod", "parataxis", "nsubj", "aux", "advmod", "acl:relcl", "punct"]}, {"id": "294", "sentence": "INCREDIBLY POOR SERVICE AN FOOD QUALITY AT EXORBITANT PRICES .", "triples": [{"uid": "294-0", "sentiment": "negative", "target_tags": "INCREDIBLY\\O POOR\\O SERVICE\\B AN\\O FOOD\\O QUALITY\\O AT\\O EXORBITANT\\O PRICES\\O .\\O", "opinion_tags": "INCREDIBLY\\O POOR\\B SERVICE\\O AN\\O FOOD\\O QUALITY\\O AT\\O EXORBITANT\\O PRICES\\O .\\O"}, {"uid": "294-1", "sentiment": "negative", "target_tags": "INCREDIBLY\\O POOR\\O SERVICE\\O AN\\O FOOD\\B QUALITY\\I AT\\O EXORBITANT\\O PRICES\\O .\\O", "opinion_tags": "INCREDIBLY\\O POOR\\B SERVICE\\O AN\\O FOOD\\O QUALITY\\O AT\\O EXORBITANT\\O PRICES\\O .\\O"}, {"uid": "294-2", "sentiment": "negative", "target_tags": "INCREDIBLY\\O POOR\\O SERVICE\\O AN\\O FOOD\\O QUALITY\\O AT\\O EXORBITANT\\O PRICES\\B .\\O", "opinion_tags": "INCREDIBLY\\O POOR\\O SERVICE\\O AN\\O FOOD\\O QUALITY\\O AT\\O EXORBITANT\\B PRICES\\O .\\O"}], "postag": ["RB", "JJ", "NN", "DT", "NN", "NN", "IN", "JJ", "NNS", "."], "head": [2, 3, 0, 6, 6, 3, 9, 9, 6, 3], "deprel": ["advmod", "amod", "root", "det", "compound", "parataxis", "case", "amod", "nmod", "punct"]}, {"id": "295", "sentence": "I have been about 4 times and have always had a great meal .", "triples": [{"uid": "295-0", "sentiment": "positive", "target_tags": "I\\O have\\O been\\O about\\O 4\\O times\\O and\\O have\\O always\\O had\\O a\\O great\\O meal\\B .\\O", "opinion_tags": "I\\O have\\O been\\O about\\O 4\\O times\\O and\\O have\\O always\\O had\\O a\\O great\\B meal\\O .\\O"}], "postag": ["PRP", "VBP", "VBN", "RB", "CD", "NNS", "CC", "VBP", "RB", "VBN", "DT", "JJ", "NN", "."], "head": [6, 6, 6, 5, 6, 0, 10, 10, 10, 6, 13, 13, 10, 6], "deprel": ["nsubj", "aux", "cop", "advmod", "nummod", "root", "cc", "aux", "advmod", "conj", "det", "amod", "obj", "punct"]}, {"id": "296", "sentence": "The wine and cheese plate are plentiful and ca n't wait to try the fondue or table grilling .", "triples": [{"uid": "296-0", "sentiment": "positive", "target_tags": "The\\O wine\\B and\\O cheese\\O plate\\O are\\O plentiful\\O and\\O ca\\O n't\\O wait\\O to\\O try\\O the\\O fondue\\O or\\O table\\O grilling\\O .\\O", "opinion_tags": "The\\O wine\\O and\\O cheese\\O plate\\O are\\O plentiful\\B and\\O ca\\O n't\\O wait\\O to\\O try\\O the\\O fondue\\O or\\O table\\O grilling\\O .\\O"}, {"uid": "296-1", "sentiment": "positive", "target_tags": "The\\O wine\\O and\\O cheese\\B plate\\O are\\O plentiful\\O and\\O ca\\O n't\\O wait\\O to\\O try\\O the\\O fondue\\O or\\O table\\O grilling\\O .\\O", "opinion_tags": "The\\O wine\\O and\\O cheese\\O plate\\O are\\O plentiful\\B and\\O ca\\O n't\\O wait\\O to\\O try\\O the\\O fondue\\O or\\O table\\O grilling\\O .\\O"}], "postag": ["DT", "NN", "CC", "NN", "NN", "VBP", "JJ", "CC", "MD", "RB", "VB", "TO", "VB", "DT", "NN", "CC", "NN", "NN", "."], "head": [5, 5, 4, 2, 7, 7, 0, 11, 11, 11, 7, 13, 11, 15, 13, 18, 18, 15, 7], "deprel": ["det", "compound", "cc", "conj", "nsubj", "cop", "root", "cc", "aux", "advmod", "conj", "mark", "advcl", "det", "obj", "cc", "compound", "conj", "punct"]}, {"id": "297", "sentence": "THE BIG COMPLAINT : NO TOASTING AVAILABLE .", "triples": [{"uid": "297-0", "sentiment": "negative", "target_tags": "THE\\O BIG\\O COMPLAINT\\O :\\O NO\\O TOASTING\\B AVAILABLE\\O .\\O", "opinion_tags": "THE\\O BIG\\O COMPLAINT\\B :\\I NO\\O TOASTING\\O AVAILABLE\\O .\\O"}], "postag": ["DT", "JJ", "NN", ":", "DT", "NN", "JJ", "."], "head": [3, 3, 0, 3, 6, 3, 3, 3], "deprel": ["det", "amod", "root", "punct", "det", "parataxis", "appos", "punct"]}, {"id": "298", "sentence": "No you 're going to go back because the food was good .", "triples": [{"uid": "298-0", "sentiment": "positive", "target_tags": "No\\O you\\O 're\\O going\\O to\\O go\\O back\\O because\\O the\\O food\\B was\\O good\\O .\\O", "opinion_tags": "No\\O you\\O 're\\O going\\O to\\O go\\O back\\O because\\O the\\O food\\O was\\O good\\B .\\O"}], "postag": ["UH", "PRP", "VBP", "VBG", "TO", "VB", "RB", "IN", "DT", "NN", "VBD", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 6, 12, 10, 12, 12, 6, 4], "deprel": ["discourse", "nsubj", "aux", "root", "mark", "xcomp", "advmod", "mark", "det", "nsubj", "cop", "advcl", "punct"]}, {"id": "299", "sentence": "The fish is fresh and each piece is sliced to perfection and seasoned by the sushi chef ( usually with a little fresh wasabi and soy sauce but also sometimes with some sea salt ) .", "triples": [{"uid": "299-0", "sentiment": "positive", "target_tags": "The\\O fish\\B is\\O fresh\\O and\\O each\\O piece\\O is\\O sliced\\O to\\O perfection\\O and\\O seasoned\\O by\\O the\\O sushi\\O chef\\O (\\O usually\\O with\\O a\\O little\\O fresh\\O wasabi\\O and\\O soy\\O sauce\\O but\\O also\\O sometimes\\O with\\O some\\O sea\\O salt\\O )\\O .\\O", "opinion_tags": "The\\O fish\\O is\\O fresh\\B and\\O each\\O piece\\O is\\O sliced\\O to\\O perfection\\O and\\O seasoned\\O by\\O the\\O sushi\\O chef\\O (\\O usually\\O with\\O a\\O little\\O fresh\\O wasabi\\O and\\O soy\\O sauce\\O but\\O also\\O sometimes\\O with\\O some\\O sea\\O salt\\O )\\O .\\O"}, {"uid": "299-1", "sentiment": "positive", "target_tags": "The\\O fish\\O is\\O fresh\\O and\\O each\\O piece\\O is\\O sliced\\O to\\O perfection\\O and\\O seasoned\\O by\\O the\\O sushi\\B chef\\I (\\O usually\\O with\\O a\\O little\\O fresh\\O wasabi\\O and\\O soy\\O sauce\\O but\\O also\\O sometimes\\O with\\O some\\O sea\\O salt\\O )\\O .\\O", "opinion_tags": "The\\O fish\\O is\\O fresh\\O and\\O each\\O piece\\O is\\O sliced\\O to\\O perfection\\O and\\O seasoned\\B by\\O the\\O sushi\\O chef\\O (\\O usually\\O with\\O a\\O little\\O fresh\\O wasabi\\O and\\O soy\\O sauce\\O but\\O also\\O sometimes\\O with\\O some\\O sea\\O salt\\O )\\O .\\O"}, {"uid": "299-2", "sentiment": "positive", "target_tags": "The\\O fish\\O is\\O fresh\\O and\\O each\\O piece\\O is\\O sliced\\O to\\O perfection\\O and\\O seasoned\\O by\\O the\\O sushi\\O chef\\O (\\O usually\\O with\\O a\\O little\\O fresh\\O wasabi\\B and\\O soy\\O sauce\\O but\\O also\\O sometimes\\O with\\O some\\O sea\\O salt\\O )\\O .\\O", "opinion_tags": "The\\O fish\\O is\\O fresh\\O and\\O each\\O piece\\O is\\O sliced\\O to\\O perfection\\O and\\O seasoned\\O by\\O the\\O sushi\\O chef\\O (\\O usually\\O with\\O a\\O little\\O fresh\\B wasabi\\O and\\O soy\\O sauce\\O but\\O also\\O sometimes\\O with\\O some\\O sea\\O salt\\O )\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "VBN", "IN", "NN", "CC", "VBN", "IN", "DT", "NN", "NN", "-LRB-", "RB", "IN", "DT", "JJ", "JJ", "NN", "CC", "NN", "NN", "CC", "RB", "RB", "IN", "DT", "NN", "NN", "-RRB-", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 11, 9, 13, 9, 17, 17, 17, 13, 24, 24, 24, 24, 24, 24, 17, 27, 27, 24, 34, 34, 34, 34, 34, 34, 24, 24, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj:pass", "aux:pass", "conj", "case", "obl", "cc", "conj", "case", "det", "compound", "obl", "punct", "advmod", "case", "det", "amod", "amod", "nmod", "cc", "compound", "conj", "cc", "advmod", "advmod", "case", "det", "compound", "conj", "punct", "punct"]}, {"id": "300", "sentence": "Veal <PERSON><PERSON><PERSON><PERSON> - Better than <PERSON><PERSON> 's !", "triples": [{"uid": "300-0", "sentiment": "positive", "target_tags": "Veal\\B Parmigana\\I -\\O Better\\O than\\O <PERSON>sy\\O 's\\O !\\O", "opinion_tags": "Veal\\O Parmigana\\O -\\O Better\\B than\\O <PERSON>\\O 's\\O !\\O"}], "postag": ["NNP", "NNP", ",", "JJR", "IN", "NNP", "POS", "."], "head": [0, 1, 1, 1, 6, 4, 6, 1], "deprel": ["root", "flat", "punct", "parataxis", "case", "obl", "case", "punct"]}, {"id": "301", "sentence": "<PERSON>t to Otti<PERSON> and was expecting outstanding pizza ( as I love La Pizza Fresca ) .", "triples": [{"uid": "301-0", "sentiment": "neutral", "target_tags": "Went\\O to\\O Ottimo\\O and\\O was\\O expecting\\O outstanding\\O pizza\\B (\\O as\\O I\\O love\\O La\\O Pizza\\O Fresca\\O )\\O .\\O", "opinion_tags": "Went\\O to\\O Ottimo\\O and\\O was\\O expecting\\O outstanding\\B pizza\\O (\\O as\\O I\\O love\\O La\\O Pizza\\O Fresca\\O )\\O .\\O"}], "postag": ["VBD", "IN", "NNP", "CC", "VBD", "VBG", "JJ", "NN", "-LRB-", "IN", "PRP", "VBP", "NNP", "NNP", "NNP", "-RRB-", "."], "head": [0, 3, 1, 6, 6, 1, 8, 6, 12, 12, 12, 6, 15, 15, 12, 12, 1], "deprel": ["root", "case", "obl", "cc", "aux", "conj", "amod", "obj", "punct", "mark", "nsubj", "advcl", "compound", "compound", "obj", "punct", "punct"]}, {"id": "302", "sentence": "Our tiny table for two ( dinner plates hung over edge ) was right in the middle of one of the lanes of waiter traffic .", "triples": [{"uid": "302-0", "sentiment": "negative", "target_tags": "Our\\O tiny\\O table\\B for\\O two\\O (\\O dinner\\O plates\\O hung\\O over\\O edge\\O )\\O was\\O right\\O in\\O the\\O middle\\O of\\O one\\O of\\O the\\O lanes\\O of\\O waiter\\O traffic\\O .\\O", "opinion_tags": "Our\\O tiny\\B table\\O for\\O two\\O (\\O dinner\\O plates\\O hung\\O over\\O edge\\O )\\O was\\O right\\O in\\O the\\O middle\\O of\\O one\\O of\\O the\\O lanes\\O of\\O waiter\\O traffic\\O .\\O"}], "postag": ["PRP$", "JJ", "NN", "IN", "CD", "-LRB-", "NN", "NNS", "VBD", "IN", "NN", "-RRB-", "VBD", "RB", "IN", "DT", "NN", "IN", "CD", "IN", "DT", "NNS", "IN", "NN", "NN", "."], "head": [3, 3, 13, 5, 3, 9, 8, 9, 3, 11, 9, 9, 0, 17, 17, 17, 13, 19, 17, 22, 22, 19, 25, 25, 22, 13], "deprel": ["nmod:poss", "amod", "nsubj", "case", "nmod", "punct", "compound", "nsubj", "parataxis", "case", "obl", "punct", "root", "advmod", "case", "det", "obl", "case", "nmod", "case", "det", "nmod", "case", "compound", "nmod", "punct"]}, {"id": "303", "sentence": "Sake collection was excellent ( Try Nanbu Bijin ) , but pricey .", "triples": [{"uid": "303-0", "sentiment": "positive", "target_tags": "Sake\\O collection\\O was\\O excellent\\O (\\O Try\\O Nanbu\\B Bijin\\I )\\O ,\\O but\\O pricey\\O .\\O", "opinion_tags": "Sake\\O collection\\O was\\O excellent\\O (\\O Try\\B Nanbu\\O Bijin\\O )\\O ,\\O but\\O pricey\\O .\\O"}], "postag": ["NN", "NN", "VBD", "JJ", "-LRB-", "VB", "NNP", "NNP", "-RRB-", ",", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 6, 6, 6, 12, 12, 6, 4], "deprel": ["compound", "nsubj", "cop", "root", "punct", "parataxis", "obj", "obj", "punct", "punct", "cc", "conj", "punct"]}, {"id": "304", "sentence": "A wonderful place !", "triples": [{"uid": "304-0", "sentiment": "positive", "target_tags": "A\\O wonderful\\O place\\B !\\O", "opinion_tags": "A\\O wonderful\\B place\\O !\\O"}], "postag": ["DT", "JJ", "NN", "."], "head": [3, 3, 0, 3], "deprel": ["det", "amod", "root", "punct"]}, {"id": "305", "sentence": "Kind of a small place but I guess if they are not too busy might be able to fit a group or kids .", "triples": [{"uid": "305-0", "sentiment": "negative", "target_tags": "Kind\\O of\\O a\\O small\\O place\\B but\\O I\\O guess\\O if\\O they\\O are\\O not\\O too\\O busy\\O might\\O be\\O able\\O to\\O fit\\O a\\O group\\O or\\O kids\\O .\\O", "opinion_tags": "Kind\\O of\\O a\\O small\\B place\\O but\\O I\\O guess\\O if\\O they\\O are\\O not\\O too\\O busy\\O might\\O be\\O able\\O to\\O fit\\O a\\O group\\O or\\O kids\\O .\\O"}], "postag": ["NN", "IN", "DT", "JJ", "NN", "CC", "PRP", "VBP", "IN", "PRP", "VBP", "RB", "RB", "JJ", "MD", "VB", "JJ", "TO", "VB", "DT", "NN", "CC", "NNS", "."], "head": [0, 5, 5, 5, 1, 8, 8, 1, 14, 14, 14, 14, 14, 17, 17, 17, 8, 19, 17, 21, 19, 23, 21, 1], "deprel": ["root", "case", "det", "amod", "nmod", "cc", "nsubj", "conj", "mark", "nsubj", "cop", "advmod", "advmod", "advcl", "aux", "cop", "ccomp", "mark", "xcomp", "det", "obj", "cc", "conj", "punct"]}, {"id": "306", "sentence": "We also ordered two hot dogs thinking they would be pretty good since there is a whole section on the menu devoted to them .", "triples": [{"uid": "306-0", "sentiment": "neutral", "target_tags": "We\\O also\\O ordered\\O two\\O hot\\B dogs\\I thinking\\O they\\O would\\O be\\O pretty\\O good\\O since\\O there\\O is\\O a\\O whole\\O section\\O on\\O the\\O menu\\O devoted\\O to\\O them\\O .\\O", "opinion_tags": "We\\O also\\O ordered\\O two\\O hot\\O dogs\\O thinking\\O they\\O would\\O be\\O pretty\\O good\\B since\\O there\\O is\\O a\\O whole\\O section\\O on\\O the\\O menu\\O devoted\\O to\\O them\\O .\\O"}], "postag": ["PRP", "RB", "VBD", "CD", "JJ", "NNS", "VBG", "PRP", "MD", "VB", "RB", "JJ", "IN", "EX", "VBZ", "DT", "JJ", "NN", "IN", "DT", "NN", "VBN", "IN", "PRP", "."], "head": [3, 3, 0, 6, 6, 3, 6, 12, 12, 12, 12, 7, 15, 15, 12, 18, 18, 15, 21, 21, 18, 18, 24, 22, 3], "deprel": ["nsubj", "advmod", "root", "nummod", "amod", "obj", "acl", "nsubj", "aux", "cop", "advmod", "ccomp", "mark", "expl", "advcl", "det", "amod", "nsubj", "case", "det", "nmod", "acl", "case", "obl", "punct"]}, {"id": "307", "sentence": "If you like the food and the value you get from some of Chinatown restaurants , this is not the place for you .", "triples": [{"uid": "307-0", "sentiment": "neutral", "target_tags": "If\\O you\\O like\\O the\\O food\\B and\\O the\\O value\\O you\\O get\\O from\\O some\\O of\\O Chinatown\\O restaurants\\O ,\\O this\\O is\\O not\\O the\\O place\\O for\\O you\\O .\\O", "opinion_tags": "If\\O you\\O like\\B the\\O food\\O and\\O the\\O value\\O you\\O get\\O from\\O some\\O of\\O Chinatown\\O restaurants\\O ,\\O this\\O is\\O not\\O the\\O place\\O for\\O you\\O .\\O"}, {"uid": "307-1", "sentiment": "neutral", "target_tags": "If\\O you\\O like\\O the\\O food\\O and\\O the\\O value\\B you\\O get\\O from\\O some\\O of\\O Chinatown\\O restaurants\\O ,\\O this\\O is\\O not\\O the\\O place\\O for\\O you\\O .\\O", "opinion_tags": "If\\O you\\O like\\B the\\O food\\O and\\O the\\O value\\O you\\O get\\O from\\O some\\O of\\O Chinatown\\O restaurants\\O ,\\O this\\O is\\O not\\O the\\O place\\O for\\O you\\O .\\O"}], "postag": ["IN", "PRP", "VBP", "DT", "NN", "CC", "DT", "NN", "PRP", "VBP", "IN", "DT", "IN", "NNP", "NNS", ",", "DT", "VBZ", "RB", "DT", "NN", "IN", "PRP", "."], "head": [3, 3, 21, 5, 3, 8, 8, 5, 10, 8, 12, 10, 15, 15, 12, 21, 21, 21, 21, 21, 0, 23, 21, 21], "deprel": ["mark", "nsubj", "advcl", "det", "obj", "cc", "det", "conj", "nsubj", "acl:relcl", "case", "obl", "case", "compound", "nmod", "punct", "nsubj", "cop", "advmod", "det", "root", "case", "nmod", "punct"]}, {"id": "308", "sentence": "The restaraurant is very small so reservations are a must .", "triples": [{"uid": "308-0", "sentiment": "neutral", "target_tags": "The\\O restaraurant\\O is\\O very\\O small\\O so\\O reservations\\B are\\O a\\O must\\O .\\O", "opinion_tags": "The\\O restaraurant\\O is\\O very\\O small\\O so\\O reservations\\O are\\O a\\O must\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "RB", "NNS", "VBP", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 10, 10, 10, 10, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "advmod", "nsubj", "cop", "det", "parataxis", "punct"]}, {"id": "309", "sentence": "The service was typical short-order , dinner type .", "triples": [{"uid": "309-0", "sentiment": "neutral", "target_tags": "The\\O service\\B was\\O typical\\O short-order\\O ,\\O dinner\\O type\\O .\\O", "opinion_tags": "The\\O service\\O was\\O typical\\B short-order\\O ,\\O dinner\\O type\\O .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "NN", ",", "NN", "NN", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 5], "deprel": ["det", "nsubj", "cop", "amod", "root", "punct", "compound", "conj", "punct"]}]