[{"id": "0", "sentence": "The food is very average ... the Thai fusion stuff is a bit too sweet , every thing they serve is too sweet here .", "triples": [{"uid": "0-0", "sentiment": "negative", "target_tags": "The\\O food\\B is\\O very\\O average\\O ...\\O the\\O Thai\\O fusion\\O stuff\\O is\\O a\\O bit\\O too\\O sweet\\O ,\\O every\\O thing\\O they\\O serve\\O is\\O too\\O sweet\\O here\\O .\\O", "opinion_tags": "The\\O food\\O is\\O very\\O average\\B ...\\O the\\O Thai\\O fusion\\O stuff\\O is\\O a\\O bit\\O too\\O sweet\\O ,\\O every\\O thing\\O they\\O serve\\O is\\O too\\O sweet\\O here\\O .\\O"}, {"uid": "0-1", "sentiment": "negative", "target_tags": "The\\O food\\O is\\O very\\O average\\O ...\\O the\\O Thai\\B fusion\\I stuff\\I is\\O a\\O bit\\O too\\O sweet\\O ,\\O every\\O thing\\O they\\O serve\\O is\\O too\\O sweet\\O here\\O .\\O", "opinion_tags": "The\\O food\\O is\\O very\\O average\\O ...\\O the\\O Thai\\O fusion\\O stuff\\O is\\O a\\O bit\\O too\\B sweet\\I ,\\O every\\O thing\\O they\\O serve\\O is\\O too\\O sweet\\O here\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", ",", "DT", "JJ", "NN", "NN", "VBZ", "DT", "NN", "RB", "JJ", ",", "DT", "NN", "PRP", "VBP", "VBZ", "RB", "JJ", "RB", "."], "head": [2, 5, 5, 5, 0, 5, 10, 10, 10, 15, 15, 13, 14, 15, 5, 23, 18, 23, 20, 18, 23, 23, 5, 23, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "det", "amod", "compound", "nsubj", "cop", "det", "nmod:npmod", "advmod", "parataxis", "punct", "det", "nsubj", "nsubj", "acl:relcl", "cop", "advmod", "parataxis", "advmod", "punct"]}, {"id": "1", "sentence": "We went around 9:30 on a Friday and it had died down a bit by then so the service was great !", "triples": [{"uid": "1-0", "sentiment": "positive", "target_tags": "We\\O went\\O around\\O 9:30\\O on\\O a\\O Friday\\O and\\O it\\O had\\O died\\O down\\O a\\O bit\\O by\\O then\\O so\\O the\\O service\\B was\\O great\\O !\\O", "opinion_tags": "We\\O went\\O around\\O 9:30\\O on\\O a\\O Friday\\O and\\O it\\O had\\O died\\O down\\O a\\O bit\\O by\\O then\\O so\\O the\\O service\\O was\\O great\\B !\\O"}], "postag": ["PRP", "VBD", "RB", "CD", "IN", "DT", "NNP", "CC", "PRP", "VBD", "VBN", "RP", "DT", "NN", "IN", "RB", "RB", "DT", "NN", "VBD", "JJ", "."], "head": [2, 0, 4, 2, 7, 7, 2, 11, 11, 11, 2, 11, 14, 11, 16, 11, 21, 19, 21, 21, 2, 2], "deprel": ["nsubj", "root", "advmod", "obl:tmod", "case", "det", "obl", "cc", "nsubj", "aux", "conj", "compound:prt", "det", "obl:npmod", "case", "obl", "advmod", "det", "nsubj", "cop", "parataxis", "punct"]}, {"id": "2", "sentence": "we love th pink pony .", "triples": [{"uid": "2-0", "sentiment": "positive", "target_tags": "we\\O love\\O th\\O pink\\B pony\\I .\\O", "opinion_tags": "we\\O love\\B th\\O pink\\O pony\\O .\\O"}], "postag": ["PRP", "VBP", "DT", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "punct"]}, {"id": "3", "sentence": "the food is decent .", "triples": [{"uid": "3-0", "sentiment": "neutral", "target_tags": "the\\O food\\B is\\O decent\\O .\\O", "opinion_tags": "the\\O food\\O is\\O decent\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"]}, {"id": "4", "sentence": "however , it 's the service that leaves a bad taste in my mouth .", "triples": [{"uid": "4-0", "sentiment": "negative", "target_tags": "however\\O ,\\O it\\O 's\\O the\\O service\\B that\\O leaves\\O a\\O bad\\O taste\\O in\\O my\\O mouth\\O .\\O", "opinion_tags": "however\\O ,\\O it\\O 's\\O the\\O service\\O that\\O leaves\\O a\\O bad\\B taste\\I in\\O my\\O mouth\\O .\\O"}], "postag": ["RB", ",", "PRP", "VBZ", "DT", "NN", "WDT", "VBZ", "DT", "JJ", "NN", "IN", "PRP$", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 8, 6, 11, 11, 8, 14, 14, 8, 6], "deprel": ["advmod", "punct", "nsubj", "cop", "det", "root", "nsubj", "acl:relcl", "det", "amod", "obj", "case", "nmod:poss", "obl", "punct"]}, {"id": "5", "sentence": "i happen to have a policy that goes along with a little bit of self-respect , which includes not letting a waiter intimidate me , i.e . make me feel bad asking for trivialities like water , or the check .", "triples": [{"uid": "5-0", "sentiment": "negative", "target_tags": "i\\O happen\\O to\\O have\\O a\\O policy\\O that\\O goes\\O along\\O with\\O a\\O little\\O bit\\O of\\O self-respect\\O ,\\O which\\O includes\\O not\\O letting\\O a\\O waiter\\B intimidate\\O me\\O ,\\O i.e\\O .\\O make\\O me\\O feel\\O bad\\O asking\\O for\\O trivialities\\O like\\O water\\O ,\\O or\\O the\\O check\\O .\\O", "opinion_tags": "i\\O happen\\O to\\O have\\O a\\O policy\\O that\\O goes\\O along\\O with\\O a\\O little\\O bit\\O of\\O self-respect\\O ,\\O which\\O includes\\O not\\O letting\\O a\\O waiter\\O intimidate\\O me\\O ,\\O i.e\\O .\\O make\\O me\\O feel\\O bad\\B asking\\O for\\O trivialities\\O like\\O water\\O ,\\O or\\O the\\O check\\O .\\O"}], "postag": ["PRP", "VBP", "TO", "VB", "DT", "NN", "WDT", "VBZ", "RB", "IN", "DT", "JJ", "NN", "IN", "NN", ",", "WDT", "VBZ", "RB", "VBG", "DT", "NN", "VB", "PRP", ",", "RB", ".", "VB", "PRP", "VB", "JJ", "VBG", "IN", "NNS", "IN", "NN", ",", "CC", "DT", "NN", "."], "head": [2, 0, 4, 2, 6, 4, 8, 6, 8, 13, 13, 13, 8, 15, 13, 18, 18, 6, 20, 18, 22, 20, 20, 23, 28, 28, 28, 2, 28, 28, 30, 30, 34, 32, 36, 34, 40, 40, 40, 36, 2], "deprel": ["nsubj", "root", "mark", "xcomp", "det", "obj", "nsubj", "acl:relcl", "advmod", "case", "det", "amod", "obl", "case", "nmod", "punct", "nsubj", "acl:relcl", "advmod", "xcomp", "det", "obj", "xcomp", "obj", "punct", "advmod", "punct", "parataxis", "obj", "xcomp", "xcomp", "xcomp", "case", "obl", "case", "nmod", "punct", "cc", "det", "conj", "punct"]}, {"id": "6", "sentence": "I tend to judge a sushi restaurant by its sea urchin , which was heavenly at sushi rose .", "triples": [{"uid": "6-0", "sentiment": "positive", "target_tags": "I\\O tend\\O to\\O judge\\O a\\O sushi\\O restaurant\\O by\\O its\\O sea\\B urchin\\I ,\\O which\\O was\\O heavenly\\O at\\O sushi\\O rose\\O .\\O", "opinion_tags": "I\\O tend\\O to\\O judge\\O a\\O sushi\\O restaurant\\O by\\O its\\O sea\\O urchin\\O ,\\O which\\O was\\O heavenly\\B at\\O sushi\\O rose\\O .\\O"}], "postag": ["PRP", "VBP", "TO", "VB", "DT", "NN", "NN", "IN", "PRP$", "NN", "NN", ",", "WDT", "VBD", "JJ", "IN", "NN", "VBD", "."], "head": [2, 0, 4, 2, 7, 7, 4, 11, 11, 11, 4, 11, 15, 15, 11, 17, 15, 15, 2], "deprel": ["nsubj", "root", "mark", "xcomp", "det", "compound", "obj", "case", "nmod:poss", "compound", "obl", "punct", "nsubj", "cop", "acl:relcl", "case", "obl", "advcl", "punct"]}, {"id": "7", "sentence": "The sushi seemed pretty fresh and was adequately proportioned .", "triples": [{"uid": "7-0", "sentiment": "positive", "target_tags": "The\\O sushi\\B seemed\\O pretty\\O fresh\\O and\\O was\\O adequately\\O proportioned\\O .\\O", "opinion_tags": "The\\O sushi\\O seemed\\O pretty\\O fresh\\B and\\O was\\O adequately\\O proportioned\\O .\\O"}, {"uid": "7-1", "sentiment": "positive", "target_tags": "The\\O sushi\\B seemed\\O pretty\\O fresh\\O and\\O was\\O adequately\\O proportioned\\O .\\O", "opinion_tags": "The\\O sushi\\O seemed\\O pretty\\O fresh\\O and\\O was\\O adequately\\O proportioned\\B .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "VBD", "RB", "VBN", "."], "head": [2, 3, 0, 5, 3, 9, 9, 9, 3, 3], "deprel": ["det", "nsubj", "root", "advmod", "xcomp", "cc", "aux:pass", "advmod", "conj", "punct"]}, {"id": "8", "sentence": "In the evening , this place attracted a well dressed , with it , NY crowd .", "triples": [{"uid": "8-0", "sentiment": "positive", "target_tags": "In\\O the\\O evening\\O ,\\O this\\O place\\O attracted\\O a\\O well\\O dressed\\O ,\\O with\\O it\\O ,\\O NY\\O crowd\\B .\\O", "opinion_tags": "In\\O the\\O evening\\O ,\\O this\\O place\\O attracted\\B a\\O well\\O dressed\\O ,\\O with\\O it\\O ,\\O NY\\O crowd\\O .\\O"}], "postag": ["IN", "DT", "NN", ",", "DT", "NN", "VBD", "DT", "RB", "VBN", ",", "IN", "PRP", ",", "NNP", "NN", "."], "head": [3, 3, 7, 7, 6, 7, 0, 16, 10, 16, 16, 13, 16, 16, 16, 7, 7], "deprel": ["case", "det", "obl", "punct", "det", "nsubj", "root", "det", "advmod", "amod", "punct", "case", "amod", "punct", "compound", "obj", "punct"]}, {"id": "9", "sentence": "The food was well prepared and the service impecable .", "triples": [{"uid": "9-0", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O well\\O prepared\\O and\\O the\\O service\\O impecable\\O .\\O", "opinion_tags": "The\\O food\\O was\\O well\\B prepared\\I and\\O the\\O service\\O impecable\\O .\\O"}, {"uid": "9-1", "sentiment": "positive", "target_tags": "The\\O food\\O was\\O well\\O prepared\\O and\\O the\\O service\\B impecable\\O .\\O", "opinion_tags": "The\\O food\\O was\\O well\\O prepared\\O and\\O the\\O service\\O impecable\\B .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "VBN", "CC", "DT", "NN", "JJ", "."], "head": [2, 5, 5, 5, 0, 9, 8, 9, 5, 5], "deprel": ["det", "nsubj:pass", "aux:pass", "advmod", "root", "cc", "det", "nsubj", "conj", "punct"]}, {"id": "10", "sentence": "The Prix Fixe menu is worth every penny and you get more than enough ( both in quantity AND quality ) .", "triples": [{"uid": "10-0", "sentiment": "positive", "target_tags": "The\\O Prix\\B Fixe\\I menu\\I is\\O worth\\O every\\O penny\\O and\\O you\\O get\\O more\\O than\\O enough\\O (\\O both\\O in\\O quantity\\O AND\\O quality\\O )\\O .\\O", "opinion_tags": "The\\O Prix\\O Fixe\\O menu\\O is\\O worth\\B every\\O penny\\O and\\O you\\O get\\O more\\O than\\O enough\\O (\\O both\\O in\\O quantity\\O AND\\O quality\\O )\\O .\\O"}], "postag": ["DT", "NNP", "NNP", "NN", "VBZ", "JJ", "DT", "NN", "CC", "PRP", "VBP", "JJR", "IN", "JJ", "-LRB-", "CC", "IN", "NN", "CC", "NN", "-RRB-", "."], "head": [4, 3, 4, 6, 6, 0, 8, 6, 11, 11, 6, 14, 12, 11, 18, 18, 18, 11, 20, 18, 18, 6], "deprel": ["det", "compound", "compound", "nsubj", "cop", "root", "det", "obj", "cc", "nsubj", "conj", "advmod", "fixed", "obj", "punct", "cc:preconj", "case", "obl", "cc", "conj", "punct", "punct"]}, {"id": "11", "sentence": "The kitchen however , is almost always slow .", "triples": [{"uid": "11-0", "sentiment": "negative", "target_tags": "The\\O kitchen\\B however\\O ,\\O is\\O almost\\O always\\O slow\\O .\\O", "opinion_tags": "The\\O kitchen\\O however\\O ,\\O is\\O almost\\O always\\O slow\\B .\\O"}], "postag": ["DT", "NN", "RB", ",", "VBZ", "RB", "RB", "JJ", "."], "head": [2, 8, 8, 8, 8, 7, 8, 0, 8], "deprel": ["det", "nsubj", "advmod", "punct", "cop", "advmod", "advmod", "root", "punct"]}, {"id": "12", "sentence": "Add to that great service and great food at a reasonable price and you have yourself the beginning of a great evening .", "triples": [{"uid": "12-0", "sentiment": "positive", "target_tags": "Add\\O to\\O that\\O great\\O service\\B and\\O great\\O food\\O at\\O a\\O reasonable\\O price\\O and\\O you\\O have\\O yourself\\O the\\O beginning\\O of\\O a\\O great\\O evening\\O .\\O", "opinion_tags": "Add\\O to\\O that\\O great\\B service\\O and\\O great\\O food\\O at\\O a\\O reasonable\\O price\\O and\\O you\\O have\\O yourself\\O the\\O beginning\\O of\\O a\\O great\\O evening\\O .\\O"}, {"uid": "12-1", "sentiment": "positive", "target_tags": "Add\\O to\\O that\\O great\\O service\\O and\\O great\\O food\\B at\\O a\\O reasonable\\O price\\O and\\O you\\O have\\O yourself\\O the\\O beginning\\O of\\O a\\O great\\O evening\\O .\\O", "opinion_tags": "Add\\O to\\O that\\O great\\O service\\O and\\O great\\B food\\O at\\O a\\O reasonable\\O price\\O and\\O you\\O have\\O yourself\\O the\\O beginning\\O of\\O a\\O great\\O evening\\O .\\O"}], "postag": ["VB", "IN", "DT", "JJ", "NN", "CC", "JJ", "NN", "IN", "DT", "JJ", "NN", "CC", "PRP", "VBP", "PRP", "DT", "NN", "IN", "DT", "JJ", "NN", "."], "head": [0, 5, 5, 5, 1, 8, 8, 5, 12, 12, 12, 1, 15, 15, 1, 15, 18, 15, 22, 22, 22, 18, 1], "deprel": ["root", "case", "det", "amod", "obl", "cc", "amod", "conj", "case", "det", "amod", "obl", "cc", "nsubj", "conj", "i<PERSON><PERSON>", "det", "obj", "case", "det", "amod", "nmod", "punct"]}, {"id": "13", "sentence": "The pizza was delivered cold and the cheese was n't even fully melted !", "triples": [{"uid": "13-0", "sentiment": "negative", "target_tags": "The\\O pizza\\B was\\O delivered\\O cold\\O and\\O the\\O cheese\\O was\\O n't\\O even\\O fully\\O melted\\O !\\O", "opinion_tags": "The\\O pizza\\O was\\O delivered\\O cold\\B and\\O the\\O cheese\\O was\\O n't\\O even\\O fully\\O melted\\O !\\O"}, {"uid": "13-1", "sentiment": "negative", "target_tags": "The\\O pizza\\O was\\O delivered\\O cold\\O and\\O the\\O cheese\\B was\\O n't\\O even\\O fully\\O melted\\O !\\O", "opinion_tags": "The\\O pizza\\O was\\O delivered\\O cold\\O and\\O the\\O cheese\\O was\\B n't\\I even\\I fully\\I melted\\I !\\O"}], "postag": ["DT", "NN", "VBD", "VBN", "JJ", "CC", "DT", "NN", "VBD", "RB", "RB", "RB", "VBN", "."], "head": [2, 4, 4, 0, 4, 13, 8, 13, 13, 13, 13, 13, 4, 4], "deprel": ["det", "nsubj:pass", "aux:pass", "root", "xcomp", "cc", "det", "nsubj:pass", "aux:pass", "advmod", "advmod", "advmod", "conj", "punct"]}, {"id": "14", "sentence": "Yes , they use fancy ingredients , but even fancy ingredients do n't make for good pizza unless someone knows how to get the crust right .", "triples": [{"uid": "14-0", "sentiment": "positive", "target_tags": "Yes\\O ,\\O they\\O use\\O fancy\\O ingredients\\B ,\\O but\\O even\\O fancy\\O ingredients\\O do\\O n't\\O make\\O for\\O good\\O pizza\\O unless\\O someone\\O knows\\O how\\O to\\O get\\O the\\O crust\\O right\\O .\\O", "opinion_tags": "Yes\\O ,\\O they\\O use\\O fancy\\B ingredients\\O ,\\O but\\O even\\O fancy\\O ingredients\\O do\\O n't\\O make\\O for\\O good\\O pizza\\O unless\\O someone\\O knows\\O how\\O to\\O get\\O the\\O crust\\O right\\O .\\O"}, {"uid": "14-1", "sentiment": "negative", "target_tags": "Yes\\O ,\\O they\\O use\\O fancy\\O ingredients\\O ,\\O but\\O even\\O fancy\\O ingredients\\O do\\O n't\\O make\\O for\\O good\\O pizza\\B unless\\O someone\\O knows\\O how\\O to\\O get\\O the\\O crust\\O right\\O .\\O", "opinion_tags": "Yes\\O ,\\O they\\O use\\O fancy\\O ingredients\\O ,\\O but\\O even\\O fancy\\O ingredients\\O do\\O n't\\O make\\O for\\O good\\B pizza\\O unless\\O someone\\O knows\\O how\\O to\\O get\\O the\\O crust\\O right\\O .\\O"}], "postag": ["UH", ",", "PRP", "VBP", "JJ", "NNS", ",", "CC", "RB", "JJ", "NNS", "VBP", "RB", "VB", "IN", "JJ", "NN", "IN", "NN", "VBZ", "WRB", "TO", "VB", "DT", "NN", "RB", "."], "head": [4, 4, 4, 0, 6, 4, 14, 14, 11, 11, 14, 14, 14, 4, 17, 17, 14, 20, 20, 14, 23, 23, 20, 25, 23, 23, 4], "deprel": ["discourse", "punct", "nsubj", "root", "amod", "obj", "punct", "cc", "advmod", "amod", "nsubj", "aux", "advmod", "conj", "case", "amod", "obl", "mark", "nsubj", "advcl", "mark", "mark", "ccomp", "det", "obj", "advmod", "punct"]}, {"id": "15", "sentence": "Best <PERSON><PERSON>i I ever had and great portion without being ridiculous .", "triples": [{"uid": "15-0", "sentiment": "positive", "target_tags": "Best\\O Pastrami\\B I\\O ever\\O had\\O and\\O great\\O portion\\O without\\O being\\O ridiculous\\O .\\O", "opinion_tags": "Best\\B Pastrami\\O I\\O ever\\O had\\O and\\O great\\O portion\\O without\\O being\\O ridiculous\\O .\\O"}, {"uid": "15-1", "sentiment": "positive", "target_tags": "Best\\O Pastrami\\O I\\O ever\\O had\\O and\\O great\\O portion\\B without\\O being\\O ridiculous\\O .\\O", "opinion_tags": "Best\\O Pastrami\\O I\\O ever\\O had\\O and\\O great\\B portion\\O without\\O being\\O ridiculous\\O .\\O"}], "postag": ["JJS", "NN", "PRP", "RB", "VBD", "CC", "JJ", "NN", "IN", "VBG", "JJ", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 11, 11, 5, 2], "deprel": ["amod", "root", "nsubj", "advmod", "acl:relcl", "cc", "amod", "conj", "mark", "cop", "advcl", "punct"]}, {"id": "16", "sentence": "My wife had the fried shrimp which are huge and loved it .", "triples": [{"uid": "16-0", "sentiment": "positive", "target_tags": "My\\O wife\\O had\\O the\\O fried\\B shrimp\\I which\\O are\\O huge\\O and\\O loved\\O it\\O .\\O", "opinion_tags": "My\\O wife\\O had\\O the\\O fried\\O shrimp\\O which\\O are\\O huge\\B and\\O loved\\O it\\O .\\O"}, {"uid": "16-1", "sentiment": "positive", "target_tags": "My\\O wife\\O had\\O the\\O fried\\B shrimp\\I which\\O are\\O huge\\O and\\O loved\\O it\\O .\\O", "opinion_tags": "My\\O wife\\O had\\O the\\O fried\\O shrimp\\O which\\O are\\O huge\\O and\\O loved\\B it\\O .\\O"}], "postag": ["PRP$", "NN", "VBD", "DT", "JJ", "NN", "WDT", "VBP", "JJ", "CC", "VBD", "PRP", "."], "head": [2, 3, 0, 6, 6, 3, 9, 9, 6, 11, 9, 11, 3], "deprel": ["nmod:poss", "nsubj", "root", "det", "amod", "obj", "nsubj", "cop", "acl:relcl", "cc", "conj", "obj", "punct"]}, {"id": "17", "sentence": "This place is the most Japanese it can ever get .", "triples": [{"uid": "17-0", "sentiment": "positive", "target_tags": "This\\O place\\B is\\O the\\O most\\O Japanese\\O it\\O can\\O ever\\O get\\O .\\O", "opinion_tags": "This\\O place\\O is\\O the\\O most\\O Japanese\\B it\\O can\\O ever\\O get\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "RBS", "JJ", "PRP", "MD", "RB", "VB", "."], "head": [2, 6, 6, 6, 6, 0, 10, 10, 10, 6, 6], "deprel": ["det", "nsubj", "cop", "det", "advmod", "root", "nsubj", "aux", "advmod", "ccomp", "punct"]}, {"id": "18", "sentence": "<PERSON> is an East Village gem : casual but hip , with well prepared basic French bistro fare , good specials , a warm and lively atmosphere .", "triples": [{"uid": "18-0", "sentiment": "positive", "target_tags": "Leon\\B is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O", "opinion_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\B but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O"}, {"uid": "18-1", "sentiment": "positive", "target_tags": "Leon\\B is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O", "opinion_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\B ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O"}, {"uid": "18-2", "sentiment": "positive", "target_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\B ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O", "opinion_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\B specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O"}, {"uid": "18-3", "sentiment": "positive", "target_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\B .\\O", "opinion_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\B and\\O lively\\O atmosphere\\O .\\O"}, {"uid": "18-4", "sentiment": "positive", "target_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\B .\\O", "opinion_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\B atmosphere\\O .\\O"}, {"uid": "18-5", "sentiment": "positive", "target_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\B bistro\\I fare\\I ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O", "opinion_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\B prepared\\I basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O"}], "postag": ["NNP", "VBZ", "DT", "NNP", "NNP", "NN", ":", "JJ", "CC", "NN", ",", "IN", "RB", "VBN", "JJ", "JJ", "NN", "NN", ",", "JJ", "NNS", ",", "DT", "JJ", "CC", "JJ", "NN", "."], "head": [6, 6, 6, 5, 6, 0, 6, 6, 10, 8, 18, 18, 14, 18, 18, 18, 18, 8, 21, 21, 18, 27, 27, 27, 26, 24, 18, 6], "deprel": ["nsubj", "cop", "det", "compound", "compound", "root", "punct", "amod", "cc", "conj", "punct", "case", "advmod", "amod", "amod", "amod", "compound", "obl", "punct", "amod", "conj", "punct", "det", "amod", "cc", "conj", "conj", "punct"]}, {"id": "19", "sentence": "The food was bland oily .", "triples": [{"uid": "19-0", "sentiment": "negative", "target_tags": "The\\O food\\B was\\O bland\\O oily\\O .\\O", "opinion_tags": "The\\O food\\O was\\O bland\\B oily\\I .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "amod", "root", "punct"]}, {"id": "20", "sentence": "I went there for lunch and it was not as good as I expected from the reviews I read .", "triples": [{"uid": "20-0", "sentiment": "negative", "target_tags": "I\\O went\\O there\\O for\\O lunch\\B and\\O it\\O was\\O not\\O as\\O good\\O as\\O I\\O expected\\O from\\O the\\O reviews\\O I\\O read\\O .\\O", "opinion_tags": "I\\O went\\O there\\O for\\O lunch\\O and\\O it\\O was\\O not\\B as\\I good\\I as\\I I\\I expected\\I from\\O the\\O reviews\\O I\\O read\\O .\\O"}], "postag": ["PRP", "VBD", "RB", "IN", "NN", "CC", "PRP", "VBD", "RB", "RB", "JJ", "IN", "PRP", "VBD", "IN", "DT", "NNS", "PRP", "VBD", "."], "head": [2, 0, 2, 5, 2, 11, 11, 11, 11, 11, 2, 14, 14, 11, 17, 17, 14, 19, 17, 2], "deprel": ["nsubj", "root", "advmod", "case", "obl", "cc", "nsubj", "cop", "advmod", "advmod", "conj", "mark", "nsubj", "advcl", "case", "det", "obl", "nsubj", "acl:relcl", "punct"]}, {"id": "21", "sentence": "This place is great .", "triples": [{"uid": "21-0", "sentiment": "positive", "target_tags": "This\\O place\\B is\\O great\\O .\\O", "opinion_tags": "This\\O place\\O is\\O great\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"]}, {"id": "22", "sentence": "Great pizza and fantastic service .", "triples": [{"uid": "22-0", "sentiment": "positive", "target_tags": "Great\\O pizza\\B and\\O fantastic\\O service\\O .\\O", "opinion_tags": "Great\\B pizza\\O and\\O fantastic\\O service\\O .\\O"}, {"uid": "22-1", "sentiment": "positive", "target_tags": "Great\\O pizza\\O and\\O fantastic\\O service\\B .\\O", "opinion_tags": "Great\\O pizza\\O and\\O fantastic\\B service\\O .\\O"}], "postag": ["JJ", "NN", "CC", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["amod", "root", "cc", "amod", "conj", "punct"]}, {"id": "23", "sentence": "The place is small and cramped but the food is fantastic .", "triples": [{"uid": "23-0", "sentiment": "negative", "target_tags": "The\\O place\\B is\\O small\\O and\\O cramped\\O but\\O the\\O food\\O is\\O fantastic\\O .\\O", "opinion_tags": "The\\O place\\O is\\O small\\B and\\O cramped\\O but\\O the\\O food\\O is\\O fantastic\\O .\\O"}, {"uid": "23-1", "sentiment": "negative", "target_tags": "The\\O place\\B is\\O small\\O and\\O cramped\\O but\\O the\\O food\\O is\\O fantastic\\O .\\O", "opinion_tags": "The\\O place\\O is\\O small\\O and\\O cramped\\B but\\O the\\O food\\O is\\O fantastic\\O .\\O"}, {"uid": "23-2", "sentiment": "positive", "target_tags": "The\\O place\\O is\\O small\\O and\\O cramped\\O but\\O the\\O food\\B is\\O fantastic\\O .\\O", "opinion_tags": "The\\O place\\O is\\O small\\O and\\O cramped\\O but\\O the\\O food\\O is\\O fantastic\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 11, 9, 11, 11, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "24", "sentence": "Moules were excellent , lobster ravioli was VERY salty !", "triples": [{"uid": "24-0", "sentiment": "positive", "target_tags": "Moules\\B were\\O excellent\\O ,\\O lobster\\O ravioli\\O was\\O VERY\\O salty\\O !\\O", "opinion_tags": "Moules\\O were\\O excellent\\B ,\\O lobster\\O ravioli\\O was\\O VERY\\O salty\\O !\\O"}, {"uid": "24-1", "sentiment": "negative", "target_tags": "Moules\\O were\\O excellent\\O ,\\O lobster\\B ravioli\\I was\\O VERY\\O salty\\O !\\O", "opinion_tags": "Moules\\O were\\O excellent\\O ,\\O lobster\\O ravioli\\O was\\O VERY\\O salty\\B !\\O"}], "postag": ["NNS", "VBD", "JJ", ",", "NN", "NN", "VBD", "RB", "JJ", "."], "head": [3, 3, 0, 3, 6, 9, 9, 9, 3, 3], "deprel": ["nsubj", "cop", "root", "punct", "compound", "nsubj", "cop", "advmod", "conj", "punct"]}, {"id": "25", "sentence": "Service is not exactly five star , but thats not really a big deal .", "triples": [{"uid": "25-0", "sentiment": "neutral", "target_tags": "Service\\B is\\O not\\O exactly\\O five\\O star\\O ,\\O but\\O thats\\O not\\O really\\O a\\O big\\O deal\\O .\\O", "opinion_tags": "Service\\O is\\O not\\B exactly\\I five\\I star\\I ,\\O but\\O thats\\O not\\O really\\O a\\O big\\O deal\\O .\\O"}], "postag": ["NN", "VBZ", "RB", "RB", "CD", "NN", ",", "CC", "VBZ", "RB", "RB", "DT", "JJ", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 14, 14, 14, 14, 14, 14, 14, 6, 6], "deprel": ["nsubj", "cop", "advmod", "advmod", "nummod", "root", "punct", "cc", "cop", "advmod", "advmod", "det", "amod", "conj", "punct"]}, {"id": "26", "sentence": "Downstairs lounge is always a good attraction", "triples": [{"uid": "26-0", "sentiment": "positive", "target_tags": "Downstairs\\B lounge\\I is\\O always\\O a\\O good\\O attraction\\O", "opinion_tags": "Downstairs\\O lounge\\O is\\O always\\O a\\O good\\B attraction\\I"}], "postag": ["JJ", "NN", "VBZ", "RB", "DT", "JJ", "NN"], "head": [2, 7, 7, 7, 7, 7, 0], "deprel": ["amod", "nsubj", "cop", "advmod", "det", "amod", "root"]}, {"id": "27", "sentence": "The staff is incredibly helpful and attentive .", "triples": [{"uid": "27-0", "sentiment": "positive", "target_tags": "The\\O staff\\B is\\O incredibly\\O helpful\\O and\\O attentive\\O .\\O", "opinion_tags": "The\\O staff\\O is\\O incredibly\\O helpful\\B and\\O attentive\\O .\\O"}, {"uid": "27-1", "sentiment": "positive", "target_tags": "The\\O staff\\B is\\O incredibly\\O helpful\\O and\\O attentive\\O .\\O", "opinion_tags": "The\\O staff\\O is\\O incredibly\\O helpful\\O and\\O attentive\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"]}, {"id": "28", "sentence": "Rude service , medicore food ... there are tons of restaurants in NY ... stay away from this one", "triples": [{"uid": "28-0", "sentiment": "negative", "target_tags": "Rude\\O service\\B ,\\O medicore\\O food\\O ...\\O there\\O are\\O tons\\O of\\O restaurants\\O in\\O NY\\O ...\\O stay\\O away\\O from\\O this\\O one\\O", "opinion_tags": "Rude\\B service\\O ,\\O medicore\\O food\\O ...\\O there\\O are\\O tons\\O of\\O restaurants\\O in\\O NY\\O ...\\O stay\\O away\\O from\\O this\\O one\\O"}, {"uid": "28-1", "sentiment": "neutral", "target_tags": "Rude\\O service\\O ,\\O medicore\\O food\\B ...\\O there\\O are\\O tons\\O of\\O restaurants\\O in\\O NY\\O ...\\O stay\\O away\\O from\\O this\\O one\\O", "opinion_tags": "Rude\\O service\\O ,\\O medicore\\B food\\O ...\\O there\\O are\\O tons\\O of\\O restaurants\\O in\\O NY\\O ...\\O stay\\O away\\O from\\O this\\O one\\O"}], "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "EX", "VBP", "NNS", "IN", "NNS", "IN", "NNP", ",", "VB", "RB", "IN", "DT", "NN"], "head": [2, 0, 2, 5, 2, 2, 8, 2, 8, 11, 9, 13, 11, 2, 2, 15, 19, 19, 15], "deprel": ["amod", "root", "punct", "amod", "list", "punct", "expl", "parataxis", "nsubj", "case", "nmod", "case", "nmod", "punct", "parataxis", "advmod", "case", "det", "obl"]}, {"id": "29", "sentence": "I had a great time at Jekyll and Hyde !", "triples": [{"uid": "29-0", "sentiment": "positive", "target_tags": "I\\O had\\O a\\O great\\O time\\O at\\O Jekyll\\B and\\I Hyde\\I !\\O", "opinion_tags": "I\\O had\\O a\\O great\\B time\\I at\\O Jekyll\\O and\\O Hyde\\O !\\O"}], "postag": ["PRP", "VBD", "DT", "JJ", "NN", "IN", "NNP", "CC", "NNP", "."], "head": [2, 0, 5, 5, 2, 7, 5, 9, 7, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "case", "nmod", "cc", "conj", "punct"]}, {"id": "30", "sentence": "The food was good too .", "triples": [{"uid": "30-0", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O good\\O too\\O .\\O", "opinion_tags": "The\\O food\\O was\\O good\\B too\\O .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "RB", "."], "head": [2, 4, 4, 0, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "advmod", "punct"]}, {"id": "31", "sentence": "The tuna and wasabe potatoes are excellent .", "triples": [{"uid": "31-0", "sentiment": "positive", "target_tags": "The\\O tuna\\B and\\O wasabe\\O potatoes\\O are\\O excellent\\O .\\O", "opinion_tags": "The\\O tuna\\O and\\O wasabe\\O potatoes\\O are\\O excellent\\B .\\O"}, {"uid": "31-1", "sentiment": "positive", "target_tags": "The\\O tuna\\O and\\O wasabe\\B potatoes\\I are\\O excellent\\O .\\O", "opinion_tags": "The\\O tuna\\O and\\O wasabe\\O potatoes\\O are\\O excellent\\B .\\O"}], "postag": ["DT", "NN", "CC", "NN", "NNS", "VBP", "JJ", "."], "head": [5, 5, 4, 2, 7, 7, 0, 7], "deprel": ["det", "compound", "cc", "conj", "nsubj", "cop", "root", "punct"]}, {"id": "32", "sentence": "Great service , great food .", "triples": [{"uid": "32-0", "sentiment": "positive", "target_tags": "Great\\O service\\B ,\\O great\\O food\\O .\\O", "opinion_tags": "Great\\B service\\O ,\\O great\\O food\\O .\\O"}, {"uid": "32-1", "sentiment": "positive", "target_tags": "Great\\O service\\O ,\\O great\\O food\\B .\\O", "opinion_tags": "Great\\O service\\O ,\\O great\\B food\\O .\\O"}], "postag": ["JJ", "NN", ",", "JJ", "NN", "."], "head": [2, 0, 2, 5, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct"]}, {"id": "33", "sentence": "The pizza is delicious - they use fresh mozzarella instead of the cheap , frozen , shredded cheese common to most pizzaria 's .", "triples": [{"uid": "33-0", "sentiment": "positive", "target_tags": "The\\O pizza\\B is\\O delicious\\O -\\O they\\O use\\O fresh\\O mozzarella\\O instead\\O of\\O the\\O cheap\\O ,\\O frozen\\O ,\\O shredded\\O cheese\\O common\\O to\\O most\\O pizzaria\\O 's\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O delicious\\B -\\O they\\O use\\O fresh\\O mozzarella\\O instead\\O of\\O the\\O cheap\\O ,\\O frozen\\O ,\\O shredded\\O cheese\\O common\\O to\\O most\\O pizzaria\\O 's\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ",", "PRP", "VBP", "JJ", "NN", "RB", "IN", "DT", "JJ", ",", "JJ", ",", "VBN", "NN", "JJ", "IN", "JJS", "NN", "POS", "."], "head": [2, 4, 4, 0, 4, 7, 4, 9, 7, 18, 10, 18, 18, 15, 18, 18, 18, 7, 18, 22, 22, 19, 22, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "parataxis", "amod", "obj", "case", "fixed", "det", "amod", "punct", "amod", "punct", "amod", "obl", "amod", "case", "amod", "obl", "case", "punct"]}, {"id": "34", "sentence": "He served me an Uni Hand roll , which I never had before , and let me tell you ... IT WAS HEAVEN !", "triples": [{"uid": "34-0", "sentiment": "positive", "target_tags": "He\\O served\\O me\\O an\\O Uni\\B Hand\\I roll\\I ,\\O which\\O I\\O never\\O had\\O before\\O ,\\O and\\O let\\O me\\O tell\\O you\\O ...\\O IT\\O WAS\\O HEAVEN\\O !\\O", "opinion_tags": "He\\O served\\O me\\O an\\O Uni\\O Hand\\O roll\\O ,\\O which\\O I\\O never\\O had\\O before\\O ,\\O and\\O let\\O me\\O tell\\O you\\O ...\\O IT\\O WAS\\O HEAVEN\\B !\\O"}], "postag": ["PRP", "VBD", "PRP", "DT", "NNP", "NNP", "NN", ",", "WDT", "PRP", "RB", "VBD", "RB", ",", "CC", "VBD", "PRP", "VB", "PRP", ",", "PRP", "VBD", "NN", "."], "head": [2, 0, 2, 7, 6, 7, 2, 7, 12, 12, 12, 7, 12, 16, 16, 2, 16, 16, 18, 2, 23, 23, 2, 2], "deprel": ["nsubj", "root", "i<PERSON><PERSON>", "det", "compound", "compound", "obj", "punct", "obj", "nsubj", "advmod", "acl:relcl", "advmod", "punct", "cc", "conj", "obj", "xcomp", "obj", "punct", "nsubj", "cop", "parataxis", "punct"]}, {"id": "35", "sentence": "My husband and I thougt it would be great to go to the Jekyll and Hyde Pub for our anniversary , and to our surprise it was fantastic .", "triples": [{"uid": "35-0", "sentiment": "positive", "target_tags": "My\\O husband\\O and\\O I\\O thougt\\O it\\O would\\O be\\O great\\O to\\O go\\O to\\O the\\O Jekyll\\B and\\I Hyde\\I Pub\\I for\\O our\\O anniversary\\O ,\\O and\\O to\\O our\\O surprise\\O it\\O was\\O fantastic\\O .\\O", "opinion_tags": "My\\O husband\\O and\\O I\\O thougt\\O it\\O would\\O be\\O great\\B to\\O go\\O to\\O the\\O Jekyll\\O and\\O Hyde\\O Pub\\O for\\O our\\O anniversary\\O ,\\O and\\O to\\O our\\O surprise\\O it\\O was\\O fantastic\\O .\\O"}, {"uid": "35-1", "sentiment": "positive", "target_tags": "My\\O husband\\O and\\O I\\O thougt\\O it\\O would\\O be\\O great\\O to\\O go\\O to\\O the\\O Jekyll\\B and\\I Hyde\\I Pub\\I for\\O our\\O anniversary\\O ,\\O and\\O to\\O our\\O surprise\\O it\\O was\\O fantastic\\O .\\O", "opinion_tags": "My\\O husband\\O and\\O I\\O thougt\\O it\\O would\\O be\\O great\\O to\\O go\\O to\\O the\\O Jekyll\\O and\\O Hyde\\O Pub\\O for\\O our\\O anniversary\\O ,\\O and\\O to\\O our\\O surprise\\O it\\O was\\O fantastic\\B .\\O"}], "postag": ["PRP$", "NN", "CC", "PRP", "VBD", "PRP", "MD", "VB", "JJ", "TO", "VB", "IN", "DT", "NNP", "CC", "NNP", "NNP", "IN", "PRP$", "NN", ",", "CC", "IN", "PRP$", "NN", "PRP", "VBD", "JJ", "."], "head": [2, 5, 4, 2, 0, 9, 9, 9, 5, 11, 9, 14, 14, 11, 17, 17, 14, 20, 20, 11, 28, 28, 25, 25, 28, 28, 28, 5, 5], "deprel": ["nmod:poss", "nsubj", "cc", "conj", "root", "expl", "aux", "cop", "ccomp", "mark", "csubj", "case", "det", "obl", "cc", "compound", "conj", "case", "nmod:poss", "obl", "punct", "cc", "case", "nmod:poss", "obl", "nsubj", "cop", "conj", "punct"]}, {"id": "36", "sentence": "This was my frist time at Cafe St. Bart 's and I must say how delicious the food and the service was .", "triples": [{"uid": "36-0", "sentiment": "positive", "target_tags": "This\\O was\\O my\\O frist\\O time\\O at\\O Cafe\\O St.\\O Bart\\O 's\\O and\\O I\\O must\\O say\\O how\\O delicious\\O the\\O food\\B and\\O the\\O service\\O was\\O .\\O", "opinion_tags": "This\\O was\\O my\\O frist\\O time\\O at\\O Cafe\\O St.\\O Bart\\O 's\\O and\\O I\\O must\\O say\\O how\\O delicious\\B the\\O food\\O and\\O the\\O service\\O was\\O .\\O"}], "postag": ["DT", "VBD", "PRP$", "JJ", "NN", "IN", "NNP", "NNP", "NNP", "POS", "CC", "PRP", "MD", "VB", "WRB", "JJ", "DT", "NN", "CC", "DT", "NN", "VBD", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 8, 8, 14, 14, 14, 5, 16, 14, 18, 16, 21, 21, 18, 16, 5], "deprel": ["nsubj", "cop", "nmod:poss", "amod", "root", "case", "compound", "nmod", "flat", "case", "cc", "nsubj", "aux", "conj", "mark", "ccomp", "det", "nsubj", "cc", "det", "conj", "cop", "punct"]}, {"id": "37", "sentence": "I have to highly recommend the lobster roll - not to much mayo ; you can tell it was a fresh lobster .", "triples": [{"uid": "37-0", "sentiment": "positive", "target_tags": "I\\O have\\O to\\O highly\\O recommend\\O the\\O lobster\\B roll\\I -\\O not\\O to\\O much\\O mayo\\O ;\\O you\\O can\\O tell\\O it\\O was\\O a\\O fresh\\O lobster\\O .\\O", "opinion_tags": "I\\O have\\O to\\O highly\\O recommend\\B the\\O lobster\\O roll\\O -\\O not\\O to\\O much\\O mayo\\O ;\\O you\\O can\\O tell\\O it\\O was\\O a\\O fresh\\O lobster\\O .\\O"}, {"uid": "37-1", "sentiment": "positive", "target_tags": "I\\O have\\O to\\O highly\\O recommend\\O the\\O lobster\\O roll\\O -\\O not\\O to\\O much\\O mayo\\O ;\\O you\\O can\\O tell\\O it\\O was\\O a\\O fresh\\O lobster\\B .\\O", "opinion_tags": "I\\O have\\O to\\O highly\\O recommend\\O the\\O lobster\\O roll\\O -\\O not\\O to\\O much\\O mayo\\O ;\\O you\\O can\\O tell\\O it\\O was\\O a\\O fresh\\B lobster\\O .\\O"}], "postag": ["PRP", "VBP", "TO", "RB", "VB", "DT", "NN", "NN", ",", "RB", "IN", "JJ", "NN", ",", "PRP", "MD", "VB", "PRP", "VBD", "DT", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 8, 13, 13, 13, 8, 2, 17, 17, 2, 22, 22, 22, 22, 17, 2], "deprel": ["nsubj", "root", "mark", "advmod", "xcomp", "det", "compound", "obj", "punct", "advmod", "case", "amod", "nmod", "punct", "nsubj", "aux", "parataxis", "nsubj", "cop", "det", "amod", "ccomp", "punct"]}, {"id": "38", "sentence": "All the staff is absolutely professional ! !", "triples": [{"uid": "38-0", "sentiment": "positive", "target_tags": "All\\O the\\O staff\\B is\\O absolutely\\O professional\\O !\\O !\\O", "opinion_tags": "All\\O the\\O staff\\O is\\O absolutely\\O professional\\B !\\O !\\O"}], "postag": ["PDT", "DT", "NN", "VBZ", "RB", "JJ", ".", "."], "head": [3, 3, 6, 6, 6, 0, 6, 6], "deprel": ["det:predet", "det", "nsubj", "cop", "advmod", "root", "punct", "punct"]}, {"id": "39", "sentence": "This restaurant was way overhyped .", "triples": [{"uid": "39-0", "sentiment": "negative", "target_tags": "This\\O restaurant\\B was\\O way\\O overhyped\\O .\\O", "opinion_tags": "This\\O restaurant\\O was\\O way\\O overhyped\\B .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"]}, {"id": "40", "sentence": "It 's boring on the inside , and our sushi was pretty below average ... the tuna was soggy and the other rolls had no flavor .", "triples": [{"uid": "40-0", "sentiment": "negative", "target_tags": "It\\O 's\\O boring\\O on\\O the\\O inside\\O ,\\O and\\O our\\O sushi\\B was\\O pretty\\O below\\O average\\O ...\\O the\\O tuna\\O was\\O soggy\\O and\\O the\\O other\\O rolls\\O had\\O no\\O flavor\\O .\\O", "opinion_tags": "It\\O 's\\O boring\\O on\\O the\\O inside\\O ,\\O and\\O our\\O sushi\\O was\\O pretty\\O below\\B average\\I ...\\O the\\O tuna\\O was\\O soggy\\O and\\O the\\O other\\O rolls\\O had\\O no\\O flavor\\O .\\O"}, {"uid": "40-1", "sentiment": "negative", "target_tags": "It\\O 's\\O boring\\O on\\O the\\O inside\\O ,\\O and\\O our\\O sushi\\O was\\O pretty\\O below\\O average\\O ...\\O the\\O tuna\\B was\\O soggy\\O and\\O the\\O other\\O rolls\\O had\\O no\\O flavor\\O .\\O", "opinion_tags": "It\\O 's\\O boring\\O on\\O the\\O inside\\O ,\\O and\\O our\\O sushi\\O was\\O pretty\\O below\\O average\\O ...\\O the\\O tuna\\O was\\O soggy\\B and\\O the\\O other\\O rolls\\O had\\O no\\O flavor\\O .\\O"}, {"uid": "40-2", "sentiment": "negative", "target_tags": "It\\O 's\\O boring\\O on\\O the\\O inside\\O ,\\O and\\O our\\O sushi\\O was\\O pretty\\O below\\O average\\O ...\\O the\\O tuna\\O was\\O soggy\\O and\\O the\\O other\\O rolls\\B had\\O no\\O flavor\\O .\\O", "opinion_tags": "It\\O 's\\O boring\\O on\\O the\\O inside\\O ,\\O and\\O our\\O sushi\\O was\\O pretty\\O below\\O average\\O ...\\O the\\O tuna\\O was\\O soggy\\O and\\O the\\O other\\O rolls\\O had\\O no\\B flavor\\I .\\O"}], "postag": ["PRP", "VBZ", "JJ", "IN", "DT", "NN", ",", "CC", "PRP$", "NN", "VBD", "RB", "RB", "JJ", ",", "DT", "NN", "VBD", "JJ", "CC", "DT", "JJ", "NNS", "VBD", "DT", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 13, 13, 10, 13, 13, 13, 3, 13, 3, 17, 19, 19, 3, 24, 23, 23, 24, 19, 26, 24, 3], "deprel": ["nsubj", "cop", "root", "case", "det", "obl", "punct", "cc", "nmod:poss", "nsubj", "cop", "advmod", "conj", "amod", "punct", "det", "nsubj", "cop", "parataxis", "cc", "det", "amod", "nsubj", "conj", "det", "obj", "punct"]}, {"id": "41", "sentence": "Their pad penang is delicious and everything else is fantastic .", "triples": [{"uid": "41-0", "sentiment": "positive", "target_tags": "Their\\O pad\\B penang\\I is\\O delicious\\O and\\O everything\\O else\\O is\\O fantastic\\O .\\O", "opinion_tags": "Their\\O pad\\O penang\\O is\\O delicious\\B and\\O everything\\O else\\O is\\O fantastic\\O .\\O"}], "postag": ["PRP$", "NN", "NN", "VBZ", "JJ", "CC", "NN", "JJ", "VBZ", "JJ", "."], "head": [3, 3, 5, 5, 0, 10, 10, 7, 10, 5, 5], "deprel": ["nmod:poss", "compound", "nsubj", "cop", "root", "cc", "nsubj", "amod", "cop", "conj", "punct"]}, {"id": "42", "sentence": "The price is reasonable although the service is poor .", "triples": [{"uid": "42-0", "sentiment": "negative", "target_tags": "The\\O price\\O is\\O reasonable\\O although\\O the\\O service\\B is\\O poor\\O .\\O", "opinion_tags": "The\\O price\\O is\\O reasonable\\O although\\O the\\O service\\O is\\O poor\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "IN", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "mark", "det", "nsubj", "cop", "advcl", "punct"]}, {"id": "43", "sentence": "The spicy Tuna roll is huge and probably the best that I 've had at this price range .", "triples": [{"uid": "43-0", "sentiment": "positive", "target_tags": "The\\O spicy\\B Tuna\\I roll\\I is\\O huge\\O and\\O probably\\O the\\O best\\O that\\O I\\O 've\\O had\\O at\\O this\\O price\\O range\\O .\\O", "opinion_tags": "The\\O spicy\\O Tuna\\O roll\\O is\\O huge\\B and\\O probably\\O the\\O best\\O that\\O I\\O 've\\O had\\O at\\O this\\O price\\O range\\O .\\O"}, {"uid": "43-1", "sentiment": "positive", "target_tags": "The\\O spicy\\B Tuna\\I roll\\I is\\O huge\\O and\\O probably\\O the\\O best\\O that\\O I\\O 've\\O had\\O at\\O this\\O price\\O range\\O .\\O", "opinion_tags": "The\\O spicy\\O Tuna\\O roll\\O is\\O huge\\O and\\O probably\\O the\\O best\\B that\\O I\\O 've\\O had\\O at\\O this\\O price\\O range\\O .\\O"}], "postag": ["DT", "JJ", "NN", "NN", "VBZ", "JJ", "CC", "RB", "DT", "JJS", "WDT", "PRP", "VBP", "VBN", "IN", "DT", "NN", "NN", "."], "head": [4, 4, 4, 6, 6, 0, 10, 10, 10, 6, 14, 14, 14, 10, 18, 18, 18, 14, 6], "deprel": ["det", "amod", "compound", "nsubj", "cop", "root", "cc", "advmod", "det", "conj", "obj", "nsubj", "aux", "acl:relcl", "case", "det", "compound", "obl", "punct"]}, {"id": "44", "sentence": "The staff was accomodating , the food was absolutely delicious and the place is lovely .", "triples": [{"uid": "44-0", "sentiment": "positive", "target_tags": "The\\O staff\\B was\\O accomodating\\O ,\\O the\\O food\\O was\\O absolutely\\O delicious\\O and\\O the\\O place\\O is\\O lovely\\O .\\O", "opinion_tags": "The\\O staff\\O was\\O accomodating\\B ,\\O the\\O food\\O was\\O absolutely\\O delicious\\O and\\O the\\O place\\O is\\O lovely\\O .\\O"}, {"uid": "44-1", "sentiment": "positive", "target_tags": "The\\O staff\\O was\\O accomodating\\O ,\\O the\\O food\\B was\\O absolutely\\O delicious\\O and\\O the\\O place\\O is\\O lovely\\O .\\O", "opinion_tags": "The\\O staff\\O was\\O accomodating\\O ,\\O the\\O food\\O was\\O absolutely\\O delicious\\B and\\O the\\O place\\O is\\O lovely\\O .\\O"}, {"uid": "44-2", "sentiment": "positive", "target_tags": "The\\O staff\\O was\\O accomodating\\O ,\\O the\\O food\\O was\\O absolutely\\O delicious\\O and\\O the\\O place\\B is\\O lovely\\O .\\O", "opinion_tags": "The\\O staff\\O was\\O accomodating\\O ,\\O the\\O food\\O was\\O absolutely\\O delicious\\O and\\O the\\O place\\O is\\O lovely\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "RB", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 10, 7, 10, 10, 10, 4, 15, 13, 15, 15, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "advmod", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "45", "sentence": "The food is authentic Italian - delicious !", "triples": [{"uid": "45-0", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O authentic\\O Italian\\O -\\O delicious\\O !\\O", "opinion_tags": "The\\O food\\O is\\O authentic\\B Italian\\I -\\O delicious\\O !\\O"}, {"uid": "45-1", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O authentic\\O Italian\\O -\\O delicious\\O !\\O", "opinion_tags": "The\\O food\\O is\\O authentic\\O Italian\\O -\\O delicious\\B !\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "JJ", ",", "JJ", "."], "head": [2, 7, 7, 5, 7, 7, 0, 7], "deprel": ["det", "nsubj", "cop", "amod", "amod", "punct", "root", "punct"]}, {"id": "46", "sentence": "I 'm still mad that i had to pay for lousy food .", "triples": [{"uid": "46-0", "sentiment": "negative", "target_tags": "I\\O 'm\\O still\\O mad\\O that\\O i\\O had\\O to\\O pay\\O for\\O lousy\\O food\\B .\\O", "opinion_tags": "I\\O 'm\\O still\\O mad\\O that\\O i\\O had\\O to\\O pay\\O for\\O lousy\\B food\\O .\\O"}], "postag": ["PRP", "VBP", "RB", "JJ", "IN", "PRP", "VBD", "TO", "VB", "IN", "JJ", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 9, 7, 12, 12, 9, 4], "deprel": ["nsubj", "cop", "advmod", "root", "mark", "nsubj", "ccomp", "mark", "xcomp", "case", "amod", "obl", "punct"]}, {"id": "47", "sentence": "Very affordable and excellent ambient !", "triples": [{"uid": "47-0", "sentiment": "positive", "target_tags": "Very\\O affordable\\O and\\O excellent\\O ambient\\B !\\O", "opinion_tags": "Very\\O affordable\\B and\\O excellent\\O ambient\\O !\\O"}, {"uid": "47-1", "sentiment": "positive", "target_tags": "Very\\O affordable\\O and\\O excellent\\O ambient\\B !\\O", "opinion_tags": "Very\\O affordable\\O and\\O excellent\\B ambient\\O !\\O"}], "postag": ["RB", "JJ", "CC", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["advmod", "root", "cc", "amod", "conj", "punct"]}, {"id": "48", "sentence": "We wo n't go to this place again for a good meal .", "triples": [{"uid": "48-0", "sentiment": "negative", "target_tags": "We\\O wo\\O n't\\O go\\O to\\O this\\O place\\O again\\O for\\O a\\O good\\O meal\\B .\\O", "opinion_tags": "We\\O wo\\O n't\\O go\\O to\\O this\\O place\\O again\\O for\\O a\\O good\\B meal\\O .\\O"}], "postag": ["PRP", "MD", "RB", "VB", "IN", "DT", "NN", "RB", "IN", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 4, 12, 12, 12, 4, 4], "deprel": ["nsubj", "aux", "advmod", "root", "case", "det", "obl", "advmod", "case", "det", "amod", "obl", "punct"]}, {"id": "49", "sentence": "However , I think this place is a good hang out spot .", "triples": [{"uid": "49-0", "sentiment": "positive", "target_tags": "However\\O ,\\O I\\O think\\O this\\O place\\B is\\O a\\O good\\O hang\\O out\\O spot\\O .\\O", "opinion_tags": "However\\O ,\\O I\\O think\\O this\\O place\\O is\\O a\\O good\\B hang\\O out\\O spot\\O .\\O"}], "postag": ["RB", ",", "PRP", "VBP", "DT", "NN", "VBZ", "DT", "JJ", "NN", "RP", "NN", "."], "head": [4, 4, 4, 0, 6, 12, 12, 12, 12, 12, 10, 4, 4], "deprel": ["advmod", "punct", "nsubj", "root", "det", "nsubj", "cop", "det", "amod", "amod", "compound:prt", "ccomp", "punct"]}, {"id": "50", "sentence": "Cute place , nice wait staff but would never go there again .", "triples": [{"uid": "50-0", "sentiment": "positive", "target_tags": "Cute\\O place\\O ,\\O nice\\O wait\\B staff\\I but\\O would\\O never\\O go\\O there\\O again\\O .\\O", "opinion_tags": "Cute\\O place\\O ,\\O nice\\B wait\\O staff\\O but\\O would\\O never\\O go\\O there\\O again\\O .\\O"}, {"uid": "50-1", "sentiment": "negative", "target_tags": "Cute\\O place\\B ,\\O nice\\O wait\\O staff\\O but\\O would\\O never\\O go\\O there\\O again\\O .\\O", "opinion_tags": "Cute\\B place\\O ,\\O nice\\O wait\\O staff\\O but\\O would\\O never\\O go\\O there\\O again\\O .\\O"}], "postag": ["JJ", "NN", ",", "JJ", "NN", "NN", "CC", "MD", "RB", "VB", "RB", "RB", "."], "head": [2, 0, 6, 6, 6, 2, 10, 10, 10, 2, 10, 10, 2], "deprel": ["amod", "root", "punct", "amod", "compound", "conj", "cc", "aux", "advmod", "conj", "advmod", "advmod", "punct"]}, {"id": "51", "sentence": "Someone else recommended the dessert - we also left that .", "triples": [{"uid": "51-0", "sentiment": "negative", "target_tags": "Someone\\O else\\O recommended\\O the\\O dessert\\B -\\O we\\O also\\O left\\O that\\O .\\O", "opinion_tags": "Someone\\O else\\O recommended\\B the\\O dessert\\O -\\O we\\O also\\O left\\O that\\O .\\O"}], "postag": ["NN", "JJ", "VBD", "DT", "NN", ",", "PRP", "RB", "VBD", "DT", "."], "head": [3, 1, 0, 5, 3, 3, 9, 9, 3, 9, 3], "deprel": ["nsubj", "amod", "root", "det", "obj", "punct", "nsubj", "advmod", "parataxis", "obj", "punct"]}, {"id": "52", "sentence": "I 've never had bad service and the fish is fresh and delicious .", "triples": [{"uid": "52-0", "sentiment": "positive", "target_tags": "I\\O 've\\O never\\O had\\O bad\\O service\\B and\\O the\\O fish\\O is\\O fresh\\O and\\O delicious\\O .\\O", "opinion_tags": "I\\O 've\\O never\\B had\\I bad\\I service\\O and\\O the\\O fish\\O is\\O fresh\\O and\\O delicious\\O .\\O"}, {"uid": "52-1", "sentiment": "positive", "target_tags": "I\\O 've\\O never\\O had\\O bad\\O service\\O and\\O the\\O fish\\B is\\O fresh\\O and\\O delicious\\O .\\O", "opinion_tags": "I\\O 've\\O never\\O had\\O bad\\O service\\O and\\O the\\O fish\\O is\\O fresh\\B and\\O delicious\\O .\\O"}, {"uid": "52-2", "sentiment": "positive", "target_tags": "I\\O 've\\O never\\O had\\O bad\\O service\\O and\\O the\\O fish\\B is\\O fresh\\O and\\O delicious\\O .\\O", "opinion_tags": "I\\O 've\\O never\\O had\\O bad\\O service\\O and\\O the\\O fish\\O is\\O fresh\\O and\\O delicious\\B .\\O"}], "postag": ["PRP", "VBP", "RB", "VBN", "JJ", "NN", "CC", "DT", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 11, 9, 11, 11, 4, 13, 11, 4], "deprel": ["nsubj", "aux", "advmod", "root", "amod", "obj", "cc", "det", "nsubj", "cop", "conj", "cc", "conj", "punct"]}, {"id": "53", "sentence": "The place is so cool and the service is prompt and curtious .", "triples": [{"uid": "53-0", "sentiment": "positive", "target_tags": "The\\O place\\O is\\O so\\O cool\\O and\\O the\\O service\\B is\\O prompt\\O and\\O curtious\\O .\\O", "opinion_tags": "The\\O place\\O is\\O so\\O cool\\O and\\O the\\O service\\O is\\O prompt\\B and\\O curtious\\O .\\O"}, {"uid": "53-1", "sentiment": "positive", "target_tags": "The\\O place\\O is\\O so\\O cool\\O and\\O the\\O service\\B is\\O prompt\\O and\\O curtious\\O .\\O", "opinion_tags": "The\\O place\\O is\\O so\\O cool\\O and\\O the\\O service\\O is\\O prompt\\O and\\O curtious\\B .\\O"}, {"uid": "53-2", "sentiment": "positive", "target_tags": "The\\O place\\B is\\O so\\O cool\\O and\\O the\\O service\\O is\\O prompt\\O and\\O curtious\\O .\\O", "opinion_tags": "The\\O place\\O is\\O so\\O cool\\B and\\O the\\O service\\O is\\O prompt\\O and\\O curtious\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 10, 8, 10, 10, 5, 12, 10, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "det", "nsubj", "cop", "conj", "cc", "conj", "punct"]}, {"id": "54", "sentence": "My boyfriend had the New England Chowder it was good but I think the award should go to the Lobster Bisque .", "triples": [{"uid": "54-0", "sentiment": "positive", "target_tags": "My\\O boyfriend\\O had\\O the\\O New\\B England\\I Chowder\\I it\\O was\\O good\\O but\\O I\\O think\\O the\\O award\\O should\\O go\\O to\\O the\\O Lobster\\O Bisque\\O .\\O", "opinion_tags": "My\\O boyfriend\\O had\\O the\\O New\\O England\\O Chowder\\O it\\O was\\O good\\B but\\O I\\O think\\O the\\O award\\O should\\O go\\O to\\O the\\O Lobster\\O Bisque\\O .\\O"}, {"uid": "54-1", "sentiment": "positive", "target_tags": "My\\O boyfriend\\O had\\O the\\O New\\O England\\O Chowder\\O it\\O was\\O good\\O but\\O I\\O think\\O the\\O award\\O should\\O go\\O to\\O the\\O Lobster\\B Bisque\\I .\\O", "opinion_tags": "My\\O boyfriend\\O had\\O the\\O New\\O England\\O Chowder\\O it\\O was\\O good\\O but\\O I\\O think\\O the\\O award\\B should\\O go\\O to\\O the\\O Lobster\\O Bisque\\O .\\O"}], "postag": ["PRP$", "NN", "VBD", "DT", "NNP", "NNP", "NNP", "PRP", "VBD", "JJ", "CC", "PRP", "VBP", "DT", "NN", "MD", "VB", "IN", "DT", "NNP", "NNP", "."], "head": [2, 3, 0, 7, 7, 7, 3, 10, 10, 3, 13, 13, 3, 15, 17, 17, 13, 21, 21, 21, 17, 3], "deprel": ["nmod:poss", "nsubj", "root", "det", "compound", "compound", "obj", "nsubj", "cop", "ccomp", "cc", "nsubj", "conj", "det", "nsubj", "aux", "ccomp", "case", "det", "compound", "obl", "punct"]}, {"id": "55", "sentence": "The characters really make for an enjoyable experience .", "triples": [{"uid": "55-0", "sentiment": "positive", "target_tags": "The\\O characters\\B really\\O make\\O for\\O an\\O enjoyable\\O experience\\O .\\O", "opinion_tags": "The\\O characters\\O really\\O make\\O for\\O an\\O enjoyable\\B experience\\O .\\O"}], "postag": ["DT", "NNS", "RB", "VBP", "IN", "DT", "JJ", "NN", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 4], "deprel": ["det", "nsubj", "advmod", "root", "case", "det", "amod", "obl", "punct"]}, {"id": "56", "sentence": "Food was good not great not worth the wait or another visit", "triples": [{"uid": "56-0", "sentiment": "neutral", "target_tags": "Food\\B was\\O good\\O not\\O great\\O not\\O worth\\O the\\O wait\\O or\\O another\\O visit\\O", "opinion_tags": "Food\\O was\\O good\\B not\\I great\\I not\\I worth\\I the\\I wait\\I or\\I another\\I visit\\I"}], "postag": ["NN", "VBD", "JJ", "RB", "JJ", "RB", "JJ", "DT", "NN", "CC", "DT", "NN"], "head": [3, 3, 0, 5, 3, 7, 3, 9, 7, 12, 12, 9], "deprel": ["nsubj", "cop", "root", "advmod", "conj", "advmod", "conj", "det", "obj", "cc", "det", "conj"]}, {"id": "57", "sentence": "The lox is always fresh too .", "triples": [{"uid": "57-0", "sentiment": "positive", "target_tags": "The\\O lox\\B is\\O always\\O fresh\\O too\\O .\\O", "opinion_tags": "The\\O lox\\O is\\O always\\O fresh\\B too\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "RB", "."], "head": [2, 5, 5, 5, 0, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "advmod", "punct"]}, {"id": "58", "sentence": "The service is ok , some of the people did n't get what they asked for .", "triples": [{"uid": "58-0", "sentiment": "neutral", "target_tags": "The\\O service\\B is\\O ok\\O ,\\O some\\O of\\O the\\O people\\O did\\O n't\\O get\\O what\\O they\\O asked\\O for\\O .\\O", "opinion_tags": "The\\O service\\O is\\O ok\\B ,\\O some\\O of\\O the\\O people\\O did\\O n't\\O get\\O what\\O they\\O asked\\O for\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ",", "DT", "IN", "DT", "NNS", "VBD", "RB", "VB", "WP", "PRP", "VBD", "IN", "."], "head": [2, 4, 4, 0, 4, 12, 9, 9, 6, 12, 12, 4, 12, 15, 13, 15, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "case", "det", "nmod", "aux", "advmod", "parataxis", "obj", "nsubj", "acl:relcl", "obl", "punct"]}, {"id": "59", "sentence": "The wine the service was very good too .", "triples": [{"uid": "59-0", "sentiment": "positive", "target_tags": "The\\O wine\\B the\\O service\\O was\\O very\\O good\\O too\\O .\\O", "opinion_tags": "The\\O wine\\O the\\O service\\O was\\O very\\O good\\B too\\O .\\O"}, {"uid": "59-1", "sentiment": "positive", "target_tags": "The\\O wine\\O the\\O service\\B was\\O very\\O good\\O too\\O .\\O", "opinion_tags": "The\\O wine\\O the\\O service\\O was\\O very\\O good\\B too\\O .\\O"}], "postag": ["DT", "NN", "DT", "NN", "VBD", "RB", "JJ", "RB", "."], "head": [2, 7, 4, 7, 7, 7, 0, 7, 7], "deprel": ["det", "nsubj", "det", "nsubj", "cop", "advmod", "root", "advmod", "punct"]}, {"id": "60", "sentence": "Great staff .", "triples": [{"uid": "60-0", "sentiment": "positive", "target_tags": "Great\\O staff\\B .\\O", "opinion_tags": "Great\\B staff\\O .\\O"}], "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"]}, {"id": "61", "sentence": "The hostess and the waitress were incredibly rude and did everything they could to rush us out .", "triples": [{"uid": "61-0", "sentiment": "negative", "target_tags": "The\\O hostess\\B and\\O the\\O waitress\\O were\\O incredibly\\O rude\\O and\\O did\\O everything\\O they\\O could\\O to\\O rush\\O us\\O out\\O .\\O", "opinion_tags": "The\\O hostess\\O and\\O the\\O waitress\\O were\\O incredibly\\O rude\\B and\\O did\\O everything\\O they\\O could\\O to\\O rush\\O us\\O out\\O .\\O"}, {"uid": "61-1", "sentiment": "negative", "target_tags": "The\\O hostess\\O and\\O the\\O waitress\\B were\\O incredibly\\O rude\\O and\\O did\\O everything\\O they\\O could\\O to\\O rush\\O us\\O out\\O .\\O", "opinion_tags": "The\\O hostess\\O and\\O the\\O waitress\\O were\\O incredibly\\O rude\\B and\\O did\\O everything\\O they\\O could\\O to\\O rush\\O us\\O out\\O .\\O"}], "postag": ["DT", "NN", "CC", "DT", "NN", "VBD", "RB", "JJ", "CC", "VBD", "NN", "PRP", "MD", "TO", "VB", "PRP", "RP", "."], "head": [2, 8, 5, 5, 2, 8, 8, 0, 10, 8, 10, 13, 11, 15, 10, 15, 15, 8], "deprel": ["det", "nsubj", "cc", "det", "conj", "cop", "advmod", "root", "cc", "conj", "obj", "nsubj", "acl:relcl", "mark", "advcl", "obj", "compound:prt", "punct"]}, {"id": "62", "sentence": "This place is always packed .", "triples": [{"uid": "62-0", "sentiment": "neutral", "target_tags": "This\\O place\\B is\\O always\\O packed\\O .\\O", "opinion_tags": "This\\O place\\O is\\O always\\O packed\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "VBN", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj:pass", "aux:pass", "advmod", "root", "punct"]}, {"id": "63", "sentence": "and yes <PERSON> is so dam good and so are all the kababs .", "triples": [{"uid": "63-0", "sentiment": "positive", "target_tags": "and\\O yes\\O Dal\\O Bukhara\\O is\\O so\\O dam\\O good\\O and\\O so\\O are\\O all\\O the\\O kababs\\B .\\O", "opinion_tags": "and\\O yes\\O Dal\\O Bukhara\\O is\\O so\\O dam\\O good\\B and\\O so\\O are\\O all\\O the\\O kababs\\O .\\O"}, {"uid": "63-1", "sentiment": "positive", "target_tags": "and\\O yes\\O Dal\\B Bukhara\\I is\\O so\\O dam\\O good\\O and\\O so\\O are\\O all\\O the\\O kababs\\O .\\O", "opinion_tags": "and\\O yes\\O Dal\\O Bukhara\\O is\\O so\\O dam\\O good\\B and\\O so\\O are\\O all\\O the\\O kababs\\O .\\O"}], "postag": ["CC", "UH", "NNP", "NNP", "VBZ", "RB", "NN", "JJ", "CC", "RB", "VBP", "PDT", "DT", "NNS", "."], "head": [8, 8, 4, 8, 8, 8, 8, 0, 14, 14, 14, 14, 14, 8, 8], "deprel": ["cc", "discourse", "compound", "nsubj", "cop", "advmod", "obl:npmod", "root", "cc", "advmod", "cop", "det:predet", "det", "conj", "punct"]}, {"id": "64", "sentence": "I have been to <PERSON> 's twice and both times were very disappointing .", "triples": [{"uid": "64-0", "sentiment": "negative", "target_tags": "I\\O have\\O been\\O to\\O Roth\\B 's\\I twice\\O and\\O both\\O times\\O were\\O very\\O disappointing\\O .\\O", "opinion_tags": "I\\O have\\O been\\O to\\O Roth\\O 's\\O twice\\O and\\O both\\O times\\O were\\O very\\O disappointing\\B .\\O"}], "postag": ["PRP", "VBP", "VBN", "IN", "NNP", "POS", "RB", "CC", "DT", "NNS", "VBD", "RB", "JJ", "."], "head": [7, 7, 5, 5, 0, 5, 5, 13, 10, 13, 13, 13, 5, 7], "deprel": ["nsubj", "aux", "cop", "case", "root", "case", "advmod", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct"]}, {"id": "65", "sentence": "Chennai Garden is my favorite Indian restaurant in the city .", "triples": [{"uid": "65-0", "sentiment": "positive", "target_tags": "Chennai\\B Garden\\I is\\O my\\O favorite\\O Indian\\O restaurant\\O in\\O the\\O city\\O .\\O", "opinion_tags": "Chennai\\O Garden\\O is\\O my\\O favorite\\B Indian\\O restaurant\\O in\\O the\\O city\\O .\\O"}], "postag": ["NNP", "NNP", "VBZ", "PRP$", "JJ", "JJ", "NN", "IN", "DT", "NN", "."], "head": [2, 7, 7, 7, 7, 7, 0, 10, 10, 7, 7], "deprel": ["compound", "nsubj", "cop", "nmod:poss", "amod", "amod", "root", "case", "det", "nmod", "punct"]}, {"id": "66", "sentence": "They have authentic Indian at amazin prices .", "triples": [{"uid": "66-0", "sentiment": "positive", "target_tags": "They\\O have\\O authentic\\O Indian\\B at\\O amazin\\O prices\\O .\\O", "opinion_tags": "They\\O have\\O authentic\\B Indian\\O at\\O amazin\\O prices\\O .\\O"}], "postag": ["PRP", "VBP", "JJ", "JJ", "IN", "JJ", "NNS", "."], "head": [2, 0, 4, 2, 7, 7, 2, 2], "deprel": ["nsubj", "root", "amod", "obj", "case", "amod", "obl", "punct"]}, {"id": "67", "sentence": "It 's a rather cramped and busy restaurant and it closes early .", "triples": [{"uid": "67-0", "sentiment": "negative", "target_tags": "It\\O 's\\O a\\O rather\\O cramped\\O and\\O busy\\O restaurant\\B and\\O it\\O closes\\O early\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O rather\\O cramped\\B and\\O busy\\O restaurant\\O and\\O it\\O closes\\O early\\O .\\O"}, {"uid": "67-1", "sentiment": "negative", "target_tags": "It\\O 's\\O a\\O rather\\O cramped\\O and\\O busy\\O restaurant\\B and\\O it\\O closes\\O early\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O rather\\O cramped\\O and\\O busy\\B restaurant\\O and\\O it\\O closes\\O early\\O .\\O"}], "postag": ["PRP", "VBZ", "DT", "RB", "JJ", "CC", "JJ", "NN", "CC", "PRP", "VBZ", "RB", "."], "head": [8, 8, 8, 5, 8, 7, 5, 0, 11, 11, 8, 11, 8], "deprel": ["nsubj", "cop", "det", "advmod", "amod", "cc", "conj", "root", "cc", "nsubj", "conj", "advmod", "punct"]}, {"id": "68", "sentence": "Food is excellent .", "triples": [{"uid": "68-0", "sentiment": "positive", "target_tags": "Food\\B is\\O excellent\\O .\\O", "opinion_tags": "Food\\O is\\O excellent\\B .\\O"}], "postag": ["NN", "VBZ", "JJ", "."], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"]}, {"id": "69", "sentence": "Fish is so very fresh .", "triples": [{"uid": "69-0", "sentiment": "positive", "target_tags": "Fish\\B is\\O so\\O very\\O fresh\\O .\\O", "opinion_tags": "Fish\\O is\\O so\\O very\\O fresh\\B .\\O"}], "postag": ["NN", "VBZ", "RB", "RB", "JJ", "."], "head": [5, 5, 5, 5, 0, 5], "deprel": ["nsubj", "cop", "advmod", "advmod", "root", "punct"]}, {"id": "70", "sentence": "Love YUKA .", "triples": [{"uid": "70-0", "sentiment": "positive", "target_tags": "Love\\O YUKA\\B .\\O", "opinion_tags": "Love\\B YUKA\\O .\\O"}], "postag": ["VBP", "NNP", "."], "head": [0, 1, 1], "deprel": ["root", "obj", "punct"]}, {"id": "71", "sentence": "The food is so cheap and the waiters are nice .", "triples": [{"uid": "71-0", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O so\\O cheap\\O and\\O the\\O waiters\\O are\\O nice\\O .\\O", "opinion_tags": "The\\O food\\O is\\O so\\O cheap\\B and\\O the\\O waiters\\O are\\O nice\\O .\\O"}, {"uid": "71-1", "sentiment": "positive", "target_tags": "The\\O food\\O is\\O so\\O cheap\\O and\\O the\\O waiters\\B are\\O nice\\O .\\O", "opinion_tags": "The\\O food\\O is\\O so\\O cheap\\O and\\O the\\O waiters\\O are\\O nice\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NNS", "VBP", "JJ", "."], "head": [2, 5, 5, 5, 0, 10, 8, 10, 10, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "72", "sentence": "Be sure to try the seasonal , and always delicious , specials .", "triples": [{"uid": "72-0", "sentiment": "positive", "target_tags": "Be\\O sure\\O to\\O try\\O the\\O seasonal\\O ,\\O and\\O always\\O delicious\\O ,\\O specials\\B .\\O", "opinion_tags": "Be\\O sure\\O to\\O try\\B the\\O seasonal\\O ,\\O and\\O always\\O delicious\\O ,\\O specials\\O .\\O"}, {"uid": "72-1", "sentiment": "positive", "target_tags": "Be\\O sure\\O to\\O try\\O the\\O seasonal\\O ,\\O and\\O always\\O delicious\\O ,\\O specials\\B .\\O", "opinion_tags": "Be\\O sure\\O to\\O try\\O the\\O seasonal\\B ,\\O and\\O always\\O delicious\\O ,\\O specials\\O .\\O"}, {"uid": "72-2", "sentiment": "positive", "target_tags": "Be\\O sure\\O to\\O try\\O the\\O seasonal\\O ,\\O and\\O always\\O delicious\\O ,\\O specials\\B .\\O", "opinion_tags": "Be\\O sure\\O to\\O try\\O the\\O seasonal\\O ,\\O and\\O always\\O delicious\\B ,\\O specials\\O .\\O"}], "postag": ["VB", "JJ", "TO", "VB", "DT", "JJ", ",", "CC", "RB", "JJ", ",", "NNS", "."], "head": [2, 0, 4, 2, 12, 12, 12, 10, 10, 6, 12, 4, 2], "deprel": ["cop", "root", "mark", "xcomp", "det", "amod", "punct", "cc", "advmod", "conj", "punct", "obj", "punct"]}, {"id": "73", "sentence": "Salads are a delicious way to begin the meal .", "triples": [{"uid": "73-0", "sentiment": "positive", "target_tags": "Salads\\B are\\O a\\O delicious\\O way\\O to\\O begin\\O the\\O meal\\O .\\O", "opinion_tags": "Salads\\O are\\O a\\O delicious\\B way\\O to\\O begin\\O the\\O meal\\O .\\O"}], "postag": ["NNS", "VBP", "DT", "JJ", "NN", "TO", "VB", "DT", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 9, 7, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "det", "obj", "punct"]}, {"id": "74", "sentence": "I would definitely recommend SEA if you like thai cuisine !", "triples": [{"uid": "74-0", "sentiment": "positive", "target_tags": "I\\O would\\O definitely\\O recommend\\O SEA\\O if\\O you\\O like\\O thai\\B cuisine\\I !\\O", "opinion_tags": "I\\O would\\O definitely\\O recommend\\O SEA\\O if\\O you\\O like\\B thai\\O cuisine\\O !\\O"}], "postag": ["PRP", "MD", "RB", "VB", "NNP", "IN", "PRP", "VBP", "JJ", "NN", "."], "head": [4, 4, 4, 0, 4, 8, 8, 4, 10, 8, 4], "deprel": ["nsubj", "aux", "advmod", "root", "obj", "mark", "nsubj", "advcl", "amod", "obj", "punct"]}, {"id": "75", "sentence": "I absolutely Loved this place .", "triples": [{"uid": "75-0", "sentiment": "positive", "target_tags": "I\\O absolutely\\O Loved\\O this\\O place\\B .\\O", "opinion_tags": "I\\O absolutely\\O Loved\\B this\\O place\\O .\\O"}], "postag": ["PRP", "RB", "VBD", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "obj", "punct"]}, {"id": "76", "sentence": "Everything was wonderful ; food , drinks , staff , mileau .", "triples": [{"uid": "76-0", "sentiment": "positive", "target_tags": "Everything\\O was\\O wonderful\\O ;\\O food\\B ,\\O drinks\\O ,\\O staff\\O ,\\O mileau\\O .\\O", "opinion_tags": "Everything\\O was\\O wonderful\\B ;\\O food\\O ,\\O drinks\\O ,\\O staff\\O ,\\O mileau\\O .\\O"}, {"uid": "76-1", "sentiment": "positive", "target_tags": "Everything\\O was\\O wonderful\\O ;\\O food\\O ,\\O drinks\\B ,\\O staff\\O ,\\O mileau\\O .\\O", "opinion_tags": "Everything\\O was\\O wonderful\\B ;\\O food\\O ,\\O drinks\\O ,\\O staff\\O ,\\O mileau\\O .\\O"}, {"uid": "76-2", "sentiment": "positive", "target_tags": "Everything\\O was\\O wonderful\\O ;\\O food\\O ,\\O drinks\\O ,\\O staff\\B ,\\O mileau\\O .\\O", "opinion_tags": "Everything\\O was\\O wonderful\\B ;\\O food\\O ,\\O drinks\\O ,\\O staff\\O ,\\O mileau\\O .\\O"}, {"uid": "76-3", "sentiment": "positive", "target_tags": "Everything\\O was\\O wonderful\\O ;\\O food\\O ,\\O drinks\\O ,\\O staff\\O ,\\O mileau\\B .\\O", "opinion_tags": "Everything\\O was\\O wonderful\\B ;\\O food\\O ,\\O drinks\\O ,\\O staff\\O ,\\O mileau\\O .\\O"}], "postag": ["NN", "VBD", "JJ", ",", "NN", ",", "NNS", ",", "NN", ",", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 5, 9, 5, 11, 5, 3], "deprel": ["nsubj", "cop", "root", "punct", "parataxis", "punct", "conj", "punct", "conj", "punct", "conj", "punct"]}, {"id": "77", "sentence": "Nice Family owned traditional restaurant .", "triples": [{"uid": "77-0", "sentiment": "positive", "target_tags": "Nice\\O Family\\O owned\\O traditional\\O restaurant\\B .\\O", "opinion_tags": "Nice\\O Family\\O owned\\O traditional\\B restaurant\\O .\\O"}], "postag": ["NNP", "NNP", "VBN", "JJ", "NN", "."], "head": [2, 3, 5, 5, 0, 5], "deprel": ["compound", "compound", "amod", "amod", "root", "punct"]}, {"id": "78", "sentence": "I also ordered the Change Mojito , which was out of this world .", "triples": [{"uid": "78-0", "sentiment": "positive", "target_tags": "I\\O also\\O ordered\\O the\\O Change\\B Mojito\\I ,\\O which\\O was\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "I\\O also\\O ordered\\O the\\O Change\\O Mojito\\O ,\\O which\\O was\\O out\\B of\\I this\\I world\\I .\\O"}], "postag": ["PRP", "RB", "VBD", "DT", "NNP", "NNP", ",", "WDT", "VBD", "IN", "IN", "DT", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 6, 13, 13, 13, 13, 13, 6, 3], "deprel": ["nsubj", "advmod", "root", "det", "compound", "obj", "punct", "nsubj", "cop", "case", "case", "det", "acl:relcl", "punct"]}, {"id": "79", "sentence": "The place was nice and calm .", "triples": [{"uid": "79-0", "sentiment": "positive", "target_tags": "The\\O place\\B was\\O nice\\O and\\O calm\\O .\\O", "opinion_tags": "The\\O place\\O was\\O nice\\B and\\O calm\\O .\\O"}, {"uid": "79-1", "sentiment": "positive", "target_tags": "The\\O place\\B was\\O nice\\O and\\O calm\\O .\\O", "opinion_tags": "The\\O place\\O was\\O nice\\O and\\O calm\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct"]}, {"id": "80", "sentence": "Consequently , their burgers fell apart in their hands and made such a mess that they did'nt feel like finishing them .", "triples": [{"uid": "80-0", "sentiment": "negative", "target_tags": "Consequently\\O ,\\O their\\O burgers\\B fell\\O apart\\O in\\O their\\O hands\\O and\\O made\\O such\\O a\\O mess\\O that\\O they\\O did'nt\\O feel\\O like\\O finishing\\O them\\O .\\O", "opinion_tags": "Consequently\\O ,\\O their\\O burgers\\O fell\\B apart\\I in\\O their\\O hands\\O and\\O made\\O such\\O a\\O mess\\O that\\O they\\O did'nt\\O feel\\O like\\O finishing\\O them\\O .\\O"}], "postag": ["RB", ",", "PRP$", "NNS", "VBD", "RB", "IN", "PRP$", "NNS", "CC", "VBD", "PDT", "DT", "NN", "WDT", "PRP", "MD", "VB", "IN", "VBG", "PRP", "."], "head": [5, 1, 4, 5, 0, 5, 9, 9, 5, 11, 5, 14, 14, 11, 18, 18, 18, 14, 20, 18, 20, 5], "deprel": ["advmod", "punct", "nmod:poss", "nsubj", "root", "advmod", "case", "nmod:poss", "obl", "cc", "conj", "det:predet", "det", "obj", "obj", "nsubj", "aux", "acl:relcl", "mark", "advcl", "obj", "punct"]}, {"id": "81", "sentence": "I had a huge pastrami sandwich on a roll .", "triples": [{"uid": "81-0", "sentiment": "neutral", "target_tags": "I\\O had\\O a\\O huge\\O pastrami\\B sandwich\\I on\\I a\\I roll\\I .\\O", "opinion_tags": "I\\O had\\O a\\O huge\\B pastrami\\O sandwich\\O on\\O a\\O roll\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "JJ", "NN", "NN", "IN", "DT", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 9, 9, 2, 2], "deprel": ["nsubj", "root", "det", "amod", "compound", "obj", "case", "det", "obl", "punct"]}, {"id": "82", "sentence": "THe back garden sitting area is very pleasant , where you can see their personal herb garden .", "triples": [{"uid": "82-0", "sentiment": "positive", "target_tags": "THe\\O back\\B garden\\I sitting\\I area\\I is\\O very\\O pleasant\\O ,\\O where\\O you\\O can\\O see\\O their\\O personal\\O herb\\O garden\\O .\\O", "opinion_tags": "THe\\O back\\O garden\\O sitting\\O area\\O is\\O very\\O pleasant\\B ,\\O where\\O you\\O can\\O see\\O their\\O personal\\O herb\\O garden\\O .\\O"}], "postag": ["DT", "JJ", "NN", "NN", "NN", "VBZ", "RB", "JJ", ",", "WRB", "PRP", "MD", "VB", "PRP$", "JJ", "NN", "NN", "."], "head": [5, 3, 5, 5, 8, 8, 8, 0, 8, 13, 13, 13, 8, 17, 17, 17, 13, 8], "deprel": ["det", "amod", "compound", "compound", "nsubj", "cop", "advmod", "root", "punct", "mark", "nsubj", "aux", "advcl", "nmod:poss", "amod", "compound", "obj", "punct"]}, {"id": "83", "sentence": "We ended our great experience by having <PERSON><PERSON><PERSON> ( dessert ) recommended by the waiter .", "triples": [{"uid": "83-0", "sentiment": "positive", "target_tags": "We\\O ended\\O our\\O great\\O experience\\O by\\O having\\O Gulab\\B Jamun\\I (\\I dessert\\I )\\I recommended\\O by\\O the\\O waiter\\O .\\O", "opinion_tags": "We\\O ended\\O our\\O great\\B experience\\O by\\O having\\O Gulab\\O Jamun\\O (\\O dessert\\O )\\O recommended\\O by\\O the\\O waiter\\O .\\O"}], "postag": ["PRP", "VBD", "PRP$", "JJ", "NN", "IN", "VBG", "NNP", "NNP", "-LRB-", "NN", "-RRB-", "VBN", "IN", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 7, 2, 9, 7, 11, 9, 11, 7, 16, 16, 13, 2], "deprel": ["nsubj", "root", "nmod:poss", "amod", "obj", "mark", "advcl", "compound", "obj", "punct", "appos", "punct", "xcomp", "case", "det", "obl", "punct"]}, {"id": "84", "sentence": "I thanked my friend who recommended me this restaurant and will certainly recommend it to others .", "triples": [{"uid": "84-0", "sentiment": "positive", "target_tags": "I\\O thanked\\O my\\O friend\\O who\\O recommended\\O me\\O this\\O restaurant\\B and\\O will\\O certainly\\O recommend\\O it\\O to\\O others\\O .\\O", "opinion_tags": "I\\O thanked\\O my\\O friend\\O who\\O recommended\\O me\\O this\\O restaurant\\O and\\O will\\O certainly\\O recommend\\B it\\O to\\O others\\O .\\O"}], "postag": ["PRP", "VBD", "PRP$", "NN", "WP", "VBD", "PRP", "DT", "NN", "CC", "MD", "RB", "VB", "PRP", "IN", "NNS", "."], "head": [2, 0, 4, 2, 6, 4, 6, 9, 6, 13, 13, 13, 2, 13, 16, 13, 2], "deprel": ["nsubj", "root", "nmod:poss", "obj", "nsubj", "acl:relcl", "i<PERSON><PERSON>", "det", "obj", "cc", "aux", "advmod", "conj", "obj", "case", "obl", "punct"]}, {"id": "85", "sentence": "I am reluctant to write because I would not want my jem of a pizza place to become overcrowded .", "triples": [{"uid": "85-0", "sentiment": "positive", "target_tags": "I\\O am\\O reluctant\\O to\\O write\\O because\\O I\\O would\\O not\\O want\\O my\\O jem\\O of\\O a\\O pizza\\B place\\I to\\O become\\O overcrowded\\O .\\O", "opinion_tags": "I\\O am\\O reluctant\\O to\\O write\\O because\\O I\\O would\\O not\\O want\\O my\\O jem\\O of\\O a\\O pizza\\O place\\O to\\O become\\O overcrowded\\B .\\O"}], "postag": ["PRP", "VBP", "JJ", "TO", "VB", "IN", "PRP", "MD", "RB", "VB", "PRP$", "NN", "IN", "DT", "NN", "NN", "TO", "VB", "JJ", "."], "head": [3, 3, 0, 5, 3, 10, 10, 10, 10, 5, 12, 10, 16, 16, 16, 12, 18, 10, 18, 3], "deprel": ["nsubj", "cop", "root", "mark", "xcomp", "mark", "nsubj", "aux", "advmod", "advcl", "nmod:poss", "obj", "case", "det", "compound", "nmod", "mark", "advcl", "xcomp", "punct"]}, {"id": "86", "sentence": "While their kitchen food is delicious , their Sushi is out of this world .", "triples": [{"uid": "86-0", "sentiment": "positive", "target_tags": "While\\O their\\O kitchen\\B food\\I is\\O delicious\\O ,\\O their\\O Sushi\\O is\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "While\\O their\\O kitchen\\O food\\O is\\O delicious\\B ,\\O their\\O Sushi\\O is\\O out\\O of\\O this\\O world\\O .\\O"}, {"uid": "86-1", "sentiment": "positive", "target_tags": "While\\O their\\O kitchen\\O food\\O is\\O delicious\\O ,\\O their\\O Sushi\\B is\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "While\\O their\\O kitchen\\O food\\O is\\O delicious\\O ,\\O their\\O Sushi\\O is\\O out\\B of\\I this\\I world\\I .\\O"}], "postag": ["IN", "PRP$", "NN", "NN", "VBZ", "JJ", ",", "PRP$", "NN", "VBZ", "IN", "IN", "DT", "NN", "."], "head": [6, 4, 4, 6, 6, 14, 14, 9, 14, 14, 14, 14, 14, 0, 14], "deprel": ["mark", "nmod:poss", "compound", "nsubj", "cop", "advcl", "punct", "nmod:poss", "nsubj", "cop", "case", "case", "det", "root", "punct"]}, {"id": "87", "sentence": "Thalia is a beautiful restaurant with beautiful people serving you , but the food does n't quite match up .", "triples": [{"uid": "87-0", "sentiment": "positive", "target_tags": "Thalia\\O is\\O a\\O beautiful\\O restaurant\\O with\\O beautiful\\O people\\B serving\\O you\\O ,\\O but\\O the\\O food\\O does\\O n't\\O quite\\O match\\O up\\O .\\O", "opinion_tags": "Thalia\\O is\\O a\\O beautiful\\O restaurant\\O with\\O beautiful\\B people\\O serving\\O you\\O ,\\O but\\O the\\O food\\O does\\O n't\\O quite\\O match\\O up\\O .\\O"}, {"uid": "87-1", "sentiment": "negative", "target_tags": "Thalia\\O is\\O a\\O beautiful\\O restaurant\\O with\\O beautiful\\O people\\O serving\\O you\\O ,\\O but\\O the\\O food\\B does\\O n't\\O quite\\O match\\O up\\O .\\O", "opinion_tags": "Thalia\\O is\\O a\\O beautiful\\O restaurant\\O with\\O beautiful\\O people\\O serving\\O you\\O ,\\O but\\O the\\O food\\O does\\B n't\\I quite\\I match\\I up\\I .\\O"}, {"uid": "87-2", "sentiment": "positive", "target_tags": "Thalia\\B is\\O a\\O beautiful\\O restaurant\\O with\\O beautiful\\O people\\O serving\\O you\\O ,\\O but\\O the\\O food\\O does\\O n't\\O quite\\O match\\O up\\O .\\O", "opinion_tags": "Thalia\\O is\\O a\\O beautiful\\B restaurant\\O with\\O beautiful\\O people\\O serving\\O you\\O ,\\O but\\O the\\O food\\O does\\O n't\\O quite\\O match\\O up\\O .\\O"}], "postag": ["NNP", "VBZ", "DT", "JJ", "NN", "IN", "JJ", "NNS", "VBG", "PRP", ",", "CC", "DT", "NN", "VBZ", "RB", "RB", "VB", "RP", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 8, 9, 18, 18, 14, 18, 18, 18, 18, 5, 18, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "case", "amod", "nmod", "acl", "obj", "punct", "cc", "det", "nsubj", "aux", "advmod", "advmod", "conj", "compound:prt", "punct"]}, {"id": "88", "sentence": "I expected quite a bit more from such an expensive menu .", "triples": [{"uid": "88-0", "sentiment": "negative", "target_tags": "I\\O expected\\O quite\\O a\\O bit\\O more\\O from\\O such\\O an\\O expensive\\O menu\\B .\\O", "opinion_tags": "I\\O expected\\O quite\\O a\\O bit\\O more\\O from\\O such\\O an\\O expensive\\B menu\\O .\\O"}], "postag": ["PRP", "VBD", "PDT", "DT", "NN", "JJR", "IN", "PDT", "DT", "JJ", "NN", "."], "head": [2, 0, 5, 5, 6, 2, 11, 11, 11, 11, 2, 2], "deprel": ["nsubj", "root", "det:predet", "det", "obl:npmod", "obj", "case", "det:predet", "det", "amod", "obl", "punct"]}, {"id": "89", "sentence": "The view is spectacular , and the food is great .", "triples": [{"uid": "89-0", "sentiment": "positive", "target_tags": "The\\O view\\B is\\O spectacular\\O ,\\O and\\O the\\O food\\O is\\O great\\O .\\O", "opinion_tags": "The\\O view\\O is\\O spectacular\\B ,\\O and\\O the\\O food\\O is\\O great\\O .\\O"}, {"uid": "89-1", "sentiment": "positive", "target_tags": "The\\O view\\O is\\O spectacular\\O ,\\O and\\O the\\O food\\B is\\O great\\O .\\O", "opinion_tags": "The\\O view\\O is\\O spectacular\\O ,\\O and\\O the\\O food\\O is\\O great\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ",", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 10, 10, 8, 10, 10, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "90", "sentence": "Authentic Taiwanese food that 's cheap ... what more could you ask for ?", "triples": [{"uid": "90-0", "sentiment": "positive", "target_tags": "Authentic\\O Taiwanese\\B food\\I that\\O 's\\O cheap\\O ...\\O what\\O more\\O could\\O you\\O ask\\O for\\O ?\\O", "opinion_tags": "Authentic\\B Taiwanese\\O food\\O that\\O 's\\O cheap\\O ...\\O what\\O more\\O could\\O you\\O ask\\O for\\O ?\\O"}, {"uid": "90-1", "sentiment": "positive", "target_tags": "Authentic\\O Taiwanese\\B food\\I that\\O 's\\O cheap\\O ...\\O what\\O more\\O could\\O you\\O ask\\O for\\O ?\\O", "opinion_tags": "Authentic\\O Taiwanese\\O food\\O that\\O 's\\O cheap\\B ...\\O what\\O more\\O could\\O you\\O ask\\O for\\O ?\\O"}], "postag": ["JJ", "JJ", "NN", "WDT", "VBZ", "JJ", ",", "WP", "JJR", "MD", "PRP", "VB", "IN", "."], "head": [3, 3, 0, 6, 6, 3, 3, 9, 12, 12, 12, 3, 12, 3], "deprel": ["amod", "amod", "root", "nsubj", "cop", "acl:relcl", "punct", "det", "advmod", "aux", "nsubj", "parataxis", "obl", "punct"]}, {"id": "91", "sentence": "Kind , attentive wait staff .", "triples": [{"uid": "91-0", "sentiment": "positive", "target_tags": "Kind\\O ,\\O attentive\\O wait\\B staff\\I .\\O", "opinion_tags": "Kind\\B ,\\O attentive\\O wait\\O staff\\O .\\O"}, {"uid": "91-1", "sentiment": "positive", "target_tags": "Kind\\O ,\\O attentive\\O wait\\B staff\\I .\\O", "opinion_tags": "Kind\\O ,\\O attentive\\B wait\\O staff\\O .\\O"}], "postag": ["JJ", ",", "JJ", "NN", "NN", "."], "head": [5, 5, 5, 5, 0, 5], "deprel": ["amod", "punct", "amod", "compound", "root", "punct"]}, {"id": "92", "sentence": "My friend devoured her chicken and mashed potatos .", "triples": [{"uid": "92-0", "sentiment": "positive", "target_tags": "My\\O friend\\O devoured\\O her\\O chicken\\B and\\I mashed\\I potatos\\I .\\O", "opinion_tags": "My\\O friend\\O devoured\\B her\\O chicken\\O and\\O mashed\\O potatos\\O .\\O"}], "postag": ["PRP$", "NN", "VBD", "PRP$", "NN", "CC", "VBD", "NNS", "."], "head": [2, 3, 0, 5, 3, 7, 3, 7, 3], "deprel": ["nmod:poss", "nsubj", "root", "nmod:poss", "obj", "cc", "conj", "obj", "punct"]}, {"id": "93", "sentence": "The atmosphere is unheralded , the service impecible , and the food magnificant .", "triples": [{"uid": "93-0", "sentiment": "positive", "target_tags": "The\\O atmosphere\\B is\\O unheralded\\O ,\\O the\\O service\\O impecible\\O ,\\O and\\O the\\O food\\O magnificant\\O .\\O", "opinion_tags": "The\\O atmosphere\\O is\\O unheralded\\B ,\\O the\\O service\\O impecible\\O ,\\O and\\O the\\O food\\O magnificant\\O .\\O"}, {"uid": "93-1", "sentiment": "positive", "target_tags": "The\\O atmosphere\\O is\\O unheralded\\O ,\\O the\\O service\\B impecible\\O ,\\O and\\O the\\O food\\O magnificant\\O .\\O", "opinion_tags": "The\\O atmosphere\\O is\\O unheralded\\O ,\\O the\\O service\\O impecible\\B ,\\O and\\O the\\O food\\O magnificant\\O .\\O"}, {"uid": "93-2", "sentiment": "positive", "target_tags": "The\\O atmosphere\\O is\\O unheralded\\O ,\\O the\\O service\\O impecible\\O ,\\O and\\O the\\O food\\B magnificant\\O .\\O", "opinion_tags": "The\\O atmosphere\\O is\\O unheralded\\O ,\\O the\\O service\\O impecible\\O ,\\O and\\O the\\O food\\O magnificant\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ",", "DT", "NN", "JJ", ",", "CC", "DT", "NN", "NN", "."], "head": [2, 4, 4, 0, 8, 7, 8, 4, 13, 13, 12, 13, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "conj", "punct", "cc", "det", "nsubj", "conj", "punct"]}, {"id": "94", "sentence": "This is such a lovely , peaceful place to eat outside .", "triples": [{"uid": "94-0", "sentiment": "positive", "target_tags": "This\\O is\\O such\\O a\\O lovely\\O ,\\O peaceful\\O place\\B to\\O eat\\O outside\\O .\\O", "opinion_tags": "This\\O is\\O such\\O a\\O lovely\\B ,\\O peaceful\\O place\\O to\\O eat\\O outside\\O .\\O"}, {"uid": "94-1", "sentiment": "positive", "target_tags": "This\\O is\\O such\\O a\\O lovely\\O ,\\O peaceful\\O place\\B to\\O eat\\O outside\\O .\\O", "opinion_tags": "This\\O is\\O such\\O a\\O lovely\\O ,\\O peaceful\\B place\\O to\\O eat\\O outside\\O .\\O"}], "postag": ["DT", "VBZ", "PDT", "DT", "JJ", ",", "JJ", "NN", "TO", "VB", "RB", "."], "head": [8, 8, 8, 8, 8, 8, 8, 0, 10, 8, 10, 8], "deprel": ["nsubj", "cop", "det:predet", "det", "amod", "punct", "amod", "root", "mark", "acl", "advmod", "punct"]}, {"id": "95", "sentence": "Great sushi experience .", "triples": [{"uid": "95-0", "sentiment": "positive", "target_tags": "Great\\O sushi\\B experience\\O .\\O", "opinion_tags": "Great\\B sushi\\O experience\\O .\\O"}], "postag": ["JJ", "NN", "NN", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "compound", "root", "punct"]}, {"id": "96", "sentence": "This place is not worth the prices .", "triples": [{"uid": "96-0", "sentiment": "negative", "target_tags": "This\\O place\\B is\\O not\\O worth\\O the\\O prices\\O .\\O", "opinion_tags": "This\\O place\\O is\\O not\\B worth\\I the\\I prices\\I .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "DT", "NNS", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "det", "obj", "punct"]}, {"id": "97", "sentence": "Love Pizza 33 ...", "triples": [{"uid": "97-0", "sentiment": "positive", "target_tags": "Love\\O Pizza\\B 33\\I ...\\O", "opinion_tags": "Love\\B Pizza\\O 33\\O ...\\O"}], "postag": ["VBP", "NN", "CD", "."], "head": [0, 1, 2, 1], "deprel": ["root", "obj", "nummod", "punct"]}, {"id": "98", "sentence": "The rice was poor quality and was cooked so badly it was hard .", "triples": [{"uid": "98-0", "sentiment": "negative", "target_tags": "The\\O rice\\B was\\O poor\\O quality\\O and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\O .\\O", "opinion_tags": "The\\O rice\\O was\\O poor\\B quality\\I and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\O .\\O"}, {"uid": "98-1", "sentiment": "negative", "target_tags": "The\\O rice\\B was\\O poor\\O quality\\O and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\O .\\O", "opinion_tags": "The\\O rice\\O was\\O poor\\O quality\\O and\\O was\\O cooked\\B so\\I badly\\I it\\O was\\O hard\\O .\\O"}, {"uid": "98-2", "sentiment": "negative", "target_tags": "The\\O rice\\B was\\O poor\\O quality\\O and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\O .\\O", "opinion_tags": "The\\O rice\\O was\\O poor\\O quality\\O and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "NN", "CC", "VBD", "VBN", "RB", "RB", "PRP", "VBD", "JJ", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 10, 8, 13, 13, 5, 5], "deprel": ["det", "nsubj", "cop", "amod", "root", "cc", "aux:pass", "conj", "advmod", "advmod", "nsubj", "cop", "conj", "punct"]}, {"id": "99", "sentence": "The fish was adequate , but inexpertly sliced .", "triples": [{"uid": "99-0", "sentiment": "negative", "target_tags": "The\\O fish\\B was\\O adequate\\O ,\\O but\\O inexpertly\\O sliced\\O .\\O", "opinion_tags": "The\\O fish\\O was\\O adequate\\B ,\\O but\\O inexpertly\\O sliced\\O .\\O"}, {"uid": "99-1", "sentiment": "negative", "target_tags": "The\\O fish\\B was\\O adequate\\O ,\\O but\\O inexpertly\\O sliced\\O .\\O", "opinion_tags": "The\\O fish\\O was\\O adequate\\O ,\\O but\\O inexpertly\\B sliced\\I .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", ",", "CC", "RB", "VBN", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "advmod", "conj", "punct"]}, {"id": "100", "sentence": "The wait staff is pleasant , fun , and for the most part gorgeous ( in the wonderful aesthetic beautification way , not in that she's-way-cuter-than-me-that-b @ # $ * way ) .", "triples": [{"uid": "100-0", "sentiment": "positive", "target_tags": "The\\O wait\\B staff\\I is\\O pleasant\\O ,\\O fun\\O ,\\O and\\O for\\O the\\O most\\O part\\O gorgeous\\O (\\O in\\O the\\O wonderful\\O aesthetic\\O beautification\\O way\\O ,\\O not\\O in\\O that\\O she's-way-cuter-than-me-that-b\\O @\\O #\\O $\\O *\\O way\\O )\\O .\\O", "opinion_tags": "The\\O wait\\O staff\\O is\\O pleasant\\B ,\\O fun\\O ,\\O and\\O for\\O the\\O most\\O part\\O gorgeous\\O (\\O in\\O the\\O wonderful\\O aesthetic\\O beautification\\O way\\O ,\\O not\\O in\\O that\\O she's-way-cuter-than-me-that-b\\O @\\O #\\O $\\O *\\O way\\O )\\O .\\O"}, {"uid": "100-1", "sentiment": "positive", "target_tags": "The\\O wait\\B staff\\I is\\O pleasant\\O ,\\O fun\\O ,\\O and\\O for\\O the\\O most\\O part\\O gorgeous\\O (\\O in\\O the\\O wonderful\\O aesthetic\\O beautification\\O way\\O ,\\O not\\O in\\O that\\O she's-way-cuter-than-me-that-b\\O @\\O #\\O $\\O *\\O way\\O )\\O .\\O", "opinion_tags": "The\\O wait\\O staff\\O is\\O pleasant\\O ,\\O fun\\B ,\\O and\\O for\\O the\\O most\\O part\\O gorgeous\\O (\\O in\\O the\\O wonderful\\O aesthetic\\O beautification\\O way\\O ,\\O not\\O in\\O that\\O she's-way-cuter-than-me-that-b\\O @\\O #\\O $\\O *\\O way\\O )\\O .\\O"}, {"uid": "100-2", "sentiment": "positive", "target_tags": "The\\O wait\\B staff\\I is\\O pleasant\\O ,\\O fun\\O ,\\O and\\O for\\O the\\O most\\O part\\O gorgeous\\O (\\O in\\O the\\O wonderful\\O aesthetic\\O beautification\\O way\\O ,\\O not\\O in\\O that\\O she's-way-cuter-than-me-that-b\\O @\\O #\\O $\\O *\\O way\\O )\\O .\\O", "opinion_tags": "The\\O wait\\O staff\\O is\\O pleasant\\O ,\\O fun\\O ,\\O and\\O for\\O the\\O most\\O part\\O gorgeous\\B (\\O in\\O the\\O wonderful\\O aesthetic\\O beautification\\O way\\O ,\\O not\\O in\\O that\\O she's-way-cuter-than-me-that-b\\O @\\O #\\O $\\O *\\O way\\O )\\O .\\O"}], "postag": ["DT", "NN", "NN", "VBZ", "JJ", ",", "JJ", ",", "CC", "IN", "DT", "JJS", "NN", "JJ", "-LRB-", "IN", "DT", "JJ", "JJ", "NN", "NN", ",", "RB", "IN", "DT", "NN", "IN", "NN", "$", "NFP", "NN", "-RRB-", "."], "head": [3, 3, 5, 5, 0, 7, 5, 14, 31, 13, 13, 13, 31, 13, 21, 21, 21, 21, 21, 21, 14, 26, 26, 26, 26, 31, 31, 31, 31, 31, 5, 31, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "conj", "punct", "cc", "case", "det", "amod", "nmod", "amod", "punct", "case", "det", "amod", "amod", "compound", "obl", "punct", "advmod", "case", "det", "nmod", "case", "compound", "compound", "punct", "conj", "punct", "punct"]}, {"id": "101", "sentence": "But the best part about LS is the late night atmosphere , delightfully free of the BTs .", "triples": [{"uid": "101-0", "sentiment": "positive", "target_tags": "But\\O the\\O best\\O part\\O about\\O LS\\O is\\O the\\O late\\B night\\I atmosphere\\I ,\\O delightfully\\O free\\O of\\O the\\O BTs\\O .\\O", "opinion_tags": "But\\O the\\O best\\B part\\O about\\O LS\\O is\\O the\\O late\\O night\\O atmosphere\\O ,\\O delightfully\\O free\\O of\\O the\\O BTs\\O .\\O"}], "postag": ["CC", "DT", "JJS", "NN", "IN", "NNP", "VBZ", "DT", "JJ", "NN", "NN", ",", "RB", "JJ", "IN", "DT", "NNS", "."], "head": [11, 4, 4, 11, 6, 4, 11, 11, 11, 11, 0, 11, 14, 11, 17, 17, 14, 11], "deprel": ["cc", "det", "amod", "nsubj", "case", "nmod", "cop", "det", "amod", "compound", "root", "punct", "advmod", "amod", "case", "det", "obl", "punct"]}, {"id": "102", "sentence": "<PERSON><PERSON> is a great place that I often take my friends ( classmates ) too .", "triples": [{"uid": "102-0", "sentiment": "positive", "target_tags": "Suan\\B is\\O a\\O great\\O place\\O that\\O I\\O often\\O take\\O my\\O friends\\O (\\O classmates\\O )\\O too\\O .\\O", "opinion_tags": "Suan\\O is\\O a\\O great\\B place\\O that\\O I\\O often\\O take\\O my\\O friends\\O (\\O classmates\\O )\\O too\\O .\\O"}], "postag": ["NNP", "VBZ", "DT", "JJ", "NN", "WDT", "PRP", "RB", "VBP", "PRP$", "NNS", "-LRB-", "NNS", "-RRB-", "RB", "."], "head": [5, 5, 5, 5, 0, 9, 9, 9, 5, 11, 9, 13, 11, 13, 9, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "nsubj", "advmod", "acl:relcl", "nmod:poss", "obj", "punct", "appos", "punct", "advmod", "punct"]}, {"id": "103", "sentence": "noodles with shrimp and chicken and coconut juice is the MUST !", "triples": [{"uid": "103-0", "sentiment": "positive", "target_tags": "noodles\\B with\\I shrimp\\I and\\I chicken\\I and\\I coconut\\I juice\\I is\\O the\\O MUST\\O !\\O", "opinion_tags": "noodles\\O with\\O shrimp\\O and\\O chicken\\O and\\O coconut\\O juice\\O is\\O the\\O MUST\\B !\\O"}], "postag": ["NNS", "IN", "NN", "CC", "NN", "CC", "NN", "NN", "VBZ", "DT", "NN", "."], "head": [11, 3, 1, 5, 3, 8, 8, 3, 11, 11, 0, 11], "deprel": ["nsubj", "case", "nmod", "cc", "conj", "cc", "compound", "conj", "cop", "det", "root", "punct"]}, {"id": "104", "sentence": "I can not imagine a friendlier staff working in a restaurant .", "triples": [{"uid": "104-0", "sentiment": "positive", "target_tags": "I\\O can\\O not\\O imagine\\O a\\O friendlier\\O staff\\B working\\O in\\O a\\O restaurant\\O .\\O", "opinion_tags": "I\\O can\\O not\\O imagine\\O a\\O friendlier\\B staff\\O working\\O in\\O a\\O restaurant\\O .\\O"}], "postag": ["PRP", "MD", "RB", "VB", "DT", "JJR", "NN", "VBG", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 7, 11, 11, 8, 4], "deprel": ["nsubj", "aux", "advmod", "root", "det", "amod", "obj", "acl", "case", "det", "obl", "punct"]}, {"id": "105", "sentence": "I can not imagine better Indian food in all of the city .", "triples": [{"uid": "105-0", "sentiment": "positive", "target_tags": "I\\O can\\O not\\O imagine\\O better\\O Indian\\B food\\I in\\O all\\O of\\O the\\O city\\O .\\O", "opinion_tags": "I\\O can\\O not\\O imagine\\O better\\B Indian\\O food\\O in\\O all\\O of\\O the\\O city\\O .\\O"}], "postag": ["PRP", "MD", "RB", "VB", "JJR", "JJ", "NN", "IN", "DT", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 9, 4, 12, 12, 9, 4], "deprel": ["nsubj", "aux", "advmod", "root", "amod", "amod", "obj", "case", "obl", "case", "det", "nmod", "punct"]}, {"id": "106", "sentence": "if you 're daring , try the balsamic vinegar over icecream , it 's wonderful !", "triples": [{"uid": "106-0", "sentiment": "positive", "target_tags": "if\\O you\\O 're\\O daring\\O ,\\O try\\O the\\O balsamic\\B vinegar\\I over\\I icecream\\I ,\\O it\\O 's\\O wonderful\\O !\\O", "opinion_tags": "if\\O you\\O 're\\O daring\\O ,\\O try\\B the\\O balsamic\\O vinegar\\O over\\O icecream\\O ,\\O it\\O 's\\O wonderful\\O !\\O"}, {"uid": "106-1", "sentiment": "positive", "target_tags": "if\\O you\\O 're\\O daring\\O ,\\O try\\O the\\O balsamic\\B vinegar\\I over\\I icecream\\I ,\\O it\\O 's\\O wonderful\\O !\\O", "opinion_tags": "if\\O you\\O 're\\O daring\\O ,\\O try\\O the\\O balsamic\\O vinegar\\O over\\O icecream\\O ,\\O it\\O 's\\O wonderful\\B !\\O"}], "postag": ["IN", "PRP", "VBP", "VBG", ",", "VB", "DT", "NN", "NN", "IN", "NN", ",", "PRP", "VBZ", "JJ", "."], "head": [4, 4, 4, 6, 6, 0, 9, 9, 6, 11, 6, 6, 15, 15, 6, 6], "deprel": ["mark", "nsubj", "aux", "advcl", "punct", "root", "det", "compound", "obj", "case", "obl", "punct", "nsubj", "cop", "parataxis", "punct"]}, {"id": "107", "sentence": "The rest of the dim sum , though pricey by Chinatown standards , is worth it .", "triples": [{"uid": "107-0", "sentiment": "positive", "target_tags": "The\\O rest\\O of\\O the\\O dim\\B sum\\I ,\\O though\\O pricey\\O by\\O Chinatown\\O standards\\O ,\\O is\\O worth\\O it\\O .\\O", "opinion_tags": "The\\O rest\\O of\\O the\\O dim\\O sum\\O ,\\O though\\O pricey\\B by\\O Chinatown\\O standards\\O ,\\O is\\O worth\\O it\\O .\\O"}, {"uid": "107-1", "sentiment": "positive", "target_tags": "The\\O rest\\O of\\O the\\O dim\\B sum\\I ,\\O though\\O pricey\\O by\\O Chinatown\\O standards\\O ,\\O is\\O worth\\O it\\O .\\O", "opinion_tags": "The\\O rest\\O of\\O the\\O dim\\O sum\\O ,\\O though\\O pricey\\O by\\O Chinatown\\O standards\\O ,\\O is\\O worth\\B it\\O .\\O"}], "postag": ["DT", "NN", "IN", "DT", "JJ", "NN", ",", "IN", "JJ", "IN", "NNP", "NNS", ",", "VBZ", "JJ", "PRP", "."], "head": [2, 15, 6, 6, 6, 2, 2, 9, 15, 12, 12, 9, 15, 15, 0, 15, 15], "deprel": ["det", "nsubj", "case", "det", "amod", "nmod", "punct", "mark", "advcl", "case", "compound", "obl", "punct", "cop", "root", "obj", "punct"]}, {"id": "108", "sentence": "A few tips : skip the turnip cake , roast pork buns and egg custards .", "triples": [{"uid": "108-0", "sentiment": "negative", "target_tags": "A\\O few\\O tips\\O :\\O skip\\O the\\O turnip\\B cake\\I ,\\O roast\\O pork\\O buns\\O and\\O egg\\O custards\\O .\\O", "opinion_tags": "A\\O few\\O tips\\O :\\O skip\\B the\\O turnip\\O cake\\O ,\\O roast\\O pork\\O buns\\O and\\O egg\\O custards\\O .\\O"}, {"uid": "108-1", "sentiment": "negative", "target_tags": "A\\O few\\O tips\\O :\\O skip\\O the\\O turnip\\O cake\\O ,\\O roast\\B pork\\I buns\\I and\\O egg\\O custards\\O .\\O", "opinion_tags": "A\\O few\\O tips\\O :\\O skip\\B the\\O turnip\\O cake\\O ,\\O roast\\O pork\\O buns\\O and\\O egg\\O custards\\O .\\O"}, {"uid": "108-2", "sentiment": "negative", "target_tags": "A\\O few\\O tips\\O :\\O skip\\O the\\O turnip\\O cake\\O ,\\O roast\\O pork\\O buns\\O and\\O egg\\B custards\\I .\\O", "opinion_tags": "A\\O few\\O tips\\O :\\O skip\\B the\\O turnip\\O cake\\O ,\\O roast\\O pork\\O buns\\O and\\O egg\\O custards\\O .\\O"}], "postag": ["DT", "JJ", "NNS", ":", "VB", "DT", "NN", "NN", ",", "VB", "NN", "NNS", "CC", "NN", "NNS", "."], "head": [3, 3, 0, 3, 3, 8, 8, 5, 10, 5, 12, 10, 15, 15, 12, 3], "deprel": ["det", "amod", "root", "punct", "parataxis", "det", "compound", "obj", "punct", "conj", "compound", "obj", "cc", "compound", "conj", "punct"]}, {"id": "109", "sentence": "The food was exceptional .", "triples": [{"uid": "109-0", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O exceptional\\O .\\O", "opinion_tags": "The\\O food\\O was\\O exceptional\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"]}, {"id": "110", "sentence": "it 's a perfect place to have a amazing indian food .", "triples": [{"uid": "110-0", "sentiment": "positive", "target_tags": "it\\O 's\\O a\\O perfect\\O place\\O to\\O have\\O a\\O amazing\\O indian\\B food\\I .\\O", "opinion_tags": "it\\O 's\\O a\\O perfect\\O place\\O to\\O have\\O a\\O amazing\\B indian\\O food\\O .\\O"}], "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "DT", "JJ", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 11, 11, 11, 7, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "csubj", "det", "amod", "amod", "obj", "punct"]}, {"id": "111", "sentence": "Drawbacks : service is slow and they do n't toast !", "triples": [{"uid": "111-0", "sentiment": "negative", "target_tags": "Drawbacks\\O :\\O service\\B is\\O slow\\O and\\O they\\O do\\O n't\\O toast\\O !\\O", "opinion_tags": "Drawbacks\\O :\\O service\\O is\\O slow\\B and\\O they\\O do\\O n't\\O toast\\O !\\O"}], "postag": ["NNS", ":", "NN", "VBZ", "JJ", "CC", "PRP", "VBP", "RB", "VB", "."], "head": [0, 1, 5, 5, 1, 10, 10, 10, 10, 5, 1], "deprel": ["root", "punct", "nsubj", "cop", "appos", "cc", "nsubj", "aux", "advmod", "conj", "punct"]}, {"id": "112", "sentence": "Downtown Dinner 2002 - Prixe fix : Appetizers were ok , waiter gave me poor suggestion ... try the potato stuff kanish best one .", "triples": [{"uid": "112-0", "sentiment": "neutral", "target_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\B were\\O ok\\O ,\\O waiter\\O gave\\O me\\O poor\\O suggestion\\O ...\\O try\\O the\\O potato\\O stuff\\O kanish\\O best\\O one\\O .\\O", "opinion_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\O were\\O ok\\B ,\\O waiter\\O gave\\O me\\O poor\\O suggestion\\O ...\\O try\\O the\\O potato\\O stuff\\O kanish\\O best\\O one\\O .\\O"}, {"uid": "112-1", "sentiment": "negative", "target_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\O were\\O ok\\O ,\\O waiter\\B gave\\O me\\O poor\\O suggestion\\O ...\\O try\\O the\\O potato\\O stuff\\O kanish\\O best\\O one\\O .\\O", "opinion_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\O were\\O ok\\O ,\\O waiter\\O gave\\O me\\O poor\\B suggestion\\O ...\\O try\\O the\\O potato\\O stuff\\O kanish\\O best\\O one\\O .\\O"}, {"uid": "112-2", "sentiment": "positive", "target_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\O were\\O ok\\O ,\\O waiter\\O gave\\O me\\O poor\\O suggestion\\O ...\\O try\\O the\\O potato\\B stuff\\I kanish\\I best\\O one\\O .\\O", "opinion_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\O were\\O ok\\O ,\\O waiter\\O gave\\O me\\O poor\\O suggestion\\O ...\\O try\\B the\\O potato\\O stuff\\O kanish\\O best\\O one\\O .\\O"}, {"uid": "112-3", "sentiment": "positive", "target_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\O were\\O ok\\O ,\\O waiter\\O gave\\O me\\O poor\\O suggestion\\O ...\\O try\\O the\\O potato\\B stuff\\I kanish\\I best\\O one\\O .\\O", "opinion_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\O were\\O ok\\O ,\\O waiter\\O gave\\O me\\O poor\\O suggestion\\O ...\\O try\\O the\\O potato\\O stuff\\O kanish\\O best\\B one\\O .\\O"}], "postag": ["NN", "NN", "CD", ",", "NNP", "NN", ":", "NNS", "VBD", "JJ", ",", "NN", "VBD", "PRP", "JJ", "NN", ",", "VB", "DT", "NN", "NN", "IN", "JJS", "NN", "."], "head": [2, 0, 2, 2, 6, 2, 2, 10, 10, 2, 10, 13, 10, 13, 16, 13, 10, 2, 21, 21, 18, 24, 24, 18, 2], "deprel": ["compound", "root", "nummod", "punct", "compound", "parataxis", "punct", "nsubj", "cop", "parataxis", "punct", "nsubj", "conj", "i<PERSON><PERSON>", "amod", "obj", "punct", "parataxis", "det", "compound", "obj", "case", "amod", "obl", "punct"]}, {"id": "113", "sentence": "The anti-pasta was excellent , especially the calamari , as were the filling pasta mains .", "triples": [{"uid": "113-0", "sentiment": "positive", "target_tags": "The\\O anti-pasta\\B was\\O excellent\\O ,\\O especially\\O the\\O calamari\\O ,\\O as\\O were\\O the\\O filling\\O pasta\\O mains\\O .\\O", "opinion_tags": "The\\O anti-pasta\\O was\\O excellent\\B ,\\O especially\\O the\\O calamari\\O ,\\O as\\O were\\O the\\O filling\\O pasta\\O mains\\O .\\O"}, {"uid": "113-1", "sentiment": "positive", "target_tags": "The\\O anti-pasta\\O was\\O excellent\\O ,\\O especially\\O the\\O calamari\\B ,\\O as\\O were\\O the\\O filling\\O pasta\\O mains\\O .\\O", "opinion_tags": "The\\O anti-pasta\\O was\\O excellent\\B ,\\O especially\\O the\\O calamari\\O ,\\O as\\O were\\O the\\O filling\\O pasta\\O mains\\O .\\O"}, {"uid": "113-2", "sentiment": "positive", "target_tags": "The\\O anti-pasta\\O was\\O excellent\\O ,\\O especially\\O the\\O calamari\\O ,\\O as\\O were\\O the\\O filling\\O pasta\\B mains\\I .\\O", "opinion_tags": "The\\O anti-pasta\\O was\\O excellent\\B ,\\O especially\\O the\\O calamari\\O ,\\O as\\O were\\O the\\O filling\\O pasta\\O mains\\O .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", ",", "RB", "DT", "NN", ",", "IN", "VBD", "DT", "VBG", "NN", "NNS", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 4, 15, 15, 15, 15, 15, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "advmod", "det", "parataxis", "punct", "mark", "cop", "det", "amod", "compound", "advcl", "punct"]}, {"id": "114", "sentence": "Great food , great decor , great service .", "triples": [{"uid": "114-0", "sentiment": "positive", "target_tags": "Great\\O food\\B ,\\O great\\O decor\\O ,\\O great\\O service\\O .\\O", "opinion_tags": "Great\\B food\\O ,\\O great\\O decor\\O ,\\O great\\O service\\O .\\O"}, {"uid": "114-1", "sentiment": "positive", "target_tags": "Great\\O food\\O ,\\O great\\O decor\\B ,\\O great\\O service\\O .\\O", "opinion_tags": "Great\\O food\\O ,\\O great\\B decor\\O ,\\O great\\O service\\O .\\O"}, {"uid": "114-2", "sentiment": "positive", "target_tags": "Great\\O food\\O ,\\O great\\O decor\\O ,\\O great\\O service\\B .\\O", "opinion_tags": "Great\\O food\\O ,\\O great\\O decor\\O ,\\O great\\B service\\O .\\O"}], "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "amod", "conj", "punct"]}, {"id": "115", "sentence": "Not the typical NYC gimmick theme restaurant .", "triples": [{"uid": "115-0", "sentiment": "positive", "target_tags": "Not\\O the\\O typical\\O NYC\\O gimmick\\O theme\\O restaurant\\B .\\O", "opinion_tags": "Not\\B the\\I typical\\I NYC\\O gimmick\\O theme\\O restaurant\\O .\\O"}], "postag": ["RB", "DT", "JJ", "NNP", "NN", "NN", "NN", "."], "head": [7, 7, 7, 5, 7, 7, 0, 7], "deprel": ["advmod", "det", "amod", "compound", "compound", "compound", "root", "punct"]}, {"id": "116", "sentence": "Service was very prompt but slightly rushed .", "triples": [{"uid": "116-0", "sentiment": "negative", "target_tags": "Service\\B was\\O very\\O prompt\\O but\\O slightly\\O rushed\\O .\\O", "opinion_tags": "Service\\O was\\O very\\O prompt\\B but\\O slightly\\O rushed\\O .\\O"}, {"uid": "116-1", "sentiment": "negative", "target_tags": "Service\\B was\\O very\\O prompt\\O but\\O slightly\\O rushed\\O .\\O", "opinion_tags": "Service\\O was\\O very\\O prompt\\O but\\O slightly\\O rushed\\B .\\O"}], "postag": ["NN", "VBD", "RB", "JJ", "CC", "RB", "VBD", "."], "head": [4, 4, 4, 0, 7, 7, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "cc", "advmod", "conj", "punct"]}, {"id": "117", "sentence": "I ca n't wait for summer , when they serve outside on their gigantic patio .", "triples": [{"uid": "117-0", "sentiment": "positive", "target_tags": "I\\O ca\\O n't\\O wait\\O for\\O summer\\O ,\\O when\\O they\\O serve\\O outside\\O on\\O their\\O gigantic\\O patio\\B .\\O", "opinion_tags": "I\\O ca\\O n't\\O wait\\O for\\O summer\\O ,\\O when\\O they\\O serve\\O outside\\O on\\O their\\O gigantic\\B patio\\O .\\O"}], "postag": ["PRP", "MD", "RB", "VB", "IN", "NN", ",", "WRB", "PRP", "VBP", "RB", "IN", "PRP$", "JJ", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 4, 10, 10, 4, 10, 15, 15, 15, 10, 4], "deprel": ["nsubj", "aux", "advmod", "root", "case", "obl", "punct", "mark", "nsubj", "advcl", "advmod", "case", "nmod:poss", "amod", "obl", "punct"]}, {"id": "118", "sentence": "Good drink .", "triples": [{"uid": "118-0", "sentiment": "positive", "target_tags": "Good\\O drink\\B .\\O", "opinion_tags": "Good\\B drink\\O .\\O"}], "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"]}, {"id": "119", "sentence": "i 've been to sapphire twice and both times the food was fine , if not good .", "triples": [{"uid": "119-0", "sentiment": "positive", "target_tags": "i\\O 've\\O been\\O to\\O sapphire\\O twice\\O and\\O both\\O times\\O the\\O food\\B was\\O fine\\O ,\\O if\\O not\\O good\\O .\\O", "opinion_tags": "i\\O 've\\O been\\O to\\O sapphire\\O twice\\O and\\O both\\O times\\O the\\O food\\O was\\O fine\\B ,\\O if\\O not\\O good\\O .\\O"}], "postag": ["PRP", "VBP", "VBN", "IN", "NN", "RB", "CC", "DT", "NNS", "DT", "NN", "VBD", "JJ", ",", "IN", "RB", "JJ", "."], "head": [5, 5, 5, 5, 0, 5, 13, 9, 13, 11, 13, 13, 5, 13, 17, 17, 13, 5], "deprel": ["nsubj", "aux", "cop", "case", "root", "advmod", "cc", "det", "obl:tmod", "det", "nsubj", "cop", "conj", "punct", "mark", "advmod", "advcl", "punct"]}, {"id": "120", "sentence": "stick with the chicken , beef , and lamb dishes .", "triples": [{"uid": "120-0", "sentiment": "positive", "target_tags": "stick\\O with\\O the\\O chicken\\B ,\\O beef\\O ,\\O and\\O lamb\\O dishes\\O .\\O", "opinion_tags": "stick\\B with\\O the\\O chicken\\O ,\\O beef\\O ,\\O and\\O lamb\\O dishes\\O .\\O"}, {"uid": "120-1", "sentiment": "positive", "target_tags": "stick\\O with\\O the\\O chicken\\O ,\\O beef\\B ,\\O and\\O lamb\\O dishes\\O .\\O", "opinion_tags": "stick\\B with\\O the\\O chicken\\O ,\\O beef\\O ,\\O and\\O lamb\\O dishes\\O .\\O"}, {"uid": "120-2", "sentiment": "positive", "target_tags": "stick\\O with\\O the\\O chicken\\O ,\\O beef\\O ,\\O and\\O lamb\\B dishes\\I .\\O", "opinion_tags": "stick\\B with\\O the\\O chicken\\O ,\\O beef\\O ,\\O and\\O lamb\\O dishes\\O .\\O"}], "postag": ["VB", "IN", "DT", "NN", ",", "NN", ",", "CC", "NN", "NNS", "."], "head": [0, 4, 4, 1, 6, 4, 10, 10, 10, 4, 1], "deprel": ["root", "case", "det", "obl", "punct", "conj", "punct", "cc", "compound", "conj", "punct"]}, {"id": "121", "sentence": "service is friendly , and never had a problem walking in and getting a table .", "triples": [{"uid": "121-0", "sentiment": "positive", "target_tags": "service\\B is\\O friendly\\O ,\\O and\\O never\\O had\\O a\\O problem\\O walking\\O in\\O and\\O getting\\O a\\O table\\O .\\O", "opinion_tags": "service\\O is\\O friendly\\B ,\\O and\\O never\\O had\\O a\\O problem\\O walking\\O in\\O and\\O getting\\O a\\O table\\O .\\O"}], "postag": ["NN", "VBZ", "JJ", ",", "CC", "RB", "VBD", "DT", "NN", "VBG", "RB", "CC", "VBG", "DT", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 9, 7, 9, 10, 13, 10, 15, 13, 3], "deprel": ["nsubj", "cop", "root", "punct", "cc", "advmod", "conj", "det", "obj", "acl", "advmod", "cc", "conj", "det", "obj", "punct"]}, {"id": "122", "sentence": "skip dessert .", "triples": [{"uid": "122-0", "sentiment": "negative", "target_tags": "skip\\O dessert\\B .\\O", "opinion_tags": "skip\\B dessert\\O .\\O"}], "postag": ["VB", "NN", "."], "head": [0, 1, 1], "deprel": ["root", "obj", "punct"]}, {"id": "123", "sentence": "Pizza was a little soggy .", "triples": [{"uid": "123-0", "sentiment": "negative", "target_tags": "Pizza\\B was\\O a\\O little\\O soggy\\O .\\O", "opinion_tags": "Pizza\\O was\\O a\\O little\\O soggy\\B .\\O"}], "postag": ["NN", "VBD", "DT", "JJ", "JJ", "."], "head": [5, 5, 4, 5, 0, 5], "deprel": ["nsubj", "cop", "det", "obl:npmod", "root", "punct"]}, {"id": "124", "sentence": "This place is a great bargain .", "triples": [{"uid": "124-0", "sentiment": "positive", "target_tags": "This\\O place\\B is\\O a\\O great\\O bargain\\O .\\O", "opinion_tags": "This\\O place\\O is\\O a\\O great\\B bargain\\I .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "JJ", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 6], "deprel": ["det", "nsubj", "cop", "det", "amod", "root", "punct"]}, {"id": "125", "sentence": "The design and atmosphere is just as good .", "triples": [{"uid": "125-0", "sentiment": "positive", "target_tags": "The\\O design\\B and\\O atmosphere\\O is\\O just\\O as\\O good\\O .\\O", "opinion_tags": "The\\O design\\O and\\O atmosphere\\O is\\O just\\O as\\O good\\B .\\O"}, {"uid": "125-1", "sentiment": "positive", "target_tags": "The\\O design\\O and\\O atmosphere\\B is\\O just\\O as\\O good\\O .\\O", "opinion_tags": "The\\O design\\O and\\O atmosphere\\O is\\O just\\O as\\O good\\B .\\O"}], "postag": ["DT", "NN", "CC", "NN", "VBZ", "RB", "RB", "JJ", "."], "head": [2, 8, 4, 2, 8, 8, 8, 0, 8], "deprel": ["det", "nsubj", "cc", "conj", "cop", "advmod", "advmod", "root", "punct"]}, {"id": "126", "sentence": "the drinks are amazing and half off till 8pm .", "triples": [{"uid": "126-0", "sentiment": "positive", "target_tags": "the\\O drinks\\B are\\O amazing\\O and\\O half\\O off\\O till\\O 8pm\\O .\\O", "opinion_tags": "the\\O drinks\\O are\\O amazing\\B and\\O half\\O off\\O till\\O 8pm\\O .\\O"}], "postag": ["DT", "NNS", "VBP", "JJ", "CC", "JJ", "RP", "IN", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 4, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "conj", "case", "obl", "punct"]}, {"id": "127", "sentence": "This is an amazing place to try some roti rolls .", "triples": [{"uid": "127-0", "sentiment": "positive", "target_tags": "This\\O is\\O an\\O amazing\\O place\\O to\\O try\\O some\\O roti\\B rolls\\I .\\O", "opinion_tags": "This\\O is\\O an\\O amazing\\O place\\O to\\O try\\B some\\O roti\\O rolls\\O .\\O"}], "postag": ["DT", "VBZ", "DT", "JJ", "NN", "TO", "VB", "DT", "NN", "NNS", "."], "head": [5, 5, 5, 5, 0, 7, 5, 10, 10, 7, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "det", "compound", "obj", "punct"]}, {"id": "128", "sentence": "The food 's as good as ever .", "triples": [{"uid": "128-0", "sentiment": "positive", "target_tags": "The\\O food\\B 's\\O as\\O good\\O as\\O ever\\O .\\O", "opinion_tags": "The\\O food\\O 's\\O as\\O good\\B as\\O ever\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "IN", "RB", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "obl", "punct"]}, {"id": "129", "sentence": "Best drumsticks over rice and sour spicy soup in town !", "triples": [{"uid": "129-0", "sentiment": "positive", "target_tags": "Best\\O drumsticks\\B over\\I rice\\I and\\O sour\\O spicy\\O soup\\O in\\O town\\O !\\O", "opinion_tags": "Best\\B drumsticks\\O over\\O rice\\O and\\O sour\\O spicy\\O soup\\O in\\O town\\O !\\O"}, {"uid": "129-1", "sentiment": "positive", "target_tags": "Best\\O drumsticks\\O over\\O rice\\O and\\O sour\\B spicy\\I soup\\I in\\O town\\O !\\O", "opinion_tags": "Best\\B drumsticks\\O over\\O rice\\O and\\O sour\\O spicy\\O soup\\O in\\O town\\O !\\O"}], "postag": ["JJS", "NNS", "IN", "NN", "CC", "JJ", "JJ", "NN", "IN", "NN", "."], "head": [2, 0, 4, 2, 8, 8, 8, 4, 10, 2, 2], "deprel": ["amod", "root", "case", "nmod", "cc", "amod", "amod", "conj", "case", "nmod", "punct"]}, {"id": "130", "sentence": "The service is good and the resturant is clean .", "triples": [{"uid": "130-0", "sentiment": "positive", "target_tags": "The\\O service\\B is\\O good\\O and\\O the\\O resturant\\O is\\O clean\\O .\\O", "opinion_tags": "The\\O service\\O is\\O good\\B and\\O the\\O resturant\\O is\\O clean\\O .\\O"}, {"uid": "130-1", "sentiment": "positive", "target_tags": "The\\O service\\O is\\O good\\O and\\O the\\O resturant\\B is\\O clean\\O .\\O", "opinion_tags": "The\\O service\\O is\\O good\\O and\\O the\\O resturant\\O is\\O clean\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "131", "sentence": "We recently decided to try this location , and to our delight , they have outdoor seating , perfect since I had my yorkie with me .", "triples": [{"uid": "131-0", "sentiment": "positive", "target_tags": "We\\O recently\\O decided\\O to\\O try\\O this\\O location\\O ,\\O and\\O to\\O our\\O delight\\O ,\\O they\\O have\\O outdoor\\B seating\\I ,\\O perfect\\O since\\O I\\O had\\O my\\O yorkie\\O with\\O me\\O .\\O", "opinion_tags": "We\\O recently\\O decided\\O to\\O try\\O this\\O location\\O ,\\O and\\O to\\O our\\O delight\\O ,\\O they\\O have\\O outdoor\\O seating\\O ,\\O perfect\\B since\\O I\\O had\\O my\\O yorkie\\O with\\O me\\O .\\O"}], "postag": ["PRP", "RB", "VBD", "TO", "VB", "DT", "NN", ",", "CC", "IN", "PRP$", "NN", ",", "PRP", "VBP", "JJ", "NN", ",", "JJ", "IN", "PRP", "VBD", "PRP$", "NN", "IN", "PRP", "."], "head": [3, 3, 0, 5, 3, 7, 5, 15, 15, 12, 12, 15, 15, 15, 3, 17, 15, 19, 17, 22, 22, 15, 24, 22, 26, 22, 3], "deprel": ["nsubj", "advmod", "root", "mark", "xcomp", "det", "obj", "punct", "cc", "case", "nmod:poss", "obl", "punct", "nsubj", "conj", "amod", "obj", "punct", "amod", "mark", "nsubj", "advcl", "nmod:poss", "obj", "case", "obl", "punct"]}, {"id": "132", "sentence": "Great Indian food and the service is incredible .", "triples": [{"uid": "132-0", "sentiment": "positive", "target_tags": "Great\\O Indian\\B food\\I and\\O the\\O service\\O is\\O incredible\\O .\\O", "opinion_tags": "Great\\B Indian\\O food\\O and\\O the\\O service\\O is\\O incredible\\O .\\O"}, {"uid": "132-1", "sentiment": "positive", "target_tags": "Great\\O Indian\\O food\\O and\\O the\\O service\\B is\\O incredible\\O .\\O", "opinion_tags": "Great\\O Indian\\O food\\O and\\O the\\O service\\O is\\O incredible\\B .\\O"}], "postag": ["JJ", "JJ", "NN", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [3, 3, 0, 8, 6, 8, 8, 3, 3], "deprel": ["amod", "amod", "root", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "133", "sentence": "The food here does a great service to the name ( Cantonese that is ... ) .", "triples": [{"uid": "133-0", "sentiment": "positive", "target_tags": "The\\O food\\B here\\O does\\O a\\O great\\O service\\O to\\O the\\O name\\O (\\O Cantonese\\O that\\O is\\O ...\\O )\\O .\\O", "opinion_tags": "The\\O food\\O here\\O does\\O a\\O great\\B service\\O to\\O the\\O name\\O (\\O Cantonese\\O that\\O is\\O ...\\O )\\O .\\O"}], "postag": ["DT", "NN", "RB", "VBZ", "DT", "JJ", "NN", "IN", "DT", "NN", "-LRB-", "NNP", "WDT", "VBZ", ",", "-RRB-", "."], "head": [2, 4, 2, 0, 7, 7, 4, 10, 10, 4, 12, 4, 14, 12, 12, 12, 4], "deprel": ["det", "nsubj", "advmod", "root", "det", "amod", "obj", "case", "det", "obl", "punct", "parataxis", "nsubj", "acl:relcl", "punct", "punct", "punct"]}, {"id": "134", "sentence": "good music , great food , speedy service affordable prices .", "triples": [{"uid": "134-0", "sentiment": "positive", "target_tags": "good\\O music\\B ,\\O great\\O food\\O ,\\O speedy\\O service\\O affordable\\O prices\\O .\\O", "opinion_tags": "good\\B music\\O ,\\O great\\O food\\O ,\\O speedy\\O service\\O affordable\\O prices\\O .\\O"}, {"uid": "134-1", "sentiment": "positive", "target_tags": "good\\O music\\O ,\\O great\\O food\\B ,\\O speedy\\O service\\O affordable\\O prices\\O .\\O", "opinion_tags": "good\\O music\\O ,\\O great\\B food\\O ,\\O speedy\\O service\\O affordable\\O prices\\O .\\O"}, {"uid": "134-2", "sentiment": "positive", "target_tags": "good\\O music\\O ,\\O great\\O food\\O ,\\O speedy\\O service\\B affordable\\O prices\\O .\\O", "opinion_tags": "good\\O music\\O ,\\O great\\O food\\O ,\\O speedy\\B service\\O affordable\\O prices\\O .\\O"}], "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "JJ", "NN", "JJ", "NNS", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 10, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "amod", "conj", "amod", "conj", "punct"]}, {"id": "135", "sentence": "Atmosphere is nice and relaxed too ...", "triples": [{"uid": "135-0", "sentiment": "positive", "target_tags": "Atmosphere\\B is\\O nice\\O and\\O relaxed\\O too\\O ...\\O", "opinion_tags": "Atmosphere\\O is\\O nice\\B and\\O relaxed\\O too\\O ...\\O"}, {"uid": "135-1", "sentiment": "positive", "target_tags": "Atmosphere\\B is\\O nice\\O and\\O relaxed\\O too\\O ...\\O", "opinion_tags": "Atmosphere\\O is\\O nice\\O and\\O relaxed\\B too\\O ...\\O"}], "postag": ["NN", "VBZ", "JJ", "CC", "JJ", "RB", "."], "head": [3, 3, 0, 5, 3, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "advmod", "punct"]}, {"id": "136", "sentence": "If you do n't mind pre-sliced low quality fish , unfriendly staff and a sushi chef that looks like he is miserable then this is your place .", "triples": [{"uid": "136-0", "sentiment": "negative", "target_tags": "If\\O you\\O do\\O n't\\O mind\\O pre-sliced\\O low\\O quality\\O fish\\B ,\\O unfriendly\\O staff\\O and\\O a\\O sushi\\O chef\\O that\\O looks\\O like\\O he\\O is\\O miserable\\O then\\O this\\O is\\O your\\O place\\O .\\O", "opinion_tags": "If\\O you\\O do\\O n't\\O mind\\O pre-sliced\\O low\\B quality\\I fish\\O ,\\O unfriendly\\O staff\\O and\\O a\\O sushi\\O chef\\O that\\O looks\\O like\\O he\\O is\\O miserable\\O then\\O this\\O is\\O your\\O place\\O .\\O"}, {"uid": "136-1", "sentiment": "negative", "target_tags": "If\\O you\\O do\\O n't\\O mind\\O pre-sliced\\O low\\O quality\\O fish\\O ,\\O unfriendly\\O staff\\B and\\O a\\O sushi\\O chef\\O that\\O looks\\O like\\O he\\O is\\O miserable\\O then\\O this\\O is\\O your\\O place\\O .\\O", "opinion_tags": "If\\O you\\O do\\O n't\\O mind\\O pre-sliced\\O low\\O quality\\O fish\\O ,\\O unfriendly\\B staff\\O and\\O a\\O sushi\\O chef\\O that\\O looks\\O like\\O he\\O is\\O miserable\\O then\\O this\\O is\\O your\\O place\\O .\\O"}, {"uid": "136-2", "sentiment": "negative", "target_tags": "If\\O you\\O do\\O n't\\O mind\\O pre-sliced\\O low\\O quality\\O fish\\O ,\\O unfriendly\\O staff\\O and\\O a\\O sushi\\B chef\\I that\\O looks\\O like\\O he\\O is\\O miserable\\O then\\O this\\O is\\O your\\O place\\O .\\O", "opinion_tags": "If\\O you\\O do\\O n't\\O mind\\O pre-sliced\\O low\\O quality\\O fish\\O ,\\O unfriendly\\O staff\\O and\\O a\\O sushi\\O chef\\O that\\O looks\\O like\\O he\\O is\\O miserable\\B then\\O this\\O is\\O your\\O place\\O .\\O"}], "postag": ["IN", "PRP", "VBP", "RB", "VB", "JJ", "JJ", "JJ", "NN", ",", "JJ", "NN", "CC", "DT", "NN", "NN", "WDT", "VBZ", "IN", "PRP", "VBZ", "JJ", "RB", "DT", "VBZ", "PRP$", "NN", "."], "head": [5, 5, 5, 5, 27, 9, 9, 9, 5, 12, 12, 9, 16, 16, 16, 9, 18, 16, 22, 22, 22, 18, 27, 27, 27, 27, 0, 27], "deprel": ["mark", "nsubj", "aux", "advmod", "advcl", "amod", "amod", "amod", "obj", "punct", "amod", "conj", "cc", "det", "compound", "conj", "nsubj", "acl:relcl", "mark", "nsubj", "cop", "advcl", "advmod", "nsubj", "cop", "nmod:poss", "root", "punct"]}, {"id": "137", "sentence": "One would think we 'd get an apology or complimentary drinks - instead , we got a snobby waiter would n't even take our order for 15 minutes and gave us lip when we asked him to do so .", "triples": [{"uid": "137-0", "sentiment": "negative", "target_tags": "One\\O would\\O think\\O we\\O 'd\\O get\\O an\\O apology\\O or\\O complimentary\\O drinks\\O -\\O instead\\O ,\\O we\\O got\\O a\\O snobby\\O waiter\\B would\\O n't\\O even\\O take\\O our\\O order\\O for\\O 15\\O minutes\\O and\\O gave\\O us\\O lip\\O when\\O we\\O asked\\O him\\O to\\O do\\O so\\O .\\O", "opinion_tags": "One\\O would\\O think\\O we\\O 'd\\O get\\O an\\O apology\\O or\\O complimentary\\O drinks\\O -\\O instead\\O ,\\O we\\O got\\O a\\O snobby\\B waiter\\O would\\O n't\\O even\\O take\\O our\\O order\\O for\\O 15\\O minutes\\O and\\O gave\\O us\\O lip\\O when\\O we\\O asked\\O him\\O to\\O do\\O so\\O .\\O"}], "postag": ["PRP", "MD", "VB", "PRP", "MD", "VB", "DT", "NN", "CC", "JJ", "NNS", ",", "RB", ",", "PRP", "VBD", "DT", "JJ", "NN", "MD", "RB", "RB", "VB", "PRP$", "NN", "IN", "CD", "NNS", "CC", "VBD", "PRP", "NN", "WRB", "PRP", "VBD", "PRP", "TO", "VB", "RB", "."], "head": [3, 3, 0, 6, 6, 3, 8, 6, 11, 11, 8, 3, 16, 16, 16, 3, 19, 19, 16, 23, 23, 23, 16, 25, 23, 28, 28, 23, 30, 23, 30, 30, 35, 35, 30, 35, 38, 35, 38, 3], "deprel": ["nsubj", "aux", "root", "nsubj", "aux", "ccomp", "det", "obj", "cc", "amod", "conj", "punct", "advmod", "punct", "nsubj", "parataxis", "det", "amod", "obj", "aux", "advmod", "advmod", "parataxis", "nmod:poss", "obj", "case", "nummod", "obl", "cc", "conj", "i<PERSON><PERSON>", "obj", "mark", "nsubj", "advcl", "obj", "mark", "xcomp", "advmod", "punct"]}, {"id": "138", "sentence": "With so many good restaurants on the UWS , I do n't need overpriced food , absurdly arrogant wait-staff who do n't recognize they work at a glorified diner , clumsy service , and management that does n't care .", "triples": [{"uid": "138-0", "sentiment": "negative", "target_tags": "With\\O so\\O many\\O good\\O restaurants\\O on\\O the\\O UWS\\O ,\\O I\\O do\\O n't\\O need\\O overpriced\\O food\\B ,\\O absurdly\\O arrogant\\O wait-staff\\O who\\O do\\O n't\\O recognize\\O they\\O work\\O at\\O a\\O glorified\\O diner\\O ,\\O clumsy\\O service\\O ,\\O and\\O management\\O that\\O does\\O n't\\O care\\O .\\O", "opinion_tags": "With\\O so\\O many\\O good\\O restaurants\\O on\\O the\\O UWS\\O ,\\O I\\O do\\O n't\\O need\\O overpriced\\B food\\O ,\\O absurdly\\O arrogant\\O wait-staff\\O who\\O do\\O n't\\O recognize\\O they\\O work\\O at\\O a\\O glorified\\O diner\\O ,\\O clumsy\\O service\\O ,\\O and\\O management\\O that\\O does\\O n't\\O care\\O .\\O"}, {"uid": "138-1", "sentiment": "negative", "target_tags": "With\\O so\\O many\\O good\\O restaurants\\O on\\O the\\O UWS\\O ,\\O I\\O do\\O n't\\O need\\O overpriced\\O food\\O ,\\O absurdly\\O arrogant\\O wait-staff\\B who\\O do\\O n't\\O recognize\\O they\\O work\\O at\\O a\\O glorified\\O diner\\O ,\\O clumsy\\O service\\O ,\\O and\\O management\\O that\\O does\\O n't\\O care\\O .\\O", "opinion_tags": "With\\O so\\O many\\O good\\O restaurants\\O on\\O the\\O UWS\\O ,\\O I\\O do\\O n't\\O need\\O overpriced\\O food\\O ,\\O absurdly\\O arrogant\\B wait-staff\\O who\\O do\\O n't\\O recognize\\O they\\O work\\O at\\O a\\O glorified\\O diner\\O ,\\O clumsy\\O service\\O ,\\O and\\O management\\O that\\O does\\O n't\\O care\\O .\\O"}, {"uid": "138-2", "sentiment": "negative", "target_tags": "With\\O so\\O many\\O good\\O restaurants\\O on\\O the\\O UWS\\O ,\\O I\\O do\\O n't\\O need\\O overpriced\\O food\\O ,\\O absurdly\\O arrogant\\O wait-staff\\O who\\O do\\O n't\\O recognize\\O they\\O work\\O at\\O a\\O glorified\\O diner\\O ,\\O clumsy\\O service\\B ,\\O and\\O management\\O that\\O does\\O n't\\O care\\O .\\O", "opinion_tags": "With\\O so\\O many\\O good\\O restaurants\\O on\\O the\\O UWS\\O ,\\O I\\O do\\O n't\\O need\\O overpriced\\O food\\O ,\\O absurdly\\O arrogant\\O wait-staff\\O who\\O do\\O n't\\O recognize\\O they\\O work\\O at\\O a\\O glorified\\O diner\\O ,\\O clumsy\\B service\\O ,\\O and\\O management\\O that\\O does\\O n't\\O care\\O .\\O"}], "postag": ["IN", "RB", "JJ", "JJ", "NNS", "IN", "DT", "NNP", ",", "PRP", "VBP", "RB", "VB", "JJ", "NN", ",", "RB", "JJ", "NN", "WP", "VBP", "RB", "VB", "PRP", "VBP", "IN", "DT", "VBN", "NN", ",", "JJ", "NN", ",", "CC", "NN", "WDT", "VBZ", "RB", "VB", "."], "head": [5, 3, 5, 5, 13, 8, 8, 5, 13, 13, 13, 13, 0, 15, 13, 19, 18, 19, 15, 23, 23, 23, 19, 25, 23, 29, 29, 29, 25, 32, 32, 29, 35, 35, 29, 39, 39, 39, 35, 13], "deprel": ["case", "advmod", "amod", "amod", "obl", "case", "det", "nmod", "punct", "nsubj", "aux", "advmod", "root", "amod", "obj", "punct", "advmod", "amod", "appos", "nsubj", "aux", "advmod", "acl:relcl", "nsubj", "ccomp", "case", "det", "amod", "obl", "punct", "amod", "conj", "punct", "cc", "conj", "nsubj", "aux", "advmod", "acl:relcl", "punct"]}, {"id": "139", "sentence": "$ 20 for all you can eat sushi can not be beaten .", "triples": [{"uid": "139-0", "sentiment": "positive", "target_tags": "$\\O 20\\O for\\O all\\B you\\I can\\I eat\\I sushi\\I can\\O not\\O be\\O beaten\\O .\\O", "opinion_tags": "$\\O 20\\O for\\O all\\O you\\O can\\O eat\\O sushi\\O can\\O not\\O be\\O beaten\\B .\\O"}], "postag": ["$", "CD", "IN", "DT", "PRP", "MD", "VB", "NN", "MD", "RB", "VB", "VBN", "."], "head": [12, 1, 4, 1, 7, 7, 4, 7, 12, 12, 12, 0, 1], "deprel": ["nsubj:pass", "nummod", "case", "nmod", "nsubj", "aux", "acl:relcl", "obj", "aux", "advmod", "aux:pass", "root", "punct"]}, {"id": "140", "sentence": "Food was good and the view of the new york city skiline was terrific even on a foggy rainy day like that of when I went .", "triples": [{"uid": "140-0", "sentiment": "positive", "target_tags": "Food\\B was\\O good\\O and\\O the\\O view\\O of\\O the\\O new\\O york\\O city\\O skiline\\O was\\O terrific\\O even\\O on\\O a\\O foggy\\O rainy\\O day\\O like\\O that\\O of\\O when\\O I\\O went\\O .\\O", "opinion_tags": "Food\\O was\\O good\\B and\\O the\\O view\\O of\\O the\\O new\\O york\\O city\\O skiline\\O was\\O terrific\\O even\\O on\\O a\\O foggy\\O rainy\\O day\\O like\\O that\\O of\\O when\\O I\\O went\\O .\\O"}, {"uid": "140-1", "sentiment": "positive", "target_tags": "Food\\O was\\O good\\O and\\O the\\O view\\B of\\I the\\I new\\I york\\I city\\I skiline\\I was\\O terrific\\O even\\O on\\O a\\O foggy\\O rainy\\O day\\O like\\O that\\O of\\O when\\O I\\O went\\O .\\O", "opinion_tags": "Food\\O was\\O good\\O and\\O the\\O view\\O of\\O the\\O new\\O york\\O city\\O skiline\\O was\\O terrific\\B even\\O on\\O a\\O foggy\\O rainy\\O day\\O like\\O that\\O of\\O when\\O I\\O went\\O .\\O"}], "postag": ["NN", "VBD", "JJ", "CC", "DT", "NN", "IN", "DT", "JJ", "NNP", "NN", "NN", "VBD", "JJ", "RB", "IN", "DT", "JJ", "JJ", "NN", "IN", "DT", "IN", "WRB", "PRP", "VBD", "."], "head": [3, 3, 0, 14, 6, 14, 12, 12, 12, 11, 12, 6, 14, 3, 20, 20, 20, 20, 20, 14, 22, 20, 24, 26, 26, 22, 3], "deprel": ["nsubj", "cop", "root", "cc", "det", "nsubj", "case", "det", "amod", "compound", "compound", "nmod", "cop", "conj", "advmod", "case", "det", "amod", "amod", "obl", "case", "nmod", "case", "mark", "nsubj", "acl:relcl", "punct"]}, {"id": "141", "sentence": "Although they do the typical what kind of water would you like questions the service was good and overall very relaxing to place to eat .", "triples": [{"uid": "141-0", "sentiment": "positive", "target_tags": "Although\\O they\\O do\\O the\\O typical\\O what\\O kind\\O of\\O water\\O would\\O you\\O like\\O questions\\O the\\O service\\B was\\O good\\O and\\O overall\\O very\\O relaxing\\O to\\O place\\O to\\O eat\\O .\\O", "opinion_tags": "Although\\O they\\O do\\O the\\O typical\\O what\\O kind\\O of\\O water\\O would\\O you\\O like\\O questions\\O the\\O service\\O was\\O good\\B and\\O overall\\O very\\O relaxing\\O to\\O place\\O to\\O eat\\O .\\O"}, {"uid": "141-1", "sentiment": "positive", "target_tags": "Although\\O they\\O do\\O the\\O typical\\O what\\O kind\\O of\\O water\\O would\\O you\\O like\\O questions\\O the\\O service\\O was\\O good\\O and\\O overall\\O very\\O relaxing\\O to\\O place\\B to\\O eat\\O .\\O", "opinion_tags": "Although\\O they\\O do\\O the\\O typical\\O what\\O kind\\O of\\O water\\O would\\O you\\O like\\O questions\\O the\\O service\\O was\\O good\\O and\\O overall\\O very\\O relaxing\\B to\\O place\\O to\\O eat\\O .\\O"}], "postag": ["IN", "PRP", "VBP", "DT", "JJ", "WDT", "NN", "IN", "NN", "MD", "PRP", "VB", "NNS", "DT", "NN", "VBD", "JJ", "CC", "RB", "RB", "JJ", "IN", "NN", "TO", "VB", "."], "head": [3, 3, 12, 5, 3, 7, 3, 9, 7, 12, 12, 0, 12, 15, 17, 17, 12, 21, 21, 21, 17, 23, 21, 25, 21, 12], "deprel": ["mark", "nsubj", "advcl", "det", "obj", "det", "obj", "case", "nmod", "aux", "nsubj", "root", "obj", "det", "nsubj", "cop", "ccomp", "cc", "advmod", "advmod", "conj", "case", "obl", "mark", "advcl", "punct"]}, {"id": "142", "sentence": "The pizza was pretty good and huge .", "triples": [{"uid": "142-0", "sentiment": "positive", "target_tags": "The\\O pizza\\B was\\O pretty\\O good\\O and\\O huge\\O .\\O", "opinion_tags": "The\\O pizza\\O was\\O pretty\\O good\\B and\\O huge\\O .\\O"}, {"uid": "142-1", "sentiment": "positive", "target_tags": "The\\O pizza\\B was\\O pretty\\O good\\O and\\O huge\\O .\\O", "opinion_tags": "The\\O pizza\\O was\\O pretty\\O good\\O and\\O huge\\B .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"]}, {"id": "143", "sentence": "La Rosa waltzes in , and I think they are doing it the best .", "triples": [{"uid": "143-0", "sentiment": "positive", "target_tags": "La\\B Rosa\\I waltzes\\O in\\O ,\\O and\\O I\\O think\\O they\\O are\\O doing\\O it\\O the\\O best\\O .\\O", "opinion_tags": "La\\O Rosa\\O waltzes\\O in\\O ,\\O and\\O I\\O think\\O they\\O are\\O doing\\O it\\O the\\O best\\B .\\O"}], "postag": ["NNP", "NNP", "VBZ", "RB", ",", "CC", "PRP", "VBP", "PRP", "VBP", "VBG", "PRP", "DT", "JJS", "."], "head": [3, 1, 0, 3, 8, 8, 8, 3, 11, 11, 8, 11, 14, 11, 3], "deprel": ["nsubj", "flat", "root", "advmod", "punct", "cc", "nsubj", "conj", "nsubj", "aux", "ccomp", "obj", "det", "obl:npmod", "punct"]}, {"id": "144", "sentence": "The mussels were fantastic and so was the dessert ... definitely going to be back very soon .", "triples": [{"uid": "144-0", "sentiment": "positive", "target_tags": "The\\O mussels\\B were\\O fantastic\\O and\\O so\\O was\\O the\\O dessert\\O ...\\O definitely\\O going\\O to\\O be\\O back\\O very\\O soon\\O .\\O", "opinion_tags": "The\\O mussels\\O were\\O fantastic\\B and\\O so\\O was\\O the\\O dessert\\O ...\\O definitely\\O going\\O to\\O be\\O back\\O very\\O soon\\O .\\O"}, {"uid": "144-1", "sentiment": "positive", "target_tags": "The\\O mussels\\O were\\O fantastic\\O and\\O so\\O was\\O the\\O dessert\\B ...\\O definitely\\O going\\O to\\O be\\O back\\O very\\O soon\\O .\\O", "opinion_tags": "The\\O mussels\\O were\\O fantastic\\B and\\O so\\O was\\O the\\O dessert\\O ...\\O definitely\\O going\\O to\\O be\\O back\\O very\\O soon\\O .\\O"}], "postag": ["DT", "NNS", "VBD", "JJ", "CC", "RB", "VBD", "DT", "NN", ",", "RB", "VBG", "TO", "VB", "RB", "RB", "RB", "."], "head": [2, 4, 4, 0, 6, 4, 6, 9, 6, 4, 12, 4, 14, 12, 14, 17, 14, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "cop", "det", "nsubj", "punct", "advmod", "parataxis", "mark", "xcomp", "advmod", "advmod", "advmod", "punct"]}, {"id": "145", "sentence": "I have been coming here for years and have nothing but good things to say about the service and the great staff at La Lanterna .", "triples": [{"uid": "145-0", "sentiment": "positive", "target_tags": "I\\O have\\O been\\O coming\\O here\\O for\\O years\\O and\\O have\\O nothing\\O but\\O good\\O things\\O to\\O say\\O about\\O the\\O service\\B and\\O the\\O great\\O staff\\O at\\O La\\O Lanterna\\O .\\O", "opinion_tags": "I\\O have\\O been\\O coming\\O here\\O for\\O years\\O and\\O have\\O nothing\\O but\\O good\\B things\\O to\\O say\\O about\\O the\\O service\\O and\\O the\\O great\\O staff\\O at\\O La\\O Lanterna\\O .\\O"}, {"uid": "145-1", "sentiment": "positive", "target_tags": "I\\O have\\O been\\O coming\\O here\\O for\\O years\\O and\\O have\\O nothing\\O but\\O good\\O things\\O to\\O say\\O about\\O the\\O service\\O and\\O the\\O great\\O staff\\B at\\O La\\O Lanterna\\O .\\O", "opinion_tags": "I\\O have\\O been\\O coming\\O here\\O for\\O years\\O and\\O have\\O nothing\\O but\\O good\\O things\\O to\\O say\\O about\\O the\\O service\\O and\\O the\\O great\\B staff\\O at\\O La\\O Lanterna\\O .\\O"}], "postag": ["PRP", "VBP", "VBN", "VBG", "RB", "IN", "NNS", "CC", "VBP", "NN", "CC", "JJ", "NNS", "TO", "VB", "IN", "DT", "NN", "CC", "DT", "JJ", "NN", "IN", "NNP", "NNP", "."], "head": [4, 4, 4, 0, 4, 7, 4, 9, 4, 9, 13, 13, 10, 15, 13, 18, 18, 15, 22, 22, 22, 18, 24, 22, 24, 4], "deprel": ["nsubj", "aux", "aux", "root", "advmod", "case", "obl", "cc", "conj", "obj", "cc", "amod", "conj", "mark", "acl", "case", "det", "obl", "cc", "det", "amod", "conj", "case", "nmod", "flat", "punct"]}, {"id": "146", "sentence": "Love Al Di La", "triples": [{"uid": "146-0", "sentiment": "positive", "target_tags": "Love\\O Al\\B Di\\I La\\I", "opinion_tags": "Love\\B Al\\O Di\\O La\\O"}], "postag": ["VBP", "NNP", "NNP", "NNP"], "head": [0, 4, 4, 1], "deprel": ["root", "compound", "compound", "obj"]}, {"id": "147", "sentence": "An awesome organic dog , and a conscious eco friendly establishment .", "triples": [{"uid": "147-0", "sentiment": "positive", "target_tags": "An\\O awesome\\O organic\\O dog\\B ,\\O and\\O a\\O conscious\\O eco\\O friendly\\O establishment\\O .\\O", "opinion_tags": "An\\O awesome\\O organic\\B dog\\O ,\\O and\\O a\\O conscious\\O eco\\O friendly\\O establishment\\O .\\O"}, {"uid": "147-1", "sentiment": "positive", "target_tags": "An\\O awesome\\O organic\\O dog\\O ,\\O and\\O a\\O conscious\\O eco\\O friendly\\O establishment\\B .\\O", "opinion_tags": "An\\O awesome\\O organic\\O dog\\O ,\\O and\\O a\\O conscious\\O eco\\B friendly\\I establishment\\O .\\O"}], "postag": ["DT", "JJ", "JJ", "NN", ",", "CC", "DT", "JJ", "JJ", "JJ", "NN", "."], "head": [4, 4, 4, 0, 11, 11, 11, 11, 11, 11, 4, 4], "deprel": ["det", "amod", "amod", "root", "punct", "cc", "det", "amod", "amod", "amod", "conj", "punct"]}, {"id": "148", "sentence": "But the best pork souvlaki I ever had is the main thing .", "triples": [{"uid": "148-0", "sentiment": "positive", "target_tags": "But\\O the\\O best\\O pork\\B souvlaki\\I I\\O ever\\O had\\O is\\O the\\O main\\O thing\\O .\\O", "opinion_tags": "But\\O the\\O best\\B pork\\O souvlaki\\O I\\O ever\\O had\\O is\\O the\\O main\\O thing\\O .\\O"}], "postag": ["CC", "DT", "JJS", "NN", "NN", "PRP", "RB", "VBD", "VBZ", "DT", "JJ", "NN", "."], "head": [12, 5, 5, 5, 12, 8, 8, 5, 12, 12, 12, 0, 12], "deprel": ["cc", "det", "amod", "compound", "nsubj", "nsubj", "advmod", "acl:relcl", "cop", "det", "amod", "root", "punct"]}, {"id": "149", "sentence": "I LOOOVE their eggplant pizza , as well as their pastas !", "triples": [{"uid": "149-0", "sentiment": "positive", "target_tags": "I\\O LOOOVE\\O their\\O eggplant\\B pizza\\I ,\\O as\\O well\\O as\\O their\\O pastas\\O !\\O", "opinion_tags": "I\\O LOOOVE\\B their\\O eggplant\\O pizza\\O ,\\O as\\O well\\O as\\O their\\O pastas\\O !\\O"}, {"uid": "149-1", "sentiment": "positive", "target_tags": "I\\O LOOOVE\\O their\\O eggplant\\O pizza\\O ,\\O as\\O well\\O as\\O their\\O pastas\\B !\\O", "opinion_tags": "I\\O LOOOVE\\B their\\O eggplant\\O pizza\\O ,\\O as\\O well\\O as\\O their\\O pastas\\O !\\O"}], "postag": ["PRP", "VBP", "PRP$", "NN", "NN", ",", "RB", "RB", "IN", "PRP$", "NNS", "."], "head": [2, 0, 5, 5, 2, 11, 11, 7, 7, 11, 5, 2], "deprel": ["nsubj", "root", "nmod:poss", "compound", "obj", "punct", "cc", "fixed", "fixed", "nmod:poss", "conj", "punct"]}, {"id": "150", "sentence": "We had half/half pizza , mine was eggplant and my friend had the buffalo and it was sooo huge for a small size pizza !", "triples": [{"uid": "150-0", "sentiment": "positive", "target_tags": "We\\O had\\O half/half\\B pizza\\I ,\\O mine\\O was\\O eggplant\\O and\\O my\\O friend\\O had\\O the\\O buffalo\\O and\\O it\\O was\\O sooo\\O huge\\O for\\O a\\O small\\O size\\O pizza\\O !\\O", "opinion_tags": "We\\O had\\O half/half\\O pizza\\O ,\\O mine\\O was\\O eggplant\\O and\\O my\\O friend\\O had\\O the\\O buffalo\\O and\\O it\\O was\\O sooo\\O huge\\B for\\O a\\O small\\O size\\O pizza\\O !\\O"}], "postag": ["PRP", "VBD", "JJ", "NN", ",", "PRP", "VBD", "JJ", "CC", "PRP$", "NN", "VBD", "DT", "NN", "CC", "PRP", "VBD", "RB", "JJ", "IN", "DT", "JJ", "NN", "NN", "."], "head": [2, 0, 4, 2, 2, 8, 8, 2, 12, 11, 12, 2, 14, 12, 19, 19, 19, 19, 2, 24, 24, 24, 24, 19, 2], "deprel": ["nsubj", "root", "amod", "obj", "punct", "nsubj", "cop", "parataxis", "cc", "nmod:poss", "nsubj", "conj", "det", "obj", "cc", "nsubj", "cop", "advmod", "conj", "case", "det", "amod", "compound", "obl", "punct"]}, {"id": "151", "sentence": "Reliable , Fresh Sushi", "triples": [{"uid": "151-0", "sentiment": "positive", "target_tags": "Reliable\\O ,\\O Fresh\\O Sushi\\B", "opinion_tags": "Reliable\\B ,\\O Fresh\\O Sushi\\O"}, {"uid": "151-1", "sentiment": "positive", "target_tags": "Reliable\\O ,\\O Fresh\\O Sushi\\B", "opinion_tags": "Reliable\\O ,\\O Fresh\\B Sushi\\O"}], "postag": ["JJ", ",", "JJ", "NN"], "head": [4, 4, 4, 0], "deprel": ["amod", "punct", "amod", "root"]}, {"id": "152", "sentence": "The sashimi is always fresh and the rolls are innovative and delicious .", "triples": [{"uid": "152-0", "sentiment": "positive", "target_tags": "The\\O sashimi\\B is\\O always\\O fresh\\O and\\O the\\O rolls\\O are\\O innovative\\O and\\O delicious\\O .\\O", "opinion_tags": "The\\O sashimi\\O is\\O always\\O fresh\\B and\\O the\\O rolls\\O are\\O innovative\\O and\\O delicious\\O .\\O"}, {"uid": "152-1", "sentiment": "positive", "target_tags": "The\\O sashimi\\O is\\O always\\O fresh\\O and\\O the\\O rolls\\B are\\O innovative\\O and\\O delicious\\O .\\O", "opinion_tags": "The\\O sashimi\\O is\\O always\\O fresh\\O and\\O the\\O rolls\\O are\\O innovative\\B and\\O delicious\\O .\\O"}, {"uid": "152-2", "sentiment": "positive", "target_tags": "The\\O sashimi\\O is\\O always\\O fresh\\O and\\O the\\O rolls\\B are\\O innovative\\O and\\O delicious\\O .\\O", "opinion_tags": "The\\O sashimi\\O is\\O always\\O fresh\\O and\\O the\\O rolls\\O are\\O innovative\\O and\\O delicious\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NNS", "VBP", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 10, 8, 10, 10, 5, 12, 10, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "det", "nsubj", "cop", "conj", "cc", "conj", "punct"]}, {"id": "153", "sentence": "Delivery guy sometimes get upset if you do n't tip more than 10 % .", "triples": [{"uid": "153-0", "sentiment": "negative", "target_tags": "Delivery\\B guy\\I sometimes\\O get\\O upset\\O if\\O you\\O do\\O n't\\O tip\\O more\\O than\\O 10\\O %\\O .\\O", "opinion_tags": "Delivery\\O guy\\O sometimes\\O get\\O upset\\B if\\O you\\O do\\O n't\\O tip\\O more\\O than\\O 10\\O %\\O .\\O"}], "postag": ["NN", "NN", "RB", "VBP", "JJ", "IN", "PRP", "VBP", "RB", "VB", "JJR", "IN", "CD", "NN", "."], "head": [2, 4, 4, 0, 4, 10, 10, 10, 10, 4, 13, 11, 14, 10, 4], "deprel": ["compound", "nsubj", "advmod", "root", "xcomp", "mark", "nsubj", "aux", "advmod", "advcl", "advmod", "fixed", "nummod", "obj", "punct"]}, {"id": "154", "sentence": "The place is a bit hidden away , but once you get there , it 's all worth it .", "triples": [{"uid": "154-0", "sentiment": "positive", "target_tags": "The\\O place\\B is\\O a\\O bit\\O hidden\\O away\\O ,\\O but\\O once\\O you\\O get\\O there\\O ,\\O it\\O 's\\O all\\O worth\\O it\\O .\\O", "opinion_tags": "The\\O place\\O is\\O a\\O bit\\O hidden\\B away\\I ,\\O but\\O once\\O you\\O get\\O there\\O ,\\O it\\O 's\\O all\\O worth\\O it\\O .\\O"}, {"uid": "154-1", "sentiment": "positive", "target_tags": "The\\O place\\B is\\O a\\O bit\\O hidden\\O away\\O ,\\O but\\O once\\O you\\O get\\O there\\O ,\\O it\\O 's\\O all\\O worth\\O it\\O .\\O", "opinion_tags": "The\\O place\\O is\\O a\\O bit\\O hidden\\O away\\O ,\\O but\\O once\\O you\\O get\\O there\\O ,\\O it\\O 's\\O all\\O worth\\B it\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "NN", "VBN", "RB", ",", "CC", "IN", "PRP", "VBP", "RB", ",", "PRP", "VBZ", "RB", "JJ", "PRP", "."], "head": [2, 6, 6, 5, 6, 0, 6, 18, 18, 12, 12, 18, 12, 18, 18, 18, 18, 6, 18, 6], "deprel": ["det", "nsubj", "cop", "det", "obl:npmod", "root", "advmod", "punct", "cc", "mark", "nsubj", "advcl", "advmod", "punct", "nsubj", "cop", "advmod", "conj", "obj", "punct"]}, {"id": "155", "sentence": "Good food : my favorite is the seafood spaghetti .", "triples": [{"uid": "155-0", "sentiment": "positive", "target_tags": "Good\\O food\\B :\\O my\\O favorite\\O is\\O the\\O seafood\\O spaghetti\\O .\\O", "opinion_tags": "Good\\B food\\O :\\O my\\O favorite\\O is\\O the\\O seafood\\O spaghetti\\O .\\O"}, {"uid": "155-1", "sentiment": "positive", "target_tags": "Good\\O food\\O :\\O my\\O favorite\\O is\\O the\\O seafood\\B spaghetti\\I .\\O", "opinion_tags": "Good\\O food\\O :\\O my\\O favorite\\B is\\O the\\O seafood\\O spaghetti\\O .\\O"}], "postag": ["JJ", "NN", ":", "PRP$", "NN", "VBZ", "DT", "NN", "NN", "."], "head": [2, 0, 2, 5, 9, 9, 9, 9, 2, 2], "deprel": ["amod", "root", "punct", "nmod:poss", "nsubj", "cop", "det", "compound", "parataxis", "punct"]}, {"id": "156", "sentence": "The wait staff is very courteous and accomodating .", "triples": [{"uid": "156-0", "sentiment": "positive", "target_tags": "The\\O wait\\B staff\\I is\\O very\\O courteous\\O and\\O accomodating\\O .\\O", "opinion_tags": "The\\O wait\\O staff\\O is\\O very\\O courteous\\B and\\O accomodating\\O .\\O"}, {"uid": "156-1", "sentiment": "positive", "target_tags": "The\\O wait\\B staff\\I is\\O very\\O courteous\\O and\\O accomodating\\O .\\O", "opinion_tags": "The\\O wait\\O staff\\O is\\O very\\O courteous\\O and\\O accomodating\\B .\\O"}], "postag": ["DT", "NN", "NN", "VBZ", "RB", "JJ", "CC", "JJ", "."], "head": [3, 3, 6, 6, 6, 0, 8, 6, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"]}, {"id": "157", "sentence": "I thought the restaurant was nice and clean .", "triples": [{"uid": "157-0", "sentiment": "positive", "target_tags": "I\\O thought\\O the\\O restaurant\\B was\\O nice\\O and\\O clean\\O .\\O", "opinion_tags": "I\\O thought\\O the\\O restaurant\\O was\\O nice\\B and\\O clean\\O .\\O"}, {"uid": "157-1", "sentiment": "positive", "target_tags": "I\\O thought\\O the\\O restaurant\\B was\\O nice\\O and\\O clean\\O .\\O", "opinion_tags": "I\\O thought\\O the\\O restaurant\\O was\\O nice\\O and\\O clean\\B .\\O"}], "postag": ["PRP", "VBD", "DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 0, 4, 6, 6, 2, 8, 6, 2], "deprel": ["nsubj", "root", "det", "nsubj", "cop", "ccomp", "cc", "conj", "punct"]}, {"id": "158", "sentence": "WORST PLACE ON <PERSON><PERSON><PERSON> STREET IN BROOKLYN", "triples": [{"uid": "158-0", "sentiment": "negative", "target_tags": "WORST\\O PLACE\\B ON\\O SMITH\\O STREET\\O IN\\O BROOKLYN\\O", "opinion_tags": "WORST\\B PLACE\\O ON\\O SMITH\\O STREET\\O IN\\O BROOKLYN\\O"}], "postag": ["JJS", "NN", "IN", "NNP", "NNP", "IN", "NNP"], "head": [2, 0, 5, 5, 2, 7, 5], "deprel": ["amod", "root", "case", "compound", "nmod", "case", "nmod"]}, {"id": "159", "sentence": "The waitress was not attentive at all .", "triples": [{"uid": "159-0", "sentiment": "negative", "target_tags": "The\\O waitress\\B was\\O not\\O attentive\\O at\\O all\\O .\\O", "opinion_tags": "The\\O waitress\\O was\\O not\\B attentive\\I at\\O all\\O .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", "IN", "DT", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "obl", "punct"]}, {"id": "160", "sentence": "The Seafood Dynamite is also otherworldly .", "triples": [{"uid": "160-0", "sentiment": "positive", "target_tags": "The\\O Seafood\\B Dynamite\\I is\\O also\\O otherworldly\\O .\\O", "opinion_tags": "The\\O Seafood\\O Dynamite\\O is\\O also\\O otherworldly\\B .\\O"}], "postag": ["DT", "NNP", "NNP", "VBZ", "RB", "JJ", "."], "head": [3, 3, 6, 6, 6, 0, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "punct"]}, {"id": "161", "sentence": "In the summer months , the back garden area is really nice .", "triples": [{"uid": "161-0", "sentiment": "positive", "target_tags": "In\\O the\\O summer\\O months\\O ,\\O the\\O back\\B garden\\I area\\I is\\O really\\O nice\\O .\\O", "opinion_tags": "In\\O the\\O summer\\O months\\O ,\\O the\\O back\\O garden\\O area\\O is\\O really\\O nice\\B .\\O"}], "postag": ["IN", "DT", "NN", "NNS", ",", "DT", "JJ", "NN", "NN", "VBZ", "RB", "JJ", "."], "head": [4, 4, 4, 12, 12, 9, 9, 9, 12, 12, 12, 0, 12], "deprel": ["case", "det", "compound", "obl", "punct", "det", "amod", "compound", "nsubj", "cop", "advmod", "root", "punct"]}, {"id": "162", "sentence": "The rolls are creative and I have yet to find another sushi place that serves up more inventive yet delicious japanese food .", "triples": [{"uid": "162-0", "sentiment": "positive", "target_tags": "The\\O rolls\\B are\\O creative\\O and\\O I\\O have\\O yet\\O to\\O find\\O another\\O sushi\\O place\\O that\\O serves\\O up\\O more\\O inventive\\O yet\\O delicious\\O japanese\\O food\\O .\\O", "opinion_tags": "The\\O rolls\\O are\\O creative\\B and\\O I\\O have\\O yet\\O to\\O find\\O another\\O sushi\\O place\\O that\\O serves\\O up\\O more\\O inventive\\O yet\\O delicious\\O japanese\\O food\\O .\\O"}, {"uid": "162-1", "sentiment": "positive", "target_tags": "The\\O rolls\\O are\\O creative\\O and\\O I\\O have\\O yet\\O to\\O find\\O another\\O sushi\\O place\\O that\\O serves\\O up\\O more\\O inventive\\O yet\\O delicious\\O japanese\\B food\\I .\\O", "opinion_tags": "The\\O rolls\\O are\\O creative\\O and\\O I\\O have\\O yet\\O to\\O find\\O another\\O sushi\\O place\\O that\\O serves\\O up\\O more\\O inventive\\B yet\\O delicious\\O japanese\\O food\\O .\\O"}, {"uid": "162-2", "sentiment": "positive", "target_tags": "The\\O rolls\\O are\\O creative\\O and\\O I\\O have\\O yet\\O to\\O find\\O another\\O sushi\\O place\\O that\\O serves\\O up\\O more\\O inventive\\O yet\\O delicious\\O japanese\\B food\\I .\\O", "opinion_tags": "The\\O rolls\\O are\\O creative\\O and\\O I\\O have\\O yet\\O to\\O find\\O another\\O sushi\\O place\\O that\\O serves\\O up\\O more\\O inventive\\O yet\\O delicious\\B japanese\\O food\\O .\\O"}], "postag": ["DT", "NNS", "VBP", "JJ", "CC", "PRP", "VBP", "RB", "TO", "VB", "DT", "NN", "NN", "WDT", "VBZ", "RP", "RBR", "JJ", "CC", "JJ", "JJ", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 7, 10, 7, 13, 13, 10, 15, 13, 15, 18, 22, 20, 18, 22, 15, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "nsubj", "conj", "advmod", "mark", "xcomp", "det", "compound", "obj", "nsubj", "acl:relcl", "compound:prt", "advmod", "amod", "cc", "conj", "amod", "obj", "punct"]}, {"id": "163", "sentence": "I CAN EAT HERE EVERY DAY OF THE WEEK REALLY LOL LOVE THIS PLACE ... )", "triples": [{"uid": "163-0", "sentiment": "positive", "target_tags": "I\\O CAN\\O EAT\\O HERE\\O EVERY\\O DAY\\O OF\\O THE\\O WEEK\\O REALLY\\O LOL\\O LOVE\\O THIS\\O PLACE\\B ...\\O )\\O", "opinion_tags": "I\\O CAN\\O EAT\\O HERE\\O EVERY\\O DAY\\O OF\\O THE\\O WEEK\\O REALLY\\O LOL\\O LOVE\\B THIS\\O PLACE\\O ...\\O )\\O"}], "postag": ["PRP", "MD", "VB", "RB", "DT", "NN", "IN", "DT", "NN", "RB", "UH", "VB", "DT", "NN", ".", "-RRB-"], "head": [3, 3, 0, 3, 6, 3, 9, 9, 6, 12, 12, 3, 14, 12, 3, 3], "deprel": ["nsubj", "aux", "root", "advmod", "det", "obl:tmod", "case", "det", "nmod", "advmod", "discourse", "parataxis", "det", "obj", "punct", "punct"]}, {"id": "164", "sentence": "Great Indian Food !", "triples": [{"uid": "164-0", "sentiment": "positive", "target_tags": "Great\\O Indian\\B Food\\I !\\O", "opinion_tags": "Great\\B Indian\\O Food\\O !\\O"}], "postag": ["JJ", "JJ", "NN", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "amod", "root", "punct"]}, {"id": "165", "sentence": "This is one of my favorite spot , very relaxing the food is great all the times , celebrated my engagement and my wedding here , it was very well organized .", "triples": [{"uid": "165-0", "sentiment": "positive", "target_tags": "This\\O is\\O one\\O of\\O my\\O favorite\\O spot\\O ,\\O very\\O relaxing\\O the\\O food\\B is\\O great\\O all\\O the\\O times\\O ,\\O celebrated\\O my\\O engagement\\O and\\O my\\O wedding\\O here\\O ,\\O it\\O was\\O very\\O well\\O organized\\O .\\O", "opinion_tags": "This\\O is\\O one\\O of\\O my\\O favorite\\O spot\\O ,\\O very\\O relaxing\\O the\\O food\\O is\\O great\\B all\\O the\\O times\\O ,\\O celebrated\\O my\\O engagement\\O and\\O my\\O wedding\\O here\\O ,\\O it\\O was\\O very\\O well\\O organized\\O .\\O"}], "postag": ["DT", "VBZ", "CD", "IN", "PRP$", "JJ", "NN", ",", "RB", "VBG", "DT", "NN", "VBZ", "JJ", "PDT", "DT", "NNS", ",", "VBD", "PRP$", "NN", "CC", "PRP$", "NN", "RB", ",", "PRP", "VBD", "RB", "RB", "VBN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 3, 10, 14, 12, 14, 14, 3, 17, 17, 14, 19, 3, 21, 19, 24, 24, 21, 19, 3, 31, 31, 30, 31, 3, 3], "deprel": ["nsubj", "cop", "root", "case", "nmod:poss", "amod", "nmod", "punct", "advmod", "csubj", "det", "nsubj", "cop", "parataxis", "det:predet", "det", "obl:tmod", "punct", "parataxis", "nmod:poss", "obj", "cc", "nmod:poss", "conj", "advmod", "punct", "nsubj:pass", "aux:pass", "advmod", "advmod", "parataxis", "punct"]}, {"id": "166", "sentence": "Love their drink menu .", "triples": [{"uid": "166-0", "sentiment": "positive", "target_tags": "Love\\O their\\O drink\\B menu\\I .\\O", "opinion_tags": "Love\\B their\\O drink\\O menu\\O .\\O"}], "postag": ["VBP", "PRP$", "NN", "NN", "."], "head": [0, 4, 4, 1, 1], "deprel": ["root", "nmod:poss", "compound", "obj", "punct"]}, {"id": "167", "sentence": "A beautifully designed dreamy Egyptian restaurant that gets sceney at night .", "triples": [{"uid": "167-0", "sentiment": "positive", "target_tags": "A\\O beautifully\\O designed\\O dreamy\\O Egyptian\\B restaurant\\I that\\O gets\\O sceney\\O at\\O night\\O .\\O", "opinion_tags": "A\\O beautifully\\O designed\\O dreamy\\B Egyptian\\O restaurant\\O that\\O gets\\O sceney\\O at\\O night\\O .\\O"}], "postag": ["DT", "RB", "VBN", "JJ", "JJ", "NN", "WDT", "VBZ", "JJ", "IN", "NN", "."], "head": [6, 3, 6, 6, 6, 0, 8, 6, 8, 11, 9, 6], "deprel": ["det", "advmod", "amod", "amod", "amod", "root", "nsubj", "acl:relcl", "xcomp", "case", "obl", "punct"]}, {"id": "168", "sentence": "Watch the talented belly dancers as you enjoy delicious baba ganoush that 's more lemony than smoky .", "triples": [{"uid": "168-0", "sentiment": "positive", "target_tags": "Watch\\O the\\O talented\\O belly\\O dancers\\O as\\O you\\O enjoy\\O delicious\\O baba\\B ganoush\\I that\\O 's\\O more\\O lemony\\O than\\O smoky\\O .\\O", "opinion_tags": "Watch\\O the\\O talented\\O belly\\O dancers\\O as\\O you\\O enjoy\\B delicious\\I baba\\O ganoush\\O that\\O 's\\O more\\O lemony\\O than\\O smoky\\O .\\O"}, {"uid": "168-1", "sentiment": "positive", "target_tags": "Watch\\O the\\O talented\\O belly\\B dancers\\I as\\O you\\O enjoy\\O delicious\\O baba\\O ganoush\\O that\\O 's\\O more\\O lemony\\O than\\O smoky\\O .\\O", "opinion_tags": "Watch\\O the\\O talented\\B belly\\O dancers\\O as\\O you\\O enjoy\\O delicious\\O baba\\O ganoush\\O that\\O 's\\O more\\O lemony\\O than\\O smoky\\O .\\O"}], "postag": ["VB", "DT", "JJ", "NN", "NNS", "IN", "PRP", "VBP", "JJ", "NN", "NN", "WDT", "VBZ", "RBR", "JJ", "IN", "JJ", "."], "head": [0, 5, 5, 5, 1, 8, 8, 1, 11, 11, 8, 15, 15, 15, 11, 17, 15, 1], "deprel": ["root", "det", "amod", "compound", "obj", "mark", "nsubj", "advcl", "amod", "compound", "obj", "nsubj", "cop", "advmod", "acl:relcl", "case", "obl", "punct"]}, {"id": "169", "sentence": "The drinks are great , especially when made by <PERSON> .", "triples": [{"uid": "169-0", "sentiment": "positive", "target_tags": "The\\O drinks\\B are\\O great\\O ,\\O especially\\O when\\O made\\O by\\O Raymond\\O .\\O", "opinion_tags": "The\\O drinks\\O are\\O great\\B ,\\O especially\\O when\\O made\\O by\\O Raymond\\O .\\O"}, {"uid": "169-1", "sentiment": "positive", "target_tags": "The\\O drinks\\O are\\O great\\O ,\\O especially\\O when\\O made\\O by\\O Raymond\\B .\\O", "opinion_tags": "The\\O drinks\\O are\\O great\\B ,\\O especially\\O when\\O made\\O by\\O Raymond\\O .\\O"}], "postag": ["DT", "NNS", "VBP", "JJ", ",", "RB", "WRB", "VBN", "IN", "NNP", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 10, 8, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "advmod", "mark", "advcl", "case", "obl", "punct"]}, {"id": "170", "sentence": "Decor needs to be upgraded but the food is amazing !", "triples": [{"uid": "170-0", "sentiment": "negative", "target_tags": "Decor\\B needs\\O to\\O be\\O upgraded\\O but\\O the\\O food\\O is\\O amazing\\O !\\O", "opinion_tags": "Decor\\O needs\\O to\\O be\\O upgraded\\B but\\O the\\O food\\O is\\O amazing\\O !\\O"}, {"uid": "170-1", "sentiment": "positive", "target_tags": "Decor\\O needs\\O to\\O be\\O upgraded\\O but\\O the\\O food\\B is\\O amazing\\O !\\O", "opinion_tags": "Decor\\O needs\\O to\\O be\\O upgraded\\O but\\O the\\O food\\O is\\O amazing\\B !\\O"}], "postag": ["NN", "VBZ", "TO", "VB", "VBN", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 0, 5, 5, 2, 10, 8, 10, 10, 2, 2], "deprel": ["nsubj", "root", "mark", "aux:pass", "xcomp", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "171", "sentence": "Great food , amazing service , this place is a class act .", "triples": [{"uid": "171-0", "sentiment": "positive", "target_tags": "Great\\O food\\B ,\\O amazing\\O service\\O ,\\O this\\O place\\O is\\O a\\O class\\O act\\O .\\O", "opinion_tags": "Great\\B food\\O ,\\O amazing\\O service\\O ,\\O this\\O place\\O is\\O a\\O class\\O act\\O .\\O"}, {"uid": "171-1", "sentiment": "positive", "target_tags": "Great\\O food\\O ,\\O amazing\\O service\\B ,\\O this\\O place\\O is\\O a\\O class\\O act\\O .\\O", "opinion_tags": "Great\\O food\\O ,\\O amazing\\B service\\O ,\\O this\\O place\\O is\\O a\\O class\\O act\\O .\\O"}, {"uid": "171-2", "sentiment": "positive", "target_tags": "Great\\O food\\O ,\\O amazing\\O service\\O ,\\O this\\O place\\B is\\O a\\O class\\O act\\O .\\O", "opinion_tags": "Great\\O food\\O ,\\O amazing\\O service\\O ,\\O this\\O place\\O is\\O a\\O class\\B act\\I .\\O"}], "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "DT", "NN", "VBZ", "DT", "NN", "NN", "."], "head": [2, 0, 5, 5, 2, 2, 8, 12, 12, 12, 12, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "det", "nsubj", "cop", "det", "compound", "parataxis", "punct"]}, {"id": "172", "sentence": "<PERSON> , the maitre d ' , was totally professional and always on top of things .", "triples": [{"uid": "172-0", "sentiment": "positive", "target_tags": "Paul\\B ,\\O the\\O maitre\\O d\\O '\\O ,\\O was\\O totally\\O professional\\O and\\O always\\O on\\O top\\O of\\O things\\O .\\O", "opinion_tags": "Paul\\O ,\\O the\\O maitre\\O d\\O '\\O ,\\O was\\O totally\\O professional\\B and\\O always\\O on\\O top\\O of\\O things\\O .\\O"}], "postag": ["NNP", ",", "DT", "NN", "NNP", "''", ",", "VBD", "RB", "JJ", "CC", "RB", "IN", "NN", "IN", "NNS", "."], "head": [10, 5, 5, 5, 1, 5, 10, 10, 10, 0, 14, 14, 14, 10, 16, 14, 10], "deprel": ["nsubj", "punct", "det", "compound", "appos", "punct", "punct", "cop", "advmod", "root", "cc", "advmod", "case", "obl", "case", "nmod", "punct"]}, {"id": "173", "sentence": "The bar drinks were Eh , ok to say the least .", "triples": [{"uid": "173-0", "sentiment": "negative", "target_tags": "The\\O bar\\B drinks\\I were\\O Eh\\O ,\\O ok\\O to\\O say\\O the\\O least\\O .\\O", "opinion_tags": "The\\O bar\\O drinks\\O were\\O Eh\\O ,\\O ok\\B to\\O say\\O the\\O least\\O .\\O"}], "postag": ["DT", "NN", "NNS", "VBD", "UH", ",", "JJ", "TO", "VB", "DT", "JJS", "."], "head": [3, 3, 5, 7, 7, 7, 0, 9, 7, 11, 9, 7], "deprel": ["det", "compound", "nsubj", "cop", "discourse", "punct", "root", "mark", "advcl", "det", "obj", "punct"]}, {"id": "174", "sentence": "The bread we received was horrible - rock hard and cold - and the `` free '' appetizer of olives was disappointing .", "triples": [{"uid": "174-0", "sentiment": "negative", "target_tags": "The\\O bread\\B we\\O received\\O was\\O horrible\\O -\\O rock\\O hard\\O and\\O cold\\O -\\O and\\O the\\O ``\\O free\\O ''\\O appetizer\\O of\\O olives\\O was\\O disappointing\\O .\\O", "opinion_tags": "The\\O bread\\O we\\O received\\O was\\O horrible\\B -\\O rock\\O hard\\O and\\O cold\\O -\\O and\\O the\\O ``\\O free\\O ''\\O appetizer\\O of\\O olives\\O was\\O disappointing\\O .\\O"}, {"uid": "174-1", "sentiment": "negative", "target_tags": "The\\O bread\\B we\\O received\\O was\\O horrible\\O -\\O rock\\O hard\\O and\\O cold\\O -\\O and\\O the\\O ``\\O free\\O ''\\O appetizer\\O of\\O olives\\O was\\O disappointing\\O .\\O", "opinion_tags": "The\\O bread\\O we\\O received\\O was\\O horrible\\O -\\O rock\\B hard\\I and\\O cold\\O -\\O and\\O the\\O ``\\O free\\O ''\\O appetizer\\O of\\O olives\\O was\\O disappointing\\O .\\O"}, {"uid": "174-2", "sentiment": "negative", "target_tags": "The\\O bread\\B we\\O received\\O was\\O horrible\\O -\\O rock\\O hard\\O and\\O cold\\O -\\O and\\O the\\O ``\\O free\\O ''\\O appetizer\\O of\\O olives\\O was\\O disappointing\\O .\\O", "opinion_tags": "The\\O bread\\O we\\O received\\O was\\O horrible\\O -\\O rock\\O hard\\O and\\O cold\\B -\\O and\\O the\\O ``\\O free\\O ''\\O appetizer\\O of\\O olives\\O was\\O disappointing\\O .\\O"}, {"uid": "174-3", "sentiment": "negative", "target_tags": "The\\O bread\\O we\\O received\\O was\\O horrible\\O -\\O rock\\O hard\\O and\\O cold\\O -\\O and\\O the\\O ``\\O free\\O ''\\O appetizer\\B of\\I olives\\I was\\O disappointing\\O .\\O", "opinion_tags": "The\\O bread\\O we\\O received\\O was\\O horrible\\O -\\O rock\\O hard\\O and\\O cold\\O -\\O and\\O the\\O ``\\O free\\O ''\\O appetizer\\O of\\O olives\\O was\\O disappointing\\B .\\O"}], "postag": ["DT", "NN", "PRP", "VBD", "VBD", "JJ", ",", "NN", "JJ", "CC", "JJ", ",", "CC", "DT", "``", "JJ", "''", "NN", "IN", "NNS", "VBD", "JJ", "."], "head": [2, 6, 4, 2, 6, 0, 6, 9, 6, 11, 9, 6, 22, 18, 18, 18, 18, 22, 20, 18, 22, 6, 6], "deprel": ["det", "nsubj", "nsubj", "acl:relcl", "cop", "root", "punct", "nsubj", "parataxis", "cc", "conj", "punct", "cc", "det", "punct", "amod", "punct", "nsubj", "case", "nmod", "cop", "conj", "punct"]}, {"id": "175", "sentence": "short and sweet – seating is great : it 's romantic , cozy and private .", "triples": [{"uid": "175-0", "sentiment": "positive", "target_tags": "short\\O and\\O sweet\\O –\\O seating\\B is\\O great\\O :\\O it\\O 's\\O romantic\\O ,\\O cozy\\O and\\O private\\O .\\O", "opinion_tags": "short\\O and\\O sweet\\O –\\O seating\\O is\\O great\\B :\\O it\\O 's\\O romantic\\O ,\\O cozy\\O and\\O private\\O .\\O"}], "postag": ["JJ", "CC", "JJ", ",", "NN", "VBZ", "JJ", ":", "PRP", "VBZ", "JJ", ",", "JJ", "CC", "JJ", "."], "head": [0, 3, 1, 1, 7, 7, 1, 7, 11, 11, 7, 13, 11, 15, 11, 1], "deprel": ["root", "cc", "conj", "punct", "nsubj", "cop", "parataxis", "punct", "nsubj", "cop", "parataxis", "punct", "conj", "cc", "conj", "punct"]}, {"id": "176", "sentence": "The service was extremely fast and attentive ( thanks to the service button on your table ) but I barely understood 1 word when the waiter took our order .", "triples": [{"uid": "176-0", "sentiment": "positive", "target_tags": "The\\O service\\B was\\O extremely\\O fast\\O and\\O attentive\\O (\\O thanks\\O to\\O the\\O service\\O button\\O on\\O your\\O table\\O )\\O but\\O I\\O barely\\O understood\\O 1\\O word\\O when\\O the\\O waiter\\O took\\O our\\O order\\O .\\O", "opinion_tags": "The\\O service\\O was\\O extremely\\O fast\\B and\\O attentive\\O (\\O thanks\\O to\\O the\\O service\\O button\\O on\\O your\\O table\\O )\\O but\\O I\\O barely\\O understood\\O 1\\O word\\O when\\O the\\O waiter\\O took\\O our\\O order\\O .\\O"}, {"uid": "176-1", "sentiment": "positive", "target_tags": "The\\O service\\B was\\O extremely\\O fast\\O and\\O attentive\\O (\\O thanks\\O to\\O the\\O service\\O button\\O on\\O your\\O table\\O )\\O but\\O I\\O barely\\O understood\\O 1\\O word\\O when\\O the\\O waiter\\O took\\O our\\O order\\O .\\O", "opinion_tags": "The\\O service\\O was\\O extremely\\O fast\\O and\\O attentive\\B (\\O thanks\\O to\\O the\\O service\\O button\\O on\\O your\\O table\\O )\\O but\\O I\\O barely\\O understood\\O 1\\O word\\O when\\O the\\O waiter\\O took\\O our\\O order\\O .\\O"}, {"uid": "176-2", "sentiment": "positive", "target_tags": "The\\O service\\O was\\O extremely\\O fast\\O and\\O attentive\\O (\\O thanks\\O to\\O the\\O service\\B button\\I on\\O your\\O table\\O )\\O but\\O I\\O barely\\O understood\\O 1\\O word\\O when\\O the\\O waiter\\O took\\O our\\O order\\O .\\O", "opinion_tags": "The\\O service\\O was\\O extremely\\O fast\\O and\\O attentive\\O (\\O thanks\\B to\\I the\\O service\\O button\\O on\\O your\\O table\\O )\\O but\\O I\\O barely\\O understood\\O 1\\O word\\O when\\O the\\O waiter\\O took\\O our\\O order\\O .\\O"}, {"uid": "176-3", "sentiment": "negative", "target_tags": "The\\O service\\O was\\O extremely\\O fast\\O and\\O attentive\\O (\\O thanks\\O to\\O the\\O service\\O button\\O on\\O your\\O table\\O )\\O but\\O I\\O barely\\O understood\\O 1\\O word\\O when\\O the\\O waiter\\B took\\O our\\O order\\O .\\O", "opinion_tags": "The\\O service\\O was\\O extremely\\O fast\\O and\\O attentive\\O (\\O thanks\\O to\\O the\\O service\\O button\\O on\\O your\\O table\\O )\\O but\\O I\\O barely\\B understood\\I 1\\O word\\O when\\O the\\O waiter\\O took\\O our\\O order\\O .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "JJ", "-LRB-", "NN", "IN", "DT", "NN", "NN", "IN", "PRP$", "NN", "-RRB-", "CC", "PRP", "RB", "VBD", "CD", "NN", "WRB", "DT", "NN", "VBD", "PRP$", "NN", "."], "head": [2, 5, 5, 5, 0, 7, 5, 9, 5, 13, 13, 13, 9, 16, 16, 13, 9, 21, 21, 21, 5, 23, 21, 27, 26, 27, 21, 29, 27, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct", "parataxis", "case", "det", "compound", "nmod", "case", "nmod:poss", "nmod", "punct", "cc", "nsubj", "advmod", "conj", "nummod", "obj", "mark", "det", "nsubj", "advcl", "nmod:poss", "obj", "punct"]}, {"id": "177", "sentence": "Highly impressed from the decor to the food to the hospitality to the great night I had !", "triples": [{"uid": "177-0", "sentiment": "positive", "target_tags": "Highly\\O impressed\\O from\\O the\\O decor\\B to\\O the\\O food\\O to\\O the\\O hospitality\\O to\\O the\\O great\\O night\\O I\\O had\\O !\\O", "opinion_tags": "Highly\\O impressed\\B from\\O the\\O decor\\O to\\O the\\O food\\O to\\O the\\O hospitality\\O to\\O the\\O great\\O night\\O I\\O had\\O !\\O"}, {"uid": "177-1", "sentiment": "positive", "target_tags": "Highly\\O impressed\\O from\\O the\\O decor\\O to\\O the\\O food\\B to\\O the\\O hospitality\\O to\\O the\\O great\\O night\\O I\\O had\\O !\\O", "opinion_tags": "Highly\\O impressed\\B from\\O the\\O decor\\O to\\O the\\O food\\O to\\O the\\O hospitality\\O to\\O the\\O great\\O night\\O I\\O had\\O !\\O"}], "postag": ["RB", "JJ", "IN", "DT", "NN", "IN", "DT", "NN", "IN", "DT", "NN", "IN", "DT", "JJ", "NN", "PRP", "VBD", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 11, 11, 2, 15, 15, 15, 2, 17, 15, 2], "deprel": ["advmod", "root", "case", "det", "obl", "case", "det", "obl", "case", "det", "obl", "case", "det", "amod", "obl", "nsubj", "acl:relcl", "punct"]}, {"id": "178", "sentence": "Food took some time to prepare , all worth waiting for .", "triples": [{"uid": "178-0", "sentiment": "positive", "target_tags": "Food\\B took\\O some\\O time\\O to\\O prepare\\O ,\\O all\\O worth\\O waiting\\O for\\O .\\O", "opinion_tags": "Food\\O took\\O some\\O time\\O to\\O prepare\\O ,\\O all\\O worth\\B waiting\\O for\\O .\\O"}], "postag": ["NN", "VBD", "DT", "NN", "TO", "VB", ",", "RB", "JJ", "VBG", "IN", "."], "head": [2, 0, 4, 2, 6, 4, 9, 9, 2, 9, 10, 2], "deprel": ["nsubj", "root", "det", "obj", "mark", "acl", "punct", "advmod", "parataxis", "xcomp", "obl", "punct"]}, {"id": "179", "sentence": "The menu looked great , and the waiter was very nice , but when the food came , it was average .", "triples": [{"uid": "179-0", "sentiment": "positive", "target_tags": "The\\O menu\\B looked\\O great\\O ,\\O and\\O the\\O waiter\\O was\\O very\\O nice\\O ,\\O but\\O when\\O the\\O food\\O came\\O ,\\O it\\O was\\O average\\O .\\O", "opinion_tags": "The\\O menu\\O looked\\O great\\B ,\\O and\\O the\\O waiter\\O was\\O very\\O nice\\O ,\\O but\\O when\\O the\\O food\\O came\\O ,\\O it\\O was\\O average\\O .\\O"}, {"uid": "179-1", "sentiment": "positive", "target_tags": "The\\O menu\\O looked\\O great\\O ,\\O and\\O the\\O waiter\\B was\\O very\\O nice\\O ,\\O but\\O when\\O the\\O food\\O came\\O ,\\O it\\O was\\O average\\O .\\O", "opinion_tags": "The\\O menu\\O looked\\O great\\O ,\\O and\\O the\\O waiter\\O was\\O very\\O nice\\B ,\\O but\\O when\\O the\\O food\\O came\\O ,\\O it\\O was\\O average\\O .\\O"}, {"uid": "179-2", "sentiment": "neutral", "target_tags": "The\\O menu\\O looked\\O great\\O ,\\O and\\O the\\O waiter\\O was\\O very\\O nice\\O ,\\O but\\O when\\O the\\O food\\B came\\O ,\\O it\\O was\\O average\\O .\\O", "opinion_tags": "The\\O menu\\O looked\\O great\\O ,\\O and\\O the\\O waiter\\O was\\O very\\O nice\\O ,\\O but\\O when\\O the\\O food\\O came\\O ,\\O it\\O was\\O average\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", ",", "CC", "DT", "NN", "VBD", "RB", "JJ", ",", "CC", "WRB", "DT", "NN", "VBD", ",", "PRP", "VBD", "JJ", "."], "head": [2, 3, 0, 3, 11, 11, 8, 11, 11, 11, 3, 21, 21, 17, 16, 17, 21, 21, 21, 21, 3, 3], "deprel": ["det", "nsubj", "root", "xcomp", "punct", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct", "cc", "mark", "det", "nsubj", "advcl", "punct", "nsubj", "cop", "conj", "punct"]}, {"id": "180", "sentence": "It 's a great place to order from or sit-in .", "triples": [{"uid": "180-0", "sentiment": "positive", "target_tags": "It\\O 's\\O a\\O great\\O place\\B to\\O order\\O from\\O or\\O sit-in\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O great\\B place\\O to\\O order\\O from\\O or\\O sit-in\\O .\\O"}], "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "IN", "CC", "JJ", "."], "head": [5, 5, 5, 5, 0, 7, 5, 7, 10, 8, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "obl", "cc", "conj", "punct"]}, {"id": "181", "sentence": "Ya<PERSON>o is an excellent place to go if youre not into sashimi , or if you have friends who doesnt like sushi much .", "triples": [{"uid": "181-0", "sentiment": "positive", "target_tags": "Yamato\\B is\\O an\\O excellent\\O place\\O to\\O go\\O if\\O youre\\O not\\O into\\O sashimi\\O ,\\O or\\O if\\O you\\O have\\O friends\\O who\\O doesnt\\O like\\O sushi\\O much\\O .\\O", "opinion_tags": "Yamato\\O is\\O an\\O excellent\\B place\\O to\\O go\\O if\\O youre\\O not\\O into\\O sashimi\\O ,\\O or\\O if\\O you\\O have\\O friends\\O who\\O doesnt\\O like\\O sushi\\O much\\O .\\O"}], "postag": ["NNP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "IN", "PRP", "RB", "IN", "NN", ",", "CC", "IN", "PRP", "VBP", "NNS", "WP", "MD", "VB", "NN", "JJ", "."], "head": [5, 5, 5, 5, 0, 7, 5, 12, 12, 12, 12, 7, 17, 17, 17, 17, 12, 17, 21, 21, 18, 23, 21, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "mark", "nsubj", "advmod", "case", "advcl", "punct", "cc", "mark", "nsubj", "conj", "obj", "nsubj", "aux", "acl:relcl", "compound", "obj", "punct"]}, {"id": "182", "sentence": "I 've had my fair share of modern Japanese and this spot delivers .", "triples": [{"uid": "182-0", "sentiment": "positive", "target_tags": "I\\O 've\\O had\\O my\\O fair\\O share\\O of\\O modern\\B Japanese\\I and\\O this\\O spot\\O delivers\\O .\\O", "opinion_tags": "I\\O 've\\O had\\O my\\O fair\\O share\\O of\\O modern\\O Japanese\\O and\\O this\\O spot\\O delivers\\B .\\O"}], "postag": ["PRP", "VBP", "VBN", "PRP$", "JJ", "NN", "IN", "JJ", "NNP", "CC", "DT", "NN", "VBZ", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 6, 13, 12, 13, 3, 3], "deprel": ["nsubj", "aux", "root", "nmod:poss", "amod", "obj", "case", "amod", "nmod", "cc", "det", "nsubj", "conj", "punct"]}, {"id": "183", "sentence": "Everything on the menu is great .", "triples": [{"uid": "183-0", "sentiment": "positive", "target_tags": "Everything\\O on\\O the\\O menu\\B is\\O great\\O .\\O", "opinion_tags": "Everything\\O on\\O the\\O menu\\O is\\O great\\B .\\O"}], "postag": ["NN", "IN", "DT", "NN", "VBZ", "JJ", "."], "head": [6, 4, 4, 1, 6, 0, 6], "deprel": ["nsubj", "case", "det", "nmod", "cop", "root", "punct"]}, {"id": "184", "sentence": "<PERSON><PERSON> was quite excellent however .", "triples": [{"uid": "184-0", "sentiment": "positive", "target_tags": "Bison\\B was\\O quite\\O excellent\\O however\\O .\\O", "opinion_tags": "Bison\\O was\\O quite\\O excellent\\B however\\O .\\O"}], "postag": ["NNP", "VBD", "RB", "JJ", "RB", "."], "head": [4, 4, 4, 0, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "advmod", "punct"]}, {"id": "185", "sentence": "All in all , the food was great ( except for the dessserts ) .", "triples": [{"uid": "185-0", "sentiment": "positive", "target_tags": "All\\O in\\O all\\O ,\\O the\\O food\\B was\\O great\\O (\\O except\\O for\\O the\\O dessserts\\O )\\O .\\O", "opinion_tags": "All\\O in\\O all\\O ,\\O the\\O food\\O was\\O great\\B (\\O except\\O for\\O the\\O dessserts\\O )\\O .\\O"}], "postag": ["DT", "IN", "DT", ",", "DT", "NN", "VBD", "JJ", "-LRB-", "IN", "IN", "DT", "NNS", "-RRB-", "."], "head": [8, 3, 1, 8, 6, 8, 8, 0, 13, 13, 13, 13, 8, 13, 8], "deprel": ["advmod", "case", "nmod", "punct", "det", "nsubj", "cop", "root", "punct", "case", "case", "det", "obl", "punct", "punct"]}, {"id": "186", "sentence": "However , our $ 14 drinks were were horrible !", "triples": [{"uid": "186-0", "sentiment": "negative", "target_tags": "However\\O ,\\O our\\O $\\O 14\\O drinks\\B were\\O were\\O horrible\\O !\\O", "opinion_tags": "However\\O ,\\O our\\O $\\O 14\\O drinks\\O were\\O were\\O horrible\\B !\\O"}], "postag": ["RB", ",", "PRP$", "$", "CD", "NNS", "VBD", "VBD", "JJ", "."], "head": [9, 9, 6, 6, 4, 9, 9, 9, 0, 9], "deprel": ["advmod", "punct", "nmod:poss", "compound", "nummod", "nsubj", "cop", "cop", "root", "punct"]}, {"id": "187", "sentence": "The menu is fairly simple without much descriptions .", "triples": [{"uid": "187-0", "sentiment": "neutral", "target_tags": "The\\O menu\\B is\\O fairly\\O simple\\O without\\O much\\O descriptions\\O .\\O", "opinion_tags": "The\\O menu\\O is\\O fairly\\O simple\\B without\\O much\\O descriptions\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "IN", "JJ", "NNS", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "amod", "obl", "punct"]}, {"id": "188", "sentence": "Not much of a selection of bottled beer either , we went with <PERSON><PERSON><PERSON> .", "triples": [{"uid": "188-0", "sentiment": "negative", "target_tags": "Not\\O much\\O of\\O a\\O selection\\B of\\I bottled\\I beer\\I either\\O ,\\O we\\O went\\O with\\O Brahma\\O .\\O", "opinion_tags": "Not\\B much\\I of\\O a\\O selection\\O of\\O bottled\\O beer\\O either\\O ,\\O we\\O went\\O with\\O Brahma\\O .\\O"}], "postag": ["RB", "JJ", "IN", "DT", "NN", "IN", "VBN", "NN", "CC", ",", "PRP", "VBD", "IN", "NNP", "."], "head": [2, 12, 5, 5, 2, 8, 8, 5, 8, 2, 12, 0, 14, 12, 12], "deprel": ["advmod", "advmod", "case", "det", "obl", "case", "amod", "nmod", "cc:preconj", "punct", "nsubj", "root", "case", "obl", "punct"]}, {"id": "189", "sentence": "All in all the food was good - a little on the expensive side , but fresh .", "triples": [{"uid": "189-0", "sentiment": "negative", "target_tags": "All\\O in\\O all\\O the\\O food\\B was\\O good\\O -\\O a\\O little\\O on\\O the\\O expensive\\O side\\O ,\\O but\\O fresh\\O .\\O", "opinion_tags": "All\\O in\\O all\\O the\\O food\\O was\\O good\\B -\\O a\\O little\\O on\\O the\\O expensive\\O side\\O ,\\O but\\O fresh\\O .\\O"}, {"uid": "189-1", "sentiment": "negative", "target_tags": "All\\O in\\O all\\O the\\O food\\B was\\O good\\O -\\O a\\O little\\O on\\O the\\O expensive\\O side\\O ,\\O but\\O fresh\\O .\\O", "opinion_tags": "All\\O in\\O all\\O the\\O food\\O was\\O good\\O -\\O a\\O little\\O on\\O the\\O expensive\\B side\\O ,\\O but\\O fresh\\O .\\O"}, {"uid": "189-2", "sentiment": "negative", "target_tags": "All\\O in\\O all\\O the\\O food\\B was\\O good\\O -\\O a\\O little\\O on\\O the\\O expensive\\O side\\O ,\\O but\\O fresh\\O .\\O", "opinion_tags": "All\\O in\\O all\\O the\\O food\\O was\\O good\\O -\\O a\\O little\\O on\\O the\\O expensive\\O side\\O ,\\O but\\O fresh\\B .\\O"}], "postag": ["DT", "IN", "PDT", "DT", "NN", "VBD", "JJ", ",", "DT", "JJ", "IN", "DT", "JJ", "NN", ",", "CC", "JJ", "."], "head": [7, 5, 5, 5, 1, 7, 0, 7, 10, 7, 14, 14, 14, 7, 17, 17, 7, 7], "deprel": ["nsubj", "case", "det:predet", "det", "nmod", "cop", "root", "punct", "det", "obl:npmod", "case", "det", "amod", "obl", "punct", "cc", "conj", "punct"]}, {"id": "190", "sentence": "It was n't as if this restaurant had any major bragging points before hand , but now it 's simply repulsive .", "triples": [{"uid": "190-0", "sentiment": "negative", "target_tags": "It\\O was\\O n't\\O as\\O if\\O this\\O restaurant\\B had\\O any\\O major\\O bragging\\O points\\O before\\O hand\\O ,\\O but\\O now\\O it\\O 's\\O simply\\O repulsive\\O .\\O", "opinion_tags": "It\\O was\\O n't\\O as\\O if\\O this\\O restaurant\\O had\\O any\\O major\\O bragging\\O points\\O before\\O hand\\O ,\\O but\\O now\\O it\\O 's\\O simply\\O repulsive\\B .\\O"}], "postag": ["PRP", "VBD", "RB", "IN", "IN", "DT", "NN", "VBD", "DT", "JJ", "NN", "NNS", "IN", "NN", ",", "CC", "RB", "PRP", "VBZ", "RB", "JJ", "."], "head": [2, 0, 2, 8, 8, 7, 8, 2, 12, 12, 12, 8, 14, 12, 21, 21, 21, 21, 21, 21, 2, 2], "deprel": ["nsubj", "root", "advmod", "mark", "mark", "det", "nsubj", "advcl", "det", "amod", "compound", "obj", "case", "nmod", "punct", "cc", "advmod", "nsubj", "cop", "advmod", "conj", "punct"]}, {"id": "191", "sentence": "Gorgeous place ideal for a romantic dinner", "triples": [{"uid": "191-0", "sentiment": "positive", "target_tags": "Gorgeous\\O place\\B ideal\\O for\\O a\\O romantic\\O dinner\\O", "opinion_tags": "Gorgeous\\B place\\O ideal\\O for\\O a\\O romantic\\O dinner\\O"}, {"uid": "191-1", "sentiment": "positive", "target_tags": "Gorgeous\\O place\\B ideal\\O for\\O a\\O romantic\\O dinner\\O", "opinion_tags": "Gorgeous\\O place\\O ideal\\B for\\O a\\O romantic\\O dinner\\O"}], "postag": ["JJ", "NN", "JJ", "IN", "DT", "JJ", "NN"], "head": [2, 0, 2, 7, 7, 7, 3], "deprel": ["amod", "root", "amod", "case", "det", "amod", "obl"]}, {"id": "192", "sentence": "The nakgi-bokum was horrible .", "triples": [{"uid": "192-0", "sentiment": "negative", "target_tags": "The\\O nakgi-bokum\\B was\\O horrible\\O .\\O", "opinion_tags": "The\\O nakgi-bokum\\O was\\O horrible\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"]}, {"id": "193", "sentence": "The side dishes were passable , and I did get a refill upon request .", "triples": [{"uid": "193-0", "sentiment": "neutral", "target_tags": "The\\O side\\B dishes\\I were\\O passable\\O ,\\O and\\O I\\O did\\O get\\O a\\O refill\\O upon\\O request\\O .\\O", "opinion_tags": "The\\O side\\O dishes\\O were\\O passable\\B ,\\O and\\O I\\O did\\O get\\O a\\O refill\\O upon\\O request\\O .\\O"}], "postag": ["DT", "NN", "NNS", "VBD", "JJ", ",", "CC", "PRP", "VBD", "VB", "DT", "NN", "IN", "NN", "."], "head": [3, 3, 5, 5, 0, 10, 10, 10, 10, 5, 12, 10, 14, 10, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "cc", "nsubj", "aux", "conj", "det", "obj", "case", "obl", "punct"]}, {"id": "194", "sentence": "My wife had barely touched that mess of a dish .", "triples": [{"uid": "194-0", "sentiment": "negative", "target_tags": "My\\O wife\\O had\\O barely\\O touched\\O that\\O mess\\O of\\O a\\O dish\\B .\\O", "opinion_tags": "My\\O wife\\O had\\O barely\\O touched\\O that\\O mess\\B of\\O a\\O dish\\O .\\O"}], "postag": ["PRP$", "NN", "VBD", "RB", "VBN", "DT", "NN", "IN", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 7, 5, 10, 10, 7, 5], "deprel": ["nmod:poss", "nsubj", "aux", "advmod", "root", "det", "obj", "case", "det", "nmod", "punct"]}, {"id": "195", "sentence": "The wife had the risotto which was amazing .", "triples": [{"uid": "195-0", "sentiment": "positive", "target_tags": "The\\O wife\\O had\\O the\\O risotto\\B which\\O was\\O amazing\\O .\\O", "opinion_tags": "The\\O wife\\O had\\O the\\O risotto\\O which\\O was\\O amazing\\B .\\O"}], "postag": ["DT", "NN", "VBD", "DT", "NN", "WDT", "VBD", "JJ", "."], "head": [2, 3, 0, 5, 3, 8, 8, 5, 3], "deprel": ["det", "nsubj", "root", "det", "obj", "nsubj", "cop", "acl:relcl", "punct"]}, {"id": "196", "sentence": "The crust has a great bite and a good chew , the sauce is light with a nice acidity to it , the salt from the cheese is great , really heightens the flavor of all the other components .", "triples": [{"uid": "196-0", "sentiment": "positive", "target_tags": "The\\O crust\\B has\\O a\\O great\\O bite\\O and\\O a\\O good\\O chew\\O ,\\O the\\O sauce\\O is\\O light\\O with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\O is\\O great\\O ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O", "opinion_tags": "The\\O crust\\O has\\O a\\O great\\B bite\\O and\\O a\\O good\\O chew\\O ,\\O the\\O sauce\\O is\\O light\\O with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\O is\\O great\\O ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O"}, {"uid": "196-1", "sentiment": "positive", "target_tags": "The\\O crust\\B has\\O a\\O great\\O bite\\O and\\O a\\O good\\O chew\\O ,\\O the\\O sauce\\O is\\O light\\O with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\O is\\O great\\O ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O", "opinion_tags": "The\\O crust\\O has\\O a\\O great\\O bite\\O and\\O a\\O good\\B chew\\O ,\\O the\\O sauce\\O is\\O light\\O with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\O is\\O great\\O ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O"}, {"uid": "196-2", "sentiment": "positive", "target_tags": "The\\O crust\\O has\\O a\\O great\\O bite\\O and\\O a\\O good\\O chew\\O ,\\O the\\O sauce\\B is\\O light\\O with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\O is\\O great\\O ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O", "opinion_tags": "The\\O crust\\O has\\O a\\O great\\O bite\\O and\\O a\\O good\\O chew\\O ,\\O the\\O sauce\\O is\\O light\\B with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\O is\\O great\\O ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O"}, {"uid": "196-3", "sentiment": "positive", "target_tags": "The\\O crust\\O has\\O a\\O great\\O bite\\O and\\O a\\O good\\O chew\\O ,\\O the\\O sauce\\O is\\O light\\O with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\B is\\O great\\O ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O", "opinion_tags": "The\\O crust\\O has\\O a\\O great\\O bite\\O and\\O a\\O good\\O chew\\O ,\\O the\\O sauce\\O is\\O light\\O with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\O is\\O great\\B ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "JJ", "NN", "CC", "DT", "JJ", "NN", ",", "DT", "NN", "VBZ", "JJ", "IN", "DT", "JJ", "NN", "IN", "PRP", ",", "DT", "NN", "IN", "DT", "NN", "VBZ", "JJ", ",", "RB", "VBZ", "DT", "NN", "IN", "PDT", "DT", "JJ", "NNS", "."], "head": [2, 3, 0, 6, 6, 3, 10, 10, 10, 6, 3, 13, 15, 15, 3, 19, 19, 19, 15, 21, 19, 29, 24, 29, 27, 27, 24, 29, 3, 32, 32, 29, 34, 32, 39, 39, 39, 39, 34, 3], "deprel": ["det", "nsubj", "root", "det", "amod", "obj", "cc", "det", "amod", "conj", "punct", "det", "nsubj", "cop", "conj", "case", "det", "amod", "obl", "case", "nmod", "punct", "det", "nsubj", "case", "det", "nmod", "cop", "parataxis", "punct", "advmod", "conj", "det", "obj", "case", "det:predet", "det", "amod", "nmod", "punct"]}, {"id": "197", "sentence": "I picked the Grilled Black Cod as my entree , which I absolutely devoured while someone commented that the Grilled Salmon dish was better .", "triples": [{"uid": "197-0", "sentiment": "positive", "target_tags": "I\\O picked\\O the\\O Grilled\\B Black\\I Cod\\I as\\O my\\O entree\\O ,\\O which\\O I\\O absolutely\\O devoured\\O while\\O someone\\O commented\\O that\\O the\\O Grilled\\O Salmon\\O dish\\O was\\O better\\O .\\O", "opinion_tags": "I\\O picked\\O the\\O Grilled\\O Black\\O Cod\\O as\\O my\\O entree\\O ,\\O which\\O I\\O absolutely\\O devoured\\B while\\O someone\\O commented\\O that\\O the\\O Grilled\\O Salmon\\O dish\\O was\\O better\\O .\\O"}, {"uid": "197-1", "sentiment": "positive", "target_tags": "I\\O picked\\O the\\O Grilled\\O Black\\O Cod\\O as\\O my\\O entree\\O ,\\O which\\O I\\O absolutely\\O devoured\\O while\\O someone\\O commented\\O that\\O the\\O Grilled\\B Salmon\\I dish\\I was\\O better\\O .\\O", "opinion_tags": "I\\O picked\\O the\\O Grilled\\O Black\\O Cod\\O as\\O my\\O entree\\O ,\\O which\\O I\\O absolutely\\O devoured\\O while\\O someone\\O commented\\O that\\O the\\O Grilled\\O Salmon\\O dish\\O was\\O better\\B .\\O"}], "postag": ["PRP", "VBD", "DT", "VBN", "NNP", "NNP", "IN", "PRP$", "NN", ",", "WDT", "PRP", "RB", "VBD", "IN", "NN", "VBD", "IN", "DT", "JJ", "NN", "NN", "VBD", "JJR", "."], "head": [2, 0, 6, 6, 6, 2, 9, 9, 2, 14, 14, 14, 14, 9, 17, 17, 14, 24, 22, 22, 22, 24, 24, 17, 2], "deprel": ["nsubj", "root", "det", "amod", "compound", "obj", "case", "nmod:poss", "obl", "punct", "obj", "nsubj", "advmod", "acl:relcl", "mark", "nsubj", "advcl", "mark", "det", "amod", "compound", "nsubj", "cop", "ccomp", "punct"]}, {"id": "198", "sentence": "The service leaves much to be desired , from feeling like you are rushed the place your order , to being ignored the rest of the night .", "triples": [{"uid": "198-0", "sentiment": "negative", "target_tags": "The\\O service\\B leaves\\O much\\O to\\O be\\O desired\\O ,\\O from\\O feeling\\O like\\O you\\O are\\O rushed\\O the\\O place\\O your\\O order\\O ,\\O to\\O being\\O ignored\\O the\\O rest\\O of\\O the\\O night\\O .\\O", "opinion_tags": "The\\O service\\O leaves\\B much\\I to\\I be\\I desired\\I ,\\O from\\O feeling\\O like\\O you\\O are\\O rushed\\O the\\O place\\O your\\O order\\O ,\\O to\\O being\\O ignored\\O the\\O rest\\O of\\O the\\O night\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "TO", "VB", "VBN", ",", "IN", "VBG", "IN", "PRP", "VBP", "VBN", "DT", "NN", "PRP$", "NN", ",", "IN", "VBG", "VBN", "DT", "NN", "IN", "DT", "NN", "."], "head": [2, 3, 0, 3, 7, 7, 4, 10, 10, 3, 14, 14, 14, 10, 16, 14, 18, 14, 14, 22, 22, 14, 24, 22, 27, 27, 24, 3], "deprel": ["det", "nsubj", "root", "obj", "mark", "aux:pass", "acl", "punct", "mark", "advcl", "mark", "nsubj:pass", "aux:pass", "advcl", "det", "obj", "nmod:poss", "obj", "punct", "mark", "aux:pass", "advcl", "det", "obj", "case", "det", "nmod", "punct"]}, {"id": "199", "sentence": "The hot dogs are good , yes , but the reason to get over here is the fantastic pork croquette sandwich , perfect on its supermarket squishy bun .", "triples": [{"uid": "199-0", "sentiment": "positive", "target_tags": "The\\O hot\\B dogs\\I are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\O croquette\\O sandwich\\O ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O good\\B ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\O croquette\\O sandwich\\O ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O"}, {"uid": "199-1", "sentiment": "positive", "target_tags": "The\\O hot\\O dogs\\O are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\B croquette\\I sandwich\\I ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\B pork\\O croquette\\O sandwich\\O ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O"}, {"uid": "199-2", "sentiment": "positive", "target_tags": "The\\O hot\\O dogs\\O are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\O croquette\\O sandwich\\O ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\B .\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\O croquette\\O sandwich\\O ,\\O perfect\\B on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O"}], "postag": ["DT", "JJ", "NNS", "VBP", "JJ", ",", "UH", ",", "CC", "DT", "NN", "TO", "VB", "IN", "RB", "VBZ", "DT", "JJ", "NN", "NN", "NN", ",", "JJ", "IN", "PRP$", "NN", "JJ", "NN", "."], "head": [3, 3, 5, 5, 0, 21, 21, 21, 21, 11, 21, 13, 11, 15, 13, 21, 21, 21, 20, 21, 5, 21, 21, 28, 28, 28, 28, 23, 5], "deprel": ["det", "amod", "nsubj", "cop", "root", "punct", "discourse", "punct", "cc", "det", "nsubj", "mark", "acl", "case", "obl", "cop", "det", "amod", "compound", "compound", "conj", "punct", "amod", "case", "nmod:poss", "compound", "amod", "obl", "punct"]}, {"id": "200", "sentence": "The family seafood entree was very good .", "triples": [{"uid": "200-0", "sentiment": "positive", "target_tags": "The\\O family\\B seafood\\I entree\\I was\\O very\\O good\\O .\\O", "opinion_tags": "The\\O family\\O seafood\\O entree\\O was\\O very\\O good\\B .\\O"}], "postag": ["DT", "NN", "NN", "NN", "VBD", "RB", "JJ", "."], "head": [4, 4, 4, 7, 7, 7, 0, 7], "deprel": ["det", "compound", "compound", "nsubj", "cop", "advmod", "root", "punct"]}, {"id": "201", "sentence": "Price is high but the food is good , so I would come back again .", "triples": [{"uid": "201-0", "sentiment": "negative", "target_tags": "Price\\O is\\O high\\O but\\O the\\O food\\B is\\O good\\O ,\\O so\\O I\\O would\\O come\\O back\\O again\\O .\\O", "opinion_tags": "Price\\O is\\O high\\O but\\O the\\O food\\O is\\O good\\B ,\\O so\\O I\\O would\\O come\\O back\\O again\\O .\\O"}], "postag": ["NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "JJ", ",", "RB", "PRP", "MD", "VB", "RB", "RB", "."], "head": [3, 3, 0, 8, 6, 8, 8, 3, 13, 13, 13, 13, 3, 13, 13, 3], "deprel": ["nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "punct", "advmod", "nsubj", "aux", "conj", "advmod", "advmod", "punct"]}, {"id": "202", "sentence": "This place is not inviting and the food is totally weird .", "triples": [{"uid": "202-0", "sentiment": "negative", "target_tags": "This\\O place\\B is\\O not\\O inviting\\O and\\O the\\O food\\O is\\O totally\\O weird\\O .\\O", "opinion_tags": "This\\O place\\O is\\O not\\B inviting\\I and\\O the\\O food\\O is\\O totally\\O weird\\O .\\O"}, {"uid": "202-1", "sentiment": "negative", "target_tags": "This\\O place\\O is\\O not\\O inviting\\O and\\O the\\O food\\B is\\O totally\\O weird\\O .\\O", "opinion_tags": "This\\O place\\O is\\O not\\O inviting\\O and\\O the\\O food\\O is\\O totally\\O weird\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "VBG", "CC", "DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 11, 8, 11, 11, 11, 5, 5], "deprel": ["det", "nsubj", "aux", "advmod", "root", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct"]}, {"id": "203", "sentence": "A coworker and I tried Pacifico after work a few Fridays and loved it .", "triples": [{"uid": "203-0", "sentiment": "positive", "target_tags": "A\\O coworker\\O and\\O I\\O tried\\O Pacifico\\B after\\O work\\O a\\O few\\O Fridays\\O and\\O loved\\O it\\O .\\O", "opinion_tags": "A\\O coworker\\O and\\O I\\O tried\\O Pacifico\\O after\\O work\\O a\\O few\\O Fridays\\O and\\O loved\\B it\\O .\\O"}], "postag": ["DT", "NN", "CC", "PRP", "VBD", "NNP", "IN", "NN", "DT", "JJ", "NNPS", "CC", "VBD", "PRP", "."], "head": [2, 5, 4, 2, 0, 5, 8, 5, 11, 11, 5, 13, 5, 13, 5], "deprel": ["det", "nsubj", "cc", "conj", "root", "obj", "case", "obl", "det", "amod", "obl:tmod", "cc", "conj", "obj", "punct"]}, {"id": "204", "sentence": "The food we ordered was excellent , although I would n't say the margaritas were anything to write home about .", "triples": [{"uid": "204-0", "sentiment": "positive", "target_tags": "The\\O food\\B we\\O ordered\\O was\\O excellent\\O ,\\O although\\O I\\O would\\O n't\\O say\\O the\\O margaritas\\O were\\O anything\\O to\\O write\\O home\\O about\\O .\\O", "opinion_tags": "The\\O food\\O we\\O ordered\\O was\\O excellent\\B ,\\O although\\O I\\O would\\O n't\\O say\\O the\\O margaritas\\O were\\O anything\\O to\\O write\\O home\\O about\\O .\\O"}], "postag": ["DT", "NN", "PRP", "VBD", "VBD", "JJ", ",", "IN", "PRP", "MD", "RB", "VB", "DT", "NNS", "VBD", "NN", "TO", "VB", "RB", "IN", "."], "head": [2, 6, 4, 2, 6, 0, 6, 12, 12, 12, 12, 6, 14, 16, 16, 12, 18, 16, 18, 18, 6], "deprel": ["det", "nsubj", "nsubj", "acl:relcl", "cop", "root", "punct", "mark", "nsubj", "aux", "advmod", "advcl", "det", "nsubj", "cop", "ccomp", "mark", "acl", "advmod", "obl", "punct"]}, {"id": "205", "sentence": "The hot dogs are top notch , and they 're <PERSON><PERSON> is amazing !", "triples": [{"uid": "205-0", "sentiment": "positive", "target_tags": "The\\O hot\\B dogs\\I are\\O top\\O notch\\O ,\\O and\\O they\\O 're\\O Slamwich\\O is\\O amazing\\O !\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O top\\B notch\\I ,\\O and\\O they\\O 're\\O Slamwich\\O is\\O amazing\\O !\\O"}, {"uid": "205-1", "sentiment": "positive", "target_tags": "The\\O hot\\O dogs\\O are\\O top\\O notch\\O ,\\O and\\O they\\O 're\\O Slamwich\\B is\\O amazing\\O !\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O top\\O notch\\O ,\\O and\\O they\\O 're\\O Slamwich\\O is\\O amazing\\B !\\O"}], "postag": ["DT", "JJ", "NNS", "VBP", "JJ", "NN", ",", "CC", "PRP", "VBP", "NNP", "VBZ", "JJ", "."], "head": [3, 3, 6, 6, 6, 0, 13, 13, 13, 13, 13, 13, 6, 6], "deprel": ["det", "amod", "nsubj", "cop", "amod", "root", "punct", "cc", "nsubj", "cop", "nsubj", "cop", "conj", "punct"]}, {"id": "206", "sentence": "But nonetheless -- great spot , great food .", "triples": [{"uid": "206-0", "sentiment": "positive", "target_tags": "But\\O nonetheless\\O --\\O great\\O spot\\B ,\\O great\\O food\\O .\\O", "opinion_tags": "But\\O nonetheless\\O --\\O great\\B spot\\O ,\\O great\\O food\\O .\\O"}, {"uid": "206-1", "sentiment": "positive", "target_tags": "But\\O nonetheless\\O --\\O great\\O spot\\O ,\\O great\\O food\\B .\\O", "opinion_tags": "But\\O nonetheless\\O --\\O great\\O spot\\O ,\\O great\\B food\\O .\\O"}], "postag": ["CC", "RB", ",", "JJ", "NN", ",", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 5, 8, 5, 5], "deprel": ["cc", "advmod", "punct", "amod", "root", "punct", "amod", "conj", "punct"]}, {"id": "207", "sentence": "This guy refused to seat her and she left , followed shortly by the four of us , but not before I told him that in my 40 years of world travel , including Paris , that I had never seen such a display of bad behavior by a frontman in a restaurant .", "triples": [{"uid": "207-0", "sentiment": "negative", "target_tags": "This\\O guy\\O refused\\O to\\O seat\\O her\\O and\\O she\\O left\\O ,\\O followed\\O shortly\\O by\\O the\\O four\\O of\\O us\\O ,\\O but\\O not\\O before\\O I\\O told\\O him\\O that\\O in\\O my\\O 40\\O years\\O of\\O world\\O travel\\O ,\\O including\\O Paris\\O ,\\O that\\O I\\O had\\O never\\O seen\\O such\\O a\\O display\\O of\\O bad\\O behavior\\O by\\O a\\O frontman\\B in\\O a\\O restaurant\\O .\\O", "opinion_tags": "This\\O guy\\O refused\\O to\\O seat\\O her\\O and\\O she\\O left\\O ,\\O followed\\O shortly\\O by\\O the\\O four\\O of\\O us\\O ,\\O but\\O not\\O before\\O I\\O told\\O him\\O that\\O in\\O my\\O 40\\O years\\O of\\O world\\O travel\\O ,\\O including\\O Paris\\O ,\\O that\\O I\\O had\\O never\\O seen\\O such\\O a\\O display\\O of\\O bad\\B behavior\\O by\\O a\\O frontman\\O in\\O a\\O restaurant\\O .\\O"}], "postag": ["DT", "NN", "VBD", "TO", "VB", "PRP", "CC", "PRP", "VBD", ",", "VBN", "RB", "IN", "DT", "CD", "IN", "PRP", ",", "CC", "RB", "IN", "PRP", "VBD", "PRP", "IN", "IN", "PRP$", "CD", "NNS", "IN", "NN", "NN", ",", "VBG", "NNP", ",", "IN", "PRP", "VBD", "RB", "VBN", "PDT", "DT", "NN", "IN", "JJ", "NN", "IN", "DT", "NN", "IN", "DT", "NN", "."], "head": [2, 3, 0, 5, 3, 5, 9, 9, 3, 11, 9, 11, 15, 15, 11, 17, 15, 23, 23, 23, 23, 23, 9, 23, 41, 29, 29, 29, 41, 32, 32, 29, 29, 35, 32, 41, 41, 41, 41, 41, 23, 44, 44, 41, 47, 47, 44, 50, 50, 41, 53, 53, 50, 3], "deprel": ["det", "nsubj", "root", "mark", "xcomp", "obj", "cc", "nsubj", "conj", "punct", "advcl", "advmod", "case", "det", "obl", "case", "nmod", "punct", "cc", "advmod", "mark", "nsubj", "advcl", "obj", "mark", "case", "nmod:poss", "nummod", "obl", "case", "compound", "nmod", "punct", "case", "nmod", "punct", "mark", "nsubj", "aux", "advmod", "ccomp", "det:predet", "det", "obj", "case", "amod", "nmod", "case", "det", "obl", "case", "det", "nmod", "punct"]}, {"id": "208", "sentence": "Mussles and calamari were superb Saturday evening .", "triples": [{"uid": "208-0", "sentiment": "positive", "target_tags": "Mussles\\B and\\O calamari\\O were\\O superb\\O Saturday\\O evening\\O .\\O", "opinion_tags": "Mussles\\O and\\O calamari\\O were\\O superb\\B Saturday\\O evening\\O .\\O"}, {"uid": "208-1", "sentiment": "positive", "target_tags": "Mussles\\O and\\O calamari\\B were\\O superb\\O Saturday\\O evening\\O .\\O", "opinion_tags": "Mussles\\O and\\O calamari\\O were\\O superb\\B Saturday\\O evening\\O .\\O"}], "postag": ["NNS", "CC", "NN", "VBD", "JJ", "NNP", "NN", "."], "head": [5, 3, 1, 5, 0, 7, 5, 5], "deprel": ["nsubj", "cc", "conj", "cop", "root", "compound", "obl:tmod", "punct"]}, {"id": "209", "sentence": "I had the Lamb special which was perfect .", "triples": [{"uid": "209-0", "sentiment": "positive", "target_tags": "I\\O had\\O the\\O Lamb\\B special\\I which\\O was\\O perfect\\O .\\O", "opinion_tags": "I\\O had\\O the\\O Lamb\\O special\\O which\\O was\\O perfect\\B .\\O"}], "postag": ["PRP", "VBD", "DT", "NNP", "NN", "WDT", "VBD", "JJ", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "nsubj", "cop", "acl:relcl", "punct"]}]