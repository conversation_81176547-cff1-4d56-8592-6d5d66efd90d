[{"id": "0", "sentence": "In the shop , these MacBooks are encased in a soft rubber enclosure - so you will never know about the razor edge until you buy it , get it home , break the seal and use it ( very clever con ) .", "triples": [{"uid": "0-0", "sentiment": "positive", "target_tags": "In\\O the\\O shop\\O ,\\O these\\O MacBooks\\O are\\O encased\\O in\\O a\\O soft\\O rubber\\B enclosure\\I -\\O so\\O you\\O will\\O never\\O know\\O about\\O the\\O razor\\O edge\\O until\\O you\\O buy\\O it\\O ,\\O get\\O it\\O home\\O ,\\O break\\O the\\O seal\\O and\\O use\\O it\\O (\\O very\\O clever\\O con\\O )\\O .\\O", "opinion_tags": "In\\O the\\O shop\\O ,\\O these\\O MacBooks\\O are\\O encased\\O in\\O a\\O soft\\B rubber\\O enclosure\\O -\\O so\\O you\\O will\\O never\\O know\\O about\\O the\\O razor\\O edge\\O until\\O you\\O buy\\O it\\O ,\\O get\\O it\\O home\\O ,\\O break\\O the\\O seal\\O and\\O use\\O it\\O (\\O very\\O clever\\O con\\O )\\O .\\O"}], "postag": ["IN", "DT", "NN", ",", "DT", "NNPS", "VBP", "VBN", "IN", "DT", "JJ", "NN", "NN", ",", "RB", "PRP", "MD", "RB", "VB", "IN", "DT", "NN", "NN", "IN", "PRP", "VBP", "PRP", ",", "VB", "PRP", "RB", ",", "VB", "DT", "NN", "CC", "VB", "PRP", "-LRB-", "RB", "JJ", "NN", "-RRB-", "."], "head": [3, 3, 8, 8, 6, 8, 8, 0, 13, 13, 13, 13, 8, 8, 19, 19, 19, 19, 8, 23, 23, 23, 19, 26, 26, 19, 26, 29, 19, 29, 29, 33, 19, 35, 33, 37, 33, 37, 42, 41, 42, 37, 42, 8], "deprel": ["case", "det", "obl", "punct", "det", "nsubj:pass", "aux:pass", "root", "case", "det", "amod", "compound", "obl", "punct", "advmod", "nsubj", "aux", "advmod", "parataxis", "case", "det", "compound", "obl", "mark", "nsubj", "advcl", "obj", "punct", "conj", "obj", "advmod", "punct", "conj", "det", "obj", "cc", "conj", "obj", "punct", "advmod", "amod", "parataxis", "punct", "punct"]}, {"id": "1", "sentence": "This laptop meets every expectation and Windows 7 is great !", "triples": [{"uid": "1-0", "sentiment": "positive", "target_tags": "This\\O laptop\\O meets\\O every\\O expectation\\O and\\O Windows\\B 7\\I is\\O great\\O !\\O", "opinion_tags": "This\\O laptop\\O meets\\O every\\O expectation\\O and\\O Windows\\O 7\\O is\\O great\\B !\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "NN", "CC", "NNS", "CD", "VBZ", "JJ", "."], "head": [2, 3, 0, 5, 3, 10, 10, 7, 10, 3, 3], "deprel": ["det", "nsubj", "root", "det", "obj", "cc", "nsubj", "nummod", "cop", "conj", "punct"]}, {"id": "2", "sentence": "Drivers updated ok but the BIOS update froze the system up and the computer shut down .", "triples": [{"uid": "2-0", "sentiment": "positive", "target_tags": "Drivers\\B updated\\O ok\\O but\\O the\\O BIOS\\O update\\O froze\\O the\\O system\\O up\\O and\\O the\\O computer\\O shut\\O down\\O .\\O", "opinion_tags": "Drivers\\O updated\\O ok\\B but\\O the\\O BIOS\\O update\\O froze\\O the\\O system\\O up\\O and\\O the\\O computer\\O shut\\O down\\O .\\O"}, {"uid": "2-1", "sentiment": "negative", "target_tags": "Drivers\\O updated\\O ok\\O but\\O the\\O BIOS\\B update\\I froze\\O the\\O system\\O up\\O and\\O the\\O computer\\O shut\\O down\\O .\\O", "opinion_tags": "Drivers\\O updated\\O ok\\O but\\O the\\O BIOS\\O update\\O froze\\B the\\O system\\O up\\O and\\O the\\O computer\\O shut\\O down\\O .\\O"}, {"uid": "2-2", "sentiment": "negative", "target_tags": "Drivers\\O updated\\O ok\\O but\\O the\\O BIOS\\O update\\O froze\\O the\\O system\\B up\\O and\\O the\\O computer\\O shut\\O down\\O .\\O", "opinion_tags": "Drivers\\O updated\\O ok\\O but\\O the\\O BIOS\\O update\\O froze\\B the\\O system\\O up\\O and\\O the\\O computer\\O shut\\O down\\O .\\O"}], "postag": ["NNS", "VBD", "JJ", "CC", "DT", "NN", "NN", "VBD", "DT", "NN", "RP", "CC", "DT", "NN", "VBD", "RP", "."], "head": [2, 0, 2, 8, 7, 7, 8, 2, 10, 8, 8, 15, 14, 15, 8, 15, 2], "deprel": ["nsubj", "root", "xcomp", "cc", "det", "compound", "nsubj", "conj", "det", "obj", "compound:prt", "cc", "det", "nsubj", "conj", "compound:prt", "punct"]}, {"id": "3", "sentence": "The fact that you can spend over $ 100 on just a webcam underscores the value of this machine .", "triples": [{"uid": "3-0", "sentiment": "positive", "target_tags": "The\\O fact\\O that\\O you\\O can\\O spend\\O over\\O $\\O 100\\O on\\O just\\O a\\O webcam\\O underscores\\O the\\O value\\B of\\O this\\O machine\\O .\\O", "opinion_tags": "The\\O fact\\O that\\O you\\O can\\O spend\\O over\\O $\\O 100\\O on\\O just\\O a\\O webcam\\O underscores\\B the\\O value\\O of\\O this\\O machine\\O .\\O"}], "postag": ["DT", "NN", "IN", "PRP", "MD", "VB", "RB", "$", "CD", "IN", "RB", "DT", "NN", "VBZ", "DT", "NN", "IN", "DT", "NN", "."], "head": [2, 14, 6, 6, 6, 2, 8, 6, 8, 13, 13, 13, 6, 0, 16, 14, 19, 19, 16, 14], "deprel": ["det", "nsubj", "mark", "nsubj", "aux", "acl", "advmod", "obj", "nummod", "case", "advmod", "det", "obl", "root", "det", "obj", "case", "det", "nmod", "punct"]}, {"id": "4", "sentence": "It rarely works and when it does it 's incredibly slow .", "triples": [{"uid": "4-0", "sentiment": "negative", "target_tags": "It\\O rarely\\O works\\B and\\O when\\O it\\O does\\O it\\O 's\\O incredibly\\O slow\\O .\\O", "opinion_tags": "It\\O rarely\\B works\\O and\\O when\\O it\\O does\\O it\\O 's\\O incredibly\\O slow\\O .\\O"}], "postag": ["PRP", "RB", "VBZ", "CC", "WRB", "PRP", "VBZ", "PRP", "VBZ", "RB", "JJ", "."], "head": [3, 3, 0, 11, 11, 11, 11, 11, 11, 11, 3, 3], "deprel": ["nsubj", "advmod", "root", "cc", "mark", "nsubj", "aux", "nsubj", "cop", "advmod", "conj", "punct"]}, {"id": "5", "sentence": "It 's so much easier to navigate through the operating system , to find files , and it runs a lot faster !", "triples": [{"uid": "5-0", "sentiment": "positive", "target_tags": "It\\O 's\\O so\\O much\\O easier\\O to\\O navigate\\O through\\O the\\O operating\\B system\\I ,\\O to\\O find\\O files\\O ,\\O and\\O it\\O runs\\O a\\O lot\\O faster\\O !\\O", "opinion_tags": "It\\O 's\\O so\\O much\\O easier\\B to\\O navigate\\O through\\O the\\O operating\\O system\\O ,\\O to\\O find\\O files\\O ,\\O and\\O it\\O runs\\O a\\O lot\\O faster\\O !\\O"}, {"uid": "5-1", "sentiment": "positive", "target_tags": "It\\O 's\\O so\\O much\\O easier\\O to\\O navigate\\O through\\O the\\O operating\\O system\\O ,\\O to\\O find\\O files\\O ,\\O and\\O it\\O runs\\B a\\O lot\\O faster\\O !\\O", "opinion_tags": "It\\O 's\\O so\\O much\\O easier\\O to\\O navigate\\O through\\O the\\O operating\\O system\\O ,\\O to\\O find\\O files\\O ,\\O and\\O it\\O runs\\O a\\O lot\\O faster\\B !\\O"}, {"uid": "5-2", "sentiment": "positive", "target_tags": "It\\O 's\\O so\\O much\\O easier\\O to\\O navigate\\B through\\O the\\O operating\\O system\\O ,\\O to\\O find\\O files\\O ,\\O and\\O it\\O runs\\O a\\O lot\\O faster\\O !\\O", "opinion_tags": "It\\O 's\\O so\\O much\\O easier\\B to\\O navigate\\O through\\O the\\O operating\\O system\\O ,\\O to\\O find\\O files\\O ,\\O and\\O it\\O runs\\O a\\O lot\\O faster\\O !\\O"}, {"uid": "5-3", "sentiment": "positive", "target_tags": "It\\O 's\\O so\\O much\\O easier\\O to\\O navigate\\O through\\O the\\O operating\\O system\\O ,\\O to\\O find\\B files\\I ,\\O and\\O it\\O runs\\O a\\O lot\\O faster\\O !\\O", "opinion_tags": "It\\O 's\\O so\\O much\\O easier\\O to\\O navigate\\O through\\O the\\O operating\\O system\\O ,\\O to\\O find\\O files\\O ,\\O and\\O it\\O runs\\O a\\O lot\\O faster\\B !\\O"}], "postag": ["PRP", "VBZ", "RB", "RB", "JJR", "TO", "VB", "IN", "DT", "NN", "NN", ",", "TO", "VB", "NNS", ",", "CC", "PRP", "VBZ", "DT", "NN", "JJR", "."], "head": [5, 5, 4, 5, 0, 7, 5, 11, 11, 11, 7, 14, 14, 7, 14, 19, 19, 19, 5, 21, 22, 19, 5], "deprel": ["expl", "cop", "advmod", "advmod", "root", "mark", "csubj", "case", "det", "compound", "obl", "punct", "mark", "advcl", "obj", "punct", "cc", "nsubj", "conj", "det", "obl:npmod", "advmod", "punct"]}, {"id": "6", "sentence": "I am using the external speaker -- sound is good .", "triples": [{"uid": "6-0", "sentiment": "positive", "target_tags": "I\\O am\\O using\\O the\\O external\\B speaker\\I --\\O sound\\O is\\O good\\O .\\O", "opinion_tags": "I\\O am\\O using\\O the\\O external\\O speaker\\O --\\O sound\\O is\\O good\\B .\\O"}, {"uid": "6-1", "sentiment": "positive", "target_tags": "I\\O am\\O using\\O the\\O external\\O speaker\\O --\\O sound\\B is\\O good\\O .\\O", "opinion_tags": "I\\O am\\O using\\O the\\O external\\O speaker\\O --\\O sound\\O is\\O good\\B .\\O"}], "postag": ["PRP", "VBP", "VBG", "DT", "JJ", "NN", ",", "NN", "VBZ", "JJ", "."], "head": [3, 3, 0, 6, 6, 3, 3, 10, 10, 3, 3], "deprel": ["nsubj", "aux", "root", "det", "amod", "obj", "punct", "nsubj", "cop", "parataxis", "punct"]}, {"id": "7", "sentence": "The battery life seems to be very good , and have had no issues with it .", "triples": [{"uid": "7-0", "sentiment": "positive", "target_tags": "The\\O battery\\B life\\I seems\\O to\\O be\\O very\\O good\\O ,\\O and\\O have\\O had\\O no\\O issues\\O with\\O it\\O .\\O", "opinion_tags": "The\\O battery\\O life\\O seems\\O to\\O be\\O very\\O good\\B ,\\O and\\O have\\O had\\O no\\O issues\\O with\\O it\\O .\\O"}, {"uid": "7-1", "sentiment": "positive", "target_tags": "The\\O battery\\B life\\I seems\\O to\\O be\\O very\\O good\\O ,\\O and\\O have\\O had\\O no\\O issues\\O with\\O it\\O .\\O", "opinion_tags": "The\\O battery\\O life\\O seems\\O to\\O be\\O very\\O good\\O ,\\O and\\O have\\O had\\O no\\B issues\\I with\\O it\\O .\\O"}], "postag": ["DT", "NN", "NN", "VBZ", "TO", "VB", "RB", "JJ", ",", "CC", "VBP", "VBN", "DT", "NNS", "IN", "PRP", "."], "head": [3, 3, 4, 0, 8, 8, 8, 4, 12, 12, 12, 4, 14, 12, 16, 14, 4], "deprel": ["det", "compound", "nsubj", "root", "mark", "cop", "advmod", "xcomp", "punct", "cc", "aux", "conj", "det", "obj", "case", "nmod", "punct"]}, {"id": "8", "sentence": "Temperatures on the outside were alright but i did not track in Core Processing Unit temperatures .", "triples": [{"uid": "8-0", "sentiment": "neutral", "target_tags": "Temperatures\\B on\\O the\\O outside\\O were\\O alright\\O but\\O i\\O did\\O not\\O track\\O in\\O Core\\O Processing\\O Unit\\O temperatures\\O .\\O", "opinion_tags": "Temperatures\\O on\\O the\\O outside\\O were\\O alright\\B but\\O i\\O did\\O not\\O track\\O in\\O Core\\O Processing\\O Unit\\O temperatures\\O .\\O"}], "postag": ["NNS", "IN", "DT", "NN", "VBD", "JJ", "CC", "PRP", "VBD", "RB", "VB", "IN", "NN", "NN", "NN", "NNS", "."], "head": [6, 4, 4, 1, 6, 0, 11, 11, 11, 11, 6, 16, 14, 15, 16, 11, 6], "deprel": ["nsubj", "case", "det", "nmod", "cop", "root", "cc", "nsubj", "aux", "advmod", "conj", "case", "compound", "compound", "compound", "obl", "punct"]}, {"id": "9", "sentence": "After about a week I finally got it back and was told that the motherboard had failed and so they installed a new motherboard .", "triples": [{"uid": "9-0", "sentiment": "neutral", "target_tags": "After\\O about\\O a\\O week\\O I\\O finally\\O got\\O it\\O back\\O and\\O was\\O told\\O that\\O the\\O motherboard\\B had\\O failed\\O and\\O so\\O they\\O installed\\O a\\O new\\O motherboard\\O .\\O", "opinion_tags": "After\\O about\\O a\\O week\\O I\\O finally\\O got\\O it\\O back\\O and\\O was\\O told\\O that\\O the\\O motherboard\\O had\\O failed\\B and\\O so\\O they\\O installed\\O a\\O new\\O motherboard\\O .\\O"}, {"uid": "9-1", "sentiment": "neutral", "target_tags": "After\\O about\\O a\\O week\\O I\\O finally\\O got\\O it\\O back\\O and\\O was\\O told\\O that\\O the\\O motherboard\\O had\\O failed\\O and\\O so\\O they\\O installed\\O a\\O new\\O motherboard\\B .\\O", "opinion_tags": "After\\O about\\O a\\O week\\O I\\O finally\\O got\\O it\\O back\\O and\\O was\\O told\\O that\\O the\\O motherboard\\O had\\O failed\\O and\\O so\\O they\\O installed\\O a\\O new\\B motherboard\\O .\\O"}], "postag": ["IN", "RB", "DT", "NN", "PRP", "RB", "VBD", "PRP", "RB", "CC", "VBD", "VBN", "IN", "DT", "NN", "VBD", "VBN", "CC", "RB", "PRP", "VBD", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 7, 7, 7, 0, 7, 7, 12, 12, 7, 17, 15, 17, 17, 12, 21, 21, 21, 7, 24, 24, 21, 7], "deprel": ["case", "advmod", "det", "obl", "nsubj", "advmod", "root", "obj", "advmod", "cc", "aux:pass", "conj", "mark", "det", "nsubj", "aux", "ccomp", "cc", "advmod", "nsubj", "conj", "det", "amod", "obj", "punct"]}, {"id": "10", "sentence": "It was slow , locked up , and also had hardware replaced after only 2 months !", "triples": [{"uid": "10-0", "sentiment": "negative", "target_tags": "It\\O was\\O slow\\O ,\\O locked\\O up\\O ,\\O and\\O also\\O had\\O hardware\\B replaced\\O after\\O only\\O 2\\O months\\O !\\O", "opinion_tags": "It\\O was\\O slow\\O ,\\O locked\\O up\\O ,\\O and\\O also\\O had\\O hardware\\O replaced\\B after\\O only\\O 2\\O months\\O !\\O"}], "postag": ["PRP", "VBD", "JJ", ",", "VBN", "RP", ",", "CC", "RB", "VBD", "NN", "VBN", "IN", "RB", "CD", "NNS", "."], "head": [3, 3, 0, 5, 3, 5, 10, 10, 10, 3, 10, 11, 16, 16, 16, 12, 3], "deprel": ["nsubj", "cop", "root", "punct", "conj", "compound:prt", "punct", "cc", "advmod", "conj", "obj", "acl", "case", "advmod", "nummod", "obl", "punct"]}, {"id": "11", "sentence": "Yes , the computer was light weight , less expensive than the average laptop , and was pretty self explantory in use .", "triples": [{"uid": "11-0", "sentiment": "positive", "target_tags": "Yes\\O ,\\O the\\O computer\\O was\\O light\\O weight\\O ,\\O less\\O expensive\\O than\\O the\\O average\\O laptop\\O ,\\O and\\O was\\O pretty\\O self\\O explantory\\O in\\O use\\B .\\O", "opinion_tags": "Yes\\O ,\\O the\\O computer\\O was\\O light\\O weight\\O ,\\O less\\O expensive\\O than\\O the\\O average\\O laptop\\O ,\\O and\\O was\\O pretty\\O self\\B explantory\\I in\\O use\\O .\\O"}], "postag": ["UH", ",", "DT", "NN", "VBD", "JJ", "NN", ",", "RBR", "JJ", "IN", "DT", "JJ", "NN", ",", "CC", "VBD", "RB", "NN", "NN", "IN", "NN", "."], "head": [7, 7, 4, 7, 7, 7, 0, 10, 10, 7, 14, 14, 14, 10, 20, 20, 20, 20, 20, 7, 22, 20, 7], "deprel": ["discourse", "punct", "det", "nsubj", "cop", "amod", "root", "punct", "advmod", "conj", "case", "det", "amod", "obl", "punct", "cc", "cop", "advmod", "compound", "conj", "case", "nmod", "punct"]}, {"id": "12", "sentence": "Keyboard is great , very quiet for all the typing that I do .", "triples": [{"uid": "12-0", "sentiment": "positive", "target_tags": "Keyboard\\B is\\O great\\O ,\\O very\\O quiet\\O for\\O all\\O the\\O typing\\O that\\O I\\O do\\O .\\O", "opinion_tags": "Keyboard\\O is\\O great\\B ,\\O very\\O quiet\\O for\\O all\\O the\\O typing\\O that\\O I\\O do\\O .\\O"}, {"uid": "12-1", "sentiment": "positive", "target_tags": "Keyboard\\B is\\O great\\O ,\\O very\\O quiet\\O for\\O all\\O the\\O typing\\O that\\O I\\O do\\O .\\O", "opinion_tags": "Keyboard\\O is\\O great\\O ,\\O very\\O quiet\\B for\\O all\\O the\\O typing\\O that\\O I\\O do\\O .\\O"}], "postag": ["NN", "VBZ", "JJ", ",", "RB", "JJ", "IN", "PDT", "DT", "NN", "WDT", "PRP", "VBP", "."], "head": [3, 3, 0, 6, 6, 3, 10, 10, 10, 3, 13, 13, 10, 3], "deprel": ["nsubj", "cop", "root", "punct", "advmod", "conj", "case", "det:predet", "det", "obl", "obj", "nsubj", "acl:relcl", "punct"]}, {"id": "13", "sentence": "The keyboard feels good and I type just fine on it .", "triples": [{"uid": "13-0", "sentiment": "positive", "target_tags": "The\\O keyboard\\B feels\\O good\\O and\\O I\\O type\\O just\\O fine\\O on\\O it\\O .\\O", "opinion_tags": "The\\O keyboard\\O feels\\O good\\B and\\O I\\O type\\O just\\O fine\\O on\\O it\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "PRP", "VBP", "RB", "JJ", "IN", "PRP", "."], "head": [2, 3, 0, 3, 7, 7, 3, 9, 7, 11, 7, 3], "deprel": ["det", "nsubj", "root", "xcomp", "cc", "nsubj", "conj", "advmod", "obj", "case", "obl", "punct"]}, {"id": "14", "sentence": "I thought the white Mac computers looked dirty too quicly where you use the mousepad and where you place your hands when typing .", "triples": [{"uid": "14-0", "sentiment": "neutral", "target_tags": "I\\O thought\\O the\\O white\\O Mac\\O computers\\O looked\\O dirty\\O too\\O quicly\\O where\\O you\\O use\\O the\\O mousepad\\B and\\O where\\O you\\O place\\O your\\O hands\\O when\\O typing\\O .\\O", "opinion_tags": "I\\O thought\\O the\\O white\\O Mac\\O computers\\O looked\\O dirty\\B too\\O quicly\\O where\\O you\\O use\\O the\\O mousepad\\O and\\O where\\O you\\O place\\O your\\O hands\\O when\\O typing\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "JJ", "NNP", "NNS", "VBD", "JJ", "RB", "RB", "WRB", "PRP", "VBP", "DT", "NN", "CC", "WRB", "PRP", "VBP", "PRP$", "NNS", "WRB", "VBG", "."], "head": [2, 0, 6, 6, 6, 7, 2, 7, 10, 7, 13, 13, 7, 15, 13, 19, 19, 19, 13, 21, 19, 23, 19, 2], "deprel": ["nsubj", "root", "det", "amod", "compound", "nsubj", "ccomp", "xcomp", "advmod", "advmod", "mark", "nsubj", "advcl", "det", "obj", "cc", "mark", "nsubj", "conj", "nmod:poss", "obj", "mark", "advcl", "punct"]}, {"id": "15", "sentence": "My favorite part of this computer is that it has a vga port so I can connect it to a bigger screen .", "triples": [{"uid": "15-0", "sentiment": "positive", "target_tags": "My\\O favorite\\O part\\O of\\O this\\O computer\\O is\\O that\\O it\\O has\\O a\\O vga\\B port\\I so\\O I\\O can\\O connect\\O it\\O to\\O a\\O bigger\\O screen\\O .\\O", "opinion_tags": "My\\O favorite\\B part\\O of\\O this\\O computer\\O is\\O that\\O it\\O has\\O a\\O vga\\O port\\O so\\O I\\O can\\O connect\\O it\\O to\\O a\\O bigger\\O screen\\O .\\O"}, {"uid": "15-1", "sentiment": "neutral", "target_tags": "My\\O favorite\\O part\\O of\\O this\\O computer\\O is\\O that\\O it\\O has\\O a\\O vga\\O port\\O so\\O I\\O can\\O connect\\O it\\O to\\O a\\O bigger\\O screen\\B .\\O", "opinion_tags": "My\\O favorite\\O part\\O of\\O this\\O computer\\O is\\O that\\O it\\O has\\O a\\O vga\\O port\\O so\\O I\\O can\\O connect\\O it\\O to\\O a\\O bigger\\B screen\\O .\\O"}], "postag": ["PRP$", "JJ", "NN", "IN", "DT", "NN", "VBZ", "IN", "PRP", "VBZ", "DT", "NN", "NN", "RB", "PRP", "MD", "VB", "PRP", "IN", "DT", "JJR", "NN", "."], "head": [3, 3, 7, 6, 6, 3, 0, 10, 10, 7, 13, 13, 10, 17, 17, 17, 10, 17, 22, 22, 22, 17, 7], "deprel": ["nmod:poss", "amod", "nsubj", "case", "det", "nmod", "root", "mark", "nsubj", "ccomp", "det", "compound", "obj", "advmod", "nsubj", "aux", "advcl", "obj", "case", "det", "amod", "obl", "punct"]}, {"id": "16", "sentence": "Granted , it 's still a very new laptop but in comparison to my previous laptops and desktops , my Mac boots up noticeably quicker .", "triples": [{"uid": "16-0", "sentiment": "positive", "target_tags": "Granted\\O ,\\O it\\O 's\\O still\\O a\\O very\\O new\\O laptop\\O but\\O in\\O comparison\\O to\\O my\\O previous\\O laptops\\O and\\O desktops\\O ,\\O my\\O Mac\\O boots\\B up\\I noticeably\\O quicker\\O .\\O", "opinion_tags": "Granted\\O ,\\O it\\O 's\\O still\\O a\\O very\\O new\\O laptop\\O but\\O in\\O comparison\\O to\\O my\\O previous\\O laptops\\O and\\O desktops\\O ,\\O my\\O Mac\\O boots\\O up\\O noticeably\\O quicker\\B .\\O"}], "postag": ["VBN", ",", "PRP", "VBZ", "RB", "DT", "RB", "JJ", "NN", "CC", "IN", "NN", "IN", "PRP$", "JJ", "NNS", "CC", "NNS", ",", "PRP$", "NNP", "VBZ", "RP", "RB", "JJR", "."], "head": [9, 9, 9, 9, 9, 9, 8, 9, 0, 22, 12, 22, 16, 16, 16, 12, 18, 16, 22, 21, 22, 9, 22, 25, 22, 9], "deprel": ["advcl", "punct", "nsubj", "cop", "advmod", "det", "advmod", "amod", "root", "cc", "case", "obl", "case", "nmod:poss", "amod", "nmod", "cc", "conj", "punct", "nmod:poss", "nsubj", "conj", "compound:prt", "advmod", "advmod", "punct"]}, {"id": "17", "sentence": "It caught a virus that completely wiped out my hard drive in a matter of hours .", "triples": [{"uid": "17-0", "sentiment": "negative", "target_tags": "It\\O caught\\O a\\O virus\\O that\\O completely\\O wiped\\O out\\O my\\O hard\\B drive\\I in\\O a\\O matter\\O of\\O hours\\O .\\O", "opinion_tags": "It\\O caught\\O a\\O virus\\O that\\O completely\\O wiped\\B out\\I my\\O hard\\O drive\\O in\\O a\\O matter\\O of\\O hours\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "NN", "WDT", "RB", "VBD", "RP", "PRP$", "JJ", "NN", "IN", "DT", "NN", "IN", "NNS", "."], "head": [2, 0, 4, 2, 7, 7, 4, 7, 11, 11, 7, 14, 14, 7, 16, 14, 2], "deprel": ["nsubj", "root", "det", "obj", "nsubj", "advmod", "acl:relcl", "compound:prt", "nmod:poss", "amod", "obj", "case", "det", "obl", "case", "nmod", "punct"]}, {"id": "18", "sentence": "The AMD Turin processor seems to always perform so much better than Intel .", "triples": [{"uid": "18-0", "sentiment": "positive", "target_tags": "The\\O AMD\\B Turin\\I processor\\I seems\\O to\\O always\\O perform\\O so\\O much\\O better\\O than\\O Intel\\O .\\O", "opinion_tags": "The\\O AMD\\O Turin\\O processor\\O seems\\O to\\O always\\O perform\\O so\\O much\\O better\\B than\\O Intel\\O .\\O"}], "postag": ["DT", "NNP", "NNP", "NN", "VBZ", "TO", "RB", "VB", "RB", "RB", "JJR", "IN", "NNP", "."], "head": [4, 4, 4, 5, 0, 8, 8, 5, 10, 11, 8, 13, 11, 5], "deprel": ["det", "compound", "compound", "nsubj", "root", "mark", "advmod", "xcomp", "advmod", "advmod", "obj", "case", "obl", "punct"]}, {"id": "19", "sentence": "High price tag , however .", "triples": [{"uid": "19-0", "sentiment": "negative", "target_tags": "High\\O price\\B tag\\I ,\\O however\\O .\\O", "opinion_tags": "High\\B price\\O tag\\O ,\\O however\\O .\\O"}], "postag": ["JJ", "NN", "NN", ",", "RB", "."], "head": [2, 3, 0, 3, 3, 3], "deprel": ["amod", "compound", "root", "punct", "advmod", "punct"]}, {"id": "20", "sentence": "I custom ordered the machine from HP and could NOT understand the techie due to his accent .", "triples": [{"uid": "20-0", "sentiment": "negative", "target_tags": "I\\O custom\\O ordered\\O the\\O machine\\O from\\O HP\\O and\\O could\\O NOT\\O understand\\O the\\O techie\\B due\\O to\\O his\\O accent\\O .\\O", "opinion_tags": "I\\O custom\\O ordered\\O the\\O machine\\O from\\O HP\\O and\\O could\\O NOT\\B understand\\I the\\O techie\\O due\\O to\\O his\\O accent\\O .\\O"}], "postag": ["PRP", "RB", "VBD", "DT", "NN", "IN", "NNP", "CC", "MD", "RB", "VB", "DT", "NN", "IN", "IN", "PRP$", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 3, 11, 11, 11, 3, 13, 11, 17, 14, 17, 11, 3], "deprel": ["nsubj", "advmod", "root", "det", "obj", "case", "obl", "cc", "aux", "advmod", "conj", "det", "obj", "case", "fixed", "nmod:poss", "obl", "punct"]}, {"id": "21", "sentence": "For the not so good , I got the stock screen - which is VERY glossy .", "triples": [{"uid": "21-0", "sentiment": "negative", "target_tags": "For\\O the\\O not\\O so\\O good\\O ,\\O I\\O got\\O the\\O stock\\B screen\\I -\\O which\\O is\\O VERY\\O glossy\\O .\\O", "opinion_tags": "For\\O the\\O not\\O so\\O good\\O ,\\O I\\O got\\O the\\O stock\\O screen\\O -\\O which\\O is\\O VERY\\O glossy\\B .\\O"}], "postag": ["IN", "DT", "RB", "RB", "JJ", ",", "PRP", "VBD", "DT", "NN", "NN", ",", "WDT", "VBZ", "RB", "JJ", "."], "head": [5, 5, 5, 5, 8, 8, 8, 0, 11, 11, 8, 11, 16, 16, 16, 11, 8], "deprel": ["case", "det", "advmod", "advmod", "obl", "punct", "nsubj", "root", "det", "compound", "obj", "punct", "nsubj", "cop", "advmod", "acl:relcl", "punct"]}, {"id": "22", "sentence": "The gray color was a good choice .", "triples": [{"uid": "22-0", "sentiment": "positive", "target_tags": "The\\O gray\\B color\\I was\\O a\\O good\\O choice\\O .\\O", "opinion_tags": "The\\O gray\\O color\\O was\\O a\\O good\\B choice\\O .\\O"}], "postag": ["DT", "JJ", "NN", "VBD", "DT", "JJ", "NN", "."], "head": [3, 3, 7, 7, 7, 7, 0, 7], "deprel": ["det", "amod", "nsubj", "cop", "det", "amod", "root", "punct"]}, {"id": "23", "sentence": "I would like to have volume buttons rather than the adjustment that is on the front .", "triples": [{"uid": "23-0", "sentiment": "negative", "target_tags": "I\\O would\\O like\\O to\\O have\\O volume\\B buttons\\I rather\\O than\\O the\\O adjustment\\O that\\O is\\O on\\O the\\O front\\O .\\O", "opinion_tags": "I\\O would\\O like\\B to\\O have\\O volume\\O buttons\\O rather\\O than\\O the\\O adjustment\\O that\\O is\\O on\\O the\\O front\\O .\\O"}], "postag": ["PRP", "MD", "VB", "TO", "VB", "NN", "NNS", "RB", "IN", "DT", "NN", "WDT", "VBZ", "IN", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 5, 11, 8, 11, 7, 16, 16, 16, 16, 11, 3], "deprel": ["nsubj", "aux", "root", "mark", "xcomp", "compound", "obj", "cc", "fixed", "det", "conj", "nsubj", "cop", "case", "det", "acl:relcl", "punct"]}, {"id": "24", "sentence": "The processor a AMD Semprom at 2.1 ghz is a bummer it does not have the power for HD or heavy computing .", "triples": [{"uid": "24-0", "sentiment": "negative", "target_tags": "The\\O processor\\B a\\O AMD\\O Semprom\\O at\\O 2.1\\O ghz\\O is\\O a\\O bummer\\O it\\O does\\O not\\O have\\O the\\O power\\O for\\O HD\\O or\\O heavy\\O computing\\O .\\O", "opinion_tags": "The\\O processor\\O a\\O AMD\\O Semprom\\O at\\O 2.1\\O ghz\\O is\\O a\\O bummer\\B it\\O does\\O not\\O have\\O the\\O power\\O for\\O HD\\O or\\O heavy\\O computing\\O .\\O"}, {"uid": "24-1", "sentiment": "negative", "target_tags": "The\\O processor\\O a\\O AMD\\O Semprom\\O at\\O 2.1\\O ghz\\O is\\O a\\O bummer\\O it\\O does\\O not\\O have\\O the\\O power\\O for\\O HD\\O or\\O heavy\\O computing\\B .\\O", "opinion_tags": "The\\O processor\\O a\\O AMD\\O Semprom\\O at\\O 2.1\\O ghz\\O is\\O a\\O bummer\\O it\\O does\\O not\\O have\\O the\\O power\\O for\\O HD\\O or\\O heavy\\B computing\\O .\\O"}], "postag": ["DT", "NN", "DT", "NNP", "NNP", "IN", "CD", "NN", "VBZ", "DT", "NN", "PRP", "VBZ", "RB", "VB", "DT", "NN", "IN", "NN", "CC", "JJ", "NN", "."], "head": [2, 11, 5, 5, 2, 8, 8, 5, 11, 11, 0, 15, 15, 15, 11, 17, 15, 19, 17, 22, 22, 19, 11], "deprel": ["det", "nsubj", "det", "compound", "appos", "case", "nummod", "nmod", "cop", "det", "root", "nsubj", "aux", "advmod", "acl:relcl", "det", "obj", "case", "nmod", "cc", "amod", "conj", "punct"]}, {"id": "25", "sentence": "It is easy to use , fast and has great graphics for the money .", "triples": [{"uid": "25-0", "sentiment": "positive", "target_tags": "It\\O is\\O easy\\O to\\O use\\O ,\\O fast\\O and\\O has\\O great\\O graphics\\B for\\O the\\O money\\O .\\O", "opinion_tags": "It\\O is\\O easy\\O to\\O use\\O ,\\O fast\\O and\\O has\\O great\\B graphics\\O for\\O the\\O money\\O .\\O"}, {"uid": "25-1", "sentiment": "positive", "target_tags": "It\\O is\\O easy\\O to\\O use\\B ,\\O fast\\O and\\O has\\O great\\O graphics\\O for\\O the\\O money\\O .\\O", "opinion_tags": "It\\O is\\O easy\\B to\\O use\\O ,\\O fast\\O and\\O has\\O great\\O graphics\\O for\\O the\\O money\\O .\\O"}], "postag": ["PRP", "VBZ", "JJ", "TO", "VB", ",", "JJ", "CC", "VBZ", "JJ", "NNS", "IN", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 5, 9, 3, 11, 9, 14, 14, 11, 3], "deprel": ["expl", "cop", "root", "mark", "csubj", "punct", "xcomp", "cc", "conj", "amod", "obj", "case", "det", "nmod", "punct"]}, {"id": "26", "sentence": "I like how the Mac OS is so simple and easy to use .", "triples": [{"uid": "26-0", "sentiment": "positive", "target_tags": "I\\O like\\O how\\O the\\O Mac\\B OS\\I is\\O so\\O simple\\O and\\O easy\\O to\\O use\\O .\\O", "opinion_tags": "I\\O like\\B how\\O the\\O Mac\\O OS\\O is\\O so\\O simple\\O and\\O easy\\O to\\O use\\O .\\O"}, {"uid": "26-1", "sentiment": "positive", "target_tags": "I\\O like\\O how\\O the\\O Mac\\O OS\\O is\\O so\\O simple\\O and\\O easy\\O to\\O use\\B .\\O", "opinion_tags": "I\\O like\\O how\\O the\\O Mac\\O OS\\O is\\O so\\O simple\\O and\\O easy\\B to\\O use\\O .\\O"}], "postag": ["PRP", "VBP", "WRB", "DT", "NNP", "NNP", "VBZ", "RB", "JJ", "CC", "JJ", "TO", "VB", "."], "head": [2, 0, 9, 6, 6, 9, 9, 9, 2, 11, 9, 13, 9, 2], "deprel": ["nsubj", "root", "mark", "det", "compound", "nsubj", "cop", "advmod", "ccomp", "cc", "conj", "mark", "advcl", "punct"]}, {"id": "27", "sentence": "Obviously one of the most important features of any computer is the `` human interface .", "triples": [{"uid": "27-0", "sentiment": "neutral", "target_tags": "Obviously\\O one\\O of\\O the\\O most\\O important\\O features\\B of\\O any\\O computer\\O is\\O the\\O ``\\O human\\O interface\\O .\\O", "opinion_tags": "Obviously\\O one\\O of\\O the\\O most\\B important\\I features\\O of\\O any\\O computer\\O is\\O the\\O ``\\O human\\O interface\\O .\\O"}, {"uid": "27-1", "sentiment": "neutral", "target_tags": "Obviously\\O one\\O of\\O the\\O most\\O important\\O features\\O of\\O any\\O computer\\O is\\O the\\O ``\\O human\\B interface\\I .\\O", "opinion_tags": "Obviously\\O one\\O of\\O the\\O most\\B important\\I features\\O of\\O any\\O computer\\O is\\O the\\O ``\\O human\\O interface\\O .\\O"}], "postag": ["RB", "CD", "IN", "DT", "RBS", "JJ", "NNS", "IN", "DT", "NN", "VBZ", "DT", "``", "JJ", "NN", "."], "head": [15, 15, 7, 7, 6, 7, 2, 10, 10, 7, 15, 15, 15, 15, 0, 15], "deprel": ["advmod", "nsubj", "case", "det", "advmod", "amod", "nmod", "case", "det", "nmod", "cop", "det", "punct", "amod", "root", "punct"]}, {"id": "28", "sentence": "A great feature is the spotlight search : one can search for documents by simply typing a keyword , rather than parsing tens of file folders for a document .", "triples": [{"uid": "28-0", "sentiment": "positive", "target_tags": "A\\O great\\O feature\\O is\\O the\\O spotlight\\B search\\I :\\O one\\O can\\O search\\O for\\O documents\\O by\\O simply\\O typing\\O a\\O keyword\\O ,\\O rather\\O than\\O parsing\\O tens\\O of\\O file\\O folders\\O for\\O a\\O document\\O .\\O", "opinion_tags": "A\\O great\\B feature\\O is\\O the\\O spotlight\\O search\\O :\\O one\\O can\\O search\\O for\\O documents\\O by\\O simply\\O typing\\O a\\O keyword\\O ,\\O rather\\O than\\O parsing\\O tens\\O of\\O file\\O folders\\O for\\O a\\O document\\O .\\O"}, {"uid": "28-1", "sentiment": "positive", "target_tags": "A\\O great\\O feature\\B is\\O the\\O spotlight\\O search\\O :\\O one\\O can\\O search\\O for\\O documents\\O by\\O simply\\O typing\\O a\\O keyword\\O ,\\O rather\\O than\\O parsing\\O tens\\O of\\O file\\O folders\\O for\\O a\\O document\\O .\\O", "opinion_tags": "A\\O great\\B feature\\O is\\O the\\O spotlight\\O search\\O :\\O one\\O can\\O search\\O for\\O documents\\O by\\O simply\\O typing\\O a\\O keyword\\O ,\\O rather\\O than\\O parsing\\O tens\\O of\\O file\\O folders\\O for\\O a\\O document\\O .\\O"}], "postag": ["DT", "JJ", "NN", "VBZ", "DT", "NN", "NN", ":", "PRP", "MD", "VB", "IN", "NNS", "IN", "RB", "VBG", "DT", "NN", ",", "RB", "IN", "VBG", "NNS", "IN", "NN", "NNS", "IN", "DT", "NN", "."], "head": [3, 3, 7, 7, 7, 7, 0, 7, 11, 11, 7, 13, 11, 16, 16, 11, 18, 16, 22, 22, 20, 16, 22, 26, 26, 23, 29, 29, 22, 7], "deprel": ["det", "amod", "nsubj", "cop", "det", "compound", "root", "punct", "nsubj", "aux", "parataxis", "case", "obl", "mark", "advmod", "advcl", "det", "obj", "punct", "cc", "fixed", "conj", "obj", "case", "compound", "nmod", "case", "det", "obl", "punct"]}, {"id": "29", "sentence": "I 've had to call Apple support to set up my new printer and have had wonderful experiences with helpful , english speaking ( from Vancouver ) techs that walked me through the processes to help me .", "triples": [{"uid": "29-0", "sentiment": "positive", "target_tags": "I\\O 've\\O had\\O to\\O call\\O Apple\\B support\\I to\\O set\\O up\\O my\\O new\\O printer\\O and\\O have\\O had\\O wonderful\\O experiences\\O with\\O helpful\\O ,\\O english\\O speaking\\O (\\O from\\O Vancouver\\O )\\O techs\\O that\\O walked\\O me\\O through\\O the\\O processes\\O to\\O help\\O me\\O .\\O", "opinion_tags": "I\\O 've\\O had\\O to\\O call\\O Apple\\O support\\O to\\O set\\O up\\O my\\O new\\O printer\\O and\\O have\\O had\\O wonderful\\B experiences\\O with\\O helpful\\O ,\\O english\\O speaking\\O (\\O from\\O Vancouver\\O )\\O techs\\O that\\O walked\\O me\\O through\\O the\\O processes\\O to\\O help\\O me\\O .\\O"}, {"uid": "29-1", "sentiment": "positive", "target_tags": "I\\O 've\\O had\\O to\\O call\\O Apple\\O support\\O to\\O set\\O up\\O my\\O new\\O printer\\O and\\O have\\O had\\O wonderful\\O experiences\\O with\\O helpful\\O ,\\O english\\O speaking\\O (\\O from\\O Vancouver\\O )\\O techs\\B that\\O walked\\O me\\O through\\O the\\O processes\\O to\\O help\\O me\\O .\\O", "opinion_tags": "I\\O 've\\O had\\O to\\O call\\O Apple\\O support\\O to\\O set\\O up\\O my\\O new\\O printer\\O and\\O have\\O had\\O wonderful\\O experiences\\O with\\O helpful\\B ,\\O english\\O speaking\\O (\\O from\\O Vancouver\\O )\\O techs\\O that\\O walked\\O me\\O through\\O the\\O processes\\O to\\O help\\O me\\O .\\O"}], "postag": ["PRP", "VBP", "VBN", "TO", "VB", "NNP", "NN", "TO", "VB", "RP", "PRP$", "JJ", "NN", "CC", "VBP", "VBN", "JJ", "NNS", "IN", "JJ", ",", "JJ", "NN", "-LRB-", "IN", "NNP", "-RRB-", "NNS", "WDT", "VBD", "PRP", "IN", "DT", "NNS", "TO", "VB", "PRP", "."], "head": [3, 3, 0, 5, 3, 7, 5, 9, 5, 9, 13, 13, 9, 16, 16, 3, 18, 16, 23, 23, 23, 23, 18, 26, 26, 23, 26, 16, 30, 28, 30, 34, 34, 30, 36, 30, 36, 3], "deprel": ["nsubj", "aux", "root", "mark", "xcomp", "compound", "obj", "mark", "advcl", "compound:prt", "nmod:poss", "amod", "obj", "cc", "aux", "conj", "amod", "obj", "case", "amod", "punct", "amod", "nmod", "punct", "case", "nmod", "punct", "obl", "nsubj", "acl:relcl", "obj", "case", "det", "obl", "mark", "advcl", "obj", "punct"]}, {"id": "30", "sentence": "I need graphic power to run my Adobe Creative apps efficiently .", "triples": [{"uid": "30-0", "sentiment": "neutral", "target_tags": "I\\O need\\O graphic\\B power\\I to\\O run\\O my\\O Adobe\\O Creative\\O apps\\O efficiently\\O .\\O", "opinion_tags": "I\\O need\\O graphic\\O power\\O to\\O run\\O my\\O Adobe\\O Creative\\O apps\\O efficiently\\B .\\O"}, {"uid": "30-1", "sentiment": "neutral", "target_tags": "I\\O need\\O graphic\\O power\\O to\\O run\\O my\\O Adobe\\B Creative\\I apps\\I efficiently\\O .\\O", "opinion_tags": "I\\O need\\O graphic\\O power\\O to\\O run\\O my\\O Adobe\\O Creative\\O apps\\O efficiently\\B .\\O"}], "postag": ["PRP", "VBP", "JJ", "NN", "TO", "VB", "PRP$", "NNP", "JJ", "NNS", "RB", "."], "head": [2, 0, 4, 2, 6, 4, 10, 10, 10, 6, 6, 2], "deprel": ["nsubj", "root", "amod", "obj", "mark", "acl", "nmod:poss", "compound", "amod", "obj", "advmod", "punct"]}, {"id": "31", "sentence": "upon giving them the serial number the first thing I was told , was that it was out of warranty and I could pay to have it repaired .", "triples": [{"uid": "31-0", "sentiment": "neutral", "target_tags": "upon\\O giving\\O them\\O the\\O serial\\O number\\O the\\O first\\O thing\\O I\\O was\\O told\\O ,\\O was\\O that\\O it\\O was\\O out\\O of\\O warranty\\B and\\O I\\O could\\O pay\\O to\\O have\\O it\\O repaired\\O .\\O", "opinion_tags": "upon\\O giving\\O them\\O the\\O serial\\O number\\O the\\O first\\O thing\\O I\\O was\\O told\\O ,\\O was\\O that\\O it\\O was\\O out\\B of\\I warranty\\O and\\O I\\O could\\O pay\\O to\\O have\\O it\\O repaired\\O .\\O"}], "postag": ["IN", "VBG", "PRP", "DT", "NN", "NN", "DT", "JJ", "NN", "PRP", "VBD", "VBN", ",", "VBD", "IN", "PRP", "VBD", "IN", "IN", "NN", "CC", "PRP", "MD", "VB", "TO", "VB", "PRP", "VBN", "."], "head": [2, 14, 2, 6, 6, 2, 9, 9, 14, 12, 12, 9, 2, 0, 20, 20, 20, 20, 20, 14, 24, 24, 24, 20, 26, 24, 26, 26, 14], "deprel": ["mark", "advcl", "i<PERSON><PERSON>", "det", "compound", "obj", "det", "amod", "nsubj", "nsubj:pass", "aux:pass", "acl:relcl", "punct", "root", "mark", "nsubj", "cop", "case", "case", "ccomp", "cc", "nsubj", "aux", "conj", "mark", "xcomp", "obj", "xcomp", "punct"]}, {"id": "32", "sentence": "Laptop was in new condition and operational , but for the audio problem when 1st sent for repair .", "triples": [{"uid": "32-0", "sentiment": "negative", "target_tags": "Laptop\\O was\\O in\\O new\\O condition\\O and\\O operational\\O ,\\O but\\O for\\O the\\O audio\\B problem\\O when\\O 1st\\O sent\\O for\\O repair\\O .\\O", "opinion_tags": "Laptop\\O was\\O in\\O new\\O condition\\O and\\O operational\\O ,\\O but\\O for\\O the\\O audio\\O problem\\B when\\O 1st\\O sent\\O for\\O repair\\O .\\O"}], "postag": ["NNP", "VBD", "IN", "JJ", "NN", "CC", "JJ", ",", "CC", "IN", "DT", "NN", "NN", "WRB", "RB", "VBD", "IN", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 13, 13, 13, 13, 13, 5, 16, 16, 13, 18, 16, 5], "deprel": ["nsubj", "cop", "case", "amod", "root", "cc", "conj", "punct", "cc", "case", "det", "compound", "conj", "mark", "advmod", "acl", "case", "obl", "punct"]}, {"id": "33", "sentence": "I was disappointed when I realized that the keyboard does n't light up on this model .", "triples": [{"uid": "33-0", "sentiment": "negative", "target_tags": "I\\O was\\O disappointed\\O when\\O I\\O realized\\O that\\O the\\O keyboard\\B does\\O n't\\O light\\O up\\O on\\O this\\O model\\O .\\O", "opinion_tags": "I\\O was\\O disappointed\\B when\\O I\\O realized\\O that\\O the\\O keyboard\\O does\\O n't\\O light\\O up\\O on\\O this\\O model\\O .\\O"}, {"uid": "33-1", "sentiment": "negative", "target_tags": "I\\O was\\O disappointed\\O when\\O I\\O realized\\O that\\O the\\O keyboard\\B does\\O n't\\O light\\O up\\O on\\O this\\O model\\O .\\O", "opinion_tags": "I\\O was\\O disappointed\\O when\\O I\\O realized\\O that\\O the\\O keyboard\\O does\\B n't\\I light\\I up\\I on\\O this\\O model\\O .\\O"}], "postag": ["PRP", "VBD", "JJ", "WRB", "PRP", "VBD", "IN", "DT", "NN", "VBZ", "RB", "VB", "RP", "IN", "DT", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 12, 9, 12, 12, 12, 6, 12, 16, 16, 12, 3], "deprel": ["nsubj", "cop", "root", "mark", "nsubj", "advcl", "mark", "det", "nsubj", "aux", "advmod", "ccomp", "compound:prt", "case", "det", "obl", "punct"]}, {"id": "34", "sentence": "It runs perfectly .", "triples": [{"uid": "34-0", "sentiment": "positive", "target_tags": "It\\O runs\\B perfectly\\O .\\O", "opinion_tags": "It\\O runs\\O perfectly\\B .\\O"}], "postag": ["PRP", "VBZ", "RB", "."], "head": [2, 0, 2, 2], "deprel": ["nsubj", "root", "advmod", "punct"]}, {"id": "35", "sentence": "Sometimes the screen even goes black on this computer .", "triples": [{"uid": "35-0", "sentiment": "negative", "target_tags": "Sometimes\\O the\\O screen\\B even\\O goes\\O black\\O on\\O this\\O computer\\O .\\O", "opinion_tags": "Sometimes\\O the\\O screen\\O even\\O goes\\O black\\B on\\O this\\O computer\\O .\\O"}], "postag": ["RB", "DT", "NN", "RB", "VBZ", "JJ", "IN", "DT", "NN", "."], "head": [5, 3, 5, 5, 0, 5, 9, 9, 5, 5], "deprel": ["advmod", "det", "nsubj", "advmod", "root", "xcomp", "case", "det", "obl", "punct"]}, {"id": "36", "sentence": "Its fast and another thing I like is that it has three USB ports .", "triples": [{"uid": "36-0", "sentiment": "positive", "target_tags": "Its\\O fast\\O and\\O another\\O thing\\O I\\O like\\O is\\O that\\O it\\O has\\O three\\O USB\\B ports\\I .\\O", "opinion_tags": "Its\\O fast\\O and\\O another\\O thing\\O I\\O like\\B is\\O that\\O it\\O has\\O three\\O USB\\O ports\\O .\\O"}], "postag": ["PRP$", "JJ", "CC", "DT", "NN", "PRP", "VBP", "VBZ", "IN", "PRP", "VBZ", "CD", "NNP", "NNS", "."], "head": [5, 5, 5, 5, 8, 7, 5, 0, 11, 11, 8, 14, 14, 11, 8], "deprel": ["nmod:poss", "amod", "cc", "det", "nsubj", "nsubj", "acl:relcl", "root", "mark", "nsubj", "ccomp", "nummod", "compound", "obj", "punct"]}, {"id": "37", "sentence": "The salesman talked us into this computer away from another we were looking at and we have had nothing but problems with software problems and just not happy with it .", "triples": [{"uid": "37-0", "sentiment": "negative", "target_tags": "The\\O salesman\\O talked\\O us\\O into\\O this\\O computer\\O away\\O from\\O another\\O we\\O were\\O looking\\O at\\O and\\O we\\O have\\O had\\O nothing\\O but\\O problems\\O with\\O software\\B problems\\O and\\O just\\O not\\O happy\\O with\\O it\\O .\\O", "opinion_tags": "The\\O salesman\\O talked\\O us\\O into\\O this\\O computer\\O away\\O from\\O another\\O we\\O were\\O looking\\O at\\O and\\O we\\O have\\O had\\O nothing\\O but\\O problems\\O with\\O software\\O problems\\B and\\O just\\O not\\O happy\\O with\\O it\\O .\\O"}, {"uid": "37-1", "sentiment": "negative", "target_tags": "The\\O salesman\\O talked\\O us\\O into\\O this\\O computer\\O away\\O from\\O another\\O we\\O were\\O looking\\O at\\O and\\O we\\O have\\O had\\O nothing\\O but\\O problems\\O with\\O software\\B problems\\O and\\O just\\O not\\O happy\\O with\\O it\\O .\\O", "opinion_tags": "The\\O salesman\\O talked\\O us\\O into\\O this\\O computer\\O away\\O from\\O another\\O we\\O were\\O looking\\O at\\O and\\O we\\O have\\O had\\O nothing\\O but\\O problems\\O with\\O software\\O problems\\O and\\O just\\O not\\B happy\\I with\\O it\\O .\\O"}], "postag": ["DT", "NN", "VBD", "PRP", "IN", "DT", "NN", "RB", "IN", "DT", "PRP", "VBD", "VBG", "IN", "CC", "PRP", "VBP", "VBN", "NN", "CC", "NNS", "IN", "NN", "NNS", "CC", "RB", "RB", "JJ", "IN", "PRP", "."], "head": [2, 3, 0, 3, 7, 7, 3, 3, 10, 3, 13, 13, 10, 13, 18, 18, 18, 3, 18, 21, 19, 24, 24, 21, 28, 28, 28, 18, 30, 28, 3], "deprel": ["det", "nsubj", "root", "obj", "case", "det", "obl", "advmod", "case", "obl", "nsubj", "aux", "acl:relcl", "obl", "cc", "nsubj", "aux", "conj", "obj", "case", "nmod", "case", "compound", "nmod", "cc", "advmod", "advmod", "conj", "case", "obl", "punct"]}, {"id": "38", "sentence": "That system is fixed .", "triples": [{"uid": "38-0", "sentiment": "neutral", "target_tags": "That\\O system\\B is\\O fixed\\O .\\O", "opinion_tags": "That\\O system\\O is\\O fixed\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "VBN", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj:pass", "aux:pass", "root", "punct"]}, {"id": "39", "sentence": "I would not recommend this to anyone wanting a notebook expecting the performance of a Desktop it does not meet the expectations .", "triples": [{"uid": "39-0", "sentiment": "negative", "target_tags": "I\\O would\\O not\\O recommend\\O this\\O to\\O anyone\\O wanting\\O a\\O notebook\\O expecting\\O the\\O performance\\B of\\O a\\O Desktop\\O it\\O does\\O not\\O meet\\O the\\O expectations\\O .\\O", "opinion_tags": "I\\O would\\O not\\B recommend\\I this\\O to\\O anyone\\O wanting\\O a\\O notebook\\O expecting\\O the\\O performance\\O of\\O a\\O Desktop\\O it\\O does\\O not\\O meet\\O the\\O expectations\\O .\\O"}], "postag": ["PRP", "MD", "RB", "VB", "DT", "IN", "NN", "VBG", "DT", "NN", "VBG", "DT", "NN", "IN", "DT", "NN", "PRP", "VBZ", "RB", "VB", "DT", "NNS", "."], "head": [4, 4, 4, 0, 4, 7, 4, 7, 10, 8, 8, 13, 11, 16, 16, 13, 20, 20, 20, 16, 22, 20, 4], "deprel": ["nsubj", "aux", "advmod", "root", "obj", "case", "obl", "acl", "det", "obj", "xcomp", "det", "obj", "case", "det", "nmod", "nsubj", "aux", "advmod", "acl:relcl", "det", "obj", "punct"]}, {"id": "40", "sentence": "The Macbook arrived in a nice twin packing and sealed in the box , all the functions works great .", "triples": [{"uid": "40-0", "sentiment": "positive", "target_tags": "The\\O Macbook\\O arrived\\O in\\O a\\O nice\\O twin\\B packing\\I and\\O sealed\\O in\\O the\\O box\\O ,\\O all\\O the\\O functions\\O works\\O great\\O .\\O", "opinion_tags": "The\\O Macbook\\O arrived\\O in\\O a\\O nice\\B twin\\O packing\\O and\\O sealed\\O in\\O the\\O box\\O ,\\O all\\O the\\O functions\\O works\\O great\\O .\\O"}, {"uid": "40-1", "sentiment": "positive", "target_tags": "The\\O Macbook\\O arrived\\O in\\O a\\O nice\\O twin\\O packing\\O and\\O sealed\\O in\\O the\\O box\\O ,\\O all\\O the\\O functions\\B works\\O great\\O .\\O", "opinion_tags": "The\\O Macbook\\O arrived\\O in\\O a\\O nice\\O twin\\O packing\\O and\\O sealed\\O in\\O the\\O box\\O ,\\O all\\O the\\O functions\\O works\\O great\\B .\\O"}], "postag": ["DT", "NNP", "VBD", "IN", "DT", "JJ", "NN", "NN", "CC", "VBD", "IN", "DT", "NN", ",", "PDT", "DT", "NNS", "VBZ", "JJ", "."], "head": [2, 3, 0, 8, 8, 8, 8, 3, 10, 3, 13, 13, 10, 3, 17, 17, 18, 3, 18, 3], "deprel": ["det", "nsubj", "root", "case", "det", "amod", "compound", "obl", "cc", "conj", "case", "det", "obl", "punct", "det:predet", "det", "nsubj", "parataxis", "xcomp", "punct"]}, {"id": "41", "sentence": "It 's super fast and a great value for the price !", "triples": [{"uid": "41-0", "sentiment": "positive", "target_tags": "It\\O 's\\O super\\O fast\\O and\\O a\\O great\\O value\\B for\\O the\\O price\\O !\\O", "opinion_tags": "It\\O 's\\O super\\O fast\\O and\\O a\\O great\\B value\\O for\\O the\\O price\\O !\\O"}, {"uid": "41-1", "sentiment": "positive", "target_tags": "It\\O 's\\O super\\O fast\\O and\\O a\\O great\\O value\\O for\\O the\\O price\\B !\\O", "opinion_tags": "It\\O 's\\O super\\O fast\\O and\\O a\\O great\\B value\\O for\\O the\\O price\\O !\\O"}], "postag": ["PRP", "VBZ", "RB", "JJ", "CC", "DT", "JJ", "NN", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 11, 11, 8, 4], "deprel": ["nsubj", "cop", "advmod", "root", "cc", "det", "amod", "conj", "case", "det", "nmod", "punct"]}, {"id": "42", "sentence": "It drives me crazy when I want to download a game or something of that nature and I ca n't play it because its not compatable with the software .", "triples": [{"uid": "42-0", "sentiment": "negative", "target_tags": "It\\O drives\\O me\\O crazy\\O when\\O I\\O want\\O to\\O download\\O a\\O game\\O or\\O something\\O of\\O that\\O nature\\O and\\O I\\O ca\\O n't\\O play\\O it\\O because\\O its\\O not\\O compatable\\O with\\O the\\O software\\B .\\O", "opinion_tags": "It\\O drives\\O me\\O crazy\\O when\\O I\\O want\\O to\\O download\\O a\\O game\\O or\\O something\\O of\\O that\\O nature\\O and\\O I\\O ca\\O n't\\O play\\O it\\O because\\O its\\O not\\B compatable\\I with\\O the\\O software\\O .\\O"}], "postag": ["PRP", "VBZ", "PRP", "JJ", "WRB", "PRP", "VBP", "TO", "VB", "DT", "NN", "CC", "NN", "IN", "DT", "NN", "CC", "PRP", "MD", "RB", "VB", "PRP", "IN", "PRP$", "RB", "RB", "IN", "DT", "NN", "."], "head": [2, 0, 2, 2, 7, 7, 2, 9, 7, 11, 9, 13, 11, 16, 16, 13, 21, 21, 21, 21, 2, 21, 26, 26, 26, 21, 29, 29, 26, 2], "deprel": ["nsubj", "root", "obj", "xcomp", "mark", "nsubj", "advcl", "mark", "xcomp", "det", "obj", "cc", "conj", "case", "det", "nmod", "cc", "nsubj", "aux", "advmod", "conj", "obj", "mark", "nmod:poss", "advmod", "advcl", "case", "det", "obl", "punct"]}, {"id": "43", "sentence": "The online tutorial videos make it super easy to learn if you have always used a PC .", "triples": [{"uid": "43-0", "sentiment": "positive", "target_tags": "The\\O online\\B tutorial\\I videos\\I make\\O it\\O super\\O easy\\O to\\O learn\\O if\\O you\\O have\\O always\\O used\\O a\\O PC\\O .\\O", "opinion_tags": "The\\O online\\O tutorial\\O videos\\O make\\O it\\O super\\O easy\\B to\\O learn\\O if\\O you\\O have\\O always\\O used\\O a\\O PC\\O .\\O"}], "postag": ["DT", "JJ", "NN", "NNS", "VBP", "PRP", "RB", "JJ", "TO", "VB", "IN", "PRP", "VBP", "RB", "VBN", "DT", "NN", "."], "head": [4, 4, 4, 5, 0, 5, 8, 5, 10, 8, 15, 15, 15, 15, 10, 17, 15, 5], "deprel": ["det", "amod", "compound", "nsubj", "root", "expl", "advmod", "xcomp", "mark", "advcl", "mark", "nsubj", "aux", "advmod", "advcl", "det", "obj", "punct"]}, {"id": "44", "sentence": "It was very easy to just pick up and use -- It did not take long to get used to the Mac OS .", "triples": [{"uid": "44-0", "sentiment": "positive", "target_tags": "It\\O was\\O very\\O easy\\O to\\O just\\O pick\\O up\\O and\\O use\\O --\\O It\\O did\\O not\\O take\\O long\\O to\\O get\\O used\\O to\\O the\\O Mac\\B OS\\I .\\O", "opinion_tags": "It\\O was\\O very\\O easy\\B to\\O just\\O pick\\O up\\O and\\O use\\O --\\O It\\O did\\O not\\O take\\O long\\O to\\O get\\O used\\O to\\O the\\O Mac\\O OS\\O .\\O"}, {"uid": "44-1", "sentiment": "positive", "target_tags": "It\\O was\\O very\\O easy\\O to\\O just\\O pick\\O up\\O and\\O use\\B --\\O It\\O did\\O not\\O take\\O long\\O to\\O get\\O used\\O to\\O the\\O Mac\\O OS\\O .\\O", "opinion_tags": "It\\O was\\O very\\O easy\\B to\\O just\\O pick\\O up\\O and\\O use\\O --\\O It\\O did\\O not\\O take\\O long\\O to\\O get\\O used\\O to\\O the\\O Mac\\O OS\\O .\\O"}], "postag": ["PRP", "VBD", "RB", "JJ", "TO", "RB", "VB", "RP", "CC", "VB", ",", "PRP", "VBD", "RB", "VB", "RB", "TO", "VB", "VBN", "IN", "DT", "NNP", "NNP", "."], "head": [4, 4, 4, 0, 7, 7, 4, 7, 10, 7, 4, 15, 15, 15, 4, 15, 18, 15, 18, 23, 23, 23, 19, 4], "deprel": ["expl", "cop", "advmod", "root", "mark", "advmod", "csubj", "compound:prt", "cc", "conj", "punct", "nsubj", "aux", "advmod", "parataxis", "advmod", "mark", "advcl", "xcomp", "case", "det", "compound", "obl", "punct"]}, {"id": "45", "sentence": "Features like the font are very block-like and old school .", "triples": [{"uid": "45-0", "sentiment": "negative", "target_tags": "Features\\O like\\O the\\O font\\B are\\O very\\O block-like\\O and\\O old\\O school\\O .\\O", "opinion_tags": "Features\\O like\\O the\\O font\\O are\\O very\\O block-like\\B and\\O old\\O school\\O .\\O"}, {"uid": "45-1", "sentiment": "negative", "target_tags": "Features\\O like\\O the\\O font\\B are\\O very\\O block-like\\O and\\O old\\O school\\O .\\O", "opinion_tags": "Features\\O like\\O the\\O font\\O are\\O very\\O block-like\\O and\\O old\\B school\\O .\\O"}, {"uid": "45-2", "sentiment": "negative", "target_tags": "Features\\B like\\O the\\O font\\O are\\O very\\O block-like\\O and\\O old\\O school\\O .\\O", "opinion_tags": "Features\\O like\\O the\\O font\\O are\\O very\\O block-like\\B and\\O old\\O school\\O .\\O"}, {"uid": "45-3", "sentiment": "negative", "target_tags": "Features\\B like\\O the\\O font\\O are\\O very\\O block-like\\O and\\O old\\O school\\O .\\O", "opinion_tags": "Features\\O like\\O the\\O font\\O are\\O very\\O block-like\\O and\\O old\\B school\\O .\\O"}], "postag": ["NNS", "IN", "DT", "NN", "VBP", "RB", "JJ", "CC", "JJ", "NN", "."], "head": [0, 7, 4, 7, 7, 7, 1, 10, 10, 7, 1], "deprel": ["root", "mark", "det", "nsubj", "cop", "advmod", "advcl", "cc", "amod", "conj", "punct"]}, {"id": "46", "sentence": "It is loaded with programs that is of no good for the average user , that makes it run way to slow .", "triples": [{"uid": "46-0", "sentiment": "negative", "target_tags": "It\\O is\\O loaded\\O with\\O programs\\B that\\O is\\O of\\O no\\O good\\O for\\O the\\O average\\O user\\O ,\\O that\\O makes\\O it\\O run\\O way\\O to\\O slow\\O .\\O", "opinion_tags": "It\\O is\\O loaded\\O with\\O programs\\O that\\O is\\O of\\O no\\B good\\I for\\O the\\O average\\O user\\O ,\\O that\\O makes\\O it\\O run\\O way\\O to\\O slow\\O .\\O"}, {"uid": "46-1", "sentiment": "negative", "target_tags": "It\\O is\\O loaded\\O with\\O programs\\O that\\O is\\O of\\O no\\O good\\O for\\O the\\O average\\O user\\O ,\\O that\\O makes\\O it\\O run\\B way\\O to\\O slow\\O .\\O", "opinion_tags": "It\\O is\\O loaded\\O with\\O programs\\O that\\O is\\O of\\O no\\O good\\O for\\O the\\O average\\O user\\O ,\\O that\\O makes\\O it\\O run\\O way\\O to\\O slow\\B .\\O"}], "postag": ["PRP", "VBZ", "VBN", "IN", "NNS", "WDT", "VBZ", "IN", "DT", "JJ", "IN", "DT", "JJ", "NN", ",", "WDT", "VBZ", "PRP", "VB", "NN", "TO", "VB", "."], "head": [3, 3, 0, 5, 3, 10, 10, 10, 10, 5, 14, 14, 14, 10, 17, 17, 14, 17, 17, 19, 22, 19, 3], "deprel": ["nsubj:pass", "aux:pass", "root", "case", "obl", "nsubj", "cop", "case", "det", "acl:relcl", "case", "det", "amod", "obl", "punct", "nsubj", "acl:relcl", "obj", "xcomp", "obj", "mark", "advcl", "punct"]}, {"id": "47", "sentence": "Now , , , , , my monitor has been acting up for about 2 months .", "triples": [{"uid": "47-0", "sentiment": "negative", "target_tags": "Now\\O ,\\O ,\\O ,\\O ,\\O ,\\O my\\O monitor\\B has\\O been\\O acting\\O up\\O for\\O about\\O 2\\O months\\O .\\O", "opinion_tags": "Now\\O ,\\O ,\\O ,\\O ,\\O ,\\O my\\O monitor\\O has\\O been\\O acting\\B up\\I for\\O about\\O 2\\O months\\O .\\O"}], "postag": ["RB", ",", ",", ",", ",", ",", "PRP$", "NN", "VBZ", "VBN", "VBG", "RP", "IN", "RB", "CD", "NNS", "."], "head": [11, 11, 11, 11, 11, 11, 8, 11, 11, 11, 0, 11, 16, 15, 16, 11, 11], "deprel": ["advmod", "punct", "punct", "punct", "punct", "punct", "nmod:poss", "nsubj", "aux", "aux", "root", "compound:prt", "case", "advmod", "nummod", "obl", "punct"]}, {"id": "48", "sentence": "Also , the extended warranty was a problem .", "triples": [{"uid": "48-0", "sentiment": "negative", "target_tags": "Also\\O ,\\O the\\O extended\\B warranty\\I was\\O a\\O problem\\O .\\O", "opinion_tags": "Also\\O ,\\O the\\O extended\\O warranty\\O was\\O a\\O problem\\B .\\O"}], "postag": ["RB", ",", "DT", "VBN", "NN", "VBD", "DT", "NN", "."], "head": [8, 8, 5, 5, 8, 8, 8, 0, 8], "deprel": ["advmod", "punct", "det", "amod", "nsubj", "cop", "det", "root", "punct"]}, {"id": "49", "sentence": "They sent it back with a huge crack in it and it still did n't work .", "triples": [{"uid": "49-0", "sentiment": "negative", "target_tags": "They\\O sent\\O it\\O back\\O with\\O a\\O huge\\O crack\\O in\\O it\\O and\\O it\\O still\\O did\\O n't\\O work\\B .\\O", "opinion_tags": "They\\O sent\\O it\\O back\\O with\\O a\\O huge\\O crack\\O in\\O it\\O and\\O it\\O still\\O did\\B n't\\I work\\O .\\O"}], "postag": ["PRP", "VBD", "PRP", "RB", "IN", "DT", "JJ", "NN", "IN", "PRP", "CC", "PRP", "RB", "VBD", "RB", "VB", "."], "head": [2, 0, 2, 2, 8, 8, 8, 2, 10, 8, 16, 16, 16, 16, 16, 2, 2], "deprel": ["nsubj", "root", "obj", "advmod", "case", "det", "amod", "obl", "case", "nmod", "cc", "nsubj", "advmod", "aux", "advmod", "conj", "punct"]}, {"id": "50", "sentence": "The size is perfect and I do not recommend anything bigger except for any person who can exceed the limited space it gives you .", "triples": [{"uid": "50-0", "sentiment": "positive", "target_tags": "The\\O size\\B is\\O perfect\\O and\\O I\\O do\\O not\\O recommend\\O anything\\O bigger\\O except\\O for\\O any\\O person\\O who\\O can\\O exceed\\O the\\O limited\\O space\\O it\\O gives\\O you\\O .\\O", "opinion_tags": "The\\O size\\O is\\O perfect\\B and\\O I\\O do\\O not\\O recommend\\O anything\\O bigger\\O except\\O for\\O any\\O person\\O who\\O can\\O exceed\\O the\\O limited\\O space\\O it\\O gives\\O you\\O .\\O"}, {"uid": "50-1", "sentiment": "negative", "target_tags": "The\\O size\\O is\\O perfect\\O and\\O I\\O do\\O not\\O recommend\\O anything\\O bigger\\O except\\O for\\O any\\O person\\O who\\O can\\O exceed\\O the\\O limited\\O space\\B it\\O gives\\O you\\O .\\O", "opinion_tags": "The\\O size\\O is\\O perfect\\O and\\O I\\O do\\O not\\O recommend\\O anything\\O bigger\\O except\\O for\\O any\\O person\\O who\\O can\\O exceed\\O the\\O limited\\B space\\O it\\O gives\\O you\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "PRP", "VBP", "RB", "VB", "NN", "JJR", "IN", "IN", "DT", "NN", "WP", "MD", "VB", "DT", "JJ", "NN", "PRP", "VBZ", "PRP", "."], "head": [2, 4, 4, 0, 9, 9, 9, 9, 4, 9, 10, 15, 15, 15, 9, 18, 18, 15, 21, 21, 18, 23, 21, 23, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "nsubj", "aux", "advmod", "conj", "obj", "amod", "case", "case", "det", "obl", "nsubj", "aux", "acl:relcl", "det", "amod", "obj", "nsubj", "acl:relcl", "obj", "punct"]}, {"id": "51", "sentence": "the mouse buttons are hard to push .", "triples": [{"uid": "51-0", "sentiment": "negative", "target_tags": "the\\O mouse\\B buttons\\I are\\O hard\\O to\\O push\\O .\\O", "opinion_tags": "the\\O mouse\\O buttons\\O are\\O hard\\B to\\O push\\O .\\O"}], "postag": ["DT", "NN", "NNS", "VBP", "JJ", "TO", "VB", "."], "head": [3, 3, 5, 5, 0, 7, 5, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "mark", "advcl", "punct"]}, {"id": "52", "sentence": "The price is another driving influence that made me purchase this laptop .", "triples": [{"uid": "52-0", "sentiment": "positive", "target_tags": "The\\O price\\B is\\O another\\O driving\\O influence\\O that\\O made\\O me\\O purchase\\O this\\O laptop\\O .\\O", "opinion_tags": "The\\O price\\O is\\O another\\O driving\\B influence\\O that\\O made\\O me\\O purchase\\O this\\O laptop\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "NN", "NN", "WDT", "VBD", "PRP", "VB", "DT", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 8, 6, 8, 8, 12, 10, 6], "deprel": ["det", "nsubj", "cop", "det", "compound", "root", "nsubj", "acl:relcl", "obj", "xcomp", "det", "obj", "punct"]}, {"id": "53", "sentence": "It is stamped and not in pieces therefore it is a stronger more resilient frame .", "triples": [{"uid": "53-0", "sentiment": "positive", "target_tags": "It\\O is\\O stamped\\O and\\O not\\O in\\O pieces\\O therefore\\O it\\O is\\O a\\O stronger\\O more\\O resilient\\O frame\\B .\\O", "opinion_tags": "It\\O is\\O stamped\\O and\\O not\\O in\\O pieces\\O therefore\\O it\\O is\\O a\\O stronger\\B more\\O resilient\\O frame\\O .\\O"}, {"uid": "53-1", "sentiment": "positive", "target_tags": "It\\O is\\O stamped\\O and\\O not\\O in\\O pieces\\O therefore\\O it\\O is\\O a\\O stronger\\O more\\O resilient\\O frame\\B .\\O", "opinion_tags": "It\\O is\\O stamped\\O and\\O not\\O in\\O pieces\\O therefore\\O it\\O is\\O a\\O stronger\\O more\\O resilient\\B frame\\O .\\O"}], "postag": ["PRP", "VBZ", "VBN", "CC", "RB", "IN", "NNS", "RB", "PRP", "VBZ", "DT", "JJR", "RBR", "JJ", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 15, 15, 15, 15, 15, 14, 15, 3, 3], "deprel": ["nsubj:pass", "aux:pass", "root", "cc", "advmod", "case", "obl", "advmod", "nsubj", "cop", "det", "amod", "advmod", "amod", "parataxis", "punct"]}, {"id": "54", "sentence": "I would like at least a 4 hr . battery life .", "triples": [{"uid": "54-0", "sentiment": "neutral", "target_tags": "I\\O would\\O like\\O at\\O least\\O a\\O 4\\O hr\\O .\\O battery\\B life\\I .\\O", "opinion_tags": "I\\O would\\O like\\B at\\O least\\O a\\O 4\\O hr\\O .\\O battery\\O life\\O .\\O"}], "postag": ["PRP", "MD", "VB", "RB", "RBS", "DT", "CD", "NN", ".", "NN", "NN", "."], "head": [3, 3, 0, 5, 7, 11, 8, 11, 11, 11, 3, 3], "deprel": ["nsubj", "aux", "root", "case", "nmod", "det", "nummod", "compound", "punct", "compound", "obj", "punct"]}, {"id": "55", "sentence": "Clear picture on it and everything .", "triples": [{"uid": "55-0", "sentiment": "positive", "target_tags": "Clear\\O picture\\B on\\O it\\O and\\O everything\\O .\\O", "opinion_tags": "Clear\\B picture\\O on\\O it\\O and\\O everything\\O .\\O"}], "postag": ["JJ", "NN", "IN", "PRP", "CC", "NN", "."], "head": [2, 0, 4, 2, 6, 2, 2], "deprel": ["amod", "root", "case", "nmod", "cc", "conj", "punct"]}, {"id": "56", "sentence": "Screen is awesome , battery life is good .", "triples": [{"uid": "56-0", "sentiment": "positive", "target_tags": "Screen\\B is\\O awesome\\O ,\\O battery\\O life\\O is\\O good\\O .\\O", "opinion_tags": "Screen\\O is\\O awesome\\B ,\\O battery\\O life\\O is\\O good\\O .\\O"}, {"uid": "56-1", "sentiment": "positive", "target_tags": "Screen\\O is\\O awesome\\O ,\\O battery\\B life\\I is\\O good\\O .\\O", "opinion_tags": "Screen\\O is\\O awesome\\O ,\\O battery\\O life\\O is\\O good\\B .\\O"}], "postag": ["NN", "VBZ", "JJ", ",", "NN", "NN", "VBZ", "JJ", "."], "head": [3, 3, 0, 3, 6, 8, 8, 3, 3], "deprel": ["nsubj", "cop", "root", "punct", "compound", "nsubj", "cop", "conj", "punct"]}, {"id": "57", "sentence": "Somehow the system clock got messed up after reboot .", "triples": [{"uid": "57-0", "sentiment": "negative", "target_tags": "Somehow\\O the\\O system\\B clock\\I got\\O messed\\O up\\O after\\O reboot\\O .\\O", "opinion_tags": "Somehow\\O the\\O system\\O clock\\O got\\O messed\\B up\\I after\\O reboot\\O .\\O"}], "postag": ["RB", "DT", "NN", "NN", "VBD", "VBN", "RP", "IN", "NN", "."], "head": [6, 4, 4, 6, 6, 0, 6, 9, 6, 6], "deprel": ["advmod", "det", "compound", "nsubj:pass", "aux:pass", "root", "compound:prt", "case", "obl", "punct"]}, {"id": "58", "sentence": "HP said it was out of warranty .", "triples": [{"uid": "58-0", "sentiment": "neutral", "target_tags": "HP\\O said\\O it\\O was\\O out\\O of\\O warranty\\B .\\O", "opinion_tags": "HP\\O said\\O it\\O was\\O out\\B of\\I warranty\\O .\\O"}], "postag": ["NNP", "VBD", "PRP", "VBD", "IN", "IN", "NN", "."], "head": [2, 0, 7, 7, 7, 7, 2, 2], "deprel": ["nsubj", "root", "nsubj", "cop", "case", "case", "ccomp", "punct"]}, {"id": "59", "sentence": "I like those programs better than Office and you can save your files to be completely compatible with the Office programs as well .", "triples": [{"uid": "59-0", "sentiment": "positive", "target_tags": "I\\O like\\O those\\O programs\\B better\\O than\\O Office\\O and\\O you\\O can\\O save\\O your\\O files\\O to\\O be\\O completely\\O compatible\\O with\\O the\\O Office\\O programs\\O as\\O well\\O .\\O", "opinion_tags": "I\\O like\\B those\\O programs\\O better\\O than\\O Office\\O and\\O you\\O can\\O save\\O your\\O files\\O to\\O be\\O completely\\O compatible\\O with\\O the\\O Office\\O programs\\O as\\O well\\O .\\O"}, {"uid": "59-1", "sentiment": "positive", "target_tags": "I\\O like\\O those\\O programs\\B better\\O than\\O Office\\O and\\O you\\O can\\O save\\O your\\O files\\O to\\O be\\O completely\\O compatible\\O with\\O the\\O Office\\O programs\\O as\\O well\\O .\\O", "opinion_tags": "I\\O like\\O those\\O programs\\O better\\B than\\O Office\\O and\\O you\\O can\\O save\\O your\\O files\\O to\\O be\\O completely\\O compatible\\O with\\O the\\O Office\\O programs\\O as\\O well\\O .\\O"}, {"uid": "59-2", "sentiment": "neutral", "target_tags": "I\\O like\\O those\\O programs\\O better\\O than\\O Office\\O and\\O you\\O can\\O save\\O your\\O files\\O to\\O be\\O completely\\O compatible\\O with\\O the\\O Office\\B programs\\I as\\O well\\O .\\O", "opinion_tags": "I\\O like\\O those\\O programs\\O better\\O than\\O Office\\O and\\O you\\O can\\O save\\O your\\O files\\O to\\O be\\O completely\\O compatible\\B with\\O the\\O Office\\O programs\\O as\\O well\\O .\\O"}], "postag": ["PRP", "VBP", "DT", "NNS", "JJR", "IN", "NNP", "CC", "PRP", "MD", "VB", "PRP$", "NNS", "TO", "VB", "RB", "JJ", "IN", "DT", "NN", "NNS", "RB", "RB", "."], "head": [2, 0, 4, 2, 2, 7, 5, 11, 11, 11, 2, 13, 11, 17, 17, 17, 11, 21, 21, 21, 17, 17, 22, 2], "deprel": ["nsubj", "root", "det", "obj", "advmod", "case", "obl", "cc", "nsubj", "aux", "conj", "nmod:poss", "obj", "mark", "cop", "advmod", "xcomp", "case", "det", "compound", "obl", "advmod", "fixed", "punct"]}, {"id": "60", "sentence": "Great wifi too .", "triples": [{"uid": "60-0", "sentiment": "positive", "target_tags": "Great\\O wifi\\B too\\O .\\O", "opinion_tags": "Great\\B wifi\\O too\\O .\\O"}], "postag": ["JJ", "NN", "RB", "."], "head": [2, 0, 2, 2], "deprel": ["amod", "root", "advmod", "punct"]}, {"id": "61", "sentence": "The lcd screen stopped working on mine after 10 months .", "triples": [{"uid": "61-0", "sentiment": "negative", "target_tags": "The\\O lcd\\B screen\\I stopped\\O working\\O on\\O mine\\O after\\O 10\\O months\\O .\\O", "opinion_tags": "The\\O lcd\\O screen\\O stopped\\B working\\O on\\O mine\\O after\\O 10\\O months\\O .\\O"}], "postag": ["DT", "NN", "NN", "VBD", "VBG", "IN", "PRP", "IN", "CD", "NNS", "."], "head": [3, 3, 4, 0, 4, 7, 5, 10, 10, 5, 4], "deprel": ["det", "compound", "nsubj", "root", "xcomp", "case", "obl", "case", "nummod", "obl", "punct"]}, {"id": "62", "sentence": "We have had numerous problems with Vista , such as Adobe Flash player just quits and has to be uninstalled and then reinsalled , Internet Explore just quits and you lose whatever you were working on , also , the same Windows update has appeared on this computer since we got it and has been updated probably 400 times , the same update .", "triples": [{"uid": "62-0", "sentiment": "negative", "target_tags": "We\\O have\\O had\\O numerous\\O problems\\O with\\O Vista\\B ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "opinion_tags": "We\\O have\\O had\\O numerous\\O problems\\B with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O"}, {"uid": "62-1", "sentiment": "negative", "target_tags": "We\\O have\\O had\\O numerous\\O problems\\O with\\O Vista\\O ,\\O such\\O as\\O Adobe\\B Flash\\I player\\I just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "opinion_tags": "We\\O have\\O had\\O numerous\\O problems\\B with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O"}, {"uid": "62-2", "sentiment": "negative", "target_tags": "We\\O have\\O had\\O numerous\\O problems\\O with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\B Explore\\I just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "opinion_tags": "We\\O have\\O had\\O numerous\\O problems\\B with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O"}, {"uid": "62-3", "sentiment": "negative", "target_tags": "We\\O have\\O had\\O numerous\\O problems\\O with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\B update\\I has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "opinion_tags": "We\\O have\\O had\\O numerous\\O problems\\B with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O"}, {"uid": "62-4", "sentiment": "negative", "target_tags": "We\\O have\\O had\\O numerous\\O problems\\O with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\B has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "opinion_tags": "We\\O have\\O had\\O numerous\\O problems\\B with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O"}], "postag": ["PRP", "VBP", "VBN", "JJ", "NNS", "IN", "NNP", ",", "JJ", "IN", "NNP", "NNP", "NN", "RB", "VBZ", "CC", "VBZ", "TO", "VB", "VBN", "CC", "RB", "VBN", ",", "NNP", "NNP", "RB", "VBZ", "CC", "PRP", "VBP", "WP", "PRP", "VBD", "VBG", "IN", ",", "RB", ",", "DT", "JJ", "NNS", "NN", "VBZ", "VBN", "IN", "DT", "NN", "IN", "PRP", "VBD", "PRP", "CC", "VBZ", "VBN", "VBN", "RB", "CD", "NNS", ",", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 5, 3, 13, 9, 13, 13, 15, 15, 3, 17, 15, 20, 20, 17, 23, 23, 15, 28, 26, 28, 28, 3, 31, 31, 28, 35, 35, 35, 31, 35, 3, 45, 45, 43, 43, 43, 45, 45, 3, 48, 48, 45, 51, 51, 45, 51, 56, 56, 56, 51, 56, 59, 56, 63, 63, 63, 56, 3], "deprel": ["nsubj", "aux", "root", "amod", "obj", "case", "nmod", "punct", "case", "fixed", "compound", "compound", "nsubj", "advmod", "advcl", "cc", "conj", "mark", "aux:pass", "xcomp", "cc", "advmod", "conj", "punct", "compound", "nsubj", "advmod", "parataxis", "cc", "nsubj", "conj", "obl", "nsubj", "aux", "ccomp", "obl", "punct", "advmod", "punct", "det", "amod", "compound", "nsubj", "aux", "parataxis", "case", "det", "obl", "mark", "nsubj", "advcl", "obj", "cc", "aux", "aux:pass", "conj", "advmod", "nummod", "obl:tmod", "punct", "det", "amod", "parataxis", "punct"]}, {"id": "63", "sentence": "I always have used a tower home PC and jumped to the laptop and have been very satisfied with its performance .", "triples": [{"uid": "63-0", "sentiment": "positive", "target_tags": "I\\O always\\O have\\O used\\O a\\O tower\\O home\\O PC\\O and\\O jumped\\O to\\O the\\O laptop\\O and\\O have\\O been\\O very\\O satisfied\\O with\\O its\\O performance\\B .\\O", "opinion_tags": "I\\O always\\O have\\O used\\O a\\O tower\\O home\\O PC\\O and\\O jumped\\O to\\O the\\O laptop\\O and\\O have\\O been\\O very\\O satisfied\\B with\\O its\\O performance\\O .\\O"}], "postag": ["PRP", "RB", "VBP", "VBN", "DT", "NN", "NN", "NNP", "CC", "VBD", "IN", "DT", "NN", "CC", "VBP", "VBN", "RB", "JJ", "IN", "PRP$", "NN", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 10, 4, 13, 13, 10, 18, 18, 18, 18, 4, 21, 21, 18, 4], "deprel": ["nsubj", "advmod", "aux", "root", "det", "compound", "compound", "obj", "cc", "conj", "case", "det", "obl", "cc", "aux", "cop", "advmod", "conj", "case", "nmod:poss", "obl", "punct"]}, {"id": "64", "sentence": "The Apple applications ( ex . iPhoto ) are fun , easy , and really cool to use ( unlike the competition ) !", "triples": [{"uid": "64-0", "sentiment": "positive", "target_tags": "The\\O Apple\\B applications\\I (\\O ex\\O .\\O iPhoto\\O )\\O are\\O fun\\O ,\\O easy\\O ,\\O and\\O really\\O cool\\O to\\O use\\O (\\O unlike\\O the\\O competition\\O )\\O !\\O", "opinion_tags": "The\\O Apple\\O applications\\O (\\O ex\\O .\\O iPhoto\\O )\\O are\\O fun\\B ,\\O easy\\O ,\\O and\\O really\\O cool\\O to\\O use\\O (\\O unlike\\O the\\O competition\\O )\\O !\\O"}, {"uid": "64-1", "sentiment": "positive", "target_tags": "The\\O Apple\\B applications\\I (\\O ex\\O .\\O iPhoto\\O )\\O are\\O fun\\O ,\\O easy\\O ,\\O and\\O really\\O cool\\O to\\O use\\O (\\O unlike\\O the\\O competition\\O )\\O !\\O", "opinion_tags": "The\\O Apple\\O applications\\O (\\O ex\\O .\\O iPhoto\\O )\\O are\\O fun\\O ,\\O easy\\B ,\\O and\\O really\\O cool\\O to\\O use\\O (\\O unlike\\O the\\O competition\\O )\\O !\\O"}, {"uid": "64-2", "sentiment": "positive", "target_tags": "The\\O Apple\\B applications\\I (\\O ex\\O .\\O iPhoto\\O )\\O are\\O fun\\O ,\\O easy\\O ,\\O and\\O really\\O cool\\O to\\O use\\O (\\O unlike\\O the\\O competition\\O )\\O !\\O", "opinion_tags": "The\\O Apple\\O applications\\O (\\O ex\\O .\\O iPhoto\\O )\\O are\\O fun\\O ,\\O easy\\O ,\\O and\\O really\\O cool\\B to\\O use\\O (\\O unlike\\O the\\O competition\\O )\\O !\\O"}, {"uid": "64-3", "sentiment": "positive", "target_tags": "The\\O Apple\\O applications\\O (\\O ex\\O .\\O iPhoto\\B )\\O are\\O fun\\O ,\\O easy\\O ,\\O and\\O really\\O cool\\O to\\O use\\O (\\O unlike\\O the\\O competition\\O )\\O !\\O", "opinion_tags": "The\\O Apple\\O applications\\O (\\O ex\\O .\\O iPhoto\\O )\\O are\\O fun\\B ,\\O easy\\O ,\\O and\\O really\\O cool\\O to\\O use\\O (\\O unlike\\O the\\O competition\\O )\\O !\\O"}, {"uid": "64-4", "sentiment": "positive", "target_tags": "The\\O Apple\\O applications\\O (\\O ex\\O .\\O iPhoto\\B )\\O are\\O fun\\O ,\\O easy\\O ,\\O and\\O really\\O cool\\O to\\O use\\O (\\O unlike\\O the\\O competition\\O )\\O !\\O", "opinion_tags": "The\\O Apple\\O applications\\O (\\O ex\\O .\\O iPhoto\\O )\\O are\\O fun\\O ,\\O easy\\B ,\\O and\\O really\\O cool\\O to\\O use\\O (\\O unlike\\O the\\O competition\\O )\\O !\\O"}, {"uid": "64-5", "sentiment": "positive", "target_tags": "The\\O Apple\\O applications\\O (\\O ex\\O .\\O iPhoto\\B )\\O are\\O fun\\O ,\\O easy\\O ,\\O and\\O really\\O cool\\O to\\O use\\O (\\O unlike\\O the\\O competition\\O )\\O !\\O", "opinion_tags": "The\\O Apple\\O applications\\O (\\O ex\\O .\\O iPhoto\\O )\\O are\\O fun\\O ,\\O easy\\O ,\\O and\\O really\\O cool\\B to\\O use\\O (\\O unlike\\O the\\O competition\\O )\\O !\\O"}], "postag": ["DT", "NNP", "NNS", "-LRB-", "FW", ".", "NNP", "-RRB-", "VBP", "JJ", ",", "JJ", ",", "CC", "RB", "JJ", "TO", "VB", "-LRB-", "IN", "DT", "NN", "-RRB-", "."], "head": [3, 3, 10, 5, 3, 5, 5, 5, 10, 0, 12, 10, 16, 16, 16, 10, 18, 16, 22, 22, 22, 10, 22, 10], "deprel": ["det", "compound", "nsubj", "punct", "appos", "punct", "nmod", "punct", "cop", "root", "punct", "conj", "punct", "cc", "advmod", "conj", "mark", "advcl", "punct", "case", "det", "obl", "punct", "punct"]}, {"id": "65", "sentence": "First it burned or fused the power adapter plug .", "triples": [{"uid": "65-0", "sentiment": "negative", "target_tags": "First\\O it\\O burned\\O or\\O fused\\O the\\O power\\B adapter\\I plug\\I .\\O", "opinion_tags": "First\\O it\\O burned\\B or\\O fused\\O the\\O power\\O adapter\\O plug\\O .\\O"}, {"uid": "65-1", "sentiment": "negative", "target_tags": "First\\O it\\O burned\\O or\\O fused\\O the\\O power\\B adapter\\I plug\\I .\\O", "opinion_tags": "First\\O it\\O burned\\O or\\O fused\\B the\\O power\\O adapter\\O plug\\O .\\O"}], "postag": ["RB", "PRP", "VBD", "CC", "VBD", "DT", "NN", "NN", "NN", "."], "head": [3, 3, 0, 5, 3, 9, 8, 9, 3, 3], "deprel": ["advmod", "nsubj", "root", "cc", "conj", "det", "compound", "compound", "obj", "punct"]}, {"id": "66", "sentence": "It was still working , but there was nothing on the screen .", "triples": [{"uid": "66-0", "sentiment": "negative", "target_tags": "It\\O was\\O still\\O working\\O ,\\O but\\O there\\O was\\O nothing\\O on\\O the\\O screen\\B .\\O", "opinion_tags": "It\\O was\\O still\\O working\\O ,\\O but\\O there\\O was\\O nothing\\B on\\O the\\O screen\\O .\\O"}], "postag": ["PRP", "VBD", "RB", "VBG", ",", "CC", "EX", "VBD", "NN", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 8, 12, 12, 9, 4], "deprel": ["nsubj", "aux", "advmod", "root", "punct", "cc", "expl", "conj", "nsubj", "case", "det", "nmod", "punct"]}, {"id": "67", "sentence": "Though the picture , video , and music software is nowhere close to professional grade software Im used to ( CS5 ) but does the job for beginner and even intermediate media designers .", "triples": [{"uid": "67-0", "sentiment": "negative", "target_tags": "Though\\O the\\O picture\\O ,\\O video\\O ,\\O and\\O music\\B software\\I is\\O nowhere\\O close\\O to\\O professional\\O grade\\O software\\O Im\\O used\\O to\\O (\\O CS5\\O )\\O but\\O does\\O the\\O job\\O for\\O beginner\\O and\\O even\\O intermediate\\O media\\O designers\\O .\\O", "opinion_tags": "Though\\O the\\O picture\\O ,\\O video\\O ,\\O and\\O music\\O software\\O is\\O nowhere\\B close\\I to\\I professional\\I grade\\O software\\O Im\\O used\\O to\\O (\\O CS5\\O )\\O but\\O does\\O the\\O job\\O for\\O beginner\\O and\\O even\\O intermediate\\O media\\O designers\\O .\\O"}, {"uid": "67-1", "sentiment": "negative", "target_tags": "Though\\O the\\O picture\\O ,\\O video\\O ,\\O and\\O music\\B software\\I is\\O nowhere\\O close\\O to\\O professional\\O grade\\O software\\O Im\\O used\\O to\\O (\\O CS5\\O )\\O but\\O does\\O the\\O job\\O for\\O beginner\\O and\\O even\\O intermediate\\O media\\O designers\\O .\\O", "opinion_tags": "Though\\O the\\O picture\\O ,\\O video\\O ,\\O and\\O music\\O software\\O is\\O nowhere\\O close\\O to\\O professional\\O grade\\O software\\O Im\\O used\\O to\\O (\\O CS5\\O )\\O but\\O does\\B the\\I job\\I for\\O beginner\\O and\\O even\\O intermediate\\O media\\O designers\\O .\\O"}, {"uid": "67-2", "sentiment": "negative", "target_tags": "Though\\O the\\O picture\\B ,\\O video\\O ,\\O and\\O music\\O software\\O is\\O nowhere\\O close\\O to\\O professional\\O grade\\O software\\O Im\\O used\\O to\\O (\\O CS5\\O )\\O but\\O does\\O the\\O job\\O for\\O beginner\\O and\\O even\\O intermediate\\O media\\O designers\\O .\\O", "opinion_tags": "Though\\O the\\O picture\\O ,\\O video\\O ,\\O and\\O music\\O software\\O is\\O nowhere\\B close\\I to\\I professional\\I grade\\O software\\O Im\\O used\\O to\\O (\\O CS5\\O )\\O but\\O does\\O the\\O job\\O for\\O beginner\\O and\\O even\\O intermediate\\O media\\O designers\\O .\\O"}, {"uid": "67-3", "sentiment": "negative", "target_tags": "Though\\O the\\O picture\\B ,\\O video\\O ,\\O and\\O music\\O software\\O is\\O nowhere\\O close\\O to\\O professional\\O grade\\O software\\O Im\\O used\\O to\\O (\\O CS5\\O )\\O but\\O does\\O the\\O job\\O for\\O beginner\\O and\\O even\\O intermediate\\O media\\O designers\\O .\\O", "opinion_tags": "Though\\O the\\O picture\\O ,\\O video\\O ,\\O and\\O music\\O software\\O is\\O nowhere\\O close\\O to\\O professional\\O grade\\O software\\O Im\\O used\\O to\\O (\\O CS5\\O )\\O but\\O does\\B the\\I job\\I for\\O beginner\\O and\\O even\\O intermediate\\O media\\O designers\\O .\\O"}, {"uid": "67-4", "sentiment": "negative", "target_tags": "Though\\O the\\O picture\\O ,\\O video\\B ,\\O and\\O music\\O software\\O is\\O nowhere\\O close\\O to\\O professional\\O grade\\O software\\O Im\\O used\\O to\\O (\\O CS5\\O )\\O but\\O does\\O the\\O job\\O for\\O beginner\\O and\\O even\\O intermediate\\O media\\O designers\\O .\\O", "opinion_tags": "Though\\O the\\O picture\\O ,\\O video\\O ,\\O and\\O music\\O software\\O is\\O nowhere\\B close\\I to\\I professional\\I grade\\O software\\O Im\\O used\\O to\\O (\\O CS5\\O )\\O but\\O does\\O the\\O job\\O for\\O beginner\\O and\\O even\\O intermediate\\O media\\O designers\\O .\\O"}, {"uid": "67-5", "sentiment": "negative", "target_tags": "Though\\O the\\O picture\\O ,\\O video\\B ,\\O and\\O music\\O software\\O is\\O nowhere\\O close\\O to\\O professional\\O grade\\O software\\O Im\\O used\\O to\\O (\\O CS5\\O )\\O but\\O does\\O the\\O job\\O for\\O beginner\\O and\\O even\\O intermediate\\O media\\O designers\\O .\\O", "opinion_tags": "Though\\O the\\O picture\\O ,\\O video\\O ,\\O and\\O music\\O software\\O is\\O nowhere\\O close\\O to\\O professional\\O grade\\O software\\O Im\\O used\\O to\\O (\\O CS5\\O )\\O but\\O does\\B the\\I job\\I for\\O beginner\\O and\\O even\\O intermediate\\O media\\O designers\\O .\\O"}], "postag": ["IN", "DT", "NN", ",", "NN", ",", "CC", "NN", "NN", "VBZ", "RB", "JJ", "IN", "JJ", "NN", "NN", "VBZ", "VBN", "IN", "-LRB-", "NNP", "-RRB-", "CC", "VBZ", "DT", "NN", "IN", "NN", "CC", "RB", "JJ", "NN", "NNS", "."], "head": [12, 3, 12, 5, 3, 9, 9, 9, 3, 12, 12, 0, 16, 16, 16, 12, 12, 12, 18, 21, 18, 21, 24, 12, 26, 24, 28, 26, 33, 33, 33, 33, 28, 12], "deprel": ["mark", "det", "nsubj", "punct", "conj", "punct", "cc", "compound", "conj", "cop", "advmod", "root", "case", "amod", "compound", "obl", "cop", "advcl", "obl", "punct", "obl", "punct", "cc", "conj", "det", "obj", "case", "nmod", "cc", "advmod", "amod", "compound", "conj", "punct"]}, {"id": "68", "sentence": "The much lauded combined touch pad and clicker is a nightmare .", "triples": [{"uid": "68-0", "sentiment": "negative", "target_tags": "The\\O much\\O lauded\\O combined\\B touch\\I pad\\I and\\I clicker\\I is\\O a\\O nightmare\\O .\\O", "opinion_tags": "The\\O much\\O lauded\\O combined\\O touch\\O pad\\O and\\O clicker\\O is\\O a\\O nightmare\\B .\\O"}], "postag": ["DT", "RB", "VBN", "VBN", "NN", "NN", "CC", "NN", "VBZ", "DT", "NN", "."], "head": [6, 3, 6, 6, 6, 11, 8, 6, 11, 11, 0, 11], "deprel": ["det", "advmod", "amod", "amod", "compound", "nsubj", "cc", "conj", "cop", "det", "root", "punct"]}, {"id": "69", "sentence": "The Unibody construction is solid , sleek and beautiful .", "triples": [{"uid": "69-0", "sentiment": "positive", "target_tags": "The\\O Unibody\\B construction\\I is\\O solid\\O ,\\O sleek\\O and\\O beautiful\\O .\\O", "opinion_tags": "The\\O Unibody\\O construction\\O is\\O solid\\B ,\\O sleek\\O and\\O beautiful\\O .\\O"}, {"uid": "69-1", "sentiment": "positive", "target_tags": "The\\O Unibody\\B construction\\I is\\O solid\\O ,\\O sleek\\O and\\O beautiful\\O .\\O", "opinion_tags": "The\\O Unibody\\O construction\\O is\\O solid\\O ,\\O sleek\\B and\\O beautiful\\O .\\O"}, {"uid": "69-2", "sentiment": "positive", "target_tags": "The\\O Unibody\\B construction\\I is\\O solid\\O ,\\O sleek\\O and\\O beautiful\\O .\\O", "opinion_tags": "The\\O Unibody\\O construction\\O is\\O solid\\O ,\\O sleek\\O and\\O beautiful\\B .\\O"}], "postag": ["DT", "NN", "NN", "VBZ", "JJ", ",", "JJ", "CC", "JJ", "."], "head": [3, 3, 5, 5, 0, 7, 5, 9, 5, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "conj", "cc", "conj", "punct"]}, {"id": "70", "sentence": "This MacBook is an outstanding product with great value .", "triples": [{"uid": "70-0", "sentiment": "positive", "target_tags": "This\\O MacBook\\O is\\O an\\O outstanding\\O product\\O with\\O great\\O value\\B .\\O", "opinion_tags": "This\\O MacBook\\O is\\O an\\O outstanding\\B product\\O with\\O great\\O value\\O .\\O"}, {"uid": "70-1", "sentiment": "positive", "target_tags": "This\\O MacBook\\O is\\O an\\O outstanding\\O product\\O with\\O great\\O value\\B .\\O", "opinion_tags": "This\\O MacBook\\O is\\O an\\O outstanding\\O product\\O with\\O great\\B value\\O .\\O"}], "postag": ["DT", "NNP", "VBZ", "DT", "JJ", "NN", "IN", "JJ", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 9, 9, 6, 6], "deprel": ["det", "nsubj", "cop", "det", "amod", "root", "case", "amod", "nmod", "punct"]}, {"id": "71", "sentence": "No temporary replacement , they are out of replacements because `` many computers had problems with the Nvidia chipset `` -Inquired status of repair .", "triples": [{"uid": "71-0", "sentiment": "negative", "target_tags": "No\\O temporary\\O replacement\\O ,\\O they\\O are\\O out\\O of\\O replacements\\O because\\O ``\\O many\\O computers\\O had\\O problems\\O with\\O the\\O Nvidia\\B chipset\\I ``\\O -Inquired\\O status\\O of\\O repair\\O .\\O", "opinion_tags": "No\\O temporary\\O replacement\\O ,\\O they\\O are\\O out\\O of\\O replacements\\O because\\O ``\\O many\\O computers\\O had\\O problems\\B with\\O the\\O Nvidia\\O chipset\\O ``\\O -Inquired\\O status\\O of\\O repair\\O .\\O"}], "postag": ["DT", "JJ", "NN", ",", "PRP", "VBP", "IN", "IN", "NNS", "IN", "``", "JJ", "NNS", "VBD", "NNS", "IN", "DT", "NNP", "NN", "``", "JJ", "NN", "IN", "NN", "."], "head": [3, 3, 9, 3, 9, 9, 9, 9, 0, 14, 14, 13, 14, 9, 14, 22, 22, 19, 22, 22, 22, 15, 24, 22, 9], "deprel": ["det", "amod", "nsubj", "punct", "nsubj", "cop", "case", "case", "root", "mark", "punct", "amod", "nsubj", "advcl", "obj", "case", "det", "compound", "compound", "punct", "amod", "nmod", "case", "nmod", "punct"]}, {"id": "72", "sentence": "I BOUGHT THIS LAP TOP AND THE CHARGE TIME DOSE N'T LAST AS LONG AS THEY SAY IT WILL MORE LIKE 2 HOURS", "triples": [{"uid": "72-0", "sentiment": "negative", "target_tags": "I\\O BOUGHT\\O THIS\\O LAP\\O TOP\\O AND\\O THE\\O CHARGE\\B TIME\\I DOSE\\O N'T\\O LAST\\O AS\\O LONG\\O AS\\O THEY\\O SAY\\O IT\\O WILL\\O MORE\\O LIKE\\O 2\\O HOURS\\O", "opinion_tags": "I\\O BOUGHT\\O THIS\\O LAP\\O TOP\\O AND\\O THE\\O CHARGE\\O TIME\\O DOSE\\O N'T\\O LAST\\O AS\\O LONG\\B AS\\O THEY\\O SAY\\O IT\\O WILL\\O MORE\\O LIKE\\O 2\\O HOURS\\O"}], "postag": ["PRP", "VBD", "DT", "NN", "NN", "CC", "DT", "NN", "NN", "NN", "RB", "VB", "RB", "RB", "IN", "PRP", "VBP", "PRP", "MD", "JJR", "VB", "CD", "NNS"], "head": [2, 0, 5, 5, 2, 12, 10, 9, 10, 12, 12, 2, 14, 12, 17, 17, 14, 21, 21, 21, 2, 23, 21], "deprel": ["nsubj", "root", "det", "compound", "obj", "cc", "det", "compound", "compound", "nsubj", "advmod", "conj", "advmod", "advmod", "mark", "nsubj", "advcl", "nsubj", "aux", "advmod", "parataxis", "nummod", "obj"]}, {"id": "73", "sentence": "The feature are good enough for what I need .", "triples": [{"uid": "73-0", "sentiment": "positive", "target_tags": "The\\O feature\\B are\\O good\\O enough\\O for\\O what\\O I\\O need\\O .\\O", "opinion_tags": "The\\O feature\\O are\\O good\\B enough\\O for\\O what\\O I\\O need\\O .\\O"}], "postag": ["DT", "NN", "VBP", "JJ", "JJ", "IN", "WP", "PRP", "VBP", "."], "head": [2, 5, 5, 5, 0, 7, 5, 9, 7, 5], "deprel": ["det", "nsubj", "cop", "amod", "root", "case", "obl", "nsubj", "acl:relcl", "punct"]}, {"id": "74", "sentence": "Finally , the biggest problem has been tech support .", "triples": [{"uid": "74-0", "sentiment": "negative", "target_tags": "Finally\\O ,\\O the\\O biggest\\O problem\\O has\\O been\\O tech\\B support\\I .\\O", "opinion_tags": "Finally\\O ,\\O the\\O biggest\\O problem\\B has\\O been\\O tech\\O support\\O .\\O"}], "postag": ["RB", ",", "DT", "JJS", "NN", "VBZ", "VBN", "JJ", "NN", "."], "head": [9, 9, 5, 5, 9, 9, 9, 9, 0, 9], "deprel": ["advmod", "punct", "det", "amod", "nsubj", "aux", "cop", "amod", "root", "punct"]}, {"id": "75", "sentence": "( I had been a Windows/Linux user before this ) I love the size because the screen is big enough for what I use it for ( Internet , artwork ) , and yet it is small enough to be reasonably portable .", "triples": [{"uid": "75-0", "sentiment": "positive", "target_tags": "(\\O I\\O had\\O been\\O a\\O Windows/Linux\\O user\\O before\\O this\\O )\\O I\\O love\\O the\\O size\\B because\\O the\\O screen\\O is\\O big\\O enough\\O for\\O what\\O I\\O use\\O it\\O for\\O (\\O Internet\\O ,\\O artwork\\O )\\O ,\\O and\\O yet\\O it\\O is\\O small\\O enough\\O to\\O be\\O reasonably\\O portable\\O .\\O", "opinion_tags": "(\\O I\\O had\\O been\\O a\\O Windows/Linux\\O user\\O before\\O this\\O )\\O I\\O love\\B the\\O size\\O because\\O the\\O screen\\O is\\O big\\O enough\\O for\\O what\\O I\\O use\\O it\\O for\\O (\\O Internet\\O ,\\O artwork\\O )\\O ,\\O and\\O yet\\O it\\O is\\O small\\O enough\\O to\\O be\\O reasonably\\O portable\\O .\\O"}, {"uid": "75-1", "sentiment": "positive", "target_tags": "(\\O I\\O had\\O been\\O a\\O Windows/Linux\\O user\\O before\\O this\\O )\\O I\\O love\\O the\\O size\\O because\\O the\\O screen\\B is\\O big\\O enough\\O for\\O what\\O I\\O use\\O it\\O for\\O (\\O Internet\\O ,\\O artwork\\O )\\O ,\\O and\\O yet\\O it\\O is\\O small\\O enough\\O to\\O be\\O reasonably\\O portable\\O .\\O", "opinion_tags": "(\\O I\\O had\\O been\\O a\\O Windows/Linux\\O user\\O before\\O this\\O )\\O I\\O love\\O the\\O size\\O because\\O the\\O screen\\O is\\O big\\B enough\\O for\\O what\\O I\\O use\\O it\\O for\\O (\\O Internet\\O ,\\O artwork\\O )\\O ,\\O and\\O yet\\O it\\O is\\O small\\O enough\\O to\\O be\\O reasonably\\O portable\\O .\\O"}, {"uid": "75-2", "sentiment": "positive", "target_tags": "(\\O I\\O had\\O been\\O a\\O Windows/Linux\\O user\\O before\\O this\\O )\\O I\\O love\\O the\\O size\\O because\\O the\\O screen\\B is\\O big\\O enough\\O for\\O what\\O I\\O use\\O it\\O for\\O (\\O Internet\\O ,\\O artwork\\O )\\O ,\\O and\\O yet\\O it\\O is\\O small\\O enough\\O to\\O be\\O reasonably\\O portable\\O .\\O", "opinion_tags": "(\\O I\\O had\\O been\\O a\\O Windows/Linux\\O user\\O before\\O this\\O )\\O I\\O love\\O the\\O size\\O because\\O the\\O screen\\O is\\O big\\O enough\\O for\\O what\\O I\\O use\\O it\\O for\\O (\\O Internet\\O ,\\O artwork\\O )\\O ,\\O and\\O yet\\O it\\O is\\O small\\B enough\\O to\\O be\\O reasonably\\O portable\\O .\\O"}], "postag": ["-LRB-", "PRP", "VBD", "VBN", "DT", "NNP", "NN", "IN", "DT", "-RRB-", "PRP", "VBP", "DT", "NN", "IN", "DT", "NN", "VBZ", "JJ", "JJ", "IN", "WP", "PRP", "VBP", "PRP", "IN", "-LRB-", "NNP", ",", "NN", "-RRB-", ",", "CC", "CC", "PRP", "VBZ", "JJ", "JJ", "TO", "VB", "RB", "JJ", "."], "head": [7, 7, 7, 7, 7, 7, 0, 9, 7, 7, 12, 7, 14, 12, 20, 17, 20, 20, 20, 12, 22, 20, 24, 22, 24, 30, 30, 30, 30, 24, 30, 37, 37, 37, 37, 37, 12, 37, 42, 42, 42, 38, 7], "deprel": ["punct", "nsubj", "aux", "cop", "det", "compound", "root", "case", "nmod", "punct", "nsubj", "parataxis", "det", "obj", "mark", "det", "nsubj", "cop", "amod", "advcl", "case", "obl", "nsubj", "acl:relcl", "obj", "case", "punct", "compound", "punct", "obl", "punct", "punct", "cc", "cc", "expl", "cop", "conj", "advmod", "mark", "cop", "advmod", "advcl", "punct"]}, {"id": "76", "sentence": "Plain and simple , it ( laptop ) runs great and loads fast .", "triples": [{"uid": "76-0", "sentiment": "positive", "target_tags": "Plain\\O and\\O simple\\O ,\\O it\\O (\\O laptop\\O )\\O runs\\B great\\O and\\O loads\\O fast\\O .\\O", "opinion_tags": "Plain\\O and\\O simple\\O ,\\O it\\O (\\O laptop\\O )\\O runs\\O great\\B and\\O loads\\O fast\\O .\\O"}, {"uid": "76-1", "sentiment": "positive", "target_tags": "Plain\\O and\\O simple\\O ,\\O it\\O (\\O laptop\\O )\\O runs\\O great\\O and\\O loads\\B fast\\O .\\O", "opinion_tags": "Plain\\O and\\O simple\\O ,\\O it\\O (\\O laptop\\O )\\O runs\\O great\\O and\\O loads\\O fast\\B .\\O"}], "postag": ["JJ", "CC", "JJ", ",", "PRP", "-LRB-", "NN", "-RRB-", "VBZ", "JJ", "CC", "NNS", "JJ", "."], "head": [9, 3, 1, 9, 9, 7, 5, 7, 0, 9, 13, 13, 9, 9], "deprel": ["advmod", "cc", "conj", "punct", "nsubj", "punct", "appos", "punct", "root", "xcomp", "cc", "nsubj", "conj", "punct"]}, {"id": "77", "sentence": "It was a great laptop , ran great and was really fast .", "triples": [{"uid": "77-0", "sentiment": "positive", "target_tags": "It\\O was\\O a\\O great\\O laptop\\O ,\\O ran\\B great\\O and\\O was\\O really\\O fast\\O .\\O", "opinion_tags": "It\\O was\\O a\\O great\\O laptop\\O ,\\O ran\\O great\\B and\\O was\\O really\\O fast\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "JJ", "NN", ",", "VBD", "JJ", "CC", "VBD", "RB", "JJ", "."], "head": [5, 5, 5, 5, 0, 7, 5, 7, 12, 12, 12, 5, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "punct", "conj", "xcomp", "cc", "cop", "advmod", "conj", "punct"]}, {"id": "78", "sentence": "The only problem is a lack of screen resolutions !", "triples": [{"uid": "78-0", "sentiment": "negative", "target_tags": "The\\O only\\O problem\\O is\\O a\\O lack\\O of\\O screen\\B resolutions\\I !\\O", "opinion_tags": "The\\O only\\O problem\\B is\\O a\\O lack\\O of\\O screen\\O resolutions\\O !\\O"}, {"uid": "78-1", "sentiment": "negative", "target_tags": "The\\O only\\O problem\\O is\\O a\\O lack\\O of\\O screen\\B resolutions\\I !\\O", "opinion_tags": "The\\O only\\O problem\\O is\\O a\\O lack\\B of\\O screen\\O resolutions\\O !\\O"}], "postag": ["DT", "JJ", "NN", "VBZ", "DT", "NN", "IN", "NN", "NNS", "."], "head": [3, 3, 6, 6, 6, 0, 9, 9, 6, 6], "deprel": ["det", "amod", "nsubj", "cop", "det", "root", "case", "compound", "nmod", "punct"]}, {"id": "79", "sentence": "It is very well built .", "triples": [{"uid": "79-0", "sentiment": "positive", "target_tags": "It\\O is\\O very\\O well\\O built\\B .\\O", "opinion_tags": "It\\O is\\O very\\O well\\B built\\O .\\O"}], "postag": ["PRP", "VBZ", "RB", "RB", "VBN", "."], "head": [5, 5, 4, 5, 0, 5], "deprel": ["nsubj:pass", "aux:pass", "advmod", "advmod", "root", "punct"]}, {"id": "80", "sentence": "The design is awesome , quality is unprecedented .", "triples": [{"uid": "80-0", "sentiment": "positive", "target_tags": "The\\O design\\B is\\O awesome\\O ,\\O quality\\O is\\O unprecedented\\O .\\O", "opinion_tags": "The\\O design\\O is\\O awesome\\B ,\\O quality\\O is\\O unprecedented\\O .\\O"}, {"uid": "80-1", "sentiment": "positive", "target_tags": "The\\O design\\O is\\O awesome\\O ,\\O quality\\B is\\O unprecedented\\O .\\O", "opinion_tags": "The\\O design\\O is\\O awesome\\O ,\\O quality\\O is\\O unprecedented\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ",", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "cop", "conj", "punct"]}, {"id": "81", "sentence": "AND the best part is that it even comes with a free printer ( when they have a certain promotion/offer going , of course ) !", "triples": [{"uid": "81-0", "sentiment": "positive", "target_tags": "AND\\O the\\O best\\O part\\O is\\O that\\O it\\O even\\O comes\\O with\\O a\\O free\\O printer\\B (\\O when\\O they\\O have\\O a\\O certain\\O promotion/offer\\O going\\O ,\\O of\\O course\\O )\\O !\\O", "opinion_tags": "AND\\O the\\O best\\O part\\O is\\O that\\O it\\O even\\O comes\\O with\\O a\\O free\\B printer\\O (\\O when\\O they\\O have\\O a\\O certain\\O promotion/offer\\O going\\O ,\\O of\\O course\\O )\\O !\\O"}], "postag": ["CC", "DT", "JJS", "NN", "VBZ", "IN", "PRP", "RB", "VBZ", "IN", "DT", "JJ", "NN", "-LRB-", "WRB", "PRP", "VBP", "DT", "JJ", "NN", "VBG", ",", "RB", "RB", "-RRB-", "."], "head": [5, 4, 4, 5, 0, 9, 9, 9, 5, 13, 13, 13, 9, 17, 17, 17, 9, 20, 20, 17, 20, 17, 17, 23, 17, 5], "deprel": ["cc", "det", "amod", "nsubj", "root", "mark", "nsubj", "advmod", "ccomp", "case", "det", "amod", "obl", "punct", "mark", "nsubj", "advcl", "det", "amod", "obj", "acl", "punct", "advmod", "fixed", "punct", "punct"]}, {"id": "82", "sentence": "it is very easy for anyone to use an apple and specially the mcbook pro notebook .", "triples": [{"uid": "82-0", "sentiment": "positive", "target_tags": "it\\O is\\O very\\O easy\\O for\\O anyone\\O to\\O use\\B an\\O apple\\O and\\O specially\\O the\\O mcbook\\O pro\\O notebook\\O .\\O", "opinion_tags": "it\\O is\\O very\\O easy\\B for\\O anyone\\O to\\O use\\O an\\O apple\\O and\\O specially\\O the\\O mcbook\\O pro\\O notebook\\O .\\O"}], "postag": ["PRP", "VBZ", "RB", "JJ", "IN", "NN", "TO", "VB", "DT", "NNP", "CC", "RB", "DT", "JJ", "NN", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 10, 8, 16, 16, 16, 16, 16, 10, 4], "deprel": ["expl", "cop", "advmod", "root", "case", "obl", "mark", "acl", "det", "obj", "cc", "advmod", "det", "amod", "compound", "conj", "punct"]}, {"id": "83", "sentence": "The touchpad is very intuitive , so much so that I never want to use buttons to click again !", "triples": [{"uid": "83-0", "sentiment": "positive", "target_tags": "The\\O touchpad\\B is\\O very\\O intuitive\\O ,\\O so\\O much\\O so\\O that\\O I\\O never\\O want\\O to\\O use\\O buttons\\O to\\O click\\O again\\O !\\O", "opinion_tags": "The\\O touchpad\\O is\\O very\\O intuitive\\B ,\\O so\\O much\\O so\\O that\\O I\\O never\\O want\\O to\\O use\\O buttons\\O to\\O click\\O again\\O !\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", ",", "RB", "JJ", "IN", "IN", "PRP", "RB", "VBP", "TO", "VB", "NNS", "TO", "VB", "RB", "."], "head": [2, 5, 5, 5, 0, 5, 8, 5, 13, 9, 13, 13, 5, 15, 13, 15, 18, 15, 18, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "advmod", "advmod", "mark", "fixed", "nsubj", "advmod", "advcl", "mark", "xcomp", "obj", "mark", "advcl", "advmod", "punct"]}, {"id": "84", "sentence": "tons of bloatware and junk programs .", "triples": [{"uid": "84-0", "sentiment": "negative", "target_tags": "tons\\O of\\O bloatware\\O and\\O junk\\O programs\\B .\\O", "opinion_tags": "tons\\O of\\O bloatware\\O and\\O junk\\B programs\\O .\\O"}], "postag": ["NNS", "IN", "NN", "CC", "NN", "NNS", "."], "head": [0, 6, 6, 5, 3, 1, 1], "deprel": ["root", "case", "compound", "cc", "conj", "nmod", "punct"]}, {"id": "85", "sentence": "The real stand out on this computer is the feel of the keyboard and it 's speed .", "triples": [{"uid": "85-0", "sentiment": "positive", "target_tags": "The\\O real\\O stand\\O out\\O on\\O this\\O computer\\O is\\O the\\O feel\\O of\\O the\\O keyboard\\B and\\O it\\O 's\\O speed\\O .\\O", "opinion_tags": "The\\O real\\O stand\\B out\\I on\\O this\\O computer\\O is\\O the\\O feel\\O of\\O the\\O keyboard\\O and\\O it\\O 's\\O speed\\O .\\O"}, {"uid": "85-1", "sentiment": "positive", "target_tags": "The\\O real\\O stand\\O out\\O on\\O this\\O computer\\O is\\O the\\O feel\\O of\\O the\\O keyboard\\O and\\O it\\O 's\\O speed\\B .\\O", "opinion_tags": "The\\O real\\O stand\\B out\\I on\\O this\\O computer\\O is\\O the\\O feel\\O of\\O the\\O keyboard\\O and\\O it\\O 's\\O speed\\O .\\O"}], "postag": ["DT", "JJ", "NN", "RP", "IN", "DT", "NN", "VBZ", "DT", "NN", "IN", "DT", "NN", "CC", "PRP", "VBZ", "NN", "."], "head": [3, 3, 10, 3, 7, 7, 3, 10, 10, 0, 13, 13, 10, 17, 17, 17, 10, 10], "deprel": ["det", "amod", "nsubj", "advmod", "case", "det", "nmod", "cop", "det", "root", "case", "det", "nmod", "cc", "nsubj", "cop", "conj", "punct"]}, {"id": "86", "sentence": "Delivery was early too .", "triples": [{"uid": "86-0", "sentiment": "positive", "target_tags": "Delivery\\B was\\O early\\O too\\O .\\O", "opinion_tags": "Delivery\\O was\\O early\\B too\\O .\\O"}], "postag": ["NN", "VBD", "JJ", "RB", "."], "head": [3, 3, 0, 3, 3], "deprel": ["nsubj", "cop", "root", "advmod", "punct"]}, {"id": "87", "sentence": "Laptops are usually used on the go , so why not give you a better battery ?", "triples": [{"uid": "87-0", "sentiment": "negative", "target_tags": "Laptops\\O are\\O usually\\O used\\O on\\O the\\O go\\O ,\\O so\\O why\\O not\\O give\\O you\\O a\\O better\\O battery\\B ?\\O", "opinion_tags": "Laptops\\O are\\O usually\\O used\\O on\\O the\\O go\\O ,\\O so\\O why\\O not\\O give\\O you\\O a\\O better\\B battery\\O ?\\O"}], "postag": ["NNS", "VBP", "RB", "VBN", "IN", "DT", "NN", ",", "RB", "WRB", "RB", "VB", "PRP", "DT", "JJR", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 12, 12, 12, 12, 4, 12, 16, 16, 12, 4], "deprel": ["nsubj:pass", "aux:pass", "advmod", "root", "case", "det", "obl", "punct", "advmod", "advmod", "advmod", "parataxis", "i<PERSON><PERSON>", "det", "amod", "obj", "punct"]}, {"id": "88", "sentence": "I was n't a big fan of the Netbooks but this one was very well designed .", "triples": [{"uid": "88-0", "sentiment": "positive", "target_tags": "I\\O was\\O n't\\O a\\O big\\O fan\\O of\\O the\\O Netbooks\\O but\\O this\\O one\\O was\\O very\\O well\\O designed\\B .\\O", "opinion_tags": "I\\O was\\O n't\\O a\\O big\\O fan\\O of\\O the\\O Netbooks\\O but\\O this\\O one\\O was\\O very\\O well\\B designed\\O .\\O"}], "postag": ["PRP", "VBD", "RB", "DT", "JJ", "NN", "IN", "DT", "NNPS", "CC", "DT", "NN", "VBD", "RB", "RB", "VBN", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 16, 12, 16, 16, 15, 16, 6, 6], "deprel": ["nsubj", "cop", "advmod", "det", "amod", "root", "case", "det", "nmod", "cc", "det", "nsubj:pass", "aux:pass", "advmod", "advmod", "conj", "punct"]}, {"id": "89", "sentence": "I still have that stupid bluetooth mouse to !", "triples": [{"uid": "89-0", "sentiment": "negative", "target_tags": "I\\O still\\O have\\O that\\O stupid\\O bluetooth\\B mouse\\I to\\O !\\O", "opinion_tags": "I\\O still\\O have\\O that\\O stupid\\B bluetooth\\O mouse\\O to\\O !\\O"}], "postag": ["PRP", "RB", "VBP", "DT", "JJ", "NN", "NN", "IN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "amod", "compound", "obj", "obl", "punct"]}, {"id": "90", "sentence": "2nd Best computer in the world only one way this computer might become the best is that it needs to upgreade patches to make less easier for people to hack into", "triples": [{"uid": "90-0", "sentiment": "negative", "target_tags": "2nd\\O Best\\O computer\\O in\\O the\\O world\\O only\\O one\\O way\\O this\\O computer\\O might\\O become\\O the\\O best\\O is\\O that\\O it\\O needs\\O to\\O upgreade\\O patches\\B to\\O make\\O less\\O easier\\O for\\O people\\O to\\O hack\\O into\\O", "opinion_tags": "2nd\\O Best\\O computer\\O in\\O the\\O world\\O only\\O one\\O way\\O this\\O computer\\O might\\O become\\O the\\O best\\O is\\O that\\O it\\O needs\\B to\\I upgreade\\I patches\\O to\\O make\\O less\\O easier\\O for\\O people\\O to\\O hack\\O into\\O"}], "postag": ["RB", "JJS", "NN", "IN", "DT", "NN", "RB", "CD", "NN", "DT", "NN", "MD", "VB", "DT", "JJS", "VBZ", "IN", "PRP", "VBZ", "TO", "VB", "NNS", "TO", "VB", "RBR", "JJR", "IN", "NNS", "TO", "VB", "IN"], "head": [3, 3, 16, 6, 6, 3, 9, 9, 3, 11, 13, 13, 9, 15, 13, 0, 19, 19, 16, 21, 19, 21, 24, 21, 26, 24, 30, 30, 30, 26, 30], "deprel": ["advmod", "amod", "nsubj", "case", "det", "nmod", "advmod", "nummod", "conj", "det", "nsubj", "aux", "acl:relcl", "det", "xcomp", "root", "mark", "nsubj", "ccomp", "mark", "xcomp", "obj", "mark", "advcl", "advmod", "xcomp", "mark", "nsubj", "mark", "advcl", "obl"]}, {"id": "91", "sentence": "It could be a perfect laptop if it would have faster system memory and its radeon 5850 would have DDR5 instead of DDR3 .", "triples": [{"uid": "91-0", "sentiment": "negative", "target_tags": "It\\O could\\O be\\O a\\O perfect\\O laptop\\O if\\O it\\O would\\O have\\O faster\\O system\\B memory\\I and\\O its\\O radeon\\O 5850\\O would\\O have\\O DDR5\\O instead\\O of\\O DDR3\\O .\\O", "opinion_tags": "It\\O could\\O be\\O a\\O perfect\\O laptop\\O if\\O it\\O would\\O have\\O faster\\B system\\O memory\\O and\\O its\\O radeon\\O 5850\\O would\\O have\\O DDR5\\O instead\\O of\\O DDR3\\O .\\O"}], "postag": ["PRP", "MD", "VB", "DT", "JJ", "NN", "IN", "PRP", "MD", "VB", "JJR", "NN", "NN", "CC", "PRP$", "NN", "CD", "MD", "VB", "NNP", "RB", "IN", "NNP", "."], "head": [6, 6, 6, 6, 6, 0, 10, 10, 10, 6, 13, 13, 10, 19, 16, 19, 16, 19, 6, 19, 23, 21, 19, 6], "deprel": ["nsubj", "aux", "cop", "det", "amod", "root", "mark", "nsubj", "aux", "advcl", "amod", "compound", "obj", "cc", "nmod:poss", "nsubj", "nummod", "aux", "conj", "obj", "case", "fixed", "obl", "punct"]}, {"id": "92", "sentence": "I definitely suggest getting an extended warranty , you will probably need it !", "triples": [{"uid": "92-0", "sentiment": "positive", "target_tags": "I\\O definitely\\O suggest\\O getting\\O an\\O extended\\B warranty\\I ,\\O you\\O will\\O probably\\O need\\O it\\O !\\O", "opinion_tags": "I\\O definitely\\O suggest\\B getting\\O an\\O extended\\O warranty\\O ,\\O you\\O will\\O probably\\O need\\O it\\O !\\O"}], "postag": ["PRP", "RB", "VBP", "VBG", "DT", "VBN", "NN", ",", "PRP", "MD", "RB", "VB", "PRP", "."], "head": [3, 3, 0, 3, 7, 7, 4, 3, 12, 12, 12, 3, 12, 3], "deprel": ["nsubj", "advmod", "root", "xcomp", "det", "amod", "obj", "punct", "nsubj", "aux", "advmod", "parataxis", "obj", "punct"]}, {"id": "93", "sentence": "The only thing I wish is the 15 inch MacBook Pro has much better speakers on the side of the keyboard .", "triples": [{"uid": "93-0", "sentiment": "negative", "target_tags": "The\\O only\\O thing\\O I\\O wish\\O is\\O the\\O 15\\O inch\\O MacBook\\O Pro\\O has\\O much\\O better\\O speakers\\B on\\O the\\O side\\O of\\O the\\O keyboard\\O .\\O", "opinion_tags": "The\\O only\\O thing\\O I\\O wish\\O is\\O the\\O 15\\O inch\\O MacBook\\O Pro\\O has\\O much\\O better\\B speakers\\O on\\O the\\O side\\O of\\O the\\O keyboard\\O .\\O"}], "postag": ["DT", "JJ", "NN", "PRP", "VBP", "VBZ", "DT", "CD", "NN", "NNP", "NNP", "VBZ", "RB", "JJR", "NNS", "IN", "DT", "NN", "IN", "DT", "NN", "."], "head": [3, 3, 11, 5, 3, 11, 11, 9, 11, 11, 0, 11, 14, 15, 12, 18, 18, 12, 21, 21, 18, 11], "deprel": ["det", "amod", "nsubj", "nsubj", "acl:relcl", "cop", "det", "nummod", "compound", "compound", "root", "acl:relcl", "advmod", "amod", "obj", "case", "det", "obl", "case", "det", "nmod", "punct"]}, {"id": "94", "sentence": "Keys stick periodically and I havent had the laptop for 45 days yet .", "triples": [{"uid": "94-0", "sentiment": "negative", "target_tags": "Keys\\B stick\\O periodically\\O and\\O I\\O havent\\O had\\O the\\O laptop\\O for\\O 45\\O days\\O yet\\O .\\O", "opinion_tags": "Keys\\O stick\\B periodically\\O and\\O I\\O havent\\O had\\O the\\O laptop\\O for\\O 45\\O days\\O yet\\O .\\O"}], "postag": ["NNS", "VBP", "RB", "CC", "PRP", "VBP", "VBN", "DT", "NN", "IN", "CD", "NNS", "RB", "."], "head": [2, 0, 2, 7, 7, 7, 2, 9, 7, 12, 12, 7, 7, 2], "deprel": ["nsubj", "root", "advmod", "cc", "nsubj", "aux", "conj", "det", "obj", "case", "nummod", "obl", "advmod", "punct"]}, {"id": "95", "sentence": "It 's graphics are n't bad at all , for the lower end of the MacBook Pro spectrum , easily capable of running StarCraft II and other games with comparable graphics .", "triples": [{"uid": "95-0", "sentiment": "neutral", "target_tags": "It\\O 's\\O graphics\\B are\\O n't\\O bad\\O at\\O all\\O ,\\O for\\O the\\O lower\\O end\\O of\\O the\\O MacBook\\O Pro\\O spectrum\\O ,\\O easily\\O capable\\O of\\O running\\O StarCraft\\O II\\O and\\O other\\O games\\O with\\O comparable\\O graphics\\O .\\O", "opinion_tags": "It\\O 's\\O graphics\\O are\\B n't\\I bad\\I at\\O all\\O ,\\O for\\O the\\O lower\\O end\\O of\\O the\\O MacBook\\O Pro\\O spectrum\\O ,\\O easily\\O capable\\O of\\O running\\O StarCraft\\O II\\O and\\O other\\O games\\O with\\O comparable\\O graphics\\O .\\O"}], "postag": ["PRP", "VBZ", "NNS", "VBP", "RB", "JJ", "IN", "DT", ",", "IN", "DT", "JJR", "NN", "IN", "DT", "NNP", "NNP", "NN", ",", "RB", "JJ", "IN", "VBG", "NNP", "NNP", "CC", "JJ", "NNS", "IN", "JJ", "NNS", "."], "head": [6, 3, 6, 6, 6, 0, 8, 6, 6, 13, 13, 13, 6, 18, 18, 18, 18, 13, 21, 21, 18, 23, 21, 25, 23, 28, 28, 25, 31, 31, 23, 6], "deprel": ["nsubj", "cop", "nsubj", "cop", "advmod", "root", "case", "obl", "punct", "case", "det", "amod", "obl", "case", "det", "compound", "compound", "nmod", "punct", "advmod", "amod", "mark", "advcl", "compound", "obj", "cc", "amod", "conj", "case", "amod", "obl", "punct"]}, {"id": "96", "sentence": "I have <PERSON> , so I am unable to install and uninstall some programs .", "triples": [{"uid": "96-0", "sentiment": "negative", "target_tags": "I\\O have\\O Vista\\O ,\\O so\\O I\\O am\\O unable\\O to\\O install\\B and\\O uninstall\\O some\\O programs\\O .\\O", "opinion_tags": "I\\O have\\O Vista\\O ,\\O so\\O I\\O am\\O unable\\B to\\O install\\O and\\O uninstall\\O some\\O programs\\O .\\O"}], "postag": ["PRP", "VBP", "NNP", ",", "RB", "PRP", "VBP", "JJ", "TO", "VB", "CC", "VB", "DT", "NNS", "."], "head": [2, 0, 2, 2, 8, 8, 8, 2, 10, 8, 12, 10, 14, 10, 2], "deprel": ["nsubj", "root", "obj", "punct", "advmod", "nsubj", "cop", "conj", "mark", "xcomp", "cc", "conj", "det", "obj", "punct"]}, {"id": "97", "sentence": "i am a huge computer person i love anykind of computer that works well , but when i got this one i was so happy with the way it works and how it runs its amazing .", "triples": [{"uid": "97-0", "sentiment": "positive", "target_tags": "i\\O am\\O a\\O huge\\O computer\\O person\\O i\\O love\\O anykind\\O of\\O computer\\O that\\O works\\O well\\O ,\\O but\\O when\\O i\\O got\\O this\\O one\\O i\\O was\\O so\\O happy\\O with\\O the\\O way\\O it\\O works\\O and\\O how\\O it\\O runs\\B its\\O amazing\\O .\\O", "opinion_tags": "i\\O am\\O a\\O huge\\O computer\\O person\\O i\\O love\\O anykind\\O of\\O computer\\O that\\O works\\O well\\O ,\\O but\\O when\\O i\\O got\\O this\\O one\\O i\\O was\\O so\\O happy\\O with\\O the\\O way\\O it\\O works\\O and\\O how\\O it\\O runs\\O its\\O amazing\\B .\\O"}], "postag": ["PRP", "VBP", "DT", "JJ", "NN", "NN", "PRP", "VBP", "NN", "IN", "NN", "WDT", "VBZ", "RB", ",", "CC", "WRB", "PRP", "VBD", "DT", "NN", "PRP", "VBD", "RB", "JJ", "IN", "DT", "NN", "PRP", "VBZ", "CC", "WRB", "PRP", "VBZ", "PRP$", "JJ", "."], "head": [6, 6, 6, 6, 6, 0, 8, 6, 8, 11, 9, 13, 11, 13, 25, 25, 19, 19, 25, 21, 19, 25, 25, 25, 6, 28, 28, 25, 30, 28, 34, 34, 34, 30, 36, 34, 6], "deprel": ["nsubj", "cop", "det", "amod", "compound", "root", "nsubj", "acl:relcl", "obj", "case", "nmod", "nsubj", "acl:relcl", "advmod", "punct", "cc", "mark", "nsubj", "advcl", "det", "obj", "nsubj", "cop", "advmod", "conj", "case", "det", "obl", "nsubj", "acl:relcl", "cc", "mark", "nsubj", "conj", "nmod:poss", "obj", "punct"]}, {"id": "98", "sentence": "Its white color is stylish for college students and easy to take to carry and take to classes .", "triples": [{"uid": "98-0", "sentiment": "positive", "target_tags": "Its\\O white\\O color\\B is\\O stylish\\O for\\O college\\O students\\O and\\O easy\\O to\\O take\\O to\\O carry\\O and\\O take\\O to\\O classes\\O .\\O", "opinion_tags": "Its\\O white\\O color\\O is\\O stylish\\B for\\O college\\O students\\O and\\O easy\\O to\\O take\\O to\\O carry\\O and\\O take\\O to\\O classes\\O .\\O"}], "postag": ["PRP$", "JJ", "NN", "VBZ", "JJ", "IN", "NN", "NNS", "CC", "JJ", "TO", "VB", "TO", "VB", "CC", "VB", "IN", "NNS", "."], "head": [3, 3, 5, 5, 0, 8, 8, 5, 10, 8, 12, 10, 14, 12, 16, 14, 18, 16, 5], "deprel": ["nmod:poss", "amod", "nsubj", "cop", "root", "case", "compound", "obl", "cc", "conj", "mark", "xcomp", "mark", "xcomp", "cc", "conj", "case", "obl", "punct"]}, {"id": "99", "sentence": "I took off a star because the machine has a lot of junk software on it .", "triples": [{"uid": "99-0", "sentiment": "negative", "target_tags": "I\\O took\\O off\\O a\\O star\\O because\\O the\\O machine\\O has\\O a\\O lot\\O of\\O junk\\O software\\B on\\O it\\O .\\O", "opinion_tags": "I\\O took\\O off\\O a\\O star\\O because\\O the\\O machine\\O has\\O a\\O lot\\O of\\O junk\\B software\\O on\\O it\\O .\\O"}], "postag": ["PRP", "VBD", "RP", "DT", "NN", "IN", "DT", "NN", "VBZ", "DT", "NN", "IN", "NN", "NN", "IN", "PRP", "."], "head": [2, 0, 2, 5, 2, 9, 8, 9, 2, 11, 9, 14, 14, 11, 16, 9, 2], "deprel": ["nsubj", "root", "compound:prt", "det", "obj", "mark", "det", "nsubj", "advcl", "det", "obj", "case", "compound", "nmod", "case", "obl", "punct"]}, {"id": "100", "sentence": "The battery life is great .", "triples": [{"uid": "100-0", "sentiment": "positive", "target_tags": "The\\O battery\\B life\\I is\\O great\\O .\\O", "opinion_tags": "The\\O battery\\O life\\O is\\O great\\B .\\O"}], "postag": ["DT", "NN", "NN", "VBZ", "JJ", "."], "head": [3, 3, 5, 5, 0, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct"]}, {"id": "101", "sentence": "I sent it back and found this time that the battery was faulty , so I got a new one and some other fixes they found .", "triples": [{"uid": "101-0", "sentiment": "negative", "target_tags": "I\\O sent\\O it\\O back\\O and\\O found\\O this\\O time\\O that\\O the\\O battery\\B was\\O faulty\\O ,\\O so\\O I\\O got\\O a\\O new\\O one\\O and\\O some\\O other\\O fixes\\O they\\O found\\O .\\O", "opinion_tags": "I\\O sent\\O it\\O back\\O and\\O found\\O this\\O time\\O that\\O the\\O battery\\O was\\O faulty\\B ,\\O so\\O I\\O got\\O a\\O new\\O one\\O and\\O some\\O other\\O fixes\\O they\\O found\\O .\\O"}], "postag": ["PRP", "VBD", "PRP", "RB", "CC", "VBD", "DT", "NN", "IN", "DT", "NN", "VBD", "JJ", ",", "RB", "PRP", "VBD", "DT", "JJ", "NN", "CC", "DT", "JJ", "NNS", "PRP", "VBD", "."], "head": [2, 0, 2, 2, 6, 2, 8, 6, 13, 11, 13, 13, 8, 17, 17, 17, 2, 20, 20, 17, 24, 24, 24, 20, 26, 24, 2], "deprel": ["nsubj", "root", "obj", "advmod", "cc", "conj", "det", "obj", "mark", "det", "nsubj", "cop", "acl", "punct", "advmod", "nsubj", "parataxis", "det", "amod", "obj", "cc", "det", "amod", "conj", "nsubj", "acl:relcl", "punct"]}, {"id": "102", "sentence": "Great pick for portability and affordability .", "triples": [{"uid": "102-0", "sentiment": "positive", "target_tags": "Great\\O pick\\O for\\O portability\\B and\\O affordability\\O .\\O", "opinion_tags": "Great\\B pick\\O for\\O portability\\O and\\O affordability\\O .\\O"}, {"uid": "102-1", "sentiment": "positive", "target_tags": "Great\\O pick\\O for\\O portability\\O and\\O affordability\\B .\\O", "opinion_tags": "Great\\B pick\\O for\\O portability\\O and\\O affordability\\O .\\O"}], "postag": ["JJ", "NN", "IN", "NN", "CC", "NN", "."], "head": [2, 0, 4, 2, 6, 4, 2], "deprel": ["amod", "root", "case", "nmod", "cc", "conj", "punct"]}, {"id": "103", "sentence": "It 's fast , it 's easy easy easy to set up , easy to hook to my wireless network .", "triples": [{"uid": "103-0", "sentiment": "positive", "target_tags": "It\\O 's\\O fast\\O ,\\O it\\O 's\\O easy\\O easy\\O easy\\O to\\O set\\B up\\I ,\\O easy\\O to\\O hook\\O to\\O my\\O wireless\\O network\\O .\\O", "opinion_tags": "It\\O 's\\O fast\\O ,\\O it\\O 's\\O easy\\B easy\\I easy\\I to\\O set\\O up\\O ,\\O easy\\O to\\O hook\\O to\\O my\\O wireless\\O network\\O .\\O"}, {"uid": "103-1", "sentiment": "positive", "target_tags": "It\\O 's\\O fast\\O ,\\O it\\O 's\\O easy\\O easy\\O easy\\O to\\O set\\O up\\O ,\\O easy\\O to\\O hook\\B to\\I my\\I wireless\\I network\\I .\\O", "opinion_tags": "It\\O 's\\O fast\\O ,\\O it\\O 's\\O easy\\O easy\\O easy\\O to\\O set\\O up\\O ,\\O easy\\B to\\O hook\\O to\\O my\\O wireless\\O network\\O .\\O"}], "postag": ["PRP", "VBZ", "JJ", ",", "PRP", "VBZ", "JJ", "JJ", "JJ", "TO", "VB", "RP", ",", "JJ", "TO", "VB", "IN", "PRP$", "JJ", "NN", "."], "head": [3, 3, 0, 3, 7, 7, 3, 9, 7, 11, 9, 11, 14, 11, 16, 14, 20, 20, 20, 16, 3], "deprel": ["nsubj", "cop", "root", "punct", "expl", "cop", "parataxis", "advmod", "conj", "mark", "csubj", "compound:prt", "punct", "xcomp", "mark", "xcomp", "case", "nmod:poss", "amod", "obl", "punct"]}, {"id": "104", "sentence": "I previously purchased a 13 '' macbook ( had pro specs and was aluminum style ) which had a nvidia 9800 ( If I am not mistaken ) and it had major heating issues .", "triples": [{"uid": "104-0", "sentiment": "neutral", "target_tags": "I\\O previously\\O purchased\\O a\\O 13\\O ''\\O macbook\\O (\\O had\\O pro\\O specs\\O and\\O was\\O aluminum\\O style\\O )\\O which\\O had\\O a\\O nvidia\\B 9800\\I (\\O If\\O I\\O am\\O not\\O mistaken\\O )\\O and\\O it\\O had\\O major\\O heating\\O issues\\O .\\O", "opinion_tags": "I\\O previously\\O purchased\\O a\\O 13\\O ''\\O macbook\\O (\\O had\\O pro\\O specs\\O and\\O was\\O aluminum\\O style\\O )\\O which\\O had\\O a\\O nvidia\\O 9800\\O (\\O If\\O I\\O am\\O not\\O mistaken\\O )\\O and\\O it\\O had\\O major\\O heating\\O issues\\B .\\O"}], "postag": ["PRP", "RB", "VBD", "DT", "CD", "''", "NN", "-LRB-", "VBD", "NN", "NNS", "CC", "VBD", "NN", "NN", "-RRB-", "WDT", "VBD", "DT", "NNP", "CD", "-LRB-", "IN", "PRP", "VBP", "RB", "JJ", "-RRB-", "CC", "PRP", "VBD", "JJ", "NN", "NNS", "."], "head": [3, 3, 0, 7, 7, 7, 3, 9, 3, 11, 9, 15, 15, 15, 9, 9, 18, 7, 21, 18, 18, 27, 27, 27, 27, 27, 18, 27, 31, 31, 18, 34, 34, 31, 3], "deprel": ["nsubj", "advmod", "root", "det", "nummod", "punct", "obj", "punct", "parataxis", "compound", "obj", "cc", "cop", "compound", "conj", "punct", "nsubj", "acl:relcl", "det", "obj", "obj", "punct", "mark", "nsubj", "cop", "advmod", "advcl", "punct", "cc", "nsubj", "conj", "amod", "compound", "obj", "punct"]}, {"id": "105", "sentence": "Oh yea , has no numeric pad on the side .", "triples": [{"uid": "105-0", "sentiment": "negative", "target_tags": "Oh\\O yea\\O ,\\O has\\O no\\O numeric\\B pad\\I on\\O the\\O side\\O .\\O", "opinion_tags": "Oh\\O yea\\O ,\\O has\\O no\\B numeric\\O pad\\O on\\O the\\O side\\O .\\O"}], "postag": ["UH", "UH", ",", "VBZ", "DT", "JJ", "NN", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 10, 10, 4, 4], "deprel": ["discourse", "discourse", "punct", "root", "det", "amod", "obj", "case", "det", "obl", "punct"]}, {"id": "106", "sentence": "More times that not the screen pops up saying I have a bad internet connection , or the page ca n't be displayed .", "triples": [{"uid": "106-0", "sentiment": "negative", "target_tags": "More\\O times\\O that\\O not\\O the\\O screen\\O pops\\O up\\O saying\\O I\\O have\\O a\\O bad\\O internet\\B connection\\I ,\\O or\\O the\\O page\\O ca\\O n't\\O be\\O displayed\\O .\\O", "opinion_tags": "More\\O times\\O that\\O not\\O the\\O screen\\O pops\\O up\\O saying\\O I\\O have\\O a\\O bad\\B internet\\O connection\\O ,\\O or\\O the\\O page\\O ca\\O n't\\O be\\O displayed\\O .\\O"}], "postag": ["JJR", "NNS", "WDT", "RB", "DT", "NN", "VBZ", "RP", "VBG", "PRP", "VBP", "DT", "JJ", "NN", "NN", ",", "CC", "DT", "NN", "MD", "RB", "VB", "VBN", "."], "head": [2, 0, 7, 6, 6, 7, 2, 7, 7, 11, 9, 15, 15, 15, 11, 23, 23, 19, 23, 23, 23, 23, 11, 2], "deprel": ["amod", "root", "obj", "advmod", "det", "nsubj", "acl:relcl", "compound:prt", "advcl", "nsubj", "ccomp", "det", "amod", "compound", "obj", "punct", "cc", "det", "nsubj:pass", "aux", "advmod", "aux:pass", "conj", "punct"]}, {"id": "107", "sentence": "The ease of use is wonderful .", "triples": [{"uid": "107-0", "sentiment": "positive", "target_tags": "The\\O ease\\O of\\O use\\B is\\O wonderful\\O .\\O", "opinion_tags": "The\\O ease\\B of\\O use\\O is\\O wonderful\\O .\\O"}, {"uid": "107-1", "sentiment": "positive", "target_tags": "The\\O ease\\O of\\O use\\B is\\O wonderful\\O .\\O", "opinion_tags": "The\\O ease\\O of\\O use\\O is\\O wonderful\\B .\\O"}], "postag": ["DT", "NN", "IN", "NN", "VBZ", "JJ", "."], "head": [2, 6, 4, 2, 6, 0, 6], "deprel": ["det", "nsubj", "case", "nmod", "cop", "root", "punct"]}, {"id": "108", "sentence": "Best thing is I can use existing 32 bit old programs .", "triples": [{"uid": "108-0", "sentiment": "positive", "target_tags": "Best\\O thing\\O is\\O I\\O can\\O use\\O existing\\O 32\\O bit\\O old\\O programs\\B .\\O", "opinion_tags": "Best\\B thing\\O is\\O I\\O can\\O use\\O existing\\O 32\\O bit\\O old\\O programs\\O .\\O"}], "postag": ["JJS", "NN", "VBZ", "PRP", "MD", "VB", "VBG", "CD", "NN", "JJ", "NNS", "."], "head": [2, 3, 0, 6, 6, 3, 11, 9, 10, 11, 6, 3], "deprel": ["amod", "nsubj", "root", "nsubj", "aux", "ccomp", "amod", "nummod", "obl:npmod", "amod", "obj", "punct"]}, {"id": "109", "sentence": "The only thing I would change about it is the mouse keys .", "triples": [{"uid": "109-0", "sentiment": "negative", "target_tags": "The\\O only\\O thing\\O I\\O would\\O change\\O about\\O it\\O is\\O the\\O mouse\\B keys\\I .\\O", "opinion_tags": "The\\O only\\O thing\\O I\\O would\\O change\\B about\\O it\\O is\\O the\\O mouse\\O keys\\O .\\O"}], "postag": ["DT", "JJ", "NN", "PRP", "MD", "VB", "IN", "PRP", "VBZ", "DT", "NN", "NNS", "."], "head": [3, 3, 12, 6, 6, 3, 8, 6, 12, 12, 12, 0, 12], "deprel": ["det", "amod", "nsubj", "nsubj", "aux", "acl:relcl", "case", "obl", "cop", "det", "compound", "root", "punct"]}, {"id": "110", "sentence": "After that I turned to email in my next vain help to get them to acknowledge that the warranty was still valid .", "triples": [{"uid": "110-0", "sentiment": "neutral", "target_tags": "After\\O that\\O I\\O turned\\O to\\O email\\O in\\O my\\O next\\O vain\\O help\\O to\\O get\\O them\\O to\\O acknowledge\\O that\\O the\\O warranty\\B was\\O still\\O valid\\O .\\O", "opinion_tags": "After\\O that\\O I\\O turned\\O to\\O email\\O in\\O my\\O next\\O vain\\O help\\O to\\O get\\O them\\O to\\O acknowledge\\O that\\O the\\O warranty\\O was\\O still\\O valid\\B .\\O"}], "postag": ["IN", "IN", "PRP", "VBD", "TO", "VB", "IN", "PRP$", "JJ", "JJ", "NN", "TO", "VB", "PRP", "TO", "VB", "IN", "DT", "NN", "VBD", "RB", "JJ", "."], "head": [4, 1, 4, 0, 6, 4, 11, 11, 11, 11, 6, 13, 6, 13, 16, 13, 22, 19, 22, 22, 22, 16, 4], "deprel": ["mark", "fixed", "nsubj", "root", "mark", "xcomp", "case", "nmod:poss", "amod", "amod", "obl", "mark", "advcl", "obj", "mark", "xcomp", "mark", "det", "nsubj", "cop", "advmod", "ccomp", "punct"]}, {"id": "111", "sentence": "They have developed excellent proprietary software for editing video and pictures and I 'm looking forward to utilizing these tools on the regular .", "triples": [{"uid": "111-0", "sentiment": "positive", "target_tags": "They\\O have\\O developed\\O excellent\\O proprietary\\B software\\I for\\O editing\\O video\\O and\\O pictures\\O and\\O I\\O 'm\\O looking\\O forward\\O to\\O utilizing\\O these\\O tools\\O on\\O the\\O regular\\O .\\O", "opinion_tags": "They\\O have\\O developed\\O excellent\\B proprietary\\O software\\O for\\O editing\\O video\\O and\\O pictures\\O and\\O I\\O 'm\\O looking\\O forward\\O to\\O utilizing\\O these\\O tools\\O on\\O the\\O regular\\O .\\O"}], "postag": ["PRP", "VBP", "VBN", "JJ", "JJ", "NN", "IN", "NN", "NN", "CC", "NNS", "CC", "PRP", "VBP", "VBG", "RB", "IN", "VBG", "DT", "NNS", "IN", "DT", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 6, 11, 9, 15, 15, 15, 3, 15, 18, 15, 20, 18, 23, 23, 18, 3], "deprel": ["nsubj", "aux", "root", "amod", "amod", "obj", "case", "compound", "nmod", "cc", "conj", "cc", "nsubj", "aux", "conj", "advmod", "mark", "advcl", "det", "obj", "case", "det", "obl", "punct"]}, {"id": "112", "sentence": "Graphics are clean and sharp , internet interfaces are seamless .", "triples": [{"uid": "112-0", "sentiment": "positive", "target_tags": "Graphics\\B are\\O clean\\O and\\O sharp\\O ,\\O internet\\O interfaces\\O are\\O seamless\\O .\\O", "opinion_tags": "Graphics\\O are\\O clean\\B and\\O sharp\\O ,\\O internet\\O interfaces\\O are\\O seamless\\O .\\O"}, {"uid": "112-1", "sentiment": "positive", "target_tags": "Graphics\\B are\\O clean\\O and\\O sharp\\O ,\\O internet\\O interfaces\\O are\\O seamless\\O .\\O", "opinion_tags": "Graphics\\O are\\O clean\\O and\\O sharp\\B ,\\O internet\\O interfaces\\O are\\O seamless\\O .\\O"}, {"uid": "112-2", "sentiment": "positive", "target_tags": "Graphics\\O are\\O clean\\O and\\O sharp\\O ,\\O internet\\B interfaces\\I are\\O seamless\\O .\\O", "opinion_tags": "Graphics\\O are\\O clean\\O and\\O sharp\\O ,\\O internet\\O interfaces\\O are\\O seamless\\B .\\O"}], "postag": ["NNS", "VBP", "JJ", "CC", "JJ", ",", "NN", "NNS", "VBP", "JJ", "."], "head": [3, 3, 0, 5, 3, 3, 8, 10, 10, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "punct", "compound", "nsubj", "cop", "conj", "punct"]}, {"id": "113", "sentence": "But after using it a couple of weeks , the overall operation is poor .", "triples": [{"uid": "113-0", "sentiment": "negative", "target_tags": "But\\O after\\O using\\O it\\O a\\O couple\\O of\\O weeks\\O ,\\O the\\O overall\\O operation\\B is\\O poor\\O .\\O", "opinion_tags": "But\\O after\\O using\\O it\\O a\\O couple\\O of\\O weeks\\O ,\\O the\\O overall\\O operation\\O is\\O poor\\B .\\O"}], "postag": ["CC", "IN", "VBG", "PRP", "DT", "NN", "IN", "NNS", ",", "DT", "JJ", "NN", "VBZ", "JJ", "."], "head": [14, 3, 14, 3, 6, 3, 8, 6, 14, 12, 12, 14, 14, 0, 14], "deprel": ["cc", "mark", "advcl", "obj", "det", "obl:tmod", "case", "nmod", "punct", "det", "amod", "nsubj", "cop", "root", "punct"]}, {"id": "114", "sentence": "I already have a HP laptop I bought last year that 's standard size .", "triples": [{"uid": "114-0", "sentiment": "neutral", "target_tags": "I\\O already\\O have\\O a\\O HP\\O laptop\\O I\\O bought\\O last\\O year\\O that\\O 's\\O standard\\O size\\B .\\O", "opinion_tags": "I\\O already\\O have\\O a\\O HP\\O laptop\\O I\\O bought\\O last\\O year\\O that\\O 's\\O standard\\B size\\O .\\O"}], "postag": ["PRP", "RB", "VBP", "DT", "NNP", "NN", "PRP", "VBD", "JJ", "NN", "DT", "VBZ", "JJ", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 8, 6, 10, 8, 14, 14, 14, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "compound", "obj", "nsubj", "acl:relcl", "amod", "obl:tmod", "nsubj", "cop", "amod", "parataxis", "punct"]}, {"id": "115", "sentence": "I just plug this into my 22 '' Monitor and the speedy MacOSX performs just as well on this dual-core that my Dell did with Windows 7 with a quad-core .", "triples": [{"uid": "115-0", "sentiment": "positive", "target_tags": "I\\O just\\O plug\\O this\\O into\\O my\\O 22\\O ''\\O Monitor\\O and\\O the\\O speedy\\O MacOSX\\B performs\\O just\\O as\\O well\\O on\\O this\\O dual-core\\O that\\O my\\O Dell\\O did\\O with\\O Windows\\O 7\\O with\\O a\\O quad-core\\O .\\O", "opinion_tags": "I\\O just\\O plug\\O this\\O into\\O my\\O 22\\O ''\\O Monitor\\O and\\O the\\O speedy\\B MacOSX\\O performs\\O just\\O as\\O well\\O on\\O this\\O dual-core\\O that\\O my\\O Dell\\O did\\O with\\O Windows\\O 7\\O with\\O a\\O quad-core\\O .\\O"}, {"uid": "115-1", "sentiment": "positive", "target_tags": "I\\O just\\O plug\\O this\\O into\\O my\\O 22\\O ''\\O Monitor\\O and\\O the\\O speedy\\O MacOSX\\B performs\\O just\\O as\\O well\\O on\\O this\\O dual-core\\O that\\O my\\O Dell\\O did\\O with\\O Windows\\O 7\\O with\\O a\\O quad-core\\O .\\O", "opinion_tags": "I\\O just\\O plug\\O this\\O into\\O my\\O 22\\O ''\\O Monitor\\O and\\O the\\O speedy\\O MacOSX\\O performs\\O just\\O as\\O well\\B on\\O this\\O dual-core\\O that\\O my\\O Dell\\O did\\O with\\O Windows\\O 7\\O with\\O a\\O quad-core\\O .\\O"}], "postag": ["PRP", "RB", "VBP", "DT", "IN", "PRP$", "CD", "''", "NN", "CC", "DT", "JJ", "NNP", "VBZ", "RB", "RB", "RB", "IN", "DT", "NN", "WDT", "PRP$", "NNP", "VBD", "IN", "NNP", "CD", "IN", "DT", "NN", "."], "head": [3, 3, 0, 3, 9, 9, 9, 9, 3, 14, 13, 13, 14, 3, 16, 20, 16, 20, 20, 14, 24, 23, 24, 20, 26, 24, 26, 30, 30, 24, 3], "deprel": ["nsubj", "advmod", "root", "obj", "case", "nmod:poss", "nummod", "punct", "obl", "cc", "det", "amod", "nsubj", "conj", "advmod", "cc", "fixed", "case", "det", "obl", "obj", "nmod:poss", "nsubj", "acl:relcl", "case", "obl", "nummod", "case", "det", "obl", "punct"]}, {"id": "116", "sentence": "Good monitor and performed well .", "triples": [{"uid": "116-0", "sentiment": "positive", "target_tags": "Good\\O monitor\\B and\\O performed\\O well\\O .\\O", "opinion_tags": "Good\\B monitor\\O and\\O performed\\O well\\O .\\O"}, {"uid": "116-1", "sentiment": "positive", "target_tags": "Good\\O monitor\\O and\\O performed\\B well\\O .\\O", "opinion_tags": "Good\\O monitor\\O and\\O performed\\O well\\B .\\O"}], "postag": ["JJ", "NN", "CC", "VBN", "RB", "."], "head": [2, 0, 4, 2, 4, 2], "deprel": ["amod", "root", "cc", "conj", "advmod", "punct"]}, {"id": "117", "sentence": "the mouse pad and buttons are the worst i 've ever seen .", "triples": [{"uid": "117-0", "sentiment": "negative", "target_tags": "the\\O mouse\\B pad\\I and\\O buttons\\O are\\O the\\O worst\\O i\\O 've\\O ever\\O seen\\O .\\O", "opinion_tags": "the\\O mouse\\O pad\\O and\\O buttons\\O are\\O the\\O worst\\B i\\O 've\\O ever\\O seen\\O .\\O"}, {"uid": "117-1", "sentiment": "negative", "target_tags": "the\\O mouse\\O pad\\O and\\O buttons\\B are\\O the\\O worst\\O i\\O 've\\O ever\\O seen\\O .\\O", "opinion_tags": "the\\O mouse\\O pad\\O and\\O buttons\\O are\\O the\\O worst\\B i\\O 've\\O ever\\O seen\\O .\\O"}], "postag": ["DT", "NN", "NN", "CC", "NNS", "VBP", "DT", "JJS", "PRP", "VBP", "RB", "VBN", "."], "head": [3, 3, 8, 5, 3, 8, 8, 0, 12, 12, 12, 8, 8], "deprel": ["det", "compound", "nsubj", "cc", "conj", "cop", "det", "root", "nsubj", "aux", "advmod", "acl:relcl", "punct"]}, {"id": "118", "sentence": "It really is perfect for work and play .", "triples": [{"uid": "118-0", "sentiment": "positive", "target_tags": "It\\O really\\O is\\O perfect\\O for\\O work\\O and\\O play\\B .\\O", "opinion_tags": "It\\O really\\O is\\O perfect\\B for\\O work\\O and\\O play\\O .\\O"}, {"uid": "118-1", "sentiment": "positive", "target_tags": "It\\O really\\O is\\O perfect\\O for\\O work\\B and\\O play\\O .\\O", "opinion_tags": "It\\O really\\O is\\O perfect\\B for\\O work\\O and\\O play\\O .\\O"}], "postag": ["PRP", "RB", "VBZ", "JJ", "IN", "NN", "CC", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 4], "deprel": ["nsubj", "advmod", "cop", "root", "case", "obl", "cc", "conj", "punct"]}, {"id": "119", "sentence": "Eventually my battery would n't charge , so unless I had it plugged in it would n't even power on .", "triples": [{"uid": "119-0", "sentiment": "negative", "target_tags": "Eventually\\O my\\O battery\\B would\\O n't\\O charge\\O ,\\O so\\O unless\\O I\\O had\\O it\\O plugged\\O in\\O it\\O would\\O n't\\O even\\O power\\O on\\O .\\O", "opinion_tags": "Eventually\\O my\\O battery\\O would\\B n't\\I charge\\I ,\\O so\\O unless\\O I\\O had\\O it\\O plugged\\O in\\O it\\O would\\O n't\\O even\\O power\\O on\\O .\\O"}], "postag": ["RB", "PRP$", "NN", "MD", "RB", "VB", ",", "RB", "IN", "PRP", "VBD", "PRP", "VBN", "IN", "PRP", "MD", "RB", "RB", "VB", "RP", "."], "head": [6, 3, 6, 6, 6, 0, 6, 19, 11, 11, 19, 11, 11, 13, 19, 19, 19, 19, 6, 19, 6], "deprel": ["advmod", "nmod:poss", "nsubj", "aux", "advmod", "root", "punct", "advmod", "mark", "nsubj", "advcl", "obj", "xcomp", "obl", "nsubj", "aux", "advmod", "advmod", "conj", "compound:prt", "punct"]}, {"id": "120", "sentence": "My first problem was with the pre-loaded Norton Firewall/Security program .", "triples": [{"uid": "120-0", "sentiment": "negative", "target_tags": "My\\O first\\O problem\\O was\\O with\\O the\\O pre-loaded\\B Norton\\I Firewall/Security\\I program\\I .\\O", "opinion_tags": "My\\O first\\O problem\\B was\\O with\\O the\\O pre-loaded\\O Norton\\O Firewall/Security\\O program\\O .\\O"}], "postag": ["PRP$", "JJ", "NN", "VBD", "IN", "DT", "JJ", "NNP", "NNP", "NN", "."], "head": [3, 3, 10, 10, 10, 10, 10, 9, 10, 0, 10], "deprel": ["nmod:poss", "amod", "nsubj", "cop", "case", "det", "amod", "compound", "compound", "root", "punct"]}, {"id": "121", "sentence": "I had a USB connect but , i ca n't use it because it is not compatible .", "triples": [{"uid": "121-0", "sentiment": "negative", "target_tags": "I\\O had\\O a\\O USB\\B connect\\I but\\O ,\\O i\\O ca\\O n't\\O use\\O it\\O because\\O it\\O is\\O not\\O compatible\\O .\\O", "opinion_tags": "I\\O had\\O a\\O USB\\O connect\\O but\\O ,\\O i\\O ca\\B n't\\I use\\I it\\O because\\O it\\O is\\O not\\O compatible\\O .\\O"}, {"uid": "121-1", "sentiment": "negative", "target_tags": "I\\O had\\O a\\O USB\\B connect\\I but\\O ,\\O i\\O ca\\O n't\\O use\\O it\\O because\\O it\\O is\\O not\\O compatible\\O .\\O", "opinion_tags": "I\\O had\\O a\\O USB\\O connect\\O but\\O ,\\O i\\O ca\\O n't\\O use\\O it\\O because\\O it\\O is\\O not\\B compatible\\I .\\O"}], "postag": ["PRP", "VBD", "DT", "NNP", "NN", "CC", ",", "PRP", "MD", "RB", "VB", "PRP", "IN", "PRP", "VBZ", "RB", "JJ", "."], "head": [2, 0, 5, 5, 2, 11, 11, 11, 11, 11, 2, 11, 17, 17, 17, 17, 11, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "cc", "punct", "nsubj", "aux", "advmod", "conj", "obj", "mark", "nsubj", "cop", "advmod", "advcl", "punct"]}, {"id": "122", "sentence": "It is easy to use , good quality and good price .", "triples": [{"uid": "122-0", "sentiment": "positive", "target_tags": "It\\O is\\O easy\\O to\\O use\\O ,\\O good\\O quality\\B and\\O good\\O price\\O .\\O", "opinion_tags": "It\\O is\\O easy\\O to\\O use\\O ,\\O good\\B quality\\O and\\O good\\O price\\O .\\O"}, {"uid": "122-1", "sentiment": "positive", "target_tags": "It\\O is\\O easy\\O to\\O use\\O ,\\O good\\O quality\\O and\\O good\\O price\\B .\\O", "opinion_tags": "It\\O is\\O easy\\O to\\O use\\O ,\\O good\\O quality\\O and\\O good\\B price\\O .\\O"}, {"uid": "122-2", "sentiment": "positive", "target_tags": "It\\O is\\O easy\\O to\\O use\\B ,\\O good\\O quality\\O and\\O good\\O price\\O .\\O", "opinion_tags": "It\\O is\\O easy\\B to\\O use\\O ,\\O good\\O quality\\O and\\O good\\O price\\O .\\O"}], "postag": ["PRP", "VBZ", "JJ", "TO", "VB", ",", "JJ", "NN", "CC", "JJ", "NN", "."], "head": [3, 3, 0, 5, 3, 8, 8, 5, 11, 11, 8, 3], "deprel": ["expl", "cop", "root", "mark", "csubj", "punct", "amod", "obj", "cc", "amod", "conj", "punct"]}, {"id": "123", "sentence": "After 2 months of complaints , <PERSON><PERSON> finally sent the right power supply to my techies .", "triples": [{"uid": "123-0", "sentiment": "neutral", "target_tags": "After\\O 2\\O months\\O of\\O complaints\\O ,\\O Asus\\O finally\\O sent\\O the\\O right\\O power\\B supply\\I to\\O my\\O techies\\O .\\O", "opinion_tags": "After\\O 2\\O months\\O of\\O complaints\\O ,\\O Asus\\O finally\\O sent\\O the\\O right\\B power\\O supply\\O to\\O my\\O techies\\O .\\O"}], "postag": ["IN", "CD", "NNS", "IN", "NNS", ",", "NNP", "RB", "VBD", "DT", "JJ", "NN", "NN", "IN", "PRP$", "NNS", "."], "head": [3, 3, 9, 5, 3, 9, 9, 9, 0, 13, 13, 13, 9, 16, 16, 9, 9], "deprel": ["case", "nummod", "obl", "case", "nmod", "punct", "nsubj", "advmod", "root", "det", "amod", "compound", "obj", "case", "nmod:poss", "obl", "punct"]}, {"id": "124", "sentence": "That included the extra Sony Sonic Stage software , the speakers and the subwoofer I got ( that WAS worth the money ) , the bluetooth mouse for my supposedly bluetooth enabled computer , the extended life battery and the Docking port .", "triples": [{"uid": "124-0", "sentiment": "neutral", "target_tags": "That\\O included\\O the\\O extra\\O Sony\\B Sonic\\I Stage\\I software\\I ,\\O the\\O speakers\\O and\\O the\\O subwoofer\\O I\\O got\\O (\\O that\\O WAS\\O worth\\O the\\O money\\O )\\O ,\\O the\\O bluetooth\\O mouse\\O for\\O my\\O supposedly\\O bluetooth\\O enabled\\O computer\\O ,\\O the\\O extended\\O life\\O battery\\O and\\O the\\O Docking\\O port\\O .\\O", "opinion_tags": "That\\O included\\O the\\O extra\\B Sony\\O Sonic\\O Stage\\O software\\O ,\\O the\\O speakers\\O and\\O the\\O subwoofer\\O I\\O got\\O (\\O that\\O WAS\\O worth\\O the\\O money\\O )\\O ,\\O the\\O bluetooth\\O mouse\\O for\\O my\\O supposedly\\O bluetooth\\O enabled\\O computer\\O ,\\O the\\O extended\\O life\\O battery\\O and\\O the\\O Docking\\O port\\O .\\O"}, {"uid": "124-1", "sentiment": "positive", "target_tags": "That\\O included\\O the\\O extra\\O Sony\\O Sonic\\O Stage\\O software\\O ,\\O the\\O speakers\\B and\\O the\\O subwoofer\\O I\\O got\\O (\\O that\\O WAS\\O worth\\O the\\O money\\O )\\O ,\\O the\\O bluetooth\\O mouse\\O for\\O my\\O supposedly\\O bluetooth\\O enabled\\O computer\\O ,\\O the\\O extended\\O life\\O battery\\O and\\O the\\O Docking\\O port\\O .\\O", "opinion_tags": "That\\O included\\O the\\O extra\\O Sony\\O Sonic\\O Stage\\O software\\O ,\\O the\\O speakers\\O and\\O the\\O subwoofer\\O I\\O got\\O (\\O that\\O WAS\\O worth\\B the\\O money\\O )\\O ,\\O the\\O bluetooth\\O mouse\\O for\\O my\\O supposedly\\O bluetooth\\O enabled\\O computer\\O ,\\O the\\O extended\\O life\\O battery\\O and\\O the\\O Docking\\O port\\O .\\O"}, {"uid": "124-2", "sentiment": "positive", "target_tags": "That\\O included\\O the\\O extra\\O Sony\\O Sonic\\O Stage\\O software\\O ,\\O the\\O speakers\\O and\\O the\\O subwoofer\\B I\\O got\\O (\\O that\\O WAS\\O worth\\O the\\O money\\O )\\O ,\\O the\\O bluetooth\\O mouse\\O for\\O my\\O supposedly\\O bluetooth\\O enabled\\O computer\\O ,\\O the\\O extended\\O life\\O battery\\O and\\O the\\O Docking\\O port\\O .\\O", "opinion_tags": "That\\O included\\O the\\O extra\\O Sony\\O Sonic\\O Stage\\O software\\O ,\\O the\\O speakers\\O and\\O the\\O subwoofer\\O I\\O got\\O (\\O that\\O WAS\\O worth\\B the\\O money\\O )\\O ,\\O the\\O bluetooth\\O mouse\\O for\\O my\\O supposedly\\O bluetooth\\O enabled\\O computer\\O ,\\O the\\O extended\\O life\\O battery\\O and\\O the\\O Docking\\O port\\O .\\O"}], "postag": ["DT", "VBD", "DT", "JJ", "NNP", "NNP", "NNP", "NN", ",", "DT", "NNS", "CC", "DT", "NN", "PRP", "VBD", "-LRB-", "DT", "VBD", "JJ", "DT", "NN", "-RRB-", ",", "DT", "NN", "NN", "IN", "PRP$", "RB", "NN", "VBN", "NN", ",", "DT", "VBN", "NN", "NN", "CC", "DT", "NN", "NN", "."], "head": [2, 0, 8, 8, 8, 7, 8, 2, 11, 11, 8, 14, 14, 8, 16, 14, 20, 20, 20, 8, 22, 20, 20, 27, 27, 27, 8, 33, 33, 31, 33, 33, 27, 38, 38, 38, 38, 8, 42, 42, 42, 8, 2], "deprel": ["nsubj", "root", "det", "amod", "compound", "compound", "compound", "obj", "punct", "det", "conj", "cc", "det", "conj", "nsubj", "acl:relcl", "punct", "nsubj", "cop", "parataxis", "det", "obj", "punct", "punct", "det", "compound", "conj", "case", "nmod:poss", "advmod", "compound", "amod", "nmod", "punct", "det", "amod", "compound", "conj", "cc", "det", "compound", "conj", "punct"]}, {"id": "125", "sentence": "also the keyboard does not liht up so unless your sitting in a room with some light you cant see anything and thats bad for me because my boyfriend tends to watch tv in the dark at night which leaves me with no way of seeing the keyboard .", "triples": [{"uid": "125-0", "sentiment": "negative", "target_tags": "also\\O the\\O keyboard\\B does\\O not\\O liht\\O up\\O so\\O unless\\O your\\O sitting\\O in\\O a\\O room\\O with\\O some\\O light\\O you\\O cant\\O see\\O anything\\O and\\O thats\\O bad\\O for\\O me\\O because\\O my\\O boyfriend\\O tends\\O to\\O watch\\O tv\\O in\\O the\\O dark\\O at\\O night\\O which\\O leaves\\O me\\O with\\O no\\O way\\O of\\O seeing\\O the\\O keyboard\\O .\\O", "opinion_tags": "also\\O the\\O keyboard\\O does\\O not\\O liht\\O up\\O so\\O unless\\O your\\O sitting\\O in\\O a\\O room\\O with\\O some\\O light\\O you\\O cant\\O see\\O anything\\O and\\O thats\\O bad\\B for\\O me\\O because\\O my\\O boyfriend\\O tends\\O to\\O watch\\O tv\\O in\\O the\\O dark\\O at\\O night\\O which\\O leaves\\O me\\O with\\O no\\O way\\O of\\O seeing\\O the\\O keyboard\\O .\\O"}], "postag": ["RB", "DT", "NN", "VBZ", "RB", "VB", "RP", "RB", "IN", "PRP$", "NN", "IN", "DT", "NN", "IN", "DT", "NN", "PRP", "MD", "VB", "NN", "CC", "RB", "JJ", "IN", "PRP", "IN", "PRP$", "NN", "VBZ", "TO", "VB", "NN", "IN", "DT", "NN", "IN", "NN", "WDT", "VBZ", "PRP", "IN", "DT", "NN", "IN", "VBG", "DT", "NN", "."], "head": [6, 3, 6, 6, 6, 0, 6, 20, 20, 11, 20, 14, 14, 11, 17, 17, 14, 20, 20, 6, 20, 24, 24, 21, 26, 24, 30, 29, 30, 20, 32, 30, 32, 36, 36, 32, 38, 32, 40, 32, 40, 44, 44, 40, 46, 44, 48, 46, 6], "deprel": ["advmod", "det", "nsubj", "aux", "advmod", "root", "compound:prt", "mark", "mark", "nmod:poss", "nsubj", "case", "det", "nmod", "case", "det", "nmod", "nsubj", "aux", "advcl", "obj", "cc", "advmod", "conj", "case", "obl", "mark", "nmod:poss", "nsubj", "advcl", "mark", "xcomp", "obj", "case", "det", "obl", "case", "obl", "nsubj", "parataxis", "obj", "case", "det", "obl", "mark", "acl", "det", "obj", "punct"]}, {"id": "126", "sentence": "I agree with the previous comment that ASUS TECH SUPPORT IS HORRIBLE WHICH IS A CON IN MY OPINION .", "triples": [{"uid": "126-0", "sentiment": "negative", "target_tags": "I\\O agree\\O with\\O the\\O previous\\O comment\\O that\\O ASUS\\B TECH\\I SUPPORT\\I IS\\O HORRIBLE\\O WHICH\\O IS\\O A\\O CON\\O IN\\O MY\\O OPINION\\O .\\O", "opinion_tags": "I\\O agree\\O with\\O the\\O previous\\O comment\\O that\\O ASUS\\O TECH\\O SUPPORT\\O IS\\O HORRIBLE\\B WHICH\\O IS\\O A\\O CON\\O IN\\O MY\\O OPINION\\O .\\O"}], "postag": ["PRP", "VBP", "IN", "DT", "JJ", "NN", "IN", "NNP", "NNP", "NNP", "VBZ", "JJ", "WDT", "VBZ", "DT", "NN", "IN", "PRP$", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 12, 10, 10, 12, 12, 6, 16, 16, 16, 12, 19, 19, 16, 2], "deprel": ["nsubj", "root", "case", "det", "amod", "obl", "mark", "compound", "compound", "nsubj", "cop", "acl", "nsubj", "cop", "det", "parataxis", "case", "nmod:poss", "nmod", "punct"]}, {"id": "127", "sentence": "I would recommend this computer to anyone searching for the perfect laptop , and the battery life is amazing .", "triples": [{"uid": "127-0", "sentiment": "positive", "target_tags": "I\\O would\\O recommend\\O this\\O computer\\O to\\O anyone\\O searching\\O for\\O the\\O perfect\\O laptop\\O ,\\O and\\O the\\O battery\\B life\\I is\\O amazing\\O .\\O", "opinion_tags": "I\\O would\\O recommend\\O this\\O computer\\O to\\O anyone\\O searching\\O for\\O the\\O perfect\\O laptop\\O ,\\O and\\O the\\O battery\\O life\\O is\\O amazing\\B .\\O"}], "postag": ["PRP", "MD", "VB", "DT", "NN", "IN", "NN", "VBG", "IN", "DT", "JJ", "NN", ",", "CC", "DT", "NN", "NN", "VBZ", "JJ", "."], "head": [3, 3, 0, 5, 3, 7, 3, 7, 12, 12, 12, 8, 19, 19, 17, 17, 19, 19, 3, 3], "deprel": ["nsubj", "aux", "root", "det", "obj", "case", "obl", "acl", "case", "det", "amod", "obl", "punct", "cc", "det", "compound", "nsubj", "cop", "conj", "punct"]}, {"id": "128", "sentence": "Not easy to carry .", "triples": [{"uid": "128-0", "sentiment": "negative", "target_tags": "Not\\O easy\\O to\\O carry\\B .\\O", "opinion_tags": "Not\\O easy\\B to\\O carry\\O .\\O"}], "postag": ["RB", "JJ", "TO", "VB", "."], "head": [2, 0, 4, 2, 2], "deprel": ["advmod", "root", "mark", "csubj", "punct"]}, {"id": "129", "sentence": "Summary : I 've had this laptop for 2 months , out of the blue the power adapter stops working .", "triples": [{"uid": "129-0", "sentiment": "negative", "target_tags": "Summary\\O :\\O I\\O 've\\O had\\O this\\O laptop\\O for\\O 2\\O months\\O ,\\O out\\O of\\O the\\O blue\\O the\\O power\\B adapter\\I stops\\O working\\O .\\O", "opinion_tags": "Summary\\O :\\O I\\O 've\\O had\\O this\\O laptop\\O for\\O 2\\O months\\O ,\\O out\\O of\\O the\\O blue\\O the\\O power\\O adapter\\O stops\\B working\\I .\\O"}], "postag": ["NN", ":", "PRP", "VBP", "VBN", "DT", "NN", "IN", "CD", "NNS", ",", "IN", "IN", "DT", "NN", "DT", "NN", "NN", "VBZ", "VBG", "."], "head": [0, 1, 5, 5, 1, 7, 5, 10, 10, 5, 5, 15, 15, 15, 5, 18, 18, 19, 5, 19, 1], "deprel": ["root", "punct", "nsubj", "aux", "appos", "det", "obj", "case", "nummod", "obl", "punct", "case", "case", "det", "obl", "det", "compound", "nsubj", "parataxis", "xcomp", "punct"]}, {"id": "130", "sentence": "Of course , for a student , weight is always an issue .", "triples": [{"uid": "130-0", "sentiment": "neutral", "target_tags": "Of\\O course\\O ,\\O for\\O a\\O student\\O ,\\O weight\\B is\\O always\\O an\\O issue\\O .\\O", "opinion_tags": "Of\\O course\\O ,\\O for\\O a\\O student\\O ,\\O weight\\O is\\O always\\O an\\O issue\\B .\\O"}], "postag": ["RB", "RB", ",", "IN", "DT", "NN", ",", "NN", "VBZ", "RB", "DT", "NN", "."], "head": [12, 1, 12, 6, 6, 12, 12, 12, 12, 12, 12, 0, 12], "deprel": ["advmod", "fixed", "punct", "case", "det", "obl", "punct", "nsubj", "cop", "advmod", "det", "root", "punct"]}, {"id": "131", "sentence": "2.The wireless card is low quality .", "triples": [{"uid": "131-0", "sentiment": "negative", "target_tags": "2.The\\O wireless\\B card\\I is\\O low\\O quality\\O .\\O", "opinion_tags": "2.The\\O wireless\\O card\\O is\\O low\\B quality\\I .\\O"}], "postag": ["DT", "JJ", "NN", "VBZ", "JJ", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 6], "deprel": ["det", "amod", "nsubj", "cop", "amod", "root", "punct"]}, {"id": "132", "sentence": "They offer the best warranty in the business , and do n't 3rd party it out like Toshiba .", "triples": [{"uid": "132-0", "sentiment": "positive", "target_tags": "They\\O offer\\O the\\O best\\O warranty\\B in\\O the\\O business\\O ,\\O and\\O do\\O n't\\O 3rd\\O party\\O it\\O out\\O like\\O Toshiba\\O .\\O", "opinion_tags": "They\\O offer\\O the\\O best\\B warranty\\O in\\O the\\O business\\O ,\\O and\\O do\\O n't\\O 3rd\\O party\\O it\\O out\\O like\\O Toshiba\\O .\\O"}], "postag": ["PRP", "VBP", "DT", "JJS", "NN", "IN", "DT", "NN", ",", "CC", "VBP", "RB", "RB", "VB", "PRP", "RP", "IN", "NNP", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 14, 14, 14, 14, 14, 2, 14, 14, 18, 14, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "case", "det", "obl", "punct", "cc", "aux", "advmod", "advmod", "conj", "obj", "compound:prt", "case", "obl", "punct"]}, {"id": "133", "sentence": "The only problems are the sound isnt very loud I have to wear headphones .", "triples": [{"uid": "133-0", "sentiment": "negative", "target_tags": "The\\O only\\O problems\\O are\\O the\\O sound\\B isnt\\O very\\O loud\\O I\\O have\\O to\\O wear\\O headphones\\O .\\O", "opinion_tags": "The\\O only\\O problems\\O are\\O the\\O sound\\O isnt\\B very\\I loud\\I I\\O have\\O to\\O wear\\O headphones\\O .\\O"}], "postag": ["DT", "JJ", "NNS", "VBP", "DT", "NN", "VBZ", "RB", "JJ", "PRP", "VBP", "TO", "VB", "NNS", "."], "head": [3, 3, 6, 6, 6, 0, 9, 9, 6, 11, 9, 13, 11, 13, 6], "deprel": ["det", "amod", "nsubj", "cop", "det", "root", "cop", "advmod", "acl:relcl", "nsubj", "parataxis", "mark", "xcomp", "obj", "punct"]}, {"id": "134", "sentence": "There are no viruses or spyware to worry about like on a Windows computer .", "triples": [{"uid": "134-0", "sentiment": "negative", "target_tags": "There\\O are\\O no\\O viruses\\O or\\O spyware\\O to\\O worry\\O about\\O like\\O on\\O a\\O Windows\\B computer\\O .\\O", "opinion_tags": "There\\O are\\O no\\O viruses\\O or\\O spyware\\O to\\O worry\\B about\\I like\\O on\\O a\\O Windows\\O computer\\O .\\O"}], "postag": ["EX", "VBP", "DT", "NNS", "CC", "NN", "TO", "VB", "IN", "IN", "IN", "DT", "NN", "NN", "."], "head": [2, 0, 4, 2, 6, 4, 8, 4, 8, 8, 14, 14, 14, 8, 2], "deprel": ["expl", "root", "det", "nsubj", "cc", "conj", "mark", "acl", "obl", "obl", "case", "det", "compound", "obl", "punct"]}, {"id": "135", "sentence": "while about 8 years ago , I hope that the quality has changed .", "triples": [{"uid": "135-0", "sentiment": "negative", "target_tags": "while\\O about\\O 8\\O years\\O ago\\O ,\\O I\\O hope\\O that\\O the\\O quality\\B has\\O changed\\O .\\O", "opinion_tags": "while\\O about\\O 8\\O years\\O ago\\O ,\\O I\\O hope\\B that\\O the\\O quality\\O has\\O changed\\O .\\O"}, {"uid": "135-1", "sentiment": "negative", "target_tags": "while\\O about\\O 8\\O years\\O ago\\O ,\\O I\\O hope\\O that\\O the\\O quality\\B has\\O changed\\O .\\O", "opinion_tags": "while\\O about\\O 8\\O years\\O ago\\O ,\\O I\\O hope\\O that\\O the\\O quality\\O has\\O changed\\B .\\O"}], "postag": ["IN", "RB", "CD", "NNS", "RB", ",", "PRP", "VBP", "IN", "DT", "NN", "VBZ", "VBN", "."], "head": [5, 3, 4, 5, 8, 8, 8, 0, 13, 11, 13, 13, 8, 8], "deprel": ["mark", "advmod", "nummod", "obl:npmod", "advcl", "punct", "nsubj", "root", "mark", "det", "nsubj", "aux", "ccomp", "punct"]}, {"id": "136", "sentence": "The difference is it 's a whole lot of fun using the laptop now , still learning the Apple navigation , but is fun and comes with a lot of cool apps .", "triples": [{"uid": "136-0", "sentiment": "neutral", "target_tags": "The\\O difference\\O is\\O it\\O 's\\O a\\O whole\\O lot\\O of\\O fun\\O using\\O the\\O laptop\\O now\\O ,\\O still\\O learning\\O the\\O Apple\\B navigation\\I ,\\O but\\O is\\O fun\\O and\\O comes\\O with\\O a\\O lot\\O of\\O cool\\O apps\\O .\\O", "opinion_tags": "The\\O difference\\O is\\O it\\O 's\\O a\\O whole\\O lot\\O of\\O fun\\O using\\O the\\O laptop\\O now\\O ,\\O still\\O learning\\O the\\O Apple\\O navigation\\O ,\\O but\\O is\\O fun\\B and\\O comes\\O with\\O a\\O lot\\O of\\O cool\\O apps\\O .\\O"}, {"uid": "136-1", "sentiment": "positive", "target_tags": "The\\O difference\\O is\\O it\\O 's\\O a\\O whole\\O lot\\O of\\O fun\\O using\\O the\\O laptop\\O now\\O ,\\O still\\O learning\\O the\\O Apple\\O navigation\\O ,\\O but\\O is\\O fun\\O and\\O comes\\O with\\O a\\O lot\\O of\\O cool\\O apps\\B .\\O", "opinion_tags": "The\\O difference\\O is\\O it\\O 's\\O a\\O whole\\O lot\\O of\\O fun\\O using\\O the\\O laptop\\O now\\O ,\\O still\\O learning\\O the\\O Apple\\O navigation\\O ,\\O but\\O is\\O fun\\O and\\O comes\\O with\\O a\\O lot\\O of\\O cool\\B apps\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "PRP", "VBZ", "DT", "JJ", "NN", "IN", "NN", "VBG", "DT", "NN", "RB", ",", "RB", "VBG", "DT", "NNP", "NN", ",", "CC", "VBZ", "JJ", "CC", "VBZ", "IN", "DT", "NN", "IN", "JJ", "NNS", "."], "head": [2, 3, 0, 8, 8, 8, 8, 3, 10, 8, 8, 13, 11, 11, 17, 17, 11, 20, 20, 17, 24, 24, 24, 17, 26, 24, 29, 29, 26, 32, 32, 29, 3], "deprel": ["det", "nsubj", "root", "nsubj", "cop", "det", "amod", "ccomp", "case", "nmod", "acl", "det", "obj", "advmod", "punct", "advmod", "conj", "det", "compound", "obj", "punct", "cc", "cop", "conj", "cc", "conj", "case", "det", "obl", "case", "amod", "nmod", "punct"]}, {"id": "137", "sentence": "The display is awesome .", "triples": [{"uid": "137-0", "sentiment": "positive", "target_tags": "The\\O display\\B is\\O awesome\\O .\\O", "opinion_tags": "The\\O display\\O is\\O awesome\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"]}, {"id": "138", "sentence": "The speakers on it are useless too .", "triples": [{"uid": "138-0", "sentiment": "negative", "target_tags": "The\\O speakers\\B on\\O it\\O are\\O useless\\O too\\O .\\O", "opinion_tags": "The\\O speakers\\O on\\O it\\O are\\O useless\\B too\\O .\\O"}], "postag": ["DT", "NNS", "IN", "PRP", "VBP", "JJ", "RB", "."], "head": [2, 6, 4, 2, 6, 0, 6, 6], "deprel": ["det", "nsubj", "case", "nmod", "cop", "root", "advmod", "punct"]}, {"id": "139", "sentence": "In early May I got it back and this time I only had it back for 1 day before it had a NEW issue so it was sent back in for the 6th time they `` expedited '' the repairs so I was only supposed to have to be without it for 3 days and it was supposed to be fixed , by a `` Senior Tech '' .", "triples": [{"uid": "139-0", "sentiment": "negative", "target_tags": "In\\O early\\O May\\O I\\O got\\O it\\O back\\O and\\O this\\O time\\O I\\O only\\O had\\O it\\O back\\O for\\O 1\\O day\\O before\\O it\\O had\\O a\\O NEW\\O issue\\O so\\O it\\O was\\O sent\\O back\\O in\\O for\\O the\\O 6th\\O time\\O they\\O ``\\O expedited\\O ''\\O the\\O repairs\\O so\\O I\\O was\\O only\\O supposed\\O to\\O have\\O to\\O be\\O without\\O it\\O for\\O 3\\O days\\O and\\O it\\O was\\O supposed\\O to\\O be\\O fixed\\O ,\\O by\\O a\\O ``\\O Senior\\B Tech\\I ''\\O .\\O", "opinion_tags": "In\\O early\\O May\\O I\\O got\\O it\\O back\\O and\\O this\\O time\\O I\\O only\\O had\\O it\\O back\\O for\\O 1\\O day\\O before\\O it\\O had\\O a\\O NEW\\O issue\\O so\\O it\\O was\\O sent\\O back\\O in\\O for\\O the\\O 6th\\O time\\O they\\O ``\\O expedited\\O ''\\O the\\O repairs\\O so\\O I\\O was\\O only\\O supposed\\O to\\O have\\O to\\O be\\O without\\O it\\O for\\O 3\\O days\\O and\\O it\\O was\\O supposed\\B to\\I be\\I fixed\\I ,\\O by\\O a\\O ``\\O Senior\\O Tech\\O ''\\O .\\O"}], "postag": ["IN", "JJ", "NNP", "PRP", "VBD", "PRP", "RB", "CC", "DT", "NN", "PRP", "RB", "VBD", "PRP", "RB", "IN", "CD", "NN", "IN", "PRP", "VBD", "DT", "JJ", "NN", "RB", "PRP", "VBD", "VBN", "RB", "RB", "IN", "DT", "JJ", "NN", "PRP", "VBP", "VBN", "''", "DT", "NNS", "RB", "PRP", "VBD", "RB", "VBN", "TO", "VB", "TO", "VB", "IN", "PRP", "IN", "CD", "NNS", "CC", "PRP", "VBD", "VBN", "TO", "VB", "VBN", ",", "IN", "DT", "``", "JJ", "NNP", "''", "."], "head": [3, 3, 5, 5, 0, 5, 5, 13, 10, 13, 13, 13, 5, 13, 13, 18, 18, 13, 21, 21, 13, 24, 24, 21, 28, 28, 28, 5, 28, 28, 34, 34, 34, 28, 37, 37, 5, 37, 40, 37, 45, 45, 45, 45, 5, 47, 45, 51, 51, 51, 47, 54, 54, 47, 58, 58, 58, 45, 61, 61, 58, 58, 67, 67, 67, 67, 61, 67, 5], "deprel": ["case", "amod", "obl", "nsubj", "root", "obj", "advmod", "cc", "det", "obl:tmod", "nsubj", "advmod", "conj", "obj", "advmod", "case", "nummod", "obl", "mark", "nsubj", "advcl", "det", "amod", "obj", "advmod", "nsubj:pass", "aux:pass", "parataxis", "advmod", "advmod", "case", "det", "amod", "obl", "nsubj", "aux", "parataxis", "punct", "det", "obj", "advmod", "nsubj", "aux", "advmod", "parataxis", "mark", "xcomp", "mark", "cop", "case", "xcomp", "case", "nummod", "obl", "cc", "nsubj:pass", "aux:pass", "conj", "mark", "aux:pass", "xcomp", "punct", "case", "det", "punct", "amod", "obl", "punct", "punct"]}, {"id": "140", "sentence": "Adjust the sensitivity since it 's not that responsive to begin with .", "triples": [{"uid": "140-0", "sentiment": "negative", "target_tags": "Adjust\\O the\\O sensitivity\\B since\\O it\\O 's\\O not\\O that\\O responsive\\O to\\O begin\\O with\\O .\\O", "opinion_tags": "Adjust\\O the\\O sensitivity\\O since\\O it\\O 's\\O not\\B that\\I responsive\\I to\\O begin\\O with\\O .\\O"}], "postag": ["VB", "DT", "NN", "IN", "PRP", "VBZ", "RB", "RB", "JJ", "TO", "VB", "IN", "."], "head": [0, 3, 1, 9, 9, 9, 9, 9, 1, 11, 9, 11, 1], "deprel": ["root", "det", "obj", "mark", "nsubj", "cop", "advmod", "advmod", "advcl", "mark", "ccomp", "obl", "punct"]}, {"id": "141", "sentence": "Also , I have had a lot of trouble with the shift key to go to other lines .", "triples": [{"uid": "141-0", "sentiment": "negative", "target_tags": "Also\\O ,\\O I\\O have\\O had\\O a\\O lot\\O of\\O trouble\\O with\\O the\\O shift\\B key\\I to\\O go\\O to\\O other\\O lines\\O .\\O", "opinion_tags": "Also\\O ,\\O I\\O have\\O had\\O a\\O lot\\O of\\O trouble\\B with\\O the\\O shift\\O key\\O to\\O go\\O to\\O other\\O lines\\O .\\O"}], "postag": ["RB", ",", "PRP", "VBP", "VBN", "DT", "NN", "IN", "NN", "IN", "DT", "NN", "NN", "TO", "VB", "IN", "JJ", "NNS", "."], "head": [5, 5, 5, 5, 0, 7, 5, 9, 7, 13, 13, 13, 5, 15, 5, 18, 18, 15, 5], "deprel": ["advmod", "punct", "nsubj", "aux", "root", "det", "obj", "case", "nmod", "case", "det", "compound", "obl", "mark", "advcl", "case", "amod", "obl", "punct"]}, {"id": "142", "sentence": "iLife is easily compatible with Microsoft Office so you can send and receive files from a PC .", "triples": [{"uid": "142-0", "sentiment": "positive", "target_tags": "iLife\\B is\\O easily\\O compatible\\O with\\O Microsoft\\O Office\\O so\\O you\\O can\\O send\\O and\\O receive\\O files\\O from\\O a\\O PC\\O .\\O", "opinion_tags": "iLife\\O is\\O easily\\B compatible\\I with\\O Microsoft\\O Office\\O so\\O you\\O can\\O send\\O and\\O receive\\O files\\O from\\O a\\O PC\\O .\\O"}], "postag": ["NNP", "VBZ", "RB", "JJ", "IN", "NNP", "NNP", "RB", "PRP", "MD", "VB", "CC", "VB", "NNS", "IN", "DT", "NNP", "."], "head": [4, 4, 4, 0, 7, 7, 4, 11, 11, 11, 4, 13, 11, 11, 17, 17, 14, 4], "deprel": ["nsubj", "cop", "advmod", "root", "case", "compound", "obl", "advmod", "nsubj", "aux", "advcl", "cc", "conj", "obj", "case", "det", "nmod", "punct"]}, {"id": "143", "sentence": "Fully charged , the MacBook Pro can last about five hours unplugged .", "triples": [{"uid": "143-0", "sentiment": "positive", "target_tags": "Fully\\O charged\\B ,\\O the\\O MacBook\\O Pro\\O can\\O last\\O about\\O five\\O hours\\O unplugged\\O .\\O", "opinion_tags": "Fully\\B charged\\O ,\\O the\\O MacBook\\O Pro\\O can\\O last\\O about\\O five\\O hours\\O unplugged\\O .\\O"}], "postag": ["RB", "VBN", ",", "DT", "NNP", "NNP", "MD", "VB", "RB", "CD", "NNS", "JJ", "."], "head": [2, 8, 8, 6, 6, 8, 8, 0, 10, 11, 12, 8, 8], "deprel": ["advmod", "advcl", "punct", "det", "compound", "nsubj", "aux", "root", "advmod", "nummod", "obl:npmod", "advmod", "punct"]}, {"id": "144", "sentence": "Also the display is exceptional !", "triples": [{"uid": "144-0", "sentiment": "positive", "target_tags": "Also\\O the\\O display\\B is\\O exceptional\\O !\\O", "opinion_tags": "Also\\O the\\O display\\O is\\O exceptional\\B !\\O"}], "postag": ["RB", "DT", "NN", "VBZ", "JJ", "."], "head": [5, 3, 5, 5, 0, 5], "deprel": ["advmod", "det", "nsubj", "cop", "root", "punct"]}, {"id": "145", "sentence": "Your cursor will end up all over the freaking place , , , it 's not uncommon for me to accidentally delete words , sentences , paragraphs because of this mousepad .", "triples": [{"uid": "145-0", "sentiment": "negative", "target_tags": "Your\\O cursor\\B will\\O end\\O up\\O all\\O over\\O the\\O freaking\\O place\\O ,\\O ,\\O ,\\O it\\O 's\\O not\\O uncommon\\O for\\O me\\O to\\O accidentally\\O delete\\O words\\O ,\\O sentences\\O ,\\O paragraphs\\O because\\O of\\O this\\O mousepad\\O .\\O", "opinion_tags": "Your\\O cursor\\O will\\O end\\B up\\I all\\O over\\O the\\O freaking\\O place\\O ,\\O ,\\O ,\\O it\\O 's\\O not\\O uncommon\\O for\\O me\\O to\\O accidentally\\O delete\\O words\\O ,\\O sentences\\O ,\\O paragraphs\\O because\\O of\\O this\\O mousepad\\O .\\O"}, {"uid": "145-1", "sentiment": "negative", "target_tags": "Your\\O cursor\\B will\\O end\\O up\\O all\\O over\\O the\\O freaking\\O place\\O ,\\O ,\\O ,\\O it\\O 's\\O not\\O uncommon\\O for\\O me\\O to\\O accidentally\\O delete\\O words\\O ,\\O sentences\\O ,\\O paragraphs\\O because\\O of\\O this\\O mousepad\\O .\\O", "opinion_tags": "Your\\O cursor\\O will\\O end\\O up\\O all\\O over\\O the\\O freaking\\B place\\O ,\\O ,\\O ,\\O it\\O 's\\O not\\O uncommon\\O for\\O me\\O to\\O accidentally\\O delete\\O words\\O ,\\O sentences\\O ,\\O paragraphs\\O because\\O of\\O this\\O mousepad\\O .\\O"}], "postag": ["PRP$", "NN", "MD", "VB", "RP", "RB", "IN", "DT", "VBG", "NN", ",", ",", ",", "PRP", "VBZ", "RB", "JJ", "IN", "PRP", "TO", "RB", "VB", "NNS", ",", "NNS", ",", "NNS", "IN", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 4, 10, 10, 10, 10, 4, 4, 4, 4, 17, 17, 17, 4, 22, 22, 22, 22, 17, 22, 25, 23, 27, 23, 31, 28, 31, 22, 4], "deprel": ["nmod:poss", "nsubj", "aux", "root", "compound:prt", "advmod", "case", "det", "amod", "obl", "punct", "punct", "punct", "expl", "cop", "advmod", "parataxis", "mark", "nsubj", "mark", "advmod", "csubj", "obj", "punct", "conj", "punct", "conj", "case", "fixed", "det", "obl", "punct"]}, {"id": "146", "sentence": "Then I 've fixed the DC jack ( inside the unit ) , rewired the DC jack to the OUTside of the laptop , replaced the power brick .", "triples": [{"uid": "146-0", "sentiment": "neutral", "target_tags": "Then\\O I\\O 've\\O fixed\\O the\\O DC\\B jack\\I (\\O inside\\O the\\O unit\\O )\\O ,\\O rewired\\O the\\O DC\\O jack\\O to\\O the\\O OUTside\\O of\\O the\\O laptop\\O ,\\O replaced\\O the\\O power\\O brick\\O .\\O", "opinion_tags": "Then\\O I\\O 've\\O fixed\\B the\\O DC\\O jack\\O (\\O inside\\O the\\O unit\\O )\\O ,\\O rewired\\O the\\O DC\\O jack\\O to\\O the\\O OUTside\\O of\\O the\\O laptop\\O ,\\O replaced\\O the\\O power\\O brick\\O .\\O"}, {"uid": "146-1", "sentiment": "neutral", "target_tags": "Then\\O I\\O 've\\O fixed\\O the\\O DC\\O jack\\O (\\O inside\\O the\\O unit\\O )\\O ,\\O rewired\\O the\\O DC\\B jack\\I to\\O the\\O OUTside\\O of\\O the\\O laptop\\O ,\\O replaced\\O the\\O power\\O brick\\O .\\O", "opinion_tags": "Then\\O I\\O 've\\O fixed\\O the\\O DC\\O jack\\O (\\O inside\\O the\\O unit\\O )\\O ,\\O rewired\\B the\\O DC\\O jack\\O to\\O the\\O OUTside\\O of\\O the\\O laptop\\O ,\\O replaced\\O the\\O power\\O brick\\O .\\O"}, {"uid": "146-2", "sentiment": "neutral", "target_tags": "Then\\O I\\O 've\\O fixed\\O the\\O DC\\O jack\\O (\\O inside\\O the\\O unit\\O )\\O ,\\O rewired\\O the\\O DC\\O jack\\O to\\O the\\O OUTside\\O of\\O the\\O laptop\\O ,\\O replaced\\O the\\O power\\B brick\\I .\\O", "opinion_tags": "Then\\O I\\O 've\\O fixed\\O the\\O DC\\O jack\\O (\\O inside\\O the\\O unit\\O )\\O ,\\O rewired\\O the\\O DC\\O jack\\O to\\O the\\O OUTside\\O of\\O the\\O laptop\\O ,\\O replaced\\B the\\O power\\O brick\\O .\\O"}], "postag": ["RB", "PRP", "VBP", "VBN", "DT", "NNP", "NN", "-LRB-", "IN", "DT", "NN", "-RRB-", ",", "VBD", "DT", "NNP", "NN", "IN", "DT", "NN", "IN", "DT", "NN", ",", "VBD", "DT", "NN", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 11, 11, 11, 7, 11, 14, 4, 17, 17, 14, 20, 20, 14, 23, 23, 20, 25, 4, 28, 28, 25, 4], "deprel": ["advmod", "nsubj", "aux", "root", "det", "compound", "obj", "punct", "case", "det", "nmod", "punct", "punct", "conj", "det", "compound", "obj", "case", "det", "obl", "case", "det", "nmod", "punct", "conj", "det", "compound", "obj", "punct"]}, {"id": "147", "sentence": "Great product , very easy to use and great graphics .", "triples": [{"uid": "147-0", "sentiment": "positive", "target_tags": "Great\\O product\\O ,\\O very\\O easy\\O to\\O use\\O and\\O great\\O graphics\\B .\\O", "opinion_tags": "Great\\O product\\O ,\\O very\\O easy\\O to\\O use\\O and\\O great\\B graphics\\O .\\O"}, {"uid": "147-1", "sentiment": "positive", "target_tags": "Great\\O product\\O ,\\O very\\O easy\\O to\\O use\\B and\\O great\\O graphics\\O .\\O", "opinion_tags": "Great\\O product\\O ,\\O very\\O easy\\B to\\O use\\O and\\O great\\O graphics\\O .\\O"}], "postag": ["JJ", "NN", ",", "RB", "JJ", "TO", "VB", "CC", "JJ", "NNS", "."], "head": [2, 0, 5, 5, 2, 7, 5, 10, 10, 2, 2], "deprel": ["amod", "root", "punct", "advmod", "parataxis", "mark", "advcl", "cc", "amod", "conj", "punct"]}, {"id": "148", "sentence": "The built-in webcam is great for Skype and similar video-chat services .", "triples": [{"uid": "148-0", "sentiment": "positive", "target_tags": "The\\O built-in\\B webcam\\I is\\O great\\O for\\O Skype\\O and\\O similar\\O video-chat\\O services\\O .\\O", "opinion_tags": "The\\O built-in\\O webcam\\O is\\O great\\B for\\O Skype\\O and\\O similar\\O video-chat\\O services\\O .\\O"}], "postag": ["DT", "JJ", "NN", "VBZ", "JJ", "IN", "NNP", "CC", "JJ", "NN", "NNS", "."], "head": [3, 3, 5, 5, 0, 7, 5, 11, 11, 11, 7, 5], "deprel": ["det", "amod", "nsubj", "cop", "root", "case", "obl", "cc", "amod", "compound", "conj", "punct"]}, {"id": "149", "sentence": "THE MOTHERBOARD IS DEAD !", "triples": [{"uid": "149-0", "sentiment": "negative", "target_tags": "THE\\O MOTHERBOARD\\B IS\\O DEAD\\O !\\O", "opinion_tags": "THE\\O MOTHERBOARD\\O IS\\O DEAD\\B !\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"]}, {"id": "150", "sentence": "This process continued to repeat itself until the mother board had been replaced 4 times and the hard drive replaced 3 times .", "triples": [{"uid": "150-0", "sentiment": "negative", "target_tags": "This\\O process\\O continued\\O to\\O repeat\\O itself\\O until\\O the\\O mother\\B board\\I had\\O been\\O replaced\\O 4\\O times\\O and\\O the\\O hard\\O drive\\O replaced\\O 3\\O times\\O .\\O", "opinion_tags": "This\\O process\\O continued\\O to\\O repeat\\O itself\\O until\\O the\\O mother\\O board\\O had\\O been\\O replaced\\B 4\\O times\\O and\\O the\\O hard\\O drive\\O replaced\\O 3\\O times\\O .\\O"}, {"uid": "150-1", "sentiment": "negative", "target_tags": "This\\O process\\O continued\\O to\\O repeat\\O itself\\O until\\O the\\O mother\\O board\\O had\\O been\\O replaced\\O 4\\O times\\O and\\O the\\O hard\\B drive\\I replaced\\O 3\\O times\\O .\\O", "opinion_tags": "This\\O process\\O continued\\O to\\O repeat\\O itself\\O until\\O the\\O mother\\O board\\O had\\O been\\O replaced\\O 4\\O times\\O and\\O the\\O hard\\O drive\\O replaced\\B 3\\O times\\O .\\O"}], "postag": ["DT", "NN", "VBD", "TO", "VB", "PRP", "IN", "DT", "NN", "NN", "VBD", "VBN", "VBN", "CD", "NNS", "CC", "DT", "JJ", "NN", "VBD", "CD", "NNS", "."], "head": [2, 3, 0, 5, 3, 5, 13, 10, 10, 13, 13, 13, 5, 15, 13, 20, 19, 19, 20, 3, 22, 20, 3], "deprel": ["det", "nsubj", "root", "mark", "xcomp", "obj", "mark", "det", "compound", "nsubj:pass", "aux", "aux:pass", "advcl", "nummod", "obl:tmod", "cc", "det", "amod", "nsubj", "conj", "nummod", "obl:tmod", "punct"]}, {"id": "151", "sentence": "I could n't believe how long the battery lasted on a single charge .", "triples": [{"uid": "151-0", "sentiment": "positive", "target_tags": "I\\O could\\O n't\\O believe\\O how\\O long\\O the\\O battery\\B lasted\\O on\\O a\\O single\\O charge\\O .\\O", "opinion_tags": "I\\O could\\O n't\\O believe\\O how\\O long\\B the\\O battery\\O lasted\\O on\\O a\\O single\\O charge\\O .\\O"}, {"uid": "151-1", "sentiment": "positive", "target_tags": "I\\O could\\O n't\\O believe\\O how\\O long\\O the\\O battery\\O lasted\\O on\\O a\\O single\\O charge\\B .\\O", "opinion_tags": "I\\O could\\O n't\\O believe\\O how\\O long\\O the\\O battery\\O lasted\\O on\\O a\\O single\\B charge\\O .\\O"}], "postag": ["PRP", "MD", "RB", "VB", "WRB", "RB", "DT", "NN", "VBD", "IN", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 0, 6, 9, 8, 9, 4, 13, 13, 13, 9, 4], "deprel": ["nsubj", "aux", "advmod", "root", "mark", "advmod", "det", "nsubj", "ccomp", "case", "det", "amod", "obl", "punct"]}, {"id": "152", "sentence": "The backlit keys are wonderful when you are working in the dark .", "triples": [{"uid": "152-0", "sentiment": "positive", "target_tags": "The\\O backlit\\B keys\\I are\\O wonderful\\O when\\O you\\O are\\O working\\O in\\O the\\O dark\\O .\\O", "opinion_tags": "The\\O backlit\\O keys\\O are\\O wonderful\\B when\\O you\\O are\\O working\\O in\\O the\\O dark\\O .\\O"}], "postag": ["DT", "NN", "NNS", "VBP", "JJ", "WRB", "PRP", "VBP", "VBG", "IN", "DT", "NN", "."], "head": [3, 3, 5, 5, 0, 9, 9, 9, 5, 12, 12, 9, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "mark", "nsubj", "aux", "advcl", "case", "det", "obl", "punct"]}, {"id": "153", "sentence": "The most recent being that my Safari internet browser is freaking out on me , but I have just been using firefox instead .", "triples": [{"uid": "153-0", "sentiment": "negative", "target_tags": "The\\O most\\O recent\\O being\\O that\\O my\\O Safari\\B internet\\I browser\\I is\\O freaking\\O out\\O on\\O me\\O ,\\O but\\O I\\O have\\O just\\O been\\O using\\O firefox\\O instead\\O .\\O", "opinion_tags": "The\\O most\\O recent\\O being\\O that\\O my\\O Safari\\O internet\\O browser\\O is\\O freaking\\B out\\I on\\O me\\O ,\\O but\\O I\\O have\\O just\\O been\\O using\\O firefox\\O instead\\O .\\O"}], "postag": ["DT", "RBS", "JJ", "NN", "WDT", "PRP$", "NNP", "NN", "NN", "VBZ", "VBG", "RP", "IN", "PRP", ",", "CC", "PRP", "VBP", "RB", "VBN", "VBG", "NNP", "RB", "."], "head": [4, 3, 4, 11, 11, 9, 9, 9, 11, 11, 0, 11, 14, 11, 21, 21, 21, 21, 21, 21, 11, 21, 21, 10], "deprel": ["det", "advmod", "amod", "nsubj", "obj", "nmod:poss", "compound", "compound", "nsubj", "aux", "root", "compound:prt", "case", "obl", "punct", "cc", "nsubj", "aux", "advmod", "aux", "conj", "obj", "advmod", "punct"]}, {"id": "154", "sentence": "The DVD drive randomly pops open when it is in my backpack as well , which is annoying .", "triples": [{"uid": "154-0", "sentiment": "negative", "target_tags": "The\\O DVD\\B drive\\I randomly\\O pops\\O open\\O when\\O it\\O is\\O in\\O my\\O backpack\\O as\\O well\\O ,\\O which\\O is\\O annoying\\O .\\O", "opinion_tags": "The\\O DVD\\O drive\\O randomly\\B pops\\I open\\I when\\O it\\O is\\O in\\O my\\O backpack\\O as\\O well\\O ,\\O which\\O is\\O annoying\\O .\\O"}, {"uid": "154-1", "sentiment": "negative", "target_tags": "The\\O DVD\\B drive\\I randomly\\O pops\\O open\\O when\\O it\\O is\\O in\\O my\\O backpack\\O as\\O well\\O ,\\O which\\O is\\O annoying\\O .\\O", "opinion_tags": "The\\O DVD\\O drive\\O randomly\\O pops\\O open\\O when\\O it\\O is\\O in\\O my\\O backpack\\O as\\O well\\O ,\\O which\\O is\\O annoying\\B .\\O"}], "postag": ["DT", "NNP", "NN", "RB", "VBZ", "JJ", "WRB", "PRP", "VBZ", "IN", "PRP$", "NN", "RB", "RB", ",", "WDT", "VBZ", "JJ", "."], "head": [3, 3, 5, 5, 0, 5, 12, 12, 12, 12, 12, 5, 12, 13, 5, 18, 18, 12, 5], "deprel": ["det", "compound", "nsubj", "advmod", "root", "xcomp", "mark", "nsubj", "cop", "case", "nmod:poss", "advcl", "advmod", "fixed", "punct", "nsubj", "cop", "acl:relcl", "punct"]}, {"id": "155", "sentence": "Its also FUN to use !", "triples": [{"uid": "155-0", "sentiment": "positive", "target_tags": "Its\\O also\\O FUN\\O to\\O use\\B !\\O", "opinion_tags": "Its\\O also\\O FUN\\B to\\O use\\O !\\O"}], "postag": ["PRP$", "RB", "NN", "TO", "VB", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nmod:poss", "advmod", "root", "mark", "acl", "punct"]}, {"id": "156", "sentence": "The graphics and screen are stunning and although I was a PC person , I was able to understand how to use a mac fairly quickly .", "triples": [{"uid": "156-0", "sentiment": "positive", "target_tags": "The\\O graphics\\B and\\O screen\\O are\\O stunning\\O and\\O although\\O I\\O was\\O a\\O PC\\O person\\O ,\\O I\\O was\\O able\\O to\\O understand\\O how\\O to\\O use\\O a\\O mac\\O fairly\\O quickly\\O .\\O", "opinion_tags": "The\\O graphics\\O and\\O screen\\O are\\O stunning\\B and\\O although\\O I\\O was\\O a\\O PC\\O person\\O ,\\O I\\O was\\O able\\O to\\O understand\\O how\\O to\\O use\\O a\\O mac\\O fairly\\O quickly\\O .\\O"}, {"uid": "156-1", "sentiment": "positive", "target_tags": "The\\O graphics\\O and\\O screen\\B are\\O stunning\\O and\\O although\\O I\\O was\\O a\\O PC\\O person\\O ,\\O I\\O was\\O able\\O to\\O understand\\O how\\O to\\O use\\O a\\O mac\\O fairly\\O quickly\\O .\\O", "opinion_tags": "The\\O graphics\\O and\\O screen\\O are\\O stunning\\B and\\O although\\O I\\O was\\O a\\O PC\\O person\\O ,\\O I\\O was\\O able\\O to\\O understand\\O how\\O to\\O use\\O a\\O mac\\O fairly\\O quickly\\O .\\O"}], "postag": ["DT", "NNS", "CC", "NN", "VBP", "JJ", "CC", "IN", "PRP", "VBD", "DT", "NNP", "NN", ",", "PRP", "VBD", "JJ", "TO", "VB", "WRB", "TO", "VB", "DT", "NN", "RB", "RB", "."], "head": [2, 6, 4, 2, 6, 0, 17, 13, 13, 13, 13, 13, 17, 17, 17, 17, 6, 19, 17, 22, 22, 19, 24, 22, 26, 22, 6], "deprel": ["det", "nsubj", "cc", "conj", "cop", "root", "cc", "mark", "nsubj", "cop", "det", "compound", "advcl", "punct", "nsubj", "cop", "conj", "mark", "xcomp", "mark", "mark", "ccomp", "det", "obj", "advmod", "advmod", "punct"]}, {"id": "157", "sentence": "I just took the broken cords into the Apple store and they gave me new ones .", "triples": [{"uid": "157-0", "sentiment": "positive", "target_tags": "I\\O just\\O took\\O the\\O broken\\O cords\\B into\\O the\\O Apple\\O store\\O and\\O they\\O gave\\O me\\O new\\O ones\\O .\\O", "opinion_tags": "I\\O just\\O took\\O the\\O broken\\B cords\\O into\\O the\\O Apple\\O store\\O and\\O they\\O gave\\O me\\O new\\O ones\\O .\\O"}], "postag": ["PRP", "RB", "VBD", "DT", "JJ", "NNS", "IN", "DT", "NNP", "NN", "CC", "PRP", "VBD", "PRP", "JJ", "NNS", "."], "head": [3, 3, 0, 6, 6, 3, 10, 10, 10, 3, 13, 13, 3, 13, 16, 13, 3], "deprel": ["nsubj", "advmod", "root", "det", "amod", "obj", "case", "det", "compound", "obl", "cc", "nsubj", "conj", "i<PERSON><PERSON>", "amod", "obj", "punct"]}, {"id": "158", "sentence": "The screen is gorgeous - yummy good .", "triples": [{"uid": "158-0", "sentiment": "positive", "target_tags": "The\\O screen\\B is\\O gorgeous\\O -\\O yummy\\O good\\O .\\O", "opinion_tags": "The\\O screen\\O is\\O gorgeous\\B -\\O yummy\\O good\\O .\\O"}, {"uid": "158-1", "sentiment": "positive", "target_tags": "The\\O screen\\B is\\O gorgeous\\O -\\O yummy\\O good\\O .\\O", "opinion_tags": "The\\O screen\\O is\\O gorgeous\\O -\\O yummy\\O good\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ",", "JJ", "JJ", "."], "head": [2, 4, 4, 0, 4, 4, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "parataxis", "parataxis", "punct"]}, {"id": "159", "sentence": "The Macbook starts fast , does n't crash , has a fantastic display , is small and light ( I have the 13.3 '' model ) , and is n't always complaining about updates , lost connections , errors , blue screens , etc .", "triples": [{"uid": "159-0", "sentiment": "positive", "target_tags": "The\\O Macbook\\O starts\\O fast\\O ,\\O does\\O n't\\O crash\\O ,\\O has\\O a\\O fantastic\\O display\\B ,\\O is\\O small\\O and\\O light\\O (\\O I\\O have\\O the\\O 13.3\\O ''\\O model\\O )\\O ,\\O and\\O is\\O n't\\O always\\O complaining\\O about\\O updates\\O ,\\O lost\\O connections\\O ,\\O errors\\O ,\\O blue\\O screens\\O ,\\O etc\\O .\\O", "opinion_tags": "The\\O Macbook\\O starts\\O fast\\O ,\\O does\\O n't\\O crash\\O ,\\O has\\O a\\O fantastic\\B display\\O ,\\O is\\O small\\O and\\O light\\O (\\O I\\O have\\O the\\O 13.3\\O ''\\O model\\O )\\O ,\\O and\\O is\\O n't\\O always\\O complaining\\O about\\O updates\\O ,\\O lost\\O connections\\O ,\\O errors\\O ,\\O blue\\O screens\\O ,\\O etc\\O .\\O"}, {"uid": "159-1", "sentiment": "positive", "target_tags": "The\\O Macbook\\O starts\\B fast\\O ,\\O does\\O n't\\O crash\\O ,\\O has\\O a\\O fantastic\\O display\\O ,\\O is\\O small\\O and\\O light\\O (\\O I\\O have\\O the\\O 13.3\\O ''\\O model\\O )\\O ,\\O and\\O is\\O n't\\O always\\O complaining\\O about\\O updates\\O ,\\O lost\\O connections\\O ,\\O errors\\O ,\\O blue\\O screens\\O ,\\O etc\\O .\\O", "opinion_tags": "The\\O Macbook\\O starts\\O fast\\B ,\\O does\\O n't\\O crash\\O ,\\O has\\O a\\O fantastic\\O display\\O ,\\O is\\O small\\O and\\O light\\O (\\O I\\O have\\O the\\O 13.3\\O ''\\O model\\O )\\O ,\\O and\\O is\\O n't\\O always\\O complaining\\O about\\O updates\\O ,\\O lost\\O connections\\O ,\\O errors\\O ,\\O blue\\O screens\\O ,\\O etc\\O .\\O"}], "postag": ["DT", "NNP", "VBZ", "RB", ",", "VBZ", "RB", "VB", ",", "VBZ", "DT", "JJ", "NN", ",", "VBZ", "JJ", "CC", "JJ", "-LRB-", "PRP", "VBP", "DT", "CD", "''", "NN", "-RRB-", ",", "CC", "VBZ", "RB", "RB", "VBG", "IN", "NNS", ",", "VBN", "NNS", ",", "NNS", ",", "JJ", "NNS", ",", "FW", "."], "head": [2, 3, 0, 3, 8, 8, 8, 3, 10, 8, 13, 13, 10, 16, 16, 3, 18, 16, 21, 21, 16, 25, 25, 25, 21, 21, 32, 32, 32, 32, 32, 3, 34, 32, 37, 37, 34, 39, 34, 42, 42, 34, 44, 34, 3], "deprel": ["det", "nsubj", "root", "advmod", "punct", "aux", "advmod", "parataxis", "punct", "conj", "det", "amod", "obj", "punct", "cop", "parataxis", "cc", "conj", "punct", "nsubj", "parataxis", "det", "nummod", "punct", "obj", "punct", "punct", "cc", "aux", "advmod", "advmod", "conj", "case", "obl", "punct", "amod", "conj", "punct", "conj", "punct", "amod", "conj", "punct", "conj", "punct"]}, {"id": "160", "sentence": "The guy then said that if I insist on having the hinge tightened , they can do it for me but I have to accept the condition after the `` repair '' .", "triples": [{"uid": "160-0", "sentiment": "neutral", "target_tags": "The\\O guy\\O then\\O said\\O that\\O if\\O I\\O insist\\O on\\O having\\O the\\O hinge\\B tightened\\O ,\\O they\\O can\\O do\\O it\\O for\\O me\\O but\\O I\\O have\\O to\\O accept\\O the\\O condition\\O after\\O the\\O ``\\O repair\\O ''\\O .\\O", "opinion_tags": "The\\O guy\\O then\\O said\\O that\\O if\\O I\\O insist\\O on\\O having\\O the\\O hinge\\O tightened\\B ,\\O they\\O can\\O do\\O it\\O for\\O me\\O but\\O I\\O have\\O to\\O accept\\O the\\O condition\\O after\\O the\\O ``\\O repair\\O ''\\O .\\O"}], "postag": ["DT", "NN", "RB", "VBD", "IN", "IN", "PRP", "VBP", "IN", "VBG", "DT", "NN", "VBN", ",", "PRP", "MD", "VB", "PRP", "IN", "PRP", "CC", "PRP", "VBP", "TO", "VB", "DT", "NN", "IN", "DT", "``", "NN", "''", "."], "head": [2, 4, 4, 0, 17, 8, 8, 17, 10, 8, 12, 10, 10, 17, 17, 17, 4, 17, 20, 17, 23, 23, 4, 25, 23, 27, 25, 31, 31, 31, 25, 31, 4], "deprel": ["det", "nsubj", "advmod", "root", "mark", "mark", "nsubj", "advcl", "mark", "advcl", "det", "obj", "xcomp", "punct", "nsubj", "aux", "ccomp", "obj", "case", "obl", "cc", "nsubj", "conj", "mark", "xcomp", "det", "obj", "case", "det", "punct", "obl", "punct", "punct"]}, {"id": "161", "sentence": "so in a brief summary i would have to say that i would not recommend dell vostro 1000 to anyone due to it being a down right awful setup so in my opinion you should steer clear of them if you want a decent laptop .", "triples": [{"uid": "161-0", "sentiment": "negative", "target_tags": "so\\O in\\O a\\O brief\\O summary\\O i\\O would\\O have\\O to\\O say\\O that\\O i\\O would\\O not\\O recommend\\O dell\\O vostro\\O 1000\\O to\\O anyone\\O due\\O to\\O it\\O being\\O a\\O down\\O right\\O awful\\O setup\\B so\\O in\\O my\\O opinion\\O you\\O should\\O steer\\O clear\\O of\\O them\\O if\\O you\\O want\\O a\\O decent\\O laptop\\O .\\O", "opinion_tags": "so\\O in\\O a\\O brief\\O summary\\O i\\O would\\O have\\O to\\O say\\O that\\O i\\O would\\O not\\O recommend\\O dell\\O vostro\\O 1000\\O to\\O anyone\\O due\\O to\\O it\\O being\\O a\\O down\\O right\\O awful\\B setup\\O so\\O in\\O my\\O opinion\\O you\\O should\\O steer\\O clear\\O of\\O them\\O if\\O you\\O want\\O a\\O decent\\O laptop\\O .\\O"}], "postag": ["RB", "IN", "DT", "JJ", "NN", "PRP", "MD", "VB", "TO", "VB", "IN", "PRP", "MD", "RB", "VB", "NNP", "NNP", "CD", "IN", "NN", "IN", "IN", "PRP", "VBG", "DT", "JJ", "RB", "JJ", "NN", "RB", "IN", "PRP$", "NN", "PRP", "MD", "VB", "JJ", "IN", "PRP", "IN", "PRP", "VBP", "DT", "JJ", "NN", "."], "head": [8, 5, 5, 5, 8, 8, 8, 0, 10, 8, 15, 15, 15, 15, 10, 17, 15, 17, 20, 15, 29, 21, 29, 29, 29, 29, 28, 29, 15, 36, 33, 33, 36, 36, 36, 8, 36, 39, 37, 42, 42, 36, 45, 45, 42, 8], "deprel": ["advmod", "case", "det", "amod", "obl", "nsubj", "aux", "root", "mark", "xcomp", "mark", "nsubj", "aux", "advmod", "ccomp", "compound", "obj", "nummod", "case", "obl", "mark", "fixed", "nsubj", "cop", "det", "amod", "advmod", "amod", "advcl", "advmod", "case", "nmod:poss", "obl", "nsubj", "aux", "conj", "xcomp", "case", "obl", "mark", "nsubj", "advcl", "det", "amod", "obj", "punct"]}, {"id": "162", "sentence": "My friend just had to replace his entire motherboard , so did my wife , and it looks like I will have to as well .", "triples": [{"uid": "162-0", "sentiment": "negative", "target_tags": "My\\O friend\\O just\\O had\\O to\\O replace\\O his\\O entire\\O motherboard\\B ,\\O so\\O did\\O my\\O wife\\O ,\\O and\\O it\\O looks\\O like\\O I\\O will\\O have\\O to\\O as\\O well\\O .\\O", "opinion_tags": "My\\O friend\\O just\\O had\\O to\\O replace\\B his\\O entire\\O motherboard\\O ,\\O so\\O did\\O my\\O wife\\O ,\\O and\\O it\\O looks\\O like\\O I\\O will\\O have\\O to\\O as\\O well\\O .\\O"}], "postag": ["PRP$", "NN", "RB", "VBD", "TO", "VB", "PRP$", "JJ", "NN", ",", "RB", "VBD", "PRP$", "NN", ",", "CC", "PRP", "VBZ", "IN", "PRP", "MD", "VB", "TO", "RB", "RB", "."], "head": [2, 4, 4, 0, 6, 4, 9, 9, 6, 12, 12, 4, 14, 12, 18, 18, 18, 4, 22, 22, 22, 18, 22, 22, 24, 4], "deprel": ["nmod:poss", "nsubj", "advmod", "root", "mark", "xcomp", "nmod:poss", "amod", "obj", "punct", "advmod", "conj", "nmod:poss", "obj", "punct", "cc", "nsubj", "conj", "mark", "nsubj", "aux", "advcl", "xcomp", "advmod", "fixed", "punct"]}, {"id": "163", "sentence": "First , it does not have a push button to open the lid .", "triples": [{"uid": "163-0", "sentiment": "negative", "target_tags": "First\\O ,\\O it\\O does\\O not\\O have\\O a\\O push\\B button\\I to\\O open\\O the\\O lid\\O .\\O", "opinion_tags": "First\\O ,\\O it\\O does\\B not\\I have\\O a\\O push\\O button\\O to\\O open\\O the\\O lid\\O .\\O"}], "postag": ["RB", ",", "PRP", "VBZ", "RB", "VB", "DT", "NN", "NN", "TO", "VB", "DT", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 11, 9, 13, 11, 6], "deprel": ["advmod", "punct", "nsubj", "aux", "advmod", "root", "det", "compound", "obj", "mark", "acl", "det", "obj", "punct"]}, {"id": "164", "sentence": "The screen almost looked like a barcode when it froze .", "triples": [{"uid": "164-0", "sentiment": "negative", "target_tags": "The\\O screen\\B almost\\O looked\\O like\\O a\\O barcode\\O when\\O it\\O froze\\O .\\O", "opinion_tags": "The\\O screen\\O almost\\O looked\\O like\\O a\\O barcode\\B when\\O it\\O froze\\O .\\O"}, {"uid": "164-1", "sentiment": "negative", "target_tags": "The\\O screen\\B almost\\O looked\\O like\\O a\\O barcode\\O when\\O it\\O froze\\O .\\O", "opinion_tags": "The\\O screen\\O almost\\O looked\\O like\\O a\\O barcode\\O when\\O it\\O froze\\B .\\O"}], "postag": ["DT", "NN", "RB", "VBD", "IN", "DT", "NN", "WRB", "PRP", "VBD", "."], "head": [2, 4, 4, 0, 7, 7, 4, 10, 10, 4, 4], "deprel": ["det", "nsubj", "advmod", "root", "case", "det", "obl", "mark", "nsubj", "advcl", "punct"]}, {"id": "165", "sentence": "I love the solid machined aluminum frame , and the keyboard is the best of any laptop I 've used .", "triples": [{"uid": "165-0", "sentiment": "positive", "target_tags": "I\\O love\\O the\\O solid\\O machined\\B aluminum\\I frame\\I ,\\O and\\O the\\O keyboard\\O is\\O the\\O best\\O of\\O any\\O laptop\\O I\\O 've\\O used\\O .\\O", "opinion_tags": "I\\O love\\B the\\O solid\\O machined\\O aluminum\\O frame\\O ,\\O and\\O the\\O keyboard\\O is\\O the\\O best\\O of\\O any\\O laptop\\O I\\O 've\\O used\\O .\\O"}, {"uid": "165-1", "sentiment": "positive", "target_tags": "I\\O love\\O the\\O solid\\O machined\\B aluminum\\I frame\\I ,\\O and\\O the\\O keyboard\\O is\\O the\\O best\\O of\\O any\\O laptop\\O I\\O 've\\O used\\O .\\O", "opinion_tags": "I\\O love\\O the\\O solid\\B machined\\O aluminum\\O frame\\O ,\\O and\\O the\\O keyboard\\O is\\O the\\O best\\O of\\O any\\O laptop\\O I\\O 've\\O used\\O .\\O"}, {"uid": "165-2", "sentiment": "positive", "target_tags": "I\\O love\\O the\\O solid\\O machined\\O aluminum\\O frame\\O ,\\O and\\O the\\O keyboard\\B is\\O the\\O best\\O of\\O any\\O laptop\\O I\\O 've\\O used\\O .\\O", "opinion_tags": "I\\O love\\O the\\O solid\\O machined\\O aluminum\\O frame\\O ,\\O and\\O the\\O keyboard\\O is\\O the\\O best\\B of\\O any\\O laptop\\O I\\O 've\\O used\\O .\\O"}], "postag": ["PRP", "VBP", "DT", "JJ", "VBN", "NN", "NN", ",", "CC", "DT", "NN", "VBZ", "DT", "JJS", "IN", "DT", "NN", "PRP", "VBP", "VBN", "."], "head": [2, 0, 7, 7, 7, 7, 2, 14, 14, 11, 14, 14, 14, 2, 17, 17, 14, 20, 20, 17, 2], "deprel": ["nsubj", "root", "det", "amod", "amod", "compound", "obj", "punct", "cc", "det", "nsubj", "cop", "det", "conj", "case", "det", "obl", "nsubj", "aux", "acl:relcl", "punct"]}, {"id": "166", "sentence": "You can even run a parallels type program easily and run any leftover PC software that you absolutely can not be without .", "triples": [{"uid": "166-0", "sentiment": "positive", "target_tags": "You\\O can\\O even\\O run\\O a\\O parallels\\B type\\I program\\I easily\\O and\\O run\\O any\\O leftover\\O PC\\O software\\O that\\O you\\O absolutely\\O can\\O not\\O be\\O without\\O .\\O", "opinion_tags": "You\\O can\\O even\\O run\\O a\\O parallels\\O type\\O program\\O easily\\B and\\O run\\O any\\O leftover\\O PC\\O software\\O that\\O you\\O absolutely\\O can\\O not\\O be\\O without\\O .\\O"}], "postag": ["PRP", "MD", "RB", "VB", "DT", "NNS", "NN", "NN", "RB", "CC", "VB", "DT", "NN", "NN", "NN", "WDT", "PRP", "RB", "MD", "RB", "VB", "IN", "."], "head": [4, 4, 4, 0, 8, 7, 8, 4, 4, 11, 4, 15, 14, 15, 11, 21, 21, 21, 21, 21, 15, 16, 4], "deprel": ["nsubj", "aux", "advmod", "root", "det", "compound", "compound", "obj", "advmod", "cc", "conj", "det", "compound", "compound", "obj", "obl", "nsubj", "advmod", "aux", "advmod", "acl:relcl", "case", "punct"]}, {"id": "167", "sentence": "It has easy to use features and all the speed and power I could ask for .", "triples": [{"uid": "167-0", "sentiment": "positive", "target_tags": "It\\O has\\O easy\\O to\\O use\\O features\\B and\\O all\\O the\\O speed\\O and\\O power\\O I\\O could\\O ask\\O for\\O .\\O", "opinion_tags": "It\\O has\\O easy\\B to\\O use\\O features\\O and\\O all\\O the\\O speed\\O and\\O power\\O I\\O could\\O ask\\O for\\O .\\O"}, {"uid": "167-1", "sentiment": "positive", "target_tags": "It\\O has\\O easy\\O to\\O use\\O features\\O and\\O all\\O the\\O speed\\B and\\O power\\O I\\O could\\O ask\\O for\\O .\\O", "opinion_tags": "It\\O has\\O easy\\B to\\O use\\O features\\O and\\O all\\O the\\O speed\\O and\\O power\\O I\\O could\\O ask\\O for\\O .\\O"}, {"uid": "167-2", "sentiment": "positive", "target_tags": "It\\O has\\O easy\\O to\\O use\\O features\\O and\\O all\\O the\\O speed\\O and\\O power\\B I\\O could\\O ask\\O for\\O .\\O", "opinion_tags": "It\\O has\\O easy\\B to\\O use\\O features\\O and\\O all\\O the\\O speed\\O and\\O power\\O I\\O could\\O ask\\O for\\O .\\O"}], "postag": ["PRP", "VBZ", "JJ", "TO", "VB", "NNS", "CC", "PDT", "DT", "NN", "CC", "NN", "PRP", "MD", "VB", "IN", "."], "head": [2, 0, 2, 5, 3, 5, 10, 10, 10, 6, 12, 10, 15, 15, 10, 15, 2], "deprel": ["nsubj", "root", "xcomp", "mark", "xcomp", "obj", "cc", "det:predet", "det", "conj", "cc", "conj", "nsubj", "aux", "acl:relcl", "obl", "punct"]}, {"id": "168", "sentence": "The Mac Snow Leopard O/S is extremely easy to use , although very different than Win XP , Visa or Win7 .", "triples": [{"uid": "168-0", "sentiment": "positive", "target_tags": "The\\O Mac\\B Snow\\I Leopard\\I O/S\\I is\\O extremely\\O easy\\O to\\O use\\O ,\\O although\\O very\\O different\\O than\\O Win\\O XP\\O ,\\O Visa\\O or\\O Win7\\O .\\O", "opinion_tags": "The\\O Mac\\O Snow\\O <PERSON>\\O O/S\\O is\\O extremely\\O easy\\B to\\I use\\I ,\\O although\\O very\\O different\\O than\\O Win\\O XP\\O ,\\O Visa\\O or\\O Win7\\O .\\O"}, {"uid": "168-1", "sentiment": "positive", "target_tags": "The\\O Mac\\B Snow\\I Leopard\\I O/S\\I is\\O extremely\\O easy\\O to\\O use\\O ,\\O although\\O very\\O different\\O than\\O Win\\O XP\\O ,\\O Visa\\O or\\O Win7\\O .\\O", "opinion_tags": "The\\O Mac\\O Snow\\O <PERSON>\\O O/S\\O is\\O extremely\\O easy\\O to\\O use\\O ,\\O although\\O very\\O different\\B than\\O Win\\O XP\\O ,\\O Visa\\O or\\O Win7\\O .\\O"}], "postag": ["DT", "NNP", "NNP", "NNP", "NNP", "VBZ", "RB", "JJ", "TO", "VB", ",", "IN", "RB", "JJ", "IN", "NNP", "NNP", ",", "NNP", "CC", "NNP", "."], "head": [5, 5, 5, 5, 8, 8, 8, 0, 10, 8, 8, 14, 14, 8, 17, 17, 14, 19, 17, 21, 17, 8], "deprel": ["det", "compound", "compound", "compound", "nsubj", "cop", "advmod", "root", "mark", "ccomp", "punct", "mark", "advmod", "advcl", "case", "compound", "obl", "punct", "conj", "cc", "conj", "punct"]}, {"id": "169", "sentence": "super fast processor and really nice graphics card ...", "triples": [{"uid": "169-0", "sentiment": "positive", "target_tags": "super\\O fast\\O processor\\B and\\O really\\O nice\\O graphics\\O card\\O ...\\O", "opinion_tags": "super\\O fast\\B processor\\O and\\O really\\O nice\\O graphics\\O card\\O ...\\O"}, {"uid": "169-1", "sentiment": "positive", "target_tags": "super\\O fast\\O processor\\O and\\O really\\O nice\\O graphics\\B card\\I ...\\O", "opinion_tags": "super\\O fast\\O processor\\O and\\O really\\O nice\\B graphics\\O card\\O ...\\O"}], "postag": ["RB", "JJ", "NN", "CC", "RB", "JJ", "NNS", "NN", "."], "head": [2, 3, 0, 8, 6, 8, 8, 3, 3], "deprel": ["advmod", "amod", "root", "cc", "advmod", "amod", "compound", "conj", "punct"]}, {"id": "170", "sentence": "I dislike the weight and size , cubersome .", "triples": [{"uid": "170-0", "sentiment": "negative", "target_tags": "I\\O dislike\\O the\\O weight\\B and\\O size\\O ,\\O cubersome\\O .\\O", "opinion_tags": "I\\O dislike\\B the\\O weight\\O and\\O size\\O ,\\O cubersome\\O .\\O"}, {"uid": "170-1", "sentiment": "negative", "target_tags": "I\\O dislike\\O the\\O weight\\B and\\O size\\O ,\\O cubersome\\O .\\O", "opinion_tags": "I\\O dislike\\O the\\O weight\\O and\\O size\\O ,\\O cubersome\\B .\\O"}, {"uid": "170-2", "sentiment": "negative", "target_tags": "I\\O dislike\\O the\\O weight\\O and\\O size\\B ,\\O cubersome\\O .\\O", "opinion_tags": "I\\O dislike\\B the\\O weight\\O and\\O size\\O ,\\O cubersome\\O .\\O"}, {"uid": "170-3", "sentiment": "negative", "target_tags": "I\\O dislike\\O the\\O weight\\O and\\O size\\B ,\\O cubersome\\O .\\O", "opinion_tags": "I\\O dislike\\O the\\O weight\\O and\\O size\\O ,\\O cubersome\\B .\\O"}], "postag": ["PRP", "VBP", "DT", "NN", "CC", "NN", ",", "NN", "."], "head": [2, 0, 4, 2, 6, 4, 8, 4, 2], "deprel": ["nsubj", "root", "det", "obj", "cc", "conj", "punct", "conj", "punct"]}, {"id": "171", "sentence": "I love windows 7 but i ca n't give <PERSON><PERSON><PERSON> any credit for that , unless y'all get serious about ergonomics and making required connections less obtrusive i will be looking to different manufacturer next time .", "triples": [{"uid": "171-0", "sentiment": "positive", "target_tags": "I\\O love\\O windows\\B 7\\I but\\O i\\O ca\\O n't\\O give\\O Toshiba\\O any\\O credit\\O for\\O that\\O ,\\O unless\\O y'all\\O get\\O serious\\O about\\O ergonomics\\O and\\O making\\O required\\O connections\\O less\\O obtrusive\\O i\\O will\\O be\\O looking\\O to\\O different\\O manufacturer\\O next\\O time\\O .\\O", "opinion_tags": "I\\O love\\B windows\\O 7\\O but\\O i\\O ca\\O n't\\O give\\O Toshiba\\O any\\O credit\\O for\\O that\\O ,\\O unless\\O y'all\\O get\\O serious\\O about\\O ergonomics\\O and\\O making\\O required\\O connections\\O less\\O obtrusive\\O i\\O will\\O be\\O looking\\O to\\O different\\O manufacturer\\O next\\O time\\O .\\O"}, {"uid": "171-1", "sentiment": "negative", "target_tags": "I\\O love\\O windows\\O 7\\O but\\O i\\O ca\\O n't\\O give\\O Toshiba\\O any\\O credit\\O for\\O that\\O ,\\O unless\\O y'all\\O get\\O serious\\O about\\O ergonomics\\B and\\O making\\O required\\O connections\\O less\\O obtrusive\\O i\\O will\\O be\\O looking\\O to\\O different\\O manufacturer\\O next\\O time\\O .\\O", "opinion_tags": "I\\O love\\O windows\\O 7\\O but\\O i\\O ca\\O n't\\O give\\O Toshiba\\O any\\O credit\\O for\\O that\\O ,\\O unless\\O y'all\\O get\\O serious\\B about\\O ergonomics\\O and\\O making\\O required\\O connections\\O less\\O obtrusive\\O i\\O will\\O be\\O looking\\O to\\O different\\O manufacturer\\O next\\O time\\O .\\O"}, {"uid": "171-2", "sentiment": "negative", "target_tags": "I\\O love\\O windows\\O 7\\O but\\O i\\O ca\\O n't\\O give\\O Toshiba\\O any\\O credit\\O for\\O that\\O ,\\O unless\\O y'all\\O get\\O serious\\O about\\O ergonomics\\O and\\O making\\O required\\O connections\\B less\\O obtrusive\\O i\\O will\\O be\\O looking\\O to\\O different\\O manufacturer\\O next\\O time\\O .\\O", "opinion_tags": "I\\O love\\O windows\\O 7\\O but\\O i\\O ca\\O n't\\O give\\O Toshiba\\O any\\O credit\\O for\\O that\\O ,\\O unless\\O y'all\\O get\\O serious\\O about\\O ergonomics\\O and\\O making\\O required\\O connections\\O less\\B obtrusive\\I i\\O will\\O be\\O looking\\O to\\O different\\O manufacturer\\O next\\O time\\O .\\O"}], "postag": ["PRP", "VBP", "NNS", "CD", "CC", "PRP", "MD", "RB", "VB", "NNP", "DT", "NN", "IN", "DT", ",", "IN", "PRP", "VBP", "JJ", "IN", "NNS", "CC", "VBG", "VBN", "NNS", "RBR", "JJ", "PRP", "MD", "VB", "VBG", "IN", "JJ", "NN", "JJ", "NN", "."], "head": [2, 0, 2, 3, 9, 9, 9, 9, 2, 9, 12, 9, 14, 9, 9, 18, 18, 9, 18, 21, 19, 23, 18, 25, 23, 27, 23, 31, 31, 31, 2, 34, 34, 31, 36, 31, 2], "deprel": ["nsubj", "root", "obj", "nummod", "cc", "nsubj", "aux", "advmod", "conj", "i<PERSON><PERSON>", "det", "obj", "case", "obl", "punct", "mark", "nsubj", "advcl", "xcomp", "case", "obl", "cc", "conj", "amod", "obj", "advmod", "xcomp", "nsubj", "aux", "aux", "parataxis", "case", "amod", "obl", "amod", "obl:tmod", "punct"]}, {"id": "172", "sentence": "Not sure how I recommend it for quality gaming , as I have a desktop rig for that reason .", "triples": [{"uid": "172-0", "sentiment": "negative", "target_tags": "Not\\O sure\\O how\\O I\\O recommend\\O it\\O for\\O quality\\O gaming\\B ,\\O as\\O I\\O have\\O a\\O desktop\\O rig\\O for\\O that\\O reason\\O .\\O", "opinion_tags": "Not\\O sure\\O how\\O I\\O recommend\\O it\\O for\\O quality\\B gaming\\O ,\\O as\\O I\\O have\\O a\\O desktop\\O rig\\O for\\O that\\O reason\\O .\\O"}], "postag": ["RB", "JJ", "WRB", "PRP", "VBP", "PRP", "IN", "NN", "NN", ",", "IN", "PRP", "VBP", "DT", "NN", "NN", "IN", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 5, 9, 9, 5, 2, 13, 13, 5, 16, 16, 13, 19, 19, 13, 2], "deprel": ["advmod", "root", "mark", "nsubj", "ccomp", "obj", "case", "compound", "obl", "punct", "mark", "nsubj", "advcl", "det", "compound", "obj", "case", "det", "obl", "punct"]}, {"id": "173", "sentence": "Great value , fast delivery -- Computer works as if brand new , no problems , very pleased", "triples": [{"uid": "173-0", "sentiment": "positive", "target_tags": "Great\\O value\\O ,\\O fast\\O delivery\\B --\\O Computer\\O works\\O as\\O if\\O brand\\O new\\O ,\\O no\\O problems\\O ,\\O very\\O pleased\\O", "opinion_tags": "Great\\O value\\O ,\\O fast\\B delivery\\O --\\O Computer\\O works\\O as\\O if\\O brand\\O new\\O ,\\O no\\O problems\\O ,\\O very\\O pleased\\O"}, {"uid": "173-1", "sentiment": "positive", "target_tags": "Great\\O value\\O ,\\O fast\\O delivery\\B --\\O Computer\\O works\\O as\\O if\\O brand\\O new\\O ,\\O no\\O problems\\O ,\\O very\\O pleased\\O", "opinion_tags": "Great\\O value\\O ,\\O fast\\O delivery\\O --\\O Computer\\O works\\O as\\O if\\O brand\\O new\\O ,\\O no\\O problems\\O ,\\O very\\O pleased\\B"}, {"uid": "173-2", "sentiment": "positive", "target_tags": "Great\\O value\\B ,\\O fast\\O delivery\\O --\\O Computer\\O works\\O as\\O if\\O brand\\O new\\O ,\\O no\\O problems\\O ,\\O very\\O pleased\\O", "opinion_tags": "Great\\B value\\O ,\\O fast\\O delivery\\O --\\O Computer\\O works\\O as\\O if\\O brand\\O new\\O ,\\O no\\O problems\\O ,\\O very\\O pleased\\O"}, {"uid": "173-3", "sentiment": "positive", "target_tags": "Great\\O value\\B ,\\O fast\\O delivery\\O --\\O Computer\\O works\\O as\\O if\\O brand\\O new\\O ,\\O no\\O problems\\O ,\\O very\\O pleased\\O", "opinion_tags": "Great\\O value\\O ,\\O fast\\O delivery\\O --\\O Computer\\O works\\O as\\O if\\O brand\\O new\\O ,\\O no\\O problems\\O ,\\O very\\O pleased\\B"}], "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "NN", "VBZ", "IN", "IN", "NN", "JJ", ",", "DT", "NNS", ",", "RB", "JJ"], "head": [2, 0, 2, 5, 2, 2, 8, 2, 12, 12, 12, 8, 15, 15, 2, 2, 18, 2], "deprel": ["amod", "root", "punct", "amod", "list", "punct", "nsubj", "parataxis", "mark", "mark", "nsubj", "advcl", "punct", "det", "parataxis", "punct", "advmod", "parataxis"]}, {"id": "174", "sentence": "so the fact that the computer does not work on the 24 twenty fourth day is my fault .", "triples": [{"uid": "174-0", "sentiment": "negative", "target_tags": "so\\O the\\O fact\\O that\\O the\\O computer\\O does\\O not\\O work\\B on\\O the\\O 24\\O twenty\\O fourth\\O day\\O is\\O my\\O fault\\O .\\O", "opinion_tags": "so\\O the\\O fact\\O that\\O the\\O computer\\O does\\B not\\I work\\O on\\O the\\O 24\\O twenty\\O fourth\\O day\\O is\\O my\\O fault\\O .\\O"}], "postag": ["RB", "DT", "NN", "IN", "DT", "NN", "VBZ", "RB", "VB", "IN", "DT", "CD", "CD", "JJ", "NN", "VBZ", "PRP$", "NN", "."], "head": [18, 3, 18, 9, 6, 9, 9, 9, 3, 15, 15, 15, 15, 15, 9, 18, 18, 0, 18], "deprel": ["advmod", "det", "nsubj", "mark", "det", "nsubj", "aux", "advmod", "acl", "case", "det", "nummod", "nummod", "amod", "obl", "cop", "nmod:poss", "root", "punct"]}, {"id": "175", "sentence": "I would definitely reccomend this if you are in the market for an ease to use , stylish , fun , awesome computer .", "triples": [{"uid": "175-0", "sentiment": "positive", "target_tags": "I\\O would\\O definitely\\O reccomend\\O this\\O if\\O you\\O are\\O in\\O the\\O market\\O for\\O an\\O ease\\O to\\O use\\B ,\\O stylish\\O ,\\O fun\\O ,\\O awesome\\O computer\\O .\\O", "opinion_tags": "I\\O would\\O definitely\\O reccomend\\O this\\O if\\O you\\O are\\O in\\O the\\O market\\O for\\O an\\O ease\\B to\\O use\\O ,\\O stylish\\O ,\\O fun\\O ,\\O awesome\\O computer\\O .\\O"}], "postag": ["PRP", "MD", "RB", "VB", "DT", "IN", "PRP", "VBP", "IN", "DT", "NN", "IN", "DT", "NN", "IN", "VB", ",", "JJ", ",", "JJ", ",", "JJ", "NN", "."], "head": [4, 4, 4, 0, 4, 11, 11, 11, 11, 11, 4, 14, 14, 11, 23, 23, 18, 23, 20, 16, 23, 23, 14, 4], "deprel": ["nsubj", "aux", "advmod", "root", "obj", "mark", "nsubj", "cop", "case", "det", "advcl", "case", "det", "nmod", "case", "amod", "punct", "amod", "punct", "conj", "punct", "amod", "nmod", "punct"]}, {"id": "176", "sentence": "The Windows 7 Starter is , in my opinion , a great way to think about using your netbook : basics , basics , basics .", "triples": [{"uid": "176-0", "sentiment": "positive", "target_tags": "The\\O Windows\\B 7\\I Starter\\I is\\O ,\\O in\\O my\\O opinion\\O ,\\O a\\O great\\O way\\O to\\O think\\O about\\O using\\O your\\O netbook\\O :\\O basics\\O ,\\O basics\\O ,\\O basics\\O .\\O", "opinion_tags": "The\\O Windows\\O 7\\O Starter\\O is\\O ,\\O in\\O my\\O opinion\\O ,\\O a\\O great\\B way\\O to\\O think\\O about\\O using\\O your\\O netbook\\O :\\O basics\\O ,\\O basics\\O ,\\O basics\\O .\\O"}], "postag": ["DT", "NNP", "CD", "NNP", "VBZ", ",", "IN", "PRP$", "NN", ",", "DT", "JJ", "NN", "TO", "VB", "IN", "VBG", "PRP$", "NN", ":", "NNS", ",", "NNS", ",", "NNS", "."], "head": [4, 4, 2, 13, 13, 13, 9, 9, 13, 13, 13, 13, 0, 15, 13, 17, 15, 19, 17, 21, 19, 23, 21, 25, 21, 13], "deprel": ["det", "compound", "nummod", "nsubj", "cop", "punct", "case", "nmod:poss", "obl", "punct", "det", "amod", "root", "mark", "acl", "mark", "advcl", "nmod:poss", "obj", "punct", "appos", "punct", "conj", "punct", "conj", "punct"]}, {"id": "177", "sentence": "So we exchanged it for the same on and after 2 hours it did n't work .", "triples": [{"uid": "177-0", "sentiment": "negative", "target_tags": "So\\O we\\O exchanged\\O it\\O for\\O the\\O same\\O on\\O and\\O after\\O 2\\O hours\\O it\\O did\\O n't\\O work\\B .\\O", "opinion_tags": "So\\O we\\O exchanged\\O it\\O for\\O the\\O same\\O on\\O and\\O after\\O 2\\O hours\\O it\\O did\\B n't\\I work\\O .\\O"}], "postag": ["RB", "PRP", "VBD", "PRP", "IN", "DT", "JJ", "IN", "CC", "IN", "CD", "NNS", "PRP", "VBD", "RB", "VB", "."], "head": [3, 3, 0, 3, 7, 7, 3, 12, 16, 12, 12, 16, 16, 16, 16, 3, 3], "deprel": ["advmod", "nsubj", "root", "obj", "case", "det", "obl", "case", "cc", "case", "nummod", "obl", "nsubj", "aux", "advmod", "conj", "punct"]}, {"id": "178", "sentence": "The OS is still as fast as the day that the laptop was purchased and continues to run flawlessly .", "triples": [{"uid": "178-0", "sentiment": "positive", "target_tags": "The\\O OS\\B is\\O still\\O as\\O fast\\O as\\O the\\O day\\O that\\O the\\O laptop\\O was\\O purchased\\O and\\O continues\\O to\\O run\\O flawlessly\\O .\\O", "opinion_tags": "The\\O OS\\O is\\O still\\O as\\O fast\\B as\\O the\\O day\\O that\\O the\\O laptop\\O was\\O purchased\\O and\\O continues\\O to\\O run\\O flawlessly\\O .\\O"}, {"uid": "178-1", "sentiment": "positive", "target_tags": "The\\O OS\\O is\\O still\\O as\\O fast\\O as\\O the\\O day\\O that\\O the\\O laptop\\O was\\O purchased\\O and\\O continues\\O to\\O run\\B flawlessly\\O .\\O", "opinion_tags": "The\\O OS\\O is\\O still\\O as\\O fast\\O as\\O the\\O day\\O that\\O the\\O laptop\\O was\\O purchased\\O and\\O continues\\O to\\O run\\O flawlessly\\B .\\O"}], "postag": ["DT", "NNP", "VBZ", "RB", "RB", "JJ", "IN", "DT", "NN", "WDT", "DT", "NN", "VBD", "VBN", "CC", "VBZ", "TO", "VB", "RB", "."], "head": [2, 6, 6, 6, 6, 0, 9, 9, 6, 14, 12, 14, 14, 9, 16, 6, 18, 16, 18, 6], "deprel": ["det", "nsubj", "cop", "advmod", "advmod", "root", "case", "det", "obl", "mark", "det", "nsubj:pass", "aux:pass", "acl:relcl", "cc", "conj", "mark", "xcomp", "advmod", "punct"]}, {"id": "179", "sentence": "After way too many times sending the thing in for repairs ( delivery service was slow , and without the laptop I had no access to the internet , and thus no way of tracking it to find out when I might hope to see my computer again ) , it finally kicked the bucket after just over 2 years .", "triples": [{"uid": "179-0", "sentiment": "negative", "target_tags": "After\\O way\\O too\\O many\\O times\\O sending\\O the\\O thing\\O in\\O for\\O repairs\\O (\\O delivery\\B service\\I was\\O slow\\O ,\\O and\\O without\\O the\\O laptop\\O I\\O had\\O no\\O access\\O to\\O the\\O internet\\O ,\\O and\\O thus\\O no\\O way\\O of\\O tracking\\O it\\O to\\O find\\O out\\O when\\O I\\O might\\O hope\\O to\\O see\\O my\\O computer\\O again\\O )\\O ,\\O it\\O finally\\O kicked\\O the\\O bucket\\O after\\O just\\O over\\O 2\\O years\\O .\\O", "opinion_tags": "After\\O way\\O too\\O many\\O times\\O sending\\O the\\O thing\\O in\\O for\\O repairs\\O (\\O delivery\\O service\\O was\\O slow\\B ,\\O and\\O without\\O the\\O laptop\\O I\\O had\\O no\\O access\\O to\\O the\\O internet\\O ,\\O and\\O thus\\O no\\O way\\O of\\O tracking\\O it\\O to\\O find\\O out\\O when\\O I\\O might\\O hope\\O to\\O see\\O my\\O computer\\O again\\O )\\O ,\\O it\\O finally\\O kicked\\O the\\O bucket\\O after\\O just\\O over\\O 2\\O years\\O .\\O"}], "postag": ["IN", "RB", "RB", "JJ", "NNS", "VBG", "DT", "NN", "IN", "IN", "NNS", "-LRB-", "NN", "NN", "VBD", "JJ", ",", "CC", "IN", "DT", "NN", "PRP", "VBD", "DT", "NN", "IN", "DT", "NN", ",", "CC", "RB", "DT", "NN", "IN", "VBG", "PRP", "TO", "VB", "RP", "WRB", "PRP", "MD", "VB", "TO", "VB", "PRP$", "NN", "RB", "-RRB-", ",", "PRP", "RB", "VBD", "DT", "NN", "IN", "RB", "RB", "CD", "NNS", "."], "head": [5, 4, 4, 5, 6, 53, 8, 6, 11, 11, 6, 16, 14, 16, 16, 6, 23, 23, 21, 21, 23, 23, 16, 25, 23, 28, 28, 25, 33, 33, 33, 33, 16, 35, 33, 35, 38, 35, 38, 43, 43, 43, 38, 45, 43, 47, 45, 45, 16, 53, 53, 53, 0, 55, 53, 60, 59, 59, 60, 53, 53], "deprel": ["case", "advmod", "advmod", "amod", "obl", "advcl", "det", "obj", "case", "case", "obl", "punct", "compound", "nsubj", "cop", "parataxis", "punct", "cc", "case", "det", "obl", "nsubj", "conj", "det", "obj", "case", "det", "nmod", "punct", "cc", "advmod", "det", "conj", "mark", "acl", "obj", "mark", "xcomp", "compound:prt", "mark", "nsubj", "aux", "advcl", "mark", "xcomp", "nmod:poss", "obj", "advmod", "punct", "punct", "nsubj", "advmod", "root", "det", "obj", "case", "advmod", "advmod", "nummod", "obl", "punct"]}, {"id": "180", "sentence": "The only thing I wish this had was the option to turn off the touchpad with a button like my big 16 '' laptop does .", "triples": [{"uid": "180-0", "sentiment": "negative", "target_tags": "The\\O only\\O thing\\O I\\O wish\\O this\\O had\\O was\\O the\\O option\\O to\\O turn\\O off\\O the\\O touchpad\\B with\\O a\\O button\\O like\\O my\\O big\\O 16\\O ''\\O laptop\\O does\\O .\\O", "opinion_tags": "The\\O only\\O thing\\O I\\O wish\\O this\\O had\\O was\\O the\\O option\\O to\\O turn\\B off\\I the\\O touchpad\\O with\\O a\\O button\\O like\\O my\\O big\\O 16\\O ''\\O laptop\\O does\\O .\\O"}], "postag": ["DT", "JJ", "NN", "PRP", "VBP", "DT", "VBD", "VBD", "DT", "NN", "TO", "VB", "RP", "DT", "NN", "IN", "DT", "NN", "IN", "PRP$", "JJ", "CD", "''", "NN", "VBZ", "."], "head": [3, 3, 10, 5, 3, 5, 3, 10, 10, 0, 12, 10, 12, 15, 12, 18, 18, 12, 25, 24, 24, 24, 24, 25, 18, 10], "deprel": ["det", "amod", "nsubj", "nsubj", "acl:relcl", "obj", "acl:relcl", "cop", "det", "root", "mark", "acl", "compound:prt", "det", "obj", "case", "det", "obl", "mark", "nmod:poss", "amod", "nummod", "punct", "nsubj", "acl", "punct"]}, {"id": "181", "sentence": "I especially like the backlit keyboard .", "triples": [{"uid": "181-0", "sentiment": "positive", "target_tags": "I\\O especially\\O like\\O the\\O backlit\\B keyboard\\I .\\O", "opinion_tags": "I\\O especially\\O like\\B the\\O backlit\\O keyboard\\O .\\O"}], "postag": ["PRP", "RB", "VBP", "DT", "NN", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "compound", "obj", "punct"]}, {"id": "182", "sentence": "The buttons you have to press a little harder than most .", "triples": [{"uid": "182-0", "sentiment": "negative", "target_tags": "The\\O buttons\\B you\\O have\\O to\\O press\\O a\\O little\\O harder\\O than\\O most\\O .\\O", "opinion_tags": "The\\O buttons\\O you\\O have\\O to\\O press\\O a\\O little\\O harder\\B than\\O most\\O .\\O"}], "postag": ["DT", "NNS", "PRP", "VBP", "TO", "VB", "DT", "JJ", "JJR", "IN", "JJS", "."], "head": [2, 0, 4, 2, 6, 4, 8, 9, 6, 11, 9, 9], "deprel": ["det", "root", "nsubj", "acl:relcl", "mark", "xcomp", "det", "obl:npmod", "advmod", "case", "obl", "punct"]}, {"id": "183", "sentence": "But to be honest , I do n't use my computer for anything like graphics editing and complex data analysis and gaming .", "triples": [{"uid": "183-0", "sentiment": "neutral", "target_tags": "But\\O to\\O be\\O honest\\O ,\\O I\\O do\\O n't\\O use\\O my\\O computer\\O for\\O anything\\O like\\O graphics\\O editing\\O and\\O complex\\O data\\O analysis\\O and\\O gaming\\B .\\O", "opinion_tags": "But\\O to\\O be\\O honest\\O ,\\O I\\O do\\B n't\\I use\\I my\\O computer\\O for\\O anything\\O like\\O graphics\\O editing\\O and\\O complex\\O data\\O analysis\\O and\\O gaming\\O .\\O"}, {"uid": "183-1", "sentiment": "neutral", "target_tags": "But\\O to\\O be\\O honest\\O ,\\O I\\O do\\O n't\\O use\\O my\\O computer\\O for\\O anything\\O like\\O graphics\\B editing\\I and\\O complex\\O data\\O analysis\\O and\\O gaming\\O .\\O", "opinion_tags": "But\\O to\\O be\\O honest\\O ,\\O I\\O do\\B n't\\I use\\I my\\O computer\\O for\\O anything\\O like\\O graphics\\O editing\\O and\\O complex\\O data\\O analysis\\O and\\O gaming\\O .\\O"}, {"uid": "183-2", "sentiment": "neutral", "target_tags": "But\\O to\\O be\\O honest\\O ,\\O I\\O do\\O n't\\O use\\O my\\O computer\\O for\\O anything\\O like\\O graphics\\O editing\\O and\\O complex\\B data\\I analysis\\I and\\O gaming\\O .\\O", "opinion_tags": "But\\O to\\O be\\O honest\\O ,\\O I\\O do\\B n't\\I use\\I my\\O computer\\O for\\O anything\\O like\\O graphics\\O editing\\O and\\O complex\\O data\\O analysis\\O and\\O gaming\\O .\\O"}], "postag": ["CC", "TO", "VB", "JJ", ",", "PRP", "VBP", "RB", "VB", "PRP$", "NN", "IN", "NN", "IN", "NNS", "NN", "CC", "JJ", "NN", "NN", "CC", "NN", "."], "head": [9, 4, 4, 9, 9, 9, 9, 9, 0, 11, 9, 13, 9, 16, 16, 13, 20, 20, 20, 16, 22, 16, 9], "deprel": ["cc", "mark", "cop", "advcl", "punct", "nsubj", "aux", "advmod", "root", "nmod:poss", "obj", "case", "obl", "case", "compound", "nmod", "cc", "amod", "compound", "conj", "cc", "conj", "punct"]}, {"id": "184", "sentence": "The one thing I wish it had was a detailed hardcopy manuel .", "triples": [{"uid": "184-0", "sentiment": "negative", "target_tags": "The\\O one\\O thing\\O I\\O wish\\O it\\O had\\O was\\O a\\O detailed\\O hardcopy\\B manuel\\I .\\O", "opinion_tags": "The\\O one\\O thing\\O I\\O wish\\B it\\O had\\O was\\O a\\O detailed\\O hardcopy\\O manuel\\O .\\O"}, {"uid": "184-1", "sentiment": "negative", "target_tags": "The\\O one\\O thing\\O I\\O wish\\O it\\O had\\O was\\O a\\O detailed\\O hardcopy\\B manuel\\I .\\O", "opinion_tags": "The\\O one\\O thing\\O I\\O wish\\O it\\O had\\O was\\O a\\O detailed\\B hardcopy\\O manuel\\O .\\O"}], "postag": ["DT", "CD", "NN", "PRP", "VBP", "PRP", "VBD", "VBD", "DT", "JJ", "NN", "NN", "."], "head": [3, 3, 12, 5, 3, 7, 3, 12, 12, 12, 12, 0, 12], "deprel": ["det", "nummod", "nsubj", "nsubj", "acl:relcl", "nsubj", "acl:relcl", "cop", "det", "amod", "compound", "root", "punct"]}, {"id": "185", "sentence": "the mcbook pro notebook will make it easy for you to write and read your emails at blazing speeds .", "triples": [{"uid": "185-0", "sentiment": "positive", "target_tags": "the\\O mcbook\\O pro\\O notebook\\O will\\O make\\O it\\O easy\\O for\\O you\\O to\\O write\\O and\\O read\\O your\\O emails\\O at\\O blazing\\O speeds\\B .\\O", "opinion_tags": "the\\O mcbook\\O pro\\O notebook\\O will\\O make\\O it\\O easy\\B for\\O you\\O to\\O write\\O and\\O read\\O your\\O emails\\O at\\O blazing\\O speeds\\O .\\O"}, {"uid": "185-1", "sentiment": "positive", "target_tags": "the\\O mcbook\\O pro\\O notebook\\O will\\O make\\O it\\O easy\\O for\\O you\\O to\\O write\\O and\\O read\\O your\\O emails\\O at\\O blazing\\O speeds\\B .\\O", "opinion_tags": "the\\O mcbook\\O pro\\O notebook\\O will\\O make\\O it\\O easy\\O for\\O you\\O to\\O write\\O and\\O read\\O your\\O emails\\O at\\O blazing\\B speeds\\O .\\O"}], "postag": ["DT", "JJ", "NN", "NN", "MD", "VB", "PRP", "JJ", "IN", "PRP", "TO", "VB", "CC", "VB", "PRP$", "NNS", "IN", "VBG", "NNS", "."], "head": [4, 4, 4, 6, 6, 0, 6, 6, 12, 12, 12, 8, 14, 12, 16, 14, 19, 19, 14, 6], "deprel": ["det", "amod", "compound", "nsubj", "aux", "root", "expl", "xcomp", "mark", "nsubj", "mark", "ccomp", "cc", "conj", "nmod:poss", "obj", "case", "amod", "obl", "punct"]}, {"id": "186", "sentence": "However , I love this particular Mac because its very fast , great size , and has fantastic features like the lighted keyboard and easy mouse pad .", "triples": [{"uid": "186-0", "sentiment": "positive", "target_tags": "However\\O ,\\O I\\O love\\O this\\O particular\\O Mac\\O because\\O its\\O very\\O fast\\O ,\\O great\\O size\\B ,\\O and\\O has\\O fantastic\\O features\\O like\\O the\\O lighted\\O keyboard\\O and\\O easy\\O mouse\\O pad\\O .\\O", "opinion_tags": "However\\O ,\\O I\\O love\\O this\\O particular\\O Mac\\O because\\O its\\O very\\O fast\\O ,\\O great\\B size\\O ,\\O and\\O has\\O fantastic\\O features\\O like\\O the\\O lighted\\O keyboard\\O and\\O easy\\O mouse\\O pad\\O .\\O"}, {"uid": "186-1", "sentiment": "positive", "target_tags": "However\\O ,\\O I\\O love\\O this\\O particular\\O Mac\\O because\\O its\\O very\\O fast\\O ,\\O great\\O size\\O ,\\O and\\O has\\O fantastic\\O features\\B like\\O the\\O lighted\\O keyboard\\O and\\O easy\\O mouse\\O pad\\O .\\O", "opinion_tags": "However\\O ,\\O I\\O love\\O this\\O particular\\O Mac\\O because\\O its\\O very\\O fast\\O ,\\O great\\O size\\O ,\\O and\\O has\\O fantastic\\B features\\O like\\O the\\O lighted\\O keyboard\\O and\\O easy\\O mouse\\O pad\\O .\\O"}, {"uid": "186-2", "sentiment": "positive", "target_tags": "However\\O ,\\O I\\O love\\O this\\O particular\\O Mac\\O because\\O its\\O very\\O fast\\O ,\\O great\\O size\\O ,\\O and\\O has\\O fantastic\\O features\\O like\\O the\\O lighted\\B keyboard\\I and\\O easy\\O mouse\\O pad\\O .\\O", "opinion_tags": "However\\O ,\\O I\\O love\\O this\\O particular\\O Mac\\O because\\O its\\O very\\O fast\\O ,\\O great\\O size\\O ,\\O and\\O has\\O fantastic\\B features\\O like\\O the\\O lighted\\O keyboard\\O and\\O easy\\O mouse\\O pad\\O .\\O"}, {"uid": "186-3", "sentiment": "positive", "target_tags": "However\\O ,\\O I\\O love\\O this\\O particular\\O Mac\\O because\\O its\\O very\\O fast\\O ,\\O great\\O size\\O ,\\O and\\O has\\O fantastic\\O features\\O like\\O the\\O lighted\\O keyboard\\O and\\O easy\\O mouse\\B pad\\I .\\O", "opinion_tags": "However\\O ,\\O I\\O love\\O this\\O particular\\O Mac\\O because\\O its\\O very\\O fast\\O ,\\O great\\O size\\O ,\\O and\\O has\\O fantastic\\B features\\O like\\O the\\O lighted\\O keyboard\\O and\\O easy\\O mouse\\O pad\\O .\\O"}, {"uid": "186-4", "sentiment": "positive", "target_tags": "However\\O ,\\O I\\O love\\O this\\O particular\\O Mac\\O because\\O its\\O very\\O fast\\O ,\\O great\\O size\\O ,\\O and\\O has\\O fantastic\\O features\\O like\\O the\\O lighted\\O keyboard\\O and\\O easy\\O mouse\\B pad\\I .\\O", "opinion_tags": "However\\O ,\\O I\\O love\\O this\\O particular\\O Mac\\O because\\O its\\O very\\O fast\\O ,\\O great\\O size\\O ,\\O and\\O has\\O fantastic\\O features\\O like\\O the\\O lighted\\O keyboard\\O and\\O easy\\B mouse\\O pad\\O .\\O"}], "postag": ["RB", ",", "PRP", "VBP", "DT", "JJ", "NNP", "IN", "PRP$", "RB", "JJ", ",", "JJ", "NN", ",", "CC", "VBZ", "JJ", "NNS", "IN", "DT", "VBN", "NN", "CC", "JJ", "NN", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 14, 14, 11, 14, 14, 14, 4, 17, 17, 4, 19, 17, 23, 23, 23, 19, 27, 27, 27, 23, 4], "deprel": ["advmod", "punct", "nsubj", "root", "det", "amod", "obj", "mark", "nmod:poss", "advmod", "amod", "punct", "amod", "advcl", "punct", "cc", "conj", "amod", "obj", "case", "det", "amod", "nmod", "cc", "amod", "compound", "conj", "punct"]}, {"id": "187", "sentence": "We paid for the three year warranty and the extended warranty after that one ended as well .", "triples": [{"uid": "187-0", "sentiment": "neutral", "target_tags": "We\\O paid\\O for\\O the\\O three\\B year\\I warranty\\I and\\O the\\O extended\\O warranty\\O after\\O that\\O one\\O ended\\O as\\O well\\O .\\O", "opinion_tags": "We\\O paid\\B for\\I the\\O three\\O year\\O warranty\\O and\\O the\\O extended\\O warranty\\O after\\O that\\O one\\O ended\\O as\\O well\\O .\\O"}, {"uid": "187-1", "sentiment": "neutral", "target_tags": "We\\O paid\\O for\\O the\\O three\\O year\\O warranty\\O and\\O the\\O extended\\B warranty\\I after\\O that\\O one\\O ended\\O as\\O well\\O .\\O", "opinion_tags": "We\\O paid\\B for\\I the\\O three\\O year\\O warranty\\O and\\O the\\O extended\\O warranty\\O after\\O that\\O one\\O ended\\O as\\O well\\O .\\O"}], "postag": ["PRP", "VBD", "IN", "DT", "CD", "NN", "NN", "CC", "DT", "VBN", "NN", "IN", "DT", "NN", "VBD", "RB", "RB", "."], "head": [2, 0, 7, 7, 6, 7, 2, 15, 11, 11, 15, 14, 14, 11, 2, 15, 16, 2], "deprel": ["nsubj", "root", "case", "det", "nummod", "compound", "obl", "cc", "det", "amod", "nsubj", "case", "det", "nmod", "conj", "advmod", "fixed", "punct"]}, {"id": "188", "sentence": "My husband got me this for <PERSON><PERSON> after getting me a very expensive laptop that did not work after 2 days .", "triples": [{"uid": "188-0", "sentiment": "negative", "target_tags": "My\\O husband\\O got\\O me\\O this\\O for\\O Chrismas\\O after\\O getting\\O me\\O a\\O very\\O expensive\\O laptop\\O that\\O did\\O not\\O work\\B after\\O 2\\O days\\O .\\O", "opinion_tags": "My\\O husband\\O got\\O me\\O this\\O for\\O Chrismas\\O after\\O getting\\O me\\O a\\O very\\O expensive\\O laptop\\O that\\O did\\O not\\B work\\O after\\O 2\\O days\\O .\\O"}], "postag": ["PRP$", "NN", "VBD", "PRP", "DT", "IN", "NNP", "IN", "VBG", "PRP", "DT", "RB", "JJ", "NN", "WDT", "VBD", "RB", "VB", "IN", "CD", "NNS", "."], "head": [2, 3, 0, 3, 3, 7, 3, 9, 3, 9, 14, 13, 14, 9, 18, 18, 18, 14, 21, 21, 18, 3], "deprel": ["nmod:poss", "nsubj", "root", "i<PERSON><PERSON>", "obj", "case", "obl", "mark", "advcl", "i<PERSON><PERSON>", "det", "advmod", "amod", "xcomp", "nsubj", "aux", "advmod", "acl:relcl", "case", "nummod", "obl", "punct"]}, {"id": "189", "sentence": "The resolution is even higher then any other laptop on the market .", "triples": [{"uid": "189-0", "sentiment": "positive", "target_tags": "The\\O resolution\\B is\\O even\\O higher\\O then\\O any\\O other\\O laptop\\O on\\O the\\O market\\O .\\O", "opinion_tags": "The\\O resolution\\O is\\O even\\O higher\\B then\\O any\\O other\\O laptop\\O on\\O the\\O market\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJR", "RB", "DT", "JJ", "NN", "IN", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 9, 9, 9, 5, 12, 12, 9, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "advmod", "det", "amod", "parataxis", "case", "det", "nmod", "punct"]}, {"id": "190", "sentence": "But with A WAY Bigger Screen , and IS able to connect to an HDMI .", "triples": [{"uid": "190-0", "sentiment": "positive", "target_tags": "But\\O with\\O A\\O WAY\\O Bigger\\O Screen\\B ,\\O and\\O IS\\O able\\O to\\O connect\\O to\\O an\\O HDMI\\O .\\O", "opinion_tags": "But\\O with\\O A\\O WAY\\O Bigger\\B Screen\\O ,\\O and\\O IS\\O able\\O to\\O connect\\O to\\O an\\O HDMI\\O .\\O"}, {"uid": "190-1", "sentiment": "neutral", "target_tags": "But\\O with\\O A\\O WAY\\O Bigger\\O Screen\\O ,\\O and\\O IS\\O able\\O to\\O connect\\O to\\O an\\O HDMI\\B .\\O", "opinion_tags": "But\\O with\\O A\\O WAY\\O Bigger\\O Screen\\O ,\\O and\\O IS\\O able\\B to\\I connect\\I to\\O an\\O HDMI\\O .\\O"}], "postag": ["CC", "IN", "DT", "NN", "JJR", "NN", ",", "CC", "VBZ", "JJ", "TO", "VB", "IN", "DT", "NN", "."], "head": [10, 4, 4, 6, 6, 0, 10, 10, 10, 6, 12, 10, 15, 15, 12, 6], "deprel": ["cc", "case", "det", "nmod", "amod", "root", "punct", "cc", "cop", "conj", "mark", "xcomp", "case", "det", "obl", "punct"]}, {"id": "191", "sentence": "the graphics are awful and the wireless switch it at the top rather than the side which I am used to it being on the side .", "triples": [{"uid": "191-0", "sentiment": "negative", "target_tags": "the\\O graphics\\B are\\O awful\\O and\\O the\\O wireless\\O switch\\O it\\O at\\O the\\O top\\O rather\\O than\\O the\\O side\\O which\\O I\\O am\\O used\\O to\\O it\\O being\\O on\\O the\\O side\\O .\\O", "opinion_tags": "the\\O graphics\\O are\\O awful\\B and\\O the\\O wireless\\O switch\\O it\\O at\\O the\\O top\\O rather\\O than\\O the\\O side\\O which\\O I\\O am\\O used\\O to\\O it\\O being\\O on\\O the\\O side\\O .\\O"}], "postag": ["DT", "NNS", "VBP", "JJ", "CC", "DT", "JJ", "VBP", "PRP", "IN", "DT", "NN", "RB", "IN", "DT", "NN", "WDT", "PRP", "VBP", "VBN", "IN", "PRP", "VBG", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 8, 7, 8, 4, 8, 12, 12, 8, 16, 13, 16, 12, 20, 20, 20, 16, 22, 20, 26, 26, 26, 20, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "conj", "obj", "case", "det", "obl", "cc", "fixed", "det", "conj", "obj", "nsubj:pass", "aux:pass", "acl:relcl", "case", "obl", "cop", "case", "det", "advcl", "punct"]}, {"id": "192", "sentence": "The speakers are terrible and are probably the cheapest ones I have ever seen in a laptop so if your planning to listen to music I suggest you get something better .", "triples": [{"uid": "192-0", "sentiment": "negative", "target_tags": "The\\O speakers\\B are\\O terrible\\O and\\O are\\O probably\\O the\\O cheapest\\O ones\\O I\\O have\\O ever\\O seen\\O in\\O a\\O laptop\\O so\\O if\\O your\\O planning\\O to\\O listen\\O to\\O music\\O I\\O suggest\\O you\\O get\\O something\\O better\\O .\\O", "opinion_tags": "The\\O speakers\\O are\\O terrible\\B and\\O are\\O probably\\O the\\O cheapest\\O ones\\O I\\O have\\O ever\\O seen\\O in\\O a\\O laptop\\O so\\O if\\O your\\O planning\\O to\\O listen\\O to\\O music\\O I\\O suggest\\O you\\O get\\O something\\O better\\O .\\O"}, {"uid": "192-1", "sentiment": "negative", "target_tags": "The\\O speakers\\B are\\O terrible\\O and\\O are\\O probably\\O the\\O cheapest\\O ones\\O I\\O have\\O ever\\O seen\\O in\\O a\\O laptop\\O so\\O if\\O your\\O planning\\O to\\O listen\\O to\\O music\\O I\\O suggest\\O you\\O get\\O something\\O better\\O .\\O", "opinion_tags": "The\\O speakers\\O are\\O terrible\\O and\\O are\\O probably\\O the\\O cheapest\\B ones\\O I\\O have\\O ever\\O seen\\O in\\O a\\O laptop\\O so\\O if\\O your\\O planning\\O to\\O listen\\O to\\O music\\O I\\O suggest\\O you\\O get\\O something\\O better\\O .\\O"}], "postag": ["DT", "NNS", "VBP", "JJ", "CC", "VBP", "RB", "DT", "JJS", "NNS", "PRP", "VBP", "RB", "VBN", "IN", "DT", "NN", "RB", "IN", "PRP$", "NN", "TO", "VB", "IN", "NN", "PRP", "VBP", "PRP", "VBP", "NN", "JJR", "."], "head": [2, 4, 4, 0, 10, 10, 10, 10, 10, 4, 14, 14, 14, 10, 17, 17, 14, 27, 23, 21, 23, 23, 27, 25, 23, 27, 4, 29, 27, 29, 30, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "cop", "advmod", "det", "amod", "conj", "nsubj", "aux", "advmod", "acl:relcl", "case", "det", "obl", "advmod", "mark", "nmod:poss", "nsubj", "mark", "advcl", "case", "obl", "nsubj", "conj", "nsubj", "ccomp", "obj", "amod", "punct"]}, {"id": "193", "sentence": "I have had no problems with it unlike some hardware defects on past models .", "triples": [{"uid": "193-0", "sentiment": "negative", "target_tags": "I\\O have\\O had\\O no\\O problems\\O with\\O it\\O unlike\\O some\\O hardware\\B defects\\O on\\O past\\O models\\O .\\O", "opinion_tags": "I\\O have\\O had\\O no\\O problems\\O with\\O it\\O unlike\\O some\\O hardware\\O defects\\B on\\O past\\O models\\O .\\O"}], "postag": ["PRP", "VBP", "VBN", "DT", "NNS", "IN", "PRP", "IN", "DT", "NN", "NNS", "IN", "JJ", "NNS", "."], "head": [3, 3, 0, 5, 3, 7, 5, 11, 11, 11, 3, 14, 14, 11, 3], "deprel": ["nsubj", "aux", "root", "det", "obj", "case", "nmod", "case", "det", "compound", "obl", "case", "amod", "nmod", "punct"]}, {"id": "194", "sentence": "Not to mention the fact that your mac comes fully loaded with all necessary basic programs .", "triples": [{"uid": "194-0", "sentiment": "positive", "target_tags": "Not\\O to\\O mention\\O the\\O fact\\O that\\O your\\O mac\\O comes\\O fully\\O loaded\\O with\\O all\\O necessary\\O basic\\O programs\\B .\\O", "opinion_tags": "Not\\O to\\O mention\\O the\\O fact\\O that\\O your\\O mac\\O comes\\O fully\\O loaded\\O with\\O all\\O necessary\\B basic\\O programs\\O .\\O"}], "postag": ["RB", "TO", "VB", "DT", "NN", "IN", "PRP$", "NN", "VBZ", "RB", "VBN", "IN", "DT", "JJ", "JJ", "NNS", "."], "head": [5, 1, 5, 5, 0, 9, 8, 9, 5, 11, 9, 16, 16, 16, 16, 11, 5], "deprel": ["cc", "fixed", "case", "det", "root", "mark", "nmod:poss", "nsubj", "acl", "advmod", "xcomp", "case", "det", "amod", "amod", "obl", "punct"]}, {"id": "195", "sentence": "If this is an improvement in Customer Service then I would hate too see what it was before !", "triples": [{"uid": "195-0", "sentiment": "negative", "target_tags": "If\\O this\\O is\\O an\\O improvement\\O in\\O Customer\\B Service\\I then\\O I\\O would\\O hate\\O too\\O see\\O what\\O it\\O was\\O before\\O !\\O", "opinion_tags": "If\\O this\\O is\\O an\\O improvement\\B in\\O Customer\\O Service\\O then\\O I\\O would\\O hate\\O too\\O see\\O what\\O it\\O was\\O before\\O !\\O"}], "postag": ["IN", "DT", "VBZ", "DT", "NN", "IN", "NN", "NN", "RB", "PRP", "MD", "VB", "RB", "VB", "WP", "PRP", "VBD", "RB", "."], "head": [5, 5, 5, 5, 12, 8, 8, 5, 12, 12, 12, 0, 14, 12, 14, 15, 15, 15, 12], "deprel": ["mark", "nsubj", "cop", "det", "advcl", "case", "compound", "nmod", "advmod", "nsubj", "aux", "root", "advmod", "xcomp", "ccomp", "nsubj", "cop", "acl:relcl", "punct"]}, {"id": "196", "sentence": "For me I was lucky and a local store was selling them for $ 2000 off and Best Buy matched their price so I was able to buy one for under $ 1000", "triples": [{"uid": "196-0", "sentiment": "positive", "target_tags": "For\\O me\\O I\\O was\\O lucky\\O and\\O a\\O local\\O store\\O was\\O selling\\O them\\O for\\O $\\O 2000\\O off\\O and\\O Best\\O Buy\\O matched\\O their\\O price\\B so\\O I\\O was\\O able\\O to\\O buy\\O one\\O for\\O under\\O $\\O 1000\\O", "opinion_tags": "For\\O me\\O I\\O was\\O lucky\\B and\\O a\\O local\\O store\\O was\\O selling\\O them\\O for\\O $\\O 2000\\O off\\O and\\O Best\\O Buy\\O matched\\O their\\O price\\O so\\O I\\O was\\O able\\O to\\O buy\\O one\\O for\\O under\\O $\\O 1000\\O"}, {"uid": "196-1", "sentiment": "positive", "target_tags": "For\\O me\\O I\\O was\\O lucky\\O and\\O a\\O local\\O store\\O was\\O selling\\O them\\O for\\O $\\O 2000\\O off\\O and\\O Best\\O Buy\\O matched\\O their\\O price\\B so\\O I\\O was\\O able\\O to\\O buy\\O one\\O for\\O under\\O $\\O 1000\\O", "opinion_tags": "For\\O me\\O I\\O was\\O lucky\\O and\\O a\\O local\\O store\\O was\\O selling\\O them\\O for\\O $\\O 2000\\O off\\O and\\O Best\\B Buy\\O matched\\O their\\O price\\O so\\O I\\O was\\O able\\O to\\O buy\\O one\\O for\\O under\\O $\\O 1000\\O"}], "postag": ["IN", "PRP", "PRP", "VBD", "JJ", "CC", "DT", "JJ", "NN", "VBD", "VBG", "PRP", "IN", "$", "CD", "RB", "CC", "JJS", "NN", "VBD", "PRP$", "NN", "RB", "PRP", "VBD", "JJ", "TO", "VB", "CD", "IN", "IN", "$", "CD"], "head": [2, 5, 5, 5, 0, 11, 9, 9, 11, 11, 5, 11, 14, 11, 14, 14, 20, 19, 20, 5, 22, 20, 26, 26, 26, 20, 28, 26, 28, 32, 32, 28, 32], "deprel": ["case", "obl", "nsubj", "cop", "root", "cc", "det", "amod", "nsubj", "aux", "conj", "obj", "case", "obl", "nummod", "advmod", "cc", "amod", "nsubj", "conj", "nmod:poss", "obj", "advmod", "nsubj", "cop", "advcl", "mark", "xcomp", "obj", "case", "case", "obl", "nummod"]}, {"id": "197", "sentence": "The layout of the MacBook is horrible and confusing ;", "triples": [{"uid": "197-0", "sentiment": "negative", "target_tags": "The\\O layout\\B of\\O the\\O MacBook\\O is\\O horrible\\O and\\O confusing\\O ;\\O", "opinion_tags": "The\\O layout\\O of\\O the\\O MacBook\\O is\\O horrible\\B and\\O confusing\\O ;\\O"}, {"uid": "197-1", "sentiment": "negative", "target_tags": "The\\O layout\\B of\\O the\\O MacBook\\O is\\O horrible\\O and\\O confusing\\O ;\\O", "opinion_tags": "The\\O layout\\O of\\O the\\O MacBook\\O is\\O horrible\\O and\\O confusing\\B ;\\O"}], "postag": ["DT", "NN", "IN", "DT", "NNP", "VBZ", "JJ", "CC", "JJ", ":"], "head": [2, 7, 5, 5, 2, 7, 0, 9, 7, 7], "deprel": ["det", "nsubj", "case", "det", "nmod", "cop", "root", "cc", "conj", "punct"]}, {"id": "198", "sentence": "Was not happy with one of the programs on it .", "triples": [{"uid": "198-0", "sentiment": "negative", "target_tags": "Was\\O not\\O happy\\O with\\O one\\B of\\I the\\I programs\\I on\\O it\\O .\\O", "opinion_tags": "Was\\O not\\B happy\\I with\\O one\\O of\\O the\\O programs\\O on\\O it\\O .\\O"}], "postag": ["VBD", "RB", "JJ", "IN", "CD", "IN", "DT", "NNS", "IN", "PRP", "."], "head": [3, 3, 0, 5, 3, 8, 8, 5, 10, 8, 3], "deprel": ["cop", "advmod", "root", "case", "obl", "case", "det", "nmod", "case", "nmod", "punct"]}, {"id": "199", "sentence": "The first one sent : Touchpad did n't work The second sent : USB did n't work The third sent : Touchpad did n't work The fourth sent : Has n't arrived yet .", "triples": [{"uid": "199-0", "sentiment": "negative", "target_tags": "The\\O first\\O one\\O sent\\O :\\O Touchpad\\B did\\O n't\\O work\\O The\\O second\\O sent\\O :\\O USB\\O did\\O n't\\O work\\O The\\O third\\O sent\\O :\\O Touchpad\\O did\\O n't\\O work\\O The\\O fourth\\O sent\\O :\\O Has\\O n't\\O arrived\\O yet\\O .\\O", "opinion_tags": "The\\O first\\O one\\O sent\\O :\\O Touchpad\\O did\\B n't\\I work\\I The\\O second\\O sent\\O :\\O USB\\O did\\O n't\\O work\\O The\\O third\\O sent\\O :\\O Touchpad\\O did\\O n't\\O work\\O The\\O fourth\\O sent\\O :\\O Has\\O n't\\O arrived\\O yet\\O .\\O"}, {"uid": "199-1", "sentiment": "negative", "target_tags": "The\\O first\\O one\\O sent\\O :\\O Touchpad\\O did\\O n't\\O work\\O The\\O second\\O sent\\O :\\O USB\\B did\\O n't\\O work\\O The\\O third\\O sent\\O :\\O Touchpad\\O did\\O n't\\O work\\O The\\O fourth\\O sent\\O :\\O Has\\O n't\\O arrived\\O yet\\O .\\O", "opinion_tags": "The\\O first\\O one\\O sent\\O :\\O Touchpad\\O did\\O n't\\O work\\O The\\O second\\O sent\\O :\\O USB\\O did\\B n't\\I work\\I The\\O third\\O sent\\O :\\O Touchpad\\O did\\O n't\\O work\\O The\\O fourth\\O sent\\O :\\O Has\\O n't\\O arrived\\O yet\\O .\\O"}, {"uid": "199-2", "sentiment": "negative", "target_tags": "The\\O first\\O one\\O sent\\O :\\O Touchpad\\B did\\O n't\\O work\\O The\\O second\\O sent\\O :\\O USB\\O did\\O n't\\O work\\O The\\O third\\O sent\\O :\\O Touchpad\\O did\\O n't\\O work\\O The\\O fourth\\O sent\\O :\\O Has\\O n't\\O arrived\\O yet\\O .\\O", "opinion_tags": "The\\O first\\O one\\O sent\\O :\\O Touchpad\\O did\\B n't\\I work\\I The\\O second\\O sent\\O :\\O USB\\O did\\O n't\\O work\\O The\\O third\\O sent\\O :\\O Touchpad\\O did\\O n't\\O work\\O The\\O fourth\\O sent\\O :\\O Has\\O n't\\O arrived\\O yet\\O .\\O"}], "postag": ["DT", "JJ", "NN", "VBD", ":", "NNP", "VBD", "RB", "VB", "DT", "JJ", "VBN", ":", "NNP", "VBD", "RB", "VB", "DT", "JJ", "VBN", ":", "NNP", "VBD", "RB", "VB", "DT", "JJ", "VBN", ":", "VBZ", "RB", "VBN", "RB", "."], "head": [3, 3, 4, 0, 4, 9, 9, 9, 4, 11, 9, 9, 4, 17, 17, 17, 4, 19, 17, 17, 4, 25, 25, 25, 4, 27, 25, 25, 32, 32, 32, 25, 32, 4], "deprel": ["det", "amod", "nsubj", "root", "punct", "nsubj", "aux", "advmod", "ccomp", "det", "obj", "xcomp", "punct", "nsubj", "aux", "advmod", "parataxis", "det", "obj", "xcomp", "punct", "nsubj", "aux", "advmod", "parataxis", "det", "obj", "xcomp", "punct", "aux", "advmod", "parataxis", "advmod", "punct"]}, {"id": "200", "sentence": "In November my computer messed up entirely and would n't power on after intalling a Windows update , I had to have my HD flashed and lost EVERYTHING on it , including my school assignments and irriplaceable pictures that were only in digital format and several other things , when this update was installed for some reason I was unable to roll back the drivers and everything to an earlier working condition because when the update was installed it deleted my history .", "triples": [{"uid": "200-0", "sentiment": "neutral", "target_tags": "In\\O November\\O my\\O computer\\O messed\\O up\\O entirely\\O and\\O would\\O n't\\O power\\O on\\O after\\O intalling\\O a\\O Windows\\B update\\I ,\\O I\\O had\\O to\\O have\\O my\\O HD\\O flashed\\O and\\O lost\\O EVERYTHING\\O on\\O it\\O ,\\O including\\O my\\O school\\O assignments\\O and\\O irriplaceable\\O pictures\\O that\\O were\\O only\\O in\\O digital\\O format\\O and\\O several\\O other\\O things\\O ,\\O when\\O this\\O update\\O was\\O installed\\O for\\O some\\O reason\\O I\\O was\\O unable\\O to\\O roll\\O back\\O the\\O drivers\\O and\\O everything\\O to\\O an\\O earlier\\O working\\O condition\\O because\\O when\\O the\\O update\\O was\\O installed\\O it\\O deleted\\O my\\O history\\O .\\O", "opinion_tags": "In\\O November\\O my\\O computer\\O messed\\B up\\I entirely\\O and\\O would\\O n't\\O power\\O on\\O after\\O intalling\\O a\\O Windows\\O update\\O ,\\O I\\O had\\O to\\O have\\O my\\O HD\\O flashed\\O and\\O lost\\O EVERYTHING\\O on\\O it\\O ,\\O including\\O my\\O school\\O assignments\\O and\\O irriplaceable\\O pictures\\O that\\O were\\O only\\O in\\O digital\\O format\\O and\\O several\\O other\\O things\\O ,\\O when\\O this\\O update\\O was\\O installed\\O for\\O some\\O reason\\O I\\O was\\O unable\\O to\\O roll\\O back\\O the\\O drivers\\O and\\O everything\\O to\\O an\\O earlier\\O working\\O condition\\O because\\O when\\O the\\O update\\O was\\O installed\\O it\\O deleted\\O my\\O history\\O .\\O"}], "postag": ["IN", "NNP", "PRP$", "NN", "VBD", "RP", "RB", "CC", "MD", "RB", "VB", "RB", "IN", "VBG", "DT", "NN", "NN", ",", "PRP", "VBD", "TO", "VB", "PRP$", "NN", "VBN", "CC", "VBN", "NN", "IN", "PRP", ",", "VBG", "PRP$", "NN", "NNS", "CC", "JJ", "NNS", "WDT", "VBD", "RB", "IN", "JJ", "NN", "CC", "JJ", "JJ", "NNS", ",", "WRB", "DT", "NN", "VBD", "VBN", "IN", "DT", "NN", "PRP", "VBD", "JJ", "TO", "VB", "RB", "DT", "NNS", "CC", "NN", "IN", "DT", "JJR", "NN", "NN", "IN", "WRB", "DT", "NN", "VBD", "VBN", "PRP", "VBD", "PRP$", "NN", "."], "head": [2, 5, 4, 5, 0, 5, 5, 11, 11, 11, 5, 11, 14, 11, 17, 17, 14, 5, 20, 5, 22, 20, 24, 22, 22, 27, 22, 27, 30, 27, 35, 35, 35, 35, 27, 38, 38, 35, 44, 44, 44, 44, 44, 38, 48, 48, 48, 44, 5, 54, 52, 54, 54, 44, 57, 57, 54, 60, 60, 57, 62, 60, 62, 65, 62, 67, 65, 72, 72, 72, 72, 62, 78, 78, 76, 78, 78, 80, 80, 62, 82, 80, 5], "deprel": ["case", "obl", "nmod:poss", "nsubj", "root", "compound:prt", "advmod", "cc", "aux", "advmod", "conj", "advmod", "mark", "advcl", "det", "compound", "obj", "punct", "nsubj", "parataxis", "mark", "xcomp", "nmod:poss", "obj", "xcomp", "cc", "conj", "obj", "case", "obl", "punct", "case", "nmod:poss", "compound", "obl", "cc", "amod", "conj", "nsubj", "cop", "advmod", "case", "amod", "acl:relcl", "cc", "amod", "amod", "conj", "punct", "mark", "det", "nsubj:pass", "aux:pass", "advcl", "case", "det", "obl", "nsubj", "cop", "acl:relcl", "mark", "xcomp", "advmod", "det", "obj", "cc", "conj", "case", "det", "amod", "compound", "obl", "mark", "mark", "det", "nsubj:pass", "aux:pass", "advcl", "nsubj", "advcl", "nmod:poss", "obj", "punct"]}, {"id": "201", "sentence": "Overall : Poor , Features : Average , Performance : Poor , Battery Life : Excellent , Price -- Value : Poor", "triples": [{"uid": "201-0", "sentiment": "neutral", "target_tags": "Overall\\O :\\O Poor\\O ,\\O Features\\B :\\O Average\\O ,\\O Performance\\O :\\O Poor\\O ,\\O Battery\\O Life\\O :\\O Excellent\\O ,\\O Price\\O --\\O Value\\O :\\O Poor\\O", "opinion_tags": "Overall\\O :\\O Poor\\O ,\\O Features\\O :\\O Average\\B ,\\O Performance\\O :\\O Poor\\O ,\\O Battery\\O Life\\O :\\O Excellent\\O ,\\O Price\\O --\\O Value\\O :\\O Poor\\O"}, {"uid": "201-1", "sentiment": "negative", "target_tags": "Overall\\O :\\O Poor\\O ,\\O Features\\O :\\O Average\\O ,\\O Performance\\B :\\O Poor\\O ,\\O Battery\\O Life\\O :\\O Excellent\\O ,\\O Price\\O --\\O Value\\O :\\O Poor\\O", "opinion_tags": "Overall\\O :\\O Poor\\O ,\\O Features\\O :\\O Average\\O ,\\O Performance\\O :\\O Poor\\B ,\\O Battery\\O Life\\O :\\O Excellent\\O ,\\O Price\\O --\\O Value\\O :\\O Poor\\O"}, {"uid": "201-2", "sentiment": "positive", "target_tags": "Overall\\O :\\O Poor\\O ,\\O Features\\O :\\O Average\\O ,\\O Performance\\O :\\O Poor\\O ,\\O Battery\\B Life\\I :\\O Excellent\\O ,\\O Price\\O --\\O Value\\O :\\O Poor\\O", "opinion_tags": "Overall\\O :\\O Poor\\O ,\\O Features\\O :\\O Average\\O ,\\O Performance\\O :\\O Poor\\O ,\\O Battery\\O Life\\O :\\O Excellent\\B ,\\O Price\\O --\\O Value\\O :\\O Poor\\O"}, {"uid": "201-3", "sentiment": "negative", "target_tags": "Overall\\O :\\O Poor\\O ,\\O Features\\O :\\O Average\\O ,\\O Performance\\O :\\O Poor\\O ,\\O Battery\\O Life\\O :\\O Excellent\\O ,\\O Price\\B --\\O Value\\O :\\O Poor\\O", "opinion_tags": "Overall\\O :\\O Poor\\O ,\\O Features\\O :\\O Average\\O ,\\O Performance\\O :\\O Poor\\O ,\\O Battery\\O Life\\O :\\O Excellent\\O ,\\O Price\\O --\\O Value\\O :\\O Poor\\B"}, {"uid": "201-4", "sentiment": "negative", "target_tags": "Overall\\O :\\O Poor\\O ,\\O Features\\O :\\O Average\\O ,\\O Performance\\O :\\O Poor\\O ,\\O Battery\\O Life\\O :\\O Excellent\\O ,\\O Price\\O --\\O Value\\B :\\O Poor\\O", "opinion_tags": "Overall\\O :\\O Poor\\O ,\\O Features\\O :\\O Average\\O ,\\O Performance\\O :\\O Poor\\O ,\\O Battery\\O Life\\O :\\O Excellent\\O ,\\O Price\\O --\\O Value\\O :\\O Poor\\B"}], "postag": ["RB", ":", "JJ", ",", "NNS", ":", "NN", ",", "NN", ":", "JJ", ",", "NN", "NN", ":", "JJ", ",", "NN", ",", "NN", ":", "JJ"], "head": [5, 5, 5, 5, 0, 5, 5, 9, 5, 5, 14, 14, 14, 5, 5, 18, 18, 5, 20, 5, 20, 20], "deprel": ["advmod", "punct", "amod", "punct", "root", "punct", "appos", "punct", "list", "punct", "amod", "punct", "compound", "list", "punct", "amod", "punct", "list", "punct", "parataxis", "punct", "appos"]}, {"id": "202", "sentence": "The graphics are stunning .", "triples": [{"uid": "202-0", "sentiment": "positive", "target_tags": "The\\O graphics\\B are\\O stunning\\O .\\O", "opinion_tags": "The\\O graphics\\O are\\O stunning\\B .\\O"}], "postag": ["DT", "NNS", "VBP", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"]}, {"id": "203", "sentence": "( I found a 2GB stick for a bit under $ 50 ) Nice and portable and definitely a decent enough system to keep you entertained while sitting in the airplane for a couple of hours , or at the hotel taking care of some last minute details and documents .", "triples": [{"uid": "203-0", "sentiment": "positive", "target_tags": "(\\O I\\O found\\O a\\O 2GB\\O stick\\O for\\O a\\O bit\\O under\\O $\\O 50\\O )\\O Nice\\O and\\O portable\\O and\\O definitely\\O a\\O decent\\O enough\\O system\\B to\\O keep\\O you\\O entertained\\O while\\O sitting\\O in\\O the\\O airplane\\O for\\O a\\O couple\\O of\\O hours\\O ,\\O or\\O at\\O the\\O hotel\\O taking\\O care\\O of\\O some\\O last\\O minute\\O details\\O and\\O documents\\O .\\O", "opinion_tags": "(\\O I\\O found\\O a\\O 2GB\\O stick\\O for\\O a\\O bit\\O under\\O $\\O 50\\O )\\O Nice\\B and\\O portable\\O and\\O definitely\\O a\\O decent\\O enough\\O system\\O to\\O keep\\O you\\O entertained\\O while\\O sitting\\O in\\O the\\O airplane\\O for\\O a\\O couple\\O of\\O hours\\O ,\\O or\\O at\\O the\\O hotel\\O taking\\O care\\O of\\O some\\O last\\O minute\\O details\\O and\\O documents\\O .\\O"}, {"uid": "203-1", "sentiment": "positive", "target_tags": "(\\O I\\O found\\O a\\O 2GB\\O stick\\O for\\O a\\O bit\\O under\\O $\\O 50\\O )\\O Nice\\O and\\O portable\\O and\\O definitely\\O a\\O decent\\O enough\\O system\\B to\\O keep\\O you\\O entertained\\O while\\O sitting\\O in\\O the\\O airplane\\O for\\O a\\O couple\\O of\\O hours\\O ,\\O or\\O at\\O the\\O hotel\\O taking\\O care\\O of\\O some\\O last\\O minute\\O details\\O and\\O documents\\O .\\O", "opinion_tags": "(\\O I\\O found\\O a\\O 2GB\\O stick\\O for\\O a\\O bit\\O under\\O $\\O 50\\O )\\O Nice\\O and\\O portable\\B and\\O definitely\\O a\\O decent\\O enough\\O system\\O to\\O keep\\O you\\O entertained\\O while\\O sitting\\O in\\O the\\O airplane\\O for\\O a\\O couple\\O of\\O hours\\O ,\\O or\\O at\\O the\\O hotel\\O taking\\O care\\O of\\O some\\O last\\O minute\\O details\\O and\\O documents\\O .\\O"}, {"uid": "203-2", "sentiment": "positive", "target_tags": "(\\O I\\O found\\O a\\O 2GB\\O stick\\O for\\O a\\O bit\\O under\\O $\\O 50\\O )\\O Nice\\O and\\O portable\\O and\\O definitely\\O a\\O decent\\O enough\\O system\\B to\\O keep\\O you\\O entertained\\O while\\O sitting\\O in\\O the\\O airplane\\O for\\O a\\O couple\\O of\\O hours\\O ,\\O or\\O at\\O the\\O hotel\\O taking\\O care\\O of\\O some\\O last\\O minute\\O details\\O and\\O documents\\O .\\O", "opinion_tags": "(\\O I\\O found\\O a\\O 2GB\\O stick\\O for\\O a\\O bit\\O under\\O $\\O 50\\O )\\O Nice\\O and\\O portable\\O and\\O definitely\\O a\\O decent\\B enough\\O system\\O to\\O keep\\O you\\O entertained\\O while\\O sitting\\O in\\O the\\O airplane\\O for\\O a\\O couple\\O of\\O hours\\O ,\\O or\\O at\\O the\\O hotel\\O taking\\O care\\O of\\O some\\O last\\O minute\\O details\\O and\\O documents\\O .\\O"}], "postag": ["-LRB-", "PRP", "VBD", "DT", "NN", "NN", "IN", "DT", "NN", "IN", "$", "CD", "-RRB-", "JJ", "CC", "JJ", "CC", "RB", "DT", "JJ", "JJ", "NN", "TO", "VB", "PRP", "JJ", "IN", "VBG", "IN", "DT", "NN", "IN", "DT", "NN", "IN", "NNS", ",", "CC", "IN", "DT", "NN", "VBG", "NN", "IN", "DT", "JJ", "NN", "NNS", "CC", "NNS", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 3, 11, 9, 11, 11, 3, 16, 14, 22, 22, 22, 22, 22, 14, 24, 22, 24, 24, 28, 24, 31, 31, 28, 34, 34, 28, 36, 34, 42, 42, 41, 41, 42, 28, 42, 48, 48, 47, 48, 42, 50, 48, 3], "deprel": ["punct", "nsubj", "root", "det", "compound", "obj", "case", "det", "obl", "case", "nmod", "nummod", "punct", "xcomp", "cc", "conj", "cc", "advmod", "det", "amod", "amod", "conj", "mark", "acl", "obj", "xcomp", "mark", "advcl", "case", "det", "obl", "case", "det", "obl", "case", "nmod", "punct", "cc", "case", "det", "nsubj", "conj", "obj", "case", "det", "amod", "compound", "obl", "cc", "conj", "punct"]}, {"id": "204", "sentence": "after much effort and 10 days ASUS replaced itThe WiFi is very weak .", "triples": [{"uid": "204-0", "sentiment": "negative", "target_tags": "after\\O much\\O effort\\O and\\O 10\\O days\\O ASUS\\O replaced\\O itThe\\O WiFi\\B is\\O very\\O weak\\O .\\O", "opinion_tags": "after\\O much\\O effort\\O and\\O 10\\O days\\O ASUS\\O replaced\\O itThe\\O WiFi\\O is\\O very\\O weak\\B .\\O"}], "postag": ["IN", "JJ", "NN", "CC", "CD", "NNS", "NNP", "VBD", "NNP", "NNP", "VBZ", "RB", "JJ", "."], "head": [8, 3, 8, 6, 6, 3, 8, 13, 10, 13, 13, 13, 0, 13], "deprel": ["mark", "amod", "nsubj", "cc", "nummod", "conj", "nsubj", "advcl", "compound", "nsubj", "cop", "advmod", "root", "punct"]}, {"id": "205", "sentence": ", Applications respond immediately ( not like the tired MS applications ) .", "triples": [{"uid": "205-0", "sentiment": "positive", "target_tags": ",\\O Applications\\B respond\\O immediately\\O (\\O not\\O like\\O the\\O tired\\O MS\\O applications\\O )\\O .\\O", "opinion_tags": ",\\O Applications\\O respond\\B immediately\\I (\\O not\\O like\\O the\\O tired\\O MS\\O applications\\O )\\O .\\O"}], "postag": [",", "NNS", "VBP", "RB", "-LRB-", "RB", "IN", "DT", "JJ", "NN", "NNS", "-RRB-", "."], "head": [3, 3, 0, 3, 11, 11, 11, 11, 11, 11, 3, 11, 3], "deprel": ["punct", "nsubj", "root", "advmod", "punct", "advmod", "case", "det", "amod", "compound", "obl", "punct", "punct"]}, {"id": "206", "sentence": "The only drawback for me is how dirty the screen gets , and rather quickly too .", "triples": [{"uid": "206-0", "sentiment": "negative", "target_tags": "The\\O only\\O drawback\\O for\\O me\\O is\\O how\\O dirty\\O the\\O screen\\B gets\\O ,\\O and\\O rather\\O quickly\\O too\\O .\\O", "opinion_tags": "The\\O only\\O drawback\\O for\\O me\\O is\\O how\\O dirty\\B the\\O screen\\O gets\\O ,\\O and\\O rather\\O quickly\\O too\\O .\\O"}, {"uid": "206-1", "sentiment": "negative", "target_tags": "The\\O only\\O drawback\\O for\\O me\\O is\\O how\\O dirty\\O the\\O screen\\B gets\\O ,\\O and\\O rather\\O quickly\\O too\\O .\\O", "opinion_tags": "The\\O only\\O drawback\\O for\\O me\\O is\\O how\\O dirty\\O the\\O screen\\O gets\\O ,\\O and\\O rather\\O quickly\\B too\\O .\\O"}], "postag": ["DT", "JJ", "NN", "IN", "PRP", "VBZ", "WRB", "JJ", "DT", "NN", "VBZ", ",", "CC", "RB", "RB", "RB", "."], "head": [3, 3, 8, 5, 3, 8, 8, 0, 10, 11, 8, 15, 15, 15, 8, 15, 8], "deprel": ["det", "amod", "nsubj", "case", "nmod", "cop", "mark", "root", "det", "nsubj", "acl:relcl", "punct", "cc", "advmod", "conj", "advmod", "punct"]}, {"id": "207", "sentence": "Seems to slow down occassionally but can run many applications ( ie Internet tabs , programs , etc ) simultaneously .", "triples": [{"uid": "207-0", "sentiment": "positive", "target_tags": "Seems\\O to\\O slow\\O down\\O occassionally\\O but\\O can\\O run\\O many\\O applications\\B (\\O ie\\O Internet\\O tabs\\O ,\\O programs\\O ,\\O etc\\O )\\O simultaneously\\O .\\O", "opinion_tags": "Seems\\O to\\O slow\\O down\\O occassionally\\O but\\O can\\O run\\O many\\B applications\\O (\\O ie\\O Internet\\O tabs\\O ,\\O programs\\O ,\\O etc\\O )\\O simultaneously\\O .\\O"}], "postag": ["VBZ", "TO", "VB", "RP", "RB", "CC", "MD", "VB", "JJ", "NNS", "-LRB-", "NN", "NN", "NNS", ",", "NNS", ",", "FW", "-RRB-", "RB", "."], "head": [0, 3, 1, 3, 3, 8, 8, 1, 10, 8, 14, 14, 14, 10, 16, 14, 18, 14, 14, 14, 1], "deprel": ["root", "mark", "xcomp", "compound:prt", "advmod", "cc", "aux", "conj", "amod", "obj", "punct", "compound", "compound", "appos", "punct", "conj", "punct", "conj", "punct", "advmod", "punct"]}, {"id": "208", "sentence": "Not to mention sometimes the whole charger unit will decide not to work entirely .", "triples": [{"uid": "208-0", "sentiment": "negative", "target_tags": "Not\\O to\\O mention\\O sometimes\\O the\\O whole\\O charger\\B unit\\I will\\O decide\\O not\\O to\\O work\\O entirely\\O .\\O", "opinion_tags": "Not\\O to\\O mention\\O sometimes\\O the\\O whole\\O charger\\O unit\\O will\\O decide\\O not\\B to\\I work\\I entirely\\I .\\O"}], "postag": ["RB", "TO", "VB", "RB", "DT", "JJ", "NN", "NN", "MD", "VB", "RB", "TO", "VB", "RB", "."], "head": [10, 1, 10, 10, 8, 8, 8, 10, 10, 0, 13, 13, 10, 13, 10], "deprel": ["cc", "fixed", "advcl", "advmod", "det", "amod", "compound", "nsubj", "aux", "root", "advmod", "mark", "xcomp", "advmod", "punct"]}, {"id": "209", "sentence": "I have other computers that get strong signals that do n't drop in places that this `` net '' book loses its signal .", "triples": [{"uid": "209-0", "sentiment": "positive", "target_tags": "I\\O have\\O other\\O computers\\O that\\O get\\O strong\\O signals\\B that\\O do\\O n't\\O drop\\O in\\O places\\O that\\O this\\O ``\\O net\\O ''\\O book\\O loses\\O its\\O signal\\O .\\O", "opinion_tags": "I\\O have\\O other\\O computers\\O that\\O get\\O strong\\B signals\\O that\\O do\\O n't\\O drop\\O in\\O places\\O that\\O this\\O ``\\O net\\O ''\\O book\\O loses\\O its\\O signal\\O .\\O"}, {"uid": "209-1", "sentiment": "negative", "target_tags": "I\\O have\\O other\\O computers\\O that\\O get\\O strong\\O signals\\O that\\O do\\O n't\\O drop\\O in\\O places\\O that\\O this\\O ``\\O net\\O ''\\O book\\O loses\\O its\\O signal\\B .\\O", "opinion_tags": "I\\O have\\O other\\O computers\\O that\\O get\\O strong\\O signals\\O that\\O do\\O n't\\O drop\\O in\\O places\\O that\\O this\\O ``\\O net\\O ''\\O book\\O loses\\B its\\O signal\\O .\\O"}], "postag": ["PRP", "VBP", "JJ", "NNS", "WDT", "VBP", "JJ", "NNS", "WDT", "VBP", "RB", "VB", "IN", "NNS", "IN", "DT", "``", "NN", "''", "NN", "VBZ", "PRP$", "NN", "."], "head": [2, 0, 4, 2, 6, 4, 8, 6, 12, 12, 12, 8, 14, 12, 21, 20, 18, 20, 18, 21, 12, 23, 21, 2], "deprel": ["nsubj", "root", "amod", "obj", "nsubj", "acl:relcl", "amod", "obj", "nsubj", "aux", "advmod", "acl:relcl", "case", "obl", "mark", "det", "punct", "compound", "punct", "nsubj", "ccomp", "nmod:poss", "obj", "punct"]}, {"id": "210", "sentence": "Anyway , in early July of this year , the DVD burner stopped working , and the computer stared having issues with power supply .", "triples": [{"uid": "210-0", "sentiment": "negative", "target_tags": "Anyway\\O ,\\O in\\O early\\O July\\O of\\O this\\O year\\O ,\\O the\\O DVD\\B burner\\I stopped\\O working\\O ,\\O and\\O the\\O computer\\O stared\\O having\\O issues\\O with\\O power\\O supply\\O .\\O", "opinion_tags": "Anyway\\O ,\\O in\\O early\\O July\\O of\\O this\\O year\\O ,\\O the\\O DVD\\O burner\\O stopped\\B working\\I ,\\O and\\O the\\O computer\\O stared\\O having\\O issues\\O with\\O power\\O supply\\O .\\O"}, {"uid": "210-1", "sentiment": "negative", "target_tags": "Anyway\\O ,\\O in\\O early\\O July\\O of\\O this\\O year\\O ,\\O the\\O DVD\\O burner\\O stopped\\O working\\O ,\\O and\\O the\\O computer\\O stared\\O having\\O issues\\O with\\O power\\B supply\\I .\\O", "opinion_tags": "Anyway\\O ,\\O in\\O early\\O July\\O of\\O this\\O year\\O ,\\O the\\O DVD\\O burner\\O stopped\\O working\\O ,\\O and\\O the\\O computer\\O stared\\O having\\O issues\\B with\\O power\\O supply\\O .\\O"}], "postag": ["RB", ",", "IN", "JJ", "NNP", "IN", "DT", "NN", ",", "DT", "NNP", "NN", "VBD", "VBG", ",", "CC", "DT", "NN", "VBD", "VBG", "NNS", "IN", "NN", "NN", "."], "head": [13, 13, 5, 5, 13, 8, 8, 5, 13, 12, 12, 13, 0, 13, 19, 19, 18, 19, 13, 19, 20, 24, 24, 21, 13], "deprel": ["advmod", "punct", "case", "amod", "obl", "case", "det", "nmod", "punct", "det", "compound", "nsubj", "root", "xcomp", "punct", "cc", "det", "nsubj", "conj", "xcomp", "obj", "case", "compound", "nmod", "punct"]}, {"id": "211", "sentence": "The screen gets smeary and dusty very quickly and it 's very noticeable .", "triples": [{"uid": "211-0", "sentiment": "negative", "target_tags": "The\\O screen\\B gets\\O smeary\\O and\\O dusty\\O very\\O quickly\\O and\\O it\\O 's\\O very\\O noticeable\\O .\\O", "opinion_tags": "The\\O screen\\O gets\\O smeary\\B and\\O dusty\\O very\\O quickly\\O and\\O it\\O 's\\O very\\O noticeable\\O .\\O"}, {"uid": "211-1", "sentiment": "negative", "target_tags": "The\\O screen\\B gets\\O smeary\\O and\\O dusty\\O very\\O quickly\\O and\\O it\\O 's\\O very\\O noticeable\\O .\\O", "opinion_tags": "The\\O screen\\O gets\\O smeary\\O and\\O dusty\\B very\\O quickly\\O and\\O it\\O 's\\O very\\O noticeable\\O .\\O"}, {"uid": "211-2", "sentiment": "negative", "target_tags": "The\\O screen\\B gets\\O smeary\\O and\\O dusty\\O very\\O quickly\\O and\\O it\\O 's\\O very\\O noticeable\\O .\\O", "opinion_tags": "The\\O screen\\O gets\\O smeary\\O and\\O dusty\\O very\\O quickly\\B and\\O it\\O 's\\O very\\O noticeable\\O .\\O"}, {"uid": "211-3", "sentiment": "negative", "target_tags": "The\\O screen\\B gets\\O smeary\\O and\\O dusty\\O very\\O quickly\\O and\\O it\\O 's\\O very\\O noticeable\\O .\\O", "opinion_tags": "The\\O screen\\O gets\\O smeary\\O and\\O dusty\\O very\\O quickly\\O and\\O it\\O 's\\O very\\O noticeable\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "JJ", "RB", "RB", "CC", "PRP", "VBZ", "RB", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 8, 4, 13, 13, 13, 13, 4, 4], "deprel": ["det", "nsubj", "aux", "root", "cc", "conj", "advmod", "advmod", "cc", "nsubj", "cop", "advmod", "conj", "punct"]}, {"id": "212", "sentence": "The battery life , before the battery completely died of course , left much to be desired .", "triples": [{"uid": "212-0", "sentiment": "negative", "target_tags": "The\\O battery\\B life\\I ,\\O before\\O the\\O battery\\O completely\\O died\\O of\\O course\\O ,\\O left\\O much\\O to\\O be\\O desired\\O .\\O", "opinion_tags": "The\\O battery\\O life\\O ,\\O before\\O the\\O battery\\O completely\\O died\\O of\\O course\\O ,\\O left\\B much\\I to\\I be\\I desired\\I .\\O"}], "postag": ["DT", "NN", "NN", ",", "IN", "DT", "NN", "RB", "VBD", "RB", "RB", ",", "VBD", "JJ", "TO", "VB", "VBN", "."], "head": [3, 3, 13, 3, 9, 7, 9, 9, 13, 9, 10, 13, 0, 13, 17, 17, 14, 13], "deprel": ["det", "compound", "nsubj", "punct", "mark", "det", "nsubj", "advmod", "advcl", "advmod", "fixed", "punct", "root", "obj", "mark", "aux:pass", "acl", "punct"]}, {"id": "213", "sentence": "It had most of the features and all of the power that I wanted to replace my desktop machine .", "triples": [{"uid": "213-0", "sentiment": "positive", "target_tags": "It\\O had\\O most\\O of\\O the\\O features\\B and\\O all\\O of\\O the\\O power\\O that\\O I\\O wanted\\O to\\O replace\\O my\\O desktop\\O machine\\O .\\O", "opinion_tags": "It\\O had\\O most\\O of\\O the\\O features\\O and\\O all\\O of\\O the\\O power\\O that\\O I\\O wanted\\B to\\O replace\\O my\\O desktop\\O machine\\O .\\O"}, {"uid": "213-1", "sentiment": "positive", "target_tags": "It\\O had\\O most\\O of\\O the\\O features\\O and\\O all\\O of\\O the\\O power\\B that\\O I\\O wanted\\O to\\O replace\\O my\\O desktop\\O machine\\O .\\O", "opinion_tags": "It\\O had\\O most\\O of\\O the\\O features\\O and\\O all\\O of\\O the\\O power\\O that\\O I\\O wanted\\B to\\O replace\\O my\\O desktop\\O machine\\O .\\O"}], "postag": ["PRP", "VBD", "JJS", "IN", "DT", "NNS", "CC", "DT", "IN", "DT", "NN", "WDT", "PRP", "VBD", "TO", "VB", "PRP$", "NN", "NN", "."], "head": [2, 0, 2, 6, 6, 3, 8, 6, 11, 11, 8, 14, 14, 11, 16, 14, 19, 19, 16, 2], "deprel": ["nsubj", "root", "obj", "case", "det", "nmod", "cc", "conj", "case", "det", "nmod", "obj", "nsubj", "acl:relcl", "mark", "xcomp", "nmod:poss", "compound", "obj", "punct"]}, {"id": "214", "sentence": "It seemed to be a very nice laptop except I was not able to load my Garmin GPS software or Microsoft Office 2003 .", "triples": [{"uid": "214-0", "sentiment": "negative", "target_tags": "It\\O seemed\\O to\\O be\\O a\\O very\\O nice\\O laptop\\O except\\O I\\O was\\O not\\O able\\O to\\O load\\O my\\O Garmin\\B GPS\\I software\\I or\\O Microsoft\\O Office\\O 2003\\O .\\O", "opinion_tags": "It\\O seemed\\O to\\O be\\O a\\O very\\O nice\\O laptop\\O except\\O I\\O was\\O not\\B able\\I to\\I load\\I my\\O Garmin\\O GPS\\O software\\O or\\O Microsoft\\O Office\\O 2003\\O .\\O"}, {"uid": "214-1", "sentiment": "negative", "target_tags": "It\\O seemed\\O to\\O be\\O a\\O very\\O nice\\O laptop\\O except\\O I\\O was\\O not\\O able\\O to\\O load\\O my\\O Garmin\\O GPS\\O software\\O or\\O Microsoft\\B Office\\I 2003\\I .\\O", "opinion_tags": "It\\O seemed\\O to\\O be\\O a\\O very\\O nice\\O laptop\\O except\\O I\\O was\\O not\\B able\\I to\\I load\\I my\\O Garmin\\O GPS\\O software\\O or\\O Microsoft\\O Office\\O 2003\\O .\\O"}], "postag": ["PRP", "VBD", "TO", "VB", "DT", "RB", "JJ", "NN", "IN", "PRP", "VBD", "RB", "JJ", "TO", "VB", "PRP$", "NNP", "NNP", "NN", "CC", "NNP", "NNP", "CD", "."], "head": [2, 0, 8, 8, 8, 7, 8, 2, 13, 13, 13, 13, 2, 15, 13, 19, 18, 19, 15, 22, 22, 19, 22, 2], "deprel": ["nsubj", "root", "mark", "cop", "det", "advmod", "amod", "xcomp", "mark", "nsubj", "cop", "advmod", "advcl", "mark", "xcomp", "nmod:poss", "compound", "compound", "obj", "cc", "compound", "conj", "nummod", "punct"]}, {"id": "215", "sentence": "Was very much worth the price i paid .", "triples": [{"uid": "215-0", "sentiment": "positive", "target_tags": "Was\\O very\\O much\\O worth\\O the\\O price\\B i\\O paid\\O .\\O", "opinion_tags": "Was\\O very\\O much\\O worth\\B the\\O price\\O i\\O paid\\O .\\O"}], "postag": ["VBD", "RB", "RB", "JJ", "DT", "NN", "PRP", "VBD", "."], "head": [4, 3, 4, 0, 6, 4, 8, 6, 4], "deprel": ["cop", "advmod", "advmod", "root", "det", "obj", "nsubj", "acl:relcl", "punct"]}, {"id": "216", "sentence": "This is a great little computer for the price .", "triples": [{"uid": "216-0", "sentiment": "positive", "target_tags": "This\\O is\\O a\\O great\\O little\\O computer\\O for\\O the\\O price\\B .\\O", "opinion_tags": "This\\O is\\O a\\O great\\B little\\O computer\\O for\\O the\\O price\\O .\\O"}], "postag": ["DT", "VBZ", "DT", "JJ", "JJ", "NN", "IN", "DT", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 6], "deprel": ["nsubj", "cop", "det", "amod", "amod", "root", "case", "det", "nmod", "punct"]}, {"id": "217", "sentence": "All apple associates are always willing to help you out with anything , no matter when you purchased the computer and how many years passed .", "triples": [{"uid": "217-0", "sentiment": "positive", "target_tags": "All\\O apple\\B associates\\I are\\O always\\O willing\\O to\\O help\\O you\\O out\\O with\\O anything\\O ,\\O no\\O matter\\O when\\O you\\O purchased\\O the\\O computer\\O and\\O how\\O many\\O years\\O passed\\O .\\O", "opinion_tags": "All\\O apple\\O associates\\O are\\O always\\O willing\\B to\\O help\\O you\\O out\\O with\\O anything\\O ,\\O no\\O matter\\O when\\O you\\O purchased\\O the\\O computer\\O and\\O how\\O many\\O years\\O passed\\O .\\O"}], "postag": ["DT", "NN", "NNS", "VBP", "RB", "JJ", "TO", "VB", "PRP", "RP", "IN", "NN", ",", "DT", "NN", "WRB", "PRP", "VBD", "DT", "NN", "CC", "WRB", "JJ", "NNS", "VBD", "."], "head": [3, 3, 6, 6, 6, 0, 8, 6, 8, 8, 12, 8, 15, 15, 6, 18, 18, 15, 20, 18, 25, 23, 24, 25, 18, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "mark", "xcomp", "obj", "compound:prt", "case", "obl", "punct", "det", "parataxis", "mark", "nsubj", "advcl", "det", "obj", "cc", "mark", "amod", "nsubj", "conj", "punct"]}, {"id": "218", "sentence": "I would like to use a different operating system altogether .", "triples": [{"uid": "218-0", "sentiment": "neutral", "target_tags": "I\\O would\\O like\\O to\\O use\\O a\\O different\\O operating\\B system\\I altogether\\O .\\O", "opinion_tags": "I\\O would\\O like\\B to\\O use\\O a\\O different\\O operating\\O system\\O altogether\\O .\\O"}, {"uid": "218-1", "sentiment": "neutral", "target_tags": "I\\O would\\O like\\O to\\O use\\O a\\O different\\O operating\\B system\\I altogether\\O .\\O", "opinion_tags": "I\\O would\\O like\\O to\\O use\\O a\\O different\\B operating\\O system\\O altogether\\O .\\O"}], "postag": ["PRP", "MD", "VB", "TO", "VB", "DT", "JJ", "NN", "NN", "RB", "."], "head": [3, 3, 0, 5, 3, 9, 9, 9, 5, 5, 3], "deprel": ["nsubj", "aux", "root", "mark", "xcomp", "det", "amod", "compound", "obj", "advmod", "punct"]}]