[{"id": "1895", "sentence": "Overall I feel this netbook was poor quality , had poor performance , although it did have great battery life when it did work .", "postag": ["RB", "PRP", "VBP", "DT", "NN", "VBD", "JJ", "NN", ",", "VBD", "JJ", "NN", ",", "IN", "PRP", "VBD", "VB", "JJ", "NN", "NN", "WRB", "PRP", "VBD", "VB", "."], "head": [3, 3, 0, 5, 8, 8, 8, 3, 10, 3, 12, 10, 17, 17, 17, 17, 3, 20, 20, 17, 24, 24, 24, 17, 3], "deprel": ["advmod", "nsubj", "root", "det", "nsubj", "cop", "amod", "ccomp", "punct", "conj", "amod", "obj", "punct", "mark", "nsubj", "aux", "advcl", "amod", "compound", "obj", "mark", "nsubj", "aux", "advcl", "punct"], "triples": [{"uid": "1895-0", "target_tags": "Overall\\O I\\O feel\\O this\\O netbook\\O was\\O poor\\O quality\\B ,\\O had\\O poor\\O performance\\O ,\\O although\\O it\\O did\\O have\\O great\\O battery\\O life\\O when\\O it\\O did\\O work\\O .\\O", "opinion_tags": "Overall\\O I\\O feel\\O this\\O netbook\\O was\\O poor\\B quality\\O ,\\O had\\O poor\\O performance\\O ,\\O although\\O it\\O did\\O have\\O great\\O battery\\O life\\O when\\O it\\O did\\O work\\O .\\O", "sentiment": "negative"}, {"uid": "1895-1", "target_tags": "Overall\\O I\\O feel\\O this\\O netbook\\O was\\O poor\\O quality\\O ,\\O had\\O poor\\O performance\\B ,\\O although\\O it\\O did\\O have\\O great\\O battery\\O life\\O when\\O it\\O did\\O work\\O .\\O", "opinion_tags": "Overall\\O I\\O feel\\O this\\O netbook\\O was\\O poor\\O quality\\O ,\\O had\\O poor\\B performance\\O ,\\O although\\O it\\O did\\O have\\O great\\O battery\\O life\\O when\\O it\\O did\\O work\\O .\\O", "sentiment": "negative"}, {"uid": "1895-2", "target_tags": "Overall\\O I\\O feel\\O this\\O netbook\\O was\\O poor\\O quality\\O ,\\O had\\O poor\\O performance\\O ,\\O although\\O it\\O did\\O have\\O great\\O battery\\B life\\I when\\O it\\O did\\O work\\O .\\O", "opinion_tags": "Overall\\O I\\O feel\\O this\\O netbook\\O was\\O poor\\O quality\\O ,\\O had\\O poor\\O performance\\O ,\\O although\\O it\\O did\\O have\\O great\\B battery\\O life\\O when\\O it\\O did\\O work\\O .\\O", "sentiment": "positive"}]}, {"id": "1638", "sentence": "The battery life is great .", "postag": ["DT", "NN", "NN", "VBZ", "JJ", "."], "head": [3, 3, 5, 5, 0, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "1638-0", "target_tags": "The\\O battery\\B life\\I is\\O great\\O .\\O", "opinion_tags": "The\\O battery\\O life\\O is\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "2174", "sentence": "Not to mention , the battery life is absolutely amazing .", "postag": ["RB", "TO", "VB", ",", "DT", "NN", "NN", "VBZ", "RB", "JJ", "."], "head": [3, 3, 10, 10, 7, 7, 10, 10, 10, 0, 10], "deprel": ["advmod", "mark", "advcl", "punct", "det", "compound", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "2174-0", "target_tags": "Not\\O to\\O mention\\O ,\\O the\\O battery\\B life\\I is\\O absolutely\\O amazing\\O .\\O", "opinion_tags": "Not\\O to\\O mention\\O ,\\O the\\O battery\\O life\\O is\\O absolutely\\O amazing\\B .\\O", "sentiment": "positive"}]}, {"id": "695", "sentence": "It is so much easier to use", "postag": ["PRP", "VBZ", "RB", "RB", "JJR", "TO", "VB"], "head": [5, 5, 4, 5, 0, 7, 5], "deprel": ["nsubj", "cop", "advmod", "advmod", "root", "mark", "xcomp"], "triples": [{"uid": "695-0", "target_tags": "It\\O is\\O so\\O much\\O easier\\O to\\O use\\B", "opinion_tags": "It\\O is\\O so\\O much\\O easier\\B to\\O use\\O", "sentiment": "positive"}]}, {"id": "2984", "sentence": "My husband uses it mostly for games , email and music .", "postag": ["PRP$", "NN", "VBZ", "PRP", "RB", "IN", "NNS", ",", "NN", "CC", "NN", "."], "head": [2, 3, 0, 3, 3, 7, 3, 9, 7, 11, 7, 3], "deprel": ["nmod:poss", "nsubj", "root", "obj", "advmod", "case", "obl", "punct", "conj", "cc", "conj", "punct"], "triples": [{"uid": "2984-0", "target_tags": "My\\O husband\\O uses\\O it\\O mostly\\O for\\O games\\B ,\\O email\\O and\\O music\\O .\\O", "opinion_tags": "My\\O husband\\O uses\\O it\\O mostly\\B for\\O games\\O ,\\O email\\O and\\O music\\O .\\O", "sentiment": "neutral"}]}, {"id": "2833", "sentence": "the laptop preformed pretty well .", "postag": ["DT", "NN", "VBD", "RB", "RB", "."], "head": [2, 3, 0, 5, 3, 3], "deprel": ["det", "nsubj", "root", "advmod", "advmod", "punct"], "triples": [{"uid": "2833-0", "target_tags": "the\\O laptop\\O preformed\\B pretty\\O well\\O .\\O", "opinion_tags": "the\\O laptop\\O preformed\\O pretty\\O well\\B .\\O", "sentiment": "positive"}]}, {"id": "609", "sentence": "keys are all in weird places and is way too large for the way it is designed .", "postag": ["NNS", "VBP", "RB", "IN", "JJ", "NNS", "CC", "VBZ", "RB", "RB", "JJ", "IN", "DT", "NN", "PRP", "VBZ", "VBN", "."], "head": [6, 6, 6, 6, 6, 0, 11, 11, 11, 11, 6, 14, 14, 11, 17, 17, 14, 6], "deprel": ["nsubj", "cop", "advmod", "case", "amod", "root", "cc", "cop", "advmod", "advmod", "conj", "case", "det", "obl", "nsubj:pass", "aux:pass", "acl:relcl", "punct"], "triples": [{"uid": "609-0", "target_tags": "keys\\B are\\O all\\O in\\O weird\\O places\\O and\\O is\\O way\\O too\\O large\\O for\\O the\\O way\\O it\\O is\\O designed\\O .\\O", "opinion_tags": "keys\\O are\\O all\\O in\\O weird\\B places\\O and\\O is\\O way\\O too\\O large\\B for\\O the\\O way\\O it\\O is\\O designed\\O .\\O", "sentiment": "negative"}]}, {"id": "1208", "sentence": "in 5 months the connect quality got worse and worse .", "postag": ["IN", "CD", "NNS", "DT", "NN", "NN", "VBD", "JJR", "CC", "JJR", "."], "head": [3, 3, 7, 6, 6, 7, 0, 7, 10, 8, 7], "deprel": ["case", "nummod", "obl", "det", "compound", "nsubj", "root", "xcomp", "cc", "conj", "punct"], "triples": [{"uid": "1208-0", "target_tags": "in\\O 5\\O months\\O the\\O connect\\B quality\\I got\\O worse\\O and\\O worse\\O .\\O", "opinion_tags": "in\\O 5\\O months\\O the\\O connect\\O quality\\O got\\O worse\\B and\\O worse\\O .\\O", "sentiment": "negative"}]}, {"id": "1985", "sentence": "From the get-go , the M6809 was unsteady in its operation ;", "postag": ["IN", "DT", "NN", ",", "DT", "NNP", "VBD", "JJ", "IN", "PRP$", "NN", ":"], "head": [3, 3, 8, 8, 6, 8, 8, 0, 11, 11, 8, 8], "deprel": ["case", "det", "obl", "punct", "det", "nsubj", "cop", "root", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "1985-0", "target_tags": "From\\O the\\O get-go\\O ,\\O the\\O M6809\\O was\\O unsteady\\O in\\O its\\O operation\\B ;\\O", "opinion_tags": "From\\O the\\O get-go\\O ,\\O the\\O M6809\\O was\\O unsteady\\B in\\O its\\O operation\\O ;\\O", "sentiment": "negative"}]}, {"id": "2994", "sentence": "The only downfall is the volume control .", "postag": ["DT", "JJ", "NN", "VBZ", "DT", "NN", "NN", "."], "head": [3, 3, 7, 7, 7, 7, 0, 7], "deprel": ["det", "amod", "nsubj", "cop", "det", "compound", "root", "punct"], "triples": [{"uid": "2994-0", "target_tags": "The\\O only\\O downfall\\O is\\O the\\O volume\\B control\\I .\\O", "opinion_tags": "The\\O only\\O downfall\\B is\\O the\\O volume\\O control\\O .\\O", "sentiment": "negative"}]}, {"id": "2783", "sentence": "Was not happy with one of the programs on it .", "postag": ["VBD", "RB", "JJ", "IN", "CD", "IN", "DT", "NNS", "IN", "PRP", "."], "head": [3, 3, 0, 5, 3, 8, 8, 5, 10, 8, 3], "deprel": ["cop", "advmod", "root", "case", "obl", "case", "det", "nmod", "case", "nmod", "punct"], "triples": [{"uid": "2783-0", "target_tags": "Was\\O not\\O happy\\O with\\O one\\B of\\I the\\I programs\\I on\\O it\\O .\\O", "opinion_tags": "Was\\O not\\B happy\\I with\\O one\\O of\\O the\\O programs\\O on\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "2239", "sentence": "After doing extensive research , macconnection had the lowest price on the 15 '' MBP i5 .", "postag": ["IN", "VBG", "JJ", "NN", ",", "NN", "VBD", "DT", "JJS", "NN", "IN", "DT", "CD", "''", "NN", "NN", "."], "head": [2, 7, 4, 2, 2, 7, 0, 10, 10, 7, 16, 16, 16, 16, 16, 10, 7], "deprel": ["mark", "advcl", "amod", "obj", "punct", "nsubj", "root", "det", "amod", "obj", "case", "det", "nummod", "punct", "compound", "nmod", "punct"], "triples": [{"uid": "2239-0", "target_tags": "After\\O doing\\O extensive\\O research\\O ,\\O macconnection\\O had\\O the\\O lowest\\O price\\B on\\O the\\O 15\\O ''\\O MBP\\O i5\\O .\\O", "opinion_tags": "After\\O doing\\O extensive\\O research\\O ,\\O macconnection\\O had\\O the\\O lowest\\B price\\O on\\O the\\O 15\\O ''\\O MBP\\O i5\\O .\\O", "sentiment": "positive"}]}, {"id": "2408", "sentence": "Mac also has many apps and programs that are quite cheap or free .", "postag": ["NNP", "RB", "VBZ", "JJ", "NNS", "CC", "NNS", "WDT", "VBP", "RB", "JJ", "CC", "JJ", "."], "head": [3, 3, 0, 5, 3, 7, 5, 11, 11, 11, 5, 13, 11, 3], "deprel": ["nsubj", "advmod", "root", "amod", "obj", "cc", "conj", "nsubj", "cop", "advmod", "acl:relcl", "cc", "conj", "punct"], "triples": [{"uid": "2408-0", "target_tags": "Mac\\O also\\O has\\O many\\O apps\\B and\\O programs\\O that\\O are\\O quite\\O cheap\\O or\\O free\\O .\\O", "opinion_tags": "Mac\\O also\\O has\\O many\\O apps\\O and\\O programs\\O that\\O are\\O quite\\O cheap\\B or\\O free\\B .\\O", "sentiment": "positive"}, {"uid": "2408-1", "target_tags": "Mac\\O also\\O has\\O many\\O apps\\O and\\O programs\\B that\\O are\\O quite\\O cheap\\O or\\O free\\O .\\O", "opinion_tags": "Mac\\O also\\O has\\O many\\O apps\\O and\\O programs\\O that\\O are\\O quite\\O cheap\\B or\\O free\\B .\\O", "sentiment": "positive"}]}, {"id": "62", "sentence": "They should have included more memory on their computers if they knew Vista would run slowly .", "postag": ["PRP", "MD", "VB", "VBN", "JJR", "NN", "IN", "PRP$", "NNS", "IN", "PRP", "VBD", "NNP", "MD", "VB", "RB", "."], "head": [4, 4, 4, 0, 6, 4, 9, 9, 4, 12, 12, 4, 15, 15, 12, 15, 4], "deprel": ["nsubj", "aux", "aux", "root", "amod", "obj", "case", "nmod:poss", "obl", "mark", "nsubj", "advcl", "nsubj", "aux", "ccomp", "advmod", "punct"], "triples": [{"uid": "62-0", "target_tags": "They\\O should\\O have\\O included\\O more\\O memory\\B on\\O their\\O computers\\O if\\O they\\O knew\\O Vista\\O would\\O run\\O slowly\\O .\\O", "opinion_tags": "They\\O should\\O have\\O included\\O more\\B memory\\O on\\O their\\O computers\\O if\\O they\\O knew\\O Vista\\O would\\O run\\O slowly\\O .\\O", "sentiment": "negative"}, {"uid": "62-1", "target_tags": "They\\O should\\O have\\O included\\O more\\O memory\\O on\\O their\\O computers\\O if\\O they\\O knew\\O Vista\\B would\\O run\\O slowly\\O .\\O", "opinion_tags": "They\\O should\\O have\\O included\\O more\\O memory\\O on\\O their\\O computers\\O if\\O they\\O knew\\O Vista\\O would\\O run\\O slowly\\B .\\O", "sentiment": "negative"}]}, {"id": "2978", "sentence": "I 've have n't had any major problems with the laptop except that the plastic piece that covers the usb port wires have all come off .", "postag": ["PRP", "VBP", "VB", "RB", "VBN", "DT", "JJ", "NNS", "IN", "DT", "NN", "IN", "IN", "DT", "JJ", "NN", "WDT", "VBZ", "DT", "NN", "NN", "NNS", "VBP", "RB", "VBN", "RP", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 11, 11, 8, 25, 12, 16, 16, 25, 18, 16, 22, 22, 22, 18, 25, 25, 5, 25, 5], "deprel": ["nsubj", "aux", "aux", "advmod", "root", "det", "amod", "obj", "case", "det", "nmod", "mark", "fixed", "det", "amod", "nsubj", "nsubj", "acl:relcl", "det", "compound", "compound", "obj", "aux", "advmod", "ccomp", "compound:prt", "punct"], "triples": [{"uid": "2978-0", "target_tags": "I\\O 've\\O have\\O n't\\O had\\O any\\O major\\O problems\\O with\\O the\\O laptop\\O except\\O that\\O the\\O plastic\\B piece\\I that\\O covers\\O the\\O usb\\O port\\O wires\\O have\\O all\\O come\\O off\\O .\\O", "opinion_tags": "I\\O 've\\O have\\O n't\\O had\\O any\\O major\\O problems\\B with\\O the\\O laptop\\O except\\O that\\O the\\O plastic\\O piece\\O that\\O covers\\O the\\O usb\\O port\\O wires\\O have\\O all\\O come\\B off\\I .\\O", "sentiment": "negative"}, {"uid": "2978-1", "target_tags": "I\\O 've\\O have\\O n't\\O had\\O any\\O major\\O problems\\O with\\O the\\O laptop\\O except\\O that\\O the\\O plastic\\O piece\\O that\\O covers\\O the\\O usb\\B port\\I wires\\I have\\O all\\O come\\O off\\O .\\O", "opinion_tags": "I\\O 've\\O have\\O n't\\O had\\O any\\O major\\O problems\\B with\\O the\\O laptop\\O except\\O that\\O the\\O plastic\\O piece\\O that\\O covers\\O the\\O usb\\O port\\O wires\\O have\\O all\\O come\\O off\\O .\\O", "sentiment": "neutral"}]}, {"id": "2329", "sentence": "While it was highly rated , would I like it ? I tried the keyboard at the store , and it seemed ok .", "postag": ["IN", "PRP", "VBD", "RB", "VBN", ",", "MD", "PRP", "VB", "PRP", ".", "PRP", "VBD", "DT", "NN", "IN", "DT", "NN", ",", "CC", "PRP", "VBD", "JJ", "."], "head": [5, 5, 5, 5, 9, 9, 9, 9, 0, 9, 9, 13, 9, 15, 13, 18, 18, 13, 22, 22, 22, 13, 22, 9], "deprel": ["mark", "nsubj:pass", "aux:pass", "advmod", "advcl", "punct", "aux", "nsubj", "root", "obj", "punct", "nsubj", "parataxis", "det", "obj", "case", "det", "obl", "punct", "cc", "nsubj", "conj", "xcomp", "punct"], "triples": [{"uid": "2329-0", "target_tags": "While\\O it\\O was\\O highly\\O rated\\O ,\\O would\\O I\\O like\\O it\\O ?\\O I\\O tried\\O the\\O keyboard\\B at\\O the\\O store\\O ,\\O and\\O it\\O seemed\\O ok\\O .\\O", "opinion_tags": "While\\O it\\O was\\O highly\\O rated\\O ,\\O would\\O I\\O like\\O it\\O ?\\O I\\O tried\\O the\\O keyboard\\O at\\O the\\O store\\O ,\\O and\\O it\\O seemed\\O ok\\B .\\O", "sentiment": "neutral"}]}, {"id": "1812", "sentence": "The difference is it 's a whole lot of fun using the laptop now , still learning the Apple navigation , but is fun and comes with a lot of cool apps .", "postag": ["DT", "NN", "VBZ", "PRP", "VBZ", "DT", "JJ", "NN", "IN", "NN", "VBG", "DT", "NN", "RB", ",", "RB", "VBG", "DT", "NNP", "NN", ",", "CC", "VBZ", "JJ", "CC", "VBZ", "IN", "DT", "NN", "IN", "JJ", "NNS", "."], "head": [2, 3, 0, 8, 8, 8, 8, 3, 10, 8, 8, 13, 11, 11, 17, 17, 11, 20, 20, 17, 24, 24, 24, 17, 26, 24, 29, 29, 26, 32, 32, 29, 3], "deprel": ["det", "nsubj", "root", "nsubj", "cop", "det", "amod", "ccomp", "case", "nmod", "acl", "det", "obj", "advmod", "punct", "advmod", "conj", "det", "compound", "obj", "punct", "cc", "cop", "conj", "cc", "conj", "case", "det", "obl", "case", "amod", "nmod", "punct"], "triples": [{"uid": "1812-0", "target_tags": "The\\O difference\\O is\\O it\\O 's\\O a\\O whole\\O lot\\O of\\O fun\\O using\\O the\\O laptop\\O now\\O ,\\O still\\O learning\\O the\\O Apple\\B navigation\\I ,\\O but\\O is\\O fun\\O and\\O comes\\O with\\O a\\O lot\\O of\\O cool\\O apps\\O .\\O", "opinion_tags": "The\\O difference\\O is\\O it\\O 's\\O a\\O whole\\O lot\\O of\\O fun\\O using\\O the\\O laptop\\O now\\O ,\\O still\\O learning\\O the\\O Apple\\O navigation\\O ,\\O but\\O is\\O fun\\B and\\O comes\\O with\\O a\\O lot\\O of\\O cool\\O apps\\O .\\O", "sentiment": "neutral"}, {"uid": "1812-1", "target_tags": "The\\O difference\\O is\\O it\\O 's\\O a\\O whole\\O lot\\O of\\O fun\\O using\\O the\\O laptop\\O now\\O ,\\O still\\O learning\\O the\\O Apple\\O navigation\\O ,\\O but\\O is\\O fun\\O and\\O comes\\O with\\O a\\O lot\\O of\\O cool\\O apps\\B .\\O", "opinion_tags": "The\\O difference\\O is\\O it\\O 's\\O a\\O whole\\O lot\\O of\\O fun\\O using\\O the\\O laptop\\O now\\O ,\\O still\\O learning\\O the\\O Apple\\O navigation\\O ,\\O but\\O is\\O fun\\O and\\O comes\\O with\\O a\\O lot\\O of\\O cool\\B apps\\O .\\O", "sentiment": "positive"}]}, {"id": "2273", "sentence": "WIth the upgraded memory , the MacBook Pro never has an issue running many many applications at once !", "postag": ["IN", "DT", "VBN", "NN", ",", "DT", "NNP", "NNP", "RB", "VBZ", "DT", "NN", "VBG", "JJ", "JJ", "NNS", "IN", "RB", "."], "head": [4, 4, 4, 10, 10, 8, 8, 10, 10, 0, 12, 10, 12, 16, 16, 13, 18, 13, 10], "deprel": ["case", "det", "amod", "obl", "punct", "det", "compound", "nsubj", "advmod", "root", "det", "obj", "acl", "amod", "amod", "obj", "case", "obl", "punct"], "triples": [{"uid": "2273-0", "target_tags": "WIth\\O the\\O upgraded\\B memory\\I ,\\O the\\O MacBook\\O Pro\\O never\\O has\\O an\\O issue\\O running\\O many\\O many\\O applications\\O at\\O once\\O !\\O", "opinion_tags": "WIth\\O the\\O upgraded\\O memory\\O ,\\O the\\O MacBook\\O Pro\\O never\\B has\\I an\\I issue\\I running\\O many\\O many\\O applications\\O at\\O once\\O !\\O", "sentiment": "positive"}]}, {"id": "2765", "sentence": "I finally decided on this laptop because it was the right price for what I need it .", "postag": ["PRP", "RB", "VBD", "IN", "DT", "NN", "IN", "PRP", "VBD", "DT", "JJ", "NN", "IN", "WP", "PRP", "VBP", "PRP", "."], "head": [3, 3, 0, 6, 6, 3, 12, 12, 12, 12, 12, 3, 14, 12, 16, 14, 16, 3], "deprel": ["nsubj", "advmod", "root", "case", "det", "obl", "mark", "nsubj", "cop", "det", "amod", "advcl", "case", "nmod", "nsubj", "acl:relcl", "obj", "punct"], "triples": [{"uid": "2765-0", "target_tags": "I\\O finally\\O decided\\O on\\O this\\O laptop\\O because\\O it\\O was\\O the\\O right\\O price\\B for\\O what\\O I\\O need\\O it\\O .\\O", "opinion_tags": "I\\O finally\\O decided\\O on\\O this\\O laptop\\O because\\O it\\O was\\O the\\O right\\B price\\O for\\O what\\O I\\O need\\O it\\O .\\O", "sentiment": "positive"}]}, {"id": "1138", "sentence": "Ever since I bought this laptop , so far I 've experience nothing but constant break downs of the laptop and bad customer services I received over the phone with toshiba customer services hotlines .", "postag": ["RB", "IN", "PRP", "VBD", "DT", "NN", ",", "RB", "RB", "PRP", "VBP", "VB", "NN", "CC", "JJ", "NN", "NNS", "IN", "DT", "NN", "CC", "JJ", "NN", "NNS", "PRP", "VBD", "IN", "DT", "NN", "IN", "NNP", "NN", "NNS", "NNS", "."], "head": [12, 4, 4, 12, 6, 4, 12, 9, 12, 12, 12, 0, 12, 17, 17, 17, 13, 20, 20, 17, 24, 24, 24, 20, 26, 24, 29, 29, 26, 34, 34, 33, 34, 26, 12], "deprel": ["advmod", "mark", "nsubj", "advcl", "det", "obj", "punct", "advmod", "advmod", "nsubj", "aux", "root", "obj", "cc", "amod", "compound", "conj", "case", "det", "nmod", "cc", "amod", "compound", "conj", "nsubj", "acl:relcl", "case", "det", "obl", "case", "compound", "compound", "compound", "obl", "punct"], "triples": [{"uid": "1138-0", "target_tags": "Ever\\O since\\O I\\O bought\\O this\\O laptop\\O ,\\O so\\O far\\O I\\O 've\\O experience\\O nothing\\O but\\O constant\\O break\\O downs\\O of\\O the\\O laptop\\O and\\O bad\\O customer\\B services\\I I\\O received\\O over\\O the\\O phone\\O with\\O toshiba\\O customer\\O services\\O hotlines\\O .\\O", "opinion_tags": "Ever\\O since\\O I\\O bought\\O this\\O laptop\\O ,\\O so\\O far\\O I\\O 've\\O experience\\O nothing\\O but\\O constant\\O break\\O downs\\O of\\O the\\O laptop\\O and\\O bad\\B customer\\O services\\O I\\O received\\O over\\O the\\O phone\\O with\\O toshiba\\O customer\\O services\\O hotlines\\O .\\O", "sentiment": "negative"}]}, {"id": "1414", "sentence": "The graphics were awful and the warranty is n't even worth the cheap payment on the computer .", "postag": ["DT", "NNS", "VBD", "JJ", "CC", "DT", "NN", "VBZ", "RB", "RB", "JJ", "DT", "JJ", "NN", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 11, 7, 11, 11, 11, 11, 4, 14, 14, 11, 17, 17, 14, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "advmod", "advmod", "conj", "det", "amod", "obj", "case", "det", "nmod", "punct"], "triples": [{"uid": "1414-0", "target_tags": "The\\O graphics\\B were\\O awful\\O and\\O the\\O warranty\\O is\\O n't\\O even\\O worth\\O the\\O cheap\\O payment\\O on\\O the\\O computer\\O .\\O", "opinion_tags": "The\\O graphics\\O were\\O awful\\B and\\O the\\O warranty\\O is\\O n't\\O even\\O worth\\O the\\O cheap\\O payment\\O on\\O the\\O computer\\O .\\O", "sentiment": "negative"}, {"uid": "1414-1", "target_tags": "The\\O graphics\\O were\\O awful\\O and\\O the\\O warranty\\B is\\O n't\\O even\\O worth\\O the\\O cheap\\O payment\\O on\\O the\\O computer\\O .\\O", "opinion_tags": "The\\O graphics\\O were\\O awful\\O and\\O the\\O warranty\\O is\\B n't\\I even\\I worth\\I the\\O cheap\\O payment\\O on\\O the\\O computer\\O .\\O", "sentiment": "negative"}]}, {"id": "1087", "sentence": "EITHER WAY , THE KEYBOARD IS UNSATISFACTORY .", "postag": ["DT", "NN", ",", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 7, 7, 5, 7, 7, 0, 7], "deprel": ["det", "obl:tmod", "punct", "det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "1087-0", "target_tags": "EITHER\\O WAY\\O ,\\O THE\\O KEYBOARD\\B IS\\O UNSATISFACTORY\\O .\\O", "opinion_tags": "EITHER\\O WAY\\O ,\\O THE\\O KEYBOARD\\O IS\\O UNSATISFACTORY\\B .\\I", "sentiment": "negative"}]}, {"id": "3042", "sentence": "Hard disk - The new editions gives you more hard disk space ( 500GB instead of 320GB ) but time has taught me never to trust an internal hard disk .", "postag": ["JJ", "NN", ",", "DT", "JJ", "NNS", "VBZ", "PRP", "RBR", "JJ", "NN", "NN", "-LRB-", "NN", "RB", "IN", "NN", "-RRB-", "CC", "NN", "VBZ", "VBN", "PRP", "RB", "TO", "VB", "DT", "JJ", "JJ", "NN", "."], "head": [2, 0, 2, 6, 6, 7, 2, 7, 10, 12, 12, 7, 14, 12, 17, 15, 14, 14, 22, 22, 22, 2, 22, 22, 26, 22, 30, 30, 30, 26, 2], "deprel": ["amod", "root", "punct", "det", "amod", "nsubj", "parataxis", "i<PERSON><PERSON>", "advmod", "amod", "compound", "obj", "punct", "appos", "case", "fixed", "nmod", "punct", "cc", "nsubj", "aux", "conj", "obj", "advmod", "mark", "xcomp", "det", "amod", "amod", "obj", "punct"], "triples": [{"uid": "3042-0", "target_tags": "Hard\\O disk\\O -\\O The\\O new\\O editions\\O gives\\O you\\O more\\O hard\\B disk\\I space\\I (\\O 500GB\\O instead\\O of\\O 320GB\\O )\\O but\\O time\\O has\\O taught\\O me\\O never\\O to\\O trust\\O an\\O internal\\O hard\\O disk\\O .\\O", "opinion_tags": "Hard\\O disk\\O -\\O The\\O new\\O editions\\O gives\\O you\\O more\\B hard\\O disk\\O space\\O (\\O 500GB\\O instead\\O of\\O 320GB\\O )\\O but\\O time\\O has\\O taught\\O me\\O never\\O to\\O trust\\O an\\O internal\\O hard\\O disk\\O .\\O", "sentiment": "positive"}, {"uid": "3042-1", "target_tags": "Hard\\O disk\\O -\\O The\\O new\\O editions\\O gives\\O you\\O more\\O hard\\O disk\\O space\\O (\\O 500GB\\O instead\\O of\\O 320GB\\O )\\O but\\O time\\O has\\O taught\\O me\\O never\\O to\\O trust\\O an\\O internal\\B hard\\I disk\\I .\\O", "opinion_tags": "Hard\\O disk\\O -\\O The\\O new\\O editions\\O gives\\O you\\O more\\O hard\\O disk\\O space\\O (\\O 500GB\\O instead\\O of\\O 320GB\\O )\\O but\\O time\\O has\\O taught\\O me\\O never\\B to\\I trust\\I an\\O internal\\O hard\\O disk\\O .\\O", "sentiment": "neutral"}]}, {"id": "941", "sentence": "I had it four months when my disc drive refused to open .", "postag": ["PRP", "VBD", "PRP", "CD", "NNS", "WRB", "PRP$", "NN", "NN", "VBD", "TO", "VB", "."], "head": [2, 0, 2, 5, 2, 10, 9, 9, 10, 2, 12, 10, 2], "deprel": ["nsubj", "root", "obj", "nummod", "obl:tmod", "mark", "nmod:poss", "compound", "nsubj", "advcl", "mark", "xcomp", "punct"], "triples": [{"uid": "941-0", "target_tags": "I\\O had\\O it\\O four\\O months\\O when\\O my\\O disc\\B drive\\I refused\\O to\\O open\\O .\\O", "opinion_tags": "I\\O had\\O it\\O four\\O months\\O when\\O my\\O disc\\O drive\\O refused\\B to\\I open\\I .\\O", "sentiment": "negative"}]}, {"id": "1913", "sentence": "This process continued to repeat itself until the mother board had been replaced 4 times and the hard drive replaced 3 times .", "postag": ["DT", "NN", "VBD", "TO", "VB", "PRP", "IN", "DT", "NN", "NN", "VBD", "VBN", "VBN", "CD", "NNS", "CC", "DT", "JJ", "NN", "VBD", "CD", "NNS", "."], "head": [2, 3, 0, 5, 3, 5, 13, 10, 10, 13, 13, 13, 5, 15, 13, 20, 19, 19, 20, 3, 22, 20, 3], "deprel": ["det", "nsubj", "root", "mark", "xcomp", "obj", "mark", "det", "compound", "nsubj:pass", "aux", "aux:pass", "advcl", "nummod", "obl:tmod", "cc", "det", "amod", "nsubj", "conj", "nummod", "obl:tmod", "punct"], "triples": [{"uid": "1913-0", "target_tags": "This\\O process\\O continued\\O to\\O repeat\\O itself\\O until\\O the\\O mother\\B board\\I had\\O been\\O replaced\\O 4\\O times\\O and\\O the\\O hard\\O drive\\O replaced\\O 3\\O times\\O .\\O", "opinion_tags": "This\\O process\\O continued\\O to\\O repeat\\O itself\\O until\\O the\\O mother\\O board\\O had\\O been\\O replaced\\B 4\\O times\\O and\\O the\\O hard\\O drive\\O replaced\\O 3\\O times\\O .\\O", "sentiment": "negative"}, {"uid": "1913-1", "target_tags": "This\\O process\\O continued\\O to\\O repeat\\O itself\\O until\\O the\\O mother\\O board\\O had\\O been\\O replaced\\O 4\\O times\\O and\\O the\\O hard\\B drive\\I replaced\\O 3\\O times\\O .\\O", "opinion_tags": "This\\O process\\O continued\\O to\\O repeat\\O itself\\O until\\O the\\O mother\\O board\\O had\\O been\\O replaced\\O 4\\O times\\O and\\O the\\O hard\\O drive\\O replaced\\B 3\\O times\\O .\\O", "sentiment": "negative"}]}, {"id": "41", "sentence": "Also , the space bar makes a noisy click every time you use it .", "postag": ["RB", ",", "DT", "NN", "NN", "VBZ", "DT", "JJ", "NN", "DT", "NN", "PRP", "VBP", "PRP", "."], "head": [6, 6, 5, 5, 6, 0, 9, 9, 6, 11, 6, 13, 11, 13, 6], "deprel": ["advmod", "punct", "det", "compound", "nsubj", "root", "det", "amod", "obj", "det", "obl:tmod", "nsubj", "acl:relcl", "obj", "punct"], "triples": [{"uid": "41-0", "target_tags": "Also\\O ,\\O the\\O space\\B bar\\I makes\\O a\\O noisy\\O click\\O every\\O time\\O you\\O use\\O it\\O .\\O", "opinion_tags": "Also\\O ,\\O the\\O space\\O bar\\O makes\\O a\\O noisy\\B click\\O every\\O time\\O you\\O use\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "1420", "sentence": "Then after paying for it to be examined I was told it was same problem cited on website but I 'd have to pay anyways since it was past warrenty .", "postag": ["RB", "IN", "VBG", "IN", "PRP", "TO", "VB", "VBN", "PRP", "VBD", "VBN", "PRP", "VBD", "JJ", "NN", "VBN", "IN", "NN", "CC", "PRP", "MD", "VB", "TO", "VB", "RB", "IN", "PRP", "VBD", "JJ", "NN", "."], "head": [11, 3, 11, 5, 3, 8, 8, 3, 11, 11, 0, 15, 15, 15, 11, 15, 18, 16, 22, 22, 22, 11, 24, 22, 24, 30, 30, 30, 30, 24, 11], "deprel": ["advmod", "mark", "advcl", "case", "obl", "mark", "aux:pass", "advcl", "nsubj:pass", "aux:pass", "root", "nsubj", "cop", "amod", "ccomp", "acl", "case", "obl", "cc", "nsubj", "aux", "conj", "mark", "xcomp", "advmod", "mark", "nsubj", "cop", "amod", "advcl", "punct"], "triples": [{"uid": "1420-0", "target_tags": "Then\\O after\\O paying\\O for\\O it\\O to\\O be\\O examined\\O I\\O was\\O told\\O it\\O was\\O same\\O problem\\O cited\\O on\\O website\\O but\\O I\\O 'd\\O have\\O to\\O pay\\O anyways\\O since\\O it\\O was\\O past\\O warrenty\\B .\\O", "opinion_tags": "Then\\O after\\O paying\\O for\\O it\\O to\\O be\\O examined\\O I\\O was\\O told\\O it\\O was\\O same\\O problem\\O cited\\O on\\O website\\O but\\O I\\O 'd\\O have\\O to\\O pay\\O anyways\\O since\\O it\\O was\\O past\\B warrenty\\O .\\O", "sentiment": "negative"}]}, {"id": "926", "sentence": "But guess what ? ( you have to buy an external dvd drive it does n't have a built in type ) The notebook ca n't be used because it does n't read anything for an external drive .", "postag": ["CC", "VB", "WP", ".", "-LRB-", "PRP", "VBP", "TO", "VB", "DT", "JJ", "NN", "NN", "PRP", "VBZ", "RB", "VB", "DT", "VBN", "IN", "NN", "-RRB-", "DT", "NN", "MD", "RB", "VB", "VBN", "IN", "PRP", "VBZ", "RB", "VB", "NN", "IN", "DT", "JJ", "NN", "."], "head": [2, 0, 2, 2, 7, 7, 2, 9, 7, 13, 13, 13, 9, 17, 17, 17, 7, 21, 21, 21, 17, 7, 24, 28, 28, 28, 28, 2, 33, 33, 33, 33, 28, 33, 38, 38, 38, 33, 2], "deprel": ["cc", "root", "obj", "punct", "punct", "nsubj", "parataxis", "mark", "xcomp", "det", "amod", "compound", "obj", "nsubj", "aux", "advmod", "parataxis", "det", "amod", "case", "obj", "punct", "det", "nsubj:pass", "aux", "advmod", "aux:pass", "parataxis", "mark", "nsubj", "aux", "advmod", "advcl", "obj", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "926-0", "target_tags": "But\\O guess\\O what\\O ?\\O (\\O you\\O have\\O to\\O buy\\O an\\O external\\B dvd\\I drive\\I it\\O does\\O n't\\O have\\O a\\O built\\O in\\O type\\O )\\O The\\O notebook\\O ca\\O n't\\O be\\O used\\O because\\O it\\O does\\O n't\\O read\\O anything\\O for\\O an\\O external\\O drive\\O .\\O", "opinion_tags": "But\\O guess\\O what\\O ?\\O (\\O you\\O have\\O to\\O buy\\O an\\O external\\O dvd\\O drive\\O it\\O does\\B n't\\I have\\I a\\O built\\O in\\O type\\O )\\O The\\O notebook\\O ca\\O n't\\O be\\O used\\O because\\O it\\O does\\O n't\\O read\\O anything\\O for\\O an\\O external\\O drive\\O .\\O", "sentiment": "negative"}, {"uid": "926-1", "target_tags": "But\\O guess\\O what\\O ?\\O (\\O you\\O have\\O to\\O buy\\O an\\O external\\O dvd\\O drive\\O it\\O does\\O n't\\O have\\O a\\O built\\O in\\O type\\O )\\O The\\O notebook\\O ca\\O n't\\O be\\O used\\O because\\O it\\O does\\O n't\\O read\\O anything\\O for\\O an\\O external\\B drive\\I .\\O", "opinion_tags": "But\\O guess\\O what\\O ?\\O (\\O you\\O have\\O to\\O buy\\O an\\O external\\O dvd\\O drive\\O it\\O does\\O n't\\O have\\O a\\O built\\O in\\O type\\O )\\O The\\O notebook\\O ca\\O n't\\O be\\O used\\O because\\O it\\O does\\B n't\\I read\\I anything\\O for\\O an\\O external\\O drive\\O .\\O", "sentiment": "negative"}]}, {"id": "2140", "sentence": "All apple associates are always willing to help you out with anything , no matter when you purchased the computer and how many years passed .", "postag": ["DT", "NN", "NNS", "VBP", "RB", "JJ", "TO", "VB", "PRP", "RP", "IN", "NN", ",", "DT", "NN", "WRB", "PRP", "VBD", "DT", "NN", "CC", "WRB", "JJ", "NNS", "VBD", "."], "head": [3, 3, 6, 6, 6, 0, 8, 6, 8, 8, 12, 8, 15, 15, 6, 18, 18, 15, 20, 18, 25, 23, 24, 25, 18, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "mark", "xcomp", "obj", "compound:prt", "case", "obl", "punct", "det", "parataxis", "mark", "nsubj", "advcl", "det", "obj", "cc", "mark", "amod", "nsubj", "conj", "punct"], "triples": [{"uid": "2140-0", "target_tags": "All\\O apple\\B associates\\I are\\O always\\O willing\\O to\\O help\\O you\\O out\\O with\\O anything\\O ,\\O no\\O matter\\O when\\O you\\O purchased\\O the\\O computer\\O and\\O how\\O many\\O years\\O passed\\O .\\O", "opinion_tags": "All\\O apple\\O associates\\O are\\O always\\O willing\\B to\\O help\\O you\\O out\\O with\\O anything\\O ,\\O no\\O matter\\O when\\O you\\O purchased\\O the\\O computer\\O and\\O how\\O many\\O years\\O passed\\O .\\O", "sentiment": "positive"}]}, {"id": "2846", "sentence": "It works great for general internet use , Microsoft Office apps , home bookkeeping , etc .", "postag": ["PRP", "VBZ", "JJ", "IN", "JJ", "NN", "NN", ",", "NNP", "NNP", "NNS", ",", "NN", "NN", ",", "FW", "."], "head": [2, 0, 2, 7, 7, 7, 2, 7, 11, 11, 7, 14, 14, 7, 16, 7, 2], "deprel": ["nsubj", "root", "xcomp", "case", "amod", "compound", "obl", "punct", "compound", "compound", "conj", "punct", "compound", "conj", "punct", "conj", "punct"], "triples": [{"uid": "2846-0", "target_tags": "It\\O works\\O great\\O for\\O general\\O internet\\O use\\O ,\\O Microsoft\\B Office\\I apps\\I ,\\O home\\O bookkeeping\\O ,\\O etc\\O .\\O", "opinion_tags": "It\\O works\\O great\\B for\\O general\\O internet\\O use\\O ,\\O Microsoft\\O Office\\O apps\\O ,\\O home\\O bookkeeping\\O ,\\O etc\\O .\\O", "sentiment": "positive"}, {"uid": "2846-1", "target_tags": "It\\O works\\O great\\O for\\O general\\O internet\\B use\\I ,\\O Microsoft\\O Office\\O apps\\O ,\\O home\\O bookkeeping\\O ,\\O etc\\O .\\O", "opinion_tags": "It\\O works\\O great\\B for\\O general\\O internet\\O use\\O ,\\O Microsoft\\O Office\\O apps\\O ,\\O home\\O bookkeeping\\O ,\\O etc\\O .\\O", "sentiment": "positive"}]}, {"id": "1410", "sentence": "There is no cd drive on the computer , which defeats the purpose of keeping files on a cd .", "postag": ["EX", "VBZ", "DT", "NN", "NN", "IN", "DT", "NN", ",", "WDT", "VBZ", "DT", "NN", "IN", "VBG", "NNS", "IN", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 11, 11, 5, 13, 11, 15, 13, 15, 19, 19, 15, 2], "deprel": ["expl", "root", "det", "compound", "nsubj", "case", "det", "nmod", "punct", "nsubj", "acl:relcl", "det", "obj", "mark", "acl", "obj", "case", "det", "obl", "punct"], "triples": [{"uid": "1410-0", "target_tags": "There\\O is\\O no\\O cd\\B drive\\I on\\O the\\O computer\\O ,\\O which\\O defeats\\O the\\O purpose\\O of\\O keeping\\O files\\O on\\O a\\O cd\\O .\\O", "opinion_tags": "There\\O is\\O no\\B cd\\O drive\\O on\\O the\\O computer\\O ,\\O which\\O defeats\\O the\\O purpose\\O of\\O keeping\\O files\\O on\\O a\\O cd\\O .\\O", "sentiment": "negative"}]}, {"id": "1879", "sentence": "2.The wireless card is low quality .", "postag": ["DT", "JJ", "NN", "VBZ", "JJ", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 6], "deprel": ["det", "amod", "nsubj", "cop", "amod", "root", "punct"], "triples": [{"uid": "1879-0", "target_tags": "2.The\\O wireless\\B card\\I is\\O low\\O quality\\O .\\O", "opinion_tags": "2.The\\O wireless\\O card\\O is\\O low\\B quality\\I .\\O", "sentiment": "negative"}]}, {"id": "2359", "sentence": "The screen is bright and vivid and the keyboard is very easy to use , very important for use quick typers .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "JJ", "CC", "DT", "NN", "VBZ", "RB", "JJ", "TO", "VB", ",", "RB", "JJ", "IN", "VB", "JJ", "NNS", "."], "head": [2, 4, 4, 0, 6, 4, 12, 9, 12, 12, 12, 4, 14, 12, 17, 17, 12, 19, 17, 21, 19, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "cc", "det", "nsubj", "cop", "advmod", "conj", "mark", "xcomp", "punct", "advmod", "conj", "mark", "advcl", "amod", "obj", "punct"], "triples": [{"uid": "2359-0", "target_tags": "The\\O screen\\B is\\O bright\\O and\\O vivid\\O and\\O the\\O keyboard\\O is\\O very\\O easy\\O to\\O use\\O ,\\O very\\O important\\O for\\O use\\O quick\\O typers\\O .\\O", "opinion_tags": "The\\O screen\\O is\\O bright\\B and\\O vivid\\B and\\O the\\O keyboard\\O is\\O very\\O easy\\O to\\O use\\O ,\\O very\\O important\\O for\\O use\\O quick\\O typers\\O .\\O", "sentiment": "positive"}, {"uid": "2359-1", "target_tags": "The\\O screen\\O is\\O bright\\O and\\O vivid\\O and\\O the\\O keyboard\\B is\\O very\\O easy\\O to\\O use\\O ,\\O very\\O important\\O for\\O use\\O quick\\O typers\\O .\\O", "opinion_tags": "The\\O screen\\O is\\O bright\\O and\\O vivid\\O and\\O the\\O keyboard\\O is\\O very\\O easy\\B to\\I use\\I ,\\O very\\O important\\O for\\O use\\O quick\\O typers\\O .\\O", "sentiment": "positive"}]}, {"id": "2352", "sentence": "A key contributor that led me to <PERSON> is the art aspect .", "postag": ["DT", "JJ", "NN", "WDT", "VBD", "PRP", "IN", "NNP", "VBZ", "DT", "NN", "NN", "."], "head": [3, 3, 12, 5, 3, 5, 8, 5, 12, 12, 12, 0, 12], "deprel": ["det", "amod", "nsubj", "nsubj", "acl:relcl", "obj", "case", "obl", "cop", "det", "compound", "root", "punct"], "triples": [{"uid": "2352-0", "target_tags": "A\\O key\\O contributor\\O that\\O led\\O me\\O to\\O Mac\\O is\\O the\\O art\\B aspect\\I .\\O", "opinion_tags": "A\\O key\\O contributor\\B that\\O led\\O me\\O to\\O Mac\\O is\\O the\\O art\\O aspect\\O .\\O", "sentiment": "positive"}]}, {"id": "2088", "sentence": "The sound is a bit quiet if you 're on a plane , this can easily be overcome with a decent pair of head phones .", "postag": ["DT", "NN", "VBZ", "DT", "NN", "JJ", "IN", "PRP", "VBP", "IN", "DT", "NN", ",", "DT", "MD", "RB", "VB", "VBN", "IN", "DT", "JJ", "NN", "IN", "NN", "NNS", "."], "head": [2, 6, 6, 5, 6, 0, 12, 12, 12, 12, 12, 6, 6, 18, 18, 18, 18, 6, 22, 22, 22, 18, 25, 25, 22, 6], "deprel": ["det", "nsubj", "cop", "det", "obl:npmod", "root", "mark", "nsubj", "cop", "case", "det", "advcl", "punct", "nsubj:pass", "aux", "advmod", "aux:pass", "parataxis", "case", "det", "amod", "obl", "case", "compound", "nmod", "punct"], "triples": [{"uid": "2088-0", "target_tags": "The\\O sound\\B is\\O a\\O bit\\O quiet\\O if\\O you\\O 're\\O on\\O a\\O plane\\O ,\\O this\\O can\\O easily\\O be\\O overcome\\O with\\O a\\O decent\\O pair\\O of\\O head\\O phones\\O .\\O", "opinion_tags": "The\\O sound\\O is\\O a\\O bit\\O quiet\\B if\\O you\\O 're\\O on\\O a\\O plane\\O ,\\O this\\O can\\O easily\\O be\\O overcome\\O with\\O a\\O decent\\O pair\\O of\\O head\\O phones\\O .\\O", "sentiment": "negative"}]}, {"id": "2063", "sentence": "Web access through the 3G network is so slow , it 's very frustrating and VERY DISAPPOINTING .", "postag": ["NN", "NN", "IN", "DT", "NNP", "NN", "VBZ", "RB", "JJ", ",", "PRP", "VBZ", "RB", "JJ", "CC", "RB", "JJ", "."], "head": [2, 9, 6, 6, 6, 2, 9, 9, 0, 9, 14, 14, 14, 9, 17, 17, 14, 9], "deprel": ["compound", "nsubj", "case", "det", "compound", "nmod", "cop", "advmod", "root", "punct", "nsubj", "cop", "advmod", "parataxis", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "2063-0", "target_tags": "Web\\O access\\O through\\O the\\O 3G\\B network\\I is\\O so\\O slow\\O ,\\O it\\O 's\\O very\\O frustrating\\O and\\O VERY\\O DISAPPOINTING\\O .\\O", "opinion_tags": "Web\\O access\\O through\\O the\\O 3G\\O network\\O is\\O so\\O slow\\B ,\\O it\\O 's\\O very\\O frustrating\\B and\\O VERY\\O DISAPPOINTING\\O .\\O", "sentiment": "negative"}, {"uid": "2063-1", "target_tags": "Web\\B access\\I through\\O the\\O 3G\\O network\\O is\\O so\\O slow\\O ,\\O it\\O 's\\O very\\O frustrating\\O and\\O VERY\\O DISAPPOINTING\\O .\\O", "opinion_tags": "Web\\O access\\O through\\O the\\O 3G\\O network\\O is\\O so\\O slow\\B ,\\O it\\O 's\\O very\\O frustrating\\B and\\O VERY\\O DISAPPOINTING\\O .\\O", "sentiment": "negative"}]}, {"id": "1610", "sentence": "My macbook is so much better looking and so thin !", "postag": ["PRP$", "NN", "VBZ", "RB", "RB", "RBR", "VBG", "CC", "RB", "JJ", "."], "head": [2, 7, 7, 5, 7, 7, 0, 10, 10, 7, 7], "deprel": ["nmod:poss", "nsubj", "cop", "advmod", "advmod", "advmod", "root", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "1610-0", "target_tags": "My\\O macbook\\O is\\O so\\O much\\O better\\O looking\\B and\\O so\\O thin\\O !\\O", "opinion_tags": "My\\O macbook\\O is\\O so\\O much\\O better\\B looking\\O and\\O so\\O thin\\O !\\O", "sentiment": "positive"}]}, {"id": "1558", "sentence": "The Macbook starts fast , does n't crash , has a fantastic display , is small and light ( I have the 13.3 '' model ) , and is n't always complaining about updates , lost connections , errors , blue screens , etc .", "postag": ["DT", "NNP", "VBZ", "RB", ",", "VBZ", "RB", "VB", ",", "VBZ", "DT", "JJ", "NN", ",", "VBZ", "JJ", "CC", "JJ", "-LRB-", "PRP", "VBP", "DT", "CD", "''", "NN", "-RRB-", ",", "CC", "VBZ", "RB", "RB", "VBG", "IN", "NNS", ",", "VBN", "NNS", ",", "NNS", ",", "JJ", "NNS", ",", "FW", "."], "head": [2, 3, 0, 3, 8, 8, 8, 3, 10, 8, 13, 13, 10, 16, 16, 3, 18, 16, 21, 21, 16, 25, 25, 25, 21, 21, 32, 32, 32, 32, 32, 3, 34, 32, 37, 37, 34, 39, 34, 42, 42, 34, 44, 34, 3], "deprel": ["det", "nsubj", "root", "advmod", "punct", "aux", "advmod", "parataxis", "punct", "conj", "det", "amod", "obj", "punct", "cop", "parataxis", "cc", "conj", "punct", "nsubj", "parataxis", "det", "nummod", "punct", "obj", "punct", "punct", "cc", "aux", "advmod", "advmod", "conj", "case", "obl", "punct", "amod", "conj", "punct", "conj", "punct", "amod", "conj", "punct", "conj", "punct"], "triples": [{"uid": "1558-0", "target_tags": "The\\O Macbook\\O starts\\O fast\\O ,\\O does\\O n't\\O crash\\O ,\\O has\\O a\\O fantastic\\O display\\B ,\\O is\\O small\\O and\\O light\\O (\\O I\\O have\\O the\\O 13.3\\O ''\\O model\\O )\\O ,\\O and\\O is\\O n't\\O always\\O complaining\\O about\\O updates\\O ,\\O lost\\O connections\\O ,\\O errors\\O ,\\O blue\\O screens\\O ,\\O etc\\O .\\O", "opinion_tags": "The\\O Macbook\\O starts\\O fast\\O ,\\O does\\O n't\\O crash\\O ,\\O has\\O a\\O fantastic\\B display\\O ,\\O is\\O small\\O and\\O light\\O (\\O I\\O have\\O the\\O 13.3\\O ''\\O model\\O )\\O ,\\O and\\O is\\O n't\\O always\\O complaining\\O about\\O updates\\O ,\\O lost\\O connections\\O ,\\O errors\\O ,\\O blue\\O screens\\O ,\\O etc\\O .\\O", "sentiment": "positive"}, {"uid": "1558-1", "target_tags": "The\\O Macbook\\O starts\\B fast\\O ,\\O does\\O n't\\O crash\\O ,\\O has\\O a\\O fantastic\\O display\\O ,\\O is\\O small\\O and\\O light\\O (\\O I\\O have\\O the\\O 13.3\\O ''\\O model\\O )\\O ,\\O and\\O is\\O n't\\O always\\O complaining\\O about\\O updates\\O ,\\O lost\\O connections\\O ,\\O errors\\O ,\\O blue\\O screens\\O ,\\O etc\\O .\\O", "opinion_tags": "The\\O Macbook\\O starts\\O fast\\B ,\\O does\\O n't\\O crash\\O ,\\O has\\O a\\O fantastic\\O display\\O ,\\O is\\O small\\O and\\O light\\O (\\O I\\O have\\O the\\O 13.3\\O ''\\O model\\O )\\O ,\\O and\\O is\\O n't\\O always\\O complaining\\O about\\O updates\\O ,\\O lost\\O connections\\O ,\\O errors\\O ,\\O blue\\O screens\\O ,\\O etc\\O .\\O", "sentiment": "positive"}]}, {"id": "762", "sentence": "I agree with the previous comment that ASUS TECH SUPPORT IS HORRIBLE WHICH IS A CON IN MY OPINION .", "postag": ["PRP", "VBP", "IN", "DT", "JJ", "NN", "IN", "NNP", "NNP", "NNP", "VBZ", "JJ", "WDT", "VBZ", "DT", "NN", "IN", "PRP$", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 12, 10, 10, 12, 12, 6, 16, 16, 16, 12, 19, 19, 16, 2], "deprel": ["nsubj", "root", "case", "det", "amod", "obl", "mark", "compound", "compound", "nsubj", "cop", "acl", "nsubj", "cop", "det", "parataxis", "case", "nmod:poss", "nmod", "punct"], "triples": [{"uid": "762-0", "target_tags": "I\\O agree\\O with\\O the\\O previous\\O comment\\O that\\O ASUS\\B TECH\\I SUPPORT\\I IS\\O HORRIBLE\\O WHICH\\O IS\\O A\\O CON\\O IN\\O MY\\O OPINION\\O .\\O", "opinion_tags": "I\\O agree\\O with\\O the\\O previous\\O comment\\O that\\O ASUS\\O TECH\\O SUPPORT\\O IS\\O HORRIBLE\\B WHICH\\O IS\\O A\\O CON\\O IN\\O MY\\O OPINION\\O .\\O", "sentiment": "negative"}]}, {"id": "1524", "sentence": "If a website ever freezes ( which is rare ) , its really easy to force quit .", "postag": ["IN", "DT", "NN", "RB", "VBZ", "-LRB-", "WDT", "VBZ", "JJ", "-RRB-", ",", "PRP$", "RB", "JJ", "TO", "VB", "VB", "."], "head": [5, 3, 5, 5, 17, 9, 9, 9, 5, 9, 5, 14, 14, 0, 16, 14, 14, 14], "deprel": ["mark", "det", "nsubj", "advmod", "advcl", "punct", "nsubj", "cop", "parataxis", "punct", "punct", "nmod:poss", "advmod", "root", "mark", "advcl", "advcl", "punct"], "triples": [{"uid": "1524-0", "target_tags": "If\\O a\\O website\\O ever\\O freezes\\O (\\O which\\O is\\O rare\\O )\\O ,\\O its\\O really\\O easy\\O to\\O force\\B quit\\I .\\O", "opinion_tags": "If\\O a\\O website\\O ever\\O freezes\\O (\\O which\\O is\\O rare\\O )\\O ,\\O its\\O really\\O easy\\B to\\O force\\O quit\\O .\\O", "sentiment": "positive"}]}, {"id": "171", "sentence": "The battery gets so HOT it is scary .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "PRP", "VBZ", "JJ", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 5], "deprel": ["det", "nsubj", "aux", "advmod", "root", "nsubj", "cop", "ccomp", "punct"], "triples": [{"uid": "171-0", "target_tags": "The\\O battery\\B gets\\O so\\O HOT\\O it\\O is\\O scary\\O .\\O", "opinion_tags": "The\\O battery\\O gets\\O so\\O HOT\\B it\\O is\\O scary\\B .\\O", "sentiment": "negative"}]}, {"id": "907", "sentence": "The people are frustrating to work with , the product itself is very cheaply made , and the accessories are less than satisfactory .", "postag": ["DT", "NNS", "VBP", "JJ", "TO", "VB", "IN", ",", "DT", "NN", "PRP", "VBZ", "RB", "RB", "VBN", ",", "CC", "DT", "NNS", "VBP", "JJR", "IN", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 6, 15, 10, 15, 10, 15, 14, 15, 4, 23, 23, 19, 23, 23, 23, 23, 15, 4], "deprel": ["det", "nsubj", "cop", "root", "mark", "advcl", "obl", "punct", "det", "nsubj:pass", "nmod:npmod", "aux:pass", "advmod", "advmod", "parataxis", "punct", "cc", "det", "nsubj", "cop", "advmod", "case", "conj", "punct"], "triples": [{"uid": "907-0", "target_tags": "The\\O people\\O are\\O frustrating\\O to\\O work\\O with\\O ,\\O the\\O product\\O itself\\O is\\O very\\O cheaply\\O made\\O ,\\O and\\O the\\O accessories\\B are\\O less\\O than\\O satisfactory\\O .\\O", "opinion_tags": "The\\O people\\O are\\O frustrating\\O to\\O work\\O with\\O ,\\O the\\O product\\O itself\\O is\\O very\\O cheaply\\O made\\O ,\\O and\\O the\\O accessories\\O are\\O less\\B than\\I satisfactory\\I .\\O", "sentiment": "negative"}]}, {"id": "1252", "sentence": "Somehow the system clock got messed up after reboot .", "postag": ["RB", "DT", "NN", "NN", "VBD", "VBN", "RP", "IN", "NN", "."], "head": [6, 4, 4, 6, 6, 0, 6, 9, 6, 6], "deprel": ["advmod", "det", "compound", "nsubj:pass", "aux:pass", "root", "compound:prt", "case", "obl", "punct"], "triples": [{"uid": "1252-0", "target_tags": "Somehow\\O the\\O system\\B clock\\I got\\O messed\\O up\\O after\\O reboot\\O .\\O", "opinion_tags": "Somehow\\O the\\O system\\O clock\\O got\\O messed\\B up\\I after\\O reboot\\O .\\O", "sentiment": "negative"}]}, {"id": "2509", "sentence": "Not only are the versions of these programs able to be saved , worked on and opened on both a PC and Mac , the versions of these programs on a Mac are graphically and functionally superior .", "postag": ["RB", "RB", "VBP", "DT", "NNS", "IN", "DT", "NNS", "JJ", "TO", "VB", "VBN", ",", "VBN", "IN", "CC", "VBN", "IN", "CC", "DT", "NNP", "CC", "NNP", ",", "DT", "NNS", "IN", "DT", "NNS", "IN", "DT", "NNP", "VBP", "RB", "CC", "RB", "JJ", "."], "head": [2, 9, 9, 5, 9, 8, 8, 5, 0, 12, 12, 9, 14, 12, 14, 17, 12, 21, 21, 21, 17, 23, 21, 9, 26, 37, 29, 29, 26, 32, 32, 29, 37, 37, 36, 34, 9, 9], "deprel": ["advmod", "advmod", "aux", "det", "nsubj", "case", "det", "nmod", "root", "mark", "aux:pass", "xcomp", "punct", "conj", "obl", "cc", "conj", "case", "cc:preconj", "det", "obl", "cc", "conj", "punct", "det", "nsubj", "case", "det", "nmod", "case", "det", "nmod", "cop", "advmod", "cc", "conj", "conj", "punct"], "triples": [{"uid": "2509-0", "target_tags": "Not\\O only\\O are\\O the\\O versions\\O of\\O these\\O programs\\O able\\O to\\O be\\O saved\\O ,\\O worked\\O on\\O and\\O opened\\O on\\O both\\O a\\O PC\\O and\\O Mac\\O ,\\O the\\O versions\\O of\\O these\\O programs\\B on\\O a\\O Mac\\O are\\O graphically\\O and\\O functionally\\O superior\\O .\\O", "opinion_tags": "Not\\O only\\O are\\O the\\O versions\\O of\\O these\\O programs\\O able\\O to\\O be\\O saved\\O ,\\O worked\\O on\\O and\\O opened\\O on\\O both\\O a\\O PC\\O and\\O Mac\\O ,\\O the\\O versions\\O of\\O these\\O programs\\O on\\O a\\O Mac\\O are\\O graphically\\O and\\O functionally\\O superior\\B .\\O", "sentiment": "positive"}]}, {"id": "1597", "sentence": "I previously purchased a 13 '' macbook ( had pro specs and was aluminum style ) which had a nvidia 9800 ( If I am not mistaken ) and it had major heating issues .", "postag": ["PRP", "RB", "VBD", "DT", "CD", "''", "NN", "-LRB-", "VBD", "NN", "NNS", "CC", "VBD", "NN", "NN", "-RRB-", "WDT", "VBD", "DT", "NNP", "CD", "-LRB-", "IN", "PRP", "VBP", "RB", "JJ", "-RRB-", "CC", "PRP", "VBD", "JJ", "NN", "NNS", "."], "head": [3, 3, 0, 7, 7, 7, 3, 9, 3, 11, 9, 15, 15, 15, 9, 9, 18, 7, 21, 18, 18, 27, 27, 27, 27, 27, 18, 27, 31, 31, 18, 34, 34, 31, 3], "deprel": ["nsubj", "advmod", "root", "det", "nummod", "punct", "obj", "punct", "parataxis", "compound", "obj", "cc", "cop", "compound", "conj", "punct", "nsubj", "acl:relcl", "det", "obj", "obj", "punct", "mark", "nsubj", "cop", "advmod", "advcl", "punct", "cc", "nsubj", "conj", "amod", "compound", "obj", "punct"], "triples": [{"uid": "1597-0", "target_tags": "I\\O previously\\O purchased\\O a\\O 13\\O ''\\O macbook\\O (\\O had\\O pro\\O specs\\O and\\O was\\O aluminum\\O style\\O )\\O which\\O had\\O a\\O nvidia\\B 9800\\I (\\O If\\O I\\O am\\O not\\O mistaken\\O )\\O and\\O it\\O had\\O major\\O heating\\O issues\\O .\\O", "opinion_tags": "I\\O previously\\O purchased\\O a\\O 13\\O ''\\O macbook\\O (\\O had\\O pro\\O specs\\O and\\O was\\O aluminum\\O style\\O )\\O which\\O had\\O a\\O nvidia\\O 9800\\O (\\O If\\O I\\O am\\O not\\O mistaken\\O )\\O and\\O it\\O had\\O major\\O heating\\O issues\\B .\\O", "sentiment": "neutral"}]}, {"id": "2865", "sentence": "The feature are good enough for what I need .", "postag": ["DT", "NN", "VBP", "JJ", "JJ", "IN", "WP", "PRP", "VBP", "."], "head": [2, 5, 5, 5, 0, 7, 5, 9, 7, 5], "deprel": ["det", "nsubj", "cop", "amod", "root", "case", "obl", "nsubj", "acl:relcl", "punct"], "triples": [{"uid": "2865-0", "target_tags": "The\\O feature\\B are\\O good\\O enough\\O for\\O what\\O I\\O need\\O .\\O", "opinion_tags": "The\\O feature\\O are\\O good\\B enough\\O for\\O what\\O I\\O need\\O .\\O", "sentiment": "positive"}]}, {"id": "1906", "sentence": "Only a few days after I received the computer back , the screen froze again .", "postag": ["RB", "DT", "JJ", "NNS", "IN", "PRP", "VBD", "DT", "NN", "RB", ",", "DT", "NN", "VBD", "RB", "."], "head": [4, 4, 4, 14, 7, 7, 14, 9, 7, 7, 14, 13, 14, 0, 14, 14], "deprel": ["advmod", "det", "amod", "obl:tmod", "mark", "nsubj", "advcl", "det", "obj", "advmod", "punct", "det", "nsubj", "root", "advmod", "punct"], "triples": [{"uid": "1906-0", "target_tags": "Only\\O a\\O few\\O days\\O after\\O I\\O received\\O the\\O computer\\O back\\O ,\\O the\\O screen\\B froze\\O again\\O .\\O", "opinion_tags": "Only\\O a\\O few\\O days\\O after\\O I\\O received\\O the\\O computer\\O back\\O ,\\O the\\O screen\\O froze\\B again\\O .\\O", "sentiment": "negative"}]}, {"id": "903", "sentence": "They gave me a hard time yet again , but their was a malfunction in the battery itself , it did n't die .", "postag": ["PRP", "VBD", "PRP", "DT", "JJ", "NN", "RB", "RB", ",", "CC", "EX", "VBD", "DT", "NN", "IN", "DT", "NN", "PRP", ",", "PRP", "VBD", "RB", "VB", "."], "head": [2, 0, 2, 6, 6, 2, 2, 2, 12, 12, 12, 2, 14, 12, 17, 17, 14, 17, 2, 23, 23, 23, 12, 2], "deprel": ["nsubj", "root", "i<PERSON><PERSON>", "det", "amod", "obj", "advmod", "advmod", "punct", "cc", "expl", "conj", "det", "nsubj", "case", "det", "nmod", "nmod:npmod", "punct", "nsubj", "aux", "advmod", "parataxis", "punct"], "triples": [{"uid": "903-0", "target_tags": "They\\O gave\\O me\\O a\\O hard\\O time\\O yet\\O again\\O ,\\O but\\O their\\O was\\O a\\O malfunction\\O in\\O the\\O battery\\B itself\\O ,\\O it\\O did\\O n't\\O die\\O .\\O", "opinion_tags": "They\\O gave\\O me\\O a\\O hard\\O time\\O yet\\O again\\O ,\\O but\\O their\\O was\\O a\\O malfunction\\B in\\O the\\O battery\\O itself\\O ,\\O it\\O did\\O n't\\O die\\O .\\O", "sentiment": "negative"}]}, {"id": "59", "sentence": "The computer runs extremely slowly , whether opening Word or My Computer .", "postag": ["DT", "NN", "VBZ", "RB", "RB", ",", "IN", "VBG", "NNP", "CC", "PRP$", "NNP", "."], "head": [2, 3, 0, 5, 3, 9, 9, 9, 3, 12, 12, 9, 3], "deprel": ["det", "nsubj", "root", "advmod", "advmod", "punct", "mark", "amod", "advcl", "cc", "nmod:poss", "conj", "punct"], "triples": [{"uid": "59-0", "target_tags": "The\\O computer\\O runs\\B extremely\\O slowly\\O ,\\O whether\\O opening\\O Word\\O or\\O My\\O Computer\\O .\\O", "opinion_tags": "The\\O computer\\O runs\\O extremely\\O slowly\\B ,\\O whether\\O opening\\O Word\\O or\\O My\\O Computer\\O .\\O", "sentiment": "negative"}]}, {"id": "145", "sentence": "But after using it a couple of weeks , the overall operation is poor .", "postag": ["CC", "IN", "VBG", "PRP", "DT", "NN", "IN", "NNS", ",", "DT", "JJ", "NN", "VBZ", "JJ", "."], "head": [14, 3, 14, 3, 6, 3, 8, 6, 14, 12, 12, 14, 14, 0, 14], "deprel": ["cc", "mark", "advcl", "obj", "det", "obl:tmod", "case", "nmod", "punct", "det", "amod", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "145-0", "target_tags": "But\\O after\\O using\\O it\\O a\\O couple\\O of\\O weeks\\O ,\\O the\\O overall\\O operation\\B is\\O poor\\O .\\O", "opinion_tags": "But\\O after\\O using\\O it\\O a\\O couple\\O of\\O weeks\\O ,\\O the\\O overall\\O operation\\O is\\O poor\\B .\\O", "sentiment": "negative"}]}, {"id": "1064", "sentence": "For the Bluetooth to work properly , you must install the Launch Manager on the Drivers/Applications DVD , or it will not show after the reload .", "postag": ["IN", "DT", "NN", "TO", "VB", "RB", ",", "PRP", "MD", "VB", "DT", "NN", "NN", "IN", "DT", "NNPS", "NNP", ",", "CC", "PRP", "MD", "RB", "VB", "IN", "DT", "NN", "."], "head": [3, 3, 10, 5, 10, 5, 10, 10, 10, 0, 13, 13, 10, 17, 17, 17, 10, 23, 23, 23, 23, 23, 10, 26, 26, 23, 10], "deprel": ["case", "det", "obl", "mark", "advcl", "advmod", "punct", "nsubj", "aux", "root", "det", "compound", "obj", "case", "det", "compound", "obl", "punct", "cc", "nsubj", "aux", "advmod", "conj", "case", "det", "obl", "punct"], "triples": [{"uid": "1064-0", "target_tags": "For\\O the\\O Bluetooth\\B to\\O work\\O properly\\O ,\\O you\\O must\\O install\\O the\\O Launch\\O Manager\\O on\\O the\\O Drivers/Applications\\O DVD\\O ,\\O or\\O it\\O will\\O not\\O show\\O after\\O the\\O reload\\O .\\O", "opinion_tags": "For\\O the\\O Bluetooth\\O to\\O work\\O properly\\B ,\\O you\\O must\\O install\\O the\\O Launch\\O Manager\\O on\\O the\\O Drivers/Applications\\O DVD\\O ,\\O or\\O it\\O will\\O not\\O show\\O after\\O the\\O reload\\O .\\O", "sentiment": "neutral"}]}, {"id": "2901", "sentence": "I have found it very easy to use , very informative , wonder alerts and tutorials making it very easy for someone like me who is not exactly technologically advanced to learn to use the various features and programs .", "postag": ["PRP", "VBP", "VBN", "PRP", "RB", "JJ", "TO", "VB", ",", "RB", "JJ", ",", "VB", "NNS", "CC", "NNS", "VBG", "PRP", "RB", "JJ", "IN", "NN", "IN", "PRP", "WP", "VBZ", "RB", "RB", "RB", "JJ", "TO", "VB", "TO", "VB", "DT", "JJ", "NNS", "CC", "NNS", "."], "head": [3, 3, 0, 3, 6, 3, 8, 6, 11, 11, 8, 13, 8, 17, 16, 14, 13, 17, 20, 17, 22, 20, 24, 22, 30, 30, 30, 30, 30, 22, 32, 30, 34, 32, 37, 37, 34, 39, 37, 3], "deprel": ["nsubj", "aux", "root", "expl", "advmod", "xcomp", "mark", "xcomp", "punct", "advmod", "xcomp", "punct", "conj", "nsubj", "cc", "conj", "advcl", "obj", "advmod", "xcomp", "case", "obl", "case", "nmod", "nsubj", "cop", "advmod", "advmod", "advmod", "acl:relcl", "mark", "advcl", "mark", "xcomp", "det", "amod", "obj", "cc", "conj", "punct"], "triples": [{"uid": "2901-0", "target_tags": "I\\O have\\O found\\O it\\O very\\O easy\\O to\\O use\\O ,\\O very\\O informative\\O ,\\O wonder\\O alerts\\O and\\O tutorials\\O making\\O it\\O very\\O easy\\O for\\O someone\\O like\\O me\\O who\\O is\\O not\\O exactly\\O technologically\\O advanced\\O to\\O learn\\O to\\O use\\O the\\O various\\O features\\B and\\O programs\\O .\\O", "opinion_tags": "I\\O have\\O found\\O it\\O very\\O easy\\O to\\O use\\O ,\\O very\\O informative\\O ,\\O wonder\\O alerts\\O and\\O tutorials\\O making\\O it\\O very\\O easy\\O for\\O someone\\O like\\O me\\O who\\O is\\O not\\O exactly\\O technologically\\O advanced\\O to\\O learn\\O to\\O use\\O the\\O various\\B features\\O and\\O programs\\O .\\O", "sentiment": "positive"}, {"uid": "2901-1", "target_tags": "I\\O have\\O found\\O it\\O very\\O easy\\O to\\O use\\O ,\\O very\\O informative\\O ,\\O wonder\\O alerts\\O and\\O tutorials\\O making\\O it\\O very\\O easy\\O for\\O someone\\O like\\O me\\O who\\O is\\O not\\O exactly\\O technologically\\O advanced\\O to\\O learn\\O to\\O use\\O the\\O various\\O features\\O and\\O programs\\B .\\O", "opinion_tags": "I\\O have\\O found\\O it\\O very\\O easy\\O to\\O use\\O ,\\O very\\O informative\\O ,\\O wonder\\O alerts\\O and\\O tutorials\\O making\\O it\\O very\\O easy\\O for\\O someone\\O like\\O me\\O who\\O is\\O not\\O exactly\\O technologically\\O advanced\\O to\\O learn\\O to\\O use\\O the\\O various\\B features\\O and\\O programs\\O .\\O", "sentiment": "positive"}, {"uid": "2901-2", "target_tags": "I\\O have\\O found\\O it\\O very\\O easy\\O to\\O use\\B ,\\O very\\O informative\\O ,\\O wonder\\O alerts\\O and\\O tutorials\\O making\\O it\\O very\\O easy\\O for\\O someone\\O like\\O me\\O who\\O is\\O not\\O exactly\\O technologically\\O advanced\\O to\\O learn\\O to\\O use\\O the\\O various\\O features\\O and\\O programs\\O .\\O", "opinion_tags": "I\\O have\\O found\\O it\\O very\\O easy\\B to\\O use\\O ,\\O very\\O informative\\O ,\\O wonder\\O alerts\\O and\\O tutorials\\O making\\O it\\O very\\O easy\\O for\\O someone\\O like\\O me\\O who\\O is\\O not\\O exactly\\O technologically\\O advanced\\O to\\O learn\\O to\\O use\\O the\\O various\\O features\\O and\\O programs\\O .\\O", "sentiment": "positive"}]}, {"id": "938", "sentence": "Now , as easy as it is to use , and I do think it is a great STARTER laptop .", "postag": ["RB", ",", "RB", "JJ", "IN", "PRP", "VBZ", "TO", "VB", ",", "CC", "PRP", "VBP", "VB", "PRP", "VBZ", "DT", "JJ", "NN", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 9, 7, 14, 14, 14, 14, 4, 20, 20, 20, 20, 20, 14, 4], "deprel": ["advmod", "punct", "advmod", "root", "mark", "nsubj", "advcl", "mark", "xcomp", "punct", "cc", "nsubj", "aux", "conj", "nsubj", "cop", "det", "amod", "compound", "ccomp", "punct"], "triples": [{"uid": "938-0", "target_tags": "Now\\O ,\\O as\\O easy\\O as\\O it\\O is\\O to\\O use\\B ,\\O and\\O I\\O do\\O think\\O it\\O is\\O a\\O great\\O STARTER\\O laptop\\O .\\O", "opinion_tags": "Now\\O ,\\O as\\O easy\\B as\\O it\\O is\\O to\\O use\\O ,\\O and\\O I\\O do\\O think\\O it\\O is\\O a\\O great\\O STARTER\\O laptop\\O .\\O", "sentiment": "positive"}]}, {"id": "2666", "sentence": "The only problem is a lack of screen resolutions !", "postag": ["DT", "JJ", "NN", "VBZ", "DT", "NN", "IN", "NN", "NNS", "."], "head": [3, 3, 6, 6, 6, 0, 9, 9, 6, 6], "deprel": ["det", "amod", "nsubj", "cop", "det", "root", "case", "compound", "nmod", "punct"], "triples": [{"uid": "2666-0", "target_tags": "The\\O only\\O problem\\O is\\O a\\O lack\\O of\\O screen\\B resolutions\\I !\\O", "opinion_tags": "The\\O only\\O problem\\B is\\O a\\O lack\\B of\\O screen\\O resolutions\\O !\\O", "sentiment": "negative"}]}, {"id": "3020", "sentence": "It is everything I 'd hoped it would be from a look and feel standpoint , but somehow a bit more sturdy .", "postag": ["PRP", "VBZ", "NN", "PRP", "VBP", "VBN", "PRP", "MD", "VB", "IN", "DT", "NN", "CC", "VB", "NN", ",", "CC", "RB", "DT", "NN", "RBR", "JJ", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 6, 12, 12, 9, 14, 9, 14, 22, 22, 22, 20, 21, 22, 3, 3], "deprel": ["nsubj", "cop", "root", "nsubj", "aux", "acl:relcl", "nsubj", "aux", "ccomp", "case", "det", "obl", "cc", "conj", "obj", "punct", "cc", "advmod", "det", "obl:npmod", "advmod", "conj", "punct"], "triples": [{"uid": "3020-0", "target_tags": "It\\O is\\O everything\\O I\\O 'd\\O hoped\\O it\\O would\\O be\\O from\\O a\\O look\\B and\\I feel\\I standpoint\\I ,\\O but\\O somehow\\O a\\O bit\\O more\\O sturdy\\O .\\O", "opinion_tags": "It\\O is\\O everything\\O I\\O 'd\\O hoped\\O it\\O would\\O be\\O from\\O a\\O look\\O and\\O feel\\O standpoint\\O ,\\O but\\O somehow\\O a\\O bit\\O more\\O sturdy\\B .\\O", "sentiment": "positive"}]}, {"id": "2574", "sentence": "I play a lot of casual games online , and the touchpad is very responsive .", "postag": ["PRP", "VBP", "DT", "NN", "IN", "JJ", "NNS", "RB", ",", "CC", "DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 0, 4, 2, 7, 7, 4, 2, 15, 15, 12, 15, 15, 15, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "case", "amod", "nmod", "advmod", "punct", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "2574-0", "target_tags": "I\\O play\\O a\\O lot\\O of\\O casual\\O games\\O online\\O ,\\O and\\O the\\O touchpad\\B is\\O very\\O responsive\\O .\\O", "opinion_tags": "I\\O play\\O a\\O lot\\O of\\O casual\\O games\\O online\\O ,\\O and\\O the\\O touchpad\\O is\\O very\\O responsive\\B .\\O", "sentiment": "positive"}]}, {"id": "32", "sentence": "First , it does not have a push button to open the lid .", "postag": ["RB", ",", "PRP", "VBZ", "RB", "VB", "DT", "NN", "NN", "TO", "VB", "DT", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 11, 9, 13, 11, 6], "deprel": ["advmod", "punct", "nsubj", "aux", "advmod", "root", "det", "compound", "obj", "mark", "acl", "det", "obj", "punct"], "triples": [{"uid": "32-0", "target_tags": "First\\O ,\\O it\\O does\\O not\\O have\\O a\\O push\\B button\\I to\\O open\\O the\\O lid\\O .\\O", "opinion_tags": "First\\O ,\\O it\\O does\\B not\\I have\\O a\\O push\\O button\\O to\\O open\\O the\\O lid\\O .\\O", "sentiment": "negative"}]}, {"id": "1372", "sentence": "We have had numerous problems with Vista , such as Adobe Flash player just quits and has to be uninstalled and then reinsalled , Internet Explore just quits and you lose whatever you were working on , also , the same Windows update has appeared on this computer since we got it and has been updated probably 400 times , the same update .", "postag": ["PRP", "VBP", "VBN", "JJ", "NNS", "IN", "NNP", ",", "JJ", "IN", "NNP", "NNP", "NN", "RB", "VBZ", "CC", "VBZ", "TO", "VB", "VBN", "CC", "RB", "VBN", ",", "NNP", "NNP", "RB", "VBZ", "CC", "PRP", "VBP", "WP", "PRP", "VBD", "VBG", "IN", ",", "RB", ",", "DT", "JJ", "NNS", "NN", "VBZ", "VBN", "IN", "DT", "NN", "IN", "PRP", "VBD", "PRP", "CC", "VBZ", "VBN", "VBN", "RB", "CD", "NNS", ",", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 5, 3, 13, 9, 13, 13, 15, 15, 3, 17, 15, 20, 20, 17, 23, 23, 15, 28, 26, 28, 28, 3, 31, 31, 28, 35, 35, 35, 31, 35, 3, 45, 45, 43, 43, 43, 45, 45, 3, 48, 48, 45, 51, 51, 45, 51, 56, 56, 56, 51, 56, 59, 56, 63, 63, 63, 56, 3], "deprel": ["nsubj", "aux", "root", "amod", "obj", "case", "nmod", "punct", "case", "fixed", "compound", "compound", "nsubj", "advmod", "advcl", "cc", "conj", "mark", "aux:pass", "xcomp", "cc", "advmod", "conj", "punct", "compound", "nsubj", "advmod", "parataxis", "cc", "nsubj", "conj", "obl", "nsubj", "aux", "ccomp", "obl", "punct", "advmod", "punct", "det", "amod", "compound", "nsubj", "aux", "parataxis", "case", "det", "obl", "mark", "nsubj", "advcl", "obj", "cc", "aux", "aux:pass", "conj", "advmod", "nummod", "obl:tmod", "punct", "det", "amod", "parataxis", "punct"], "triples": [{"uid": "1372-0", "target_tags": "We\\O have\\O had\\O numerous\\O problems\\O with\\O Vista\\B ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "opinion_tags": "We\\O have\\O had\\O numerous\\O problems\\B with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "sentiment": "negative"}, {"uid": "1372-1", "target_tags": "We\\O have\\O had\\O numerous\\O problems\\O with\\O Vista\\O ,\\O such\\O as\\O Adobe\\B Flash\\I player\\I just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "opinion_tags": "We\\O have\\O had\\O numerous\\O problems\\B with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "sentiment": "negative"}, {"uid": "1372-2", "target_tags": "We\\O have\\O had\\O numerous\\O problems\\O with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\B Explore\\I just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "opinion_tags": "We\\O have\\O had\\O numerous\\O problems\\B with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "sentiment": "negative"}, {"uid": "1372-3", "target_tags": "We\\O have\\O had\\O numerous\\O problems\\O with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\B update\\I has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "opinion_tags": "We\\O have\\O had\\O numerous\\O problems\\B with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "sentiment": "negative"}, {"uid": "1372-4", "target_tags": "We\\O have\\O had\\O numerous\\O problems\\O with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\B has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "opinion_tags": "We\\O have\\O had\\O numerous\\O problems\\B with\\O Vista\\O ,\\O such\\O as\\O Adobe\\O Flash\\O player\\O just\\O quits\\O and\\O has\\O to\\O be\\O uninstalled\\O and\\O then\\O reinsalled\\O ,\\O Internet\\O Explore\\O just\\O quits\\O and\\O you\\O lose\\O whatever\\O you\\O were\\O working\\O on\\O ,\\O also\\O ,\\O the\\O same\\O Windows\\O update\\O has\\O appeared\\O on\\O this\\O computer\\O since\\O we\\O got\\O it\\O and\\O has\\O been\\O updated\\O probably\\O 400\\O times\\O ,\\O the\\O same\\O update\\O .\\O", "sentiment": "negative"}]}, {"id": "2424", "sentence": "It is much faster than my desktop which is a Core2 Quad running at 2.83 GHz .", "postag": ["PRP", "VBZ", "RB", "JJR", "IN", "PRP$", "NN", "WDT", "VBZ", "DT", "NNP", "NN", "VBG", "IN", "CD", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 12, 12, 12, 12, 7, 12, 16, 16, 13, 4], "deprel": ["nsubj", "cop", "advmod", "root", "case", "nmod:poss", "obl", "nsubj", "cop", "det", "compound", "acl:relcl", "acl", "case", "nummod", "obl", "punct"], "triples": [{"uid": "2424-0", "target_tags": "It\\O is\\O much\\O faster\\O than\\O my\\O desktop\\O which\\O is\\O a\\O Core2\\B Quad\\I running\\O at\\O 2.83\\O GHz\\O .\\O", "opinion_tags": "It\\O is\\O much\\O faster\\B than\\O my\\O desktop\\O which\\O is\\O a\\O Core2\\O Quad\\O running\\O at\\O 2.83\\O GHz\\O .\\O", "sentiment": "neutral"}]}, {"id": "2211", "sentence": "The graphics on this computer are also stellar - very clear and vivid .", "postag": ["DT", "NNS", "IN", "DT", "NN", "VBP", "RB", "JJ", ",", "RB", "JJ", "CC", "JJ", "."], "head": [2, 8, 5, 5, 2, 8, 8, 0, 11, 11, 8, 13, 11, 8], "deprel": ["det", "nsubj", "case", "det", "nmod", "cop", "advmod", "root", "punct", "advmod", "appos", "cc", "conj", "punct"], "triples": [{"uid": "2211-0", "target_tags": "The\\O graphics\\B on\\O this\\O computer\\O are\\O also\\O stellar\\O -\\O very\\O clear\\O and\\O vivid\\O .\\O", "opinion_tags": "The\\O graphics\\O on\\O this\\O computer\\O are\\O also\\O stellar\\B -\\O very\\O clear\\B and\\O vivid\\B .\\O", "sentiment": "positive"}]}, {"id": "1771", "sentence": "It is easy to navigate and update programs .", "postag": ["PRP", "VBZ", "JJ", "TO", "VB", "CC", "VB", "NNS", "."], "head": [3, 3, 0, 5, 3, 7, 5, 5, 3], "deprel": ["expl", "cop", "root", "mark", "csubj", "cc", "conj", "obj", "punct"], "triples": [{"uid": "1771-0", "target_tags": "It\\O is\\O easy\\O to\\O navigate\\O and\\O update\\B programs\\I .\\O", "opinion_tags": "It\\O is\\O easy\\B to\\O navigate\\O and\\O update\\O programs\\O .\\O", "sentiment": "neutral"}, {"uid": "1771-1", "target_tags": "It\\O is\\O easy\\O to\\O navigate\\B and\\O update\\O programs\\O .\\O", "opinion_tags": "It\\O is\\O easy\\B to\\O navigate\\O and\\O update\\O programs\\O .\\O", "sentiment": "positive"}]}, {"id": "1576", "sentence": "The display on this computer is the best I 've seen in a very long time , the battery life is very long and very convenient .", "postag": ["DT", "NN", "IN", "DT", "NN", "VBZ", "DT", "JJS", "PRP", "VBP", "VBN", "IN", "DT", "RB", "JJ", "NN", ",", "DT", "NN", "NN", "VBZ", "RB", "JJ", "CC", "RB", "JJ", "."], "head": [2, 8, 5, 5, 2, 8, 8, 0, 11, 11, 8, 16, 16, 15, 16, 11, 8, 20, 20, 23, 23, 23, 8, 26, 26, 23, 8], "deprel": ["det", "nsubj", "case", "det", "nmod", "cop", "det", "root", "nsubj", "aux", "acl:relcl", "case", "det", "advmod", "amod", "obl", "punct", "det", "compound", "nsubj", "cop", "advmod", "parataxis", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "1576-0", "target_tags": "The\\O display\\B on\\O this\\O computer\\O is\\O the\\O best\\O I\\O 've\\O seen\\O in\\O a\\O very\\O long\\O time\\O ,\\O the\\O battery\\O life\\O is\\O very\\O long\\O and\\O very\\O convenient\\O .\\O", "opinion_tags": "The\\O display\\O on\\O this\\O computer\\O is\\O the\\O best\\B I\\O 've\\O seen\\O in\\O a\\O very\\O long\\O time\\O ,\\O the\\O battery\\O life\\O is\\O very\\O long\\O and\\O very\\O convenient\\O .\\O", "sentiment": "positive"}, {"uid": "1576-1", "target_tags": "The\\O display\\O on\\O this\\O computer\\O is\\O the\\O best\\O I\\O 've\\O seen\\O in\\O a\\O very\\O long\\O time\\O ,\\O the\\O battery\\B life\\I is\\O very\\O long\\O and\\O very\\O convenient\\O .\\O", "opinion_tags": "The\\O display\\O on\\O this\\O computer\\O is\\O the\\O best\\O I\\O 've\\O seen\\O in\\O a\\O very\\O long\\O time\\O ,\\O the\\O battery\\O life\\O is\\O very\\O long\\B and\\O very\\O convenient\\B .\\O", "sentiment": "positive"}]}, {"id": "1768", "sentence": "Iphoto is great for adding pictures right to facebook and other social networking sites .", "postag": ["NNP", "VBZ", "JJ", "IN", "VBG", "NNS", "RB", "IN", "NN", "CC", "JJ", "JJ", "NN", "NNS", "."], "head": [3, 3, 0, 5, 3, 5, 9, 9, 5, 14, 14, 14, 14, 9, 3], "deprel": ["nsubj", "cop", "root", "mark", "advcl", "obj", "advmod", "case", "obl", "cc", "amod", "amod", "compound", "conj", "punct"], "triples": [{"uid": "1768-0", "target_tags": "Iphoto\\B is\\O great\\O for\\O adding\\O pictures\\O right\\O to\\O facebook\\O and\\O other\\O social\\O networking\\O sites\\O .\\O", "opinion_tags": "Iphoto\\O is\\O great\\B for\\O adding\\O pictures\\O right\\O to\\O facebook\\O and\\O other\\O social\\O networking\\O sites\\O .\\O", "sentiment": "positive"}]}, {"id": "1996", "sentence": "I am not sure if it was the drive itself , however ;", "postag": ["PRP", "VBP", "RB", "JJ", "IN", "PRP", "VBD", "DT", "NN", "PRP", ",", "RB", ":"], "head": [4, 4, 4, 0, 9, 9, 9, 9, 4, 9, 4, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "mark", "nsubj", "cop", "det", "advcl", "nmod:npmod", "punct", "advmod", "punct"], "triples": [{"uid": "1996-0", "target_tags": "I\\O am\\O not\\O sure\\O if\\O it\\O was\\O the\\O drive\\B itself\\O ,\\O however\\O ;\\O", "opinion_tags": "I\\O am\\O not\\B sure\\I if\\O it\\O was\\O the\\O drive\\O itself\\O ,\\O however\\O ;\\O", "sentiment": "neutral"}]}, {"id": "2654", "sentence": "Other than that its a great performing machine and well meets all my needs and more .", "postag": ["JJ", "IN", "DT", "PRP$", "DT", "JJ", "NN", "NN", "CC", "RB", "VBZ", "PDT", "PRP$", "NNS", "CC", "JJR", "."], "head": [8, 3, 1, 8, 8, 8, 8, 0, 11, 11, 8, 14, 14, 11, 16, 14, 8], "deprel": ["advmod", "case", "obl", "nmod:poss", "det", "amod", "compound", "root", "cc", "advmod", "conj", "det:predet", "nmod:poss", "obj", "cc", "conj", "punct"], "triples": [{"uid": "2654-0", "target_tags": "Other\\O than\\O that\\O its\\O a\\O great\\O performing\\B machine\\O and\\O well\\O meets\\O all\\O my\\O needs\\O and\\O more\\O .\\O", "opinion_tags": "Other\\O than\\O that\\O its\\O a\\O great\\B performing\\O machine\\O and\\O well\\O meets\\O all\\O my\\O needs\\O and\\O more\\O .\\O", "sentiment": "positive"}]}, {"id": "2961", "sentence": "My one complaint with this laptop is that I 've noticed an electronic fuzz sound coming out of the headphone jack when headphones are plugged in .", "postag": ["PRP$", "CD", "NN", "IN", "DT", "NN", "VBZ", "IN", "PRP", "VBP", "VBN", "DT", "JJ", "NN", "NN", "VBG", "IN", "IN", "DT", "NN", "NN", "WRB", "NNS", "VBP", "VBN", "RP", "."], "head": [3, 3, 7, 6, 6, 3, 0, 11, 11, 11, 7, 15, 15, 15, 11, 15, 21, 21, 21, 21, 16, 25, 25, 25, 16, 25, 7], "deprel": ["nmod:poss", "nummod", "nsubj", "case", "det", "nmod", "root", "mark", "nsubj", "aux", "ccomp", "det", "amod", "compound", "obj", "acl", "case", "case", "det", "compound", "obl", "mark", "nsubj:pass", "aux:pass", "advcl", "compound:prt", "punct"], "triples": [{"uid": "2961-0", "target_tags": "My\\O one\\O complaint\\O with\\O this\\O laptop\\O is\\O that\\O I\\O 've\\O noticed\\O an\\O electronic\\B fuzz\\I sound\\I coming\\O out\\O of\\O the\\O headphone\\O jack\\O when\\O headphones\\O are\\O plugged\\O in\\O .\\O", "opinion_tags": "My\\O one\\O complaint\\B with\\O this\\O laptop\\O is\\O that\\O I\\O 've\\O noticed\\O an\\O electronic\\O fuzz\\O sound\\O coming\\O out\\O of\\O the\\O headphone\\O jack\\O when\\O headphones\\O are\\O plugged\\O in\\O .\\O", "sentiment": "negative"}, {"uid": "2961-1", "target_tags": "My\\O one\\O complaint\\O with\\O this\\O laptop\\O is\\O that\\O I\\O 've\\O noticed\\O an\\O electronic\\O fuzz\\O sound\\O coming\\O out\\O of\\O the\\O headphone\\B jack\\I when\\O headphones\\O are\\O plugged\\O in\\O .\\O", "opinion_tags": "My\\O one\\O complaint\\B with\\O this\\O laptop\\O is\\O that\\O I\\O 've\\O noticed\\O an\\O electronic\\O fuzz\\O sound\\O coming\\O out\\O of\\O the\\O headphone\\O jack\\O when\\O headphones\\O are\\O plugged\\O in\\O .\\O", "sentiment": "negative"}]}, {"id": "1427", "sentence": "BEST BUY - 5 STARS + + + ( sales , service , respect for old men who are n't familiar with the technology ) DELL COMPUTERS - 3 stars DELL SUPPORT - owes a me a couple", "postag": ["JJS", "NN", ",", "CD", "NNS", "SYM", "SYM", "SYM", "-LRB-", "NNS", ",", "NN", ",", "NN", "IN", "JJ", "NNS", "WP", "VBP", "RB", "JJ", "IN", "DT", "NN", "-RRB-", "NNP", "NNS", ",", "CD", "NNS", "NNP", "NN", ",", "VBZ", "DT", "PRP", "DT", "NN"], "head": [2, 34, 2, 5, 2, 5, 5, 10, 10, 2, 12, 10, 14, 10, 17, 17, 14, 21, 21, 21, 17, 24, 24, 21, 10, 27, 2, 2, 30, 27, 32, 27, 2, 0, 36, 34, 38, 34], "deprel": ["amod", "nsubj", "punct", "nummod", "appos", "punct", "conj", "advmod", "punct", "parataxis", "punct", "conj", "punct", "conj", "case", "amod", "nmod", "nsubj", "cop", "advmod", "acl:relcl", "case", "det", "obl", "punct", "compound", "parataxis", "punct", "nummod", "appos", "compound", "appos", "punct", "root", "det", "obj", "det", "obj"], "triples": [{"uid": "1427-0", "target_tags": "BEST\\O BUY\\O -\\O 5\\O STARS\\O +\\O +\\O +\\O (\\O sales\\B ,\\O service\\O ,\\O respect\\O for\\O old\\O men\\O who\\O are\\O n't\\O familiar\\O with\\O the\\O technology\\O )\\O DELL\\O COMPUTERS\\O -\\O 3\\O stars\\O DELL\\O SUPPORT\\O -\\O owes\\O a\\O me\\O a\\O couple\\O", "opinion_tags": "BEST\\B BUY\\O -\\O 5\\O STARS\\O +\\O +\\O +\\O (\\O sales\\O ,\\O service\\O ,\\O respect\\O for\\O old\\O men\\O who\\O are\\O n't\\O familiar\\O with\\O the\\O technology\\O )\\O DELL\\O COMPUTERS\\O -\\O 3\\O stars\\O DELL\\O SUPPORT\\O -\\O owes\\O a\\O me\\O a\\O couple\\O", "sentiment": "positive"}, {"uid": "1427-1", "target_tags": "BEST\\O BUY\\O -\\O 5\\O STARS\\O +\\O +\\O +\\O (\\O sales\\O ,\\O service\\B ,\\O respect\\O for\\O old\\O men\\O who\\O are\\O n't\\O familiar\\O with\\O the\\O technology\\O )\\O DELL\\O COMPUTERS\\O -\\O 3\\O stars\\O DELL\\O SUPPORT\\O -\\O owes\\O a\\O me\\O a\\O couple\\O", "opinion_tags": "BEST\\B BUY\\O -\\O 5\\O STARS\\O +\\O +\\O +\\O (\\O sales\\O ,\\O service\\O ,\\O respect\\O for\\O old\\O men\\O who\\O are\\O n't\\O familiar\\O with\\O the\\O technology\\O )\\O DELL\\O COMPUTERS\\O -\\O 3\\O stars\\O DELL\\O SUPPORT\\O -\\O owes\\O a\\O me\\O a\\O couple\\O", "sentiment": "positive"}]}, {"id": "460", "sentence": "Then after 4 or so months the charger stopped working so I was forced to go out and buy new hardware just to keep this computer running .", "postag": ["RB", "IN", "CD", "CC", "RB", "NNS", "DT", "NN", "VBD", "VBG", "RB", "PRP", "VBD", "VBN", "TO", "VB", "RB", "CC", "VB", "JJ", "NN", "RB", "TO", "VB", "DT", "NN", "VBG", "."], "head": [9, 6, 6, 5, 3, 9, 8, 9, 0, 9, 14, 14, 14, 9, 16, 14, 16, 19, 16, 21, 19, 24, 24, 19, 26, 24, 24, 9], "deprel": ["advmod", "case", "nummod", "cc", "conj", "obl", "det", "nsubj", "root", "xcomp", "advmod", "nsubj:pass", "aux:pass", "conj", "mark", "xcomp", "advmod", "cc", "conj", "amod", "obj", "advmod", "mark", "advcl", "det", "obj", "xcomp", "punct"], "triples": [{"uid": "460-0", "target_tags": "Then\\O after\\O 4\\O or\\O so\\O months\\O the\\O charger\\B stopped\\O working\\O so\\O I\\O was\\O forced\\O to\\O go\\O out\\O and\\O buy\\O new\\O hardware\\O just\\O to\\O keep\\O this\\O computer\\O running\\O .\\O", "opinion_tags": "Then\\O after\\O 4\\O or\\O so\\O months\\O the\\O charger\\O stopped\\B working\\I so\\O I\\O was\\O forced\\O to\\O go\\O out\\O and\\O buy\\O new\\O hardware\\O just\\O to\\O keep\\O this\\O computer\\O running\\O .\\O", "sentiment": "negative"}, {"uid": "460-1", "target_tags": "Then\\O after\\O 4\\O or\\O so\\O months\\O the\\O charger\\B stopped\\O working\\O so\\O I\\O was\\O forced\\O to\\O go\\O out\\O and\\O buy\\O new\\O hardware\\O just\\O to\\O keep\\O this\\O computer\\O running\\O .\\O", "opinion_tags": "Then\\O after\\O 4\\O or\\O so\\O months\\O the\\O charger\\O stopped\\B working\\I so\\O I\\O was\\O forced\\O to\\O go\\O out\\O and\\O buy\\O new\\O hardware\\O just\\O to\\O keep\\O this\\O computer\\O running\\O .\\O", "sentiment": "negative"}, {"uid": "460-2", "target_tags": "Then\\O after\\O 4\\O or\\O so\\O months\\O the\\O charger\\O stopped\\O working\\O so\\O I\\O was\\O forced\\O to\\O go\\O out\\O and\\O buy\\O new\\O hardware\\B just\\O to\\O keep\\O this\\O computer\\O running\\O .\\O", "opinion_tags": "Then\\O after\\O 4\\O or\\O so\\O months\\O the\\O charger\\O stopped\\O working\\O so\\O I\\O was\\O forced\\O to\\O go\\O out\\O and\\O buy\\O new\\B hardware\\O just\\O to\\O keep\\O this\\O computer\\O running\\O .\\O", "sentiment": "negative"}]}, {"id": "2393", "sentence": "I love the size , keyboard , the functions .", "postag": ["PRP", "VBP", "DT", "NN", ",", "NN", ",", "DT", "NNS", "."], "head": [2, 0, 4, 2, 6, 4, 9, 9, 4, 2], "deprel": ["nsubj", "root", "det", "obj", "punct", "conj", "punct", "det", "conj", "punct"], "triples": [{"uid": "2393-0", "target_tags": "I\\O love\\O the\\O size\\B ,\\O keyboard\\O ,\\O the\\O functions\\O .\\O", "opinion_tags": "I\\O love\\B the\\O size\\O ,\\O keyboard\\O ,\\O the\\O functions\\O .\\O", "sentiment": "positive"}, {"uid": "2393-1", "target_tags": "I\\O love\\O the\\O size\\O ,\\O keyboard\\B ,\\O the\\O functions\\O .\\O", "opinion_tags": "I\\O love\\B the\\O size\\O ,\\O keyboard\\O ,\\O the\\O functions\\O .\\O", "sentiment": "positive"}, {"uid": "2393-2", "target_tags": "I\\O love\\O the\\O size\\O ,\\O keyboard\\O ,\\O the\\O functions\\B .\\O", "opinion_tags": "I\\O love\\B the\\O size\\O ,\\O keyboard\\O ,\\O the\\O functions\\O .\\O", "sentiment": "positive"}]}, {"id": "1053", "sentence": "Adjust the sensitivity since it 's not that responsive to begin with .", "postag": ["VB", "DT", "NN", "IN", "PRP", "VBZ", "RB", "RB", "JJ", "TO", "VB", "IN", "."], "head": [0, 3, 1, 9, 9, 9, 9, 9, 1, 11, 9, 11, 1], "deprel": ["root", "det", "obj", "mark", "nsubj", "cop", "advmod", "advmod", "advcl", "mark", "ccomp", "obl", "punct"], "triples": [{"uid": "1053-0", "target_tags": "Adjust\\O the\\O sensitivity\\B since\\O it\\O 's\\O not\\O that\\O responsive\\O to\\O begin\\O with\\O .\\O", "opinion_tags": "Adjust\\O the\\O sensitivity\\O since\\O it\\O 's\\O not\\B that\\I responsive\\I to\\O begin\\O with\\O .\\O", "sentiment": "negative"}]}, {"id": "2142", "sentence": "everything about a mac is wonderful , it takes a little used to learning and getting used to the new system , but you will learn fast and its all worth it .", "postag": ["NN", "IN", "DT", "NN", "VBZ", "JJ", ",", "PRP", "VBZ", "DT", "JJ", "VBN", "IN", "VBG", "CC", "VBG", "VBN", "IN", "DT", "JJ", "NN", ",", "CC", "PRP", "MD", "VB", "JJ", "CC", "PRP$", "DT", "JJ", "PRP", "."], "head": [6, 4, 4, 1, 6, 0, 9, 9, 6, 11, 12, 9, 14, 12, 16, 17, 14, 21, 21, 21, 17, 26, 26, 26, 26, 6, 26, 31, 31, 31, 26, 31, 6], "deprel": ["nsubj", "case", "det", "nmod", "cop", "root", "punct", "nsubj", "parataxis", "det", "obl:npmod", "xcomp", "mark", "advcl", "cc", "aux:pass", "conj", "case", "det", "amod", "obl", "punct", "cc", "nsubj", "aux", "conj", "xcomp", "cc", "nmod:poss", "nsubj", "conj", "obj", "punct"], "triples": [{"uid": "2142-0", "target_tags": "everything\\O about\\O a\\O mac\\O is\\O wonderful\\O ,\\O it\\O takes\\O a\\O little\\O used\\O to\\O learning\\O and\\O getting\\O used\\O to\\O the\\O new\\O system\\B ,\\O but\\O you\\O will\\O learn\\O fast\\O and\\O its\\O all\\O worth\\O it\\O .\\O", "opinion_tags": "everything\\O about\\O a\\O mac\\O is\\O wonderful\\O ,\\O it\\O takes\\O a\\O little\\O used\\O to\\O learning\\O and\\O getting\\O used\\O to\\O the\\O new\\B system\\O ,\\O but\\O you\\O will\\O learn\\B fast\\I and\\O its\\O all\\O worth\\B it\\O .\\O", "sentiment": "neutral"}]}, {"id": "2933", "sentence": "The graphics are great .", "postag": ["DT", "NNS", "VBP", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "2933-0", "target_tags": "The\\O graphics\\B are\\O great\\O .\\O", "opinion_tags": "The\\O graphics\\O are\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "969", "sentence": "The biggest problem is that the box had no instructions in it .", "postag": ["DT", "JJS", "NN", "VBZ", "IN", "DT", "NN", "VBD", "DT", "NNS", "IN", "PRP", "."], "head": [3, 3, 4, 0, 8, 7, 8, 4, 10, 8, 12, 8, 4], "deprel": ["det", "amod", "nsubj", "root", "mark", "det", "nsubj", "ccomp", "det", "obj", "case", "obl", "punct"], "triples": [{"uid": "969-0", "target_tags": "The\\O biggest\\O problem\\O is\\O that\\O the\\O box\\O had\\O no\\O instructions\\B in\\O it\\O .\\O", "opinion_tags": "The\\O biggest\\O problem\\B is\\O that\\O the\\O box\\O had\\O no\\B instructions\\O in\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "2588", "sentence": "Its Office compatible , but the features and its functioning is all new again so you might as well save the money and just learn the pre installed mac programs .", "postag": ["PRP$", "NN", "JJ", ",", "CC", "DT", "NNS", "CC", "PRP$", "NN", "VBZ", "RB", "JJ", "RB", "RB", "PRP", "MD", "RB", "RB", "VB", "DT", "NN", "CC", "RB", "VB", "DT", "NN", "VBN", "NN", "NNS", "."], "head": [2, 3, 0, 13, 13, 7, 13, 10, 10, 7, 13, 13, 3, 13, 20, 20, 20, 20, 18, 13, 22, 20, 25, 25, 20, 30, 28, 30, 30, 25, 3], "deprel": ["nmod:poss", "nsubj", "root", "punct", "cc", "det", "nsubj", "cc", "nmod:poss", "conj", "cop", "advmod", "conj", "advmod", "advmod", "nsubj", "aux", "advmod", "fixed", "advcl", "det", "obj", "cc", "advmod", "conj", "det", "compound", "amod", "compound", "obj", "punct"], "triples": [{"uid": "2588-0", "target_tags": "Its\\O Office\\O compatible\\O ,\\O but\\O the\\O features\\B and\\O its\\O functioning\\O is\\O all\\O new\\O again\\O so\\O you\\O might\\O as\\O well\\O save\\O the\\O money\\O and\\O just\\O learn\\O the\\O pre\\O installed\\O mac\\O programs\\O .\\O", "opinion_tags": "Its\\O Office\\O compatible\\O ,\\O but\\O the\\O features\\O and\\O its\\O functioning\\O is\\O all\\O new\\B again\\O so\\O you\\O might\\O as\\O well\\O save\\O the\\O money\\O and\\O just\\O learn\\O the\\O pre\\O installed\\O mac\\O programs\\O .\\O", "sentiment": "neutral"}, {"uid": "2588-1", "target_tags": "Its\\O Office\\O compatible\\O ,\\O but\\O the\\O features\\O and\\O its\\O functioning\\B is\\O all\\O new\\O again\\O so\\O you\\O might\\O as\\O well\\O save\\O the\\O money\\O and\\O just\\O learn\\O the\\O pre\\O installed\\O mac\\O programs\\O .\\O", "opinion_tags": "Its\\O Office\\O compatible\\O ,\\O but\\O the\\O features\\O and\\O its\\O functioning\\O is\\O all\\O new\\B again\\O so\\O you\\O might\\O as\\O well\\O save\\O the\\O money\\O and\\O just\\O learn\\O the\\O pre\\O installed\\O mac\\O programs\\O .\\O", "sentiment": "neutral"}, {"uid": "2588-2", "target_tags": "Its\\O Office\\B compatible\\O ,\\O but\\O the\\O features\\O and\\O its\\O functioning\\O is\\O all\\O new\\O again\\O so\\O you\\O might\\O as\\O well\\O save\\O the\\O money\\O and\\O just\\O learn\\O the\\O pre\\O installed\\O mac\\O programs\\O .\\O", "opinion_tags": "Its\\O Office\\O compatible\\B ,\\O but\\O the\\O features\\O and\\O its\\O functioning\\O is\\O all\\O new\\O again\\O so\\O you\\O might\\O as\\O well\\O save\\O the\\O money\\O and\\O just\\O learn\\O the\\O pre\\O installed\\O mac\\O programs\\O .\\O", "sentiment": "positive"}]}, {"id": "597", "sentence": "The iLife software that comes with the computer is so simple to use and produces a great finished product .", "postag": ["DT", "NNP", "NN", "WDT", "VBZ", "IN", "DT", "NN", "VBZ", "RB", "JJ", "TO", "VB", "CC", "VBZ", "DT", "JJ", "VBN", "NN", "."], "head": [3, 3, 11, 5, 3, 8, 8, 5, 11, 11, 0, 13, 11, 15, 13, 19, 19, 19, 15, 11], "deprel": ["det", "compound", "nsubj", "nsubj", "acl:relcl", "case", "det", "obl", "cop", "advmod", "root", "mark", "xcomp", "cc", "conj", "det", "amod", "amod", "obj", "punct"], "triples": [{"uid": "597-0", "target_tags": "The\\O iLife\\B software\\I that\\O comes\\O with\\O the\\O computer\\O is\\O so\\O simple\\O to\\O use\\O and\\O produces\\O a\\O great\\O finished\\O product\\O .\\O", "opinion_tags": "The\\O iLife\\O software\\O that\\O comes\\O with\\O the\\O computer\\O is\\O so\\O simple\\B to\\O use\\O and\\O produces\\O a\\O great\\B finished\\O product\\O .\\O", "sentiment": "positive"}, {"uid": "597-1", "target_tags": "The\\O iLife\\O software\\O that\\O comes\\O with\\O the\\O computer\\O is\\O so\\O simple\\O to\\O use\\B and\\O produces\\O a\\O great\\O finished\\O product\\O .\\O", "opinion_tags": "The\\O iLife\\O software\\O that\\O comes\\O with\\O the\\O computer\\O is\\O so\\O simple\\B to\\O use\\O and\\O produces\\O a\\O great\\O finished\\O product\\O .\\O", "sentiment": "positive"}]}, {"id": "1190", "sentence": "Maybe this is virus related , maybe not , but the computer has locked up many times , and on two occasions , the screen has simply gone black .", "postag": ["RB", "DT", "VBZ", "NN", "JJ", ",", "RB", "RB", ",", "CC", "DT", "NN", "VBZ", "VBN", "RP", "JJ", "NNS", ",", "CC", "IN", "CD", "NNS", ",", "DT", "NN", "VBZ", "RB", "VBN", "JJ", "."], "head": [5, 5, 5, 5, 0, 5, 8, 5, 14, 14, 12, 14, 14, 5, 14, 17, 14, 28, 28, 22, 22, 28, 28, 25, 28, 28, 28, 5, 28, 5], "deprel": ["advmod", "nsubj", "cop", "obl:npmod", "root", "punct", "advmod", "advmod", "punct", "cc", "det", "nsubj", "aux", "conj", "compound:prt", "amod", "obl:tmod", "punct", "cc", "case", "nummod", "obl", "punct", "det", "nsubj", "aux", "advmod", "conj", "xcomp", "punct"], "triples": [{"uid": "1190-0", "target_tags": "Maybe\\O this\\O is\\O virus\\O related\\O ,\\O maybe\\O not\\O ,\\O but\\O the\\O computer\\O has\\O locked\\O up\\O many\\O times\\O ,\\O and\\O on\\O two\\O occasions\\O ,\\O the\\O screen\\B has\\O simply\\O gone\\O black\\O .\\O", "opinion_tags": "Maybe\\O this\\O is\\O virus\\O related\\O ,\\O maybe\\O not\\O ,\\O but\\O the\\O computer\\O has\\O locked\\O up\\O many\\O times\\O ,\\O and\\O on\\O two\\O occasions\\O ,\\O the\\O screen\\O has\\O simply\\O gone\\O black\\B .\\O", "sentiment": "negative"}]}, {"id": "2339", "sentence": "I charge it at night and skip taking the cord with me because of the good battery life .", "postag": ["PRP", "VBP", "PRP", "IN", "NN", "CC", "VBP", "VBG", "DT", "NN", "IN", "PRP", "IN", "IN", "DT", "JJ", "NN", "NN", "."], "head": [2, 0, 2, 5, 2, 7, 2, 7, 10, 8, 12, 8, 18, 13, 18, 18, 18, 8, 2], "deprel": ["nsubj", "root", "obj", "case", "obl", "cc", "conj", "xcomp", "det", "obj", "case", "obl", "case", "fixed", "det", "amod", "compound", "obl", "punct"], "triples": [{"uid": "2339-0", "target_tags": "I\\O charge\\O it\\O at\\O night\\O and\\O skip\\O taking\\O the\\O cord\\O with\\O me\\O because\\O of\\O the\\O good\\O battery\\B life\\I .\\O", "opinion_tags": "I\\O charge\\O it\\O at\\O night\\O and\\O skip\\O taking\\O the\\O cord\\O with\\O me\\O because\\O of\\O the\\O good\\B battery\\O life\\O .\\O", "sentiment": "positive"}]}, {"id": "1256", "sentence": "Wasted me at least 8 hours of installation time .", "postag": ["VBD", "PRP", "RB", "RBS", "CD", "NNS", "IN", "NN", "NN", "."], "head": [0, 1, 4, 5, 6, 1, 9, 9, 6, 1], "deprel": ["root", "obj", "case", "nmod", "nummod", "obj", "case", "compound", "nmod", "punct"], "triples": [{"uid": "1256-0", "target_tags": "Wasted\\O me\\O at\\O least\\O 8\\O hours\\O of\\O installation\\B time\\I .\\O", "opinion_tags": "Wasted\\B me\\O at\\O least\\O 8\\O hours\\O of\\O installation\\O time\\O .\\O", "sentiment": "negative"}]}, {"id": "1645", "sentence": "See when it comes to laptops you buy it and get just a normal operating system with trials of must need stuff that should come with it .", "postag": ["VB", "WRB", "PRP", "VBZ", "IN", "NNS", "PRP", "VBP", "PRP", "CC", "VBP", "RB", "DT", "JJ", "NN", "NN", "IN", "NNS", "IN", "MD", "VB", "NN", "WDT", "MD", "VB", "IN", "PRP", "."], "head": [0, 4, 4, 1, 6, 4, 8, 6, 8, 11, 4, 16, 16, 16, 16, 11, 18, 16, 21, 21, 18, 21, 25, 25, 22, 27, 25, 1], "deprel": ["root", "mark", "nsubj", "advcl", "case", "obl", "nsubj", "acl:relcl", "obj", "cc", "conj", "advmod", "det", "amod", "compound", "obj", "case", "nmod", "mark", "aux", "acl", "obj", "nsubj", "aux", "acl:relcl", "case", "obl", "punct"], "triples": [{"uid": "1645-0", "target_tags": "See\\O when\\O it\\O comes\\O to\\O laptops\\O you\\O buy\\O it\\O and\\O get\\O just\\O a\\O normal\\O operating\\B system\\I with\\O trials\\O of\\O must\\O need\\O stuff\\O that\\O should\\O come\\O with\\O it\\O .\\O", "opinion_tags": "See\\O when\\O it\\O comes\\O to\\O laptops\\O you\\O buy\\O it\\O and\\O get\\O just\\O a\\O normal\\B operating\\O system\\O with\\O trials\\O of\\O must\\O need\\O stuff\\O that\\O should\\O come\\O with\\O it\\O .\\O", "sentiment": "neutral"}]}, {"id": "14", "sentence": "Not too much `` junk '' software to remove .", "postag": ["RB", "RB", "JJ", "``", "NN", "''", "NN", "TO", "VB", "."], "head": [3, 3, 7, 7, 7, 7, 0, 9, 7, 7], "deprel": ["advmod", "advmod", "amod", "punct", "compound", "punct", "root", "mark", "acl", "punct"], "triples": [{"uid": "14-0", "target_tags": "Not\\O too\\O much\\O ``\\O junk\\O ''\\O software\\B to\\O remove\\O .\\O", "opinion_tags": "Not\\O too\\O much\\O ``\\O junk\\B ''\\O software\\O to\\O remove\\O .\\O", "sentiment": "positive"}]}, {"id": "2435", "sentence": "I was disappointed when I realized that the keyboard does n't light up on this model .", "postag": ["PRP", "VBD", "JJ", "WRB", "PRP", "VBD", "IN", "DT", "NN", "VBZ", "RB", "VB", "RP", "IN", "DT", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 12, 9, 12, 12, 12, 6, 12, 16, 16, 12, 3], "deprel": ["nsubj", "cop", "root", "mark", "nsubj", "advcl", "mark", "det", "nsubj", "aux", "advmod", "ccomp", "compound:prt", "case", "det", "obl", "punct"], "triples": [{"uid": "2435-0", "target_tags": "I\\O was\\O disappointed\\O when\\O I\\O realized\\O that\\O the\\O keyboard\\B does\\O n't\\O light\\O up\\O on\\O this\\O model\\O .\\O", "opinion_tags": "I\\O was\\O disappointed\\B when\\O I\\O realized\\O that\\O the\\O keyboard\\O does\\B n't\\I light\\I up\\I on\\O this\\O model\\O .\\O", "sentiment": "negative"}]}, {"id": "2447", "sentence": "Its fast , has High definition quality in the videos .", "postag": ["PRP$", "JJ", ",", "VBZ", "JJ", "NN", "NN", "IN", "DT", "NNS", "."], "head": [2, 0, 4, 2, 6, 7, 4, 10, 10, 7, 2], "deprel": ["nmod:poss", "root", "punct", "parataxis", "amod", "compound", "obj", "case", "det", "nmod", "punct"], "triples": [{"uid": "2447-0", "target_tags": "Its\\O fast\\O ,\\O has\\O High\\B definition\\I quality\\I in\\O the\\O videos\\O .\\O", "opinion_tags": "Its\\O fast\\O ,\\O has\\O High\\B definition\\O quality\\O in\\O the\\O videos\\O .\\O", "sentiment": "positive"}]}, {"id": "2071", "sentence": "So I called customer support ( which is good too ) and they went through it and it is just a safety feature and it does not affect performance at all , I just chose to hide the message .", "postag": ["RB", "PRP", "VBD", "NN", "NN", "-LRB-", "WDT", "VBZ", "JJ", "RB", "-RRB-", "CC", "PRP", "VBD", "IN", "PRP", "CC", "PRP", "VBZ", "RB", "DT", "NN", "NN", "CC", "PRP", "VBZ", "RB", "VB", "NN", "IN", "DT", ",", "PRP", "RB", "VBD", "TO", "VB", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 9, 9, 9, 5, 9, 9, 14, 14, 3, 16, 14, 23, 23, 23, 23, 23, 23, 14, 28, 28, 28, 28, 23, 28, 31, 28, 3, 35, 35, 3, 37, 35, 39, 37, 3], "deprel": ["advmod", "nsubj", "root", "compound", "obj", "punct", "nsubj", "cop", "acl:relcl", "advmod", "punct", "cc", "nsubj", "conj", "case", "obl", "cc", "nsubj", "cop", "advmod", "det", "compound", "conj", "cc", "nsubj", "aux", "advmod", "conj", "obj", "case", "obl", "punct", "nsubj", "advmod", "parataxis", "mark", "xcomp", "det", "obj", "punct"], "triples": [{"uid": "2071-0", "target_tags": "So\\O I\\O called\\O customer\\B support\\I (\\O which\\O is\\O good\\O too\\O )\\O and\\O they\\O went\\O through\\O it\\O and\\O it\\O is\\O just\\O a\\O safety\\O feature\\O and\\O it\\O does\\O not\\O affect\\O performance\\O at\\O all\\O ,\\O I\\O just\\O chose\\O to\\O hide\\O the\\O message\\O .\\O", "opinion_tags": "So\\O I\\O called\\O customer\\O support\\O (\\O which\\O is\\O good\\B too\\O )\\O and\\O they\\O went\\O through\\O it\\O and\\O it\\O is\\O just\\O a\\O safety\\O feature\\O and\\O it\\O does\\O not\\O affect\\O performance\\O at\\O all\\O ,\\O I\\O just\\O chose\\O to\\O hide\\O the\\O message\\O .\\O", "sentiment": "positive"}, {"uid": "2071-1", "target_tags": "So\\O I\\O called\\O customer\\O support\\O (\\O which\\O is\\O good\\O too\\O )\\O and\\O they\\O went\\O through\\O it\\O and\\O it\\O is\\O just\\O a\\O safety\\O feature\\O and\\O it\\O does\\O not\\O affect\\O performance\\B at\\O all\\O ,\\O I\\O just\\O chose\\O to\\O hide\\O the\\O message\\O .\\O", "opinion_tags": "So\\O I\\O called\\O customer\\O support\\O (\\O which\\O is\\O good\\O too\\O )\\O and\\O they\\O went\\O through\\O it\\O and\\O it\\O is\\O just\\O a\\O safety\\O feature\\O and\\O it\\O does\\O not\\B affect\\I performance\\O at\\O all\\O ,\\O I\\O just\\O chose\\O to\\O hide\\O the\\O message\\O .\\O", "sentiment": "neutral"}]}, {"id": "2117", "sentence": "When you look at the specs on Apple products in comparison to a Dell or a HP , yes they do seem to offer less for a higher cost .", "postag": ["WRB", "PRP", "VBP", "IN", "DT", "NNS", "IN", "NNP", "NNS", "IN", "NN", "IN", "DT", "NNP", "CC", "DT", "NNP", ",", "UH", "PRP", "VBP", "VB", "TO", "VB", "JJR", "IN", "DT", "JJR", "NN", "."], "head": [3, 3, 22, 6, 6, 3, 9, 9, 6, 11, 3, 14, 14, 11, 17, 17, 14, 22, 22, 22, 22, 0, 24, 22, 24, 29, 29, 29, 24, 22], "deprel": ["mark", "nsubj", "advcl", "case", "det", "obl", "case", "compound", "nmod", "case", "obl", "case", "det", "nmod", "cc", "det", "conj", "punct", "discourse", "nsubj", "aux", "root", "mark", "xcomp", "obj", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "2117-0", "target_tags": "When\\O you\\O look\\O at\\O the\\O specs\\O on\\O Apple\\O products\\O in\\O comparison\\O to\\O a\\O Dell\\O or\\O a\\O HP\\O ,\\O yes\\O they\\O do\\O seem\\O to\\O offer\\O less\\O for\\O a\\O higher\\O cost\\B .\\O", "opinion_tags": "When\\O you\\O look\\O at\\O the\\O specs\\O on\\O Apple\\O products\\O in\\O comparison\\O to\\O a\\O Dell\\O or\\O a\\O HP\\O ,\\O yes\\O they\\O do\\O seem\\O to\\O offer\\O less\\O for\\O a\\O higher\\B cost\\O .\\O", "sentiment": "negative"}]}, {"id": "2500", "sentence": "I was taught to use Photoshop and was amazed .", "postag": ["PRP", "VBD", "VBN", "TO", "VB", "NNP", "CC", "VBD", "VBN", "."], "head": [3, 3, 0, 5, 3, 5, 9, 9, 3, 3], "deprel": ["nsubj:pass", "aux:pass", "root", "mark", "xcomp", "obj", "cc", "aux:pass", "conj", "punct"], "triples": [{"uid": "2500-0", "target_tags": "I\\O was\\O taught\\O to\\O use\\O Photoshop\\B and\\O was\\O amazed\\O .\\O", "opinion_tags": "I\\O was\\O taught\\O to\\O use\\O Photoshop\\O and\\O was\\O amazed\\B .\\O", "sentiment": "positive"}]}, {"id": "400", "sentence": "The quality , engineering design and warranty are superior -- covers damage from dropping the laptop .", "postag": ["DT", "NN", ",", "NN", "NN", "CC", "NN", "VBP", "JJ", ",", "VBZ", "NN", "IN", "VBG", "DT", "NN", "."], "head": [2, 9, 5, 5, 2, 7, 2, 9, 0, 9, 9, 11, 14, 11, 16, 14, 9], "deprel": ["det", "nsubj", "punct", "compound", "conj", "cc", "conj", "cop", "root", "punct", "parataxis", "obj", "mark", "advcl", "det", "obj", "punct"], "triples": [{"uid": "400-0", "target_tags": "The\\O quality\\B ,\\O engineering\\O design\\O and\\O warranty\\O are\\O superior\\O --\\O covers\\O damage\\O from\\O dropping\\O the\\O laptop\\O .\\O", "opinion_tags": "The\\O quality\\O ,\\O engineering\\O design\\O and\\O warranty\\O are\\O superior\\B --\\O covers\\O damage\\O from\\O dropping\\O the\\O laptop\\O .\\O", "sentiment": "positive"}, {"uid": "400-1", "target_tags": "The\\O quality\\O ,\\O engineering\\B design\\I and\\O warranty\\O are\\O superior\\O --\\O covers\\O damage\\O from\\O dropping\\O the\\O laptop\\O .\\O", "opinion_tags": "The\\O quality\\O ,\\O engineering\\O design\\O and\\O warranty\\O are\\O superior\\B --\\O covers\\O damage\\O from\\O dropping\\O the\\O laptop\\O .\\O", "sentiment": "positive"}, {"uid": "400-2", "target_tags": "The\\O quality\\O ,\\O engineering\\O design\\O and\\O warranty\\B are\\O superior\\O --\\O covers\\O damage\\O from\\O dropping\\O the\\O laptop\\O .\\O", "opinion_tags": "The\\O quality\\O ,\\O engineering\\O design\\O and\\O warranty\\O are\\O superior\\B --\\O covers\\O damage\\O from\\O dropping\\O the\\O laptop\\O .\\O", "sentiment": "positive"}]}, {"id": "555", "sentence": "It 's perfect for everything and runs faster than an average pc !", "postag": ["PRP", "VBZ", "JJ", "IN", "NN", "CC", "VBZ", "JJR", "IN", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 3, 7, 12, 12, 12, 8, 3], "deprel": ["nsubj", "cop", "root", "case", "obl", "cc", "conj", "advmod", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "555-0", "target_tags": "It\\O 's\\O perfect\\O for\\O everything\\O and\\O runs\\B faster\\O than\\O an\\O average\\O pc\\O !\\O", "opinion_tags": "It\\O 's\\O perfect\\O for\\O everything\\O and\\O runs\\O faster\\B than\\O an\\O average\\O pc\\O !\\O", "sentiment": "positive"}]}, {"id": "1921", "sentence": "Programs would crash all the time , and it turned out to be a very unstable , unreliable laptop for me .", "postag": ["NNS", "MD", "VB", "PDT", "DT", "NN", ",", "CC", "PRP", "VBD", "RP", "TO", "VB", "DT", "RB", "JJ", ",", "JJ", "NN", "IN", "PRP", "."], "head": [3, 3, 0, 6, 6, 3, 10, 10, 10, 3, 10, 19, 19, 19, 16, 19, 19, 19, 10, 21, 19, 3], "deprel": ["nsubj", "aux", "root", "det:predet", "det", "obl:tmod", "punct", "cc", "nsubj", "conj", "compound:prt", "mark", "cop", "det", "advmod", "amod", "punct", "amod", "xcomp", "case", "nmod", "punct"], "triples": [{"uid": "1921-0", "target_tags": "Programs\\B would\\O crash\\O all\\O the\\O time\\O ,\\O and\\O it\\O turned\\O out\\O to\\O be\\O a\\O very\\O unstable\\O ,\\O unreliable\\O laptop\\O for\\O me\\O .\\O", "opinion_tags": "Programs\\O would\\O crash\\B all\\O the\\O time\\O ,\\O and\\O it\\O turned\\O out\\O to\\O be\\O a\\O very\\O unstable\\O ,\\O unreliable\\O laptop\\O for\\O me\\O .\\O", "sentiment": "negative"}]}, {"id": "1574", "sentence": "I love the operating system and the preloaded software .", "postag": ["PRP", "VBP", "DT", "NN", "NN", "CC", "DT", "VBN", "NN", "."], "head": [2, 0, 5, 5, 2, 9, 9, 9, 5, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "cc", "det", "amod", "conj", "punct"], "triples": [{"uid": "1574-0", "target_tags": "I\\O love\\O the\\O operating\\B system\\I and\\O the\\O preloaded\\O software\\O .\\O", "opinion_tags": "I\\O love\\B the\\O operating\\O system\\O and\\O the\\O preloaded\\O software\\O .\\O", "sentiment": "positive"}, {"uid": "1574-1", "target_tags": "I\\O love\\O the\\O operating\\O system\\O and\\O the\\O preloaded\\B software\\I .\\O", "opinion_tags": "I\\O love\\B the\\O operating\\O system\\O and\\O the\\O preloaded\\O software\\O .\\O", "sentiment": "positive"}]}, {"id": "500", "sentence": "We paid for the three year warranty and the extended warranty after that one ended as well .", "postag": ["PRP", "VBD", "IN", "DT", "CD", "NN", "NN", "CC", "DT", "VBN", "NN", "IN", "DT", "NN", "VBD", "RB", "RB", "."], "head": [2, 0, 7, 7, 6, 7, 2, 15, 11, 11, 15, 14, 14, 11, 2, 15, 16, 2], "deprel": ["nsubj", "root", "case", "det", "nummod", "compound", "obl", "cc", "det", "amod", "nsubj", "case", "det", "nmod", "conj", "advmod", "fixed", "punct"], "triples": [{"uid": "500-0", "target_tags": "We\\O paid\\O for\\O the\\O three\\B year\\I warranty\\I and\\O the\\O extended\\O warranty\\O after\\O that\\O one\\O ended\\O as\\O well\\O .\\O", "opinion_tags": "We\\O paid\\B for\\I the\\O three\\O year\\O warranty\\O and\\O the\\O extended\\O warranty\\O after\\O that\\O one\\O ended\\O as\\O well\\O .\\O", "sentiment": "neutral"}, {"uid": "500-1", "target_tags": "We\\O paid\\O for\\O the\\O three\\O year\\O warranty\\O and\\O the\\O extended\\B warranty\\I after\\O that\\O one\\O ended\\O as\\O well\\O .\\O", "opinion_tags": "We\\O paid\\B for\\I the\\O three\\O year\\O warranty\\O and\\O the\\O extended\\O warranty\\O after\\O that\\O one\\O ended\\O as\\O well\\O .\\O", "sentiment": "neutral"}]}, {"id": "2276", "sentence": "I connect a LaCie 2Big external drive via the firewire 800 interface , which is useful for Time Machine .", "postag": ["PRP", "VBP", "DT", "NNP", "NN", "JJ", "NN", "IN", "DT", "NN", "CD", "NN", ",", "WDT", "VBZ", "JJ", "IN", "NNP", "NNP", "."], "head": [2, 0, 7, 7, 7, 7, 2, 12, 12, 12, 10, 2, 12, 16, 16, 12, 19, 19, 16, 2], "deprel": ["nsubj", "root", "det", "compound", "compound", "amod", "obj", "case", "det", "compound", "nummod", "obl", "punct", "nsubj", "cop", "acl:relcl", "case", "compound", "obl", "punct"], "triples": [{"uid": "2276-0", "target_tags": "I\\O connect\\O a\\O LaCie\\B 2Big\\I external\\I drive\\I via\\O the\\O firewire\\O 800\\O interface\\O ,\\O which\\O is\\O useful\\O for\\O Time\\O Machine\\O .\\O", "opinion_tags": "I\\O connect\\O a\\O LaCie\\O 2Big\\O external\\O drive\\O via\\O the\\O firewire\\O 800\\O interface\\O ,\\O which\\O is\\O useful\\B for\\O Time\\O Machine\\O .\\O", "sentiment": "neutral"}, {"uid": "2276-1", "target_tags": "I\\O connect\\O a\\O LaCie\\O 2Big\\O external\\O drive\\O via\\O the\\O firewire\\B 800\\I interface\\I ,\\O which\\O is\\O useful\\O for\\O Time\\O Machine\\O .\\O", "opinion_tags": "I\\O connect\\O a\\O LaCie\\O 2Big\\O external\\O drive\\O via\\O the\\O firewire\\O 800\\O interface\\O ,\\O which\\O is\\O useful\\B for\\O Time\\O Machine\\O .\\O", "sentiment": "positive"}, {"uid": "2276-2", "target_tags": "I\\O connect\\O a\\O LaCie\\O 2Big\\O external\\O drive\\O via\\O the\\O firewire\\O 800\\O interface\\O ,\\O which\\O is\\O useful\\O for\\O Time\\B Machine\\I .\\O", "opinion_tags": "I\\O connect\\O a\\O LaCie\\O 2Big\\O external\\O drive\\O via\\O the\\O firewire\\O 800\\O interface\\O ,\\O which\\O is\\O useful\\B for\\O Time\\O Machine\\O .\\O", "sentiment": "neutral"}]}, {"id": "2917", "sentence": "The notebook is lacking a HDMI port and a S-video port that would enable one to hook it to a TV .", "postag": ["DT", "NN", "VBZ", "VBG", "DT", "NN", "NN", "CC", "DT", "NN", "NN", "WDT", "MD", "VB", "CD", "TO", "VB", "PRP", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 11, 11, 11, 7, 14, 14, 11, 14, 17, 14, 17, 21, 21, 17, 4], "deprel": ["det", "nsubj", "aux", "root", "det", "compound", "obj", "cc", "det", "compound", "conj", "nsubj", "aux", "acl:relcl", "obj", "mark", "xcomp", "obj", "case", "det", "obl", "punct"], "triples": [{"uid": "2917-0", "target_tags": "The\\O notebook\\O is\\O lacking\\O a\\O HDMI\\B port\\I and\\O a\\O S-video\\O port\\O that\\O would\\O enable\\O one\\O to\\O hook\\O it\\O to\\O a\\O TV\\O .\\O", "opinion_tags": "The\\O notebook\\O is\\O lacking\\B a\\O HDMI\\O port\\O and\\O a\\O S-video\\O port\\O that\\O would\\O enable\\O one\\O to\\O hook\\O it\\O to\\O a\\O TV\\O .\\O", "sentiment": "negative"}, {"uid": "2917-1", "target_tags": "The\\O notebook\\O is\\O lacking\\O a\\O HDMI\\O port\\O and\\O a\\O S-video\\B port\\I that\\O would\\O enable\\O one\\O to\\O hook\\O it\\O to\\O a\\O TV\\O .\\O", "opinion_tags": "The\\O notebook\\O is\\O lacking\\B a\\O HDMI\\O port\\O and\\O a\\O S-video\\O port\\O that\\O would\\O enable\\O one\\O to\\O hook\\O it\\O to\\O a\\O TV\\O .\\O", "sentiment": "negative"}]}, {"id": "1722", "sentence": "After talking it over with the very knowledgeable sales associate , I chose the MacBook Pro over the white MacBook .", "postag": ["IN", "VBG", "PRP", "RP", "IN", "DT", "RB", "JJ", "NNS", "NN", ",", "PRP", "VBD", "DT", "NNP", "NNP", "IN", "DT", "JJ", "NNP", "."], "head": [2, 13, 2, 2, 10, 10, 8, 10, 10, 2, 2, 13, 0, 16, 16, 13, 20, 20, 20, 13, 13], "deprel": ["mark", "advcl", "obj", "compound:prt", "case", "det", "advmod", "amod", "compound", "obl", "punct", "nsubj", "root", "det", "compound", "obj", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "1722-0", "target_tags": "After\\O talking\\O it\\O over\\O with\\O the\\O very\\O knowledgeable\\O sales\\B associate\\I ,\\O I\\O chose\\O the\\O MacBook\\O Pro\\O over\\O the\\O white\\O MacBook\\O .\\O", "opinion_tags": "After\\O talking\\O it\\O over\\O with\\O the\\O very\\O knowledgeable\\B sales\\O associate\\O ,\\O I\\O chose\\O the\\O MacBook\\O Pro\\O over\\O the\\O white\\O MacBook\\O .\\O", "sentiment": "positive"}]}, {"id": "3062", "sentence": "The screen is nice and the images comes very clear , the keyboard and the fit just feels right .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NNS", "VBZ", "RB", "JJ", ",", "DT", "NN", "CC", "DT", "NN", "RB", "VBZ", "RB", "."], "head": [2, 4, 4, 0, 8, 7, 8, 4, 10, 8, 18, 13, 18, 16, 16, 13, 18, 4, 18, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "conj", "advmod", "xcomp", "punct", "det", "nsubj", "cc", "det", "conj", "advmod", "conj", "advmod", "punct"], "triples": [{"uid": "3062-0", "target_tags": "The\\O screen\\B is\\O nice\\O and\\O the\\O images\\O comes\\O very\\O clear\\O ,\\O the\\O keyboard\\O and\\O the\\O fit\\O just\\O feels\\O right\\O .\\O", "opinion_tags": "The\\O screen\\O is\\O nice\\B and\\O the\\O images\\O comes\\O very\\O clear\\O ,\\O the\\O keyboard\\O and\\O the\\O fit\\O just\\O feels\\O right\\O .\\O", "sentiment": "positive"}, {"uid": "3062-1", "target_tags": "The\\O screen\\O is\\O nice\\O and\\O the\\O images\\O comes\\O very\\O clear\\O ,\\O the\\O keyboard\\B and\\O the\\O fit\\O just\\O feels\\O right\\O .\\O", "opinion_tags": "The\\O screen\\O is\\O nice\\O and\\O the\\O images\\O comes\\O very\\O clear\\O ,\\O the\\O keyboard\\O and\\O the\\O fit\\O just\\O feels\\O right\\B .\\O", "sentiment": "positive"}, {"uid": "3062-2", "target_tags": "The\\O screen\\O is\\O nice\\O and\\O the\\O images\\O comes\\O very\\O clear\\O ,\\O the\\O keyboard\\O and\\O the\\O fit\\B just\\O feels\\O right\\O .\\O", "opinion_tags": "The\\O screen\\O is\\O nice\\O and\\O the\\O images\\O comes\\O very\\O clear\\O ,\\O the\\O keyboard\\O and\\O the\\O fit\\O just\\O feels\\O right\\B .\\O", "sentiment": "positive"}]}, {"id": "834", "sentence": "This laptop looks great on the surface : 17 '' inch screen , good price-point , nice appearance , boots up quickly , runs fast etc .", "postag": ["DT", "NN", "VBZ", "JJ", "IN", "DT", "NN", ":", "CD", "''", "NN", "NN", ",", "JJ", "JJ", ",", "JJ", "NN", ",", "NNS", "RB", "RB", ",", "NNS", "JJ", "FW", "."], "head": [2, 3, 0, 3, 7, 7, 4, 12, 11, 12, 12, 3, 12, 18, 18, 18, 18, 12, 20, 12, 20, 21, 24, 12, 24, 12, 3], "deprel": ["det", "nsubj", "root", "xcomp", "case", "det", "obl", "punct", "nummod", "punct", "compound", "parataxis", "punct", "amod", "amod", "punct", "amod", "conj", "punct", "conj", "advmod", "advmod", "punct", "conj", "amod", "conj", "punct"], "triples": [{"uid": "834-0", "target_tags": "This\\O laptop\\O looks\\O great\\O on\\O the\\O surface\\O :\\O 17\\B ''\\I inch\\I screen\\I ,\\O good\\O price-point\\O ,\\O nice\\O appearance\\O ,\\O boots\\O up\\O quickly\\O ,\\O runs\\O fast\\O etc\\O .\\O", "opinion_tags": "This\\O laptop\\O looks\\O great\\B on\\O the\\O surface\\O :\\O 17\\O ''\\O inch\\O screen\\O ,\\O good\\B price-point\\O ,\\O nice\\O appearance\\O ,\\O boots\\O up\\O quickly\\O ,\\O runs\\O fast\\O etc\\O .\\O", "sentiment": "positive"}, {"uid": "834-1", "target_tags": "This\\O laptop\\O looks\\O great\\O on\\O the\\O surface\\O :\\O 17\\O ''\\O inch\\O screen\\O ,\\O good\\O price-point\\B ,\\O nice\\O appearance\\O ,\\O boots\\O up\\O quickly\\O ,\\O runs\\O fast\\O etc\\O .\\O", "opinion_tags": "This\\O laptop\\O looks\\O great\\B on\\O the\\O surface\\O :\\O 17\\O ''\\O inch\\O screen\\O ,\\O good\\B price-point\\O ,\\O nice\\O appearance\\O ,\\O boots\\O up\\O quickly\\O ,\\O runs\\O fast\\O etc\\O .\\O", "sentiment": "positive"}, {"uid": "834-2", "target_tags": "This\\O laptop\\O looks\\O great\\O on\\O the\\O surface\\O :\\O 17\\O ''\\O inch\\O screen\\O ,\\O good\\O price-point\\O ,\\O nice\\O appearance\\B ,\\O boots\\O up\\O quickly\\O ,\\O runs\\O fast\\O etc\\O .\\O", "opinion_tags": "This\\O laptop\\O looks\\O great\\B on\\O the\\O surface\\O :\\O 17\\O ''\\O inch\\O screen\\O ,\\O good\\O price-point\\O ,\\O nice\\B appearance\\O ,\\O boots\\O up\\O quickly\\O ,\\O runs\\O fast\\O etc\\O .\\O", "sentiment": "positive"}, {"uid": "834-3", "target_tags": "This\\O laptop\\O looks\\O great\\O on\\O the\\O surface\\O :\\O 17\\O ''\\O inch\\O screen\\O ,\\O good\\O price-point\\O ,\\O nice\\O appearance\\O ,\\O boots\\B up\\I quickly\\O ,\\O runs\\O fast\\O etc\\O .\\O", "opinion_tags": "This\\O laptop\\O looks\\O great\\O on\\O the\\O surface\\O :\\O 17\\O ''\\O inch\\O screen\\O ,\\O good\\O price-point\\O ,\\O nice\\O appearance\\O ,\\O boots\\O up\\O quickly\\B ,\\O runs\\O fast\\O etc\\O .\\O", "sentiment": "positive"}, {"uid": "834-4", "target_tags": "This\\O laptop\\O looks\\O great\\O on\\O the\\O surface\\O :\\O 17\\O ''\\O inch\\O screen\\O ,\\O good\\O price-point\\O ,\\O nice\\O appearance\\O ,\\O boots\\O up\\O quickly\\O ,\\O runs\\B fast\\O etc\\O .\\O", "opinion_tags": "This\\O laptop\\O looks\\O great\\O on\\O the\\O surface\\O :\\O 17\\O ''\\O inch\\O screen\\O ,\\O good\\O price-point\\O ,\\O nice\\O appearance\\O ,\\O boots\\O up\\O quickly\\O ,\\O runs\\O fast\\B etc\\O .\\O", "sentiment": "positive"}]}, {"id": "162", "sentence": "/ awesome cooling system / much better grafics card ( ATI 5870 ) / 8GB RAM / LED backlit screen ...", "postag": [",", "JJ", "NN", "NN", ",", "RB", "JJR", "NNS", "NN", "-LRB-", "NN", "CD", "-RRB-", ",", "NN", "NN", ",", "NN", "NN", "NN", "."], "head": [4, 4, 4, 0, 9, 7, 9, 9, 4, 9, 12, 9, 9, 16, 16, 20, 18, 16, 20, 9, 4], "deprel": ["punct", "amod", "compound", "root", "punct", "advmod", "amod", "compound", "list", "punct", "compound", "appos", "punct", "punct", "compound", "compound", "cc", "conj", "compound", "conj", "punct"], "triples": [{"uid": "162-0", "target_tags": "/\\O awesome\\O cooling\\B system\\I /\\O much\\O better\\O grafics\\O card\\O (\\O ATI\\O 5870\\O )\\O /\\O 8GB\\O RAM\\O /\\O LED\\O backlit\\O screen\\O ...\\O", "opinion_tags": "/\\O awesome\\B cooling\\O system\\O /\\O much\\O better\\O grafics\\O card\\O (\\O ATI\\O 5870\\O )\\O /\\O 8GB\\O RAM\\O /\\O LED\\O backlit\\O screen\\O ...\\O", "sentiment": "positive"}, {"uid": "162-1", "target_tags": "/\\O awesome\\O cooling\\O system\\O /\\O much\\O better\\O grafics\\B card\\I (\\O ATI\\O 5870\\O )\\O /\\O 8GB\\O RAM\\O /\\O LED\\O backlit\\O screen\\O ...\\O", "opinion_tags": "/\\O awesome\\O cooling\\O system\\O /\\O much\\O better\\B grafics\\O card\\O (\\O ATI\\O 5870\\O )\\O /\\O 8GB\\O RAM\\O /\\O LED\\O backlit\\O screen\\O ...\\O", "sentiment": "positive"}, {"uid": "162-2", "target_tags": "/\\O awesome\\O cooling\\O system\\O /\\O much\\O better\\O grafics\\O card\\O (\\O ATI\\O 5870\\O )\\O /\\O 8GB\\O RAM\\O /\\O LED\\B backlit\\I screen\\I ...\\O", "opinion_tags": "/\\O awesome\\O cooling\\O system\\O /\\O much\\O better\\B grafics\\O card\\O (\\O ATI\\O 5870\\O )\\O /\\O 8GB\\O RAM\\O /\\O LED\\O backlit\\O screen\\O ...\\O", "sentiment": "positive"}, {"uid": "162-3", "target_tags": "/\\O awesome\\O cooling\\O system\\O /\\O much\\O better\\O grafics\\O card\\O (\\O ATI\\O 5870\\O )\\O /\\O 8GB\\B RAM\\I /\\O LED\\O backlit\\O screen\\O ...\\O", "opinion_tags": "/\\O awesome\\O cooling\\O system\\O /\\O much\\O better\\B grafics\\O card\\O (\\O ATI\\O 5870\\O )\\O /\\O 8GB\\O RAM\\O /\\O LED\\O backlit\\O screen\\O ...\\O", "sentiment": "positive"}]}, {"id": "1682", "sentence": "As a user of a PC , I will will admit that the macBook Pro has a better running system in which I found myself `` Getting the job done quicker .", "postag": ["IN", "DT", "NN", "IN", "DT", "NNP", ",", "PRP", "MD", "MD", "VB", "IN", "DT", "NNP", "NNP", "VBZ", "DT", "JJR", "NN", "NN", "IN", "WDT", "PRP", "VBD", "PRP", "``", "VBG", "DT", "NN", "VBN", "RBR", "."], "head": [3, 3, 11, 6, 6, 3, 11, 11, 11, 11, 0, 16, 15, 15, 16, 11, 20, 20, 20, 16, 22, 24, 24, 20, 24, 27, 24, 29, 27, 27, 30, 11], "deprel": ["case", "det", "obl", "case", "det", "nmod", "punct", "nsubj", "aux", "aux", "root", "mark", "det", "compound", "nsubj", "ccomp", "det", "amod", "compound", "obj", "case", "obl", "nsubj", "acl:relcl", "obj", "punct", "ccomp", "det", "obj", "xcomp", "advmod", "punct"], "triples": [{"uid": "1682-0", "target_tags": "As\\O a\\O user\\O of\\O a\\O PC\\O ,\\O I\\O will\\O will\\O admit\\O that\\O the\\O macBook\\O Pro\\O has\\O a\\O better\\O running\\B system\\I in\\O which\\O I\\O found\\O myself\\O ``\\O Getting\\O the\\O job\\O done\\O quicker\\O .\\O", "opinion_tags": "As\\O a\\O user\\O of\\O a\\O PC\\O ,\\O I\\O will\\O will\\O admit\\O that\\O the\\O macBook\\O Pro\\O has\\O a\\O better\\B running\\O system\\O in\\O which\\O I\\O found\\O myself\\O ``\\O Getting\\O the\\O job\\O done\\O quicker\\O .\\O", "sentiment": "positive"}]}, {"id": "1614", "sentence": "I believe that the quality of a mac is worth the price .", "postag": ["PRP", "VBP", "IN", "DT", "NN", "IN", "DT", "NN", "VBZ", "JJ", "DT", "NN", "."], "head": [2, 0, 10, 5, 10, 8, 8, 5, 10, 2, 12, 10, 2], "deprel": ["nsubj", "root", "mark", "det", "nsubj", "case", "det", "nmod", "cop", "ccomp", "det", "obj", "punct"], "triples": [{"uid": "1614-0", "target_tags": "I\\O believe\\O that\\O the\\O quality\\B of\\O a\\O mac\\O is\\O worth\\O the\\O price\\O .\\O", "opinion_tags": "I\\O believe\\O that\\O the\\O quality\\O of\\O a\\O mac\\O is\\O worth\\B the\\O price\\O .\\O", "sentiment": "positive"}, {"uid": "1614-1", "target_tags": "I\\O believe\\O that\\O the\\O quality\\O of\\O a\\O mac\\O is\\O worth\\O the\\O price\\B .\\O", "opinion_tags": "I\\O believe\\O that\\O the\\O quality\\O of\\O a\\O mac\\O is\\O worth\\B the\\O price\\O .\\O", "sentiment": "positive"}]}, {"id": "404", "sentence": "Then HP sends it back to me with the hardware screwed up , not able to connect .", "postag": ["RB", "NNP", "VBZ", "PRP", "RB", "IN", "PRP", "IN", "DT", "NN", "VBN", "RP", ",", "RB", "JJ", "TO", "VB", "."], "head": [3, 3, 0, 3, 3, 7, 3, 10, 10, 3, 10, 11, 15, 15, 11, 17, 15, 3], "deprel": ["advmod", "nsubj", "root", "obj", "advmod", "case", "obl", "case", "det", "obl", "acl", "compound:prt", "punct", "advmod", "conj", "mark", "xcomp", "punct"], "triples": [{"uid": "404-0", "target_tags": "Then\\O HP\\O sends\\O it\\O back\\O to\\O me\\O with\\O the\\O hardware\\B screwed\\O up\\O ,\\O not\\O able\\O to\\O connect\\O .\\O", "opinion_tags": "Then\\O HP\\O sends\\O it\\O back\\O to\\O me\\O with\\O the\\O hardware\\O screwed\\B up\\I ,\\O not\\O able\\O to\\O connect\\O .\\O", "sentiment": "negative"}]}, {"id": "319", "sentence": "It is super easy to use .", "postag": ["PRP", "VBZ", "RB", "JJ", "TO", "VB", "."], "head": [4, 4, 4, 0, 6, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "mark", "csubj", "punct"], "triples": [{"uid": "319-0", "target_tags": "It\\O is\\O super\\O easy\\O to\\O use\\B .\\O", "opinion_tags": "It\\O is\\O super\\O easy\\B to\\O use\\O .\\O", "sentiment": "positive"}]}, {"id": "2243", "sentence": "It 's wonderful for computer gaming .", "postag": ["PRP", "VBZ", "JJ", "IN", "NN", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 3], "deprel": ["nsubj", "cop", "root", "case", "compound", "obl", "punct"], "triples": [{"uid": "2243-0", "target_tags": "It\\O 's\\O wonderful\\O for\\O computer\\O gaming\\B .\\O", "opinion_tags": "It\\O 's\\O wonderful\\B for\\O computer\\O gaming\\O .\\O", "sentiment": "positive"}]}, {"id": "1364", "sentence": "Product support very poor as each phone call costs me long distan", "postag": ["NN", "NN", "RB", "JJ", "IN", "DT", "NN", "NN", "VBZ", "PRP", "RB", "."], "head": [2, 0, 4, 2, 9, 8, 8, 9, 4, 9, 9, 2], "deprel": ["compound", "root", "advmod", "amod", "mark", "det", "compound", "nsubj", "advcl", "obj", "advmod", "punct"], "triples": [{"uid": "1364-0", "target_tags": "Product\\B support\\I very\\O poor\\O as\\O each\\O phone\\O call\\O costs\\O me\\O long\\O distan\\O", "opinion_tags": "Product\\O support\\O very\\O poor\\B as\\O each\\O phone\\O call\\O costs\\O me\\O long\\O distan\\O", "sentiment": "negative"}]}, {"id": "2828", "sentence": "Excellent speed for processing data .", "postag": ["JJ", "NN", "IN", "NN", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["amod", "root", "case", "compound", "nmod", "punct"], "triples": [{"uid": "2828-0", "target_tags": "Excellent\\O speed\\B for\\O processing\\O data\\O .\\O", "opinion_tags": "Excellent\\B speed\\O for\\O processing\\O data\\O .\\O", "sentiment": "positive"}]}, {"id": "2130", "sentence": "I also liked the glass screen .", "postag": ["PRP", "RB", "VBD", "DT", "NN", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "compound", "obj", "punct"], "triples": [{"uid": "2130-0", "target_tags": "I\\O also\\O liked\\O the\\O glass\\B screen\\I .\\O", "opinion_tags": "I\\O also\\O liked\\B the\\O glass\\O screen\\O .\\O", "sentiment": "positive"}]}, {"id": "3023", "sentence": "The reflectiveness of the display is only a minor inconvenience if you work in a controlled-lighting environment like me ( I prefer it dark ) or if you can crank up the brightness .", "postag": ["DT", "NN", "IN", "DT", "NN", "VBZ", "RB", "DT", "JJ", "NN", "IN", "PRP", "VBP", "IN", "DT", "JJ", "NN", "IN", "PRP", "-LRB-", "PRP", "VBP", "PRP", "JJ", "-RRB-", "CC", "IN", "PRP", "MD", "VB", "RP", "DT", "NN", "."], "head": [2, 10, 5, 5, 2, 10, 10, 10, 10, 0, 13, 13, 10, 17, 17, 17, 13, 19, 17, 22, 22, 10, 22, 22, 22, 30, 30, 30, 30, 13, 30, 33, 30, 10], "deprel": ["det", "nsubj", "case", "det", "nmod", "cop", "advmod", "det", "amod", "root", "mark", "nsubj", "advcl", "case", "det", "amod", "obl", "case", "nmod", "punct", "nsubj", "parataxis", "obj", "xcomp", "punct", "cc", "mark", "nsubj", "aux", "conj", "compound:prt", "det", "obj", "punct"], "triples": [{"uid": "3023-0", "target_tags": "The\\O reflectiveness\\O of\\O the\\O display\\B is\\O only\\O a\\O minor\\O inconvenience\\O if\\O you\\O work\\O in\\O a\\O controlled-lighting\\O environment\\O like\\O me\\O (\\O I\\O prefer\\O it\\O dark\\O )\\O or\\O if\\O you\\O can\\O crank\\O up\\O the\\O brightness\\O .\\O", "opinion_tags": "The\\O reflectiveness\\O of\\O the\\O display\\O is\\O only\\O a\\O minor\\B inconvenience\\I if\\O you\\O work\\O in\\O a\\O controlled-lighting\\O environment\\O like\\O me\\O (\\O I\\O prefer\\O it\\O dark\\O )\\O or\\O if\\O you\\O can\\O crank\\O up\\O the\\O brightness\\O .\\O", "sentiment": "negative"}, {"uid": "3023-1", "target_tags": "The\\O reflectiveness\\O of\\O the\\O display\\O is\\O only\\O a\\O minor\\O inconvenience\\O if\\O you\\O work\\O in\\O a\\O controlled-lighting\\O environment\\O like\\O me\\O (\\O I\\O prefer\\O it\\O dark\\O )\\O or\\O if\\O you\\O can\\O crank\\O up\\O the\\O brightness\\B .\\O", "opinion_tags": "The\\O reflectiveness\\O of\\O the\\O display\\O is\\O only\\O a\\O minor\\O inconvenience\\O if\\O you\\O work\\O in\\O a\\O controlled-lighting\\O environment\\O like\\O me\\O (\\O I\\O prefer\\O it\\O dark\\O )\\O or\\O if\\O you\\O can\\O crank\\B up\\I the\\O brightness\\O .\\O", "sentiment": "neutral"}]}, {"id": "2398", "sentence": "I bought it for my mom and she reports that the battery life lasts all day for her , it 's very lightweight , and the response for the computing she 's doing ( Internet focused activity : mail , research , etc . ) is excellent ;", "postag": ["PRP", "VBD", "PRP", "IN", "PRP$", "NN", "CC", "PRP", "VBZ", "IN", "DT", "NN", "NN", "VBZ", "DT", "NN", "IN", "PRP", ",", "PRP", "VBZ", "RB", "JJ", ",", "CC", "DT", "NN", "IN", "DT", "NN", "PRP", "VBZ", "VBG", "-LRB-", "NN", "VBN", "NN", ":", "NN", ",", "NN", ",", "FW", ".", "-RRB-", "VBZ", "JJ", ":"], "head": [2, 0, 2, 6, 6, 2, 9, 9, 2, 14, 13, 13, 14, 9, 16, 14, 18, 14, 23, 23, 23, 23, 2, 47, 47, 27, 47, 30, 30, 27, 33, 33, 30, 37, 37, 37, 27, 37, 37, 41, 39, 43, 39, 37, 37, 47, 2, 2], "deprel": ["nsubj", "root", "obj", "case", "nmod:poss", "obl", "cc", "nsubj", "conj", "mark", "det", "compound", "nsubj", "ccomp", "det", "obl:tmod", "case", "obl", "punct", "nsubj", "cop", "advmod", "parataxis", "punct", "cc", "det", "nsubj", "case", "det", "nmod", "nsubj", "aux", "acl:relcl", "punct", "compound", "amod", "appos", "punct", "appos", "punct", "conj", "punct", "conj", "punct", "punct", "cop", "conj", "punct"], "triples": [{"uid": "2398-0", "target_tags": "I\\O bought\\O it\\O for\\O my\\O mom\\O and\\O she\\O reports\\O that\\O the\\O battery\\B life\\I lasts\\O all\\O day\\O for\\O her\\O ,\\O it\\O 's\\O very\\O lightweight\\O ,\\O and\\O the\\O response\\O for\\O the\\O computing\\O she\\O 's\\O doing\\O (\\O Internet\\O focused\\O activity\\O :\\O mail\\O ,\\O research\\O ,\\O etc\\O .\\O )\\O is\\O excellent\\O ;\\O", "opinion_tags": "I\\O bought\\O it\\O for\\O my\\O mom\\O and\\O she\\O reports\\O that\\O the\\O battery\\O life\\O lasts\\B all\\I day\\I for\\O her\\O ,\\O it\\O 's\\O very\\O lightweight\\O ,\\O and\\O the\\O response\\O for\\O the\\O computing\\O she\\O 's\\O doing\\O (\\O Internet\\O focused\\O activity\\O :\\O mail\\O ,\\O research\\O ,\\O etc\\O .\\O )\\O is\\O excellent\\O ;\\O", "sentiment": "positive"}, {"uid": "2398-1", "target_tags": "I\\O bought\\O it\\O for\\O my\\O mom\\O and\\O she\\O reports\\O that\\O the\\O battery\\O life\\O lasts\\O all\\O day\\O for\\O her\\O ,\\O it\\O 's\\O very\\O lightweight\\O ,\\O and\\O the\\O response\\B for\\O the\\O computing\\O she\\O 's\\O doing\\O (\\O Internet\\O focused\\O activity\\O :\\O mail\\O ,\\O research\\O ,\\O etc\\O .\\O )\\O is\\O excellent\\O ;\\O", "opinion_tags": "I\\O bought\\O it\\O for\\O my\\O mom\\O and\\O she\\O reports\\O that\\O the\\O battery\\O life\\O lasts\\O all\\O day\\O for\\O her\\O ,\\O it\\O 's\\O very\\O lightweight\\O ,\\O and\\O the\\O response\\O for\\O the\\O computing\\O she\\O 's\\O doing\\O (\\O Internet\\O focused\\O activity\\O :\\O mail\\O ,\\O research\\O ,\\O etc\\O .\\O )\\O is\\O excellent\\B ;\\O", "sentiment": "positive"}, {"uid": "2398-2", "target_tags": "I\\O bought\\O it\\O for\\O my\\O mom\\O and\\O she\\O reports\\O that\\O the\\O battery\\O life\\O lasts\\O all\\O day\\O for\\O her\\O ,\\O it\\O 's\\O very\\O lightweight\\O ,\\O and\\O the\\O response\\O for\\O the\\O computing\\O she\\O 's\\O doing\\O (\\O Internet\\B focused\\I activity\\I :\\O mail\\O ,\\O research\\O ,\\O etc\\O .\\O )\\O is\\O excellent\\O ;\\O", "opinion_tags": "I\\O bought\\O it\\O for\\O my\\O mom\\O and\\O she\\O reports\\O that\\O the\\O battery\\O life\\O lasts\\O all\\O day\\O for\\O her\\O ,\\O it\\O 's\\O very\\O lightweight\\O ,\\O and\\O the\\O response\\O for\\O the\\O computing\\O she\\O 's\\O doing\\O (\\O Internet\\O focused\\O activity\\O :\\O mail\\O ,\\O research\\O ,\\O etc\\O .\\O )\\O is\\O excellent\\B ;\\O", "sentiment": "neutral"}]}, {"id": "2625", "sentence": "The resolution on the screen is almost pure HD .", "postag": ["DT", "NN", "IN", "DT", "NN", "VBZ", "RB", "JJ", "NN", "."], "head": [2, 9, 5, 5, 2, 9, 8, 9, 0, 9], "deprel": ["det", "nsubj", "case", "det", "nmod", "cop", "advmod", "amod", "root", "punct"], "triples": [{"uid": "2625-0", "target_tags": "The\\O resolution\\B on\\I the\\I screen\\I is\\O almost\\O pure\\O HD\\O .\\O", "opinion_tags": "The\\O resolution\\O on\\O the\\O screen\\O is\\O almost\\O pure\\B HD\\I .\\O", "sentiment": "positive"}]}, {"id": "503", "sentence": "I have never really been big on downloading anything so I was n't too worried about getting a virus , plus I thought I was protected by <PERSON> .", "postag": ["PRP", "VBP", "RB", "RB", "VBN", "JJ", "IN", "VBG", "NN", "RB", "PRP", "VBD", "RB", "RB", "JJ", "IN", "VBG", "DT", "NN", ",", "CC", "PRP", "VBD", "PRP", "VBD", "VBN", "IN", "NNP", "."], "head": [6, 6, 6, 6, 6, 0, 8, 6, 8, 15, 15, 15, 15, 15, 6, 17, 15, 19, 17, 23, 23, 23, 6, 26, 26, 23, 28, 26, 6], "deprel": ["nsubj", "aux", "advmod", "advmod", "cop", "root", "mark", "advcl", "obj", "advmod", "nsubj", "cop", "advmod", "advmod", "conj", "mark", "advcl", "det", "obj", "punct", "cc", "nsubj", "conj", "nsubj:pass", "aux:pass", "ccomp", "case", "obl", "punct"], "triples": [{"uid": "503-0", "target_tags": "I\\O have\\O never\\O really\\O been\\O big\\O on\\O downloading\\O anything\\O so\\O I\\O was\\O n't\\O too\\O worried\\O about\\O getting\\O a\\O virus\\O ,\\O plus\\O I\\O thought\\O I\\O was\\O protected\\O by\\O Norton\\B .\\O", "opinion_tags": "I\\O have\\O never\\O really\\O been\\O big\\O on\\O downloading\\O anything\\O so\\O I\\O was\\O n't\\O too\\O worried\\O about\\O getting\\O a\\O virus\\O ,\\O plus\\O I\\O thought\\O I\\O was\\O protected\\B by\\O Norton\\O .\\O", "sentiment": "negative"}]}, {"id": "2718", "sentence": "I 've owned this labtop for less then two months , already the mouse button has broke .", "postag": ["PRP", "VBP", "VBN", "DT", "NN", "IN", "JJR", "RB", "CD", "NNS", ",", "RB", "DT", "NN", "NN", "VBZ", "VBN", "."], "head": [3, 3, 0, 5, 3, 10, 8, 10, 10, 3, 3, 17, 15, 15, 17, 17, 3, 3], "deprel": ["nsubj", "aux", "root", "det", "obj", "case", "advmod", "advmod", "nummod", "obl", "punct", "advmod", "det", "compound", "nsubj", "aux", "parataxis", "punct"], "triples": [{"uid": "2718-0", "target_tags": "I\\O 've\\O owned\\O this\\O labtop\\O for\\O less\\O then\\O two\\O months\\O ,\\O already\\O the\\O mouse\\B button\\I has\\O broke\\O .\\O", "opinion_tags": "I\\O 've\\O owned\\O this\\O labtop\\O for\\O less\\O then\\O two\\O months\\O ,\\O already\\O the\\O mouse\\O button\\O has\\O broke\\B .\\O", "sentiment": "negative"}]}, {"id": "309", "sentence": "Since I 've had this computer I 've only used the trackpad because it is so nice and smooth .", "postag": ["IN", "PRP", "VBP", "VBN", "DT", "NN", "PRP", "VBP", "RB", "VBN", "DT", "NN", "IN", "PRP", "VBZ", "RB", "JJ", "CC", "JJ", "."], "head": [4, 4, 4, 10, 6, 4, 10, 10, 10, 0, 12, 10, 17, 17, 17, 17, 10, 19, 17, 10], "deprel": ["mark", "nsubj", "aux", "advcl", "det", "obj", "nsubj", "aux", "advmod", "root", "det", "obj", "mark", "nsubj", "cop", "advmod", "advcl", "cc", "conj", "punct"], "triples": [{"uid": "309-0", "target_tags": "Since\\O I\\O 've\\O had\\O this\\O computer\\O I\\O 've\\O only\\O used\\O the\\O trackpad\\B because\\O it\\O is\\O so\\O nice\\O and\\O smooth\\O .\\O", "opinion_tags": "Since\\O I\\O 've\\O had\\O this\\O computer\\O I\\O 've\\O only\\O used\\O the\\O trackpad\\O because\\O it\\O is\\O so\\O nice\\B and\\O smooth\\B .\\O", "sentiment": "positive"}]}, {"id": "1154", "sentence": "So , after Apple replaced the hard drive I enjoyed another 4 months of my new computer , until it froze this morning -- completely .", "postag": ["RB", ",", "IN", "NNP", "VBD", "DT", "JJ", "NN", "PRP", "VBD", "DT", "CD", "NNS", "IN", "PRP$", "JJ", "NN", ",", "IN", "PRP", "VBD", "DT", "NN", ",", "RB", "."], "head": [10, 10, 5, 5, 10, 8, 8, 5, 10, 0, 13, 13, 10, 17, 17, 17, 13, 10, 21, 21, 10, 23, 21, 10, 21, 10], "deprel": ["advmod", "punct", "mark", "nsubj", "advcl", "det", "amod", "obj", "nsubj", "root", "det", "nummod", "obj", "case", "nmod:poss", "amod", "nmod", "punct", "mark", "nsubj", "advcl", "det", "obl:tmod", "punct", "advmod", "punct"], "triples": [{"uid": "1154-0", "target_tags": "So\\O ,\\O after\\O Apple\\O replaced\\O the\\O hard\\B drive\\I I\\O enjoyed\\O another\\O 4\\O months\\O of\\O my\\O new\\O computer\\O ,\\O until\\O it\\O froze\\O this\\O morning\\O --\\O completely\\O .\\O", "opinion_tags": "So\\O ,\\O after\\O Apple\\O replaced\\B the\\O hard\\O drive\\O I\\O enjoyed\\B another\\O 4\\O months\\O of\\O my\\O new\\O computer\\O ,\\O until\\O it\\O froze\\B this\\O morning\\O --\\O completely\\O .\\O", "sentiment": "neutral"}]}, {"id": "2373", "sentence": "The screen takes some getting use to , because it is smaller than the laptop .", "postag": ["DT", "NN", "VBZ", "DT", "VBG", "NN", "IN", ",", "IN", "PRP", "VBZ", "JJR", "IN", "DT", "NN", "."], "head": [2, 3, 0, 5, 3, 5, 6, 3, 12, 12, 12, 3, 15, 15, 12, 3], "deprel": ["det", "nsubj", "root", "nsubj", "xcomp", "obj", "obl", "punct", "mark", "nsubj", "cop", "advcl", "case", "det", "obl", "punct"], "triples": [{"uid": "2373-0", "target_tags": "The\\O screen\\B takes\\O some\\O getting\\O use\\O to\\O ,\\O because\\O it\\O is\\O smaller\\O than\\O the\\O laptop\\O .\\O", "opinion_tags": "The\\O screen\\O takes\\O some\\O getting\\O use\\O to\\O ,\\O because\\O it\\O is\\O smaller\\B than\\O the\\O laptop\\O .\\O", "sentiment": "negative"}]}, {"id": "1795", "sentence": "Unfortunately , Apple 's quality has continued to slide .", "postag": ["RB", ",", "NNP", "POS", "NN", "VBZ", "VBN", "TO", "VB", "."], "head": [7, 7, 5, 3, 7, 7, 0, 9, 7, 7], "deprel": ["advmod", "punct", "nmod:poss", "case", "nsubj", "aux", "root", "mark", "xcomp", "punct"], "triples": [{"uid": "1795-0", "target_tags": "Unfortunately\\O ,\\O Apple\\O 's\\O quality\\B has\\O continued\\O to\\O slide\\O .\\O", "opinion_tags": "Unfortunately\\B ,\\O Apple\\O 's\\O quality\\O has\\O continued\\O to\\O slide\\B .\\O", "sentiment": "negative"}]}, {"id": "1924", "sentence": "In November my computer messed up entirely and would n't power on after intalling a Windows update , I had to have my HD flashed and lost EVERYTHING on it , including my school assignments and irriplaceable pictures that were only in digital format and several other things , when this update was installed for some reason I was unable to roll back the drivers and everything to an earlier working condition because when the update was installed it deleted my history .", "postag": ["IN", "NNP", "PRP$", "NN", "VBD", "RP", "RB", "CC", "MD", "RB", "VB", "RB", "IN", "VBG", "DT", "NN", "NN", ",", "PRP", "VBD", "TO", "VB", "PRP$", "NN", "VBN", "CC", "VBN", "NN", "IN", "PRP", ",", "VBG", "PRP$", "NN", "NNS", "CC", "JJ", "NNS", "WDT", "VBD", "RB", "IN", "JJ", "NN", "CC", "JJ", "JJ", "NNS", ",", "WRB", "DT", "NN", "VBD", "VBN", "IN", "DT", "NN", "PRP", "VBD", "JJ", "TO", "VB", "RB", "DT", "NNS", "CC", "NN", "IN", "DT", "JJR", "NN", "NN", "IN", "WRB", "DT", "NN", "VBD", "VBN", "PRP", "VBD", "PRP$", "NN", "."], "head": [2, 5, 4, 5, 0, 5, 5, 11, 11, 11, 5, 11, 14, 11, 17, 17, 14, 5, 20, 5, 22, 20, 24, 22, 22, 27, 22, 27, 30, 27, 35, 35, 35, 35, 27, 38, 38, 35, 44, 44, 44, 44, 44, 38, 48, 48, 48, 44, 5, 54, 52, 54, 54, 44, 57, 57, 54, 60, 60, 57, 62, 60, 62, 65, 62, 67, 65, 72, 72, 72, 72, 62, 78, 78, 76, 78, 78, 80, 80, 62, 82, 80, 5], "deprel": ["case", "obl", "nmod:poss", "nsubj", "root", "compound:prt", "advmod", "cc", "aux", "advmod", "conj", "advmod", "mark", "advcl", "det", "compound", "obj", "punct", "nsubj", "parataxis", "mark", "xcomp", "nmod:poss", "obj", "xcomp", "cc", "conj", "obj", "case", "obl", "punct", "case", "nmod:poss", "compound", "obl", "cc", "amod", "conj", "nsubj", "cop", "advmod", "case", "amod", "acl:relcl", "cc", "amod", "amod", "conj", "punct", "mark", "det", "nsubj:pass", "aux:pass", "advcl", "case", "det", "obl", "nsubj", "cop", "acl:relcl", "mark", "xcomp", "advmod", "det", "obj", "cc", "conj", "case", "det", "amod", "compound", "obl", "mark", "mark", "det", "nsubj:pass", "aux:pass", "advcl", "nsubj", "advcl", "nmod:poss", "obj", "punct"], "triples": [{"uid": "1924-0", "target_tags": "In\\O November\\O my\\O computer\\O messed\\O up\\O entirely\\O and\\O would\\O n't\\O power\\O on\\O after\\O intalling\\O a\\O Windows\\B update\\I ,\\O I\\O had\\O to\\O have\\O my\\O HD\\O flashed\\O and\\O lost\\O EVERYTHING\\O on\\O it\\O ,\\O including\\O my\\O school\\O assignments\\O and\\O irriplaceable\\O pictures\\O that\\O were\\O only\\O in\\O digital\\O format\\O and\\O several\\O other\\O things\\O ,\\O when\\O this\\O update\\O was\\O installed\\O for\\O some\\O reason\\O I\\O was\\O unable\\O to\\O roll\\O back\\O the\\O drivers\\O and\\O everything\\O to\\O an\\O earlier\\O working\\O condition\\O because\\O when\\O the\\O update\\O was\\O installed\\O it\\O deleted\\O my\\O history\\O .\\O", "opinion_tags": "In\\O November\\O my\\O computer\\O messed\\B up\\I entirely\\O and\\O would\\O n't\\O power\\O on\\O after\\O intalling\\O a\\O Windows\\O update\\O ,\\O I\\O had\\O to\\O have\\O my\\O HD\\O flashed\\O and\\O lost\\O EVERYTHING\\O on\\O it\\O ,\\O including\\O my\\O school\\O assignments\\O and\\O irriplaceable\\O pictures\\O that\\O were\\O only\\O in\\O digital\\O format\\O and\\O several\\O other\\O things\\O ,\\O when\\O this\\O update\\O was\\O installed\\O for\\O some\\O reason\\O I\\O was\\O unable\\O to\\O roll\\O back\\O the\\O drivers\\O and\\O everything\\O to\\O an\\O earlier\\O working\\O condition\\O because\\O when\\O the\\O update\\O was\\O installed\\O it\\O deleted\\O my\\O history\\O .\\O", "sentiment": "neutral"}]}, {"id": "1925", "sentence": "When I got my laptop back after this first instance it worked okay for a little bit then I started expeirencing issues again , everything from programs and drivers failing again , to it powering off for no reason , to locking up and freezing and just all sorts of issues .", "postag": ["WRB", "PRP", "VBD", "PRP$", "NN", "RB", "IN", "DT", "JJ", "NN", "PRP", "VBD", "JJ", "IN", "DT", "JJ", "NN", "RB", "PRP", "VBD", "VBG", "NNS", "RB", ",", "NN", "IN", "NNS", "CC", "NNS", "VBG", "RB", ",", "IN", "PRP", "VBG", "RP", "IN", "DT", "NN", ",", "IN", "VBG", "RP", "CC", "VBG", "CC", "RB", "DT", "NNS", "IN", "NNS", "."], "head": [3, 3, 20, 5, 3, 3, 10, 10, 10, 3, 12, 3, 12, 17, 17, 17, 12, 20, 20, 0, 20, 21, 21, 20, 20, 27, 25, 29, 27, 25, 30, 35, 34, 35, 30, 35, 39, 39, 35, 42, 42, 35, 42, 45, 42, 49, 49, 49, 42, 51, 49, 20], "deprel": ["mark", "nsubj", "advcl", "nmod:poss", "obj", "advmod", "case", "det", "amod", "obl", "nsubj", "conj", "xcomp", "case", "det", "amod", "obl", "advmod", "nsubj", "root", "xcomp", "obj", "advmod", "punct", "nsubj", "case", "nmod", "cc", "conj", "acl", "advmod", "punct", "case", "obl", "advcl", "compound:prt", "case", "det", "obl", "punct", "mark", "advcl", "compound:prt", "cc", "conj", "cc", "advmod", "det", "conj", "case", "nmod", "punct"], "triples": [{"uid": "1925-0", "target_tags": "When\\O I\\O got\\O my\\O laptop\\O back\\O after\\O this\\O first\\O instance\\O it\\O worked\\O okay\\O for\\O a\\O little\\O bit\\O then\\O I\\O started\\O expeirencing\\O issues\\O again\\O ,\\O everything\\O from\\O programs\\B and\\O drivers\\O failing\\O again\\O ,\\O to\\O it\\O powering\\O off\\O for\\O no\\O reason\\O ,\\O to\\O locking\\O up\\O and\\O freezing\\O and\\O just\\O all\\O sorts\\O of\\O issues\\O .\\O", "opinion_tags": "When\\O I\\O got\\O my\\O laptop\\O back\\O after\\O this\\O first\\O instance\\O it\\O worked\\O okay\\O for\\O a\\O little\\O bit\\O then\\O I\\O started\\O expeirencing\\O issues\\B again\\O ,\\O everything\\O from\\O programs\\O and\\O drivers\\O failing\\B again\\O ,\\O to\\O it\\O powering\\O off\\O for\\O no\\O reason\\O ,\\O to\\O locking\\O up\\O and\\O freezing\\O and\\O just\\O all\\O sorts\\O of\\O issues\\O .\\O", "sentiment": "negative"}, {"uid": "1925-1", "target_tags": "When\\O I\\O got\\O my\\O laptop\\O back\\O after\\O this\\O first\\O instance\\O it\\O worked\\O okay\\O for\\O a\\O little\\O bit\\O then\\O I\\O started\\O expeirencing\\O issues\\O again\\O ,\\O everything\\O from\\O programs\\O and\\O drivers\\B failing\\O again\\O ,\\O to\\O it\\O powering\\O off\\O for\\O no\\O reason\\O ,\\O to\\O locking\\O up\\O and\\O freezing\\O and\\O just\\O all\\O sorts\\O of\\O issues\\O .\\O", "opinion_tags": "When\\O I\\O got\\O my\\O laptop\\O back\\O after\\O this\\O first\\O instance\\O it\\O worked\\O okay\\O for\\O a\\O little\\O bit\\O then\\O I\\O started\\O expeirencing\\O issues\\B again\\O ,\\O everything\\O from\\O programs\\O and\\O drivers\\O failing\\B again\\O ,\\O to\\O it\\O powering\\O off\\O for\\O no\\O reason\\O ,\\O to\\O locking\\O up\\O and\\O freezing\\O and\\O just\\O all\\O sorts\\O of\\O issues\\O .\\O", "sentiment": "negative"}]}, {"id": "1599", "sentence": "I do not experience a lot of heat coming out of it , however I would highly suggest purchasing a stand however , due to the nature of the design of the macbook as it is one very large heat sink .", "postag": ["PRP", "VBP", "RB", "VB", "DT", "NN", "IN", "NN", "VBG", "IN", "IN", "PRP", ",", "RB", "PRP", "MD", "RB", "VB", "VBG", "DT", "NN", "RB", ",", "IN", "IN", "DT", "NN", "IN", "DT", "NN", "IN", "DT", "NN", "IN", "PRP", "VBZ", "CD", "RB", "JJ", "NN", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 6, 12, 12, 9, 4, 18, 18, 18, 18, 4, 18, 21, 19, 19, 27, 27, 24, 27, 19, 30, 30, 27, 33, 33, 30, 41, 41, 41, 41, 39, 41, 41, 19, 4], "deprel": ["nsubj", "aux", "advmod", "root", "det", "obj", "case", "nmod", "acl", "case", "case", "obl", "punct", "advmod", "nsubj", "aux", "advmod", "parataxis", "xcomp", "det", "obj", "advmod", "punct", "case", "fixed", "det", "obl", "case", "det", "nmod", "case", "det", "nmod", "mark", "nsubj", "cop", "nummod", "advmod", "amod", "compound", "advcl", "punct"], "triples": [{"uid": "1599-0", "target_tags": "I\\O do\\O not\\O experience\\O a\\O lot\\O of\\O heat\\O coming\\O out\\O of\\O it\\O ,\\O however\\O I\\O would\\O highly\\O suggest\\O purchasing\\O a\\O stand\\B however\\O ,\\O due\\O to\\O the\\O nature\\O of\\O the\\O design\\O of\\O the\\O macbook\\O as\\O it\\O is\\O one\\O very\\O large\\O heat\\O sink\\O .\\O", "opinion_tags": "I\\O do\\O not\\O experience\\O a\\O lot\\O of\\O heat\\O coming\\O out\\O of\\O it\\O ,\\O however\\O I\\O would\\O highly\\O suggest\\B purchasing\\O a\\O stand\\O however\\O ,\\O due\\O to\\O the\\O nature\\O of\\O the\\O design\\O of\\O the\\O macbook\\O as\\O it\\O is\\O one\\O very\\O large\\O heat\\O sink\\O .\\O", "sentiment": "neutral"}, {"uid": "1599-1", "target_tags": "I\\O do\\O not\\O experience\\O a\\O lot\\O of\\O heat\\O coming\\O out\\O of\\O it\\O ,\\O however\\O I\\O would\\O highly\\O suggest\\O purchasing\\O a\\O stand\\O however\\O ,\\O due\\O to\\O the\\O nature\\O of\\O the\\O design\\O of\\O the\\O macbook\\O as\\O it\\O is\\O one\\O very\\O large\\O heat\\B sink\\I .\\O", "opinion_tags": "I\\O do\\O not\\O experience\\O a\\O lot\\O of\\O heat\\O coming\\O out\\O of\\O it\\O ,\\O however\\O I\\O would\\O highly\\O suggest\\O purchasing\\O a\\O stand\\O however\\O ,\\O due\\O to\\O the\\O nature\\O of\\O the\\O design\\O of\\O the\\O macbook\\O as\\O it\\O is\\O one\\O very\\O large\\B heat\\O sink\\O .\\O", "sentiment": "negative"}]}, {"id": "2419", "sentence": "It is easy to go from one keyboard to another .", "postag": ["PRP", "VBZ", "JJ", "TO", "VB", "IN", "CD", "NN", "IN", "DT", "."], "head": [3, 3, 0, 5, 3, 8, 8, 5, 10, 5, 3], "deprel": ["expl", "cop", "root", "mark", "csubj", "case", "nummod", "obl", "case", "obl", "punct"], "triples": [{"uid": "2419-0", "target_tags": "It\\O is\\O easy\\O to\\O go\\O from\\O one\\O keyboard\\B to\\O another\\O .\\O", "opinion_tags": "It\\O is\\O easy\\B to\\O go\\O from\\O one\\O keyboard\\O to\\O another\\O .\\O", "sentiment": "neutral"}]}, {"id": "1669", "sentence": "It 's like 9 punds , but if you can look past it , it 's GREAT !", "postag": ["PRP", "VBZ", "IN", "CD", "NNS", ",", "CC", "IN", "PRP", "MD", "VB", "IN", "PRP", ",", "PRP", "VBZ", "JJ", "."], "head": [5, 5, 5, 5, 0, 17, 17, 11, 11, 11, 17, 13, 11, 17, 17, 17, 5, 5], "deprel": ["nsubj", "cop", "case", "nummod", "root", "punct", "cc", "mark", "nsubj", "aux", "advcl", "case", "obl", "punct", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "1669-0", "target_tags": "It\\O 's\\O like\\O 9\\B punds\\I ,\\O but\\O if\\O you\\O can\\O look\\O past\\O it\\O ,\\O it\\O 's\\O GREAT\\O !\\O", "opinion_tags": "It\\O 's\\O like\\O 9\\O punds\\O ,\\O but\\O if\\O you\\O can\\O look\\O past\\O it\\O ,\\O it\\O 's\\O GREAT\\B !\\O", "sentiment": "negative"}]}, {"id": "2247", "sentence": "iLife is easily compatible with Microsoft Office so you can send and receive files from a PC .", "postag": ["NNP", "VBZ", "RB", "JJ", "IN", "NNP", "NNP", "RB", "PRP", "MD", "VB", "CC", "VB", "NNS", "IN", "DT", "NNP", "."], "head": [4, 4, 4, 0, 7, 7, 4, 11, 11, 11, 4, 13, 11, 11, 17, 17, 14, 4], "deprel": ["nsubj", "cop", "advmod", "root", "case", "compound", "obl", "advmod", "nsubj", "aux", "advcl", "cc", "conj", "obj", "case", "det", "nmod", "punct"], "triples": [{"uid": "2247-0", "target_tags": "iLife\\B is\\O easily\\O compatible\\O with\\O Microsoft\\O Office\\O so\\O you\\O can\\O send\\O and\\O receive\\O files\\O from\\O a\\O PC\\O .\\O", "opinion_tags": "iLife\\O is\\O easily\\B compatible\\I with\\O Microsoft\\O Office\\O so\\O you\\O can\\O send\\O and\\O receive\\O files\\O from\\O a\\O PC\\O .\\O", "sentiment": "positive"}]}, {"id": "2805", "sentence": "little short on RAM but you get what you pay for .", "postag": ["JJ", "JJ", "IN", "NN", "CC", "PRP", "VBP", "WP", "PRP", "VBP", "IN", "."], "head": [2, 0, 4, 2, 7, 7, 2, 7, 10, 8, 10, 2], "deprel": ["advmod", "root", "case", "obl", "cc", "nsubj", "conj", "obj", "nsubj", "acl:relcl", "obl", "punct"], "triples": [{"uid": "2805-0", "target_tags": "little\\O short\\O on\\O RAM\\B but\\O you\\O get\\O what\\O you\\O pay\\O for\\O .\\O", "opinion_tags": "little\\O short\\B on\\O RAM\\O but\\O you\\O get\\O what\\O you\\O pay\\O for\\O .\\O", "sentiment": "negative"}]}, {"id": "1177", "sentence": "Take your time and go through the tutorials patiently .", "postag": ["VB", "PRP$", "NN", "CC", "VB", "IN", "DT", "NNS", "RB", "."], "head": [0, 3, 1, 5, 1, 8, 8, 5, 5, 1], "deprel": ["root", "nmod:poss", "obj", "cc", "conj", "case", "det", "obl", "advmod", "punct"], "triples": [{"uid": "1177-0", "target_tags": "Take\\O your\\O time\\O and\\O go\\O through\\O the\\O tutorials\\B patiently\\O .\\O", "opinion_tags": "Take\\O your\\O time\\O and\\O go\\O through\\O the\\O tutorials\\O patiently\\B .\\O", "sentiment": "neutral"}]}, {"id": "1707", "sentence": "I love this program , it is superior to windows movie maker .", "postag": ["PRP", "VBP", "DT", "NN", ",", "PRP", "VBZ", "JJ", "IN", "NNS", "NN", "NN", "."], "head": [2, 0, 4, 2, 2, 8, 8, 2, 12, 12, 12, 8, 2], "deprel": ["nsubj", "root", "det", "obj", "punct", "nsubj", "cop", "parataxis", "case", "compound", "compound", "obl", "punct"], "triples": [{"uid": "1707-0", "target_tags": "I\\O love\\O this\\O program\\B ,\\O it\\O is\\O superior\\O to\\O windows\\O movie\\O maker\\O .\\O", "opinion_tags": "I\\O love\\B this\\O program\\O ,\\O it\\O is\\O superior\\B to\\O windows\\O movie\\O maker\\O .\\O", "sentiment": "positive"}]}, {"id": "158", "sentence": "I acually believe the issue is with the Nvidia grafics card , but still requires a return .", "postag": ["PRP", "RB", "VBP", "DT", "NN", "VBZ", "IN", "DT", "NNP", "NNS", "NN", ",", "CC", "RB", "VBZ", "DT", "NN", "."], "head": [3, 3, 0, 5, 11, 11, 11, 11, 10, 11, 3, 15, 15, 15, 3, 17, 15, 3], "deprel": ["nsubj", "advmod", "root", "det", "nsubj", "cop", "case", "det", "compound", "compound", "ccomp", "punct", "cc", "advmod", "conj", "det", "obj", "punct"], "triples": [{"uid": "158-0", "target_tags": "I\\O acually\\O believe\\O the\\O issue\\O is\\O with\\O the\\O Nvidia\\B grafics\\I card\\I ,\\O but\\O still\\O requires\\O a\\O return\\O .\\O", "opinion_tags": "I\\O acually\\O believe\\O the\\O issue\\B is\\O with\\O the\\O Nvidia\\O grafics\\O card\\O ,\\O but\\O still\\O requires\\O a\\O return\\O .\\O", "sentiment": "negative"}]}, {"id": "27", "sentence": "The ease of set up was terrific .", "postag": ["DT", "NN", "IN", "NN", "NN", "VBD", "JJ", "."], "head": [2, 7, 5, 5, 2, 7, 0, 7], "deprel": ["det", "nsubj", "case", "compound", "nmod", "cop", "root", "punct"], "triples": [{"uid": "27-0", "target_tags": "The\\O ease\\O of\\O set\\B up\\I was\\O terrific\\O .\\O", "opinion_tags": "The\\O ease\\B of\\O set\\O up\\O was\\O terrific\\B .\\O", "sentiment": "positive"}]}, {"id": "349", "sentence": "i also love having the extra calculator number set up on the keyboard which most laptops do not have .", "postag": ["PRP", "RB", "VBP", "VBG", "DT", "JJ", "JJ", "NN", "VBN", "RP", "IN", "DT", "NN", "WDT", "JJS", "NNS", "VBP", "RB", "VB", "."], "head": [3, 3, 0, 3, 8, 8, 8, 4, 8, 9, 13, 13, 9, 19, 16, 19, 19, 19, 13, 3], "deprel": ["nsubj", "advmod", "root", "xcomp", "det", "amod", "amod", "obj", "acl", "compound:prt", "case", "det", "obl", "obj", "amod", "nsubj", "aux", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "349-0", "target_tags": "i\\O also\\O love\\O having\\O the\\O extra\\O calculator\\O number\\O set\\O up\\O on\\O the\\O keyboard\\B which\\O most\\O laptops\\O do\\O not\\O have\\O .\\O", "opinion_tags": "i\\O also\\O love\\B having\\O the\\O extra\\O calculator\\O number\\O set\\O up\\O on\\O the\\O keyboard\\O which\\O most\\O laptops\\O do\\O not\\O have\\O .\\O", "sentiment": "positive"}]}, {"id": "3011", "sentence": "Great OS , fabulous improvements to the existing line bumping up the processor speed and adding the thunderbolt port .", "postag": ["JJ", "NN", ",", "JJ", "NNS", "IN", "DT", "VBG", "NN", "VBG", "RP", "DT", "NN", "NN", "CC", "VBG", "DT", "NN", "NN", "."], "head": [2, 0, 5, 5, 2, 9, 9, 9, 2, 9, 10, 14, 14, 10, 16, 10, 19, 19, 16, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "case", "det", "amod", "nmod", "acl", "compound:prt", "det", "compound", "obj", "cc", "conj", "det", "compound", "obj", "punct"], "triples": [{"uid": "3011-0", "target_tags": "Great\\O OS\\B ,\\O fabulous\\O improvements\\O to\\O the\\O existing\\O line\\O bumping\\O up\\O the\\O processor\\O speed\\O and\\O adding\\O the\\O thunderbolt\\O port\\O .\\O", "opinion_tags": "Great\\B OS\\O ,\\O fabulous\\B improvements\\O to\\O the\\O existing\\O line\\O bumping\\O up\\O the\\O processor\\O speed\\O and\\O adding\\O the\\O thunderbolt\\O port\\O .\\O", "sentiment": "positive"}, {"uid": "3011-1", "target_tags": "Great\\O OS\\O ,\\O fabulous\\O improvements\\O to\\O the\\O existing\\O line\\O bumping\\O up\\O the\\O processor\\B speed\\I and\\O adding\\O the\\O thunderbolt\\O port\\O .\\O", "opinion_tags": "Great\\O OS\\O ,\\O fabulous\\B improvements\\O to\\O the\\O existing\\O line\\O bumping\\O up\\O the\\O processor\\O speed\\O and\\O adding\\O the\\O thunderbolt\\O port\\O .\\O", "sentiment": "positive"}, {"uid": "3011-2", "target_tags": "Great\\O OS\\O ,\\O fabulous\\O improvements\\O to\\O the\\O existing\\O line\\O bumping\\O up\\O the\\O processor\\O speed\\O and\\O adding\\O the\\O thunderbolt\\B port\\I .\\O", "opinion_tags": "Great\\O OS\\O ,\\O fabulous\\O improvements\\O to\\O the\\O existing\\O line\\O bumping\\O up\\O the\\O processor\\O speed\\O and\\O adding\\B the\\O thunderbolt\\O port\\O .\\O", "sentiment": "positive"}]}, {"id": "1476", "sentence": "The image is great , and the soud is excelent .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 10, 10, 8, 10, 10, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "1476-0", "target_tags": "The\\O image\\B is\\O great\\O ,\\O and\\O the\\O soud\\O is\\O excelent\\O .\\O", "opinion_tags": "The\\O image\\O is\\O great\\B ,\\O and\\O the\\O soud\\O is\\O excelent\\O .\\O", "sentiment": "positive"}, {"uid": "1476-1", "target_tags": "The\\O image\\O is\\O great\\O ,\\O and\\O the\\O soud\\B is\\O excelent\\O .\\O", "opinion_tags": "The\\O image\\O is\\O great\\O ,\\O and\\O the\\O soud\\O is\\O excelent\\B .\\O", "sentiment": "positive"}]}, {"id": "1716", "sentence": "There was a little difficulty doing the migration as the firewire cable system ca n't be used with the iBook .", "postag": ["EX", "VBD", "DT", "JJ", "NN", "VBG", "DT", "NN", "IN", "DT", "NN", "NN", "NN", "MD", "RB", "VB", "VBN", "IN", "DT", "NNP", "."], "head": [2, 0, 5, 5, 2, 5, 8, 6, 17, 13, 12, 13, 17, 17, 17, 17, 2, 20, 20, 17, 2], "deprel": ["expl", "root", "det", "amod", "nsubj", "acl", "det", "obj", "mark", "det", "compound", "compound", "nsubj:pass", "aux", "advmod", "aux:pass", "advcl", "case", "det", "obl", "punct"], "triples": [{"uid": "1716-0", "target_tags": "There\\O was\\O a\\O little\\O difficulty\\O doing\\O the\\O migration\\O as\\O the\\O firewire\\B cable\\I system\\I ca\\O n't\\O be\\O used\\O with\\O the\\O iBook\\O .\\O", "opinion_tags": "There\\O was\\O a\\O little\\O difficulty\\B doing\\O the\\O migration\\O as\\O the\\O firewire\\O cable\\O system\\O ca\\B n't\\I be\\I used\\I with\\O the\\O iBook\\O .\\O", "sentiment": "negative"}]}, {"id": "2975", "sentence": "The only bad thing about it is they give you the worst batteries possible .", "postag": ["DT", "JJ", "JJ", "NN", "IN", "PRP", "VBZ", "PRP", "VBP", "PRP", "DT", "JJS", "NNS", "JJ", "."], "head": [4, 4, 4, 7, 6, 4, 0, 9, 7, 9, 13, 13, 9, 13, 7], "deprel": ["det", "amod", "amod", "nsubj", "case", "nmod", "root", "nsubj", "ccomp", "i<PERSON><PERSON>", "det", "amod", "obj", "amod", "punct"], "triples": [{"uid": "2975-0", "target_tags": "The\\O only\\O bad\\O thing\\O about\\O it\\O is\\O they\\O give\\O you\\O the\\O worst\\O batteries\\B possible\\O .\\O", "opinion_tags": "The\\O only\\O bad\\O thing\\O about\\O it\\O is\\O they\\O give\\O you\\O the\\O worst\\B batteries\\O possible\\O .\\O", "sentiment": "negative"}]}, {"id": "1409", "sentence": "But the quality , in general was less than the worth of the cheap laptop .", "postag": ["CC", "DT", "NN", ",", "IN", "NN", "VBD", "JJR", "IN", "DT", "JJ", "IN", "DT", "JJ", "NN", "."], "head": [8, 3, 8, 3, 6, 3, 8, 0, 11, 11, 8, 15, 15, 15, 11, 8], "deprel": ["cc", "det", "nsubj", "punct", "case", "nmod", "cop", "root", "case", "det", "obl", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "1409-0", "target_tags": "But\\O the\\O quality\\B ,\\O in\\O general\\O was\\O less\\O than\\O the\\O worth\\O of\\O the\\O cheap\\O laptop\\O .\\O", "opinion_tags": "But\\O the\\O quality\\O ,\\O in\\O general\\O was\\O less\\B than\\I the\\I worth\\I of\\O the\\O cheap\\O laptop\\O .\\O", "sentiment": "negative"}]}, {"id": "2380", "sentence": "previous laptops were pc 's , still have them , but the mac osx is a clean and smooth operating system .", "postag": ["JJ", "NNS", "VBD", "NNP", "POS", ",", "RB", "VBP", "PRP", ",", "CC", "DT", "NN", "NNP", "VBZ", "DT", "JJ", "CC", "JJ", "NN", "NN", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 8, 21, 21, 14, 14, 21, 21, 21, 21, 19, 17, 21, 4, 4], "deprel": ["amod", "nsubj", "cop", "root", "case", "punct", "advmod", "conj", "obj", "punct", "cc", "det", "compound", "nsubj", "cop", "det", "amod", "cc", "conj", "compound", "conj", "punct"], "triples": [{"uid": "2380-0", "target_tags": "previous\\O laptops\\O were\\O pc\\O 's\\O ,\\O still\\O have\\O them\\O ,\\O but\\O the\\O mac\\B osx\\I is\\O a\\O clean\\O and\\O smooth\\O operating\\O system\\O .\\O", "opinion_tags": "previous\\O laptops\\O were\\O pc\\O 's\\O ,\\O still\\O have\\O them\\O ,\\O but\\O the\\O mac\\O osx\\O is\\O a\\O clean\\B and\\O smooth\\B operating\\O system\\O .\\O", "sentiment": "positive"}, {"uid": "2380-1", "target_tags": "previous\\O laptops\\O were\\O pc\\O 's\\O ,\\O still\\O have\\O them\\O ,\\O but\\O the\\O mac\\O osx\\O is\\O a\\O clean\\O and\\O smooth\\O operating\\B system\\I .\\O", "opinion_tags": "previous\\O laptops\\O were\\O pc\\O 's\\O ,\\O still\\O have\\O them\\O ,\\O but\\O the\\O mac\\O osx\\O is\\O a\\O clean\\B and\\O smooth\\B operating\\O system\\O .\\O", "sentiment": "positive"}]}, {"id": "1865", "sentence": "Until I bought the Dell , I thought you just looked for what you wanted ( size , software , options , hardware ) and purchase the best deal you could find .", "postag": ["IN", "PRP", "VBD", "DT", "NNP", ",", "PRP", "VBD", "PRP", "RB", "VBD", "IN", "WP", "PRP", "VBD", "-LRB-", "NN", ",", "NN", ",", "NNS", ",", "NN", "-RRB-", "CC", "VB", "DT", "JJS", "NN", "PRP", "MD", "VB", "."], "head": [3, 3, 8, 5, 3, 8, 8, 0, 11, 11, 8, 13, 11, 15, 13, 17, 11, 19, 17, 21, 17, 23, 17, 17, 26, 11, 29, 29, 26, 32, 32, 29, 8], "deprel": ["mark", "nsubj", "advcl", "det", "obj", "punct", "nsubj", "root", "nsubj", "advmod", "ccomp", "case", "obl", "nsubj", "acl:relcl", "punct", "parataxis", "punct", "conj", "punct", "conj", "punct", "conj", "punct", "cc", "conj", "det", "amod", "obj", "nsubj", "aux", "acl:relcl", "punct"], "triples": [{"uid": "1865-0", "target_tags": "Until\\O I\\O bought\\O the\\O Dell\\O ,\\O I\\O thought\\O you\\O just\\O looked\\O for\\O what\\O you\\O wanted\\O (\\O size\\O ,\\O software\\O ,\\O options\\O ,\\O hardware\\B )\\O and\\O purchase\\O the\\O best\\O deal\\O you\\O could\\O find\\O .\\O", "opinion_tags": "Until\\O I\\O bought\\O the\\O Dell\\O ,\\O I\\O thought\\O you\\O just\\O looked\\O for\\O what\\O you\\O wanted\\O (\\O size\\O ,\\O software\\O ,\\O options\\O ,\\O hardware\\O )\\O and\\O purchase\\O the\\O best\\B deal\\O you\\O could\\O find\\O .\\O", "sentiment": "neutral"}]}, {"id": "563", "sentence": "the mcbook pro notebook will make it easy for you to write and read your emails at blazing speeds .", "postag": ["DT", "JJ", "NN", "NN", "MD", "VB", "PRP", "JJ", "IN", "PRP", "TO", "VB", "CC", "VB", "PRP$", "NNS", "IN", "VBG", "NNS", "."], "head": [4, 4, 4, 6, 6, 0, 6, 6, 12, 12, 12, 8, 14, 12, 16, 14, 19, 19, 14, 6], "deprel": ["det", "amod", "compound", "nsubj", "aux", "root", "expl", "xcomp", "mark", "nsubj", "mark", "ccomp", "cc", "conj", "nmod:poss", "obj", "case", "amod", "obl", "punct"], "triples": [{"uid": "563-0", "target_tags": "the\\O mcbook\\O pro\\O notebook\\O will\\O make\\O it\\O easy\\O for\\O you\\O to\\O write\\O and\\O read\\O your\\O emails\\O at\\O blazing\\O speeds\\B .\\O", "opinion_tags": "the\\O mcbook\\O pro\\O notebook\\O will\\O make\\O it\\O easy\\B for\\O you\\O to\\O write\\O and\\O read\\O your\\O emails\\O at\\O blazing\\B speeds\\O .\\O", "sentiment": "positive"}]}, {"id": "1128", "sentence": "Keyboard could use some trimming .", "postag": ["NN", "MD", "VB", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "aux", "root", "det", "obj", "punct"], "triples": [{"uid": "1128-0", "target_tags": "Keyboard\\B could\\O use\\O some\\O trimming\\O .\\O", "opinion_tags": "Keyboard\\O could\\O use\\O some\\O trimming\\B .\\O", "sentiment": "negative"}]}, {"id": "2101", "sentence": "Which is great I am running Vista Business and scored a 5.X on the index I have never seen a windows machine have a total score in the 5 's .", "postag": ["WDT", "VBZ", "JJ", "PRP", "VBP", "VBG", "NNP", "NNP", "CC", "VBD", "DT", "NNP", "IN", "DT", "NN", "PRP", "VBP", "RB", "VBN", "DT", "NNS", "NN", "VB", "DT", "JJ", "NN", "IN", "DT", "CD", "POS", "."], "head": [3, 3, 0, 6, 6, 3, 8, 6, 10, 6, 12, 10, 15, 15, 10, 19, 19, 19, 3, 22, 22, 19, 19, 26, 26, 23, 29, 29, 26, 29, 3], "deprel": ["nsubj", "cop", "root", "nsubj", "aux", "ccomp", "compound", "obj", "cc", "conj", "det", "obj", "case", "det", "obl", "nsubj", "aux", "advmod", "parataxis", "det", "compound", "obj", "ccomp", "det", "amod", "obj", "case", "det", "nmod", "case", "punct"], "triples": [{"uid": "2101-0", "target_tags": "Which\\O is\\O great\\O I\\O am\\O running\\O Vista\\B Business\\I and\\O scored\\O a\\O 5.X\\O on\\O the\\O index\\O I\\O have\\O never\\O seen\\O a\\O windows\\O machine\\O have\\O a\\O total\\O score\\O in\\O the\\O 5\\O 's\\O .\\O", "opinion_tags": "Which\\O is\\O great\\B I\\O am\\O running\\O Vista\\O Business\\O and\\O scored\\O a\\O 5.X\\O on\\O the\\O index\\O I\\O have\\O never\\O seen\\O a\\O windows\\O machine\\O have\\O a\\O total\\O score\\O in\\O the\\O 5\\O 's\\O .\\O", "sentiment": "positive"}]}, {"id": "1419", "sentence": "I actually contact <PERSON><PERSON><PERSON> before I started having problem and was given run around after I supplied serial number in order to delay me sending in laptop until after warrenty expired .", "postag": ["PRP", "RB", "VBP", "NNP", "IN", "PRP", "VBD", "VBG", "NN", "CC", "VBD", "VBN", "VBN", "RB", "IN", "PRP", "VBD", "NN", "NN", "IN", "NN", "TO", "VB", "PRP", "VBG", "RP", "NN", "IN", "IN", "NN", "VBD", "."], "head": [3, 3, 0, 3, 7, 7, 3, 7, 8, 12, 12, 3, 12, 13, 17, 17, 12, 19, 17, 23, 20, 23, 17, 23, 23, 25, 25, 31, 31, 31, 25, 3], "deprel": ["nsubj", "advmod", "root", "obj", "mark", "nsubj", "advcl", "xcomp", "obj", "cc", "aux:pass", "conj", "xcomp", "advmod", "mark", "nsubj", "advcl", "compound", "obj", "mark", "fixed", "mark", "advcl", "obj", "xcomp", "compound:prt", "obj", "mark", "mark", "nsubj", "advcl", "punct"], "triples": [{"uid": "1419-0", "target_tags": "I\\O actually\\O contact\\O Toshiba\\O before\\O I\\O started\\O having\\O problem\\O and\\O was\\O given\\O run\\O around\\O after\\O I\\O supplied\\O serial\\O number\\O in\\O order\\O to\\O delay\\O me\\O sending\\O in\\O laptop\\O until\\O after\\O warrenty\\B expired\\O .\\O", "opinion_tags": "I\\O actually\\O contact\\O Toshiba\\O before\\O I\\O started\\O having\\O problem\\O and\\O was\\O given\\O run\\O around\\O after\\O I\\O supplied\\O serial\\O number\\O in\\O order\\O to\\O delay\\O me\\O sending\\O in\\O laptop\\O until\\O after\\O warrenty\\O expired\\B .\\O", "sentiment": "negative"}]}, {"id": "2636", "sentence": "the key bindings take a little getting used to , but have loved the Macbook Pro .", "postag": ["DT", "JJ", "NNS", "VBP", "DT", "JJ", "VBG", "VBN", "IN", ",", "CC", "VBP", "VBN", "DT", "NNP", "NNP", "."], "head": [3, 3, 4, 0, 6, 8, 8, 4, 8, 13, 13, 13, 4, 16, 16, 13, 4], "deprel": ["det", "amod", "nsubj", "root", "det", "nsubj:pass", "aux:pass", "xcomp", "obl", "punct", "cc", "aux", "conj", "det", "compound", "obj", "punct"], "triples": [{"uid": "2636-0", "target_tags": "the\\O key\\B bindings\\I take\\O a\\O little\\O getting\\O used\\O to\\O ,\\O but\\O have\\O loved\\O the\\O Macbook\\O Pro\\O .\\O", "opinion_tags": "the\\O key\\O bindings\\O take\\O a\\O little\\O getting\\O used\\O to\\O ,\\O but\\O have\\O loved\\B the\\O Macbook\\O Pro\\O .\\O", "sentiment": "negative"}]}, {"id": "607", "sentence": "tons of bloatware and junk programs .", "postag": ["NNS", "IN", "NN", "CC", "NN", "NNS", "."], "head": [0, 6, 6, 5, 3, 1, 1], "deprel": ["root", "case", "compound", "cc", "conj", "nmod", "punct"], "triples": [{"uid": "607-0", "target_tags": "tons\\O of\\O bloatware\\O and\\O junk\\O programs\\B .\\O", "opinion_tags": "tons\\O of\\O bloatware\\O and\\O junk\\B programs\\O .\\O", "sentiment": "negative"}]}, {"id": "285", "sentence": "It doesnt work worth a damn .", "postag": ["PRP", "MD", "VB", "JJ", "DT", "NN", "."], "head": [3, 3, 0, 3, 6, 4, 3], "deprel": ["nsubj", "aux", "root", "xcomp", "det", "obj", "punct"], "triples": [{"uid": "285-0", "target_tags": "It\\O doesnt\\O work\\B worth\\O a\\O damn\\O .\\O", "opinion_tags": "It\\O doesnt\\B work\\O worth\\O a\\O damn\\B .\\O", "sentiment": "negative"}]}, {"id": "1438", "sentence": "Externally the keys on my keyboard are falling off , after a few uses the paint is rubbing off the button below the mouse pad and where the heals of my hands sit , and the screen has a terrible glare .", "postag": ["RB", "DT", "NNS", "IN", "PRP$", "NN", "VBP", "VBG", "RB", ",", "IN", "DT", "JJ", "NNS", "DT", "NN", "VBZ", "VBG", "IN", "DT", "NN", "IN", "DT", "NN", "NN", "CC", "WRB", "DT", "NNS", "IN", "PRP$", "NNS", "VBP", ",", "CC", "DT", "NN", "VBZ", "DT", "JJ", "NN", "."], "head": [8, 3, 8, 6, 6, 3, 8, 0, 8, 8, 18, 14, 14, 18, 16, 18, 18, 8, 21, 21, 18, 25, 25, 25, 21, 33, 33, 29, 33, 32, 32, 29, 18, 38, 38, 37, 38, 8, 41, 41, 38, 8], "deprel": ["advmod", "det", "nsubj", "case", "nmod:poss", "nmod", "aux", "root", "advmod", "punct", "mark", "det", "amod", "obl", "det", "nsubj", "aux", "advcl", "case", "det", "obl", "case", "det", "compound", "nmod", "cc", "mark", "det", "nsubj", "case", "nmod:poss", "nmod", "conj", "punct", "cc", "det", "nsubj", "conj", "det", "amod", "obj", "punct"], "triples": [{"uid": "1438-0", "target_tags": "Externally\\O the\\O keys\\B on\\O my\\O keyboard\\O are\\O falling\\O off\\O ,\\O after\\O a\\O few\\O uses\\O the\\O paint\\O is\\O rubbing\\O off\\O the\\O button\\O below\\O the\\O mouse\\O pad\\O and\\O where\\O the\\O heals\\O of\\O my\\O hands\\O sit\\O ,\\O and\\O the\\O screen\\O has\\O a\\O terrible\\O glare\\O .\\O", "opinion_tags": "Externally\\O the\\O keys\\O on\\O my\\O keyboard\\O are\\O falling\\B off\\I ,\\O after\\O a\\O few\\O uses\\O the\\O paint\\O is\\O rubbing\\O off\\O the\\O button\\O below\\O the\\O mouse\\O pad\\O and\\O where\\O the\\O heals\\O of\\O my\\O hands\\O sit\\O ,\\O and\\O the\\O screen\\O has\\O a\\O terrible\\O glare\\O .\\O", "sentiment": "negative"}, {"uid": "1438-1", "target_tags": "Externally\\O the\\O keys\\O on\\O my\\O keyboard\\B are\\O falling\\O off\\O ,\\O after\\O a\\O few\\O uses\\O the\\O paint\\O is\\O rubbing\\O off\\O the\\O button\\O below\\O the\\O mouse\\O pad\\O and\\O where\\O the\\O heals\\O of\\O my\\O hands\\O sit\\O ,\\O and\\O the\\O screen\\O has\\O a\\O terrible\\O glare\\O .\\O", "opinion_tags": "Externally\\O the\\O keys\\O on\\O my\\O keyboard\\O are\\O falling\\B off\\I ,\\O after\\O a\\O few\\O uses\\O the\\O paint\\O is\\O rubbing\\O off\\O the\\O button\\O below\\O the\\O mouse\\O pad\\O and\\O where\\O the\\O heals\\O of\\O my\\O hands\\O sit\\O ,\\O and\\O the\\O screen\\O has\\O a\\O terrible\\O glare\\O .\\O", "sentiment": "negative"}, {"uid": "1438-2", "target_tags": "Externally\\O the\\O keys\\O on\\O my\\O keyboard\\O are\\O falling\\O off\\O ,\\O after\\O a\\O few\\O uses\\O the\\O paint\\O is\\O rubbing\\O off\\O the\\O button\\B below\\I the\\I mouse\\I pad\\I and\\O where\\O the\\O heals\\O of\\O my\\O hands\\O sit\\O ,\\O and\\O the\\O screen\\O has\\O a\\O terrible\\O glare\\O .\\O", "opinion_tags": "Externally\\O the\\O keys\\O on\\O my\\O keyboard\\O are\\O falling\\O off\\O ,\\O after\\O a\\O few\\O uses\\O the\\O paint\\O is\\O rubbing\\B off\\I the\\O button\\O below\\O the\\O mouse\\O pad\\O and\\O where\\O the\\O heals\\O of\\O my\\O hands\\O sit\\O ,\\O and\\O the\\O screen\\O has\\O a\\O terrible\\O glare\\O .\\O", "sentiment": "negative"}, {"uid": "1438-3", "target_tags": "Externally\\O the\\O keys\\O on\\O my\\O keyboard\\O are\\O falling\\O off\\O ,\\O after\\O a\\O few\\O uses\\O the\\O paint\\O is\\O rubbing\\O off\\O the\\O button\\O below\\O the\\O mouse\\O pad\\O and\\O where\\O the\\O heals\\O of\\O my\\O hands\\O sit\\O ,\\O and\\O the\\O screen\\B has\\O a\\O terrible\\O glare\\O .\\O", "opinion_tags": "Externally\\O the\\O keys\\O on\\O my\\O keyboard\\O are\\O falling\\O off\\O ,\\O after\\O a\\O few\\O uses\\O the\\O paint\\O is\\O rubbing\\O off\\O the\\O button\\O below\\O the\\O mouse\\O pad\\O and\\O where\\O the\\O heals\\O of\\O my\\O hands\\O sit\\O ,\\O and\\O the\\O screen\\O has\\O a\\O terrible\\B glare\\I .\\O", "sentiment": "negative"}, {"uid": "1438-4", "target_tags": "Externally\\O the\\O keys\\O on\\O my\\O keyboard\\O are\\O falling\\O off\\O ,\\O after\\O a\\O few\\O uses\\O the\\O paint\\B is\\O rubbing\\O off\\O the\\O button\\O below\\O the\\O mouse\\O pad\\O and\\O where\\O the\\O heals\\O of\\O my\\O hands\\O sit\\O ,\\O and\\O the\\O screen\\O has\\O a\\O terrible\\O glare\\O .\\O", "opinion_tags": "Externally\\O the\\O keys\\O on\\O my\\O keyboard\\O are\\O falling\\O off\\O ,\\O after\\O a\\O few\\O uses\\O the\\O paint\\O is\\O rubbing\\B off\\I the\\O button\\O below\\O the\\O mouse\\O pad\\O and\\O where\\O the\\O heals\\O of\\O my\\O hands\\O sit\\O ,\\O and\\O the\\O screen\\O has\\O a\\O terrible\\O glare\\O .\\O", "sentiment": "negative"}]}, {"id": "1928", "sentence": "After about a week I finally got it back and was told that the motherboard had failed and so they installed a new motherboard .", "postag": ["IN", "RB", "DT", "NN", "PRP", "RB", "VBD", "PRP", "RB", "CC", "VBD", "VBN", "IN", "DT", "NN", "VBD", "VBN", "CC", "RB", "PRP", "VBD", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 7, 7, 7, 0, 7, 7, 12, 12, 7, 17, 15, 17, 17, 12, 21, 21, 21, 7, 24, 24, 21, 7], "deprel": ["case", "advmod", "det", "obl", "nsubj", "advmod", "root", "obj", "advmod", "cc", "aux:pass", "conj", "mark", "det", "nsubj", "aux", "ccomp", "cc", "advmod", "nsubj", "conj", "det", "amod", "obj", "punct"], "triples": [{"uid": "1928-0", "target_tags": "After\\O about\\O a\\O week\\O I\\O finally\\O got\\O it\\O back\\O and\\O was\\O told\\O that\\O the\\O motherboard\\B had\\O failed\\O and\\O so\\O they\\O installed\\O a\\O new\\O motherboard\\O .\\O", "opinion_tags": "After\\O about\\O a\\O week\\O I\\O finally\\O got\\O it\\O back\\O and\\O was\\O told\\O that\\O the\\O motherboard\\O had\\O failed\\B and\\O so\\O they\\O installed\\O a\\O new\\O motherboard\\O .\\O", "sentiment": "negative"}, {"uid": "1928-1", "target_tags": "After\\O about\\O a\\O week\\O I\\O finally\\O got\\O it\\O back\\O and\\O was\\O told\\O that\\O the\\O motherboard\\O had\\O failed\\O and\\O so\\O they\\O installed\\O a\\O new\\O motherboard\\B .\\O", "opinion_tags": "After\\O about\\O a\\O week\\O I\\O finally\\O got\\O it\\O back\\O and\\O was\\O told\\O that\\O the\\O motherboard\\O had\\O failed\\O and\\O so\\O they\\O installed\\O a\\O new\\B motherboard\\O .\\O", "sentiment": "neutral"}]}, {"id": "2246", "sentence": "It has many great programs , such as ILife , iPhotos and others .", "postag": ["PRP", "VBZ", "JJ", "JJ", "NNS", ",", "JJ", "IN", "NNP", ",", "NNP", "CC", "NNS", "."], "head": [2, 0, 5, 5, 2, 9, 9, 7, 5, 11, 9, 13, 9, 2], "deprel": ["nsubj", "root", "amod", "amod", "obj", "punct", "case", "fixed", "nmod", "punct", "conj", "cc", "conj", "punct"], "triples": [{"uid": "2246-0", "target_tags": "It\\O has\\O many\\O great\\O programs\\B ,\\O such\\O as\\O ILife\\O ,\\O iPhotos\\O and\\O others\\O .\\O", "opinion_tags": "It\\O has\\O many\\O great\\B programs\\O ,\\O such\\O as\\O ILife\\O ,\\O iPhotos\\O and\\O others\\O .\\O", "sentiment": "positive"}, {"uid": "2246-1", "target_tags": "It\\O has\\O many\\O great\\O programs\\O ,\\O such\\O as\\O ILife\\B ,\\O iPhotos\\O and\\O others\\O .\\O", "opinion_tags": "It\\O has\\O many\\O great\\B programs\\O ,\\O such\\O as\\O ILife\\O ,\\O iPhotos\\O and\\O others\\O .\\O", "sentiment": "positive"}, {"uid": "2246-2", "target_tags": "It\\O has\\O many\\O great\\O programs\\O ,\\O such\\O as\\O ILife\\O ,\\O iPhotos\\B and\\O others\\O .\\O", "opinion_tags": "It\\O has\\O many\\O great\\B programs\\O ,\\O such\\O as\\O ILife\\O ,\\O iPhotos\\O and\\O others\\O .\\O", "sentiment": "positive"}]}, {"id": "2689", "sentence": "I dislike the weight and size , cubersome .", "postag": ["PRP", "VBP", "DT", "NN", "CC", "NN", ",", "NN", "."], "head": [2, 0, 4, 2, 6, 4, 8, 4, 2], "deprel": ["nsubj", "root", "det", "obj", "cc", "conj", "punct", "conj", "punct"], "triples": [{"uid": "2689-0", "target_tags": "I\\O dislike\\O the\\O weight\\B and\\O size\\O ,\\O cubersome\\O .\\O", "opinion_tags": "I\\O dislike\\B the\\O weight\\O and\\O size\\O ,\\O cubersome\\B .\\O", "sentiment": "negative"}, {"uid": "2689-1", "target_tags": "I\\O dislike\\O the\\O weight\\O and\\O size\\B ,\\O cubersome\\O .\\O", "opinion_tags": "I\\O dislike\\B the\\O weight\\O and\\O size\\O ,\\O cubersome\\B .\\O", "sentiment": "negative"}]}, {"id": "2854", "sentence": "Then just the other day , my left `` mouse '' button snapped !", "postag": ["RB", "RB", "DT", "JJ", "NN", ",", "PRP$", "JJ", "``", "NN", "''", "NN", "VBD", "."], "head": [13, 5, 5, 5, 13, 13, 12, 12, 10, 12, 10, 13, 0, 13], "deprel": ["advmod", "advmod", "det", "amod", "obl:tmod", "punct", "nmod:poss", "amod", "punct", "compound", "punct", "nsubj", "root", "punct"], "triples": [{"uid": "2854-0", "target_tags": "Then\\O just\\O the\\O other\\O day\\O ,\\O my\\O left\\B ``\\I mouse\\I ''\\I button\\I snapped\\O !\\O", "opinion_tags": "Then\\O just\\O the\\O other\\O day\\O ,\\O my\\O left\\O ``\\O mouse\\O ''\\O button\\O snapped\\B !\\O", "sentiment": "negative"}]}, {"id": "2940", "sentence": "Runs smooth and quick .", "postag": ["VBZ", "JJ", "CC", "JJ", "."], "head": [0, 1, 4, 2, 1], "deprel": ["root", "xcomp", "cc", "conj", "punct"], "triples": [{"uid": "2940-0", "target_tags": "Runs\\B smooth\\O and\\O quick\\O .\\O", "opinion_tags": "Runs\\O smooth\\B and\\O quick\\B .\\O", "sentiment": "positive"}]}, {"id": "2992", "sentence": "Battery is not upgradable to a longer life battery .", "postag": ["NN", "VBZ", "RB", "JJ", "IN", "DT", "JJR", "NN", "NN", "."], "head": [4, 4, 4, 0, 9, 9, 9, 9, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "case", "det", "amod", "compound", "obl", "punct"], "triples": [{"uid": "2992-0", "target_tags": "Battery\\B is\\O not\\O upgradable\\O to\\O a\\O longer\\O life\\O battery\\O .\\O", "opinion_tags": "Battery\\O is\\O not\\B upgradable\\I to\\O a\\O longer\\O life\\O battery\\O .\\O", "sentiment": "negative"}]}, {"id": "2105", "sentence": "It 's software and speed enable it to do amazing things .", "postag": ["PRP", "VBZ", "NN", "CC", "NN", "VBP", "PRP", "TO", "VB", "JJ", "NNS", "."], "head": [3, 3, 0, 5, 3, 3, 6, 9, 6, 11, 9, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "conj", "obj", "mark", "xcomp", "amod", "obj", "punct"], "triples": [{"uid": "2105-0", "target_tags": "It\\O 's\\O software\\B and\\O speed\\O enable\\O it\\O to\\O do\\O amazing\\O things\\O .\\O", "opinion_tags": "It\\O 's\\O software\\O and\\O speed\\O enable\\O it\\O to\\O do\\O amazing\\B things\\O .\\O", "sentiment": "positive"}, {"uid": "2105-1", "target_tags": "It\\O 's\\O software\\O and\\O speed\\B enable\\O it\\O to\\O do\\O amazing\\O things\\O .\\O", "opinion_tags": "It\\O 's\\O software\\O and\\O speed\\O enable\\O it\\O to\\O do\\O amazing\\B things\\O .\\O", "sentiment": "positive"}]}, {"id": "1648", "sentence": "And the screen on this thing is absolutely amazing for high quality videos and movies and gaming .", "postag": ["CC", "DT", "NN", "IN", "DT", "NN", "VBZ", "RB", "JJ", "IN", "JJ", "NN", "NNS", "CC", "NNS", "CC", "NN", "."], "head": [9, 3, 9, 6, 6, 3, 9, 9, 0, 13, 12, 13, 9, 15, 13, 17, 13, 9], "deprel": ["cc", "det", "nsubj", "case", "det", "nmod", "cop", "advmod", "root", "case", "amod", "compound", "obl", "cc", "conj", "cc", "conj", "punct"], "triples": [{"uid": "1648-0", "target_tags": "And\\O the\\O screen\\B on\\O this\\O thing\\O is\\O absolutely\\O amazing\\O for\\O high\\O quality\\O videos\\O and\\O movies\\O and\\O gaming\\O .\\O", "opinion_tags": "And\\O the\\O screen\\O on\\O this\\O thing\\O is\\O absolutely\\O amazing\\B for\\O high\\O quality\\O videos\\O and\\O movies\\O and\\O gaming\\O .\\O", "sentiment": "positive"}, {"uid": "1648-1", "target_tags": "And\\O the\\O screen\\O on\\O this\\O thing\\O is\\O absolutely\\O amazing\\O for\\O high\\O quality\\O videos\\O and\\O movies\\O and\\O gaming\\B .\\O", "opinion_tags": "And\\O the\\O screen\\O on\\O this\\O thing\\O is\\O absolutely\\O amazing\\O for\\O high\\B quality\\O videos\\O and\\O movies\\O and\\O gaming\\O .\\O", "sentiment": "positive"}]}, {"id": "2918", "sentence": "If you check you will find the same notebook with the above missing ports and a dual core AMD or Intel processor .", "postag": ["IN", "PRP", "VBP", "PRP", "MD", "VB", "DT", "JJ", "NN", "IN", "DT", "JJ", "JJ", "NNS", "CC", "DT", "JJ", "NN", "NN", "CC", "NNP", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 9, 9, 6, 14, 14, 14, 14, 9, 22, 22, 22, 19, 22, 21, 19, 14, 6], "deprel": ["mark", "nsubj", "advcl", "nsubj", "aux", "root", "det", "amod", "obj", "case", "det", "amod", "amod", "nmod", "cc", "det", "amod", "compound", "compound", "cc", "conj", "conj", "punct"], "triples": [{"uid": "2918-0", "target_tags": "If\\O you\\O check\\O you\\O will\\O find\\O the\\O same\\O notebook\\O with\\O the\\O above\\O missing\\O ports\\B and\\O a\\O dual\\O core\\O AMD\\O or\\O Intel\\O processor\\O .\\O", "opinion_tags": "If\\O you\\O check\\O you\\O will\\O find\\O the\\O same\\O notebook\\O with\\O the\\O above\\O missing\\B ports\\O and\\O a\\O dual\\O core\\O AMD\\O or\\O Intel\\O processor\\O .\\O", "sentiment": "neutral"}]}, {"id": "1592", "sentence": "Enjoy that Toshib force and durability unparalleled", "postag": ["VB", "IN", "NN", "NN", "CC", "NN", "JJ"], "head": [0, 7, 4, 1, 6, 4, 4], "deprel": ["root", "mark", "compound", "obj", "cc", "conj", "amod"], "triples": [{"uid": "1592-0", "target_tags": "Enjoy\\O that\\O Toshib\\O force\\B and\\O durability\\O unparalleled\\O", "opinion_tags": "Enjoy\\B that\\O Toshib\\O force\\O and\\O durability\\O unparalleled\\O", "sentiment": "positive"}, {"uid": "1592-1", "target_tags": "Enjoy\\O that\\O Toshib\\O force\\O and\\O durability\\B unparalleled\\O", "opinion_tags": "Enjoy\\B that\\O Toshib\\O force\\O and\\O durability\\O unparalleled\\B", "sentiment": "positive"}]}, {"id": "63", "sentence": "I 've also had to have the keyboard replaced at my expense .", "postag": ["PRP", "VBP", "RB", "VBN", "TO", "VB", "DT", "NN", "VBN", "IN", "PRP$", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 6, 12, 12, 9, 4], "deprel": ["nsubj", "aux", "advmod", "root", "mark", "xcomp", "det", "obj", "xcomp", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "63-0", "target_tags": "I\\O 've\\O also\\O had\\O to\\O have\\O the\\O keyboard\\B replaced\\O at\\O my\\O expense\\O .\\O", "opinion_tags": "I\\O 've\\O also\\O had\\O to\\O have\\O the\\O keyboard\\O replaced\\B at\\O my\\O expense\\O .\\O", "sentiment": "negative"}]}, {"id": "2510", "sentence": "Waiting for the i7 was well worth it , great value for the price .", "postag": ["VBG", "IN", "DT", "NNP", "VBD", "RB", "JJ", "PRP", ",", "JJ", "NN", "IN", "DT", "NN", "."], "head": [7, 4, 4, 1, 7, 7, 0, 7, 11, 11, 7, 14, 14, 11, 7], "deprel": ["csubj", "case", "det", "obl", "cop", "advmod", "root", "obj", "punct", "amod", "parataxis", "case", "det", "nmod", "punct"], "triples": [{"uid": "2510-0", "target_tags": "Waiting\\O for\\O the\\O i7\\O was\\O well\\O worth\\O it\\O ,\\O great\\O value\\O for\\O the\\O price\\B .\\O", "opinion_tags": "Waiting\\O for\\O the\\O i7\\O was\\O well\\O worth\\O it\\O ,\\O great\\B value\\O for\\O the\\O price\\O .\\O", "sentiment": "positive"}, {"uid": "2510-1", "target_tags": "Waiting\\O for\\O the\\O i7\\B was\\O well\\O worth\\O it\\O ,\\O great\\O value\\O for\\O the\\O price\\O .\\O", "opinion_tags": "Waiting\\O for\\O the\\O i7\\O was\\O well\\B worth\\I it\\O ,\\O great\\O value\\O for\\O the\\O price\\O .\\O", "sentiment": "positive"}]}, {"id": "277", "sentence": "The internet was locekd and froze every time it was trying to be used , and the command prompt would not work at all .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "VBD", "DT", "NN", "PRP", "VBD", "VBG", "TO", "VB", "VBN", ",", "CC", "DT", "NN", "NN", "MD", "RB", "VB", "IN", "DT", "."], "head": [2, 4, 4, 0, 6, 4, 8, 6, 11, 11, 8, 14, 14, 11, 22, 22, 19, 19, 22, 22, 22, 4, 24, 22, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "det", "obl:tmod", "nsubj", "aux", "acl:relcl", "mark", "aux:pass", "xcomp", "punct", "cc", "det", "compound", "nsubj", "aux", "advmod", "conj", "case", "obl", "punct"], "triples": [{"uid": "277-0", "target_tags": "The\\O internet\\B was\\O locekd\\O and\\O froze\\O every\\O time\\O it\\O was\\O trying\\O to\\O be\\O used\\O ,\\O and\\O the\\O command\\O prompt\\O would\\O not\\O work\\O at\\O all\\O .\\O", "opinion_tags": "The\\O internet\\O was\\O locekd\\B and\\O froze\\B every\\O time\\O it\\O was\\O trying\\O to\\O be\\O used\\O ,\\O and\\O the\\O command\\O prompt\\O would\\O not\\O work\\O at\\O all\\O .\\O", "sentiment": "negative"}, {"uid": "277-1", "target_tags": "The\\O internet\\O was\\O locekd\\O and\\O froze\\O every\\O time\\O it\\O was\\O trying\\O to\\O be\\O used\\O ,\\O and\\O the\\O command\\B prompt\\I would\\O not\\O work\\O at\\O all\\O .\\O", "opinion_tags": "The\\O internet\\O was\\O locekd\\O and\\O froze\\O every\\O time\\O it\\O was\\O trying\\O to\\O be\\O used\\O ,\\O and\\O the\\O command\\O prompt\\O would\\O not\\B work\\I at\\O all\\O .\\O", "sentiment": "negative"}]}, {"id": "2426", "sentence": "I personally like the gaming look but needed a machine that delivered gaming performance while still looking professional in front of my customers .", "postag": ["PRP", "RB", "VBP", "DT", "NN", "NN", "CC", "VBD", "DT", "NN", "WDT", "VBD", "NN", "NN", "IN", "RB", "VBG", "JJ", "IN", "NN", "IN", "PRP$", "NNS", "."], "head": [3, 3, 0, 6, 6, 3, 8, 3, 10, 8, 12, 10, 14, 12, 17, 17, 12, 17, 20, 17, 23, 23, 20, 3], "deprel": ["nsubj", "advmod", "root", "det", "compound", "obj", "cc", "conj", "det", "obj", "nsubj", "acl:relcl", "compound", "obj", "mark", "advmod", "advcl", "xcomp", "case", "obl", "case", "nmod:poss", "nmod", "punct"], "triples": [{"uid": "2426-0", "target_tags": "I\\O personally\\O like\\O the\\O gaming\\B look\\I but\\O needed\\O a\\O machine\\O that\\O delivered\\O gaming\\O performance\\O while\\O still\\O looking\\O professional\\O in\\O front\\O of\\O my\\O customers\\O .\\O", "opinion_tags": "I\\O personally\\O like\\B the\\O gaming\\O look\\O but\\O needed\\O a\\O machine\\O that\\O delivered\\O gaming\\O performance\\O while\\O still\\O looking\\O professional\\O in\\O front\\O of\\O my\\O customers\\O .\\O", "sentiment": "positive"}]}, {"id": "2257", "sentence": "Awesome graphics !", "postag": ["JJ", "NNS", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "2257-0", "target_tags": "Awesome\\O graphics\\B !\\O", "opinion_tags": "Awesome\\B graphics\\O !\\O", "sentiment": "positive"}]}, {"id": "2529", "sentence": "Well , I have to say since I bought my Mac , I wo n't ever go back to any Windows .", "postag": ["UH", ",", "PRP", "VBP", "TO", "VB", "IN", "PRP", "VBD", "PRP$", "NNP", ",", "PRP", "MD", "RB", "RB", "VB", "RB", "IN", "DT", "NNS", "."], "head": [4, 4, 4, 0, 6, 4, 9, 9, 6, 11, 9, 4, 17, 17, 17, 17, 4, 17, 21, 21, 18, 4], "deprel": ["discourse", "punct", "nsubj", "root", "mark", "xcomp", "mark", "nsubj", "advcl", "nmod:poss", "obj", "punct", "nsubj", "aux", "advmod", "advmod", "parataxis", "advmod", "case", "det", "obl", "punct"], "triples": [{"uid": "2529-0", "target_tags": "Well\\O ,\\O I\\O have\\O to\\O say\\O since\\O I\\O bought\\O my\\O Mac\\O ,\\O I\\O wo\\O n't\\O ever\\O go\\O back\\O to\\O any\\O Windows\\B .\\O", "opinion_tags": "Well\\O ,\\O I\\O have\\O to\\O say\\O since\\O I\\O bought\\O my\\O Mac\\O ,\\O I\\O wo\\B n't\\I ever\\I go\\I back\\I to\\O any\\O Windows\\O .\\O", "sentiment": "negative"}]}, {"id": "582", "sentence": "The apple care has never failed me , and I expect it to be the same for this computer as well .", "postag": ["DT", "NNP", "NN", "VBZ", "RB", "VBN", "PRP", ",", "CC", "PRP", "VBP", "PRP", "TO", "VB", "DT", "JJ", "IN", "DT", "NN", "RB", "RB", "."], "head": [3, 3, 6, 6, 6, 0, 6, 11, 11, 11, 6, 11, 16, 16, 16, 11, 19, 19, 16, 16, 20, 6], "deprel": ["det", "compound", "nsubj", "aux", "advmod", "root", "obj", "punct", "cc", "nsubj", "conj", "obj", "mark", "cop", "det", "xcomp", "case", "det", "obl", "advmod", "fixed", "punct"], "triples": [{"uid": "582-0", "target_tags": "The\\O apple\\B care\\I has\\O never\\O failed\\O me\\O ,\\O and\\O I\\O expect\\O it\\O to\\O be\\O the\\O same\\O for\\O this\\O computer\\O as\\O well\\O .\\O", "opinion_tags": "The\\O apple\\O care\\O has\\O never\\B failed\\I me\\O ,\\O and\\O I\\O expect\\O it\\O to\\O be\\O the\\O same\\O for\\O this\\O computer\\O as\\O well\\O .\\O", "sentiment": "positive"}]}, {"id": "2937", "sentence": "I would recommend it just because of the internet speed probably because thats the only thing i really care about .", "postag": ["PRP", "MD", "VB", "PRP", "RB", "IN", "IN", "DT", "NN", "NN", "RB", "IN", "RB", "DT", "JJ", "NN", "PRP", "RB", "VBP", "IN", "."], "head": [3, 3, 0, 3, 10, 10, 6, 10, 10, 3, 16, 16, 12, 16, 16, 3, 19, 19, 16, 19, 3], "deprel": ["nsubj", "aux", "root", "obj", "advmod", "case", "fixed", "det", "compound", "obl", "advmod", "case", "fixed", "det", "amod", "obl", "nsubj", "advmod", "acl:relcl", "obl", "punct"], "triples": [{"uid": "2937-0", "target_tags": "I\\O would\\O recommend\\O it\\O just\\O because\\O of\\O the\\O internet\\B speed\\I probably\\O because\\O thats\\O the\\O only\\O thing\\O i\\O really\\O care\\O about\\O .\\O", "opinion_tags": "I\\O would\\O recommend\\B it\\O just\\O because\\O of\\O the\\O internet\\O speed\\O probably\\O because\\O thats\\O the\\O only\\O thing\\O i\\O really\\O care\\O about\\O .\\O", "sentiment": "positive"}]}, {"id": "653", "sentence": "It played various games without problems and ran aero smoothly and flawlessly .", "postag": ["PRP", "VBD", "JJ", "NNS", "IN", "NNS", "CC", "VBD", "NN", "RB", "CC", "RB", "."], "head": [2, 0, 4, 2, 6, 2, 8, 2, 8, 8, 12, 10, 2], "deprel": ["nsubj", "root", "amod", "obj", "case", "obl", "cc", "conj", "obj", "advmod", "cc", "conj", "punct"], "triples": [{"uid": "653-0", "target_tags": "It\\O played\\O various\\O games\\B without\\O problems\\O and\\O ran\\O aero\\O smoothly\\O and\\O flawlessly\\O .\\O", "opinion_tags": "It\\O played\\O various\\O games\\O without\\B problems\\I and\\O ran\\O aero\\O smoothly\\O and\\O flawlessly\\O .\\O", "sentiment": "neutral"}, {"uid": "653-1", "target_tags": "It\\O played\\O various\\O games\\O without\\O problems\\O and\\O ran\\O aero\\B smoothly\\O and\\O flawlessly\\O .\\O", "opinion_tags": "It\\O played\\O various\\O games\\O without\\O problems\\O and\\O ran\\O aero\\O smoothly\\B and\\O flawlessly\\B .\\O", "sentiment": "neutral"}]}, {"id": "2995", "sentence": "The wheel that turns the volume up and down does n't work in real time .", "postag": ["DT", "NN", "WDT", "VBZ", "DT", "NN", "RP", "CC", "RB", "VBZ", "RB", "VB", "IN", "JJ", "NN", "."], "head": [2, 12, 4, 2, 6, 4, 4, 9, 7, 12, 12, 0, 15, 15, 12, 12], "deprel": ["det", "nsubj", "nsubj", "acl:relcl", "det", "obj", "compound:prt", "cc", "conj", "aux", "advmod", "root", "case", "amod", "obl", "punct"], "triples": [{"uid": "2995-0", "target_tags": "The\\O wheel\\B that\\O turns\\O the\\O volume\\O up\\O and\\O down\\O does\\O n't\\O work\\O in\\O real\\O time\\O .\\O", "opinion_tags": "The\\O wheel\\O that\\O turns\\O the\\O volume\\O up\\O and\\O down\\O does\\B n't\\I work\\I in\\O real\\O time\\O .\\O", "sentiment": "negative"}]}, {"id": "2541", "sentence": "the only fact i dont like about apples is they generally use safari and i dont use safari but after i install Mozzilla firfox i love every single bit about it .", "postag": ["DT", "JJ", "NN", "PRP", "MD", "VB", "IN", "NNS", "VBZ", "PRP", "RB", "VBP", "NN", "CC", "PRP", "MD", "VB", "NN", "CC", "IN", "PRP", "VBP", "NNP", "CC", "PRP", "VBP", "DT", "JJ", "NN", "IN", "PRP", "."], "head": [3, 3, 9, 6, 6, 3, 8, 6, 0, 12, 12, 9, 12, 17, 17, 17, 9, 17, 26, 22, 22, 26, 22, 22, 26, 9, 29, 29, 26, 31, 29, 9], "deprel": ["det", "amod", "nsubj", "nsubj", "aux", "acl:relcl", "case", "obl", "root", "nsubj", "advmod", "ccomp", "obj", "cc", "nsubj", "aux", "conj", "obj", "cc", "mark", "nsubj", "advcl", "obj", "obj", "nsubj", "conj", "det", "amod", "obj", "case", "nmod", "punct"], "triples": [{"uid": "2541-0", "target_tags": "the\\O only\\O fact\\O i\\O dont\\O like\\O about\\O apples\\O is\\O they\\O generally\\O use\\O safari\\O and\\O i\\O dont\\O use\\O safari\\B but\\O after\\O i\\O install\\O Mozzilla\\O firfox\\O i\\O love\\O every\\O single\\O bit\\O about\\O it\\O .\\O", "opinion_tags": "the\\O only\\O fact\\O i\\O dont\\O like\\O about\\O apples\\O is\\O they\\O generally\\O use\\O safari\\O and\\O i\\O dont\\B use\\I safari\\O but\\O after\\O i\\O install\\O Mozzilla\\O firfox\\O i\\O love\\O every\\O single\\O bit\\O about\\O it\\O .\\O", "sentiment": "negative"}, {"uid": "2541-1", "target_tags": "the\\O only\\O fact\\O i\\O dont\\O like\\O about\\O apples\\O is\\O they\\O generally\\O use\\O safari\\O and\\O i\\O dont\\O use\\O safari\\O but\\O after\\O i\\O install\\O Mozzilla\\B firfox\\I i\\O love\\O every\\O single\\O bit\\O about\\O it\\O .\\O", "opinion_tags": "the\\O only\\O fact\\O i\\O dont\\O like\\O about\\O apples\\O is\\O they\\O generally\\O use\\O safari\\O and\\O i\\O dont\\O use\\O safari\\O but\\O after\\O i\\O install\\O Mozzilla\\O firfox\\O i\\O love\\B every\\O single\\O bit\\O about\\O it\\O .\\O", "sentiment": "positive"}]}, {"id": "1952", "sentence": "I had read online that some users were having sound problems .", "postag": ["PRP", "VBD", "VBN", "RB", "IN", "DT", "NNS", "VBD", "VBG", "NN", "NNS", "."], "head": [3, 3, 0, 3, 9, 7, 9, 9, 3, 11, 9, 3], "deprel": ["nsubj", "aux", "root", "advmod", "mark", "det", "nsubj", "aux", "ccomp", "compound", "obj", "punct"], "triples": [{"uid": "1952-0", "target_tags": "I\\O had\\O read\\O online\\O that\\O some\\O users\\O were\\O having\\O sound\\B problems\\O .\\O", "opinion_tags": "I\\O had\\O read\\O online\\O that\\O some\\O users\\O were\\O having\\O sound\\O problems\\B .\\O", "sentiment": "negative"}]}, {"id": "2789", "sentence": "Easy to start up and does not overheat as much as other laptops .", "postag": ["JJ", "TO", "VB", "RP", "CC", "VBZ", "RB", "VB", "RB", "RB", "IN", "JJ", "NNS", "."], "head": [0, 3, 1, 3, 8, 8, 8, 1, 10, 8, 13, 13, 8, 1], "deprel": ["root", "mark", "csubj", "compound:prt", "cc", "aux", "advmod", "conj", "advmod", "advmod", "case", "amod", "obl", "punct"], "triples": [{"uid": "2789-0", "target_tags": "Easy\\O to\\O start\\B up\\I and\\O does\\O not\\O overheat\\O as\\O much\\O as\\O other\\O laptops\\O .\\O", "opinion_tags": "Easy\\B to\\O start\\O up\\O and\\O does\\O not\\O overheat\\O as\\O much\\O as\\O other\\O laptops\\O .\\O", "sentiment": "positive"}]}, {"id": "881", "sentence": "After 2 months of complaints , <PERSON><PERSON> finally sent the right power supply to my techies .", "postag": ["IN", "CD", "NNS", "IN", "NNS", ",", "NNP", "RB", "VBD", "DT", "JJ", "NN", "NN", "IN", "PRP$", "NNS", "."], "head": [3, 3, 9, 5, 3, 9, 9, 9, 0, 13, 13, 13, 9, 16, 16, 9, 9], "deprel": ["case", "nummod", "obl", "case", "nmod", "punct", "nsubj", "advmod", "root", "det", "amod", "compound", "obj", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "881-0", "target_tags": "After\\O 2\\O months\\O of\\O complaints\\O ,\\O Asus\\O finally\\O sent\\O the\\O right\\O power\\B supply\\I to\\O my\\O techies\\O .\\O", "opinion_tags": "After\\O 2\\O months\\O of\\O complaints\\O ,\\O Asus\\O finally\\O sent\\O the\\O right\\B power\\O supply\\O to\\O my\\O techies\\O .\\O", "sentiment": "neutral"}]}, {"id": "732", "sentence": "Although i do believe that Windows operating system may be to fault for some of the problems .", "postag": ["IN", "PRP", "VBP", "VB", "IN", "NNS", "NN", "NN", "MD", "VB", "IN", "NN", "IN", "DT", "IN", "DT", "NNS", "."], "head": [4, 4, 4, 0, 12, 8, 8, 12, 12, 12, 12, 4, 14, 12, 17, 17, 14, 4], "deprel": ["mark", "nsubj", "aux", "root", "mark", "compound", "compound", "nsubj", "aux", "cop", "case", "ccomp", "case", "obl", "case", "det", "nmod", "punct"], "triples": [{"uid": "732-0", "target_tags": "Although\\O i\\O do\\O believe\\O that\\O Windows\\B operating\\I system\\I may\\O be\\O to\\O fault\\O for\\O some\\O of\\O the\\O problems\\O .\\O", "opinion_tags": "Although\\O i\\O do\\O believe\\O that\\O Windows\\O operating\\O system\\O may\\O be\\O to\\O fault\\B for\\O some\\O of\\O the\\O problems\\O .\\O", "sentiment": "negative"}]}, {"id": "2572", "sentence": "Very long-life battery ( up to 10-11 hours depending on how you configure power level settings ) .", "postag": ["RB", "JJ", "NN", "-LRB-", "IN", "IN", "CD", "NNS", "VBG", "IN", "WRB", "PRP", "VBP", "NN", "NN", "NNS", "-RRB-", "."], "head": [2, 3, 0, 3, 7, 5, 8, 3, 11, 11, 13, 13, 3, 15, 16, 13, 3, 3], "deprel": ["advmod", "amod", "root", "punct", "advmod", "fixed", "nummod", "parataxis", "case", "case", "mark", "nsubj", "advcl", "compound", "compound", "obj", "punct", "punct"], "triples": [{"uid": "2572-0", "target_tags": "Very\\O long-life\\O battery\\B (\\O up\\O to\\O 10-11\\O hours\\O depending\\O on\\O how\\O you\\O configure\\O power\\O level\\O settings\\O )\\O .\\O", "opinion_tags": "Very\\O long-life\\B battery\\O (\\O up\\O to\\O 10-11\\O hours\\O depending\\O on\\O how\\O you\\O configure\\O power\\O level\\O settings\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "1850", "sentence": "After that I turned to email in my next vain help to get them to acknowledge that the warranty was still valid .", "postag": ["IN", "IN", "PRP", "VBD", "TO", "VB", "IN", "PRP$", "JJ", "JJ", "NN", "TO", "VB", "PRP", "TO", "VB", "IN", "DT", "NN", "VBD", "RB", "JJ", "."], "head": [4, 1, 4, 0, 6, 4, 11, 11, 11, 11, 6, 13, 6, 13, 16, 13, 22, 19, 22, 22, 22, 16, 4], "deprel": ["mark", "fixed", "nsubj", "root", "mark", "xcomp", "case", "nmod:poss", "amod", "amod", "obl", "mark", "advcl", "obj", "mark", "xcomp", "mark", "det", "nsubj", "cop", "advmod", "ccomp", "punct"], "triples": [{"uid": "1850-0", "target_tags": "After\\O that\\O I\\O turned\\O to\\O email\\O in\\O my\\O next\\O vain\\O help\\O to\\O get\\O them\\O to\\O acknowledge\\O that\\O the\\O warranty\\B was\\O still\\O valid\\O .\\O", "opinion_tags": "After\\O that\\O I\\O turned\\O to\\O email\\O in\\O my\\O next\\O vain\\O help\\O to\\O get\\O them\\O to\\O acknowledge\\O that\\O the\\O warranty\\O was\\O still\\O valid\\B .\\O", "sentiment": "neutral"}]}, {"id": "2694", "sentence": "Peformance is good for the price .", "postag": ["NN", "VBZ", "JJ", "IN", "DT", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 3], "deprel": ["nsubj", "cop", "root", "case", "det", "obl", "punct"], "triples": [{"uid": "2694-0", "target_tags": "Peformance\\B is\\O good\\O for\\O the\\O price\\O .\\O", "opinion_tags": "Peformance\\O is\\O good\\B for\\O the\\O price\\O .\\O", "sentiment": "positive"}, {"uid": "2694-1", "target_tags": "Peformance\\O is\\O good\\O for\\O the\\O price\\B .\\O", "opinion_tags": "Peformance\\O is\\O good\\B for\\O the\\O price\\O .\\O", "sentiment": "positive"}]}, {"id": "612", "sentence": "Apple care included .", "postag": ["NNP", "NN", "VBD", "."], "head": [2, 3, 0, 3], "deprel": ["compound", "nsubj", "root", "punct"], "triples": [{"uid": "612-0", "target_tags": "Apple\\B care\\I included\\O .\\O", "opinion_tags": "Apple\\O care\\O included\\B .\\O", "sentiment": "neutral"}]}, {"id": "392", "sentence": "I custom ordered the machine from HP and could NOT understand the techie due to his accent .", "postag": ["PRP", "RB", "VBD", "DT", "NN", "IN", "NNP", "CC", "MD", "RB", "VB", "DT", "NN", "IN", "IN", "PRP$", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 3, 11, 11, 11, 3, 13, 11, 17, 14, 17, 11, 3], "deprel": ["nsubj", "advmod", "root", "det", "obj", "case", "obl", "cc", "aux", "advmod", "conj", "det", "obj", "case", "fixed", "nmod:poss", "obl", "punct"], "triples": [{"uid": "392-0", "target_tags": "I\\O custom\\O ordered\\O the\\O machine\\O from\\O HP\\O and\\O could\\O NOT\\O understand\\O the\\O techie\\B due\\O to\\O his\\O accent\\O .\\O", "opinion_tags": "I\\O custom\\O ordered\\O the\\O machine\\O from\\O HP\\O and\\O could\\O NOT\\B understand\\I the\\O techie\\O due\\O to\\O his\\O accent\\O .\\O", "sentiment": "negative"}]}, {"id": "1964", "sentence": "This computer was so challenging to carry and handle .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "TO", "VB", "CC", "VB", "."], "head": [2, 5, 5, 5, 0, 7, 5, 9, 7, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "mark", "advcl", "cc", "conj", "punct"], "triples": [{"uid": "1964-0", "target_tags": "This\\O computer\\O was\\O so\\O challenging\\O to\\O carry\\B and\\O handle\\O .\\O", "opinion_tags": "This\\O computer\\O was\\O so\\O challenging\\B to\\O carry\\O and\\O handle\\O .\\O", "sentiment": "negative"}, {"uid": "1964-1", "target_tags": "This\\O computer\\O was\\O so\\O challenging\\O to\\O carry\\O and\\O handle\\B .\\O", "opinion_tags": "This\\O computer\\O was\\O so\\O challenging\\B to\\O carry\\O and\\O handle\\O .\\O", "sentiment": "negative"}]}, {"id": "1713", "sentence": "Love the speed , especially !", "postag": ["VBP", "DT", "NN", ",", "RB", "."], "head": [0, 3, 1, 1, 1, 1], "deprel": ["root", "det", "obj", "punct", "advmod", "punct"], "triples": [{"uid": "1713-0", "target_tags": "Love\\O the\\O speed\\B ,\\O especially\\O !\\O", "opinion_tags": "Love\\B the\\O speed\\O ,\\O especially\\O !\\O", "sentiment": "positive"}]}, {"id": "2709", "sentence": "Keys stick periodically and I havent had the laptop for 45 days yet .", "postag": ["NNS", "VBP", "RB", "CC", "PRP", "VBP", "VBN", "DT", "NN", "IN", "CD", "NNS", "RB", "."], "head": [2, 0, 2, 7, 7, 7, 2, 9, 7, 12, 12, 7, 7, 2], "deprel": ["nsubj", "root", "advmod", "cc", "nsubj", "aux", "conj", "det", "obj", "case", "nummod", "obl", "advmod", "punct"], "triples": [{"uid": "2709-0", "target_tags": "Keys\\B stick\\O periodically\\O and\\O I\\O havent\\O had\\O the\\O laptop\\O for\\O 45\\O days\\O yet\\O .\\O", "opinion_tags": "Keys\\O stick\\B periodically\\O and\\O I\\O havent\\O had\\O the\\O laptop\\O for\\O 45\\O days\\O yet\\O .\\O", "sentiment": "negative"}]}, {"id": "755", "sentence": "Summary : HP knew they were shipping out bad BIOS and did nothing proactive to resolve it .", "postag": ["NN", ":", "NNP", "VBD", "PRP", "VBD", "VBG", "RP", "JJ", "NN", "CC", "VBD", "NN", "JJ", "TO", "VB", "PRP", "."], "head": [0, 1, 4, 1, 7, 7, 4, 7, 10, 7, 12, 7, 12, 13, 16, 14, 16, 1], "deprel": ["root", "punct", "nsubj", "appos", "nsubj", "aux", "ccomp", "compound:prt", "amod", "obj", "cc", "conj", "obj", "amod", "mark", "advcl", "obj", "punct"], "triples": [{"uid": "755-0", "target_tags": "Summary\\O :\\O HP\\O knew\\O they\\O were\\O shipping\\O out\\O bad\\O BIOS\\B and\\O did\\O nothing\\O proactive\\O to\\O resolve\\O it\\O .\\O", "opinion_tags": "Summary\\O :\\O HP\\O knew\\O they\\O were\\O shipping\\O out\\O bad\\B BIOS\\O and\\O did\\O nothing\\O proactive\\O to\\O resolve\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "1997", "sentence": "it might be something deep within Windows , for I was unable to create a disk image on my hard drive .", "postag": ["PRP", "MD", "VB", "NN", "JJ", "IN", "NNS", ",", "IN", "PRP", "VBD", "JJ", "TO", "VB", "DT", "NN", "NN", "IN", "PRP$", "JJ", "NN", "."], "head": [4, 4, 4, 0, 4, 7, 4, 4, 12, 12, 12, 4, 14, 12, 17, 17, 14, 21, 21, 21, 14, 4], "deprel": ["nsubj", "aux", "cop", "root", "amod", "case", "nmod", "punct", "mark", "nsubj", "cop", "advcl", "mark", "xcomp", "det", "compound", "obj", "case", "nmod:poss", "amod", "obl", "punct"], "triples": [{"uid": "1997-0", "target_tags": "it\\O might\\O be\\O something\\O deep\\O within\\O Windows\\B ,\\O for\\O I\\O was\\O unable\\O to\\O create\\O a\\O disk\\O image\\O on\\O my\\O hard\\O drive\\O .\\O", "opinion_tags": "it\\O might\\O be\\O something\\O deep\\O within\\O Windows\\O ,\\O for\\O I\\O was\\O unable\\B to\\O create\\O a\\O disk\\O image\\O on\\O my\\O hard\\O drive\\O .\\O", "sentiment": "negative"}, {"uid": "1997-1", "target_tags": "it\\O might\\O be\\O something\\O deep\\O within\\O Windows\\O ,\\O for\\O I\\O was\\O unable\\O to\\O create\\O a\\O disk\\O image\\O on\\O my\\O hard\\B drive\\I .\\O", "opinion_tags": "it\\O might\\O be\\O something\\O deep\\O within\\O Windows\\O ,\\O for\\O I\\O was\\O unable\\B to\\O create\\O a\\O disk\\O image\\O on\\O my\\O hard\\O drive\\O .\\O", "sentiment": "negative"}, {"uid": "1997-2", "target_tags": "it\\O might\\O be\\O something\\O deep\\O within\\O Windows\\O ,\\O for\\O I\\O was\\O unable\\O to\\O create\\O a\\O disk\\B image\\I on\\O my\\O hard\\O drive\\O .\\O", "opinion_tags": "it\\O might\\O be\\O something\\O deep\\O within\\O Windows\\O ,\\O for\\O I\\O was\\O unable\\B to\\O create\\O a\\O disk\\O image\\O on\\O my\\O hard\\O drive\\O .\\O", "sentiment": "negative"}]}, {"id": "668", "sentence": "the features are great , the only thing it needs is better speakers .", "postag": ["DT", "NNS", "VBP", "JJ", ",", "DT", "JJ", "NN", "PRP", "VBZ", "VBZ", "JJR", "NNS", "."], "head": [2, 4, 4, 0, 13, 8, 8, 13, 10, 8, 13, 13, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "amod", "nsubj", "nsubj", "acl:relcl", "cop", "amod", "parataxis", "punct"], "triples": [{"uid": "668-0", "target_tags": "the\\O features\\B are\\O great\\O ,\\O the\\O only\\O thing\\O it\\O needs\\O is\\O better\\O speakers\\O .\\O", "opinion_tags": "the\\O features\\O are\\O great\\B ,\\O the\\O only\\O thing\\O it\\O needs\\O is\\O better\\O speakers\\O .\\O", "sentiment": "positive"}, {"uid": "668-1", "target_tags": "the\\O features\\O are\\O great\\O ,\\O the\\O only\\O thing\\O it\\O needs\\O is\\O better\\O speakers\\B .\\O", "opinion_tags": "the\\O features\\O are\\O great\\O ,\\O the\\O only\\O thing\\O it\\O needs\\O is\\O better\\B speakers\\O .\\O", "sentiment": "negative"}]}, {"id": "49", "sentence": "In due course , I 'll remove the hard disc from this new MacBook Pro and dump it where it belongs - in the trash .", "postag": ["IN", "JJ", "RB", ",", "PRP", "MD", "VB", "DT", "JJ", "NN", "IN", "DT", "JJ", "NNP", "NNP", "CC", "VB", "PRP", "WRB", "PRP", "VBZ", ",", "IN", "DT", "NN", "."], "head": [2, 7, 2, 7, 7, 7, 0, 10, 10, 7, 15, 15, 15, 15, 7, 17, 7, 17, 21, 21, 17, 21, 25, 25, 21, 7], "deprel": ["case", "obl", "advmod", "punct", "nsubj", "aux", "root", "det", "amod", "obj", "case", "det", "amod", "compound", "obl", "cc", "conj", "obj", "mark", "nsubj", "advcl", "punct", "case", "det", "obl", "punct"], "triples": [{"uid": "49-0", "target_tags": "In\\O due\\O course\\O ,\\O I\\O 'll\\O remove\\O the\\O hard\\B disc\\I from\\O this\\O new\\O MacBook\\O Pro\\O and\\O dump\\O it\\O where\\O it\\O belongs\\O -\\O in\\O the\\O trash\\O .\\O", "opinion_tags": "In\\O due\\O course\\O ,\\O I\\O 'll\\O remove\\B the\\O hard\\O disc\\O from\\O this\\O new\\O MacBook\\O Pro\\O and\\O dump\\O it\\O where\\O it\\O belongs\\O -\\O in\\O the\\O trash\\O .\\O", "sentiment": "negative"}]}, {"id": "15", "sentence": "Runs fast and the regular layout keyboard is so much better .", "postag": ["VBZ", "JJ", "CC", "DT", "JJ", "NN", "NN", "VBZ", "RB", "RB", "JJR", "."], "head": [0, 1, 11, 7, 7, 7, 11, 11, 10, 11, 1, 1], "deprel": ["root", "xcomp", "cc", "det", "amod", "compound", "nsubj", "cop", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "15-0", "target_tags": "Runs\\O fast\\O and\\O the\\O regular\\B layout\\I keyboard\\I is\\O so\\O much\\O better\\O .\\O", "opinion_tags": "Runs\\O fast\\O and\\O the\\O regular\\O layout\\O keyboard\\O is\\O so\\O much\\O better\\B .\\O", "sentiment": "positive"}, {"uid": "15-1", "target_tags": "Runs\\B fast\\O and\\O the\\O regular\\O layout\\O keyboard\\O is\\O so\\O much\\O better\\O .\\O", "opinion_tags": "Runs\\O fast\\B and\\O the\\O regular\\O layout\\O keyboard\\O is\\O so\\O much\\O better\\O .\\O", "sentiment": "positive"}]}, {"id": "2704", "sentence": "Its a good laptop for its value .", "postag": ["PRP$", "DT", "JJ", "NN", "IN", "PRP$", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 4], "deprel": ["nmod:poss", "det", "amod", "root", "case", "nmod:poss", "nmod", "punct"], "triples": [{"uid": "2704-0", "target_tags": "Its\\O a\\O good\\O laptop\\O for\\O its\\O value\\B .\\O", "opinion_tags": "Its\\O a\\O good\\B laptop\\O for\\O its\\O value\\O .\\O", "sentiment": "positive"}]}, {"id": "3074", "sentence": "The screen is bright and the keyboard is nice ;", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "JJ", ":"], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "3074-0", "target_tags": "The\\O screen\\B is\\O bright\\O and\\O the\\O keyboard\\O is\\O nice\\O ;\\O", "opinion_tags": "The\\O screen\\O is\\O bright\\B and\\O the\\O keyboard\\O is\\O nice\\O ;\\O", "sentiment": "positive"}, {"uid": "3074-1", "target_tags": "The\\O screen\\O is\\O bright\\O and\\O the\\O keyboard\\B is\\O nice\\O ;\\O", "opinion_tags": "The\\O screen\\O is\\O bright\\O and\\O the\\O keyboard\\O is\\O nice\\B ;\\O", "sentiment": "positive"}]}, {"id": "1377", "sentence": "You know , using the computer should be fun , not aggrevation , especially when you are playing games or working with photos .", "postag": ["PRP", "VBP", ",", "VBG", "DT", "NN", "MD", "VB", "JJ", ",", "RB", "NN", ",", "RB", "WRB", "PRP", "VBP", "VBG", "NNS", "CC", "VBG", "IN", "NNS", "."], "head": [2, 9, 9, 9, 6, 4, 9, 9, 0, 12, 12, 9, 9, 18, 18, 18, 18, 9, 18, 21, 18, 23, 21, 9], "deprel": ["nsubj", "parataxis", "punct", "csubj", "det", "obj", "aux", "cop", "root", "punct", "advmod", "conj", "punct", "advmod", "mark", "nsubj", "aux", "advcl", "obj", "cc", "conj", "case", "obl", "punct"], "triples": [{"uid": "1377-0", "target_tags": "You\\O know\\O ,\\O using\\O the\\O computer\\O should\\O be\\O fun\\O ,\\O not\\O aggrevation\\O ,\\O especially\\O when\\O you\\O are\\O playing\\B games\\I or\\O working\\O with\\O photos\\O .\\O", "opinion_tags": "You\\O know\\O ,\\O using\\O the\\O computer\\O should\\O be\\O fun\\B ,\\O not\\B aggrevation\\I ,\\O especially\\O when\\O you\\O are\\O playing\\O games\\O or\\O working\\O with\\O photos\\O .\\O", "sentiment": "neutral"}]}, {"id": "3006", "sentence": "lots of preloaded software .", "postag": ["NNS", "IN", "JJ", "NN", "."], "head": [0, 4, 4, 1, 1], "deprel": ["root", "case", "amod", "nmod", "punct"], "triples": [{"uid": "3006-0", "target_tags": "lots\\O of\\O preloaded\\B software\\I .\\O", "opinion_tags": "lots\\B of\\O preloaded\\O software\\O .\\O", "sentiment": "positive"}]}, {"id": "2859", "sentence": "Some features arent friendly ( volume wheel , sound quality , etc .", "postag": ["DT", "NNS", "VBP", "JJ", "-LRB-", "NN", "NN", ",", "NN", "NN", ",", "FW", "."], "head": [2, 4, 4, 0, 7, 7, 4, 10, 10, 7, 12, 7, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "compound", "parataxis", "punct", "compound", "conj", "punct", "conj", "punct"], "triples": [{"uid": "2859-0", "target_tags": "Some\\O features\\O arent\\O friendly\\O (\\O volume\\B wheel\\I ,\\O sound\\O quality\\O ,\\O etc\\O .\\O", "opinion_tags": "Some\\O features\\O arent\\B friendly\\I (\\O volume\\O wheel\\O ,\\O sound\\O quality\\O ,\\O etc\\O .\\O", "sentiment": "negative"}, {"uid": "2859-1", "target_tags": "Some\\O features\\O arent\\O friendly\\O (\\O volume\\O wheel\\O ,\\O sound\\B quality\\I ,\\O etc\\O .\\O", "opinion_tags": "Some\\O features\\O arent\\B friendly\\I (\\O volume\\O wheel\\O ,\\O sound\\O quality\\O ,\\O etc\\O .\\O", "sentiment": "negative"}, {"uid": "2859-2", "target_tags": "Some\\O features\\B arent\\O friendly\\O (\\O volume\\O wheel\\O ,\\O sound\\O quality\\O ,\\O etc\\O .\\O", "opinion_tags": "Some\\O features\\O arent\\B friendly\\I (\\O volume\\O wheel\\O ,\\O sound\\O quality\\O ,\\O etc\\O .\\O", "sentiment": "negative"}]}, {"id": "378", "sentence": "I could n't believe how long the battery lasted on a single charge .", "postag": ["PRP", "MD", "RB", "VB", "WRB", "RB", "DT", "NN", "VBD", "IN", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 0, 6, 9, 8, 9, 4, 13, 13, 13, 9, 4], "deprel": ["nsubj", "aux", "advmod", "root", "mark", "advmod", "det", "nsubj", "ccomp", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "378-0", "target_tags": "I\\O could\\O n't\\O believe\\O how\\O long\\O the\\O battery\\B lasted\\O on\\O a\\O single\\O charge\\O .\\O", "opinion_tags": "I\\O could\\O n't\\O believe\\O how\\O long\\B the\\O battery\\O lasted\\O on\\O a\\O single\\O charge\\O .\\O", "sentiment": "positive"}, {"uid": "378-1", "target_tags": "I\\O could\\O n't\\O believe\\O how\\O long\\O the\\O battery\\O lasted\\O on\\O a\\O single\\O charge\\B .\\O", "opinion_tags": "I\\O could\\O n't\\O believe\\O how\\O long\\O the\\O battery\\O lasted\\O on\\O a\\O single\\B charge\\O .\\O", "sentiment": "positive"}]}, {"id": "2318", "sentence": "Wonderful zooming .", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "2318-0", "target_tags": "Wonderful\\O zooming\\B .\\O", "opinion_tags": "Wonderful\\B zooming\\O .\\O", "sentiment": "positive"}]}, {"id": "2479", "sentence": "It is a much more streamlined system for adding programs , using the internet , and doing other things everyone does on a computer .", "postag": ["PRP", "VBZ", "DT", "RB", "RBR", "JJ", "NN", "IN", "VBG", "NNS", ",", "VBG", "DT", "NN", ",", "CC", "VBG", "JJ", "NNS", "NN", "VBZ", "IN", "DT", "NN", "."], "head": [7, 7, 7, 5, 6, 7, 0, 9, 7, 9, 12, 9, 14, 12, 17, 17, 9, 19, 17, 21, 19, 24, 24, 21, 7], "deprel": ["nsubj", "cop", "det", "advmod", "advmod", "amod", "root", "mark", "acl", "obj", "punct", "conj", "det", "obj", "punct", "cc", "conj", "amod", "obj", "nsubj", "acl:relcl", "case", "det", "obl", "punct"], "triples": [{"uid": "2479-0", "target_tags": "It\\O is\\O a\\O much\\O more\\O streamlined\\O system\\O for\\O adding\\O programs\\B ,\\O using\\O the\\O internet\\O ,\\O and\\O doing\\O other\\O things\\O everyone\\O does\\O on\\O a\\O computer\\O .\\O", "opinion_tags": "It\\O is\\O a\\O much\\O more\\O streamlined\\O system\\O for\\O adding\\B programs\\O ,\\O using\\O the\\O internet\\O ,\\O and\\O doing\\O other\\O things\\O everyone\\O does\\O on\\O a\\O computer\\O .\\O", "sentiment": "neutral"}, {"uid": "2479-1", "target_tags": "It\\O is\\O a\\O much\\O more\\O streamlined\\O system\\B for\\O adding\\O programs\\O ,\\O using\\O the\\O internet\\O ,\\O and\\O doing\\O other\\O things\\O everyone\\O does\\O on\\O a\\O computer\\O .\\O", "opinion_tags": "It\\O is\\O a\\O much\\O more\\O streamlined\\B system\\O for\\O adding\\O programs\\O ,\\O using\\O the\\O internet\\O ,\\O and\\O doing\\O other\\O things\\O everyone\\O does\\O on\\O a\\O computer\\O .\\O", "sentiment": "positive"}, {"uid": "2479-2", "target_tags": "It\\O is\\O a\\O much\\O more\\O streamlined\\O system\\O for\\O adding\\O programs\\O ,\\O using\\B the\\I internet\\I ,\\O and\\O doing\\O other\\O things\\O everyone\\O does\\O on\\O a\\O computer\\O .\\O", "opinion_tags": "It\\O is\\O a\\O much\\O more\\O streamlined\\B system\\O for\\O adding\\O programs\\O ,\\O using\\O the\\O internet\\O ,\\O and\\O doing\\O other\\O things\\O everyone\\O does\\O on\\O a\\O computer\\O .\\O", "sentiment": "neutral"}]}, {"id": "1764", "sentence": "It has all the features that are necessary for college and if not they are able to be added onto the computer .", "postag": ["PRP", "VBZ", "PDT", "DT", "NNS", "WDT", "VBP", "JJ", "IN", "NN", "CC", "IN", "RB", "PRP", "VBP", "JJ", "TO", "VB", "VBN", "IN", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 10, 8, 16, 13, 16, 16, 16, 2, 19, 19, 16, 22, 22, 19, 2], "deprel": ["nsubj", "root", "det:predet", "det", "obj", "nsubj", "cop", "acl:relcl", "case", "obl", "cc", "mark", "advmod", "nsubj", "cop", "conj", "mark", "aux:pass", "xcomp", "case", "det", "obl", "punct"], "triples": [{"uid": "1764-0", "target_tags": "It\\O has\\O all\\O the\\O features\\B that\\O are\\O necessary\\O for\\O college\\O and\\O if\\O not\\O they\\O are\\O able\\O to\\O be\\O added\\O onto\\O the\\O computer\\O .\\O", "opinion_tags": "It\\O has\\O all\\O the\\O features\\O that\\O are\\O necessary\\B for\\O college\\O and\\O if\\O not\\O they\\O are\\O able\\O to\\O be\\O added\\O onto\\O the\\O computer\\O .\\O", "sentiment": "positive"}]}, {"id": "2867", "sentence": "It has easy to use features and all the speed and power I could ask for .", "postag": ["PRP", "VBZ", "JJ", "TO", "VB", "NNS", "CC", "PDT", "DT", "NN", "CC", "NN", "PRP", "MD", "VB", "IN", "."], "head": [2, 0, 2, 5, 3, 5, 10, 10, 10, 6, 12, 10, 15, 15, 10, 15, 2], "deprel": ["nsubj", "root", "xcomp", "mark", "xcomp", "obj", "cc", "det:predet", "det", "conj", "cc", "conj", "nsubj", "aux", "acl:relcl", "obl", "punct"], "triples": [{"uid": "2867-0", "target_tags": "It\\O has\\O easy\\O to\\O use\\O features\\B and\\O all\\O the\\O speed\\O and\\O power\\O I\\O could\\O ask\\O for\\O .\\O", "opinion_tags": "It\\O has\\O easy\\B to\\O use\\O features\\O and\\O all\\O the\\O speed\\O and\\O power\\O I\\O could\\O ask\\O for\\O .\\O", "sentiment": "positive"}, {"uid": "2867-1", "target_tags": "It\\O has\\O easy\\O to\\O use\\O features\\O and\\O all\\O the\\O speed\\B and\\O power\\O I\\O could\\O ask\\O for\\O .\\O", "opinion_tags": "It\\O has\\O easy\\B to\\O use\\O features\\O and\\O all\\O the\\O speed\\O and\\O power\\O I\\O could\\O ask\\O for\\O .\\O", "sentiment": "positive"}, {"uid": "2867-2", "target_tags": "It\\O has\\O easy\\O to\\O use\\O features\\O and\\O all\\O the\\O speed\\O and\\O power\\B I\\O could\\O ask\\O for\\O .\\O", "opinion_tags": "It\\O has\\O easy\\B to\\O use\\O features\\O and\\O all\\O the\\O speed\\O and\\O power\\O I\\O could\\O ask\\O for\\O .\\O", "sentiment": "positive"}]}, {"id": "2977", "sentence": "Laptops are usually used on the go , so why not give you a better battery ?", "postag": ["NNS", "VBP", "RB", "VBN", "IN", "DT", "NN", ",", "RB", "WRB", "RB", "VB", "PRP", "DT", "JJR", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 12, 12, 12, 12, 4, 12, 16, 16, 12, 4], "deprel": ["nsubj:pass", "aux:pass", "advmod", "root", "case", "det", "obl", "punct", "advmod", "advmod", "advmod", "parataxis", "i<PERSON><PERSON>", "det", "amod", "obj", "punct"], "triples": [{"uid": "2977-0", "target_tags": "Laptops\\O are\\O usually\\O used\\O on\\O the\\O go\\O ,\\O so\\O why\\O not\\O give\\O you\\O a\\O better\\O battery\\B ?\\O", "opinion_tags": "Laptops\\O are\\O usually\\O used\\O on\\O the\\O go\\O ,\\O so\\O why\\O not\\O give\\O you\\O a\\O better\\B battery\\O ?\\O", "sentiment": "negative"}]}, {"id": "2443", "sentence": "Boots up fast and runs great !", "postag": ["VBZ", "RB", "RB", "CC", "VBZ", "JJ", "."], "head": [0, 3, 1, 5, 1, 5, 1], "deprel": ["root", "advmod", "advmod", "cc", "conj", "xcomp", "punct"], "triples": [{"uid": "2443-0", "target_tags": "Boots\\B up\\I fast\\O and\\O runs\\O great\\O !\\O", "opinion_tags": "Boots\\O up\\O fast\\B and\\O runs\\O great\\O !\\O", "sentiment": "positive"}, {"uid": "2443-1", "target_tags": "Boots\\O up\\O fast\\O and\\O runs\\B great\\O !\\O", "opinion_tags": "Boots\\O up\\O fast\\O and\\O runs\\O great\\B !\\O", "sentiment": "positive"}]}, {"id": "854", "sentence": "The hard drive crashed as well , and I had to buy a new power cord .", "postag": ["DT", "JJ", "NN", "VBD", "RB", "RB", ",", "CC", "PRP", "VBD", "TO", "VB", "DT", "JJ", "NN", "NN", "."], "head": [3, 3, 4, 0, 4, 5, 10, 10, 10, 4, 12, 10, 16, 16, 16, 12, 4], "deprel": ["det", "amod", "nsubj", "root", "advmod", "fixed", "punct", "cc", "nsubj", "conj", "mark", "xcomp", "det", "amod", "compound", "obj", "punct"], "triples": [{"uid": "854-0", "target_tags": "The\\O hard\\B drive\\I crashed\\O as\\O well\\O ,\\O and\\O I\\O had\\O to\\O buy\\O a\\O new\\O power\\O cord\\O .\\O", "opinion_tags": "The\\O hard\\O drive\\O crashed\\B as\\O well\\O ,\\O and\\O I\\O had\\O to\\O buy\\O a\\O new\\O power\\O cord\\O .\\O", "sentiment": "negative"}]}, {"id": "2077", "sentence": "The screen shows great colors .", "postag": ["DT", "NN", "VBZ", "JJ", "NNS", "."], "head": [2, 3, 0, 5, 3, 3], "deprel": ["det", "nsubj", "root", "amod", "obj", "punct"], "triples": [{"uid": "2077-0", "target_tags": "The\\O screen\\B shows\\O great\\O colors\\O .\\O", "opinion_tags": "The\\O screen\\O shows\\O great\\B colors\\O .\\O", "sentiment": "positive"}, {"uid": "2077-1", "target_tags": "The\\O screen\\O shows\\O great\\O colors\\B .\\O", "opinion_tags": "The\\O screen\\O shows\\O great\\B colors\\O .\\O", "sentiment": "positive"}]}, {"id": "440", "sentence": "I love its solid build , light wt and excellent battery life ( for now ) .", "postag": ["PRP", "VBP", "PRP$", "JJ", "NN", ",", "JJ", "NN", "CC", "JJ", "NN", "NN", "-LRB-", "IN", "RB", "-RRB-", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 12, 12, 12, 5, 15, 15, 2, 15, 2], "deprel": ["nsubj", "root", "nmod:poss", "amod", "obj", "punct", "amod", "conj", "cc", "amod", "compound", "conj", "punct", "case", "obl", "punct", "punct"], "triples": [{"uid": "440-0", "target_tags": "I\\O love\\O its\\O solid\\O build\\B ,\\O light\\O wt\\O and\\O excellent\\O battery\\O life\\O (\\O for\\O now\\O )\\O .\\O", "opinion_tags": "I\\O love\\B its\\O solid\\B build\\O ,\\O light\\O wt\\O and\\O excellent\\O battery\\O life\\O (\\O for\\O now\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "440-1", "target_tags": "I\\O love\\O its\\O solid\\O build\\O ,\\O light\\O wt\\B and\\O excellent\\O battery\\O life\\O (\\O for\\O now\\O )\\O .\\O", "opinion_tags": "I\\O love\\B its\\O solid\\O build\\O ,\\O light\\B wt\\O and\\O excellent\\O battery\\O life\\O (\\O for\\O now\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "440-2", "target_tags": "I\\O love\\O its\\O solid\\O build\\O ,\\O light\\O wt\\O and\\O excellent\\O battery\\B life\\I (\\O for\\O now\\O )\\O .\\O", "opinion_tags": "I\\O love\\B its\\O solid\\O build\\O ,\\O light\\O wt\\O and\\O excellent\\B battery\\O life\\O (\\O for\\O now\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "7", "sentence": "AND the best part is that it even comes with a free printer ( when they have a certain promotion/offer going , of course ) !", "postag": ["CC", "DT", "JJS", "NN", "VBZ", "IN", "PRP", "RB", "VBZ", "IN", "DT", "JJ", "NN", "-LRB-", "WRB", "PRP", "VBP", "DT", "JJ", "NN", "VBG", ",", "RB", "RB", "-RRB-", "."], "head": [5, 4, 4, 5, 0, 9, 9, 9, 5, 13, 13, 13, 9, 17, 17, 17, 9, 20, 20, 17, 20, 17, 17, 23, 17, 5], "deprel": ["cc", "det", "amod", "nsubj", "root", "mark", "nsubj", "advmod", "ccomp", "case", "det", "amod", "obl", "punct", "mark", "nsubj", "advcl", "det", "amod", "obj", "acl", "punct", "advmod", "fixed", "punct", "punct"], "triples": [{"uid": "7-0", "target_tags": "AND\\O the\\O best\\O part\\O is\\O that\\O it\\O even\\O comes\\O with\\O a\\O free\\O printer\\B (\\O when\\O they\\O have\\O a\\O certain\\O promotion/offer\\O going\\O ,\\O of\\O course\\O )\\O !\\O", "opinion_tags": "AND\\O the\\O best\\O part\\O is\\O that\\O it\\O even\\O comes\\O with\\O a\\O free\\B printer\\O (\\O when\\O they\\O have\\O a\\O certain\\O promotion/offer\\O going\\O ,\\O of\\O course\\O )\\O !\\O", "sentiment": "positive"}]}, {"id": "2225", "sentence": "I also enjoy the fact that my MacBook Pro laptop allows me to run Windows 7 on it by using the VMWare program .", "postag": ["PRP", "RB", "VBP", "DT", "NN", "IN", "PRP$", "NNP", "NNP", "NN", "VBZ", "PRP", "TO", "VB", "NNP", "CD", "IN", "PRP", "IN", "VBG", "DT", "NNP", "NN", "."], "head": [3, 3, 0, 5, 3, 11, 10, 10, 10, 11, 5, 11, 14, 11, 14, 15, 18, 14, 20, 14, 23, 23, 20, 3], "deprel": ["nsubj", "advmod", "root", "det", "obj", "mark", "nmod:poss", "compound", "compound", "nsubj", "acl", "obj", "mark", "xcomp", "obj", "nummod", "case", "obl", "mark", "advcl", "det", "compound", "obj", "punct"], "triples": [{"uid": "2225-0", "target_tags": "I\\O also\\O enjoy\\O the\\O fact\\O that\\O my\\O MacBook\\O Pro\\O laptop\\O allows\\O me\\O to\\O run\\O Windows\\B 7\\I on\\O it\\O by\\O using\\O the\\O VMWare\\O program\\O .\\O", "opinion_tags": "I\\O also\\O enjoy\\B the\\O fact\\O that\\O my\\O MacBook\\O Pro\\O laptop\\O allows\\O me\\O to\\O run\\O Windows\\O 7\\O on\\O it\\O by\\O using\\O the\\O VMWare\\O program\\O .\\O", "sentiment": "positive"}, {"uid": "2225-1", "target_tags": "I\\O also\\O enjoy\\O the\\O fact\\O that\\O my\\O MacBook\\O Pro\\O laptop\\O allows\\O me\\O to\\O run\\O Windows\\O 7\\O on\\O it\\O by\\O using\\O the\\O VMWare\\B program\\I .\\O", "opinion_tags": "I\\O also\\O enjoy\\B the\\O fact\\O that\\O my\\O MacBook\\O Pro\\O laptop\\O allows\\O me\\O to\\O run\\O Windows\\O 7\\O on\\O it\\O by\\O using\\O the\\O VMWare\\O program\\O .\\O", "sentiment": "neutral"}]}, {"id": "451", "sentence": "It was very easy to just pick up and use -- It did not take long to get used to the Mac OS .", "postag": ["PRP", "VBD", "RB", "JJ", "TO", "RB", "VB", "RP", "CC", "VB", ",", "PRP", "VBD", "RB", "VB", "RB", "TO", "VB", "VBN", "IN", "DT", "NNP", "NNP", "."], "head": [4, 4, 4, 0, 7, 7, 4, 7, 10, 7, 4, 15, 15, 15, 4, 15, 18, 15, 18, 23, 23, 23, 19, 4], "deprel": ["expl", "cop", "advmod", "root", "mark", "advmod", "csubj", "compound:prt", "cc", "conj", "punct", "nsubj", "aux", "advmod", "parataxis", "advmod", "mark", "advcl", "xcomp", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "451-0", "target_tags": "It\\O was\\O very\\O easy\\O to\\O just\\O pick\\O up\\O and\\O use\\O --\\O It\\O did\\O not\\O take\\O long\\O to\\O get\\O used\\O to\\O the\\O Mac\\B OS\\I .\\O", "opinion_tags": "It\\O was\\O very\\O easy\\B to\\O just\\O pick\\O up\\O and\\O use\\O --\\O It\\O did\\O not\\O take\\O long\\O to\\O get\\O used\\O to\\O the\\O Mac\\O OS\\O .\\O", "sentiment": "positive"}, {"uid": "451-1", "target_tags": "It\\O was\\O very\\O easy\\O to\\O just\\O pick\\O up\\O and\\O use\\B --\\O It\\O did\\O not\\O take\\O long\\O to\\O get\\O used\\O to\\O the\\O Mac\\O OS\\O .\\O", "opinion_tags": "It\\O was\\O very\\O easy\\B to\\O just\\O pick\\O up\\O and\\O use\\O --\\O It\\O did\\O not\\O take\\O long\\O to\\O get\\O used\\O to\\O the\\O Mac\\O OS\\O .\\O", "sentiment": "positive"}]}, {"id": "1497", "sentence": "The PhotoBooth is a great program , it takes very good pictures with the built-in camera .", "postag": ["DT", "NNP", "VBZ", "DT", "JJ", "NN", ",", "PRP", "VBZ", "RB", "JJ", "NNS", "IN", "DT", "JJ", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 6, 9, 6, 11, 12, 9, 16, 16, 16, 9, 6], "deprel": ["det", "nsubj", "cop", "det", "amod", "root", "punct", "nsubj", "parataxis", "advmod", "amod", "obj", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "1497-0", "target_tags": "The\\O PhotoBooth\\B is\\O a\\O great\\O program\\O ,\\O it\\O takes\\O very\\O good\\O pictures\\O with\\O the\\O built-in\\O camera\\O .\\O", "opinion_tags": "The\\O PhotoBooth\\O is\\O a\\O great\\B program\\O ,\\O it\\O takes\\O very\\O good\\O pictures\\O with\\O the\\O built-in\\O camera\\O .\\O", "sentiment": "positive"}, {"uid": "1497-1", "target_tags": "The\\O PhotoBooth\\O is\\O a\\O great\\O program\\B ,\\O it\\O takes\\O very\\O good\\O pictures\\O with\\O the\\O built-in\\O camera\\O .\\O", "opinion_tags": "The\\O PhotoBooth\\O is\\O a\\O great\\B program\\O ,\\O it\\O takes\\O very\\O good\\O pictures\\O with\\O the\\O built-in\\O camera\\O .\\O", "sentiment": "positive"}, {"uid": "1497-2", "target_tags": "The\\O PhotoBooth\\O is\\O a\\O great\\O program\\O ,\\O it\\O takes\\O very\\O good\\O pictures\\O with\\O the\\O built-in\\B camera\\I .\\O", "opinion_tags": "The\\O PhotoBooth\\O is\\O a\\O great\\O program\\O ,\\O it\\O takes\\O very\\O good\\B pictures\\O with\\O the\\O built-in\\O camera\\O .\\O", "sentiment": "positive"}]}, {"id": "1902", "sentence": "The screen almost looked like a barcode when it froze .", "postag": ["DT", "NN", "RB", "VBD", "IN", "DT", "NN", "WRB", "PRP", "VBD", "."], "head": [2, 4, 4, 0, 7, 7, 4, 10, 10, 4, 4], "deprel": ["det", "nsubj", "advmod", "root", "case", "det", "obl", "mark", "nsubj", "advcl", "punct"], "triples": [{"uid": "1902-0", "target_tags": "The\\O screen\\B almost\\O looked\\O like\\O a\\O barcode\\O when\\O it\\O froze\\O .\\O", "opinion_tags": "The\\O screen\\O almost\\O looked\\O like\\O a\\O barcode\\B when\\O it\\O froze\\B .\\O", "sentiment": "negative"}]}, {"id": "1221", "sentence": "I was loving this Netbook because it had an amazing screen and display and was small and light , but after 1 week it stopped openning web pages for me ( even after installing new browsers ) then eventually it just started giving me a blue screen and crashing everytime I booted it .", "postag": ["PRP", "VBD", "VBG", "DT", "NN", "IN", "PRP", "VBD", "DT", "JJ", "NN", "CC", "NN", "CC", "VBD", "JJ", "CC", "JJ", ",", "CC", "IN", "CD", "NN", "PRP", "VBD", "VBG", "NN", "NNS", "IN", "PRP", "-LRB-", "RB", "IN", "VBG", "JJ", "NNS", "-RRB-", "RB", "RB", "PRP", "RB", "VBD", "VBG", "PRP", "DT", "JJ", "NN", "CC", "VBG", "RB", "PRP", "VBD", "PRP", "."], "head": [3, 3, 0, 5, 3, 8, 8, 3, 11, 11, 8, 13, 11, 16, 16, 3, 18, 16, 25, 25, 23, 23, 25, 25, 16, 25, 28, 26, 30, 26, 34, 34, 34, 25, 36, 34, 34, 42, 42, 42, 42, 3, 42, 43, 47, 47, 43, 49, 43, 49, 52, 42, 52, 3], "deprel": ["nsubj", "aux", "root", "det", "obj", "mark", "nsubj", "advcl", "det", "amod", "obj", "cc", "conj", "cc", "cop", "conj", "cc", "conj", "punct", "cc", "case", "nummod", "obl", "nsubj", "conj", "xcomp", "compound", "obj", "case", "obl", "punct", "advmod", "mark", "advcl", "amod", "obj", "punct", "advmod", "advmod", "nsubj", "advmod", "conj", "xcomp", "i<PERSON><PERSON>", "det", "amod", "obj", "cc", "conj", "advmod", "nsubj", "conj", "obj", "punct"], "triples": [{"uid": "1221-0", "target_tags": "I\\O was\\O loving\\O this\\O Netbook\\O because\\O it\\O had\\O an\\O amazing\\O screen\\B and\\O display\\O and\\O was\\O small\\O and\\O light\\O ,\\O but\\O after\\O 1\\O week\\O it\\O stopped\\O openning\\O web\\O pages\\O for\\O me\\O (\\O even\\O after\\O installing\\O new\\O browsers\\O )\\O then\\O eventually\\O it\\O just\\O started\\O giving\\O me\\O a\\O blue\\O screen\\O and\\O crashing\\O everytime\\O I\\O booted\\O it\\O .\\O", "opinion_tags": "I\\O was\\O loving\\O this\\O Netbook\\O because\\O it\\O had\\O an\\O amazing\\B screen\\O and\\O display\\O and\\O was\\O small\\O and\\O light\\O ,\\O but\\O after\\O 1\\O week\\O it\\O stopped\\O openning\\O web\\O pages\\O for\\O me\\O (\\O even\\O after\\O installing\\O new\\O browsers\\O )\\O then\\O eventually\\O it\\O just\\O started\\O giving\\O me\\O a\\O blue\\O screen\\O and\\O crashing\\O everytime\\O I\\O booted\\O it\\O .\\O", "sentiment": "positive"}, {"uid": "1221-1", "target_tags": "I\\O was\\O loving\\O this\\O Netbook\\O because\\O it\\O had\\O an\\O amazing\\O screen\\O and\\O display\\B and\\O was\\O small\\O and\\O light\\O ,\\O but\\O after\\O 1\\O week\\O it\\O stopped\\O openning\\O web\\O pages\\O for\\O me\\O (\\O even\\O after\\O installing\\O new\\O browsers\\O )\\O then\\O eventually\\O it\\O just\\O started\\O giving\\O me\\O a\\O blue\\O screen\\O and\\O crashing\\O everytime\\O I\\O booted\\O it\\O .\\O", "opinion_tags": "I\\O was\\O loving\\O this\\O Netbook\\O because\\O it\\O had\\O an\\O amazing\\O screen\\O and\\O display\\O and\\O was\\O small\\B and\\O light\\B ,\\O but\\O after\\O 1\\O week\\O it\\O stopped\\O openning\\O web\\O pages\\O for\\O me\\O (\\O even\\O after\\O installing\\O new\\O browsers\\O )\\O then\\O eventually\\O it\\O just\\O started\\O giving\\O me\\O a\\O blue\\O screen\\O and\\O crashing\\O everytime\\O I\\O booted\\O it\\O .\\O", "sentiment": "positive"}, {"uid": "1221-2", "target_tags": "I\\O was\\O loving\\O this\\O Netbook\\O because\\O it\\O had\\O an\\O amazing\\O screen\\O and\\O display\\O and\\O was\\O small\\O and\\O light\\O ,\\O but\\O after\\O 1\\O week\\O it\\O stopped\\O openning\\O web\\O pages\\O for\\O me\\O (\\O even\\O after\\O installing\\O new\\O browsers\\B )\\O then\\O eventually\\O it\\O just\\O started\\O giving\\O me\\O a\\O blue\\O screen\\O and\\O crashing\\O everytime\\O I\\O booted\\O it\\O .\\O", "opinion_tags": "I\\O was\\O loving\\O this\\O Netbook\\O because\\O it\\O had\\O an\\O amazing\\O screen\\O and\\O display\\O and\\O was\\O small\\O and\\O light\\O ,\\O but\\O after\\O 1\\O week\\O it\\O stopped\\O openning\\O web\\O pages\\O for\\O me\\O (\\O even\\O after\\O installing\\O new\\B browsers\\O )\\O then\\O eventually\\O it\\O just\\O started\\O giving\\O me\\O a\\O blue\\O screen\\O and\\O crashing\\O everytime\\O I\\O booted\\O it\\O .\\O", "sentiment": "neutral"}]}, {"id": "2168", "sentence": "There is a backlit keyboard which is perfect for typing in the dark .", "postag": ["EX", "VBZ", "DT", "NN", "NN", "WDT", "VBZ", "JJ", "IN", "VBG", "IN", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 10, 8, 13, 13, 10, 2], "deprel": ["expl", "root", "det", "compound", "nsubj", "nsubj", "cop", "acl:relcl", "mark", "advcl", "case", "det", "obl", "punct"], "triples": [{"uid": "2168-0", "target_tags": "There\\O is\\O a\\O backlit\\B keyboard\\I which\\O is\\O perfect\\O for\\O typing\\O in\\O the\\O dark\\O .\\O", "opinion_tags": "There\\O is\\O a\\O backlit\\O keyboard\\O which\\O is\\O perfect\\B for\\O typing\\O in\\O the\\O dark\\O .\\O", "sentiment": "positive"}]}, {"id": "1570", "sentence": "company provides UPS shipping , fast , great !", "postag": ["NN", "VBZ", "NN", "NN", ",", "JJ", ",", "JJ", "."], "head": [2, 0, 4, 2, 4, 4, 4, 4, 2], "deprel": ["nsubj", "root", "compound", "obj", "punct", "amod", "punct", "amod", "punct"], "triples": [{"uid": "1570-0", "target_tags": "company\\O provides\\O UPS\\O shipping\\B ,\\O fast\\O ,\\O great\\O !\\O", "opinion_tags": "company\\O provides\\O UPS\\O shipping\\O ,\\O fast\\B ,\\O great\\B !\\O", "sentiment": "positive"}]}, {"id": "2506", "sentence": "I was originally concerned that I could n't view work I had done in college on my Mac because of the PC formatting , but I was even more thrilled to learn of programs like iLife and iWork that allow you to convert your PC documents into readable files on Macs .", "postag": ["PRP", "VBD", "RB", "JJ", "IN", "PRP", "MD", "RB", "VB", "NN", "PRP", "VBD", "VBN", "IN", "NN", "IN", "PRP$", "NNP", "IN", "IN", "DT", "NN", "NN", ",", "CC", "PRP", "VBD", "RB", "RBR", "JJ", "TO", "VB", "IN", "NNS", "IN", "NNP", "CC", "NNP", "WDT", "VBP", "PRP", "TO", "VB", "PRP$", "NN", "NNS", "IN", "JJ", "NNS", "IN", "NNP", "."], "head": [4, 4, 4, 0, 9, 9, 9, 9, 4, 9, 13, 13, 10, 15, 13, 18, 18, 13, 23, 19, 23, 23, 9, 30, 30, 30, 30, 30, 30, 4, 32, 30, 34, 32, 36, 34, 38, 36, 40, 34, 40, 43, 40, 46, 46, 43, 49, 49, 43, 51, 49, 4], "deprel": ["nsubj", "cop", "advmod", "root", "mark", "nsubj", "aux", "advmod", "ccomp", "obj", "nsubj", "aux", "acl:relcl", "case", "obl", "case", "nmod:poss", "obl", "case", "fixed", "det", "compound", "obl", "punct", "cc", "nsubj", "cop", "advmod", "advmod", "conj", "mark", "xcomp", "case", "obl", "case", "nmod", "cc", "conj", "nsubj", "acl:relcl", "obj", "mark", "xcomp", "nmod:poss", "compound", "obj", "case", "amod", "obl", "case", "nmod", "punct"], "triples": [{"uid": "2506-0", "target_tags": "I\\O was\\O originally\\O concerned\\O that\\O I\\O could\\O n't\\O view\\O work\\O I\\O had\\O done\\O in\\O college\\O on\\O my\\O Mac\\O because\\O of\\O the\\O PC\\O formatting\\O ,\\O but\\O I\\O was\\O even\\O more\\O thrilled\\O to\\O learn\\O of\\O programs\\B like\\O iLife\\O and\\O iWork\\O that\\O allow\\O you\\O to\\O convert\\O your\\O PC\\O documents\\O into\\O readable\\O files\\O on\\O Macs\\O .\\O", "opinion_tags": "I\\O was\\O originally\\O concerned\\O that\\O I\\O could\\O n't\\O view\\O work\\O I\\O had\\O done\\O in\\O college\\O on\\O my\\O Mac\\O because\\O of\\O the\\O PC\\O formatting\\O ,\\O but\\O I\\O was\\O even\\O more\\O thrilled\\B to\\O learn\\O of\\O programs\\O like\\O iLife\\O and\\O iWork\\O that\\O allow\\O you\\O to\\O convert\\O your\\O PC\\O documents\\O into\\O readable\\O files\\O on\\O Macs\\O .\\O", "sentiment": "positive"}, {"uid": "2506-1", "target_tags": "I\\O was\\O originally\\O concerned\\O that\\O I\\O could\\O n't\\O view\\O work\\O I\\O had\\O done\\O in\\O college\\O on\\O my\\O Mac\\O because\\O of\\O the\\O PC\\O formatting\\O ,\\O but\\O I\\O was\\O even\\O more\\O thrilled\\O to\\O learn\\O of\\O programs\\O like\\O iLife\\B and\\O iWork\\O that\\O allow\\O you\\O to\\O convert\\O your\\O PC\\O documents\\O into\\O readable\\O files\\O on\\O Macs\\O .\\O", "opinion_tags": "I\\O was\\O originally\\O concerned\\O that\\O I\\O could\\O n't\\O view\\O work\\O I\\O had\\O done\\O in\\O college\\O on\\O my\\O Mac\\O because\\O of\\O the\\O PC\\O formatting\\O ,\\O but\\O I\\O was\\O even\\O more\\O thrilled\\B to\\O learn\\O of\\O programs\\O like\\O iLife\\O and\\O iWork\\O that\\O allow\\O you\\O to\\O convert\\O your\\O PC\\O documents\\O into\\O readable\\O files\\O on\\O Macs\\O .\\O", "sentiment": "positive"}, {"uid": "2506-2", "target_tags": "I\\O was\\O originally\\O concerned\\O that\\O I\\O could\\O n't\\O view\\O work\\O I\\O had\\O done\\O in\\O college\\O on\\O my\\O Mac\\O because\\O of\\O the\\O PC\\O formatting\\O ,\\O but\\O I\\O was\\O even\\O more\\O thrilled\\O to\\O learn\\O of\\O programs\\O like\\O iLife\\O and\\O iWork\\B that\\O allow\\O you\\O to\\O convert\\O your\\O PC\\O documents\\O into\\O readable\\O files\\O on\\O Macs\\O .\\O", "opinion_tags": "I\\O was\\O originally\\O concerned\\O that\\O I\\O could\\O n't\\O view\\O work\\O I\\O had\\O done\\O in\\O college\\O on\\O my\\O Mac\\O because\\O of\\O the\\O PC\\O formatting\\O ,\\O but\\O I\\O was\\O even\\O more\\O thrilled\\B to\\O learn\\O of\\O programs\\O like\\O iLife\\O and\\O iWork\\O that\\O allow\\O you\\O to\\O convert\\O your\\O PC\\O documents\\O into\\O readable\\O files\\O on\\O Macs\\O .\\O", "sentiment": "positive"}]}, {"id": "2697", "sentence": "I also had a problem with the touchpad that caused the mouse pointer to jump all over the screen .", "postag": ["PRP", "RB", "VBD", "DT", "NN", "IN", "DT", "NN", "WDT", "VBD", "DT", "NN", "NN", "TO", "VB", "RB", "IN", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 8, 8, 5, 10, 8, 13, 13, 10, 15, 10, 19, 19, 19, 15, 3], "deprel": ["nsubj", "advmod", "root", "det", "obj", "case", "det", "nmod", "nsubj", "acl:relcl", "det", "compound", "obj", "mark", "advcl", "advmod", "case", "det", "obl", "punct"], "triples": [{"uid": "2697-0", "target_tags": "I\\O also\\O had\\O a\\O problem\\O with\\O the\\O touchpad\\B that\\O caused\\O the\\O mouse\\O pointer\\O to\\O jump\\O all\\O over\\O the\\O screen\\O .\\O", "opinion_tags": "I\\O also\\O had\\O a\\O problem\\B with\\O the\\O touchpad\\O that\\O caused\\O the\\O mouse\\O pointer\\O to\\O jump\\O all\\O over\\O the\\O screen\\O .\\O", "sentiment": "negative"}, {"uid": "2697-1", "target_tags": "I\\O also\\O had\\O a\\O problem\\O with\\O the\\O touchpad\\O that\\O caused\\O the\\O mouse\\O pointer\\O to\\O jump\\O all\\O over\\O the\\O screen\\B .\\O", "opinion_tags": "I\\O also\\O had\\O a\\O problem\\B with\\O the\\O touchpad\\O that\\O caused\\O the\\O mouse\\O pointer\\O to\\O jump\\O all\\O over\\O the\\O screen\\O .\\O", "sentiment": "neutral"}, {"uid": "2697-2", "target_tags": "I\\O also\\O had\\O a\\O problem\\O with\\O the\\O touchpad\\O that\\O caused\\O the\\O mouse\\B pointer\\I to\\O jump\\O all\\O over\\O the\\O screen\\O .\\O", "opinion_tags": "I\\O also\\O had\\O a\\O problem\\B with\\O the\\O touchpad\\O that\\O caused\\O the\\O mouse\\O pointer\\O to\\O jump\\O all\\O over\\O the\\O screen\\O .\\O", "sentiment": "negative"}]}, {"id": "2874", "sentence": "The delivery was fast , and I would not hesitate to purchase this laptop again .", "postag": ["DT", "NN", "VBD", "JJ", ",", "CC", "PRP", "MD", "RB", "VB", "TO", "VB", "DT", "NN", "RB", "."], "head": [2, 4, 4, 0, 10, 10, 10, 10, 10, 4, 12, 10, 14, 12, 12, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "nsubj", "aux", "advmod", "conj", "mark", "xcomp", "det", "obj", "advmod", "punct"], "triples": [{"uid": "2874-0", "target_tags": "The\\O delivery\\B was\\O fast\\O ,\\O and\\O I\\O would\\O not\\O hesitate\\O to\\O purchase\\O this\\O laptop\\O again\\O .\\O", "opinion_tags": "The\\O delivery\\O was\\O fast\\B ,\\O and\\O I\\O would\\O not\\O hesitate\\O to\\O purchase\\O this\\O laptop\\O again\\O .\\O", "sentiment": "positive"}]}, {"id": "138", "sentence": "only good thing is the graphics quality .", "postag": ["RB", "JJ", "NN", "VBZ", "DT", "NNS", "NN", "."], "head": [2, 3, 0, 3, 7, 7, 3, 3], "deprel": ["advmod", "amod", "root", "cop", "det", "compound", "nsubj", "punct"], "triples": [{"uid": "138-0", "target_tags": "only\\O good\\O thing\\O is\\O the\\O graphics\\B quality\\I .\\O", "opinion_tags": "only\\O good\\B thing\\O is\\O the\\O graphics\\O quality\\O .\\O", "sentiment": "positive"}]}, {"id": "836", "sentence": "The AC power port becomes loose over time", "postag": ["DT", "NNP", "NN", "NN", "VBZ", "RB", "IN", "NN"], "head": [4, 4, 4, 5, 0, 5, 8, 5], "deprel": ["det", "compound", "compound", "nsubj", "root", "advmod", "case", "obl"], "triples": [{"uid": "836-0", "target_tags": "The\\O AC\\B power\\I port\\I becomes\\O loose\\O over\\O time\\O", "opinion_tags": "The\\O AC\\O power\\O port\\O becomes\\O loose\\B over\\O time\\O", "sentiment": "negative"}]}, {"id": "2004", "sentence": "If you really want a bang-up system and do n't need to run Windows applications , go with an Apple ;", "postag": ["IN", "PRP", "RB", "VBP", "DT", "JJ", "NN", "CC", "VBP", "RB", "VB", "TO", "VB", "NNS", "NNS", ",", "VB", "IN", "DT", "NNP", ":"], "head": [4, 4, 4, 17, 7, 7, 4, 11, 11, 11, 4, 13, 11, 15, 13, 17, 0, 20, 20, 17, 17], "deprel": ["mark", "nsubj", "advmod", "advcl", "det", "amod", "obj", "cc", "aux", "advmod", "conj", "mark", "xcomp", "compound", "obj", "punct", "root", "case", "det", "obl", "punct"], "triples": [{"uid": "2004-0", "target_tags": "If\\O you\\O really\\O want\\O a\\O bang-up\\O system\\B and\\O do\\O n't\\O need\\O to\\O run\\O Windows\\O applications\\O ,\\O go\\O with\\O an\\O Apple\\O ;\\O", "opinion_tags": "If\\O you\\O really\\O want\\O a\\O bang-up\\B system\\O and\\O do\\O n't\\O need\\O to\\O run\\O Windows\\O applications\\O ,\\O go\\O with\\O an\\O Apple\\O ;\\O", "sentiment": "positive"}]}, {"id": "2778", "sentence": "The screen is a little glary , and I hated the clicking buttons , but I got used to them .", "postag": ["DT", "NN", "VBZ", "DT", "JJ", "JJ", ",", "CC", "PRP", "VBD", "DT", "NN", "NNS", ",", "CC", "PRP", "VBD", "VBN", "IN", "PRP", "."], "head": [2, 6, 6, 5, 6, 0, 10, 10, 10, 6, 13, 13, 10, 18, 18, 18, 18, 6, 20, 18, 6], "deprel": ["det", "nsubj", "cop", "det", "obl:npmod", "root", "punct", "cc", "nsubj", "conj", "det", "compound", "obj", "punct", "cc", "nsubj:pass", "aux:pass", "conj", "case", "obl", "punct"], "triples": [{"uid": "2778-0", "target_tags": "The\\O screen\\B is\\O a\\O little\\O glary\\O ,\\O and\\O I\\O hated\\O the\\O clicking\\O buttons\\O ,\\O but\\O I\\O got\\O used\\O to\\O them\\O .\\O", "opinion_tags": "The\\O screen\\O is\\O a\\O little\\O glary\\B ,\\O and\\O I\\O hated\\O the\\O clicking\\O buttons\\O ,\\O but\\O I\\O got\\O used\\O to\\O them\\O .\\O", "sentiment": "negative"}, {"uid": "2778-1", "target_tags": "The\\O screen\\O is\\O a\\O little\\O glary\\O ,\\O and\\O I\\O hated\\O the\\O clicking\\B buttons\\I ,\\O but\\O I\\O got\\O used\\O to\\O them\\O .\\O", "opinion_tags": "The\\O screen\\O is\\O a\\O little\\O glary\\O ,\\O and\\O I\\O hated\\B the\\O clicking\\O buttons\\O ,\\O but\\O I\\O got\\O used\\O to\\O them\\O .\\O", "sentiment": "negative"}]}, {"id": "2279", "sentence": "The sheer power and flexibility makes the MacBook Pro a must have for any techie !", "postag": ["DT", "JJ", "NN", "CC", "NN", "VBZ", "DT", "NNP", "NN", "NN", "MD", "VB", "IN", "DT", "NN", "."], "head": [3, 3, 6, 5, 3, 0, 10, 9, 10, 12, 12, 6, 15, 15, 12, 6], "deprel": ["det", "amod", "nsubj", "cc", "conj", "root", "det", "compound", "compound", "nsubj", "aux", "ccomp", "case", "det", "obl", "punct"], "triples": [{"uid": "2279-0", "target_tags": "The\\O sheer\\O power\\B and\\O flexibility\\O makes\\O the\\O MacBook\\O Pro\\O a\\O must\\O have\\O for\\O any\\O techie\\O !\\O", "opinion_tags": "The\\O sheer\\O power\\O and\\O flexibility\\O makes\\O the\\O MacBook\\O Pro\\O a\\O must\\B have\\O for\\O any\\O techie\\O !\\O", "sentiment": "positive"}, {"uid": "2279-1", "target_tags": "The\\O sheer\\O power\\O and\\O flexibility\\B makes\\O the\\O MacBook\\O Pro\\O a\\O must\\O have\\O for\\O any\\O techie\\O !\\O", "opinion_tags": "The\\O sheer\\O power\\O and\\O flexibility\\O makes\\O the\\O MacBook\\O Pro\\O a\\O must\\B have\\O for\\O any\\O techie\\O !\\O", "sentiment": "positive"}]}, {"id": "1743", "sentence": "But sitting on a lap or on a desk in front of you it looks more than big enough ( this could be because <PERSON><PERSON> used to my Lenovo 10 tablet now ) plus this is a great size if I want to unplug the external keyboard , mouse , and monitor to take it with me when I take photos and video .", "postag": ["CC", "VBG", "IN", "DT", "NN", "CC", "IN", "DT", "NN", "IN", "NN", "IN", "PRP", "PRP", "VBZ", "JJR", "IN", "JJ", "JJ", "-LRB-", "DT", "MD", "VB", "IN", "PRP", "VBN", "IN", "PRP$", "NNP", "CD", "NN", "RB", "-RRB-", "CC", "DT", "VBZ", "DT", "JJ", "NN", "IN", "PRP", "VBP", "TO", "VB", "DT", "JJ", "NN", ",", "NN", ",", "CC", "VB", "TO", "VB", "PRP", "IN", "PRP", "WRB", "PRP", "VBP", "NNS", "CC", "NN", "."], "head": [15, 15, 5, 5, 2, 9, 9, 9, 5, 11, 2, 13, 11, 15, 0, 15, 18, 19, 16, 23, 23, 23, 15, 26, 26, 23, 31, 31, 31, 29, 26, 26, 23, 39, 39, 39, 39, 39, 15, 42, 42, 39, 44, 42, 47, 47, 44, 49, 44, 52, 52, 44, 54, 52, 54, 57, 54, 60, 60, 54, 60, 63, 61, 15], "deprel": ["cc", "advcl", "case", "det", "obl", "cc", "case", "det", "conj", "case", "obl", "case", "nmod", "nsubj", "root", "advmod", "case", "amod", "obl", "punct", "nsubj", "aux", "parataxis", "mark", "nsubj", "advcl", "case", "nmod:poss", "compound", "nummod", "obl", "advmod", "punct", "cc", "nsubj", "cop", "det", "amod", "conj", "mark", "nsubj", "advcl", "mark", "xcomp", "det", "amod", "obj", "punct", "obj", "punct", "cc", "conj", "mark", "xcomp", "obj", "case", "obl", "mark", "nsubj", "advcl", "obj", "cc", "conj", "punct"], "triples": [{"uid": "1743-0", "target_tags": "But\\O sitting\\O on\\O a\\O lap\\O or\\O on\\O a\\O desk\\O in\\O front\\O of\\O you\\O it\\O looks\\O more\\O than\\O big\\O enough\\O (\\O this\\O could\\O be\\O because\\O Im\\O used\\O to\\O my\\O Lenovo\\O 10\\O tablet\\O now\\O )\\O plus\\O this\\O is\\O a\\O great\\O size\\B if\\O I\\O want\\O to\\O unplug\\O the\\O external\\O keyboard\\O ,\\O mouse\\O ,\\O and\\O monitor\\O to\\O take\\O it\\O with\\O me\\O when\\O I\\O take\\O photos\\O and\\O video\\O .\\O", "opinion_tags": "But\\O sitting\\O on\\O a\\O lap\\O or\\O on\\O a\\O desk\\O in\\O front\\O of\\O you\\O it\\O looks\\O more\\O than\\O big\\O enough\\O (\\O this\\O could\\O be\\O because\\O Im\\O used\\O to\\O my\\O Lenovo\\O 10\\O tablet\\O now\\O )\\O plus\\O this\\O is\\O a\\O great\\B size\\O if\\O I\\O want\\O to\\O unplug\\O the\\O external\\O keyboard\\O ,\\O mouse\\O ,\\O and\\O monitor\\O to\\O take\\O it\\O with\\O me\\O when\\O I\\O take\\O photos\\O and\\O video\\O .\\O", "sentiment": "positive"}]}, {"id": "1595", "sentence": "Its pretty fast and does not have hiccups while I am using it for web browsing , uploading photos , watching movies ( 720p ) on occasion and creating presentations .", "postag": ["PRP$", "RB", "JJ", "CC", "VBZ", "RB", "VB", "NNS", "IN", "PRP", "VBP", "VBG", "PRP", "IN", "NN", "NN", ",", "NN", "NNS", ",", "VBG", "NNS", "-LRB-", "NN", "-RRB-", "IN", "NN", "CC", "VBG", "NNS", "."], "head": [3, 3, 0, 7, 7, 7, 3, 7, 12, 12, 12, 7, 12, 16, 16, 12, 19, 19, 16, 21, 12, 21, 24, 22, 24, 27, 21, 29, 12, 29, 3], "deprel": ["nmod:poss", "advmod", "root", "cc", "aux", "advmod", "conj", "obj", "mark", "nsubj", "aux", "advcl", "obj", "case", "compound", "obl", "punct", "amod", "conj", "punct", "advcl", "obj", "punct", "appos", "punct", "case", "obl", "cc", "conj", "obj", "punct"], "triples": [{"uid": "1595-0", "target_tags": "Its\\O pretty\\O fast\\O and\\O does\\O not\\O have\\O hiccups\\O while\\O I\\O am\\O using\\O it\\O for\\O web\\B browsing\\I ,\\O uploading\\O photos\\O ,\\O watching\\O movies\\O (\\O 720p\\O )\\O on\\O occasion\\O and\\O creating\\O presentations\\O .\\O", "opinion_tags": "Its\\O pretty\\O fast\\B and\\O does\\O not\\O have\\O hiccups\\O while\\O I\\O am\\O using\\O it\\O for\\O web\\O browsing\\O ,\\O uploading\\O photos\\O ,\\O watching\\O movies\\O (\\O 720p\\O )\\O on\\O occasion\\O and\\O creating\\O presentations\\O .\\O", "sentiment": "positive"}, {"uid": "1595-1", "target_tags": "Its\\O pretty\\O fast\\O and\\O does\\O not\\O have\\O hiccups\\O while\\O I\\O am\\O using\\O it\\O for\\O web\\O browsing\\O ,\\O uploading\\B photos\\I ,\\O watching\\O movies\\O (\\O 720p\\O )\\O on\\O occasion\\O and\\O creating\\O presentations\\O .\\O", "opinion_tags": "Its\\O pretty\\O fast\\B and\\O does\\O not\\O have\\O hiccups\\O while\\O I\\O am\\O using\\O it\\O for\\O web\\O browsing\\O ,\\O uploading\\O photos\\O ,\\O watching\\O movies\\O (\\O 720p\\O )\\O on\\O occasion\\O and\\O creating\\O presentations\\O .\\O", "sentiment": "positive"}, {"uid": "1595-2", "target_tags": "Its\\O pretty\\O fast\\O and\\O does\\O not\\O have\\O hiccups\\O while\\O I\\O am\\O using\\O it\\O for\\O web\\O browsing\\O ,\\O uploading\\O photos\\O ,\\O watching\\B movies\\I (\\O 720p\\O )\\O on\\O occasion\\O and\\O creating\\O presentations\\O .\\O", "opinion_tags": "Its\\O pretty\\O fast\\B and\\O does\\O not\\O have\\O hiccups\\O while\\O I\\O am\\O using\\O it\\O for\\O web\\O browsing\\O ,\\O uploading\\O photos\\O ,\\O watching\\O movies\\O (\\O 720p\\O )\\O on\\O occasion\\O and\\O creating\\O presentations\\O .\\O", "sentiment": "positive"}, {"uid": "1595-3", "target_tags": "Its\\O pretty\\O fast\\O and\\O does\\O not\\O have\\O hiccups\\O while\\O I\\O am\\O using\\O it\\O for\\O web\\O browsing\\O ,\\O uploading\\O photos\\O ,\\O watching\\O movies\\O (\\O 720p\\O )\\O on\\O occasion\\O and\\O creating\\B presentations\\I .\\O", "opinion_tags": "Its\\O pretty\\O fast\\B and\\O does\\O not\\O have\\O hiccups\\O while\\O I\\O am\\O using\\O it\\O for\\O web\\O browsing\\O ,\\O uploading\\O photos\\O ,\\O watching\\O movies\\O (\\O 720p\\O )\\O on\\O occasion\\O and\\O creating\\O presentations\\O .\\O", "sentiment": "positive"}]}, {"id": "578", "sentence": "The Final Cut Pro on this laptop is so fast and easy , and I can use this to seemlessly transfer all my work to my home computer , which is also a mac .", "postag": ["DT", "JJ", "NN", "NN", "IN", "DT", "NN", "VBZ", "RB", "JJ", "CC", "JJ", ",", "CC", "PRP", "MD", "VB", "DT", "TO", "RB", "VB", "PDT", "PRP$", "NN", "IN", "PRP$", "NN", "NN", ",", "WDT", "VBZ", "RB", "DT", "NN", "."], "head": [4, 4, 4, 10, 7, 7, 4, 10, 10, 0, 12, 10, 17, 17, 17, 17, 10, 17, 21, 21, 17, 24, 24, 21, 28, 28, 28, 21, 28, 34, 34, 34, 34, 28, 10], "deprel": ["det", "amod", "compound", "nsubj", "case", "det", "nmod", "cop", "advmod", "root", "cc", "conj", "punct", "cc", "nsubj", "aux", "conj", "obj", "mark", "advmod", "advcl", "det:predet", "nmod:poss", "obj", "case", "nmod:poss", "compound", "obl", "punct", "nsubj", "cop", "advmod", "det", "acl:relcl", "punct"], "triples": [{"uid": "578-0", "target_tags": "The\\O Final\\B Cut\\I Pro\\I on\\O this\\O laptop\\O is\\O so\\O fast\\O and\\O easy\\O ,\\O and\\O I\\O can\\O use\\O this\\O to\\O seemlessly\\O transfer\\O all\\O my\\O work\\O to\\O my\\O home\\O computer\\O ,\\O which\\O is\\O also\\O a\\O mac\\O .\\O", "opinion_tags": "The\\O Final\\O Cut\\O Pro\\O on\\O this\\O laptop\\O is\\O so\\O fast\\B and\\O easy\\B ,\\O and\\O I\\O can\\O use\\O this\\O to\\O seemlessly\\O transfer\\O all\\O my\\O work\\O to\\O my\\O home\\O computer\\O ,\\O which\\O is\\O also\\O a\\O mac\\O .\\O", "sentiment": "positive"}]}, {"id": "2550", "sentence": "The OS is also very user friendly , even for those that switch from a PC , with a little practice you can take full advantage of this OS !", "postag": ["DT", "NNP", "VBZ", "RB", "RB", "NN", "JJ", ",", "RB", "IN", "DT", "WDT", "VBP", "IN", "DT", "NN", ",", "IN", "DT", "JJ", "NN", "PRP", "MD", "VB", "JJ", "NN", "IN", "DT", "NN", "."], "head": [2, 7, 7, 7, 7, 7, 0, 7, 11, 11, 24, 13, 11, 16, 16, 13, 24, 21, 21, 21, 24, 24, 24, 7, 26, 24, 29, 29, 26, 7], "deprel": ["det", "nsubj", "cop", "advmod", "advmod", "obl:npmod", "root", "punct", "advmod", "case", "obl", "nsubj", "acl:relcl", "case", "det", "obl", "punct", "case", "det", "amod", "obl", "nsubj", "aux", "parataxis", "amod", "obj", "case", "det", "nmod", "punct"], "triples": [{"uid": "2550-0", "target_tags": "The\\O OS\\B is\\O also\\O very\\O user\\O friendly\\O ,\\O even\\O for\\O those\\O that\\O switch\\O from\\O a\\O PC\\O ,\\O with\\O a\\O little\\O practice\\O you\\O can\\O take\\O full\\O advantage\\O of\\O this\\O OS\\O !\\O", "opinion_tags": "The\\O OS\\O is\\O also\\O very\\O user\\B friendly\\I ,\\O even\\O for\\O those\\O that\\O switch\\O from\\O a\\O PC\\O ,\\O with\\O a\\O little\\O practice\\O you\\O can\\O take\\O full\\O advantage\\O of\\O this\\O OS\\O !\\O", "sentiment": "positive"}]}, {"id": "2711", "sentence": "Seems to slow down occassionally but can run many applications ( ie Internet tabs , programs , etc ) simultaneously .", "postag": ["VBZ", "TO", "VB", "RP", "RB", "CC", "MD", "VB", "JJ", "NNS", "-LRB-", "NN", "NN", "NNS", ",", "NNS", ",", "FW", "-RRB-", "RB", "."], "head": [0, 3, 1, 3, 3, 8, 8, 1, 10, 8, 14, 14, 14, 10, 16, 14, 18, 14, 14, 14, 1], "deprel": ["root", "mark", "xcomp", "compound:prt", "advmod", "cc", "aux", "conj", "amod", "obj", "punct", "compound", "compound", "appos", "punct", "conj", "punct", "conj", "punct", "advmod", "punct"], "triples": [{"uid": "2711-0", "target_tags": "Seems\\O to\\O slow\\O down\\O occassionally\\O but\\O can\\O run\\O many\\O applications\\B (\\O ie\\O Internet\\O tabs\\O ,\\O programs\\O ,\\O etc\\O )\\O simultaneously\\O .\\O", "opinion_tags": "Seems\\O to\\O slow\\O down\\O occassionally\\O but\\O can\\O run\\O many\\B applications\\O (\\O ie\\O Internet\\O tabs\\O ,\\O programs\\O ,\\O etc\\O )\\O simultaneously\\O .\\O", "sentiment": "positive"}]}, {"id": "2129", "sentence": "It is stamped and not in pieces therefore it is a stronger more resilient frame .", "postag": ["PRP", "VBZ", "VBN", "CC", "RB", "IN", "NNS", "RB", "PRP", "VBZ", "DT", "JJR", "RBR", "JJ", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 15, 15, 15, 15, 15, 14, 15, 3, 3], "deprel": ["nsubj:pass", "aux:pass", "root", "cc", "advmod", "case", "obl", "advmod", "nsubj", "cop", "det", "amod", "advmod", "amod", "parataxis", "punct"], "triples": [{"uid": "2129-0", "target_tags": "It\\O is\\O stamped\\O and\\O not\\O in\\O pieces\\O therefore\\O it\\O is\\O a\\O stronger\\O more\\O resilient\\O frame\\B .\\O", "opinion_tags": "It\\O is\\O stamped\\O and\\O not\\O in\\O pieces\\O therefore\\O it\\O is\\O a\\O stronger\\B more\\O resilient\\B frame\\O .\\O", "sentiment": "positive"}]}, {"id": "2724", "sentence": "Only issue is that it is a little slow , and I 'm fixing that by adding more RAM .", "postag": ["JJ", "NN", "VBZ", "IN", "PRP", "VBZ", "DT", "JJ", "JJ", ",", "CC", "PRP", "VBP", "VBG", "DT", "IN", "VBG", "JJR", "NN", "."], "head": [2, 3, 0, 9, 9, 9, 8, 9, 3, 14, 14, 14, 14, 9, 14, 17, 14, 19, 17, 3], "deprel": ["amod", "nsubj", "root", "mark", "nsubj", "cop", "det", "obl:npmod", "ccomp", "punct", "cc", "nsubj", "aux", "conj", "obj", "mark", "advcl", "amod", "obj", "punct"], "triples": [{"uid": "2724-0", "target_tags": "Only\\O issue\\O is\\O that\\O it\\O is\\O a\\O little\\O slow\\O ,\\O and\\O I\\O 'm\\O fixing\\O that\\O by\\O adding\\O more\\O RAM\\B .\\O", "opinion_tags": "Only\\O issue\\O is\\O that\\O it\\O is\\O a\\O little\\O slow\\O ,\\O and\\O I\\O 'm\\O fixing\\O that\\O by\\O adding\\O more\\B RAM\\O .\\O", "sentiment": "neutral"}]}, {"id": "2787", "sentence": "Overall the computer is very easy to use , the screen is perfect , great computer , my daughter loves .", "postag": ["RB", "DT", "NN", "VBZ", "RB", "JJ", "TO", "VB", ",", "DT", "NN", "VBZ", "JJ", ",", "JJ", "NN", ",", "PRP$", "NN", "VBZ", "."], "head": [6, 3, 6, 6, 6, 0, 8, 6, 13, 11, 13, 13, 6, 16, 16, 13, 20, 19, 20, 6, 6], "deprel": ["advmod", "det", "nsubj", "cop", "advmod", "root", "mark", "xcomp", "punct", "det", "nsubj", "cop", "conj", "punct", "amod", "conj", "punct", "nmod:poss", "nsubj", "parataxis", "punct"], "triples": [{"uid": "2787-0", "target_tags": "Overall\\O the\\O computer\\O is\\O very\\O easy\\O to\\O use\\O ,\\O the\\O screen\\B is\\O perfect\\O ,\\O great\\O computer\\O ,\\O my\\O daughter\\O loves\\O .\\O", "opinion_tags": "Overall\\O the\\O computer\\O is\\O very\\O easy\\O to\\O use\\O ,\\O the\\O screen\\O is\\O perfect\\B ,\\O great\\O computer\\O ,\\O my\\O daughter\\O loves\\O .\\O", "sentiment": "positive"}, {"uid": "2787-1", "target_tags": "Overall\\O the\\O computer\\O is\\O very\\O easy\\O to\\O use\\B ,\\O the\\O screen\\O is\\O perfect\\O ,\\O great\\O computer\\O ,\\O my\\O daughter\\O loves\\O .\\O", "opinion_tags": "Overall\\O the\\O computer\\O is\\O very\\O easy\\B to\\O use\\O ,\\O the\\O screen\\O is\\O perfect\\O ,\\O great\\O computer\\O ,\\O my\\O daughter\\O loves\\O .\\O", "sentiment": "positive"}]}, {"id": "2671", "sentence": "while about 8 years ago , I hope that the quality has changed .", "postag": ["IN", "RB", "CD", "NNS", "RB", ",", "PRP", "VBP", "IN", "DT", "NN", "VBZ", "VBN", "."], "head": [5, 3, 4, 5, 8, 8, 8, 0, 13, 11, 13, 13, 8, 8], "deprel": ["mark", "advmod", "nummod", "obl:npmod", "advcl", "punct", "nsubj", "root", "mark", "det", "nsubj", "aux", "ccomp", "punct"], "triples": [{"uid": "2671-0", "target_tags": "while\\O about\\O 8\\O years\\O ago\\O ,\\O I\\O hope\\O that\\O the\\O quality\\B has\\O changed\\O .\\O", "opinion_tags": "while\\O about\\O 8\\O years\\O ago\\O ,\\O I\\O hope\\B that\\O the\\O quality\\O has\\O changed\\B .\\O", "sentiment": "negative"}]}, {"id": "107", "sentence": "My laptop now has no battery .", "postag": ["PRP$", "NN", "RB", "VBZ", "DT", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["nmod:poss", "nsubj", "advmod", "root", "det", "obj", "punct"], "triples": [{"uid": "107-0", "target_tags": "My\\O laptop\\O now\\O has\\O no\\O battery\\B .\\O", "opinion_tags": "My\\O laptop\\O now\\O has\\O no\\B battery\\O .\\O", "sentiment": "negative"}]}, {"id": "2811", "sentence": "Fan vents to the side , so no cooling pad needed , great feature !", "postag": ["NN", "NNS", "IN", "DT", "NN", ",", "RB", "DT", "NN", "NN", "VBN", ",", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 2, 11, 10, 10, 11, 2, 2, 14, 2, 2], "deprel": ["compound", "root", "case", "det", "nmod", "punct", "advmod", "det", "compound", "compound", "parataxis", "punct", "amod", "parataxis", "punct"], "triples": [{"uid": "2811-0", "target_tags": "Fan\\O vents\\O to\\O the\\O side\\O ,\\O so\\O no\\O cooling\\B pad\\I needed\\O ,\\O great\\O feature\\O !\\O", "opinion_tags": "Fan\\O vents\\O to\\O the\\O side\\O ,\\O so\\O no\\B cooling\\O pad\\O needed\\O ,\\O great\\O feature\\O !\\O", "sentiment": "neutral"}, {"uid": "2811-1", "target_tags": "Fan\\O vents\\O to\\O the\\O side\\O ,\\O so\\O no\\O cooling\\O pad\\O needed\\O ,\\O great\\O feature\\B !\\O", "opinion_tags": "Fan\\O vents\\O to\\O the\\O side\\O ,\\O so\\O no\\O cooling\\O pad\\O needed\\O ,\\O great\\B feature\\O !\\O", "sentiment": "positive"}]}, {"id": "2275", "sentence": "The built in camera is very useful when chatting with other techs in remote buildings on our campus .", "postag": ["DT", "VBN", "IN", "NN", "VBZ", "RB", "JJ", "WRB", "VBG", "IN", "JJ", "NNS", "IN", "JJ", "NNS", "IN", "PRP$", "NN", "."], "head": [4, 4, 2, 7, 7, 7, 0, 9, 7, 12, 12, 9, 15, 15, 12, 18, 18, 9, 7], "deprel": ["det", "amod", "compound", "nsubj", "cop", "advmod", "root", "mark", "advcl", "case", "amod", "obl", "case", "amod", "nmod", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "2275-0", "target_tags": "The\\O built\\B in\\I camera\\I is\\O very\\O useful\\O when\\O chatting\\O with\\O other\\O techs\\O in\\O remote\\O buildings\\O on\\O our\\O campus\\O .\\O", "opinion_tags": "The\\O built\\O in\\O camera\\O is\\O very\\O useful\\B when\\O chatting\\O with\\O other\\O techs\\O in\\O remote\\O buildings\\O on\\O our\\O campus\\O .\\O", "sentiment": "positive"}]}, {"id": "29", "sentence": "I had to adjust my mousepad sensitivity , because it is very sensitive .", "postag": ["PRP", "VBD", "TO", "VB", "PRP$", "NN", "NN", ",", "IN", "PRP", "VBZ", "RB", "JJ", "."], "head": [2, 0, 4, 2, 7, 7, 4, 13, 13, 13, 13, 13, 4, 2], "deprel": ["nsubj", "root", "mark", "xcomp", "nmod:poss", "compound", "obj", "punct", "mark", "nsubj", "cop", "advmod", "advcl", "punct"], "triples": [{"uid": "29-0", "target_tags": "I\\O had\\O to\\O adjust\\O my\\O mousepad\\B sensitivity\\I ,\\O because\\O it\\O is\\O very\\O sensitive\\O .\\O", "opinion_tags": "I\\O had\\O to\\O adjust\\O my\\O mousepad\\O sensitivity\\O ,\\O because\\O it\\O is\\O very\\O sensitive\\B .\\O", "sentiment": "negative"}]}, {"id": "1766", "sentence": "It is sleek and lightweight and charges quickly when needed .", "postag": ["PRP", "VBZ", "JJ", "CC", "JJ", "CC", "VBZ", "RB", "WRB", "VBN", "."], "head": [3, 3, 0, 5, 3, 7, 3, 7, 10, 7, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "cc", "conj", "advmod", "mark", "advcl", "punct"], "triples": [{"uid": "1766-0", "target_tags": "It\\O is\\O sleek\\O and\\O lightweight\\O and\\O charges\\B quickly\\O when\\O needed\\O .\\O", "opinion_tags": "It\\O is\\O sleek\\O and\\O lightweight\\O and\\O charges\\O quickly\\B when\\O needed\\O .\\O", "sentiment": "positive"}]}, {"id": "302", "sentence": "I highly recommend this laptop to anybody that wants great performance from a laptop and would like to relax and not become enraged cursing the gods about to throw your laptop out the door .", "postag": ["PRP", "RB", "VBP", "DT", "NN", "IN", "NN", "WDT", "VBZ", "JJ", "NN", "IN", "DT", "NN", "CC", "MD", "VB", "TO", "VB", "CC", "RB", "VB", "VBN", "VBG", "DT", "NNS", "RB", "TO", "VB", "PRP$", "NN", "IN", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 3, 9, 7, 11, 9, 14, 14, 9, 17, 17, 3, 19, 17, 22, 22, 17, 22, 23, 26, 24, 29, 29, 24, 31, 29, 34, 34, 29, 3], "deprel": ["nsubj", "advmod", "root", "det", "obj", "case", "obl", "nsubj", "acl:relcl", "amod", "obj", "case", "det", "obl", "cc", "aux", "conj", "mark", "xcomp", "cc", "advmod", "conj", "xcomp", "xcomp", "det", "obj", "advmod", "mark", "advcl", "nmod:poss", "obj", "case", "det", "obl", "punct"], "triples": [{"uid": "302-0", "target_tags": "I\\O highly\\O recommend\\O this\\O laptop\\O to\\O anybody\\O that\\O wants\\O great\\O performance\\B from\\O a\\O laptop\\O and\\O would\\O like\\O to\\O relax\\O and\\O not\\O become\\O enraged\\O cursing\\O the\\O gods\\O about\\O to\\O throw\\O your\\O laptop\\O out\\O the\\O door\\O .\\O", "opinion_tags": "I\\O highly\\O recommend\\O this\\O laptop\\O to\\O anybody\\O that\\O wants\\O great\\B performance\\O from\\O a\\O laptop\\O and\\O would\\O like\\O to\\O relax\\O and\\O not\\O become\\O enraged\\O cursing\\O the\\O gods\\O about\\O to\\O throw\\O your\\O laptop\\O out\\O the\\O door\\O .\\O", "sentiment": "positive"}]}, {"id": "1529", "sentence": "A mac is very easy to use and it simply makes sense .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "TO", "VB", "CC", "PRP", "RB", "VBZ", "NN", "."], "head": [2, 5, 5, 5, 0, 7, 5, 11, 11, 11, 5, 11, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "mark", "xcomp", "cc", "nsubj", "advmod", "conj", "obj", "punct"], "triples": [{"uid": "1529-0", "target_tags": "A\\O mac\\O is\\O very\\O easy\\O to\\O use\\B and\\O it\\O simply\\O makes\\O sense\\O .\\O", "opinion_tags": "A\\O mac\\O is\\O very\\O easy\\B to\\O use\\O and\\O it\\O simply\\O makes\\O sense\\O .\\O", "sentiment": "positive"}]}]