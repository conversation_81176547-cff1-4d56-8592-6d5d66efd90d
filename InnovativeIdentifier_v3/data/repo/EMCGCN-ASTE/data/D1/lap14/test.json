[{"id": "892:1", "sentence": "Boot time is super fast , around anywhere from 35 seconds to 1 minute .", "postag": ["NN", "NN", "VBZ", "RB", "JJ", ",", "RB", "RB", "IN", "CD", "NNS", "IN", "CD", "NN", "."], "head": [2, 5, 5, 5, 0, 5, 8, 5, 11, 11, 8, 14, 14, 8, 5], "deprel": ["compound", "nsubj", "cop", "advmod", "root", "punct", "advmod", "advmod", "case", "nummod", "obl", "case", "nummod", "obl", "punct"], "triples": [{"uid": "892:1-0", "target_tags": "Boot\\B time\\I is\\O super\\O fast\\O ,\\O around\\O anywhere\\O from\\O 35\\O seconds\\O to\\O 1\\O minute\\O .\\O", "opinion_tags": "Boot\\O time\\O is\\O super\\O fast\\B ,\\O around\\O anywhere\\O from\\O 35\\O seconds\\O to\\O 1\\O minute\\O .\\O", "sentiment": "positive"}]}, {"id": "1144:1", "sentence": "tech support would not fix the problem unless I bought your plan for $ 150 plus .", "postag": ["NN", "NN", "MD", "RB", "VB", "DT", "NN", "IN", "PRP", "VBD", "PRP$", "NN", "IN", "$", "CD", "CC", "."], "head": [2, 5, 5, 5, 0, 7, 5, 10, 10, 5, 12, 10, 14, 10, 14, 14, 5], "deprel": ["compound", "nsubj", "aux", "advmod", "root", "det", "obj", "mark", "nsubj", "advcl", "nmod:poss", "obj", "case", "obl", "nummod", "advmod", "punct"], "triples": [{"uid": "1144:1-0", "target_tags": "tech\\B support\\I would\\O not\\O fix\\O the\\O problem\\O unless\\O I\\O bought\\O your\\O plan\\O for\\O $\\O 150\\O plus\\O .\\O", "opinion_tags": "tech\\O support\\O would\\O not\\B fix\\I the\\O problem\\O unless\\O I\\O bought\\O your\\O plan\\O for\\O $\\O 150\\O plus\\O .\\O", "sentiment": "negative"}]}, {"id": "359:1", "sentence": "Set up was easy .", "postag": ["VBN", "RP", "VBD", "JJ", "."], "head": [4, 1, 4, 0, 4], "deprel": ["csubj", "compound:prt", "cop", "root", "punct"], "triples": [{"uid": "359:1-0", "target_tags": "Set\\B up\\I was\\O easy\\O .\\O", "opinion_tags": "Set\\O up\\O was\\O easy\\B .\\O", "sentiment": "positive"}]}, {"id": "562:1", "sentence": "Did not enjoy the new Windows 8 and touchscreen functions .", "postag": ["VBD", "RB", "VB", "DT", "JJ", "NNS", "CD", "CC", "NN", "NNS", "."], "head": [3, 3, 0, 6, 6, 3, 6, 10, 10, 6, 3], "deprel": ["aux", "advmod", "root", "det", "amod", "obj", "nummod", "cc", "compound", "conj", "punct"], "triples": [{"uid": "562:1-0", "target_tags": "Did\\O not\\O enjoy\\O the\\O new\\O Windows\\B 8\\I and\\O touchscreen\\O functions\\O .\\O", "opinion_tags": "Did\\O not\\B enjoy\\I the\\O new\\O Windows\\O 8\\O and\\O touchscreen\\O functions\\O .\\O", "sentiment": "negative"}, {"uid": "562:1-1", "target_tags": "Did\\O not\\O enjoy\\O the\\O new\\O Windows\\O 8\\O and\\O touchscreen\\B functions\\I .\\O", "opinion_tags": "Did\\O not\\B enjoy\\I the\\O new\\O Windows\\O 8\\O and\\O touchscreen\\O functions\\O .\\O", "sentiment": "negative"}]}, {"id": "958:1", "sentence": "Other than not being a fan of click pads ( industry standard these days ) and the lousy internal speakers , it 's hard for me to find things about this notebook I do n't like , especially considering the $ 350 price tag .", "postag": ["JJ", "IN", "RB", "VBG", "DT", "NN", "IN", "NN", "NNS", "-LRB-", "NN", "JJ", "DT", "NNS", "-RRB-", "CC", "DT", "JJ", "JJ", "NNS", ",", "PRP", "VBZ", "JJ", "IN", "PRP", "TO", "VB", "NNS", "IN", "DT", "NN", "PRP", "VBP", "RB", "VB", ",", "RB", "VBG", "DT", "$", "CD", "NN", "NN", "."], "head": [24, 6, 6, 6, 6, 24, 9, 9, 6, 12, 12, 9, 14, 12, 12, 20, 20, 20, 20, 9, 24, 24, 24, 0, 28, 28, 28, 24, 28, 32, 32, 29, 36, 36, 36, 32, 24, 39, 24, 44, 44, 41, 44, 39, 24], "deprel": ["advmod", "mark", "advmod", "cop", "det", "advcl", "case", "compound", "nmod", "punct", "nsubj", "parataxis", "det", "obl:tmod", "punct", "cc", "det", "amod", "amod", "conj", "punct", "expl", "cop", "root", "mark", "nsubj", "mark", "csubj", "obj", "case", "det", "nmod", "nsubj", "aux", "advmod", "acl:relcl", "punct", "advmod", "csubj", "det", "compound", "nummod", "compound", "obj", "punct"], "triples": [{"uid": "958:1-0", "target_tags": "Other\\O than\\O not\\O being\\O a\\O fan\\O of\\O click\\O pads\\O (\\O industry\\O standard\\O these\\O days\\O )\\O and\\O the\\O lousy\\O internal\\B speakers\\I ,\\O it\\O 's\\O hard\\O for\\O me\\O to\\O find\\O things\\O about\\O this\\O notebook\\O I\\O do\\O n't\\O like\\O ,\\O especially\\O considering\\O the\\O $\\O 350\\O price\\O tag\\O .\\O", "opinion_tags": "Other\\O than\\O not\\O being\\O a\\O fan\\O of\\O click\\O pads\\O (\\O industry\\O standard\\O these\\O days\\O )\\O and\\O the\\O lousy\\B internal\\O speakers\\O ,\\O it\\O 's\\O hard\\O for\\O me\\O to\\O find\\O things\\O about\\O this\\O notebook\\O I\\O do\\O n't\\O like\\O ,\\O especially\\O considering\\O the\\O $\\O 350\\O price\\O tag\\O .\\O", "sentiment": "negative"}]}, {"id": "282:9", "sentence": "No installation disk ( DVD ) is included .", "postag": ["DT", "NN", "NN", "-LRB-", "NN", "-RRB-", "VBZ", "VBN", "."], "head": [3, 3, 8, 5, 3, 5, 8, 0, 8], "deprel": ["det", "compound", "nsubj:pass", "punct", "appos", "punct", "aux:pass", "root", "punct"], "triples": [{"uid": "282:9-0", "target_tags": "No\\O installation\\B disk\\I (\\O DVD\\O )\\O is\\O included\\O .\\O", "opinion_tags": "No\\O installation\\O disk\\O (\\O DVD\\O )\\O is\\O included\\B .\\O", "sentiment": "neutral"}]}, {"id": "819:1", "sentence": "It 's fast , light , and simple to use .", "postag": ["PRP", "VBZ", "JJ", ",", "JJ", ",", "CC", "JJ", "TO", "VB", "."], "head": [3, 3, 0, 5, 3, 8, 8, 3, 10, 8, 3], "deprel": ["nsubj", "cop", "root", "punct", "conj", "punct", "cc", "conj", "mark", "xcomp", "punct"], "triples": [{"uid": "819:1-0", "target_tags": "It\\O 's\\O fast\\O ,\\O light\\O ,\\O and\\O simple\\O to\\O use\\B .\\O", "opinion_tags": "It\\O 's\\O fast\\B ,\\O light\\B ,\\O and\\O simple\\B to\\O use\\O .\\O", "sentiment": "positive"}]}, {"id": "130:1", "sentence": "Works well , and I am extremely happy to be back to an apple OS .", "postag": ["VBZ", "RB", ",", "CC", "PRP", "VBP", "RB", "JJ", "TO", "VB", "RB", "IN", "DT", "NNP", "NNP", "."], "head": [0, 1, 8, 8, 8, 8, 8, 1, 10, 8, 10, 15, 15, 15, 11, 1], "deprel": ["root", "advmod", "punct", "cc", "nsubj", "cop", "advmod", "conj", "mark", "xcomp", "advmod", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "130:1-0", "target_tags": "Works\\B well\\O ,\\O and\\O I\\O am\\O extremely\\O happy\\O to\\O be\\O back\\O to\\O an\\O apple\\O OS\\O .\\O", "opinion_tags": "Works\\O well\\B ,\\O and\\O I\\O am\\O extremely\\O happy\\O to\\O be\\O back\\O to\\O an\\O apple\\O OS\\O .\\O", "sentiment": "positive"}, {"uid": "130:1-1", "target_tags": "Works\\O well\\O ,\\O and\\O I\\O am\\O extremely\\O happy\\O to\\O be\\O back\\O to\\O an\\O apple\\B OS\\I .\\O", "opinion_tags": "Works\\O well\\O ,\\O and\\O I\\O am\\O extremely\\O happy\\B to\\O be\\O back\\O to\\O an\\O apple\\O OS\\O .\\O", "sentiment": "positive"}]}, {"id": "593:1", "sentence": "Sure it 's not light and slim but the features make up for it 100 % .", "postag": ["JJ", "PRP", "VBZ", "RB", "JJ", "CC", "JJ", "CC", "DT", "NNS", "VBP", "RP", "IN", "PRP", "CD", "NN", "."], "head": [0, 5, 5, 5, 1, 7, 5, 11, 10, 11, 5, 11, 14, 11, 16, 11, 1], "deprel": ["root", "nsubj", "cop", "advmod", "ccomp", "cc", "conj", "cc", "det", "nsubj", "conj", "compound:prt", "case", "obl", "nummod", "obl:tmod", "punct"], "triples": [{"uid": "593:1-0", "target_tags": "Sure\\O it\\O 's\\O not\\O light\\O and\\O slim\\O but\\O the\\O features\\B make\\O up\\O for\\O it\\O 100\\O %\\O .\\O", "opinion_tags": "Sure\\O it\\O 's\\O not\\B light\\I and\\I slim\\I but\\O the\\O features\\O make\\O up\\O for\\O it\\O 100\\O %\\O .\\O", "sentiment": "positive"}]}, {"id": "996:1", "sentence": "I am pleased with the fast log on , speedy WiFi connection and the long battery life ( > 6 hrs ) .", "postag": ["PRP", "VBP", "JJ", "IN", "DT", "JJ", "NN", "IN", ",", "JJ", "NN", "NN", "CC", "DT", "JJ", "NN", "NN", "-LRB-", ",", "CD", "NNS", "-RRB-", "."], "head": [3, 3, 0, 7, 7, 7, 3, 12, 12, 12, 12, 7, 17, 17, 17, 17, 7, 21, 21, 21, 3, 21, 3], "deprel": ["nsubj", "cop", "root", "case", "det", "amod", "obl", "case", "punct", "amod", "compound", "conj", "cc", "det", "amod", "compound", "conj", "punct", "punct", "nummod", "parataxis", "punct", "punct"], "triples": [{"uid": "996:1-0", "target_tags": "I\\O am\\O pleased\\O with\\O the\\O fast\\O log\\B on\\I ,\\O speedy\\O WiFi\\O connection\\O and\\O the\\O long\\O battery\\O life\\O (\\O >\\O 6\\O hrs\\O )\\O .\\O", "opinion_tags": "I\\O am\\O pleased\\B with\\O the\\O fast\\B log\\O on\\O ,\\O speedy\\O WiFi\\O connection\\O and\\O the\\O long\\O battery\\O life\\O (\\O >\\O 6\\O hrs\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "996:1-1", "target_tags": "I\\O am\\O pleased\\O with\\O the\\O fast\\O log\\O on\\O ,\\O speedy\\O WiFi\\B connection\\I and\\O the\\O long\\O battery\\O life\\O (\\O >\\O 6\\O hrs\\O )\\O .\\O", "opinion_tags": "I\\O am\\O pleased\\B with\\O the\\O fast\\O log\\O on\\O ,\\O speedy\\B WiFi\\O connection\\O and\\O the\\O long\\O battery\\O life\\O (\\O >\\O 6\\O hrs\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "996:1-2", "target_tags": "I\\O am\\O pleased\\O with\\O the\\O fast\\O log\\O on\\O ,\\O speedy\\O WiFi\\O connection\\O and\\O the\\O long\\O battery\\B life\\I (\\O >\\O 6\\O hrs\\O )\\O .\\O", "opinion_tags": "I\\O am\\O pleased\\B with\\O the\\O fast\\O log\\O on\\O ,\\O speedy\\O WiFi\\O connection\\O and\\O the\\O long\\B battery\\O life\\O (\\O >\\O 6\\O hrs\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "314:21", "sentence": "The Apple engineers have not yet discovered the delete key .", "postag": ["DT", "NNP", "NNS", "VBP", "RB", "RB", "VBN", "DT", "NN", "NN", "."], "head": [3, 3, 7, 7, 7, 7, 0, 10, 10, 7, 7], "deprel": ["det", "compound", "nsubj", "aux", "advmod", "advmod", "root", "det", "compound", "obj", "punct"], "triples": [{"uid": "314:21-0", "target_tags": "The\\O Apple\\O engineers\\O have\\O not\\O yet\\O discovered\\O the\\O delete\\B key\\I .\\O", "opinion_tags": "The\\O Apple\\O engineers\\O have\\O not\\B yet\\I discovered\\I the\\O delete\\O key\\O .\\O", "sentiment": "negative"}]}, {"id": "147:5", "sentence": "Made interneting ( part of my business ) very difficult to maintain .", "postag": ["VBN", "NN", "-LRB-", "NN", "IN", "PRP$", "NN", "-RRB-", "RB", "JJ", "TO", "VB", "."], "head": [10, 1, 4, 2, 7, 7, 4, 4, 10, 0, 12, 10, 10], "deprel": ["csubj", "obj", "punct", "appos", "case", "nmod:poss", "nmod", "punct", "advmod", "root", "mark", "ccomp", "punct"], "triples": [{"uid": "147:5-0", "target_tags": "Made\\O interneting\\B (\\O part\\O of\\O my\\O business\\O )\\O very\\O difficult\\O to\\O maintain\\O .\\O", "opinion_tags": "Made\\O interneting\\O (\\O part\\O of\\O my\\O business\\O )\\O very\\O difficult\\B to\\O maintain\\O .\\O", "sentiment": "negative"}]}, {"id": "3:1", "sentence": "Luckily , for all of us contemplating the decision , the Mac Mini is priced just right .", "postag": ["RB", ",", "IN", "DT", "IN", "PRP", "VBG", "DT", "NN", ",", "DT", "NNP", "NNP", "VBZ", "VBN", "RB", "RB", "."], "head": [15, 15, 7, 7, 6, 4, 15, 9, 7, 15, 13, 13, 15, 15, 0, 17, 15, 15], "deprel": ["advmod", "punct", "mark", "nsubj", "case", "nmod", "advcl", "det", "obj", "punct", "det", "compound", "nsubj:pass", "aux:pass", "root", "advmod", "advmod", "punct"], "triples": [{"uid": "3:1-0", "target_tags": "Luckily\\O ,\\O for\\O all\\O of\\O us\\O contemplating\\O the\\O decision\\O ,\\O the\\O Mac\\O Mini\\O is\\O priced\\B just\\O right\\O .\\O", "opinion_tags": "Luckily\\O ,\\O for\\O all\\O of\\O us\\O contemplating\\O the\\O decision\\O ,\\O the\\O Mac\\O Mini\\O is\\O priced\\O just\\O right\\B .\\O", "sentiment": "positive"}]}, {"id": "373:1", "sentence": "Only problem that I had was that the track pad was not very good for me , I only had a problem once or twice with it , But probably my computer was a bit defective .", "postag": ["JJ", "NN", "WDT", "PRP", "VBD", "VBD", "IN", "DT", "NN", "NN", "VBD", "RB", "RB", "JJ", "IN", "PRP", ",", "PRP", "RB", "VBD", "DT", "NN", "RB", "CC", "RB", "IN", "PRP", ",", "CC", "RB", "PRP$", "NN", "VBD", "DT", "NN", "JJ", "."], "head": [2, 6, 5, 5, 2, 0, 14, 10, 10, 14, 14, 14, 14, 6, 16, 14, 6, 20, 20, 6, 22, 20, 20, 25, 23, 27, 23, 36, 36, 36, 32, 36, 36, 35, 36, 14, 6], "deprel": ["amod", "nsubj", "obj", "nsubj", "acl:relcl", "root", "mark", "det", "compound", "nsubj", "cop", "advmod", "advmod", "ccomp", "case", "obl", "punct", "nsubj", "advmod", "parataxis", "det", "obj", "advmod", "cc", "conj", "case", "obl", "punct", "cc", "advmod", "nmod:poss", "nsubj", "cop", "det", "obl:npmod", "conj", "punct"], "triples": [{"uid": "373:1-0", "target_tags": "Only\\O problem\\O that\\O I\\O had\\O was\\O that\\O the\\O track\\B pad\\I was\\O not\\O very\\O good\\O for\\O me\\O ,\\O I\\O only\\O had\\O a\\O problem\\O once\\O or\\O twice\\O with\\O it\\O ,\\O But\\O probably\\O my\\O computer\\O was\\O a\\O bit\\O defective\\O .\\O", "opinion_tags": "Only\\O problem\\O that\\O I\\O had\\O was\\O that\\O the\\O track\\O pad\\O was\\O not\\B very\\I good\\I for\\O me\\O ,\\O I\\O only\\O had\\O a\\O problem\\O once\\O or\\O twice\\O with\\O it\\O ,\\O But\\O probably\\O my\\O computer\\O was\\O a\\O bit\\O defective\\O .\\O", "sentiment": "negative"}]}, {"id": "759:1", "sentence": "It is super fast and has outstanding graphics .", "postag": ["PRP", "VBZ", "RB", "JJ", "CC", "VBZ", "JJ", "NNS", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 4], "deprel": ["nsubj", "cop", "advmod", "root", "cc", "conj", "amod", "obj", "punct"], "triples": [{"uid": "759:1-0", "target_tags": "It\\O is\\O super\\O fast\\O and\\O has\\O outstanding\\O graphics\\B .\\O", "opinion_tags": "It\\O is\\O super\\O fast\\O and\\O has\\O outstanding\\B graphics\\O .\\O", "sentiment": "positive"}]}, {"id": "326:1", "sentence": "But the mountain lion is just too slow .", "postag": ["CC", "DT", "NN", "NN", "VBZ", "RB", "RB", "JJ", "."], "head": [8, 4, 4, 8, 8, 8, 8, 0, 8], "deprel": ["cc", "det", "compound", "nsubj", "cop", "advmod", "advmod", "root", "punct"], "triples": [{"uid": "326:1-0", "target_tags": "But\\O the\\O mountain\\B lion\\I is\\O just\\O too\\O slow\\O .\\O", "opinion_tags": "But\\O the\\O mountain\\O lion\\O is\\O just\\O too\\O slow\\B .\\O", "sentiment": "negative"}]}, {"id": "488:1", "sentence": "Strong build though which really adds to its durability .", "postag": ["JJ", "NN", "IN", "WDT", "RB", "VBZ", "IN", "PRP$", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 9, 9, 6, 2], "deprel": ["amod", "root", "mark", "nsubj", "advmod", "acl:relcl", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "488:1-0", "target_tags": "Strong\\O build\\O though\\O which\\O really\\O adds\\O to\\O its\\O durability\\B .\\O", "opinion_tags": "Strong\\B build\\O though\\O which\\O really\\O adds\\O to\\O its\\O durability\\O .\\O", "sentiment": "positive"}, {"uid": "488:1-1", "target_tags": "Strong\\O build\\B though\\O which\\O really\\O adds\\O to\\O its\\O durability\\O .\\O", "opinion_tags": "Strong\\B build\\O though\\O which\\O really\\O adds\\O to\\O its\\O durability\\O .\\O", "sentiment": "positive"}]}, {"id": "847:1", "sentence": "The battery life is excellent -- 6-7 hours without charging .", "postag": ["DT", "NN", "NN", "VBZ", "JJ", ",", "CD", "NNS", "IN", "VBG", "."], "head": [3, 3, 5, 5, 0, 5, 8, 5, 10, 5, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "nummod", "obl:npmod", "mark", "advcl", "punct"], "triples": [{"uid": "847:1-0", "target_tags": "The\\O battery\\B life\\I is\\O excellent\\O --\\O 6-7\\O hours\\O without\\O charging\\O .\\O", "opinion_tags": "The\\O battery\\O life\\O is\\O excellent\\B --\\O 6-7\\O hours\\O without\\O charging\\O .\\O", "sentiment": "positive"}]}, {"id": "561:1", "sentence": "I 've had my computer for 2 weeks already and it works perfectly .", "postag": ["PRP", "VBP", "VBN", "PRP$", "NN", "IN", "CD", "NNS", "RB", "CC", "PRP", "VBZ", "RB", "."], "head": [3, 3, 0, 5, 3, 8, 8, 3, 3, 12, 12, 3, 12, 3], "deprel": ["nsubj", "aux", "root", "nmod:poss", "obj", "case", "nummod", "obl", "advmod", "cc", "nsubj", "conj", "advmod", "punct"], "triples": [{"uid": "561:1-0", "target_tags": "I\\O 've\\O had\\O my\\O computer\\O for\\O 2\\O weeks\\O already\\O and\\O it\\O works\\B perfectly\\O .\\O", "opinion_tags": "I\\O 've\\O had\\O my\\O computer\\O for\\O 2\\O weeks\\O already\\O and\\O it\\O works\\O perfectly\\B .\\O", "sentiment": "positive"}]}, {"id": "1062:1", "sentence": "And I may be the only one but I am really liking Windows 8 .", "postag": ["CC", "PRP", "MD", "VB", "DT", "JJ", "NN", "CC", "PRP", "VBP", "RB", "VBG", "NNP", "CD", "."], "head": [12, 7, 7, 7, 7, 7, 0, 12, 12, 12, 12, 7, 12, 13, 7], "deprel": ["cc", "nsubj", "aux", "cop", "det", "amod", "root", "cc", "nsubj", "aux", "advmod", "conj", "obj", "nummod", "punct"], "triples": [{"uid": "1062:1-0", "target_tags": "And\\O I\\O may\\O be\\O the\\O only\\O one\\O but\\O I\\O am\\O really\\O liking\\O Windows\\B 8\\I .\\O", "opinion_tags": "And\\O I\\O may\\O be\\O the\\O only\\O one\\O but\\O I\\O am\\O really\\O liking\\B Windows\\O 8\\O .\\O", "sentiment": "positive"}]}, {"id": "792:2", "sentence": "The baterry is very longer .", "postag": ["DT", "NN", "VBZ", "RB", "JJR", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "792:2-0", "target_tags": "The\\O baterry\\B is\\O very\\O longer\\O .\\O", "opinion_tags": "The\\O baterry\\O is\\O very\\O longer\\B .\\O", "sentiment": "positive"}]}, {"id": "809:1", "sentence": "Its size is ideal and the weight is acceptable .", "postag": ["PRP$", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 4], "deprel": ["nmod:poss", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "809:1-0", "target_tags": "Its\\O size\\B is\\O ideal\\O and\\O the\\O weight\\O is\\O acceptable\\O .\\O", "opinion_tags": "Its\\O size\\O is\\O ideal\\B and\\O the\\O weight\\O is\\O acceptable\\O .\\O", "sentiment": "positive"}, {"uid": "809:1-1", "target_tags": "Its\\O size\\O is\\O ideal\\O and\\O the\\O weight\\B is\\O acceptable\\O .\\O", "opinion_tags": "Its\\O size\\O is\\O ideal\\O and\\O the\\O weight\\O is\\O acceptable\\B .\\O", "sentiment": "positive"}]}, {"id": "495:1", "sentence": "I can say that I am fully satisfied with the performance that the computer has supplied .", "postag": ["PRP", "MD", "VB", "IN", "PRP", "VBP", "RB", "JJ", "IN", "DT", "NN", "WDT", "DT", "NN", "VBZ", "VBN", "."], "head": [3, 3, 0, 8, 8, 8, 8, 3, 11, 11, 8, 16, 14, 16, 16, 11, 3], "deprel": ["nsubj", "aux", "root", "mark", "nsubj", "cop", "advmod", "ccomp", "case", "det", "obl", "obj", "det", "nsubj", "aux", "acl:relcl", "punct"], "triples": [{"uid": "495:1-0", "target_tags": "I\\O can\\O say\\O that\\O I\\O am\\O fully\\O satisfied\\O with\\O the\\O performance\\B that\\O the\\O computer\\O has\\O supplied\\O .\\O", "opinion_tags": "I\\O can\\O say\\O that\\O I\\O am\\O fully\\O satisfied\\B with\\O the\\O performance\\O that\\O the\\O computer\\O has\\O supplied\\O .\\O", "sentiment": "positive"}]}, {"id": "499:1", "sentence": "It has so much more speed and the screen is very sharp .", "postag": ["PRP", "VBZ", "RB", "RB", "JJR", "NN", "CC", "DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 0, 4, 5, 6, 2, 12, 9, 12, 12, 12, 2, 2], "deprel": ["nsubj", "root", "advmod", "advmod", "amod", "obj", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "499:1-0", "target_tags": "It\\O has\\O so\\O much\\O more\\O speed\\B and\\O the\\O screen\\O is\\O very\\O sharp\\O .\\O", "opinion_tags": "It\\O has\\O so\\O much\\B more\\I speed\\O and\\O the\\O screen\\O is\\O very\\O sharp\\O .\\O", "sentiment": "positive"}, {"uid": "499:1-1", "target_tags": "It\\O has\\O so\\O much\\O more\\O speed\\O and\\O the\\O screen\\B is\\O very\\O sharp\\O .\\O", "opinion_tags": "It\\O has\\O so\\O much\\O more\\O speed\\O and\\O the\\O screen\\O is\\O very\\O sharp\\B .\\O", "sentiment": "positive"}]}, {"id": "636:1", "sentence": "Everything I wanted and everything I needed and the price was great !", "postag": ["NN", "PRP", "VBD", "CC", "NN", "PRP", "VBD", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [12, 3, 1, 5, 1, 7, 5, 12, 10, 12, 12, 0, 12], "deprel": ["nsubj", "nsubj", "acl:relcl", "cc", "conj", "nsubj", "acl:relcl", "cc", "det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "636:1-0", "target_tags": "Everything\\O I\\O wanted\\O and\\O everything\\O I\\O needed\\O and\\O the\\O price\\B was\\O great\\O !\\O", "opinion_tags": "Everything\\O I\\O wanted\\O and\\O everything\\O I\\O needed\\O and\\O the\\O price\\O was\\O great\\B !\\O", "sentiment": "positive"}]}, {"id": "48:1", "sentence": "It 's not inexpensive but the Hardware performance is impressive for a computer this small .", "postag": ["PRP", "VBZ", "RB", "JJ", "CC", "DT", "NN", "NN", "VBZ", "JJ", "IN", "DT", "NN", "DT", "JJ", "."], "head": [4, 4, 4, 0, 10, 8, 8, 10, 10, 4, 13, 13, 10, 15, 13, 4], "deprel": ["nsubj", "cop", "advmod", "root", "cc", "det", "compound", "nsubj", "cop", "conj", "case", "det", "obl", "nsubj", "amod", "punct"], "triples": [{"uid": "48:1-0", "target_tags": "It\\O 's\\O not\\O inexpensive\\O but\\O the\\O Hardware\\B performance\\I is\\O impressive\\O for\\O a\\O computer\\O this\\O small\\O .\\O", "opinion_tags": "It\\O 's\\O not\\O inexpensive\\O but\\O the\\O Hardware\\O performance\\O is\\O impressive\\B for\\O a\\O computer\\O this\\O small\\O .\\O", "sentiment": "positive"}]}, {"id": "57:1", "sentence": "This thing is awesome , everything always works , everything is always easy to set up , everything is compatible , its literally everything I could ask for .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "NN", "RB", "VBZ", ",", "NN", "VBZ", "RB", "JJ", "TO", "VB", "RP", ",", "NN", "VBZ", "JJ", ",", "PRP$", "RB", "NN", "PRP", "MD", "VB", "IN", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 4, 13, 13, 13, 4, 15, 13, 15, 4, 20, 20, 4, 24, 24, 24, 20, 27, 27, 24, 27, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "advmod", "parataxis", "punct", "nsubj", "cop", "advmod", "parataxis", "mark", "xcomp", "compound:prt", "punct", "nsubj", "cop", "parataxis", "punct", "nmod:poss", "advmod", "parataxis", "nsubj", "aux", "acl:relcl", "obl", "punct"], "triples": [{"uid": "57:1-0", "target_tags": "This\\O thing\\O is\\O awesome\\O ,\\O everything\\O always\\O works\\B ,\\O everything\\O is\\O always\\O easy\\O to\\O set\\O up\\O ,\\O everything\\O is\\O compatible\\O ,\\O its\\O literally\\O everything\\O I\\O could\\O ask\\O for\\O .\\O", "opinion_tags": "This\\O thing\\O is\\O awesome\\O ,\\O everything\\O always\\B works\\O ,\\O everything\\O is\\O always\\O easy\\O to\\O set\\O up\\O ,\\O everything\\O is\\O compatible\\O ,\\O its\\O literally\\O everything\\O I\\O could\\O ask\\O for\\O .\\O", "sentiment": "positive"}, {"uid": "57:1-1", "target_tags": "This\\O thing\\O is\\O awesome\\O ,\\O everything\\O always\\O works\\O ,\\O everything\\O is\\O always\\O easy\\O to\\O set\\B up\\I ,\\O everything\\O is\\O compatible\\O ,\\O its\\O literally\\O everything\\O I\\O could\\O ask\\O for\\O .\\O", "opinion_tags": "This\\O thing\\O is\\O awesome\\O ,\\O everything\\O always\\O works\\O ,\\O everything\\O is\\O always\\O easy\\B to\\O set\\O up\\O ,\\O everything\\O is\\O compatible\\O ,\\O its\\O literally\\O everything\\O I\\O could\\O ask\\O for\\O .\\O", "sentiment": "positive"}]}, {"id": "387:7", "sentence": "Keyboard responds well to presses .", "postag": ["NN", "VBZ", "RB", "IN", "NNS", "."], "head": [2, 0, 2, 5, 2, 2], "deprel": ["nsubj", "root", "advmod", "case", "obl", "punct"], "triples": [{"uid": "387:7-0", "target_tags": "Keyboard\\B responds\\O well\\O to\\O presses\\O .\\O", "opinion_tags": "Keyboard\\O responds\\B well\\I to\\O presses\\O .\\O", "sentiment": "positive"}]}, {"id": "1063:166", "sentence": "Lastly , Windows 8 is annoying .", "postag": ["RB", ",", "NNS", "CD", "VBZ", "JJ", "."], "head": [6, 6, 6, 3, 6, 0, 6], "deprel": ["advmod", "punct", "nsubj", "nummod", "cop", "root", "punct"], "triples": [{"uid": "1063:166-0", "target_tags": "Lastly\\O ,\\O Windows\\B 8\\I is\\O annoying\\O .\\O", "opinion_tags": "Lastly\\O ,\\O Windows\\O 8\\O is\\O annoying\\B .\\O", "sentiment": "negative"}]}, {"id": "479:1", "sentence": "Everything is so easy and intuitive to setup or configure .", "postag": ["NN", "VBZ", "RB", "JJ", "CC", "JJ", "TO", "VB", "CC", "VB", "."], "head": [4, 4, 4, 0, 6, 4, 8, 4, 10, 8, 4], "deprel": ["nsubj", "cop", "advmod", "root", "cc", "conj", "mark", "advcl", "cc", "conj", "punct"], "triples": [{"uid": "479:1-0", "target_tags": "Everything\\O is\\O so\\O easy\\O and\\O intuitive\\O to\\O setup\\B or\\O configure\\O .\\O", "opinion_tags": "Everything\\O is\\O so\\O easy\\B and\\O intuitive\\B to\\O setup\\O or\\O configure\\O .\\O", "sentiment": "positive"}, {"uid": "479:1-1", "target_tags": "Everything\\O is\\O so\\O easy\\O and\\O intuitive\\O to\\O setup\\O or\\O configure\\B .\\O", "opinion_tags": "Everything\\O is\\O so\\O easy\\B and\\O intuitive\\B to\\O setup\\O or\\O configure\\O .\\O", "sentiment": "positive"}]}, {"id": "1107:1", "sentence": "Biggest complaint is Windows 8 .", "postag": ["JJS", "NN", "VBZ", "NNS", "CD", "."], "head": [2, 4, 4, 0, 4, 4], "deprel": ["amod", "nsubj", "cop", "root", "nummod", "punct"], "triples": [{"uid": "1107:1-0", "target_tags": "Biggest\\O complaint\\O is\\O Windows\\B 8\\I .\\O", "opinion_tags": "Biggest\\O complaint\\B is\\O Windows\\O 8\\O .\\O", "sentiment": "negative"}]}, {"id": "298:29", "sentence": "Only 2 usb ports ... seems kind of ... limited .", "postag": ["RB", "CD", "NN", "NNS", ",", "VBZ", "NN", "IN", ",", "JJ", "."], "head": [4, 4, 4, 0, 4, 4, 6, 10, 10, 6, 4], "deprel": ["advmod", "nummod", "compound", "root", "punct", "parataxis", "obj", "case", "punct", "obl", "punct"], "triples": [{"uid": "298:29-0", "target_tags": "Only\\O 2\\O usb\\B ports\\I ...\\O seems\\O kind\\O of\\O ...\\O limited\\O .\\O", "opinion_tags": "Only\\O 2\\O usb\\O ports\\O ...\\O seems\\O kind\\O of\\O ...\\O limited\\B .\\O", "sentiment": "negative"}]}, {"id": "1061:1", "sentence": "It has all the expected features and more +plus a wide screen and more than roomy keyboard .", "postag": ["PRP", "VBZ", "PDT", "DT", "VBN", "NNS", "CC", "JJR", "IN", "DT", "JJ", "NN", "CC", "JJR", "IN", "JJ", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 12, 12, 12, 12, 12, 6, 17, 17, 14, 17, 6, 2], "deprel": ["nsubj", "root", "det:predet", "det", "amod", "obj", "cc", "advmod", "case", "det", "amod", "conj", "cc", "advmod", "fixed", "amod", "conj", "punct"], "triples": [{"uid": "1061:1-0", "target_tags": "It\\O has\\O all\\O the\\O expected\\O features\\B and\\O more\\O +plus\\O a\\O wide\\O screen\\O and\\O more\\O than\\O roomy\\O keyboard\\O .\\O", "opinion_tags": "It\\O has\\O all\\O the\\O expected\\B features\\O and\\O more\\O +plus\\O a\\O wide\\O screen\\O and\\O more\\O than\\O roomy\\O keyboard\\O .\\O", "sentiment": "positive"}, {"uid": "1061:1-1", "target_tags": "It\\O has\\O all\\O the\\O expected\\O features\\O and\\O more\\O +plus\\O a\\O wide\\O screen\\B and\\O more\\O than\\O roomy\\O keyboard\\O .\\O", "opinion_tags": "It\\O has\\O all\\O the\\O expected\\O features\\O and\\O more\\O +plus\\O a\\O wide\\B screen\\O and\\O more\\O than\\O roomy\\O keyboard\\O .\\O", "sentiment": "positive"}, {"uid": "1061:1-2", "target_tags": "It\\O has\\O all\\O the\\O expected\\O features\\O and\\O more\\O +plus\\O a\\O wide\\O screen\\O and\\O more\\O than\\O roomy\\O keyboard\\B .\\O", "opinion_tags": "It\\O has\\O all\\O the\\O expected\\O features\\O and\\O more\\O +plus\\O a\\O wide\\O screen\\O and\\O more\\O than\\O roomy\\B keyboard\\O .\\O", "sentiment": "positive"}]}, {"id": "128:1", "sentence": "Amazing Performance for anything I throw at it .", "postag": ["JJ", "NN", "IN", "NN", "PRP", "VBP", "IN", "PRP", "."], "head": [2, 0, 4, 2, 6, 4, 8, 6, 2], "deprel": ["amod", "root", "case", "nmod", "nsubj", "acl:relcl", "case", "obl", "punct"], "triples": [{"uid": "128:1-0", "target_tags": "Amazing\\O Performance\\B for\\O anything\\O I\\O throw\\O at\\O it\\O .\\O", "opinion_tags": "Amazing\\B Performance\\O for\\O anything\\O I\\O throw\\O at\\O it\\O .\\O", "sentiment": "positive"}]}, {"id": "655:1", "sentence": "The receiver was full of superlatives for the quality and performance .", "postag": ["DT", "NN", "VBD", "JJ", "IN", "NNS", "IN", "DT", "NN", "CC", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 9, 9, 6, 11, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "case", "obl", "case", "det", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "655:1-0", "target_tags": "The\\O receiver\\O was\\O full\\O of\\O superlatives\\O for\\O the\\O quality\\B and\\O performance\\O .\\O", "opinion_tags": "The\\O receiver\\O was\\O full\\O of\\O superlatives\\B for\\O the\\O quality\\O and\\O performance\\O .\\O", "sentiment": "positive"}, {"uid": "655:1-1", "target_tags": "The\\O receiver\\O was\\O full\\O of\\O superlatives\\O for\\O the\\O quality\\O and\\O performance\\B .\\O", "opinion_tags": "The\\O receiver\\O was\\O full\\O of\\O superlatives\\B for\\O the\\O quality\\O and\\O performance\\O .\\O", "sentiment": "positive"}]}, {"id": "301:1", "sentence": "I was extremely happy with the OS itself .", "postag": ["PRP", "VBD", "RB", "JJ", "IN", "DT", "NN", "PRP", "."], "head": [4, 4, 4, 0, 7, 7, 4, 7, 4], "deprel": ["nsubj", "cop", "advmod", "root", "case", "det", "obl", "nmod:npmod", "punct"], "triples": [{"uid": "301:1-0", "target_tags": "I\\O was\\O extremely\\O happy\\O with\\O the\\O OS\\B itself\\O .\\O", "opinion_tags": "I\\O was\\O extremely\\O happy\\B with\\O the\\O OS\\O itself\\O .\\O", "sentiment": "positive"}]}, {"id": "448:3", "sentence": "The new MBP offers great portability and gives us confidence that we are not going to need to purchase a new laptop in 18 months .", "postag": ["DT", "JJ", "NNP", "VBZ", "JJ", "NN", "CC", "VBZ", "PRP", "NN", "WDT", "PRP", "VBP", "RB", "VBG", "TO", "VB", "TO", "VB", "DT", "JJ", "NN", "IN", "CD", "NNS", "."], "head": [3, 3, 4, 0, 6, 4, 8, 4, 8, 8, 15, 15, 15, 15, 10, 17, 15, 19, 17, 22, 22, 19, 25, 25, 19, 4], "deprel": ["det", "amod", "nsubj", "root", "amod", "obj", "cc", "conj", "i<PERSON><PERSON>", "obj", "obj", "nsubj", "aux", "advmod", "acl:relcl", "mark", "xcomp", "mark", "xcomp", "det", "amod", "obj", "case", "nummod", "obl", "punct"], "triples": [{"uid": "448:3-0", "target_tags": "The\\O new\\O MBP\\O offers\\O great\\O portability\\B and\\O gives\\O us\\O confidence\\O that\\O we\\O are\\O not\\O going\\O to\\O need\\O to\\O purchase\\O a\\O new\\O laptop\\O in\\O 18\\O months\\O .\\O", "opinion_tags": "The\\O new\\O MBP\\O offers\\O great\\B portability\\O and\\O gives\\O us\\O confidence\\O that\\O we\\O are\\O not\\O going\\O to\\O need\\O to\\O purchase\\O a\\O new\\O laptop\\O in\\O 18\\O months\\O .\\O", "sentiment": "positive"}]}, {"id": "787:444", "sentence": "The criticism has waned , and now I 'd be the first to recommend an Air for truly portable computing .", "postag": ["DT", "NN", "VBZ", "VBN", ",", "CC", "RB", "PRP", "MD", "VB", "DT", "JJ", "TO", "VB", "DT", "NNP", "IN", "RB", "JJ", "NN", "."], "head": [2, 4, 4, 0, 12, 12, 12, 12, 12, 12, 12, 4, 14, 12, 16, 14, 20, 19, 20, 14, 4], "deprel": ["det", "nsubj", "aux", "root", "punct", "cc", "advmod", "nsubj", "aux", "cop", "det", "conj", "mark", "advcl", "det", "obj", "case", "advmod", "amod", "obl", "punct"], "triples": [{"uid": "787:444-0", "target_tags": "The\\O criticism\\O has\\O waned\\O ,\\O and\\O now\\O I\\O 'd\\O be\\O the\\O first\\O to\\O recommend\\O an\\O Air\\O for\\O truly\\O portable\\B computing\\I .\\O", "opinion_tags": "The\\O criticism\\O has\\O waned\\O ,\\O and\\O now\\O I\\O 'd\\O be\\O the\\O first\\O to\\O recommend\\B an\\O Air\\O for\\O truly\\B portable\\O computing\\O .\\O", "sentiment": "positive"}]}, {"id": "787:495", "sentence": "MS Office 2011 for <PERSON> is wonderful , well worth it .", "postag": ["NNP", "NNP", "CD", "IN", "NNP", "VBZ", "JJ", ",", "UH", "JJ", "PRP", "."], "head": [2, 7, 2, 5, 2, 7, 0, 7, 10, 7, 10, 7], "deprel": ["compound", "nsubj", "nummod", "case", "nmod", "cop", "root", "punct", "discourse", "parataxis", "obj", "punct"], "triples": [{"uid": "787:495-0", "target_tags": "MS\\B Office\\I 2011\\I for\\I Mac\\I is\\O wonderful\\O ,\\O well\\O worth\\O it\\O .\\O", "opinion_tags": "MS\\O Office\\O 2011\\O for\\O Mac\\O is\\O wonderful\\B ,\\O well\\B worth\\I it\\O .\\O", "sentiment": "positive"}]}, {"id": "106:1", "sentence": "But the performance of <PERSON> is a huge disappointment .", "postag": ["CC", "DT", "NN", "IN", "NNP", "NNP", "VBZ", "DT", "JJ", "NN", "."], "head": [10, 3, 10, 6, 6, 3, 10, 10, 10, 0, 10], "deprel": ["cc", "det", "nsubj", "case", "compound", "nmod", "cop", "det", "amod", "root", "punct"], "triples": [{"uid": "106:1-0", "target_tags": "But\\O the\\O performance\\B of\\O Mac\\O Mini\\O is\\O a\\O huge\\O disappointment\\O .\\O", "opinion_tags": "But\\O the\\O performance\\O of\\O Mac\\O Mini\\O is\\O a\\O huge\\O disappointment\\B .\\O", "sentiment": "negative"}]}, {"id": "294:1", "sentence": "They do n't just look good ; they deliver excellent performance .", "postag": ["PRP", "VBP", "RB", "RB", "VB", "JJ", ",", "PRP", "VBP", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 5, 5, 9, 5, 11, 9, 5], "deprel": ["nsubj", "aux", "advmod", "advmod", "root", "xcomp", "punct", "nsubj", "parataxis", "amod", "obj", "punct"], "triples": [{"uid": "294:1-0", "target_tags": "They\\O do\\O n't\\O just\\O look\\B good\\O ;\\O they\\O deliver\\O excellent\\O performance\\O .\\O", "opinion_tags": "They\\O do\\O n't\\O just\\O look\\O good\\B ;\\O they\\O deliver\\O excellent\\O performance\\O .\\O", "sentiment": "positive"}, {"uid": "294:1-1", "target_tags": "They\\O do\\O n't\\O just\\O look\\O good\\O ;\\O they\\O deliver\\O excellent\\O performance\\B .\\O", "opinion_tags": "They\\O do\\O n't\\O just\\O look\\O good\\O ;\\O they\\O deliver\\O excellent\\B performance\\O .\\O", "sentiment": "positive"}]}, {"id": "553:1", "sentence": "I have had it over a year now with out a Glitch of any kind..I love the lit up keys and screen display ... this thing is Fast and clear as can be .", "postag": ["PRP", "VBP", "VBN", "PRP", "IN", "DT", "NN", "RB", "IN", "RP", "DT", "NN", "IN", "DT", "NN", "NN", "DT", "VBN", "RP", "NNS", "CC", "NN", "NN", ",", "DT", "NN", "VBZ", "JJ", "CC", "JJ", "IN", "MD", "VB", "."], "head": [3, 3, 0, 3, 7, 7, 3, 3, 12, 12, 12, 3, 16, 16, 16, 12, 20, 20, 18, 12, 23, 23, 20, 3, 26, 28, 28, 3, 30, 28, 33, 33, 28, 3], "deprel": ["nsubj", "aux", "root", "obj", "case", "det", "obl", "advmod", "case", "case", "det", "obl", "case", "det", "compound", "nmod", "det", "amod", "compound:prt", "nmod", "cc", "compound", "conj", "punct", "det", "nsubj", "cop", "parataxis", "cc", "conj", "mark", "aux", "advcl", "punct"], "triples": [{"uid": "553:1-0", "target_tags": "I\\O have\\O had\\O it\\O over\\O a\\O year\\O now\\O with\\O out\\O a\\O Glitch\\O of\\O any\\O kind..I\\O love\\O the\\O lit\\B up\\I keys\\I and\\O screen\\O display\\O ...\\O this\\O thing\\O is\\O Fast\\O and\\O clear\\O as\\O can\\O be\\O .\\O", "opinion_tags": "I\\O have\\O had\\O it\\O over\\O a\\O year\\O now\\O with\\O out\\O a\\O Glitch\\O of\\O any\\O kind..I\\O love\\B the\\O lit\\O up\\O keys\\O and\\O screen\\O display\\O ...\\O this\\O thing\\O is\\O Fast\\B and\\O clear\\B as\\O can\\O be\\O .\\O", "sentiment": "positive"}, {"uid": "553:1-1", "target_tags": "I\\O have\\O had\\O it\\O over\\O a\\O year\\O now\\O with\\O out\\O a\\O Glitch\\O of\\O any\\O kind..I\\O love\\O the\\O lit\\O up\\O keys\\O and\\O screen\\B display\\I ...\\O this\\O thing\\O is\\O Fast\\O and\\O clear\\O as\\O can\\O be\\O .\\O", "opinion_tags": "I\\O have\\O had\\O it\\O over\\O a\\O year\\O now\\O with\\O out\\O a\\O Glitch\\O of\\O any\\O kind..I\\O love\\B the\\O lit\\O up\\O keys\\O and\\O screen\\O display\\O ...\\O this\\O thing\\O is\\O Fast\\B and\\O clear\\B as\\O can\\O be\\O .\\O", "sentiment": "positive"}]}, {"id": "315:1", "sentence": "The Mountain Lion OS is not hard to figure out if you are familiar with Microsoft Windows .", "postag": ["DT", "NNP", "NNP", "NNP", "VBZ", "RB", "JJ", "TO", "VB", "RP", "IN", "PRP", "VBP", "JJ", "IN", "NNP", "NNP", "."], "head": [4, 3, 4, 7, 7, 7, 0, 9, 7, 9, 14, 14, 14, 9, 17, 17, 14, 7], "deprel": ["det", "compound", "compound", "nsubj", "cop", "advmod", "root", "mark", "xcomp", "compound:prt", "mark", "nsubj", "cop", "advcl", "case", "compound", "obl", "punct"], "triples": [{"uid": "315:1-0", "target_tags": "The\\O Mountain\\B Lion\\I OS\\I is\\O not\\O hard\\O to\\O figure\\O out\\O if\\O you\\O are\\O familiar\\O with\\O Microsoft\\O Windows\\O .\\O", "opinion_tags": "The\\O Mountain\\O Lion\\O OS\\O is\\O not\\B hard\\I to\\O figure\\O out\\O if\\O you\\O are\\O familiar\\O with\\O Microsoft\\O Windows\\O .\\O", "sentiment": "positive"}, {"uid": "315:1-1", "target_tags": "The\\O Mountain\\O Lion\\O OS\\O is\\O not\\O hard\\O to\\O figure\\O out\\O if\\O you\\O are\\O familiar\\O with\\O Microsoft\\B Windows\\I .\\O", "opinion_tags": "The\\O Mountain\\O Lion\\O OS\\O is\\O not\\O hard\\O to\\O figure\\O out\\O if\\O you\\O are\\O familiar\\B with\\O Microsoft\\O Windows\\O .\\O", "sentiment": "neutral"}]}, {"id": "17:4", "sentence": "However , I can refute that OSX is `` FAST '' .", "postag": ["RB", ",", "PRP", "MD", "VB", "IN", "NNP", "VBZ", "``", "JJ", "''", "."], "head": [5, 5, 5, 5, 0, 10, 10, 10, 10, 5, 10, 5], "deprel": ["advmod", "punct", "nsubj", "aux", "root", "mark", "nsubj", "cop", "punct", "ccomp", "punct", "punct"], "triples": [{"uid": "17:4-0", "target_tags": "However\\O ,\\O I\\O can\\O refute\\O that\\O OSX\\B is\\O ``\\O FAST\\O ''\\O .\\O", "opinion_tags": "However\\O ,\\O I\\O can\\O refute\\O that\\O OSX\\O is\\O ``\\O FAST\\B ''\\O .\\O", "sentiment": "negative"}]}, {"id": "787:575", "sentence": "Enjoy using Microsoft Office !", "postag": ["VB", "VBG", "NNP", "NNP", "."], "head": [0, 1, 4, 2, 1], "deprel": ["root", "xcomp", "compound", "obj", "punct"], "triples": [{"uid": "787:575-0", "target_tags": "Enjoy\\O using\\O Microsoft\\B Office\\I !\\O", "opinion_tags": "Enjoy\\B using\\O Microsoft\\O Office\\O !\\O", "sentiment": "positive"}]}, {"id": "412:1", "sentence": "Incredible graphics and brilliant colors .", "postag": ["JJ", "NNS", "CC", "JJ", "NNS", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["amod", "root", "cc", "amod", "conj", "punct"], "triples": [{"uid": "412:1-0", "target_tags": "Incredible\\O graphics\\B and\\O brilliant\\O colors\\O .\\O", "opinion_tags": "Incredible\\B graphics\\O and\\O brilliant\\O colors\\O .\\O", "sentiment": "positive"}, {"uid": "412:1-1", "target_tags": "Incredible\\O graphics\\O and\\O brilliant\\O colors\\B .\\O", "opinion_tags": "Incredible\\O graphics\\O and\\O brilliant\\B colors\\O .\\O", "sentiment": "positive"}]}, {"id": "747:5", "sentence": "Built-in apps are purely amazing .", "postag": ["JJ", "NNS", "VBP", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["amod", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "747:5-0", "target_tags": "Built-in\\B apps\\I are\\O purely\\O amazing\\O .\\O", "opinion_tags": "Built-in\\O apps\\O are\\O purely\\O amazing\\B .\\O", "sentiment": "positive"}]}, {"id": "469:1", "sentence": "From the speed to the multi touch gestures this operating system beats Windows easily .", "postag": ["IN", "DT", "NN", "IN", "DT", "JJ", "NN", "NNS", "DT", "NN", "NN", "VBZ", "NNS", "RB", "."], "head": [3, 3, 12, 8, 8, 8, 8, 3, 11, 11, 12, 0, 12, 12, 12], "deprel": ["case", "det", "obl", "case", "det", "amod", "compound", "nmod", "det", "compound", "nsubj", "root", "obj", "advmod", "punct"], "triples": [{"uid": "469:1-0", "target_tags": "From\\O the\\O speed\\O to\\O the\\O multi\\O touch\\O gestures\\O this\\O operating\\B system\\I beats\\O Windows\\O easily\\O .\\O", "opinion_tags": "From\\O the\\O speed\\O to\\O the\\O multi\\O touch\\O gestures\\O this\\O operating\\O system\\O beats\\B Windows\\O easily\\O .\\O", "sentiment": "positive"}]}, {"id": "1028:1", "sentence": "I really like the size and I 'm a fan of the ACERS .", "postag": ["PRP", "RB", "VBP", "DT", "NN", "CC", "PRP", "VBP", "DT", "NN", "IN", "DT", "NNP", "."], "head": [3, 3, 0, 5, 3, 10, 10, 10, 10, 3, 13, 13, 10, 3], "deprel": ["nsubj", "advmod", "root", "det", "obj", "cc", "nsubj", "cop", "det", "conj", "case", "det", "nmod", "punct"], "triples": [{"uid": "1028:1-0", "target_tags": "I\\O really\\O like\\O the\\O size\\B and\\O I\\O 'm\\O a\\O fan\\O of\\O the\\O ACERS\\O .\\O", "opinion_tags": "I\\O really\\O like\\B the\\O size\\O and\\O I\\O 'm\\O a\\O fan\\O of\\O the\\O ACERS\\O .\\O", "sentiment": "positive"}]}, {"id": "286:15", "sentence": "I opted for the SquareTrade 3-Year Computer Accidental Protection Warranty ( $ 1500-2000 ) which also support `` accidents '' like drops and spills that are NOT covered by AppleCare .", "postag": ["PRP", "VBD", "IN", "DT", "NNP", "NNP", "NNP", "NNP", "NNP", "NNP", "-LRB-", "$", "CD", "-RRB-", "WDT", "RB", "VBP", "``", "NNS", "''", "IN", "NNS", "CC", "NNS", "WDT", "VBP", "RB", "VBN", "IN", "NNP", "."], "head": [2, 0, 10, 10, 10, 10, 10, 9, 10, 2, 12, 10, 12, 12, 17, 17, 10, 19, 17, 19, 22, 19, 24, 22, 28, 28, 28, 22, 30, 28, 2], "deprel": ["nsubj", "root", "case", "det", "compound", "compound", "compound", "compound", "compound", "obl", "punct", "appos", "nummod", "punct", "nsubj", "advmod", "acl:relcl", "punct", "obj", "punct", "case", "nmod", "cc", "conj", "nsubj:pass", "aux:pass", "advmod", "acl:relcl", "case", "obl", "punct"], "triples": [{"uid": "286:15-0", "target_tags": "I\\O opted\\O for\\O the\\O SquareTrade\\B 3-Year\\I Computer\\I Accidental\\I Protection\\I Warranty\\I (\\O $\\O 1500-2000\\O )\\O which\\O also\\O support\\O ``\\O accidents\\O ''\\O like\\O drops\\O and\\O spills\\O that\\O are\\O NOT\\O covered\\O by\\O AppleCare\\O .\\O", "opinion_tags": "I\\O opted\\O for\\O the\\O SquareTrade\\O 3-Year\\O Computer\\O Accidental\\O Protection\\O Warranty\\O (\\O $\\O 1500-2000\\O )\\O which\\O also\\O support\\B ``\\O accidents\\O ''\\O like\\O drops\\O and\\O spills\\O that\\O are\\O NOT\\O covered\\O by\\O AppleCare\\O .\\O", "sentiment": "positive"}, {"uid": "286:15-1", "target_tags": "I\\O opted\\O for\\O the\\O SquareTrade\\O 3-Year\\O Computer\\O Accidental\\O Protection\\O Warranty\\O (\\O $\\O 1500-2000\\O )\\O which\\O also\\O support\\O ``\\O accidents\\O ''\\O like\\O drops\\O and\\O spills\\O that\\O are\\O NOT\\O covered\\O by\\O AppleCare\\B .\\O", "opinion_tags": "I\\O opted\\O for\\O the\\O SquareTrade\\O 3-Year\\O Computer\\O Accidental\\O Protection\\O Warranty\\O (\\O $\\O 1500-2000\\O )\\O which\\O also\\O support\\O ``\\O accidents\\O ''\\O like\\O drops\\O and\\O spills\\O that\\O are\\O NOT\\B covered\\I by\\O AppleCare\\O .\\O", "sentiment": "negative"}]}, {"id": "1002:1", "sentence": "It 's light and easy to transport .", "postag": ["PRP", "VBZ", "JJ", "CC", "JJ", "TO", "VB", "."], "head": [3, 3, 0, 5, 3, 7, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "mark", "acl", "punct"], "triples": [{"uid": "1002:1-0", "target_tags": "It\\O 's\\O light\\O and\\O easy\\O to\\O transport\\B .\\O", "opinion_tags": "It\\O 's\\O light\\B and\\O easy\\B to\\O transport\\O .\\O", "sentiment": "positive"}]}, {"id": "1118:1", "sentence": "Once you get past learning how to use the poorly designed Windows 8 Set-Up you may feel frustrated .", "postag": ["IN", "PRP", "VBP", "JJ", "VBG", "WRB", "TO", "VB", "DT", "RB", "VBN", "NNS", "CD", "NN", "PRP", "MD", "VB", "JJ", "."], "head": [3, 3, 17, 5, 3, 8, 8, 5, 12, 11, 12, 8, 14, 8, 17, 17, 0, 17, 17], "deprel": ["mark", "nsubj", "advcl", "amod", "xcomp", "mark", "mark", "xcomp", "det", "advmod", "amod", "obj", "nummod", "obj", "nsubj", "aux", "root", "xcomp", "punct"], "triples": [{"uid": "1118:1-0", "target_tags": "Once\\O you\\O get\\O past\\O learning\\O how\\O to\\O use\\O the\\O poorly\\O designed\\O Windows\\B 8\\I Set-Up\\I you\\O may\\O feel\\O frustrated\\O .\\O", "opinion_tags": "Once\\O you\\O get\\O past\\O learning\\O how\\O to\\O use\\O the\\O poorly\\B designed\\I Windows\\O 8\\O Set-Up\\O you\\O may\\O feel\\O frustrated\\B .\\O", "sentiment": "negative"}]}, {"id": "747:8", "sentence": "The aluminum body sure makes it stand out .", "postag": ["DT", "NN", "NN", "JJ", "VBZ", "PRP", "VB", "RP", "."], "head": [3, 3, 5, 3, 0, 5, 5, 7, 5], "deprel": ["det", "compound", "nsubj", "amod", "root", "obj", "xcomp", "advmod", "punct"], "triples": [{"uid": "747:8-0", "target_tags": "The\\O aluminum\\B body\\I sure\\O makes\\O it\\O stand\\O out\\O .\\O", "opinion_tags": "The\\O aluminum\\O body\\O sure\\O makes\\O it\\O stand\\B out\\I .\\O", "sentiment": "positive"}]}, {"id": "37:1", "sentence": "It is very easy to integrate bluetooth devices , and USB devices are recognized almost instantly .", "postag": ["PRP", "VBZ", "RB", "JJ", "TO", "VB", "NN", "NNS", ",", "CC", "NN", "NNS", "VBP", "VBN", "RB", "RB", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 14, 14, 12, 14, 14, 4, 16, 14, 4], "deprel": ["expl", "cop", "advmod", "root", "mark", "csubj", "compound", "obj", "punct", "cc", "compound", "nsubj:pass", "aux:pass", "conj", "advmod", "advmod", "punct"], "triples": [{"uid": "37:1-0", "target_tags": "It\\O is\\O very\\O easy\\O to\\O integrate\\B bluetooth\\I devices\\I ,\\O and\\O USB\\O devices\\O are\\O recognized\\O almost\\O instantly\\O .\\O", "opinion_tags": "It\\O is\\O very\\O easy\\B to\\O integrate\\O bluetooth\\O devices\\O ,\\O and\\O USB\\O devices\\O are\\O recognized\\O almost\\O instantly\\O .\\O", "sentiment": "positive"}, {"uid": "37:1-1", "target_tags": "It\\O is\\O very\\O easy\\O to\\O integrate\\O bluetooth\\O devices\\O ,\\O and\\O USB\\B devices\\I are\\O recognized\\O almost\\O instantly\\O .\\O", "opinion_tags": "It\\O is\\O very\\O easy\\O to\\O integrate\\O bluetooth\\O devices\\O ,\\O and\\O USB\\O devices\\O are\\O recognized\\O almost\\O instantly\\B .\\O", "sentiment": "positive"}]}, {"id": "891:8", "sentence": "And the fact that Apple is driving the 13 '' RMBP with the Intel4000 graphic chip seems underpowered ( to me .", "postag": ["CC", "DT", "NN", "IN", "NNP", "VBZ", "VBG", "DT", "CD", "''", "NN", "IN", "DT", "NNP", "JJ", "NN", "VBZ", "JJ", "-LRB-", "IN", "PRP", "."], "head": [17, 3, 17, 7, 7, 7, 3, 11, 11, 11, 7, 16, 16, 16, 16, 11, 0, 17, 21, 21, 17, 17], "deprel": ["cc", "det", "nsubj", "mark", "nsubj", "aux", "acl", "det", "nummod", "punct", "obj", "case", "det", "compound", "amod", "nmod", "root", "xcomp", "punct", "case", "obl", "punct"], "triples": [{"uid": "891:8-0", "target_tags": "And\\O the\\O fact\\O that\\O Apple\\O is\\O driving\\O the\\O 13\\O ''\\O RMBP\\O with\\O the\\O Intel4000\\B graphic\\I chip\\I seems\\O underpowered\\O (\\O to\\O me\\O .\\O", "opinion_tags": "And\\O the\\O fact\\O that\\O Apple\\O is\\O driving\\O the\\O 13\\O ''\\O RMBP\\O with\\O the\\O Intel4000\\O graphic\\O chip\\O seems\\O underpowered\\B (\\O to\\O me\\O .\\O", "sentiment": "negative"}]}, {"id": "19:1", "sentence": "Apple removed the DVD drive Firewire port ( will work with adapter ) and put the SDXC slot in a silly position on the back .", "postag": ["NNP", "VBD", "DT", "NN", "NN", "NN", "NN", "-LRB-", "MD", "VB", "IN", "NN", "-RRB-", "CC", "VB", "DT", "NN", "NN", "IN", "DT", "JJ", "NN", "IN", "DT", "NN", "."], "head": [2, 0, 7, 5, 7, 7, 2, 10, 10, 2, 12, 10, 10, 15, 2, 18, 18, 15, 22, 22, 22, 15, 25, 25, 22, 2], "deprel": ["nsubj", "root", "det", "compound", "compound", "compound", "obj", "punct", "aux", "parataxis", "case", "obl", "punct", "cc", "conj", "det", "compound", "obj", "case", "det", "amod", "obl", "case", "det", "nmod", "punct"], "triples": [{"uid": "19:1-0", "target_tags": "Apple\\O removed\\O the\\O DVD\\B drive\\I Firewire\\I port\\I (\\O will\\O work\\O with\\O adapter\\O )\\O and\\O put\\O the\\O SDXC\\O slot\\O in\\O a\\O silly\\O position\\O on\\O the\\O back\\O .\\O", "opinion_tags": "Apple\\O removed\\B the\\O DVD\\O drive\\O Firewire\\O port\\O (\\O will\\O work\\O with\\O adapter\\O )\\O and\\O put\\O the\\O SDXC\\O slot\\O in\\O a\\O silly\\O position\\O on\\O the\\O back\\O .\\O", "sentiment": "neutral"}, {"uid": "19:1-1", "target_tags": "Apple\\O removed\\O the\\O DVD\\O drive\\O Firewire\\O port\\O (\\O will\\O work\\O with\\O adapter\\O )\\O and\\O put\\O the\\O SDXC\\B slot\\I in\\O a\\O silly\\O position\\O on\\O the\\O back\\O .\\O", "opinion_tags": "Apple\\O removed\\O the\\O DVD\\O drive\\O Firewire\\O port\\O (\\O will\\O work\\O with\\O adapter\\O )\\O and\\O put\\O the\\O SDXC\\O slot\\O in\\O a\\O silly\\B position\\O on\\O the\\O back\\O .\\O", "sentiment": "negative"}]}, {"id": "363:27", "sentence": "The durability of the laptop will make it worth the money .", "postag": ["DT", "NN", "IN", "DT", "NN", "MD", "VB", "PRP", "JJ", "DT", "NN", "."], "head": [2, 7, 5, 5, 2, 7, 0, 7, 7, 11, 9, 7], "deprel": ["det", "nsubj", "case", "det", "nmod", "aux", "root", "obj", "xcomp", "det", "obj", "punct"], "triples": [{"uid": "363:27-0", "target_tags": "The\\O durability\\B of\\O the\\O laptop\\O will\\O make\\O it\\O worth\\O the\\O money\\O .\\O", "opinion_tags": "The\\O durability\\O of\\O the\\O laptop\\O will\\O make\\O it\\O worth\\B the\\O money\\O .\\O", "sentiment": "positive"}]}, {"id": "422:1", "sentence": "Well designed and fast .", "postag": ["RB", "VBN", "CC", "JJ", "."], "head": [2, 0, 4, 2, 2], "deprel": ["advmod", "root", "cc", "conj", "punct"], "triples": [{"uid": "422:1-0", "target_tags": "Well\\O designed\\B and\\O fast\\O .\\O", "opinion_tags": "Well\\B designed\\O and\\O fast\\O .\\O", "sentiment": "positive"}]}, {"id": "295:1", "sentence": "But I was completely wrong , this computer is UNBELIEVABLE amazing and easy to use .", "postag": ["CC", "PRP", "VBD", "RB", "JJ", ",", "DT", "NN", "VBZ", "JJ", "JJ", "CC", "JJ", "TO", "VB", "."], "head": [5, 5, 5, 5, 0, 5, 8, 11, 11, 11, 5, 13, 11, 15, 13, 5], "deprel": ["cc", "nsubj", "cop", "advmod", "root", "punct", "det", "nsubj", "cop", "advmod", "parataxis", "cc", "conj", "mark", "xcomp", "punct"], "triples": [{"uid": "295:1-0", "target_tags": "But\\O I\\O was\\O completely\\O wrong\\O ,\\O this\\O computer\\O is\\O UNBELIEVABLE\\O amazing\\O and\\O easy\\O to\\O use\\B .\\O", "opinion_tags": "But\\O I\\O was\\O completely\\O wrong\\O ,\\O this\\O computer\\O is\\O UNBELIEVABLE\\O amazing\\O and\\O easy\\B to\\O use\\O .\\O", "sentiment": "positive"}]}, {"id": "1119:1", "sentence": "Exactly as posted plus a great value .", "postag": ["RB", "IN", "VBN", "CC", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 3], "deprel": ["advmod", "mark", "root", "cc", "det", "amod", "conj", "punct"], "triples": [{"uid": "1119:1-0", "target_tags": "Exactly\\O as\\O posted\\O plus\\O a\\O great\\O value\\B .\\O", "opinion_tags": "Exactly\\O as\\O posted\\O plus\\O a\\O great\\B value\\O .\\O", "sentiment": "positive"}]}, {"id": "29:81", "sentence": "The specs are pretty good too .", "postag": ["DT", "NNS", "VBP", "RB", "JJ", "RB", "."], "head": [2, 5, 5, 5, 0, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "advmod", "punct"], "triples": [{"uid": "29:81-0", "target_tags": "The\\O specs\\B are\\O pretty\\O good\\O too\\O .\\O", "opinion_tags": "The\\O specs\\O are\\O pretty\\O good\\B too\\O .\\O", "sentiment": "positive"}]}, {"id": "211:1", "sentence": "Apple is unmatched in product quality , aesthetics , craftmanship , and customer service .", "postag": ["NNP", "VBZ", "JJ", "IN", "NN", "NN", ",", "NNS", ",", "NN", ",", "CC", "NN", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 8, 6, 10, 6, 14, 14, 14, 6, 3], "deprel": ["nsubj", "cop", "root", "case", "compound", "obl", "punct", "conj", "punct", "conj", "punct", "cc", "compound", "conj", "punct"], "triples": [{"uid": "211:1-0", "target_tags": "Apple\\O is\\O unmatched\\O in\\O product\\B quality\\I ,\\O aesthetics\\O ,\\O craftmanship\\O ,\\O and\\O customer\\O service\\O .\\O", "opinion_tags": "Apple\\O is\\O unmatched\\B in\\O product\\O quality\\O ,\\O aesthetics\\O ,\\O craftmanship\\O ,\\O and\\O customer\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "211:1-1", "target_tags": "Apple\\O is\\O unmatched\\O in\\O product\\O quality\\O ,\\O aesthetics\\B ,\\O craftmanship\\O ,\\O and\\O customer\\O service\\O .\\O", "opinion_tags": "Apple\\O is\\O unmatched\\B in\\O product\\O quality\\O ,\\O aesthetics\\O ,\\O craftmanship\\O ,\\O and\\O customer\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "211:1-2", "target_tags": "Apple\\O is\\O unmatched\\O in\\O product\\O quality\\O ,\\O aesthetics\\O ,\\O craftmanship\\B ,\\O and\\O customer\\O service\\O .\\O", "opinion_tags": "Apple\\O is\\O unmatched\\B in\\O product\\O quality\\O ,\\O aesthetics\\O ,\\O craftmanship\\O ,\\O and\\O customer\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "211:1-3", "target_tags": "Apple\\O is\\O unmatched\\O in\\O product\\O quality\\O ,\\O aesthetics\\O ,\\O craftmanship\\O ,\\O and\\O customer\\B service\\I .\\O", "opinion_tags": "Apple\\O is\\O unmatched\\B in\\O product\\O quality\\O ,\\O aesthetics\\O ,\\O craftmanship\\O ,\\O and\\O customer\\O service\\O .\\O", "sentiment": "positive"}]}, {"id": "1159:1", "sentence": "It is a great size and amazing windows 8 included !", "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "CC", "JJ", "NNS", "CD", "VBN", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 8, 5, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "cc", "amod", "conj", "nummod", "conj", "punct"], "triples": [{"uid": "1159:1-0", "target_tags": "It\\O is\\O a\\O great\\O size\\B and\\O amazing\\O windows\\O 8\\O included\\O !\\O", "opinion_tags": "It\\O is\\O a\\O great\\B size\\O and\\O amazing\\O windows\\O 8\\O included\\O !\\O", "sentiment": "positive"}, {"uid": "1159:1-1", "target_tags": "It\\O is\\O a\\O great\\O size\\O and\\O amazing\\O windows\\B 8\\I included\\O !\\O", "opinion_tags": "It\\O is\\O a\\O great\\O size\\O and\\O amazing\\B windows\\O 8\\O included\\O !\\O", "sentiment": "positive"}]}, {"id": "1150:1", "sentence": "I do not like too much Windows 8 .", "postag": ["PRP", "VBP", "RB", "VB", "RB", "JJ", "NNS", "CD", "."], "head": [4, 4, 4, 0, 6, 7, 4, 7, 4], "deprel": ["nsubj", "aux", "advmod", "root", "advmod", "amod", "obj", "nummod", "punct"], "triples": [{"uid": "1150:1-0", "target_tags": "I\\O do\\O not\\O like\\O too\\O much\\O Windows\\B 8\\I .\\O", "opinion_tags": "I\\O do\\O not\\B like\\I too\\O much\\O Windows\\O 8\\O .\\O", "sentiment": "negative"}]}, {"id": "787:272", "sentence": "Startup times are incredibly long : over two minutes .", "postag": ["NN", "NNS", "VBP", "RB", "JJ", ":", "RB", "CD", "NNS", "."], "head": [2, 5, 5, 5, 0, 5, 8, 9, 5, 5], "deprel": ["compound", "nsubj", "cop", "advmod", "root", "punct", "advmod", "nummod", "obl", "punct"], "triples": [{"uid": "787:272-0", "target_tags": "Startup\\B times\\I are\\O incredibly\\O long\\O :\\O over\\O two\\O minutes\\O .\\O", "opinion_tags": "Startup\\O times\\O are\\O incredibly\\O long\\B :\\O over\\O two\\O minutes\\O .\\O", "sentiment": "negative"}]}, {"id": "743:1", "sentence": "Also stunning colors and speedy", "postag": ["RB", "JJ", "NNS", "CC", "JJ"], "head": [3, 3, 0, 5, 3], "deprel": ["advmod", "amod", "root", "cc", "conj"], "triples": [{"uid": "743:1-0", "target_tags": "Also\\O stunning\\O colors\\B and\\O speedy\\O", "opinion_tags": "Also\\O stunning\\B colors\\O and\\O speedy\\O", "sentiment": "positive"}]}, {"id": "681:1", "sentence": "great price free shipping what else can i ask for ! !", "postag": ["JJ", "NN", "JJ", "NN", "WP", "JJ", "MD", "PRP", "VB", "IN", ".", "."], "head": [2, 0, 4, 2, 9, 5, 9, 9, 2, 5, 2, 2], "deprel": ["amod", "root", "amod", "conj", "obl", "amod", "aux", "nsubj", "parataxis", "case", "punct", "punct"], "triples": [{"uid": "681:1-0", "target_tags": "great\\O price\\B free\\O shipping\\O what\\O else\\O can\\O i\\O ask\\O for\\O !\\O !\\O", "opinion_tags": "great\\B price\\O free\\O shipping\\O what\\O else\\O can\\O i\\O ask\\O for\\O !\\O !\\O", "sentiment": "positive"}, {"uid": "681:1-1", "target_tags": "great\\O price\\O free\\O shipping\\B what\\O else\\O can\\O i\\O ask\\O for\\O !\\O !\\O", "opinion_tags": "great\\O price\\O free\\B shipping\\O what\\O else\\O can\\O i\\O ask\\O for\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "801:1", "sentence": "This mouse is terrific .", "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "801:1-0", "target_tags": "This\\O mouse\\B is\\O terrific\\O .\\O", "opinion_tags": "This\\O mouse\\O is\\O terrific\\B .\\O", "sentiment": "positive"}]}, {"id": "786:1423", "sentence": "It is really thick around the battery .", "postag": ["PRP", "VBZ", "RB", "JJ", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "case", "det", "obl", "punct"], "triples": [{"uid": "786:1423-0", "target_tags": "It\\O is\\O really\\O thick\\O around\\O the\\O battery\\B .\\O", "opinion_tags": "It\\O is\\O really\\O thick\\B around\\O the\\O battery\\O .\\O", "sentiment": "neutral"}]}, {"id": "974:1", "sentence": "And windows 7 works like a charm .", "postag": ["CC", "NNS", "CD", "NNS", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 4], "deprel": ["cc", "nsubj", "nummod", "root", "case", "det", "nmod", "punct"], "triples": [{"uid": "974:1-0", "target_tags": "And\\O windows\\B 7\\I works\\O like\\O a\\O charm\\O .\\O", "opinion_tags": "And\\O windows\\O 7\\O works\\O like\\O a\\O charm\\B .\\O", "sentiment": "positive"}]}, {"id": "605:1", "sentence": ": ) Great product , great price , great delivery , and great service .", "postag": [":", "-RRB-", "JJ", "NN", ",", "JJ", "NN", ",", "JJ", "NN", ",", "CC", "JJ", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 10, 10, 4, 14, 14, 14, 4, 4], "deprel": ["punct", "punct", "amod", "root", "punct", "amod", "conj", "punct", "amod", "conj", "punct", "cc", "amod", "conj", "punct"], "triples": [{"uid": "605:1-0", "target_tags": ":\\O )\\O Great\\O product\\O ,\\O great\\O price\\B ,\\O great\\O delivery\\O ,\\O and\\O great\\O service\\O .\\O", "opinion_tags": ":\\O )\\O Great\\O product\\O ,\\O great\\B price\\O ,\\O great\\O delivery\\O ,\\O and\\O great\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "605:1-1", "target_tags": ":\\O )\\O Great\\O product\\O ,\\O great\\O price\\O ,\\O great\\O delivery\\B ,\\O and\\O great\\O service\\O .\\O", "opinion_tags": ":\\O )\\O Great\\O product\\O ,\\O great\\O price\\O ,\\O great\\B delivery\\O ,\\O and\\O great\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "605:1-2", "target_tags": ":\\O )\\O Great\\O product\\O ,\\O great\\O price\\O ,\\O great\\O delivery\\O ,\\O and\\O great\\O service\\B .\\O", "opinion_tags": ":\\O )\\O Great\\O product\\O ,\\O great\\O price\\O ,\\O great\\O delivery\\O ,\\O and\\O great\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "846:1", "sentence": ": ] It arrived so fast and customer service was great .", "postag": [":", "-RRB-", "PRP", "VBD", "RB", "JJ", "CC", "NN", "NN", "VBD", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 11, 9, 11, 11, 4, 4], "deprel": ["punct", "punct", "nsubj", "root", "advmod", "xcomp", "cc", "compound", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "846:1-0", "target_tags": ":\\O ]\\O It\\O arrived\\O so\\O fast\\O and\\O customer\\B service\\I was\\O great\\O .\\O", "opinion_tags": ":\\O ]\\O It\\O arrived\\O so\\O fast\\O and\\O customer\\O service\\O was\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "983:1", "sentence": "tried windows 8 and hated it ! ! !", "postag": ["VBD", "NNS", "CD", "CC", "VBD", "PRP", ".", ".", "."], "head": [0, 1, 2, 5, 1, 5, 1, 1, 1], "deprel": ["root", "obj", "nummod", "cc", "conj", "obj", "punct", "punct", "punct"], "triples": [{"uid": "983:1-0", "target_tags": "tried\\O windows\\B 8\\I and\\O hated\\O it\\O !\\O !\\O !\\O", "opinion_tags": "tried\\O windows\\O 8\\O and\\O hated\\B it\\O !\\O !\\O !\\O", "sentiment": "negative"}]}, {"id": "723:1", "sentence": "Set up was a breeze .", "postag": ["VBN", "RP", "VBD", "DT", "NN", "."], "head": [5, 1, 5, 5, 0, 5], "deprel": ["csubj", "compound:prt", "cop", "det", "root", "punct"], "triples": [{"uid": "723:1-0", "target_tags": "Set\\B up\\I was\\O a\\O breeze\\O .\\O", "opinion_tags": "Set\\O up\\O was\\O a\\O breeze\\B .\\O", "sentiment": "positive"}]}, {"id": "1122:1", "sentence": "But I do NOT like Win8 .", "postag": ["CC", "PRP", "VBP", "RB", "VB", "NNP", "."], "head": [5, 5, 5, 5, 0, 5, 5], "deprel": ["cc", "nsubj", "aux", "advmod", "root", "obj", "punct"], "triples": [{"uid": "1122:1-0", "target_tags": "But\\O I\\O do\\O NOT\\O like\\O Win8\\B .\\O", "opinion_tags": "But\\O I\\O do\\O NOT\\B like\\I Win8\\O .\\O", "sentiment": "negative"}]}, {"id": "609:1", "sentence": "I had the same reasons as most PC users : the price , the overbearing restrictions of OSX and lack of support for games .", "postag": ["PRP", "VBD", "DT", "JJ", "NNS", "IN", "JJS", "NNP", "NNS", ":", "DT", "NN", ",", "DT", "JJ", "NNS", "IN", "NNP", "CC", "NN", "IN", "NN", "IN", "NNS", "."], "head": [2, 0, 5, 5, 2, 9, 9, 9, 5, 12, 12, 9, 16, 16, 16, 12, 18, 16, 20, 18, 22, 20, 24, 22, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "case", "amod", "compound", "nmod", "punct", "det", "appos", "punct", "det", "amod", "appos", "case", "nmod", "cc", "conj", "case", "nmod", "case", "nmod", "punct"], "triples": [{"uid": "609:1-0", "target_tags": "I\\O had\\O the\\O same\\O reasons\\O as\\O most\\O PC\\O users\\O :\\O the\\O price\\O ,\\O the\\O overbearing\\O restrictions\\O of\\O OSX\\B and\\O lack\\O of\\O support\\O for\\O games\\O .\\O", "opinion_tags": "I\\O had\\O the\\O same\\O reasons\\O as\\O most\\O PC\\O users\\O :\\O the\\O price\\O ,\\O the\\O overbearing\\B restrictions\\O of\\O OSX\\O and\\O lack\\O of\\O support\\O for\\O games\\O .\\O", "sentiment": "negative"}, {"uid": "609:1-1", "target_tags": "I\\O had\\O the\\O same\\O reasons\\O as\\O most\\O PC\\O users\\O :\\O the\\O price\\O ,\\O the\\O overbearing\\O restrictions\\O of\\O OSX\\O and\\O lack\\O of\\O support\\B for\\I games\\I .\\O", "opinion_tags": "I\\O had\\O the\\O same\\O reasons\\O as\\O most\\O PC\\O users\\O :\\O the\\O price\\O ,\\O the\\O overbearing\\O restrictions\\O of\\O OSX\\O and\\O lack\\B of\\O support\\O for\\O games\\O .\\O", "sentiment": "negative"}]}, {"id": "101:1", "sentence": "I wanted it for it 's mobility and man , this little bad boy is very nice .", "postag": ["PRP", "VBD", "PRP", "IN", "PRP", "VBZ", "NN", "CC", "NN", ",", "DT", "JJ", "JJ", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 0, 2, 5, 2, 7, 2, 9, 7, 2, 14, 14, 14, 17, 17, 17, 7, 2], "deprel": ["nsubj", "root", "obj", "case", "obl", "cop", "ccomp", "cc", "conj", "punct", "det", "amod", "amod", "nsubj", "cop", "advmod", "parataxis", "punct"], "triples": [{"uid": "101:1-0", "target_tags": "I\\O wanted\\O it\\O for\\O it\\O 's\\O mobility\\B and\\O man\\O ,\\O this\\O little\\O bad\\O boy\\O is\\O very\\O nice\\O .\\O", "opinion_tags": "I\\O wanted\\O it\\O for\\O it\\O 's\\O mobility\\O and\\O man\\O ,\\O this\\O little\\O bad\\O boy\\O is\\O very\\O nice\\B .\\O", "sentiment": "positive"}]}, {"id": "156:1", "sentence": "I found the mini to be : Exceptionally easy to set up", "postag": ["PRP", "VBD", "DT", "NN", "TO", "VB", ":", "RB", "JJ", "TO", "VB", "RP"], "head": [2, 0, 4, 2, 9, 9, 9, 9, 2, 11, 9, 11], "deprel": ["nsubj", "root", "det", "obj", "mark", "cop", "punct", "advmod", "parataxis", "mark", "ccomp", "compound:prt"], "triples": [{"uid": "156:1-0", "target_tags": "I\\O found\\O the\\O mini\\O to\\O be\\O :\\O Exceptionally\\O easy\\O to\\O set\\B up\\I", "opinion_tags": "I\\O found\\O the\\O mini\\O to\\O be\\O :\\O Exceptionally\\O easy\\B to\\O set\\O up\\O", "sentiment": "positive"}]}, {"id": "480:7", "sentence": "The sound is nice and loud ; I do n't have any problems with hearing anything .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "JJ", ",", "PRP", "VBP", "RB", "VB", "DT", "NNS", "IN", "VBG", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 4, 11, 11, 11, 4, 13, 11, 15, 13, 15, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct", "nsubj", "aux", "advmod", "parataxis", "det", "obj", "mark", "acl", "obj", "punct"], "triples": [{"uid": "480:7-0", "target_tags": "The\\O sound\\B is\\O nice\\O and\\O loud\\O ;\\O I\\O do\\O n't\\O have\\O any\\O problems\\O with\\O hearing\\O anything\\O .\\O", "opinion_tags": "The\\O sound\\O is\\O nice\\B and\\O loud\\B ;\\O I\\O do\\O n't\\O have\\O any\\O problems\\O with\\O hearing\\O anything\\O .\\O", "sentiment": "positive"}]}, {"id": "720:1", "sentence": "It is very slim , the track pad is very much impressed with me .", "postag": ["PRP", "VBZ", "RB", "JJ", ",", "DT", "NN", "NN", "VBZ", "RB", "RB", "JJ", "IN", "PRP", "."], "head": [4, 4, 4, 0, 4, 8, 8, 12, 12, 11, 12, 4, 14, 12, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct", "det", "compound", "nsubj", "cop", "advmod", "advmod", "parataxis", "case", "obl", "punct"], "triples": [{"uid": "720:1-0", "target_tags": "It\\O is\\O very\\O slim\\O ,\\O the\\O track\\B pad\\I is\\O very\\O much\\O impressed\\O with\\O me\\O .\\O", "opinion_tags": "It\\O is\\O very\\O slim\\B ,\\O the\\O track\\O pad\\O is\\O very\\O much\\O impressed\\B with\\O me\\O .\\O", "sentiment": "positive"}]}, {"id": "1122:5", "sentence": "The settings are not user-friendly either .", "postag": ["DT", "NNS", "VBP", "RB", "RB", "RB", "."], "head": [2, 3, 0, 5, 3, 5, 3], "deprel": ["det", "nsubj", "root", "advmod", "advmod", "advmod", "punct"], "triples": [{"uid": "1122:5-0", "target_tags": "The\\O settings\\B are\\O not\\O user-friendly\\O either\\O .\\O", "opinion_tags": "The\\O settings\\O are\\O not\\B user-friendly\\I either\\O .\\O", "sentiment": "negative"}]}, {"id": "1063:180", "sentence": "Thank goodness for OpenOffice !", "postag": ["VBP", "NN", "IN", "NNP", "."], "head": [0, 1, 4, 1, 1], "deprel": ["root", "obj", "case", "obl", "punct"], "triples": [{"uid": "1063:180-0", "target_tags": "Thank\\O goodness\\O for\\O OpenOffice\\B !\\O", "opinion_tags": "Thank\\O goodness\\B for\\O OpenOffice\\O !\\O", "sentiment": "positive"}]}, {"id": "627:1", "sentence": "Awesome form factor , great battery life , wonderful UX .", "postag": ["JJ", "NN", "NN", ",", "JJ", "NN", "NN", ",", "JJ", "NN", "."], "head": [3, 3, 0, 3, 7, 7, 3, 10, 10, 3, 3], "deprel": ["amod", "compound", "root", "punct", "amod", "compound", "conj", "punct", "amod", "conj", "punct"], "triples": [{"uid": "627:1-0", "target_tags": "Awesome\\O form\\B factor\\I ,\\O great\\O battery\\O life\\O ,\\O wonderful\\O UX\\O .\\O", "opinion_tags": "Awesome\\B form\\O factor\\O ,\\O great\\O battery\\O life\\O ,\\O wonderful\\O UX\\O .\\O", "sentiment": "positive"}, {"uid": "627:1-1", "target_tags": "Awesome\\O form\\O factor\\O ,\\O great\\O battery\\B life\\I ,\\O wonderful\\O UX\\O .\\O", "opinion_tags": "Awesome\\O form\\O factor\\O ,\\O great\\B battery\\O life\\O ,\\O wonderful\\O UX\\O .\\O", "sentiment": "positive"}, {"uid": "627:1-2", "target_tags": "Awesome\\O form\\O factor\\O ,\\O great\\O battery\\O life\\O ,\\O wonderful\\O UX\\B .\\O", "opinion_tags": "Awesome\\O form\\O factor\\O ,\\O great\\O battery\\O life\\O ,\\O wonderful\\B UX\\O .\\O", "sentiment": "positive"}]}, {"id": "876:1", "sentence": "i love the keyboard and the screen .", "postag": ["PRP", "VBP", "DT", "NN", "CC", "DT", "NN", "."], "head": [2, 0, 4, 2, 7, 7, 4, 2], "deprel": ["nsubj", "root", "det", "obj", "cc", "det", "conj", "punct"], "triples": [{"uid": "876:1-0", "target_tags": "i\\O love\\O the\\O keyboard\\B and\\O the\\O screen\\O .\\O", "opinion_tags": "i\\O love\\B the\\O keyboard\\O and\\O the\\O screen\\O .\\O", "sentiment": "positive"}, {"uid": "876:1-1", "target_tags": "i\\O love\\O the\\O keyboard\\O and\\O the\\O screen\\B .\\O", "opinion_tags": "i\\O love\\B the\\O keyboard\\O and\\O the\\O screen\\O .\\O", "sentiment": "positive"}]}, {"id": "998:1", "sentence": "However , there are MAJOR issues with the touchpad which render the device nearly useless .", "postag": ["RB", ",", "EX", "VBP", "JJ", "NNS", "IN", "DT", "NN", "WDT", "VBP", "DT", "NN", "RB", "JJ", "."], "head": [4, 1, 4, 0, 6, 4, 9, 9, 6, 11, 9, 13, 11, 15, 11, 4], "deprel": ["advmod", "punct", "expl", "root", "amod", "nsubj", "case", "det", "nmod", "nsubj", "acl:relcl", "det", "obj", "advmod", "xcomp", "punct"], "triples": [{"uid": "998:1-0", "target_tags": "However\\O ,\\O there\\O are\\O MAJOR\\O issues\\O with\\O the\\O touchpad\\B which\\O render\\O the\\O device\\O nearly\\O useless\\O .\\O", "opinion_tags": "However\\O ,\\O there\\O are\\O MAJOR\\O issues\\B with\\O the\\O touchpad\\O which\\O render\\O the\\O device\\O nearly\\O useless\\B .\\O", "sentiment": "negative"}]}, {"id": "104:1", "sentence": "I 've already upgraded o Mavericks and I am impressed with everything about this computer .", "postag": ["PRP", "VBP", "RB", "VBN", "IN", "NNPS", "CC", "PRP", "VBP", "JJ", "IN", "NN", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 10, 10, 10, 4, 12, 10, 15, 15, 12, 4], "deprel": ["nsubj", "aux", "advmod", "root", "case", "obl", "cc", "nsubj", "cop", "conj", "case", "obl", "case", "det", "nmod", "punct"], "triples": [{"uid": "104:1-0", "target_tags": "I\\O 've\\O already\\O upgraded\\O o\\O Mavericks\\B and\\O I\\O am\\O impressed\\O with\\O everything\\O about\\O this\\O computer\\O .\\O", "opinion_tags": "I\\O 've\\O already\\O upgraded\\O o\\O Mavericks\\O and\\O I\\O am\\O impressed\\B with\\O everything\\O about\\O this\\O computer\\O .\\O", "sentiment": "positive"}]}, {"id": "218:1", "sentence": "Not as fast as I would have expect for an i5 .", "postag": ["RB", "RB", "RB", "IN", "PRP", "MD", "VB", "VB", "IN", "DT", "NNP", "."], "head": [3, 3, 0, 8, 8, 8, 8, 3, 11, 11, 8, 1], "deprel": ["advmod", "advmod", "root", "mark", "nsubj", "aux", "aux", "advcl", "case", "det", "obl", "punct"], "triples": [{"uid": "218:1-0", "target_tags": "Not\\O as\\O fast\\O as\\O I\\O would\\O have\\O expect\\O for\\O an\\O i5\\B .\\O", "opinion_tags": "Not\\B as\\I fast\\I as\\O I\\O would\\O have\\O expect\\O for\\O an\\O i5\\O .\\O", "sentiment": "negative"}]}, {"id": "79:1", "sentence": "thanks for great service and shipping !", "postag": ["NN", "IN", "JJ", "NN", "CC", "NN", "."], "head": [0, 4, 4, 1, 6, 4, 1], "deprel": ["root", "case", "amod", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "79:1-0", "target_tags": "thanks\\O for\\O great\\O service\\B and\\O shipping\\O !\\O", "opinion_tags": "thanks\\O for\\O great\\B service\\O and\\O shipping\\O !\\O", "sentiment": "positive"}, {"uid": "79:1-1", "target_tags": "thanks\\O for\\O great\\O service\\O and\\O shipping\\B !\\O", "opinion_tags": "thanks\\O for\\O great\\B service\\O and\\O shipping\\O !\\O", "sentiment": "positive"}]}, {"id": "16:1", "sentence": "The performance seems quite good , and built-in applications like iPhoto work great with my phone and camera .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", ",", "CC", "JJ", "NNS", "IN", "NNP", "VBP", "JJ", "IN", "PRP$", "NN", "CC", "NN", "."], "head": [2, 3, 0, 5, 3, 12, 12, 9, 12, 11, 9, 3, 12, 16, 16, 12, 18, 16, 3], "deprel": ["det", "nsubj", "root", "advmod", "xcomp", "punct", "cc", "amod", "nsubj", "case", "nmod", "conj", "xcomp", "case", "nmod:poss", "obl", "cc", "conj", "punct"], "triples": [{"uid": "16:1-0", "target_tags": "The\\O performance\\B seems\\O quite\\O good\\O ,\\O and\\O built-in\\O applications\\O like\\O iPhoto\\O work\\O great\\O with\\O my\\O phone\\O and\\O camera\\O .\\O", "opinion_tags": "The\\O performance\\O seems\\O quite\\O good\\B ,\\O and\\O built-in\\O applications\\O like\\O iPhoto\\O work\\O great\\O with\\O my\\O phone\\O and\\O camera\\O .\\O", "sentiment": "positive"}, {"uid": "16:1-1", "target_tags": "The\\O performance\\O seems\\O quite\\O good\\O ,\\O and\\O built-in\\B applications\\I like\\O iPhoto\\O work\\O great\\O with\\O my\\O phone\\O and\\O camera\\O .\\O", "opinion_tags": "The\\O performance\\O seems\\O quite\\O good\\O ,\\O and\\O built-in\\O applications\\O like\\O iPhoto\\O work\\O great\\B with\\O my\\O phone\\O and\\O camera\\O .\\O", "sentiment": "positive"}, {"uid": "16:1-2", "target_tags": "The\\O performance\\O seems\\O quite\\O good\\O ,\\O and\\O built-in\\O applications\\O like\\O iPhoto\\B work\\O great\\O with\\O my\\O phone\\O and\\O camera\\O .\\O", "opinion_tags": "The\\O performance\\O seems\\O quite\\O good\\O ,\\O and\\O built-in\\O applications\\O like\\O iPhoto\\O work\\O great\\B with\\O my\\O phone\\O and\\O camera\\O .\\O", "sentiment": "positive"}]}, {"id": "895:1", "sentence": "I did swap out the hard drive for a Samsung 830 SSD which I highly recommend .", "postag": ["PRP", "VBD", "VB", "RP", "DT", "JJ", "NN", "IN", "DT", "NNP", "CD", "NNP", "WDT", "PRP", "RB", "VBP", "."], "head": [3, 3, 0, 3, 7, 7, 3, 12, 12, 12, 10, 7, 16, 16, 16, 12, 3], "deprel": ["nsubj", "aux", "root", "compound:prt", "det", "amod", "obj", "case", "det", "compound", "nummod", "nmod", "obj", "nsubj", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "895:1-0", "target_tags": "I\\O did\\O swap\\O out\\O the\\O hard\\O drive\\O for\\O a\\O Samsung\\B 830\\I SSD\\I which\\O I\\O highly\\O recommend\\O .\\O", "opinion_tags": "I\\O did\\O swap\\O out\\O the\\O hard\\O drive\\O for\\O a\\O Samsung\\O 830\\O SSD\\O which\\O I\\O highly\\O recommend\\B .\\O", "sentiment": "positive"}]}, {"id": "786:876", "sentence": "Yes , that 's a good thing , but it 's made from aluminum that scratches easily .", "postag": ["UH", ",", "DT", "VBZ", "DT", "JJ", "NN", ",", "CC", "PRP", "VBZ", "VBN", "IN", "NN", "WDT", "VBZ", "RB", "."], "head": [7, 7, 7, 7, 7, 7, 0, 12, 12, 12, 12, 7, 14, 12, 16, 14, 16, 7], "deprel": ["discourse", "punct", "nsubj", "cop", "det", "amod", "root", "punct", "cc", "nsubj:pass", "aux:pass", "conj", "case", "obl", "nsubj", "acl:relcl", "advmod", "punct"], "triples": [{"uid": "786:876-0", "target_tags": "Yes\\O ,\\O that\\O 's\\O a\\O good\\O thing\\O ,\\O but\\O it\\O 's\\O made\\O from\\O aluminum\\B that\\O scratches\\O easily\\O .\\O", "opinion_tags": "Yes\\O ,\\O that\\O 's\\O a\\O good\\O thing\\O ,\\O but\\O it\\O 's\\O made\\O from\\O aluminum\\O that\\O scratches\\B easily\\I .\\O", "sentiment": "negative"}]}, {"id": "278:5", "sentence": "I was also informed that the components of the Mac Book were dirty .", "postag": ["PRP", "VBD", "RB", "VBN", "IN", "DT", "NNS", "IN", "DT", "NNP", "NNP", "VBD", "JJ", "."], "head": [4, 4, 4, 0, 13, 7, 13, 11, 11, 11, 7, 13, 4, 4], "deprel": ["nsubj:pass", "aux:pass", "advmod", "root", "mark", "det", "nsubj", "case", "det", "compound", "nmod", "cop", "ccomp", "punct"], "triples": [{"uid": "278:5-0", "target_tags": "I\\O was\\O also\\O informed\\O that\\O the\\O components\\B of\\O the\\O Mac\\O Book\\O were\\O dirty\\O .\\O", "opinion_tags": "I\\O was\\O also\\O informed\\O that\\O the\\O components\\O of\\O the\\O Mac\\O Book\\O were\\O dirty\\B .\\O", "sentiment": "negative"}]}, {"id": "337:1", "sentence": "the hardware problems have been so bad , i ca n't wait till it completely dies in 3 years , TOPS !", "postag": ["DT", "NN", "NNS", "VBP", "VBN", "RB", "JJ", ",", "PRP", "MD", "RB", "VB", "IN", "PRP", "RB", "VBZ", "IN", "CD", "NNS", ",", "NNS", "."], "head": [3, 3, 7, 7, 7, 7, 0, 7, 12, 12, 12, 7, 16, 16, 16, 12, 19, 19, 16, 21, 19, 7], "deprel": ["det", "compound", "nsubj", "aux", "cop", "advmod", "root", "punct", "nsubj", "aux", "advmod", "parataxis", "mark", "nsubj", "advmod", "advcl", "case", "nummod", "obl", "punct", "appos", "punct"], "triples": [{"uid": "337:1-0", "target_tags": "the\\O hardware\\B problems\\O have\\O been\\O so\\O bad\\O ,\\O i\\O ca\\O n't\\O wait\\O till\\O it\\O completely\\O dies\\O in\\O 3\\O years\\O ,\\O TOPS\\O !\\O", "opinion_tags": "the\\O hardware\\O problems\\O have\\O been\\O so\\O bad\\B ,\\O i\\O ca\\O n't\\O wait\\O till\\O it\\O completely\\O dies\\O in\\O 3\\O years\\O ,\\O TOPS\\O !\\O", "sentiment": "negative"}]}, {"id": "857:1", "sentence": "It 's so nice that the battery last so long and that this machine has the snow lion !", "postag": ["PRP", "VBZ", "RB", "JJ", "IN", "DT", "NN", "JJ", "RB", "RB", "CC", "IN", "DT", "NN", "VBZ", "DT", "NN", "NN", "."], "head": [4, 4, 4, 0, 8, 7, 4, 7, 10, 8, 15, 15, 14, 15, 10, 18, 18, 15, 4], "deprel": ["nsubj", "cop", "advmod", "root", "mark", "det", "ccomp", "amod", "advmod", "advmod", "cc", "mark", "det", "nsubj", "conj", "det", "compound", "obj", "punct"], "triples": [{"uid": "857:1-0", "target_tags": "It\\O 's\\O so\\O nice\\O that\\O the\\O battery\\B last\\O so\\O long\\O and\\O that\\O this\\O machine\\O has\\O the\\O snow\\O lion\\O !\\O", "opinion_tags": "It\\O 's\\O so\\O nice\\B that\\O the\\O battery\\O last\\O so\\O long\\B and\\O that\\O this\\O machine\\O has\\O the\\O snow\\O lion\\O !\\O", "sentiment": "positive"}]}, {"id": "679:1", "sentence": "it 's exactly what i wanted , and it has all the new features and whatnot .", "postag": ["PRP", "VBZ", "RB", "WP", "PRP", "VBD", ",", "CC", "PRP", "VBZ", "PDT", "DT", "JJ", "NNS", "CC", "NN", "."], "head": [2, 0, 4, 2, 6, 4, 10, 10, 10, 2, 14, 14, 14, 10, 16, 14, 2], "deprel": ["nsubj", "root", "advmod", "ccomp", "nsubj", "acl:relcl", "punct", "cc", "nsubj", "conj", "det:predet", "det", "amod", "obj", "cc", "conj", "punct"], "triples": [{"uid": "679:1-0", "target_tags": "it\\O 's\\O exactly\\O what\\O i\\O wanted\\O ,\\O and\\O it\\O has\\O all\\O the\\O new\\O features\\B and\\O whatnot\\O .\\O", "opinion_tags": "it\\O 's\\O exactly\\O what\\O i\\O wanted\\B ,\\O and\\O it\\O has\\O all\\O the\\O new\\B features\\O and\\O whatnot\\O .\\O", "sentiment": "positive"}]}, {"id": "1040:1", "sentence": "It feels cheap , the keyboard is not very sensitive .", "postag": ["PRP", "VBZ", "JJ", ",", "DT", "NN", "VBZ", "RB", "RB", "JJ", "."], "head": [2, 0, 2, 2, 6, 10, 10, 10, 10, 2, 2], "deprel": ["nsubj", "root", "xcomp", "punct", "det", "nsubj", "cop", "advmod", "advmod", "parataxis", "punct"], "triples": [{"uid": "1040:1-0", "target_tags": "It\\O feels\\O cheap\\O ,\\O the\\O keyboard\\B is\\O not\\O very\\O sensitive\\O .\\O", "opinion_tags": "It\\O feels\\O cheap\\B ,\\O the\\O keyboard\\O is\\O not\\B very\\I sensitive\\I .\\O", "sentiment": "negative"}]}, {"id": "982:1", "sentence": "Though please note that sometimes it crashes , and the sound quality isnt superb .", "postag": ["IN", "UH", "VB", "IN", "RB", "PRP", "VBZ", ",", "CC", "DT", "NN", "NN", "VBZ", "JJ", "."], "head": [3, 3, 0, 7, 7, 7, 3, 14, 14, 12, 12, 14, 14, 3, 3], "deprel": ["mark", "discourse", "root", "mark", "advmod", "nsubj", "ccomp", "punct", "cc", "det", "compound", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "982:1-0", "target_tags": "Though\\O please\\O note\\O that\\O sometimes\\O it\\O crashes\\O ,\\O and\\O the\\O sound\\B quality\\I isnt\\O superb\\O .\\O", "opinion_tags": "Though\\O please\\O note\\O that\\O sometimes\\O it\\O crashes\\O ,\\O and\\O the\\O sound\\O quality\\O isnt\\B superb\\I .\\O", "sentiment": "negative"}]}, {"id": "748:1", "sentence": "It is very easy to navigate even for a novice .", "postag": ["PRP", "VBZ", "RB", "JJ", "TO", "VB", "RB", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 10, 10, 10, 6, 4], "deprel": ["expl", "cop", "advmod", "root", "mark", "csubj", "advmod", "case", "det", "obl", "punct"], "triples": [{"uid": "748:1-0", "target_tags": "It\\O is\\O very\\O easy\\O to\\O navigate\\B even\\O for\\O a\\O novice\\O .\\O", "opinion_tags": "It\\O is\\O very\\O easy\\B to\\O navigate\\O even\\O for\\O a\\O novice\\O .\\O", "sentiment": "positive"}]}, {"id": "989:1", "sentence": "Does everything I need it to , has a wonderful battery life and I could n't be happier .", "postag": ["VBZ", "NN", "PRP", "VBP", "PRP", "TO", ",", "VBZ", "DT", "JJ", "NN", "NN", "CC", "PRP", "MD", "RB", "VB", "JJR", "."], "head": [8, 8, 4, 2, 4, 4, 8, 0, 12, 12, 12, 8, 18, 18, 18, 18, 18, 8, 8], "deprel": ["aux", "nsubj", "nsubj", "acl:relcl", "obj", "xcomp", "punct", "root", "det", "amod", "compound", "obj", "cc", "nsubj", "aux", "advmod", "cop", "conj", "punct"], "triples": [{"uid": "989:1-0", "target_tags": "Does\\O everything\\O I\\O need\\O it\\O to\\O ,\\O has\\O a\\O wonderful\\O battery\\B life\\I and\\O I\\O could\\O n't\\O be\\O happier\\O .\\O", "opinion_tags": "Does\\O everything\\O I\\O need\\O it\\O to\\O ,\\O has\\O a\\O wonderful\\B battery\\O life\\O and\\O I\\O could\\O n't\\O be\\O happier\\O .\\O", "sentiment": "positive"}]}, {"id": "765:1", "sentence": "Great Performance and Quality .", "postag": ["JJ", "NN", "CC", "NN", "."], "head": [2, 0, 4, 2, 2], "deprel": ["amod", "root", "cc", "conj", "punct"], "triples": [{"uid": "765:1-0", "target_tags": "Great\\O Performance\\B and\\O Quality\\O .\\O", "opinion_tags": "Great\\B Performance\\O and\\O Quality\\O .\\O", "sentiment": "positive"}, {"uid": "765:1-1", "target_tags": "Great\\O Performance\\O and\\O Quality\\B .\\O", "opinion_tags": "Great\\B Performance\\O and\\O Quality\\O .\\O", "sentiment": "positive"}]}, {"id": "787:628", "sentence": "One more thing , this mac does NOT come with restore disks and I am not sure if you can make them direct from the mac like you can with newer PC 's , also the charging cables are made of the same cheap material as the iPhone/iPod touch cables .", "postag": ["CD", "JJR", "NN", ",", "DT", "NN", "VBZ", "RB", "VB", "IN", "VB", "NNS", "CC", "PRP", "VBP", "RB", "JJ", "IN", "PRP", "MD", "VB", "PRP", "VB", "IN", "DT", "NN", "IN", "PRP", "MD", "IN", "JJR", "NNP", "POS", ",", "RB", "DT", "NN", "NNS", "VBP", "VBN", "IN", "DT", "JJ", "JJ", "NN", "IN", "DT", "NNP", "NN", "NNS", "."], "head": [3, 3, 0, 3, 6, 9, 9, 9, 3, 12, 9, 9, 17, 17, 17, 17, 3, 21, 21, 21, 17, 21, 21, 26, 26, 23, 29, 29, 23, 32, 32, 29, 32, 40, 40, 38, 38, 40, 40, 17, 45, 45, 45, 45, 40, 50, 50, 50, 50, 40, 3], "deprel": ["nummod", "amod", "root", "punct", "det", "nsubj", "aux", "advmod", "parataxis", "case", "advcl", "obl", "cc", "nsubj", "cop", "advmod", "conj", "mark", "nsubj", "aux", "advcl", "obj", "xcomp", "case", "det", "obl", "mark", "nsubj", "advcl", "case", "amod", "obl", "case", "punct", "advmod", "det", "compound", "nsubj:pass", "aux:pass", "parataxis", "case", "det", "amod", "amod", "obl", "case", "det", "compound", "compound", "obl", "punct"], "triples": [{"uid": "787:628-0", "target_tags": "One\\O more\\O thing\\O ,\\O this\\O mac\\O does\\O NOT\\O come\\O with\\O restore\\O disks\\O and\\O I\\O am\\O not\\O sure\\O if\\O you\\O can\\O make\\O them\\O direct\\O from\\O the\\O mac\\O like\\O you\\O can\\O with\\O newer\\O PC\\O 's\\O ,\\O also\\O the\\O charging\\O cables\\O are\\O made\\O of\\O the\\O same\\O cheap\\O material\\B as\\O the\\O iPhone/iPod\\O touch\\O cables\\O .\\O", "opinion_tags": "One\\O more\\O thing\\O ,\\O this\\O mac\\O does\\O NOT\\O come\\O with\\O restore\\O disks\\O and\\O I\\O am\\O not\\O sure\\O if\\O you\\O can\\O make\\O them\\O direct\\O from\\O the\\O mac\\O like\\O you\\O can\\O with\\O newer\\O PC\\O 's\\O ,\\O also\\O the\\O charging\\O cables\\O are\\O made\\O of\\O the\\O same\\O cheap\\B material\\O as\\O the\\O iPhone/iPod\\O touch\\O cables\\O .\\O", "sentiment": "negative"}]}, {"id": "580:1", "sentence": "The new os is great on my macbook pro !", "postag": ["DT", "JJ", "NN", "VBZ", "JJ", "IN", "PRP$", "NN", "NN", "."], "head": [3, 3, 5, 5, 0, 9, 9, 9, 5, 5], "deprel": ["det", "amod", "nsubj", "cop", "root", "case", "nmod:poss", "compound", "obl", "punct"], "triples": [{"uid": "580:1-0", "target_tags": "The\\O new\\O os\\B is\\O great\\O on\\O my\\O macbook\\O pro\\O !\\O", "opinion_tags": "The\\O new\\O os\\O is\\O great\\B on\\O my\\O macbook\\O pro\\O !\\O", "sentiment": "positive"}]}, {"id": "705:1", "sentence": "I have experienced no problems , works as anticipated .", "postag": ["PRP", "VBP", "VBN", "DT", "NNS", ",", "NNS", "IN", "VBN", "."], "head": [3, 3, 0, 5, 3, 7, 5, 9, 3, 3], "deprel": ["nsubj", "aux", "root", "det", "obj", "punct", "conj", "mark", "advcl", "punct"], "triples": [{"uid": "705:1-0", "target_tags": "I\\O have\\O experienced\\O no\\O problems\\O ,\\O works\\B as\\O anticipated\\O .\\O", "opinion_tags": "I\\O have\\O experienced\\O no\\O problems\\O ,\\O works\\O as\\B anticipated\\I .\\O", "sentiment": "positive"}]}, {"id": "873:1", "sentence": "System is running great .", "postag": ["NN", "VBZ", "VBG", "JJ", "."], "head": [3, 3, 0, 3, 3], "deprel": ["nsubj", "aux", "root", "xcomp", "punct"], "triples": [{"uid": "873:1-0", "target_tags": "System\\B is\\O running\\O great\\O .\\O", "opinion_tags": "System\\O is\\O running\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "633:1", "sentence": "Easy to customize setting and even create your own bookmarks .", "postag": ["JJ", "TO", "VB", "NN", "CC", "RB", "VB", "PRP$", "JJ", "NNS", "."], "head": [0, 3, 1, 3, 7, 7, 1, 10, 10, 7, 1], "deprel": ["root", "mark", "csubj", "obj", "cc", "advmod", "conj", "nmod:poss", "amod", "obj", "punct"], "triples": [{"uid": "633:1-0", "target_tags": "Easy\\O to\\O customize\\B setting\\I and\\O even\\O create\\O your\\O own\\O bookmarks\\O .\\O", "opinion_tags": "Easy\\B to\\O customize\\O setting\\O and\\O even\\O create\\O your\\O own\\O bookmarks\\O .\\O", "sentiment": "positive"}, {"uid": "633:1-1", "target_tags": "Easy\\O to\\O customize\\O setting\\O and\\O even\\O create\\B your\\I own\\I bookmarks\\I .\\O", "opinion_tags": "Easy\\B to\\O customize\\O setting\\O and\\O even\\O create\\O your\\O own\\O bookmarks\\O .\\O", "sentiment": "positive"}]}, {"id": "1076:1", "sentence": "it has all the features that we expected and the price was good , working well so far .", "postag": ["PRP", "VBZ", "PDT", "DT", "NNS", "WDT", "PRP", "VBD", "CC", "DT", "NN", "VBD", "JJ", ",", "VBG", "RB", "RB", "RB", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 13, 11, 13, 13, 2, 13, 13, 15, 18, 15, 2], "deprel": ["nsubj", "root", "det:predet", "det", "obj", "obj", "nsubj", "acl:relcl", "cc", "det", "nsubj", "cop", "conj", "punct", "advcl", "advmod", "advmod", "advmod", "punct"], "triples": [{"uid": "1076:1-0", "target_tags": "it\\O has\\O all\\O the\\O features\\O that\\O we\\O expected\\O and\\O the\\O price\\B was\\O good\\O ,\\O working\\O well\\O so\\O far\\O .\\O", "opinion_tags": "it\\O has\\O all\\O the\\O features\\O that\\O we\\O expected\\O and\\O the\\O price\\O was\\O good\\B ,\\O working\\O well\\O so\\O far\\O .\\O", "sentiment": "positive"}, {"uid": "1076:1-1", "target_tags": "it\\O has\\O all\\O the\\O features\\O that\\O we\\O expected\\O and\\O the\\O price\\O was\\O good\\O ,\\O working\\B well\\O so\\O far\\O .\\O", "opinion_tags": "it\\O has\\O all\\O the\\O features\\O that\\O we\\O expected\\O and\\O the\\O price\\O was\\O good\\O ,\\O working\\O well\\B so\\O far\\O .\\O", "sentiment": "positive"}]}, {"id": "453:1", "sentence": "The new operating system makes this computer into a super iPad .", "postag": ["DT", "JJ", "NN", "NN", "VBZ", "DT", "NN", "IN", "DT", "JJ", "NNP", "."], "head": [4, 4, 4, 5, 0, 7, 5, 11, 11, 11, 5, 5], "deprel": ["det", "amod", "compound", "nsubj", "root", "det", "obj", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "453:1-0", "target_tags": "The\\O new\\O operating\\B system\\I makes\\O this\\O computer\\O into\\O a\\O super\\O iPad\\O .\\O", "opinion_tags": "The\\O new\\B operating\\O system\\O makes\\O this\\O computer\\O into\\O a\\O super\\O iPad\\O .\\O", "sentiment": "positive"}]}, {"id": "179:1", "sentence": "Easy to set up and go !", "postag": ["JJ", "TO", "VB", "RP", "CC", "VB", "."], "head": [0, 3, 1, 3, 6, 3, 1], "deprel": ["root", "mark", "csubj", "compound:prt", "cc", "conj", "punct"], "triples": [{"uid": "179:1-0", "target_tags": "Easy\\O to\\O set\\B up\\I and\\O go\\O !\\O", "opinion_tags": "Easy\\B to\\O set\\O up\\O and\\O go\\O !\\O", "sentiment": "positive"}]}, {"id": "900:1", "sentence": "I ca n't believe how quiet the hard drive is and how quick this thing boots up .", "postag": ["PRP", "MD", "RB", "VB", "WRB", "JJ", "DT", "JJ", "NN", "VBZ", "CC", "WRB", "JJ", "DT", "NN", "VBZ", "RP", "."], "head": [4, 4, 4, 0, 6, 4, 9, 9, 6, 6, 16, 13, 16, 15, 16, 6, 16, 4], "deprel": ["nsubj", "aux", "advmod", "root", "mark", "ccomp", "det", "amod", "nsubj", "cop", "cc", "mark", "xcomp", "det", "nsubj", "conj", "compound:prt", "punct"], "triples": [{"uid": "900:1-0", "target_tags": "I\\O ca\\O n't\\O believe\\O how\\O quiet\\O the\\O hard\\B drive\\I is\\O and\\O how\\O quick\\O this\\O thing\\O boots\\O up\\O .\\O", "opinion_tags": "I\\O ca\\O n't\\O believe\\O how\\O quiet\\B the\\O hard\\O drive\\O is\\O and\\O how\\O quick\\O this\\O thing\\O boots\\O up\\O .\\O", "sentiment": "positive"}, {"uid": "900:1-1", "target_tags": "I\\O ca\\O n't\\O believe\\O how\\O quiet\\O the\\O hard\\O drive\\O is\\O and\\O how\\O quick\\O this\\O thing\\O boots\\B up\\I .\\O", "opinion_tags": "I\\O ca\\O n't\\O believe\\O how\\O quiet\\O the\\O hard\\O drive\\O is\\O and\\O how\\O quick\\B this\\O thing\\O boots\\O up\\O .\\O", "sentiment": "positive"}]}, {"id": "945:1", "sentence": "It 's just what we were looking for and it works great .", "postag": ["PRP", "VBZ", "RB", "WP", "PRP", "VBD", "VBG", "IN", "CC", "PRP", "VBZ", "JJ", "."], "head": [2, 0, 2, 2, 7, 7, 4, 7, 11, 11, 2, 11, 2], "deprel": ["nsubj", "root", "advmod", "ccomp", "nsubj", "aux", "acl:relcl", "obl", "cc", "nsubj", "conj", "xcomp", "punct"], "triples": [{"uid": "945:1-0", "target_tags": "It\\O 's\\O just\\O what\\O we\\O were\\O looking\\O for\\O and\\O it\\O works\\B great\\O .\\O", "opinion_tags": "It\\O 's\\O just\\O what\\O we\\O were\\O looking\\O for\\O and\\O it\\O works\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "709:1", "sentence": "It 's so quick and responsive that it makes working / surfing on a computer so much more pleasurable !", "postag": ["PRP", "VBZ", "RB", "JJ", "CC", "JJ", "IN", "PRP", "VBZ", "VBG", ",", "VBG", "IN", "DT", "NN", "RB", "RB", "RBR", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 9, 9, 4, 9, 12, 10, 15, 15, 10, 17, 18, 19, 9, 4], "deprel": ["nsubj", "cop", "advmod", "root", "cc", "conj", "mark", "nsubj", "ccomp", "xcomp", "cc", "conj", "case", "det", "obl", "advmod", "advmod", "advmod", "xcomp", "punct"], "triples": [{"uid": "709:1-0", "target_tags": "It\\O 's\\O so\\O quick\\O and\\O responsive\\O that\\O it\\O makes\\O working\\B /\\O surfing\\O on\\O a\\O computer\\O so\\O much\\O more\\O pleasurable\\O !\\O", "opinion_tags": "It\\O 's\\O so\\O quick\\O and\\O responsive\\O that\\O it\\O makes\\O working\\O /\\O surfing\\O on\\O a\\O computer\\O so\\O much\\O more\\O pleasurable\\B !\\O", "sentiment": "positive"}, {"uid": "709:1-1", "target_tags": "It\\O 's\\O so\\O quick\\O and\\O responsive\\O that\\O it\\O makes\\O working\\O /\\O surfing\\B on\\O a\\O computer\\O so\\O much\\O more\\O pleasurable\\O !\\O", "opinion_tags": "It\\O 's\\O so\\O quick\\O and\\O responsive\\O that\\O it\\O makes\\O working\\O /\\O surfing\\O on\\O a\\O computer\\O so\\O much\\O more\\O pleasurable\\B !\\O", "sentiment": "positive"}]}, {"id": "87:1", "sentence": "It works fine , and all the software seems to run pretty well .", "postag": ["PRP", "VBZ", "JJ", ",", "CC", "PDT", "DT", "NN", "VBZ", "TO", "VB", "RB", "RB", "."], "head": [2, 0, 2, 9, 9, 8, 8, 9, 2, 11, 9, 13, 11, 2], "deprel": ["nsubj", "root", "xcomp", "punct", "cc", "det:predet", "det", "nsubj", "conj", "mark", "xcomp", "advmod", "advmod", "punct"], "triples": [{"uid": "87:1-0", "target_tags": "It\\O works\\B fine\\O ,\\O and\\O all\\O the\\O software\\O seems\\O to\\O run\\O pretty\\O well\\O .\\O", "opinion_tags": "It\\O works\\O fine\\B ,\\O and\\O all\\O the\\O software\\O seems\\O to\\O run\\O pretty\\O well\\O .\\O", "sentiment": "positive"}, {"uid": "87:1-1", "target_tags": "It\\O works\\O fine\\O ,\\O and\\O all\\O the\\O software\\B seems\\O to\\O run\\O pretty\\O well\\O .\\O", "opinion_tags": "It\\O works\\O fine\\O ,\\O and\\O all\\O the\\O software\\O seems\\O to\\O run\\O pretty\\O well\\B .\\O", "sentiment": "positive"}]}, {"id": "383:1", "sentence": "I wanted a computer that was quite , fast , and that had overall great performance .", "postag": ["PRP", "VBD", "DT", "NN", "WDT", "VBD", "RB", ",", "JJ", ",", "CC", "DT", "VBD", "JJ", "JJ", "NN", "."], "head": [2, 0, 4, 2, 9, 9, 9, 9, 4, 13, 13, 13, 2, 16, 16, 13, 2], "deprel": ["nsubj", "root", "det", "obj", "nsubj", "cop", "advmod", "punct", "acl:relcl", "punct", "cc", "nsubj", "conj", "amod", "amod", "obj", "punct"], "triples": [{"uid": "383:1-0", "target_tags": "I\\O wanted\\O a\\O computer\\O that\\O was\\O quite\\O ,\\O fast\\O ,\\O and\\O that\\O had\\O overall\\O great\\O performance\\B .\\O", "opinion_tags": "I\\O wanted\\O a\\O computer\\O that\\O was\\O quite\\O ,\\O fast\\O ,\\O and\\O that\\O had\\O overall\\O great\\B performance\\O .\\O", "sentiment": "neutral"}]}, {"id": "279:0", "sentence": "Harddrive was in poor condition , had to replace it .", "postag": ["NNP", "VBD", "IN", "JJ", "NN", ",", "VBD", "TO", "VB", "PRP", "."], "head": [5, 5, 5, 5, 0, 7, 5, 9, 7, 9, 5], "deprel": ["nsubj", "cop", "case", "amod", "root", "punct", "parataxis", "mark", "xcomp", "obj", "punct"], "triples": [{"uid": "279:0-0", "target_tags": "Harddrive\\B was\\O in\\O poor\\O condition\\O ,\\O had\\O to\\O replace\\O it\\O .\\O", "opinion_tags": "Harddrive\\O was\\O in\\O poor\\B condition\\O ,\\O had\\O to\\O replace\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "200:1", "sentence": "The on/off switch is a bit obscure in the rear corner .", "postag": ["DT", "NN", "NN", "VBZ", "DT", "NN", "JJ", "IN", "DT", "JJ", "NN", "."], "head": [3, 3, 7, 7, 6, 7, 0, 11, 11, 11, 7, 7], "deprel": ["det", "compound", "nsubj", "cop", "det", "obl:npmod", "root", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "200:1-0", "target_tags": "The\\O on/off\\B switch\\I is\\O a\\O bit\\O obscure\\O in\\O the\\O rear\\O corner\\O .\\O", "opinion_tags": "The\\O on/off\\O switch\\O is\\O a\\O bit\\O obscure\\B in\\O the\\O rear\\O corner\\O .\\O", "sentiment": "negative"}]}, {"id": "28:1", "sentence": "My only complaint is the total lack of instructions that come with the mac mini .", "postag": ["PRP$", "JJ", "NN", "VBZ", "DT", "JJ", "NN", "IN", "NNS", "WDT", "VBP", "IN", "DT", "NN", "NN", "."], "head": [3, 3, 7, 7, 7, 7, 0, 9, 7, 11, 9, 15, 15, 15, 11, 7], "deprel": ["nmod:poss", "amod", "nsubj", "cop", "det", "amod", "root", "case", "nmod", "nsubj", "acl:relcl", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "28:1-0", "target_tags": "My\\O only\\O complaint\\O is\\O the\\O total\\O lack\\O of\\O instructions\\B that\\O come\\O with\\O the\\O mac\\O mini\\O .\\O", "opinion_tags": "My\\O only\\O complaint\\O is\\O the\\O total\\O lack\\B of\\O instructions\\O that\\O come\\O with\\O the\\O mac\\O mini\\O .\\O", "sentiment": "negative"}]}, {"id": "2:1", "sentence": "The only task that this computer would not be good enough for would be gaming , otherwise the integrated Intel 4000 graphics work well for other tasks .", "postag": ["DT", "JJ", "NN", "WDT", "DT", "NN", "MD", "RB", "VB", "JJ", "JJ", "IN", "MD", "VB", "VBG", ",", "RB", "DT", "VBN", "NNP", "CD", "NNS", "VBP", "RB", "IN", "JJ", "NNS", "."], "head": [3, 3, 15, 10, 6, 10, 10, 10, 10, 3, 10, 10, 15, 15, 0, 15, 23, 22, 22, 22, 22, 23, 15, 23, 27, 27, 23, 15], "deprel": ["det", "amod", "nsubj", "obj", "det", "nsubj", "aux", "advmod", "cop", "acl:relcl", "advmod", "obl", "aux", "aux", "root", "punct", "advmod", "det", "amod", "compound", "nummod", "nsubj", "parataxis", "advmod", "case", "amod", "obl", "punct"], "triples": [{"uid": "2:1-0", "target_tags": "The\\O only\\O task\\O that\\O this\\O computer\\O would\\O not\\O be\\O good\\O enough\\O for\\O would\\O be\\O gaming\\B ,\\O otherwise\\O the\\O integrated\\O Intel\\O 4000\\O graphics\\O work\\O well\\O for\\O other\\O tasks\\O .\\O", "opinion_tags": "The\\O only\\O task\\O that\\O this\\O computer\\O would\\O not\\B be\\I good\\I enough\\O for\\O would\\O be\\O gaming\\O ,\\O otherwise\\O the\\O integrated\\O Intel\\O 4000\\O graphics\\O work\\O well\\O for\\O other\\O tasks\\O .\\O", "sentiment": "negative"}]}, {"id": "923:1", "sentence": "I use it mostly for content creation ( Audio , video , photo editing ) and its reliable .", "postag": ["PRP", "VBP", "PRP", "RB", "IN", "NN", "NN", "-LRB-", "NN", ",", "NN", ",", "NN", "NN", "-RRB-", "CC", "PRP$", "JJ", "."], "head": [2, 0, 2, 2, 7, 7, 2, 9, 7, 11, 9, 14, 14, 9, 9, 18, 18, 9, 2], "deprel": ["nsubj", "root", "obj", "advmod", "case", "compound", "obl", "punct", "appos", "punct", "conj", "punct", "compound", "conj", "punct", "cc", "nmod:poss", "conj", "punct"], "triples": [{"uid": "923:1-0", "target_tags": "I\\O use\\O it\\O mostly\\O for\\O content\\O creation\\O (\\O Audio\\B ,\\O video\\O ,\\O photo\\O editing\\O )\\O and\\O its\\O reliable\\O .\\O", "opinion_tags": "I\\O use\\O it\\O mostly\\O for\\O content\\O creation\\O (\\O Audio\\O ,\\O video\\O ,\\O photo\\O editing\\O )\\O and\\O its\\O reliable\\B .\\O", "sentiment": "positive"}, {"uid": "923:1-1", "target_tags": "I\\O use\\O it\\O mostly\\O for\\O content\\O creation\\O (\\O Audio\\O ,\\O video\\B ,\\O photo\\O editing\\O )\\O and\\O its\\O reliable\\O .\\O", "opinion_tags": "I\\O use\\O it\\O mostly\\O for\\O content\\O creation\\O (\\O Audio\\O ,\\O video\\O ,\\O photo\\O editing\\O )\\O and\\O its\\O reliable\\B .\\O", "sentiment": "positive"}, {"uid": "923:1-2", "target_tags": "I\\O use\\O it\\O mostly\\O for\\O content\\O creation\\O (\\O Audio\\O ,\\O video\\O ,\\O photo\\B editing\\I )\\O and\\O its\\O reliable\\O .\\O", "opinion_tags": "I\\O use\\O it\\O mostly\\O for\\O content\\O creation\\O (\\O Audio\\O ,\\O video\\O ,\\O photo\\O editing\\O )\\O and\\O its\\O reliable\\B .\\O", "sentiment": "positive"}]}, {"id": "462:8", "sentence": "Screen is bright and gorgeous .", "postag": ["NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "462:8-0", "target_tags": "Screen\\B is\\O bright\\O and\\O gorgeous\\O .\\O", "opinion_tags": "Screen\\O is\\O bright\\B and\\O gorgeous\\B .\\O", "sentiment": "positive"}]}, {"id": "670:1", "sentence": "It is robust , with a friendly use as all Apple products .", "postag": ["PRP", "VBZ", "JJ", ",", "IN", "DT", "JJ", "NN", "IN", "DT", "NNP", "NNS", "."], "head": [3, 3, 0, 3, 8, 8, 8, 3, 12, 12, 12, 8, 3], "deprel": ["nsubj", "cop", "root", "punct", "case", "det", "amod", "obl", "case", "det", "compound", "nmod", "punct"], "triples": [{"uid": "670:1-0", "target_tags": "It\\O is\\O robust\\O ,\\O with\\O a\\O friendly\\O use\\B as\\O all\\O Apple\\O products\\O .\\O", "opinion_tags": "It\\O is\\O robust\\O ,\\O with\\O a\\O friendly\\B use\\O as\\O all\\O Apple\\O products\\O .\\O", "sentiment": "positive"}]}, {"id": "880:1", "sentence": "It is fast and easy to use .", "postag": ["PRP", "VBZ", "JJ", "CC", "JJ", "TO", "VB", "."], "head": [3, 3, 0, 5, 3, 7, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "mark", "csubj", "punct"], "triples": [{"uid": "880:1-0", "target_tags": "It\\O is\\O fast\\O and\\O easy\\O to\\O use\\B .\\O", "opinion_tags": "It\\O is\\O fast\\O and\\O easy\\B to\\O use\\O .\\O", "sentiment": "positive"}]}, {"id": "745:1", "sentence": "And the fact that it comes with an i5 processor definitely speeds things up", "postag": ["CC", "DT", "NN", "IN", "PRP", "VBZ", "IN", "DT", "NNP", "NN", "RB", "VBZ", "NNS", "RP"], "head": [12, 3, 12, 6, 6, 3, 10, 10, 10, 6, 12, 0, 12, 12], "deprel": ["cc", "det", "nsubj", "mark", "nsubj", "acl", "case", "det", "compound", "obl", "advmod", "root", "obj", "compound:prt"], "triples": [{"uid": "745:1-0", "target_tags": "And\\O the\\O fact\\O that\\O it\\O comes\\O with\\O an\\O i5\\B processor\\I definitely\\O speeds\\O things\\O up\\O", "opinion_tags": "And\\O the\\O fact\\O that\\O it\\O comes\\O with\\O an\\O i5\\O processor\\O definitely\\O speeds\\B things\\I up\\I", "sentiment": "positive"}]}, {"id": "889:1", "sentence": "I have been PC for years but this computer is intuitive and its built in features are a great help", "postag": ["PRP", "VBP", "VBN", "NNP", "IN", "NNS", "CC", "DT", "NN", "VBZ", "JJ", "CC", "PRP$", "VBN", "IN", "NNS", "VBP", "DT", "JJ", "NN"], "head": [4, 4, 4, 0, 6, 4, 11, 9, 11, 11, 4, 20, 16, 20, 16, 14, 20, 20, 20, 4], "deprel": ["nsubj", "aux", "cop", "root", "case", "obl", "cc", "det", "nsubj", "cop", "conj", "cc", "nmod:poss", "nsubj", "case", "obl", "cop", "det", "amod", "conj"], "triples": [{"uid": "889:1-0", "target_tags": "I\\O have\\O been\\O PC\\O for\\O years\\O but\\O this\\O computer\\O is\\O intuitive\\O and\\O its\\O built\\B in\\I features\\I are\\O a\\O great\\O help\\O", "opinion_tags": "I\\O have\\O been\\O PC\\O for\\O years\\O but\\O this\\O computer\\O is\\O intuitive\\O and\\O its\\O built\\O in\\O features\\O are\\O a\\O great\\B help\\I", "sentiment": "positive"}]}, {"id": "1094:1", "sentence": "Nice screen , keyboard works great !", "postag": ["JJ", "NN", ",", "NN", "VBZ", "JJ", "."], "head": [2, 0, 2, 5, 2, 5, 2], "deprel": ["amod", "root", "punct", "nsubj", "parataxis", "xcomp", "punct"], "triples": [{"uid": "1094:1-0", "target_tags": "Nice\\O screen\\B ,\\O keyboard\\O works\\O great\\O !\\O", "opinion_tags": "Nice\\B screen\\O ,\\O keyboard\\O works\\O great\\O !\\O", "sentiment": "positive"}, {"uid": "1094:1-1", "target_tags": "Nice\\O screen\\O ,\\O keyboard\\B works\\O great\\O !\\O", "opinion_tags": "Nice\\O screen\\O ,\\O keyboard\\O works\\O great\\B !\\O", "sentiment": "positive"}]}, {"id": "575:1", "sentence": "I was amazed at how fast the delivery was .", "postag": ["PRP", "VBD", "JJ", "IN", "WRB", "JJ", "DT", "NN", "VBD", "."], "head": [3, 3, 0, 6, 6, 3, 8, 6, 6, 3], "deprel": ["nsubj", "cop", "root", "mark", "mark", "advcl", "det", "nsubj", "cop", "punct"], "triples": [{"uid": "575:1-0", "target_tags": "I\\O was\\O amazed\\O at\\O how\\O fast\\O the\\O delivery\\B was\\O .\\O", "opinion_tags": "I\\O was\\O amazed\\B at\\O how\\O fast\\B the\\O delivery\\O was\\O .\\O", "sentiment": "positive"}]}, {"id": "972:1", "sentence": "The memory was gone and it was not able to be used .", "postag": ["DT", "NN", "VBD", "VBN", "CC", "PRP", "VBD", "RB", "JJ", "TO", "VB", "VBN", "."], "head": [2, 4, 4, 0, 9, 9, 9, 9, 4, 12, 12, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "nsubj", "cop", "advmod", "conj", "mark", "aux:pass", "xcomp", "punct"], "triples": [{"uid": "972:1-0", "target_tags": "The\\O memory\\B was\\O gone\\O and\\O it\\O was\\O not\\O able\\O to\\O be\\O used\\O .\\O", "opinion_tags": "The\\O memory\\O was\\O gone\\B and\\O it\\O was\\O not\\B able\\I to\\I be\\I used\\I .\\O", "sentiment": "negative"}]}, {"id": "796:1", "sentence": "It works great and I am so happy I bought it .", "postag": ["PRP", "VBZ", "JJ", "CC", "PRP", "VBP", "RB", "JJ", "PRP", "VBD", "PRP", "."], "head": [2, 0, 2, 8, 8, 8, 8, 2, 10, 8, 10, 2], "deprel": ["nsubj", "root", "xcomp", "cc", "nsubj", "cop", "advmod", "conj", "nsubj", "ccomp", "obj", "punct"], "triples": [{"uid": "796:1-0", "target_tags": "It\\O works\\B great\\O and\\O I\\O am\\O so\\O happy\\O I\\O bought\\O it\\O .\\O", "opinion_tags": "It\\O works\\O great\\B and\\O I\\O am\\O so\\O happy\\O I\\O bought\\O it\\O .\\O", "sentiment": "positive"}]}, {"id": "1138:1", "sentence": "I like the design and ease of use with the keyboard , plenty of ports .", "postag": ["PRP", "VBP", "DT", "NN", "CC", "NN", "IN", "NN", "IN", "DT", "NN", ",", "NN", "IN", "NNS", "."], "head": [2, 0, 4, 2, 6, 4, 8, 4, 11, 11, 8, 13, 11, 15, 13, 2], "deprel": ["nsubj", "root", "det", "obj", "cc", "conj", "case", "nmod", "case", "det", "nmod", "punct", "appos", "case", "nmod", "punct"], "triples": [{"uid": "1138:1-0", "target_tags": "I\\O like\\O the\\O design\\B and\\O ease\\O of\\O use\\O with\\O the\\O keyboard\\O ,\\O plenty\\O of\\O ports\\O .\\O", "opinion_tags": "I\\O like\\B the\\O design\\O and\\O ease\\O of\\O use\\O with\\O the\\O keyboard\\O ,\\O plenty\\O of\\O ports\\O .\\O", "sentiment": "positive"}, {"uid": "1138:1-1", "target_tags": "I\\O like\\O the\\O design\\O and\\O ease\\O of\\O use\\O with\\O the\\O keyboard\\B ,\\O plenty\\O of\\O ports\\O .\\O", "opinion_tags": "I\\O like\\B the\\O design\\O and\\O ease\\B of\\I use\\I with\\O the\\O keyboard\\O ,\\O plenty\\O of\\O ports\\O .\\O", "sentiment": "positive"}, {"uid": "1138:1-2", "target_tags": "I\\O like\\O the\\O design\\O and\\O ease\\O of\\O use\\O with\\O the\\O keyboard\\O ,\\O plenty\\O of\\O ports\\B .\\O", "opinion_tags": "I\\O like\\O the\\O design\\O and\\O ease\\O of\\O use\\O with\\O the\\O keyboard\\O ,\\O plenty\\B of\\O ports\\O .\\O", "sentiment": "positive"}]}, {"id": "322:1", "sentence": "it definitely beats my old mac and the service was great .", "postag": ["PRP", "RB", "VBZ", "PRP$", "JJ", "NN", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [3, 3, 0, 6, 6, 3, 11, 9, 11, 11, 3, 3], "deprel": ["nsubj", "advmod", "root", "nmod:poss", "amod", "obj", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "322:1-0", "target_tags": "it\\O definitely\\O beats\\O my\\O old\\O mac\\O and\\O the\\O service\\B was\\O great\\O .\\O", "opinion_tags": "it\\O definitely\\O beats\\O my\\O old\\O mac\\O and\\O the\\O service\\O was\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "16:2", "sentence": "Web browsing is very quick with Safari browser .", "postag": ["NN", "NN", "VBZ", "RB", "JJ", "IN", "NNP", "NN", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 5], "deprel": ["compound", "nsubj", "cop", "advmod", "root", "case", "compound", "obl", "punct"], "triples": [{"uid": "16:2-0", "target_tags": "Web\\B browsing\\I is\\O very\\O quick\\O with\\O Safari\\O browser\\O .\\O", "opinion_tags": "Web\\O browsing\\O is\\O very\\O quick\\B with\\O Safari\\O browser\\O .\\O", "sentiment": "positive"}, {"uid": "16:2-1", "target_tags": "Web\\O browsing\\O is\\O very\\O quick\\O with\\O Safari\\B browser\\I .\\O", "opinion_tags": "Web\\O browsing\\O is\\O very\\O quick\\B with\\O Safari\\O browser\\O .\\O", "sentiment": "positive"}]}, {"id": "481:1", "sentence": "I like the lighted screen at night .", "postag": ["PRP", "VBP", "DT", "VBN", "NN", "IN", "NN", "."], "head": [2, 0, 5, 5, 2, 7, 2, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "case", "obl", "punct"], "triples": [{"uid": "481:1-0", "target_tags": "I\\O like\\O the\\O lighted\\B screen\\I at\\O night\\O .\\O", "opinion_tags": "I\\O like\\B the\\O lighted\\O screen\\O at\\O night\\O .\\O", "sentiment": "positive"}]}, {"id": "948:1", "sentence": "It is really easy to use and it is quick to start up .", "postag": ["PRP", "VBZ", "RB", "JJ", "TO", "VB", "CC", "PRP", "VBZ", "JJ", "TO", "VB", "RP", "."], "head": [4, 4, 4, 0, 6, 4, 10, 10, 10, 4, 12, 10, 12, 4], "deprel": ["expl", "cop", "advmod", "root", "mark", "csubj", "cc", "expl", "cop", "conj", "mark", "csubj", "compound:prt", "punct"], "triples": [{"uid": "948:1-0", "target_tags": "It\\O is\\O really\\O easy\\O to\\O use\\B and\\O it\\O is\\O quick\\O to\\O start\\O up\\O .\\O", "opinion_tags": "It\\O is\\O really\\O easy\\B to\\O use\\O and\\O it\\O is\\O quick\\O to\\O start\\O up\\O .\\O", "sentiment": "positive"}, {"uid": "948:1-1", "target_tags": "It\\O is\\O really\\O easy\\O to\\O use\\O and\\O it\\O is\\O quick\\O to\\O start\\B up\\I .\\O", "opinion_tags": "It\\O is\\O really\\O easy\\O to\\O use\\O and\\O it\\O is\\O quick\\B to\\O start\\O up\\O .\\O", "sentiment": "positive"}]}, {"id": "307:1", "sentence": "I 've lived with the crashes and slow operation and restarts .", "postag": ["PRP", "VBP", "VBN", "IN", "DT", "NNS", "CC", "JJ", "NN", "CC", "NNS", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 6, 11, 6, 3], "deprel": ["nsubj", "aux", "root", "case", "det", "obl", "cc", "amod", "conj", "cc", "conj", "punct"], "triples": [{"uid": "307:1-0", "target_tags": "I\\O 've\\O lived\\O with\\O the\\O crashes\\O and\\O slow\\O operation\\B and\\O restarts\\O .\\O", "opinion_tags": "I\\O 've\\O lived\\O with\\O the\\O crashes\\O and\\O slow\\B operation\\O and\\O restarts\\O .\\O", "sentiment": "negative"}]}, {"id": "29:319", "sentence": "USB3 Peripherals are noticably less expensive than the ThunderBolt ones .", "postag": ["NNP", "NNS", "VBP", "RB", "RBR", "JJ", "IN", "DT", "NNP", "NNS", "."], "head": [2, 6, 6, 5, 6, 0, 10, 10, 10, 6, 6], "deprel": ["compound", "nsubj", "cop", "advmod", "advmod", "root", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "29:319-0", "target_tags": "USB3\\B Peripherals\\I are\\O noticably\\O less\\O expensive\\O than\\O the\\O ThunderBolt\\O ones\\O .\\O", "opinion_tags": "USB3\\O Peripherals\\O are\\O noticably\\O less\\B expensive\\I than\\O the\\O ThunderBolt\\O ones\\O .\\O", "sentiment": "positive"}]}, {"id": "894:1", "sentence": "It 's fast , light , and is perfect for media editing , which is mostly why I bought it in the first place .", "postag": ["PRP", "VBZ", "JJ", ",", "JJ", ",", "CC", "VBZ", "JJ", "IN", "NN", "NN", ",", "WDT", "VBZ", "RB", "WRB", "PRP", "VBD", "PRP", "IN", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 5, 3, 9, 9, 9, 3, 12, 12, 9, 12, 17, 17, 17, 12, 19, 17, 19, 24, 24, 24, 19, 3], "deprel": ["nsubj", "cop", "root", "punct", "conj", "punct", "cc", "cop", "conj", "case", "compound", "obl", "punct", "nsubj", "cop", "advmod", "acl:relcl", "nsubj", "acl:relcl", "obj", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "894:1-0", "target_tags": "It\\O 's\\O fast\\O ,\\O light\\O ,\\O and\\O is\\O perfect\\O for\\O media\\B editing\\I ,\\O which\\O is\\O mostly\\O why\\O I\\O bought\\O it\\O in\\O the\\O first\\O place\\O .\\O", "opinion_tags": "It\\O 's\\O fast\\O ,\\O light\\O ,\\O and\\O is\\O perfect\\B for\\O media\\O editing\\O ,\\O which\\O is\\O mostly\\O why\\O I\\O bought\\O it\\O in\\O the\\O first\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "391:1", "sentence": "The battery lasts as advertised ( give or take 15-20 minutes ) , and the entire user experience is very elegant .", "postag": ["DT", "NN", "VBZ", "IN", "VBN", "-LRB-", "VB", "CC", "VB", "CD", "NNS", "-RRB-", ",", "CC", "DT", "JJ", "NN", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 3, 0, 5, 3, 7, 3, 9, 7, 11, 7, 7, 21, 21, 18, 18, 18, 21, 21, 21, 3, 3], "deprel": ["det", "nsubj", "root", "mark", "advcl", "punct", "parataxis", "cc", "conj", "nummod", "obj", "punct", "punct", "cc", "det", "amod", "compound", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "391:1-0", "target_tags": "The\\O battery\\O lasts\\O as\\O advertised\\O (\\O give\\O or\\O take\\O 15-20\\O minutes\\O )\\O ,\\O and\\O the\\O entire\\O user\\B experience\\I is\\O very\\O elegant\\O .\\O", "opinion_tags": "The\\O battery\\O lasts\\O as\\O advertised\\O (\\O give\\O or\\O take\\O 15-20\\O minutes\\O )\\O ,\\O and\\O the\\O entire\\O user\\O experience\\O is\\O very\\O elegant\\B .\\O", "sentiment": "positive"}]}, {"id": "949:1", "sentence": "Thanks for the fast shipment and great price .", "postag": ["NN", "IN", "DT", "JJ", "NN", "CC", "JJ", "NN", "."], "head": [0, 5, 5, 5, 1, 8, 8, 5, 1], "deprel": ["root", "case", "det", "amod", "nmod", "cc", "amod", "conj", "punct"], "triples": [{"uid": "949:1-0", "target_tags": "Thanks\\O for\\O the\\O fast\\O shipment\\B and\\O great\\O price\\O .\\O", "opinion_tags": "Thanks\\O for\\O the\\O fast\\B shipment\\O and\\O great\\O price\\O .\\O", "sentiment": "positive"}, {"uid": "949:1-1", "target_tags": "Thanks\\O for\\O the\\O fast\\O shipment\\O and\\O great\\O price\\B .\\O", "opinion_tags": "Thanks\\O for\\O the\\O fast\\O shipment\\O and\\O great\\B price\\O .\\O", "sentiment": "positive"}]}, {"id": "824:1", "sentence": "! Excellent performance , usability , presentation and time response .", "postag": [".", "JJ", "NN", ",", "NN", ",", "NN", "CC", "NN", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 3, 10, 10, 3, 3], "deprel": ["punct", "amod", "root", "punct", "conj", "punct", "conj", "cc", "compound", "conj", "punct"], "triples": [{"uid": "824:1-0", "target_tags": "!\\O Excellent\\O performance\\B ,\\O usability\\O ,\\O presentation\\O and\\O time\\O response\\O .\\O", "opinion_tags": "!\\O Excellent\\B performance\\O ,\\O usability\\O ,\\O presentation\\O and\\O time\\O response\\O .\\O", "sentiment": "positive"}, {"uid": "824:1-1", "target_tags": "!\\O Excellent\\O performance\\O ,\\O usability\\B ,\\O presentation\\O and\\O time\\O response\\O .\\O", "opinion_tags": "!\\O Excellent\\B performance\\O ,\\O usability\\O ,\\O presentation\\O and\\O time\\O response\\O .\\O", "sentiment": "positive"}, {"uid": "824:1-2", "target_tags": "!\\O Excellent\\O performance\\O ,\\O usability\\O ,\\O presentation\\B and\\O time\\O response\\O .\\O", "opinion_tags": "!\\O Excellent\\B performance\\O ,\\O usability\\O ,\\O presentation\\O and\\O time\\O response\\O .\\O", "sentiment": "positive"}, {"uid": "824:1-3", "target_tags": "!\\O Excellent\\O performance\\O ,\\O usability\\O ,\\O presentation\\O and\\O time\\B response\\I .\\O", "opinion_tags": "!\\O Excellent\\B performance\\O ,\\O usability\\O ,\\O presentation\\O and\\O time\\O response\\O .\\O", "sentiment": "positive"}]}, {"id": "999:1", "sentence": "The smaller size was a bonus because of space restrictions .", "postag": ["DT", "JJR", "NN", "VBD", "DT", "NN", "IN", "IN", "NN", "NNS", "."], "head": [3, 3, 6, 6, 6, 0, 10, 7, 10, 6, 6], "deprel": ["det", "amod", "nsubj", "cop", "det", "root", "case", "fixed", "compound", "obl", "punct"], "triples": [{"uid": "999:1-0", "target_tags": "The\\O smaller\\O size\\B was\\O a\\O bonus\\O because\\O of\\O space\\O restrictions\\O .\\O", "opinion_tags": "The\\O smaller\\B size\\O was\\O a\\O bonus\\B because\\O of\\O space\\O restrictions\\O .\\O", "sentiment": "positive"}]}, {"id": "29:310", "sentence": "I blame the Mac OS .", "postag": ["PRP", "VBP", "DT", "NNP", "NNP", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "punct"], "triples": [{"uid": "29:310-0", "target_tags": "I\\O blame\\O the\\O Mac\\B OS\\I .\\O", "opinion_tags": "I\\O blame\\B the\\O Mac\\O OS\\O .\\O", "sentiment": "negative"}]}, {"id": "708:1", "sentence": "I like the operating system .", "postag": ["PRP", "VBP", "DT", "NN", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "punct"], "triples": [{"uid": "708:1-0", "target_tags": "I\\O like\\O the\\O operating\\B system\\I .\\O", "opinion_tags": "I\\O like\\B the\\O operating\\O system\\O .\\O", "sentiment": "positive"}]}, {"id": "376:1", "sentence": "I love the form factor .", "postag": ["PRP", "VBP", "DT", "NN", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "punct"], "triples": [{"uid": "376:1-0", "target_tags": "I\\O love\\O the\\O form\\B factor\\I .\\O", "opinion_tags": "I\\O love\\B the\\O form\\O factor\\O .\\O", "sentiment": "positive"}]}, {"id": "201:1", "sentence": "It 's fast at loading the internet .", "postag": ["PRP", "VBZ", "JJ", "IN", "VBG", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 5, 3], "deprel": ["nsubj", "cop", "root", "mark", "advcl", "det", "obj", "punct"], "triples": [{"uid": "201:1-0", "target_tags": "It\\O 's\\O fast\\O at\\O loading\\B the\\I internet\\I .\\O", "opinion_tags": "It\\O 's\\O fast\\B at\\O loading\\O the\\O internet\\O .\\O", "sentiment": "positive"}]}, {"id": "763:1", "sentence": "So much faster and sleeker looking .", "postag": ["RB", "RB", "JJR", "CC", "JJR", "VBG", "."], "head": [2, 3, 6, 5, 3, 0, 6], "deprel": ["advmod", "advmod", "amod", "cc", "conj", "root", "punct"], "triples": [{"uid": "763:1-0", "target_tags": "So\\O much\\O faster\\O and\\O sleeker\\O looking\\B .\\O", "opinion_tags": "So\\O much\\O faster\\B and\\O sleeker\\B looking\\O .\\O", "sentiment": "positive"}]}, {"id": "962:1", "sentence": "Unfortunately , it runs XP and Microsoft is dropping support next April .", "postag": ["RB", ",", "PRP", "VBZ", "NNP", "CC", "NNP", "VBZ", "VBG", "NN", "JJ", "NNP", "."], "head": [4, 4, 4, 0, 4, 9, 9, 9, 4, 9, 12, 9, 4], "deprel": ["advmod", "punct", "nsubj", "root", "obj", "cc", "nsubj", "aux", "conj", "obj", "amod", "obl:tmod", "punct"], "triples": [{"uid": "962:1-0", "target_tags": "Unfortunately\\O ,\\O it\\O runs\\O XP\\B and\\O Microsoft\\O is\\O dropping\\O support\\O next\\O April\\O .\\O", "opinion_tags": "Unfortunately\\B ,\\O it\\O runs\\O XP\\O and\\O Microsoft\\O is\\O dropping\\O support\\O next\\O April\\O .\\O", "sentiment": "neutral"}, {"uid": "962:1-1", "target_tags": "Unfortunately\\O ,\\O it\\O runs\\O XP\\O and\\O Microsoft\\O is\\O dropping\\O support\\B next\\O April\\O .\\O", "opinion_tags": "Unfortunately\\B ,\\O it\\O runs\\O XP\\O and\\O Microsoft\\O is\\O dropping\\B support\\O next\\O April\\O .\\O", "sentiment": "negative"}]}, {"id": "281:1", "sentence": "First off , I really do like my MBP ... once used to the OS it is pretty easy to get around , and the overall build is great ... eg the keyboard is one of the best to type on .", "postag": ["RB", "RB", ",", "PRP", "RB", "VBP", "VB", "PRP$", "NN", ",", "RB", "VBN", "IN", "DT", "NNP", "PRP", "VBZ", "RB", "JJ", "TO", "VB", "RB", ",", "CC", "DT", "JJ", "NN", "VBZ", "JJ", ",", "CC", "DT", "NN", "VBZ", "CD", "IN", "DT", "JJS", "TO", "VB", "RP", "."], "head": [2, 7, 7, 7, 7, 7, 0, 9, 7, 7, 12, 19, 15, 15, 12, 19, 19, 19, 7, 21, 19, 21, 29, 29, 27, 27, 29, 29, 7, 7, 35, 33, 35, 35, 7, 38, 38, 35, 40, 38, 40, 7], "deprel": ["advmod", "advmod", "punct", "nsubj", "advmod", "aux", "root", "nmod:poss", "obj", "punct", "advmod", "advcl", "case", "det", "obl", "nsubj", "cop", "advmod", "parataxis", "mark", "advcl", "advmod", "punct", "cc", "det", "amod", "nsubj", "cop", "conj", "punct", "cc", "det", "nsubj", "cop", "conj", "case", "det", "nmod", "mark", "acl", "compound:prt", "punct"], "triples": [{"uid": "281:1-0", "target_tags": "First\\O off\\O ,\\O I\\O really\\O do\\O like\\O my\\O MBP\\O ...\\O once\\O used\\O to\\O the\\O OS\\B it\\O is\\O pretty\\O easy\\O to\\O get\\O around\\O ,\\O and\\O the\\O overall\\O build\\O is\\O great\\O ...\\O eg\\O the\\O keyboard\\O is\\O one\\O of\\O the\\O best\\O to\\O type\\O on\\O .\\O", "opinion_tags": "First\\O off\\O ,\\O I\\O really\\O do\\O like\\B my\\O MBP\\O ...\\O once\\O used\\O to\\O the\\O OS\\O it\\O is\\O pretty\\O easy\\B to\\O get\\O around\\O ,\\O and\\O the\\O overall\\O build\\O is\\O great\\O ...\\O eg\\O the\\O keyboard\\O is\\O one\\O of\\O the\\O best\\O to\\O type\\O on\\O .\\O", "sentiment": "positive"}, {"uid": "281:1-1", "target_tags": "First\\O off\\O ,\\O I\\O really\\O do\\O like\\O my\\O MBP\\O ...\\O once\\O used\\O to\\O the\\O OS\\O it\\O is\\O pretty\\O easy\\O to\\O get\\O around\\O ,\\O and\\O the\\O overall\\B build\\I is\\O great\\O ...\\O eg\\O the\\O keyboard\\O is\\O one\\O of\\O the\\O best\\O to\\O type\\O on\\O .\\O", "opinion_tags": "First\\O off\\O ,\\O I\\O really\\O do\\O like\\O my\\O MBP\\O ...\\O once\\O used\\O to\\O the\\O OS\\O it\\O is\\O pretty\\O easy\\O to\\O get\\O around\\O ,\\O and\\O the\\O overall\\O build\\O is\\O great\\B ...\\O eg\\O the\\O keyboard\\O is\\O one\\O of\\O the\\O best\\O to\\O type\\O on\\O .\\O", "sentiment": "positive"}, {"uid": "281:1-2", "target_tags": "First\\O off\\O ,\\O I\\O really\\O do\\O like\\O my\\O MBP\\O ...\\O once\\O used\\O to\\O the\\O OS\\O it\\O is\\O pretty\\O easy\\O to\\O get\\O around\\O ,\\O and\\O the\\O overall\\O build\\O is\\O great\\O ...\\O eg\\O the\\O keyboard\\B is\\O one\\O of\\O the\\O best\\O to\\O type\\O on\\O .\\O", "opinion_tags": "First\\O off\\O ,\\O I\\O really\\O do\\O like\\O my\\O MBP\\O ...\\O once\\O used\\O to\\O the\\O OS\\O it\\O is\\O pretty\\O easy\\O to\\O get\\O around\\O ,\\O and\\O the\\O overall\\O build\\O is\\O great\\O ...\\O eg\\O the\\O keyboard\\O is\\O one\\O of\\O the\\O best\\B to\\O type\\O on\\O .\\O", "sentiment": "positive"}]}, {"id": "926:1", "sentence": "It is made of such solid construction and since I have never had a Mac using my iPhone helped me get used to the system a bit .", "postag": ["PRP", "VBZ", "VBN", "IN", "JJ", "JJ", "NN", "CC", "IN", "PRP", "VBP", "RB", "VBN", "DT", "NNP", "VBG", "PRP$", "NNP", "VBD", "PRP", "VB", "VBN", "IN", "DT", "NN", "DT", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 19, 13, 13, 13, 13, 19, 15, 13, 15, 18, 16, 3, 19, 19, 21, 25, 25, 22, 27, 22, 3], "deprel": ["nsubj:pass", "aux:pass", "root", "case", "amod", "amod", "obl", "cc", "mark", "nsubj", "aux", "advmod", "advcl", "det", "obj", "acl", "nmod:poss", "obj", "conj", "obj", "xcomp", "xcomp", "case", "det", "obl", "det", "obl:npmod", "punct"], "triples": [{"uid": "926:1-0", "target_tags": "It\\O is\\O made\\O of\\O such\\O solid\\O construction\\B and\\O since\\O I\\O have\\O never\\O had\\O a\\O Mac\\O using\\O my\\O iPhone\\O helped\\O me\\O get\\O used\\O to\\O the\\O system\\O a\\O bit\\O .\\O", "opinion_tags": "It\\O is\\O made\\O of\\O such\\O solid\\B construction\\O and\\O since\\O I\\O have\\O never\\O had\\O a\\O Mac\\O using\\O my\\O iPhone\\O helped\\O me\\O get\\O used\\O to\\O the\\O system\\O a\\O bit\\O .\\O", "sentiment": "positive"}]}, {"id": "829:1", "sentence": "Very nice unibody construction .", "postag": ["RB", "JJ", "NN", "NN", "."], "head": [2, 4, 4, 0, 4], "deprel": ["advmod", "amod", "compound", "root", "punct"], "triples": [{"uid": "829:1-0", "target_tags": "Very\\O nice\\O unibody\\B construction\\I .\\O", "opinion_tags": "Very\\O nice\\B unibody\\O construction\\O .\\O", "sentiment": "positive"}]}, {"id": "318:1", "sentence": "This Macbook Pro is fast , powerful , and runs super quiet and cool .", "postag": ["DT", "NNP", "NNP", "VBZ", "JJ", ",", "JJ", ",", "CC", "VBZ", "RB", "JJ", "CC", "JJ", "."], "head": [3, 3, 5, 5, 0, 7, 5, 10, 10, 5, 12, 10, 14, 12, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "conj", "punct", "cc", "conj", "advmod", "xcomp", "cc", "conj", "punct"], "triples": [{"uid": "318:1-0", "target_tags": "This\\O Macbook\\O Pro\\O is\\O fast\\O ,\\O powerful\\O ,\\O and\\O runs\\B super\\O quiet\\O and\\O cool\\O .\\O", "opinion_tags": "This\\O Macbook\\O Pro\\O is\\O fast\\O ,\\O powerful\\O ,\\O and\\O runs\\O super\\O quiet\\B and\\O cool\\B .\\O", "sentiment": "positive"}]}, {"id": "786:1477", "sentence": "It 's ok but does n't have a disk drive which I did n't know until after I bought it .", "postag": ["PRP", "VBZ", "JJ", "CC", "VBZ", "RB", "VB", "DT", "NN", "NN", "WDT", "PRP", "VBD", "RB", "VB", "IN", "IN", "PRP", "VBD", "PRP", "."], "head": [3, 3, 0, 7, 7, 7, 3, 10, 10, 7, 15, 15, 15, 15, 10, 19, 19, 19, 15, 19, 3], "deprel": ["nsubj", "cop", "root", "cc", "aux", "advmod", "conj", "det", "compound", "obj", "obj", "nsubj", "aux", "advmod", "acl:relcl", "mark", "mark", "nsubj", "advcl", "obj", "punct"], "triples": [{"uid": "786:1477-0", "target_tags": "It\\O 's\\O ok\\O but\\O does\\O n't\\O have\\O a\\O disk\\B drive\\I which\\O I\\O did\\O n't\\O know\\O until\\O after\\O I\\O bought\\O it\\O .\\O", "opinion_tags": "It\\O 's\\O ok\\O but\\O does\\B n't\\I have\\I a\\O disk\\O drive\\O which\\O I\\O did\\O n't\\O know\\O until\\O after\\O I\\O bought\\O it\\O .\\O", "sentiment": "neutral"}]}, {"id": "29:783", "sentence": "There is no HDMI receptacle , nor is there an SD card slot located anywhere on the device .", "postag": ["EX", "VBZ", "DT", "NN", "NN", ",", "CC", "VBZ", "EX", "DT", "NN", "NN", "NN", "VBN", "RB", "IN", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 8, 13, 12, 13, 8, 13, 14, 18, 18, 14, 2], "deprel": ["expl", "root", "det", "compound", "nsubj", "punct", "cc", "conj", "expl", "det", "compound", "compound", "nsubj", "acl", "advmod", "case", "det", "obl", "punct"], "triples": [{"uid": "29:783-0", "target_tags": "There\\O is\\O no\\O HDMI\\B receptacle\\I ,\\O nor\\O is\\O there\\O an\\O SD\\O card\\O slot\\O located\\O anywhere\\O on\\O the\\O device\\O .\\O", "opinion_tags": "There\\O is\\O no\\B HDMI\\O receptacle\\O ,\\O nor\\O is\\O there\\O an\\O SD\\O card\\O slot\\O located\\O anywhere\\O on\\O the\\O device\\O .\\O", "sentiment": "neutral"}, {"uid": "29:783-1", "target_tags": "There\\O is\\O no\\O HDMI\\O receptacle\\O ,\\O nor\\O is\\O there\\O an\\O SD\\B card\\I slot\\I located\\O anywhere\\O on\\O the\\O device\\O .\\O", "opinion_tags": "There\\O is\\O no\\O HDMI\\O receptacle\\O ,\\O nor\\B is\\O there\\O an\\O SD\\O card\\O slot\\O located\\O anywhere\\O on\\O the\\O device\\O .\\O", "sentiment": "neutral"}]}, {"id": "758:1", "sentence": "It came in brand new and works perfectly .", "postag": ["PRP", "VBD", "IN", "NN", "JJ", "CC", "VBZ", "RB", "."], "head": [2, 0, 4, 2, 2, 7, 2, 7, 2], "deprel": ["nsubj", "root", "case", "obl", "advmod", "cc", "conj", "advmod", "punct"], "triples": [{"uid": "758:1-0", "target_tags": "It\\O came\\O in\\O brand\\O new\\O and\\O works\\B perfectly\\O .\\O", "opinion_tags": "It\\O came\\O in\\O brand\\O new\\O and\\O works\\O perfectly\\B .\\O", "sentiment": "positive"}]}, {"id": "355:1", "sentence": "It should n't happen like that , I do n't have any design app open or anything .", "postag": ["PRP", "MD", "RB", "VB", "IN", "DT", ",", "PRP", "VBP", "RB", "VB", "DT", "NN", "NN", "JJ", "CC", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 4, 11, 11, 11, 4, 14, 14, 11, 14, 17, 15, 4], "deprel": ["nsubj", "aux", "advmod", "root", "case", "obl", "punct", "nsubj", "aux", "advmod", "parataxis", "det", "compound", "obj", "amod", "cc", "conj", "punct"], "triples": [{"uid": "355:1-0", "target_tags": "It\\O should\\O n't\\O happen\\O like\\O that\\O ,\\O I\\O do\\O n't\\O have\\O any\\O design\\B app\\I open\\O or\\O anything\\O .\\O", "opinion_tags": "It\\O should\\O n't\\O happen\\O like\\O that\\O ,\\O I\\O do\\B n't\\I have\\I any\\O design\\O app\\O open\\O or\\O anything\\O .\\O", "sentiment": "neutral"}]}, {"id": "335:0", "sentence": "MY TRACKPAD IS NOT WORKING .", "postag": ["PRP$", "NN", "VBZ", "RB", "VBG", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["nmod:poss", "nsubj", "aux", "advmod", "root", "punct"], "triples": [{"uid": "335:0-0", "target_tags": "MY\\O TRACKPAD\\B IS\\O NOT\\O WORKING\\O .\\O", "opinion_tags": "MY\\O TRACKPAD\\O IS\\O NOT\\B WORKING\\I .\\O", "sentiment": "negative"}]}, {"id": "706:1", "sentence": "It looks and feels solid , with a flawless finish .", "postag": ["PRP", "VBZ", "CC", "VBZ", "JJ", ",", "IN", "DT", "JJ", "NN", "."], "head": [2, 0, 4, 2, 4, 10, 10, 10, 10, 4, 2], "deprel": ["nsubj", "root", "cc", "conj", "xcomp", "punct", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "706:1-0", "target_tags": "It\\O looks\\O and\\O feels\\O solid\\O ,\\O with\\O a\\O flawless\\O finish\\B .\\O", "opinion_tags": "It\\O looks\\O and\\O feels\\O solid\\O ,\\O with\\O a\\O flawless\\B finish\\O .\\O", "sentiment": "positive"}, {"uid": "706:1-1", "target_tags": "It\\O looks\\B and\\O feels\\O solid\\O ,\\O with\\O a\\O flawless\\O finish\\O .\\O", "opinion_tags": "It\\O looks\\O and\\O feels\\O solid\\B ,\\O with\\O a\\O flawless\\O finish\\O .\\O", "sentiment": "positive"}, {"uid": "706:1-2", "target_tags": "It\\O looks\\O and\\O feels\\B solid\\O ,\\O with\\O a\\O flawless\\O finish\\O .\\O", "opinion_tags": "It\\O looks\\O and\\O feels\\O solid\\B ,\\O with\\O a\\O flawless\\O finish\\O .\\O", "sentiment": "positive"}]}, {"id": "671:1", "sentence": "Price was higher when purchased on MAC when compared to price showing on PC when I bought this product .", "postag": ["NN", "VBD", "JJR", "WRB", "VBN", "IN", "NNP", "WRB", "VBN", "IN", "NN", "VBG", "IN", "NNP", "WRB", "PRP", "VBD", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 5, 9, 5, 11, 9, 9, 14, 12, 17, 17, 12, 19, 17, 3], "deprel": ["nsubj", "cop", "root", "mark", "advcl", "case", "obl", "mark", "advcl", "case", "obl", "advcl", "case", "obl", "mark", "nsubj", "advcl", "det", "obj", "punct"], "triples": [{"uid": "671:1-0", "target_tags": "Price\\B was\\O higher\\O when\\O purchased\\O on\\O MAC\\O when\\O compared\\O to\\O price\\O showing\\O on\\O PC\\O when\\O I\\O bought\\O this\\O product\\O .\\O", "opinion_tags": "Price\\O was\\O higher\\B when\\O purchased\\O on\\O MAC\\O when\\O compared\\O to\\O price\\O showing\\O on\\O PC\\O when\\O I\\O bought\\O this\\O product\\O .\\O", "sentiment": "negative"}]}, {"id": "460:1", "sentence": "Then the system would many times not power down without a forced power-off .", "postag": ["RB", "DT", "NN", "MD", "JJ", "NNS", "RB", "NN", "RP", "IN", "DT", "VBN", "NN", "."], "head": [8, 3, 8, 8, 6, 8, 8, 0, 8, 13, 13, 13, 8, 8], "deprel": ["advmod", "det", "nsubj", "aux", "amod", "obl:tmod", "advmod", "root", "advmod", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "460:1-0", "target_tags": "Then\\O the\\O system\\B would\\O many\\O times\\O not\\O power\\O down\\O without\\O a\\O forced\\O power-off\\O .\\O", "opinion_tags": "Then\\O the\\O system\\O would\\O many\\O times\\O not\\B power\\I down\\I without\\O a\\O forced\\O power-off\\O .\\O", "sentiment": "negative"}, {"uid": "460:1-1", "target_tags": "Then\\O the\\O system\\O would\\O many\\O times\\O not\\O power\\B down\\I without\\O a\\O forced\\O power-off\\O .\\O", "opinion_tags": "Then\\O the\\O system\\O would\\O many\\O times\\O not\\B power\\O down\\O without\\O a\\O forced\\O power-off\\O .\\O", "sentiment": "negative"}]}, {"id": "786:723", "sentence": "The configuration is perfect for my needs .", "postag": ["DT", "NN", "VBZ", "JJ", "IN", "PRP$", "NNS", "."], "head": [2, 4, 4, 0, 7, 7, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "786:723-0", "target_tags": "The\\O configuration\\B is\\O perfect\\O for\\O my\\O needs\\O .\\O", "opinion_tags": "The\\O configuration\\O is\\O perfect\\B for\\O my\\O needs\\O .\\O", "sentiment": "positive"}]}, {"id": "1171:1", "sentence": "and the speakers is the worst ever .", "postag": ["CC", "DT", "NNS", "VBZ", "DT", "JJS", "RB", "."], "head": [6, 3, 6, 6, 6, 0, 6, 6], "deprel": ["cc", "det", "nsubj", "cop", "det", "root", "advmod", "punct"], "triples": [{"uid": "1171:1-0", "target_tags": "and\\O the\\O speakers\\B is\\O the\\O worst\\O ever\\O .\\O", "opinion_tags": "and\\O the\\O speakers\\O is\\O the\\O worst\\B ever\\O .\\O", "sentiment": "negative"}]}, {"id": "515:1", "sentence": "Its the best , its got the looks , super easy to use and love all you can do with the trackpad ! ..", "postag": ["PRP$", "DT", "JJS", ",", "PRP$", "VBN", "DT", "VBZ", ",", "RB", "JJ", "TO", "VB", "CC", "VB", "DT", "PRP", "MD", "VB", "IN", "DT", "NN", ".", "."], "head": [3, 3, 8, 3, 6, 8, 6, 0, 11, 11, 8, 13, 11, 15, 13, 15, 19, 19, 16, 22, 22, 19, 8, 8], "deprel": ["nmod:poss", "det", "nsubj", "punct", "nmod:poss", "nsubj", "obj", "root", "punct", "advmod", "xcomp", "mark", "xcomp", "cc", "conj", "obj", "nsubj", "aux", "acl:relcl", "case", "det", "obl", "punct", "punct"], "triples": [{"uid": "515:1-0", "target_tags": "Its\\O the\\O best\\O ,\\O its\\O got\\O the\\O looks\\O ,\\O super\\O easy\\O to\\O use\\B and\\O love\\O all\\O you\\O can\\O do\\O with\\O the\\O trackpad\\O !\\O ..\\O", "opinion_tags": "Its\\O the\\O best\\O ,\\O its\\O got\\O the\\O looks\\O ,\\O super\\O easy\\B to\\O use\\O and\\O love\\O all\\O you\\O can\\O do\\O with\\O the\\O trackpad\\O !\\O ..\\O", "sentiment": "positive"}, {"uid": "515:1-1", "target_tags": "Its\\O the\\O best\\O ,\\O its\\O got\\O the\\O looks\\O ,\\O super\\O easy\\O to\\O use\\O and\\O love\\O all\\O you\\O can\\O do\\O with\\O the\\O trackpad\\B !\\O ..\\O", "opinion_tags": "Its\\O the\\O best\\O ,\\O its\\O got\\O the\\O looks\\O ,\\O super\\O easy\\O to\\O use\\O and\\O love\\B all\\O you\\O can\\O do\\O with\\O the\\O trackpad\\O !\\O ..\\O", "sentiment": "positive"}]}, {"id": "786:458", "sentence": "Web surfuring is smooth and seamless .", "postag": ["NN", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["compound", "nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "786:458-0", "target_tags": "Web\\B surfuring\\I is\\O smooth\\O and\\O seamless\\O .\\O", "opinion_tags": "Web\\O surfuring\\O is\\O smooth\\B and\\O seamless\\B .\\O", "sentiment": "positive"}]}, {"id": "520:2", "sentence": "I 'm overall pleased with the interface and the portability of this product .", "postag": ["PRP", "VBP", "RB", "JJ", "IN", "DT", "NN", "CC", "DT", "NN", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 10, 10, 7, 13, 13, 10, 4], "deprel": ["nsubj", "cop", "advmod", "root", "case", "det", "obl", "cc", "det", "conj", "case", "det", "nmod", "punct"], "triples": [{"uid": "520:2-0", "target_tags": "I\\O 'm\\O overall\\O pleased\\O with\\O the\\O interface\\B and\\O the\\O portability\\O of\\O this\\O product\\O .\\O", "opinion_tags": "I\\O 'm\\O overall\\O pleased\\B with\\O the\\O interface\\O and\\O the\\O portability\\O of\\O this\\O product\\O .\\O", "sentiment": "positive"}, {"uid": "520:2-1", "target_tags": "I\\O 'm\\O overall\\O pleased\\O with\\O the\\O interface\\O and\\O the\\O portability\\B of\\O this\\O product\\O .\\O", "opinion_tags": "I\\O 'm\\O overall\\O pleased\\B with\\O the\\O interface\\O and\\O the\\O portability\\O of\\O this\\O product\\O .\\O", "sentiment": "positive"}]}, {"id": "952:1", "sentence": "This item is a beautiful piece , it works well , it is easy to carry and handle .", "postag": ["DT", "NN", "VBZ", "DT", "JJ", "NN", ",", "PRP", "VBZ", "RB", ",", "PRP", "VBZ", "JJ", "TO", "VB", "CC", "VB", "."], "head": [2, 6, 6, 6, 6, 0, 6, 9, 6, 9, 6, 14, 14, 6, 16, 14, 18, 16, 6], "deprel": ["det", "nsubj", "cop", "det", "amod", "root", "punct", "nsubj", "parataxis", "advmod", "punct", "expl", "cop", "parataxis", "mark", "csubj", "cc", "conj", "punct"], "triples": [{"uid": "952:1-0", "target_tags": "This\\O item\\O is\\O a\\O beautiful\\O piece\\O ,\\O it\\O works\\B well\\O ,\\O it\\O is\\O easy\\O to\\O carry\\O and\\O handle\\O .\\O", "opinion_tags": "This\\O item\\O is\\O a\\O beautiful\\O piece\\O ,\\O it\\O works\\O well\\B ,\\O it\\O is\\O easy\\O to\\O carry\\O and\\O handle\\O .\\O", "sentiment": "positive"}, {"uid": "952:1-1", "target_tags": "This\\O item\\O is\\O a\\O beautiful\\O piece\\O ,\\O it\\O works\\O well\\O ,\\O it\\O is\\O easy\\O to\\O carry\\B and\\O handle\\O .\\O", "opinion_tags": "This\\O item\\O is\\O a\\O beautiful\\O piece\\O ,\\O it\\O works\\O well\\O ,\\O it\\O is\\O easy\\B to\\O carry\\O and\\O handle\\O .\\O", "sentiment": "positive"}, {"uid": "952:1-2", "target_tags": "This\\O item\\O is\\O a\\O beautiful\\O piece\\O ,\\O it\\O works\\O well\\O ,\\O it\\O is\\O easy\\O to\\O carry\\O and\\O handle\\B .\\O", "opinion_tags": "This\\O item\\O is\\O a\\O beautiful\\O piece\\O ,\\O it\\O works\\O well\\O ,\\O it\\O is\\O easy\\B to\\O carry\\O and\\O handle\\O .\\O", "sentiment": "positive"}]}, {"id": "394:1", "sentence": "It was also suffering from hardware ( keyboard ) issues , relatively slow performance and shortening battery lifetime .", "postag": ["PRP", "VBD", "RB", "VBG", "IN", "NN", "-LRB-", "NN", "-RRB-", "NNS", ",", "RB", "JJ", "NN", "CC", "VBG", "NN", "NN", "."], "head": [4, 4, 4, 0, 10, 10, 8, 10, 8, 4, 14, 13, 14, 10, 18, 18, 18, 10, 4], "deprel": ["nsubj", "aux", "advmod", "root", "case", "compound", "punct", "compound", "punct", "obl", "punct", "advmod", "amod", "conj", "cc", "amod", "compound", "conj", "punct"], "triples": [{"uid": "394:1-0", "target_tags": "It\\O was\\O also\\O suffering\\O from\\O hardware\\O (\\O keyboard\\O )\\O issues\\O ,\\O relatively\\O slow\\O performance\\B and\\O shortening\\O battery\\O lifetime\\O .\\O", "opinion_tags": "It\\O was\\O also\\O suffering\\O from\\O hardware\\O (\\O keyboard\\O )\\O issues\\O ,\\O relatively\\O slow\\B performance\\O and\\O shortening\\O battery\\O lifetime\\O .\\O", "sentiment": "negative"}, {"uid": "394:1-1", "target_tags": "It\\O was\\O also\\O suffering\\O from\\O hardware\\O (\\O keyboard\\O )\\O issues\\O ,\\O relatively\\O slow\\O performance\\O and\\O shortening\\O battery\\B lifetime\\I .\\O", "opinion_tags": "It\\O was\\O also\\O suffering\\O from\\O hardware\\O (\\O keyboard\\O )\\O issues\\O ,\\O relatively\\O slow\\O performance\\O and\\O shortening\\B battery\\O lifetime\\O .\\O", "sentiment": "negative"}, {"uid": "394:1-2", "target_tags": "It\\O was\\O also\\O suffering\\O from\\O hardware\\B (\\I keyboard\\I )\\I issues\\O ,\\O relatively\\O slow\\O performance\\O and\\O shortening\\O battery\\O lifetime\\O .\\O", "opinion_tags": "It\\O was\\O also\\O suffering\\B from\\O hardware\\O (\\O keyboard\\O )\\O issues\\B ,\\O relatively\\O slow\\O performance\\O and\\O shortening\\O battery\\O lifetime\\O .\\O", "sentiment": "negative"}]}, {"id": "526:1", "sentence": "Runs good and does the job , ca n't complain about that !", "postag": ["VBZ", "JJ", "CC", "VBZ", "DT", "NN", ",", "MD", "RB", "VB", "IN", "DT", "."], "head": [0, 1, 4, 1, 6, 4, 1, 10, 10, 1, 12, 10, 1], "deprel": ["root", "xcomp", "cc", "conj", "det", "obj", "punct", "aux", "advmod", "parataxis", "case", "obl", "punct"], "triples": [{"uid": "526:1-0", "target_tags": "Runs\\B good\\O and\\O does\\O the\\O job\\O ,\\O ca\\O n't\\O complain\\O about\\O that\\O !\\O", "opinion_tags": "Runs\\O good\\B and\\O does\\O the\\O job\\O ,\\O ca\\O n't\\O complain\\O about\\O that\\O !\\O", "sentiment": "positive"}]}, {"id": "25:1", "sentence": "It 's silent and has a very small footprint on my desk .", "postag": ["PRP", "VBZ", "JJ", "CC", "VBZ", "DT", "RB", "JJ", "NN", "IN", "PRP$", "NN", "."], "head": [3, 3, 0, 5, 3, 9, 8, 9, 5, 12, 12, 9, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "det", "advmod", "amod", "obj", "case", "nmod:poss", "nmod", "punct"], "triples": [{"uid": "25:1-0", "target_tags": "It\\O 's\\O silent\\O and\\O has\\O a\\O very\\O small\\O footprint\\B on\\O my\\O desk\\O .\\O", "opinion_tags": "It\\O 's\\O silent\\O and\\O has\\O a\\O very\\O small\\B footprint\\O on\\O my\\O desk\\O .\\O", "sentiment": "positive"}]}, {"id": "379:1", "sentence": "The exterior is absolutely gorgeous .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "379:1-0", "target_tags": "The\\O exterior\\B is\\O absolutely\\O gorgeous\\O .\\O", "opinion_tags": "The\\O exterior\\O is\\O absolutely\\O gorgeous\\B .\\O", "sentiment": "positive"}]}, {"id": "1140:1", "sentence": "It has a very high performance , just for what I needed for .", "postag": ["PRP", "VBZ", "DT", "RB", "JJ", "NN", ",", "RB", "IN", "WP", "PRP", "VBD", "IN", "."], "head": [2, 0, 6, 5, 6, 2, 2, 10, 10, 6, 12, 10, 12, 2], "deprel": ["nsubj", "root", "det", "advmod", "amod", "obj", "punct", "advmod", "case", "nmod", "nsubj", "acl:relcl", "obl", "punct"], "triples": [{"uid": "1140:1-0", "target_tags": "It\\O has\\O a\\O very\\O high\\O performance\\B ,\\O just\\O for\\O what\\O I\\O needed\\O for\\O .\\O", "opinion_tags": "It\\O has\\O a\\O very\\O high\\B performance\\O ,\\O just\\O for\\O what\\O I\\O needed\\O for\\O .\\O", "sentiment": "positive"}]}, {"id": "204:1", "sentence": "Apple is aware of this issue and this is either old stock or a defective design involving the intel 4000 graphics chipset .", "postag": ["NNP", "VBZ", "JJ", "IN", "DT", "NN", "CC", "DT", "VBZ", "CC", "JJ", "NN", "CC", "DT", "JJ", "NN", "VBG", "DT", "NNP", "CD", "NNS", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 12, 12, 12, 12, 12, 3, 16, 16, 16, 12, 16, 22, 22, 19, 22, 17, 3], "deprel": ["nsubj", "cop", "root", "case", "det", "obl", "cc", "nsubj", "cop", "cc:preconj", "amod", "conj", "cc", "det", "amod", "conj", "acl", "det", "compound", "nummod", "compound", "obj", "punct"], "triples": [{"uid": "204:1-0", "target_tags": "Apple\\O is\\O aware\\O of\\O this\\O issue\\O and\\O this\\O is\\O either\\O old\\O stock\\O or\\O a\\O defective\\O design\\O involving\\O the\\O intel\\B 4000\\I graphics\\I chipset\\I .\\O", "opinion_tags": "Apple\\O is\\O aware\\O of\\O this\\O issue\\O and\\O this\\O is\\O either\\O old\\O stock\\O or\\O a\\O defective\\B design\\O involving\\O the\\O intel\\O 4000\\O graphics\\O chipset\\O .\\O", "sentiment": "neutral"}, {"uid": "204:1-1", "target_tags": "Apple\\O is\\O aware\\O of\\O this\\O issue\\O and\\O this\\O is\\O either\\O old\\O stock\\O or\\O a\\O defective\\O design\\B involving\\O the\\O intel\\O 4000\\O graphics\\O chipset\\O .\\O", "opinion_tags": "Apple\\O is\\O aware\\O of\\O this\\O issue\\O and\\O this\\O is\\O either\\O old\\O stock\\O or\\O a\\O defective\\B design\\O involving\\O the\\O intel\\O 4000\\O graphics\\O chipset\\O .\\O", "sentiment": "neutral"}]}, {"id": "786:713", "sentence": "I just bought the new MacBook Pro , the 13 '' model , and I ca n't believe Apple keeps making the same mistake with regard to USB ports .", "postag": ["PRP", "RB", "VBD", "DT", "JJ", "NNP", "NNP", ",", "DT", "CD", "''", "NN", ",", "CC", "PRP", "MD", "RB", "VB", "NNP", "VBZ", "VBG", "DT", "JJ", "NN", "IN", "NN", "IN", "NNP", "NNS", "."], "head": [3, 3, 0, 12, 12, 7, 12, 12, 12, 12, 12, 3, 18, 18, 18, 18, 18, 3, 20, 18, 20, 24, 24, 21, 26, 21, 29, 29, 26, 3], "deprel": ["nsubj", "advmod", "root", "det", "amod", "compound", "compound", "punct", "det", "compound", "punct", "obj", "punct", "cc", "nsubj", "aux", "advmod", "conj", "nsubj", "ccomp", "xcomp", "det", "amod", "obj", "case", "obl", "case", "compound", "nmod", "punct"], "triples": [{"uid": "786:713-0", "target_tags": "I\\O just\\O bought\\O the\\O new\\O MacBook\\O Pro\\O ,\\O the\\O 13\\O ''\\O model\\O ,\\O and\\O I\\O ca\\O n't\\O believe\\O Apple\\O keeps\\O making\\O the\\O same\\O mistake\\O with\\O regard\\O to\\O USB\\B ports\\I .\\O", "opinion_tags": "I\\O just\\O bought\\O the\\O new\\O MacBook\\O Pro\\O ,\\O the\\O 13\\O ''\\O model\\O ,\\O and\\O I\\O ca\\O n't\\O believe\\O Apple\\O keeps\\O making\\O the\\O same\\O mistake\\B with\\O regard\\O to\\O USB\\O ports\\O .\\O", "sentiment": "negative"}]}, {"id": "860:1", "sentence": "It is the perfect size and speed for me .", "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "CC", "NN", "IN", "PRP", "."], "head": [5, 5, 5, 5, 0, 7, 5, 9, 5, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "cc", "conj", "case", "nmod", "punct"], "triples": [{"uid": "860:1-0", "target_tags": "It\\O is\\O the\\O perfect\\O size\\B and\\O speed\\O for\\O me\\O .\\O", "opinion_tags": "It\\O is\\O the\\O perfect\\B size\\O and\\O speed\\O for\\O me\\O .\\O", "sentiment": "positive"}, {"uid": "860:1-1", "target_tags": "It\\O is\\O the\\O perfect\\O size\\O and\\O speed\\B for\\O me\\O .\\O", "opinion_tags": "It\\O is\\O the\\O perfect\\B size\\O and\\O speed\\O for\\O me\\O .\\O", "sentiment": "positive"}]}, {"id": "555:1", "sentence": "THE CUSTOMER SERVICE IS TERRIFIC ! !", "postag": ["DT", "NN", "NN", "VBZ", "JJ", ".", "."], "head": [3, 3, 5, 5, 0, 5, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "punct"], "triples": [{"uid": "555:1-0", "target_tags": "THE\\O CUSTOMER\\B SERVICE\\I IS\\O TERRIFIC\\O !\\O !\\O", "opinion_tags": "THE\\O CUSTOMER\\O SERVICE\\O IS\\O TERRIFIC\\B !\\O !\\O", "sentiment": "positive"}]}, {"id": "959:1", "sentence": "My last laptop was a 17 '' ASUS gaming machine , which performed admirably , but having since built my own desktop and really settling into the college life , I found myself wanting something smaller and less cumbersome , not to mention that the ASUS had been slowly developing problems ever since I bought it about 4 years ago .", "postag": ["PRP$", "JJ", "NN", "VBD", "DT", "CD", "``", "NN", "NN", "NN", ",", "WDT", "VBD", "RB", ",", "CC", "VBG", "RB", "VBN", "PRP$", "JJ", "NN", "CC", "RB", "VBG", "IN", "DT", "NN", "NN", ",", "PRP", "VBD", "PRP", "VBG", "NN", "JJR", "CC", "RBR", "JJ", ",", "RB", "TO", "VB", "IN", "DT", "NNP", "VBD", "VBN", "RB", "VBG", "NNS", "RB", "IN", "PRP", "VBD", "PRP", "RB", "CD", "NNS", "RB", "."], "head": [3, 3, 10, 10, 10, 10, 10, 9, 10, 0, 10, 13, 10, 13, 19, 19, 19, 19, 13, 22, 22, 19, 25, 25, 19, 29, 29, 29, 25, 32, 32, 10, 32, 32, 34, 35, 39, 39, 36, 32, 32, 41, 41, 50, 46, 50, 50, 50, 50, 43, 50, 50, 55, 55, 50, 55, 58, 59, 60, 55, 10], "deprel": ["nmod:poss", "amod", "nsubj", "cop", "det", "nummod", "punct", "compound", "compound", "root", "punct", "nsubj", "acl:relcl", "advmod", "punct", "cc", "aux", "advmod", "conj", "nmod:poss", "amod", "obj", "cc", "advmod", "conj", "case", "det", "compound", "obl", "punct", "nsubj", "conj", "obj", "xcomp", "obj", "amod", "cc", "advmod", "conj", "punct", "cc", "fixed", "fixed", "mark", "det", "nsubj", "aux", "aux", "advmod", "ccomp", "obj", "advmod", "mark", "nsubj", "advcl", "obj", "advmod", "nummod", "obl:npmod", "advmod", "punct"], "triples": [{"uid": "959:1-0", "target_tags": "My\\O last\\O laptop\\O was\\O a\\O 17\\O ''\\O ASUS\\O gaming\\O machine\\O ,\\O which\\O performed\\B admirably\\O ,\\O but\\O having\\O since\\O built\\O my\\O own\\O desktop\\O and\\O really\\O settling\\O into\\O the\\O college\\O life\\O ,\\O I\\O found\\O myself\\O wanting\\O something\\O smaller\\O and\\O less\\O cumbersome\\O ,\\O not\\O to\\O mention\\O that\\O the\\O ASUS\\O had\\O been\\O slowly\\O developing\\O problems\\O ever\\O since\\O I\\O bought\\O it\\O about\\O 4\\O years\\O ago\\O .\\O", "opinion_tags": "My\\O last\\O laptop\\O was\\O a\\O 17\\O ''\\O ASUS\\O gaming\\O machine\\O ,\\O which\\O performed\\O admirably\\B ,\\O but\\O having\\O since\\O built\\O my\\O own\\O desktop\\O and\\O really\\O settling\\O into\\O the\\O college\\O life\\O ,\\O I\\O found\\O myself\\O wanting\\O something\\O smaller\\O and\\O less\\O cumbersome\\O ,\\O not\\O to\\O mention\\O that\\O the\\O ASUS\\O had\\O been\\O slowly\\O developing\\O problems\\O ever\\O since\\O I\\O bought\\O it\\O about\\O 4\\O years\\O ago\\O .\\O", "sentiment": "positive"}]}, {"id": "908:1", "sentence": "However , it did not have any scratches , ZERO battery cycle count ( pretty surprised ) , and all the hardware seemed to be working perfectly .", "postag": ["RB", ",", "PRP", "VBD", "RB", "VB", "DT", "NNS", ",", "JJ", "NN", "NN", "NN", "-LRB-", "RB", "JJ", "-RRB-", ",", "CC", "PDT", "DT", "NN", "VBD", "TO", "VB", "VBG", "RB", "."], "head": [6, 6, 6, 6, 6, 0, 8, 6, 13, 13, 12, 13, 8, 16, 16, 13, 16, 23, 23, 22, 22, 23, 6, 26, 26, 23, 26, 6], "deprel": ["advmod", "punct", "nsubj", "aux", "advmod", "root", "det", "obj", "punct", "amod", "compound", "compound", "conj", "punct", "advmod", "amod", "punct", "punct", "cc", "det:predet", "det", "nsubj", "conj", "mark", "aux", "xcomp", "advmod", "punct"], "triples": [{"uid": "908:1-0", "target_tags": "However\\O ,\\O it\\O did\\O not\\O have\\O any\\O scratches\\O ,\\O ZERO\\O battery\\B cycle\\I count\\I (\\O pretty\\O surprised\\O )\\O ,\\O and\\O all\\O the\\O hardware\\O seemed\\O to\\O be\\O working\\O perfectly\\O .\\O", "opinion_tags": "However\\O ,\\O it\\O did\\O not\\O have\\O any\\O scratches\\O ,\\O ZERO\\B battery\\O cycle\\O count\\O (\\O pretty\\O surprised\\B )\\O ,\\O and\\O all\\O the\\O hardware\\O seemed\\O to\\O be\\O working\\O perfectly\\O .\\O", "sentiment": "positive"}, {"uid": "908:1-1", "target_tags": "However\\O ,\\O it\\O did\\O not\\O have\\O any\\O scratches\\O ,\\O ZERO\\O battery\\O cycle\\O count\\O (\\O pretty\\O surprised\\O )\\O ,\\O and\\O all\\O the\\O hardware\\B seemed\\O to\\O be\\O working\\O perfectly\\O .\\O", "opinion_tags": "However\\O ,\\O it\\O did\\O not\\O have\\O any\\O scratches\\O ,\\O ZERO\\O battery\\O cycle\\O count\\O (\\O pretty\\O surprised\\O )\\O ,\\O and\\O all\\O the\\O hardware\\O seemed\\O to\\O be\\O working\\O perfectly\\B .\\O", "sentiment": "positive"}]}, {"id": "787:193", "sentence": "And as for all the fancy finger swipes -- I just gave up and attached a mouse .", "postag": ["CC", "IN", "IN", "PDT", "DT", "JJ", "NN", "NNS", ",", "PRP", "RB", "VBD", "RP", "CC", "VBD", "DT", "NN", "."], "head": [12, 8, 8, 8, 8, 8, 8, 12, 12, 12, 12, 0, 12, 15, 12, 17, 15, 12], "deprel": ["cc", "case", "case", "det:predet", "det", "amod", "compound", "obl", "punct", "nsubj", "advmod", "root", "compound:prt", "cc", "conj", "det", "obj", "punct"], "triples": [{"uid": "787:193-0", "target_tags": "And\\O as\\O for\\O all\\O the\\O fancy\\O finger\\B swipes\\I --\\O I\\O just\\O gave\\O up\\O and\\O attached\\O a\\O mouse\\O .\\O", "opinion_tags": "And\\O as\\O for\\O all\\O the\\O fancy\\B finger\\O swipes\\O --\\O I\\O just\\O gave\\O up\\O and\\O attached\\O a\\O mouse\\O .\\O", "sentiment": "negative"}]}, {"id": "447:1", "sentence": "I needed a laptop with big storage , a nice screen and fast so I can photoshop without any problem .", "postag": ["PRP", "VBD", "DT", "NN", "IN", "JJ", "NN", ",", "DT", "JJ", "NN", "CC", "JJ", "RB", "PRP", "MD", "VB", "IN", "DT", "NN", "."], "head": [2, 0, 4, 2, 7, 7, 4, 11, 11, 11, 7, 13, 7, 17, 17, 17, 2, 20, 20, 17, 2], "deprel": ["nsubj", "root", "det", "obj", "case", "amod", "nmod", "punct", "det", "amod", "conj", "cc", "conj", "advmod", "nsubj", "aux", "conj", "case", "det", "obl", "punct"], "triples": [{"uid": "447:1-0", "target_tags": "I\\O needed\\O a\\O laptop\\O with\\O big\\O storage\\B ,\\O a\\O nice\\O screen\\O and\\O fast\\O so\\O I\\O can\\O photoshop\\O without\\O any\\O problem\\O .\\O", "opinion_tags": "I\\O needed\\O a\\O laptop\\O with\\O big\\B storage\\O ,\\O a\\O nice\\O screen\\O and\\O fast\\O so\\O I\\O can\\O photoshop\\O without\\O any\\O problem\\O .\\O", "sentiment": "neutral"}, {"uid": "447:1-1", "target_tags": "I\\O needed\\O a\\O laptop\\O with\\O big\\O storage\\O ,\\O a\\O nice\\O screen\\B and\\O fast\\O so\\O I\\O can\\O photoshop\\O without\\O any\\O problem\\O .\\O", "opinion_tags": "I\\O needed\\O a\\O laptop\\O with\\O big\\O storage\\O ,\\O a\\O nice\\B screen\\O and\\O fast\\O so\\O I\\O can\\O photoshop\\O without\\O any\\O problem\\O .\\O", "sentiment": "neutral"}]}, {"id": "928:1", "sentence": "I like coming back to Mac OS but this laptop is lacking in speaker quality compared to my $ 400 old HP laptop .", "postag": ["PRP", "VBP", "VBG", "RB", "IN", "NNP", "NNP", "CC", "DT", "NN", "VBZ", "VBG", "IN", "NN", "NN", "VBN", "IN", "PRP$", "$", "CD", "JJ", "NNP", "NN", "."], "head": [2, 0, 2, 3, 7, 7, 4, 12, 10, 12, 12, 2, 15, 15, 12, 23, 23, 23, 23, 19, 23, 23, 12, 2], "deprel": ["nsubj", "root", "xcomp", "advmod", "case", "compound", "obl", "cc", "det", "nsubj", "aux", "conj", "case", "compound", "obl", "case", "case", "nmod:poss", "compound", "nummod", "amod", "compound", "obl", "punct"], "triples": [{"uid": "928:1-0", "target_tags": "I\\O like\\O coming\\O back\\O to\\O Mac\\B OS\\I but\\O this\\O laptop\\O is\\O lacking\\O in\\O speaker\\O quality\\O compared\\O to\\O my\\O $\\O 400\\O old\\O HP\\O laptop\\O .\\O", "opinion_tags": "I\\O like\\B coming\\O back\\O to\\O Mac\\O OS\\O but\\O this\\O laptop\\O is\\O lacking\\O in\\O speaker\\O quality\\O compared\\O to\\O my\\O $\\O 400\\O old\\O HP\\O laptop\\O .\\O", "sentiment": "positive"}, {"uid": "928:1-1", "target_tags": "I\\O like\\O coming\\O back\\O to\\O Mac\\O OS\\O but\\O this\\O laptop\\O is\\O lacking\\O in\\O speaker\\B quality\\I compared\\O to\\O my\\O $\\O 400\\O old\\O HP\\O laptop\\O .\\O", "opinion_tags": "I\\O like\\O coming\\O back\\O to\\O Mac\\O OS\\O but\\O this\\O laptop\\O is\\O lacking\\B in\\O speaker\\O quality\\O compared\\O to\\O my\\O $\\O 400\\O old\\O HP\\O laptop\\O .\\O", "sentiment": "negative"}]}, {"id": "776:1", "sentence": "Shipped very quickly and safely .", "postag": ["VBN", "RB", "RB", "CC", "RB", "."], "head": [0, 3, 1, 5, 3, 1], "deprel": ["root", "advmod", "advmod", "cc", "conj", "punct"], "triples": [{"uid": "776:1-0", "target_tags": "Shipped\\B very\\O quickly\\O and\\O safely\\O .\\O", "opinion_tags": "Shipped\\O very\\O quickly\\B and\\O safely\\B .\\O", "sentiment": "positive"}]}, {"id": "787:568", "sentence": "The thunderbolt port is awesome !", "postag": ["DT", "NN", "NN", "VBZ", "JJ", "."], "head": [3, 3, 5, 5, 0, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "787:568-0", "target_tags": "The\\O thunderbolt\\B port\\I is\\O awesome\\O !\\O", "opinion_tags": "The\\O thunderbolt\\O port\\O is\\O awesome\\B !\\O", "sentiment": "positive"}]}, {"id": "691:1", "sentence": "The performance is definitely superior to any computer I 've ever put my hands on .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "IN", "DT", "NN", "PRP", "VBP", "RB", "VBN", "PRP$", "NNS", "RB", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 12, 12, 12, 8, 14, 12, 12, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "det", "obl", "nsubj", "aux", "advmod", "acl:relcl", "nmod:poss", "obj", "advmod", "punct"], "triples": [{"uid": "691:1-0", "target_tags": "The\\O performance\\B is\\O definitely\\O superior\\O to\\O any\\O computer\\O I\\O 've\\O ever\\O put\\O my\\O hands\\O on\\O .\\O", "opinion_tags": "The\\O performance\\O is\\O definitely\\O superior\\B to\\O any\\O computer\\O I\\O 've\\O ever\\O put\\O my\\O hands\\O on\\O .\\O", "sentiment": "positive"}]}, {"id": "242:1", "sentence": "It 's great for streaming video and other entertainment uses .", "postag": ["PRP", "VBZ", "JJ", "IN", "NN", "NN", "CC", "JJ", "NN", "NNS", "."], "head": [3, 3, 0, 6, 6, 3, 10, 10, 10, 6, 3], "deprel": ["nsubj", "cop", "root", "case", "compound", "obl", "cc", "amod", "compound", "conj", "punct"], "triples": [{"uid": "242:1-0", "target_tags": "It\\O 's\\O great\\O for\\O streaming\\B video\\I and\\O other\\O entertainment\\O uses\\O .\\O", "opinion_tags": "It\\O 's\\O great\\B for\\O streaming\\O video\\O and\\O other\\O entertainment\\O uses\\O .\\O", "sentiment": "positive"}, {"uid": "242:1-1", "target_tags": "It\\O 's\\O great\\O for\\O streaming\\O video\\O and\\O other\\O entertainment\\B uses\\I .\\O", "opinion_tags": "It\\O 's\\O great\\B for\\O streaming\\O video\\O and\\O other\\O entertainment\\O uses\\O .\\O", "sentiment": "positive"}]}, {"id": "567:1", "sentence": "I like the design and its features but there are somethings I think needs to be improved .", "postag": ["PRP", "VBP", "DT", "NN", "CC", "PRP$", "NNS", "CC", "EX", "VBP", "NNS", "PRP", "VBP", "VBZ", "TO", "VB", "VBN", "."], "head": [2, 0, 4, 2, 7, 7, 4, 10, 10, 2, 10, 13, 11, 13, 17, 17, 14, 2], "deprel": ["nsubj", "root", "det", "obj", "cc", "nmod:poss", "conj", "cc", "expl", "conj", "nsubj", "nsubj", "acl:relcl", "ccomp", "mark", "aux:pass", "xcomp", "punct"], "triples": [{"uid": "567:1-0", "target_tags": "I\\O like\\O the\\O design\\B and\\O its\\O features\\O but\\O there\\O are\\O somethings\\O I\\O think\\O needs\\O to\\O be\\O improved\\O .\\O", "opinion_tags": "I\\O like\\B the\\O design\\O and\\O its\\O features\\O but\\O there\\O are\\O somethings\\O I\\O think\\O needs\\O to\\O be\\O improved\\O .\\O", "sentiment": "positive"}, {"uid": "567:1-1", "target_tags": "I\\O like\\O the\\O design\\O and\\O its\\O features\\B but\\O there\\O are\\O somethings\\O I\\O think\\O needs\\O to\\O be\\O improved\\O .\\O", "opinion_tags": "I\\O like\\O the\\O design\\O and\\O its\\O features\\O but\\O there\\O are\\O somethings\\O I\\O think\\O needs\\B to\\I be\\I improved\\I .\\O", "sentiment": "positive"}]}, {"id": "507:1", "sentence": "There were small problems with Mac office .", "postag": ["EX", "VBD", "JJ", "NNS", "IN", "NNP", "NN", "."], "head": [2, 0, 4, 2, 7, 7, 4, 2], "deprel": ["expl", "root", "amod", "nsubj", "case", "compound", "nmod", "punct"], "triples": [{"uid": "507:1-0", "target_tags": "There\\O were\\O small\\O problems\\O with\\O Mac\\B office\\I .\\O", "opinion_tags": "There\\O were\\O small\\O problems\\B with\\O Mac\\O office\\O .\\O", "sentiment": "negative"}]}, {"id": "19:0", "sentence": "I ordered my 2012 mac mini after being disappointed with spec of the new 27 '' Imacs .", "postag": ["PRP", "VBD", "PRP$", "CD", "NN", "NN", "IN", "VBG", "JJ", "IN", "NN", "IN", "DT", "JJ", "CD", "''", "NNPS", "."], "head": [2, 0, 6, 6, 6, 2, 9, 9, 2, 11, 9, 17, 17, 17, 17, 17, 11, 2], "deprel": ["nsubj", "root", "nmod:poss", "compound", "compound", "obj", "mark", "cop", "advcl", "case", "obl", "case", "det", "amod", "compound", "punct", "nmod", "punct"], "triples": [{"uid": "19:0-0", "target_tags": "I\\O ordered\\O my\\O 2012\\O mac\\O mini\\O after\\O being\\O disappointed\\O with\\O spec\\B of\\O the\\O new\\O 27\\O ''\\O Imacs\\O .\\O", "opinion_tags": "I\\O ordered\\O my\\O 2012\\O mac\\O mini\\O after\\O being\\O disappointed\\B with\\O spec\\O of\\O the\\O new\\O 27\\O ''\\O Imacs\\O .\\O", "sentiment": "negative"}]}, {"id": "816:1", "sentence": "Its fast , easy to use and it looks great .", "postag": ["PRP$", "JJ", ",", "JJ", "TO", "VB", "CC", "PRP", "VBZ", "JJ", "."], "head": [2, 0, 4, 2, 6, 2, 9, 9, 2, 9, 2], "deprel": ["nmod:poss", "root", "punct", "conj", "mark", "advcl", "cc", "nsubj", "conj", "xcomp", "punct"], "triples": [{"uid": "816:1-0", "target_tags": "Its\\O fast\\O ,\\O easy\\O to\\O use\\B and\\O it\\O looks\\O great\\O .\\O", "opinion_tags": "Its\\O fast\\O ,\\O easy\\B to\\O use\\O and\\O it\\O looks\\O great\\O .\\O", "sentiment": "positive"}, {"uid": "816:1-1", "target_tags": "Its\\O fast\\O ,\\O easy\\O to\\O use\\O and\\O it\\O looks\\B great\\O .\\O", "opinion_tags": "Its\\O fast\\O ,\\O easy\\O to\\O use\\O and\\O it\\O looks\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "313:1", "sentence": "Performance is much much better on the Pro , especially if you install an SSD on it .", "postag": ["NN", "VBZ", "RB", "RB", "JJR", "IN", "DT", "NN", ",", "RB", "IN", "PRP", "VBP", "DT", "NN", "IN", "PRP", "."], "head": [5, 5, 4, 5, 0, 8, 8, 5, 5, 13, 13, 13, 5, 15, 13, 17, 13, 5], "deprel": ["nsubj", "cop", "advmod", "advmod", "root", "case", "det", "obl", "punct", "advmod", "mark", "nsubj", "advcl", "det", "obj", "case", "obl", "punct"], "triples": [{"uid": "313:1-0", "target_tags": "Performance\\B is\\O much\\O much\\O better\\O on\\O the\\O Pro\\O ,\\O especially\\O if\\O you\\O install\\O an\\O SSD\\O on\\O it\\O .\\O", "opinion_tags": "Performance\\O is\\O much\\O much\\O better\\B on\\O the\\O Pro\\O ,\\O especially\\O if\\O you\\O install\\O an\\O SSD\\O on\\O it\\O .\\O", "sentiment": "positive"}, {"uid": "313:1-1", "target_tags": "Performance\\O is\\O much\\O much\\O better\\O on\\O the\\O Pro\\O ,\\O especially\\O if\\O you\\O install\\O an\\O SSD\\B on\\O it\\O .\\O", "opinion_tags": "Performance\\O is\\O much\\O much\\O better\\B on\\O the\\O Pro\\O ,\\O especially\\O if\\O you\\O install\\O an\\O SSD\\O on\\O it\\O .\\O", "sentiment": "positive"}]}, {"id": "286:1", "sentence": "Note , however , that any existing MagSafe accessories you have will not work with the MagSafe 2 connection .", "postag": ["NN", ",", "RB", ",", "IN", "DT", "VBG", "NN", "NNS", "PRP", "VBP", "MD", "RB", "VB", "IN", "DT", "NN", "CD", "NN", "."], "head": [0, 1, 14, 14, 14, 9, 9, 9, 14, 11, 9, 14, 14, 1, 19, 19, 19, 17, 14, 1], "deprel": ["root", "punct", "advmod", "punct", "mark", "det", "amod", "compound", "nsubj", "nsubj", "acl:relcl", "aux", "advmod", "appos", "case", "det", "compound", "nummod", "obl", "punct"], "triples": [{"uid": "286:1-0", "target_tags": "Note\\O ,\\O however\\O ,\\O that\\O any\\O existing\\O MagSafe\\B accessories\\I you\\O have\\O will\\O not\\O work\\O with\\O the\\O MagSafe\\O 2\\O connection\\O .\\O", "opinion_tags": "Note\\O ,\\O however\\O ,\\O that\\O any\\O existing\\O MagSafe\\O accessories\\O you\\O have\\O will\\O not\\B work\\I with\\O the\\O MagSafe\\O 2\\O connection\\O .\\O", "sentiment": "neutral"}, {"uid": "286:1-1", "target_tags": "Note\\O ,\\O however\\O ,\\O that\\O any\\O existing\\O MagSafe\\O accessories\\O you\\O have\\O will\\O not\\O work\\O with\\O the\\O MagSafe\\B 2\\I connection\\I .\\O", "opinion_tags": "Note\\O ,\\O however\\O ,\\O that\\O any\\O existing\\O MagSafe\\O accessories\\O you\\O have\\O will\\O not\\B work\\I with\\O the\\O MagSafe\\O 2\\O connection\\O .\\O", "sentiment": "negative"}]}, {"id": "1074:1", "sentence": "The only thing I dislike is the touchpad , alot of the times its unresponsive and does things I dont want it too , I would recommend using a mouse with it .", "postag": ["DT", "JJ", "NN", "PRP", "VBP", "VBZ", "DT", "NN", ",", "DT", "IN", "DT", "NNS", "PRP$", "JJ", "CC", "VBZ", "NNS", "PRP", "MD", "VB", "PRP", "RB", ",", "PRP", "MD", "VB", "VBG", "DT", "NN", "IN", "PRP", "."], "head": [3, 3, 8, 5, 3, 8, 8, 0, 8, 8, 13, 13, 10, 15, 8, 17, 8, 17, 21, 21, 18, 21, 21, 8, 27, 27, 8, 27, 30, 28, 32, 28, 8], "deprel": ["det", "amod", "nsubj", "nsubj", "acl:relcl", "cop", "det", "root", "punct", "appos", "case", "det", "nmod", "nmod:poss", "parataxis", "cc", "conj", "obj", "nsubj", "aux", "acl:relcl", "obj", "advmod", "punct", "nsubj", "aux", "parataxis", "xcomp", "det", "obj", "case", "obl", "punct"], "triples": [{"uid": "1074:1-0", "target_tags": "The\\O only\\O thing\\O I\\O dislike\\O is\\O the\\O touchpad\\B ,\\O alot\\O of\\O the\\O times\\O its\\O unresponsive\\O and\\O does\\O things\\O I\\O dont\\O want\\O it\\O too\\O ,\\O I\\O would\\O recommend\\O using\\O a\\O mouse\\O with\\O it\\O .\\O", "opinion_tags": "The\\O only\\O thing\\O I\\O dislike\\B is\\O the\\O touchpad\\O ,\\O alot\\O of\\O the\\O times\\O its\\O unresponsive\\B and\\O does\\O things\\O I\\O dont\\O want\\O it\\O too\\O ,\\O I\\O would\\O recommend\\O using\\O a\\O mouse\\O with\\O it\\O .\\O", "sentiment": "negative"}, {"uid": "1074:1-1", "target_tags": "The\\O only\\O thing\\O I\\O dislike\\O is\\O the\\O touchpad\\O ,\\O alot\\O of\\O the\\O times\\O its\\O unresponsive\\O and\\O does\\O things\\O I\\O dont\\O want\\O it\\O too\\O ,\\O I\\O would\\O recommend\\O using\\O a\\O mouse\\B with\\O it\\O .\\O", "opinion_tags": "The\\O only\\O thing\\O I\\O dislike\\O is\\O the\\O touchpad\\O ,\\O alot\\O of\\O the\\O times\\O its\\O unresponsive\\O and\\O does\\O things\\O I\\O dont\\O want\\O it\\O too\\O ,\\O I\\O would\\O recommend\\B using\\O a\\O mouse\\O with\\O it\\O .\\O", "sentiment": "neutral"}]}, {"id": "202:1", "sentence": "The Mac mini is about 8x smaller than my old computer which is a huge bonus and runs very quiet , actually the fans are n't audible unlike my old pc", "postag": ["DT", "NNP", "NN", "VBZ", "RB", "NN", "JJR", "IN", "PRP$", "JJ", "NN", "WDT", "VBZ", "DT", "JJ", "NN", "CC", "VBZ", "RB", "JJ", ",", "RB", "DT", "NNS", "VBP", "RB", "JJ", "IN", "PRP$", "JJ", "NN"], "head": [3, 3, 7, 7, 6, 7, 0, 11, 11, 11, 7, 16, 16, 16, 16, 11, 18, 7, 20, 18, 7, 27, 24, 27, 27, 27, 7, 31, 31, 31, 27], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "obl:npmod", "root", "case", "nmod:poss", "amod", "obl", "nsubj", "cop", "det", "amod", "acl:relcl", "cc", "conj", "advmod", "advmod", "punct", "advmod", "det", "nsubj", "cop", "advmod", "parataxis", "case", "nmod:poss", "amod", "obl"], "triples": [{"uid": "202:1-0", "target_tags": "The\\O Mac\\O mini\\O is\\O about\\O 8x\\O smaller\\O than\\O my\\O old\\O computer\\O which\\O is\\O a\\O huge\\O bonus\\O and\\O runs\\B very\\O quiet\\O ,\\O actually\\O the\\O fans\\O are\\O n't\\O audible\\O unlike\\O my\\O old\\O pc\\O", "opinion_tags": "The\\O Mac\\O mini\\O is\\O about\\O 8x\\O smaller\\O than\\O my\\O old\\O computer\\O which\\O is\\O a\\O huge\\O bonus\\O and\\O runs\\O very\\O quiet\\B ,\\O actually\\O the\\O fans\\O are\\O n't\\O audible\\O unlike\\O my\\O old\\O pc\\O", "sentiment": "positive"}, {"uid": "202:1-1", "target_tags": "The\\O Mac\\O mini\\O is\\O about\\O 8x\\O smaller\\O than\\O my\\O old\\O computer\\O which\\O is\\O a\\O huge\\O bonus\\O and\\O runs\\O very\\O quiet\\O ,\\O actually\\O the\\O fans\\B are\\O n't\\O audible\\O unlike\\O my\\O old\\O pc\\O", "opinion_tags": "The\\O Mac\\O mini\\O is\\O about\\O 8x\\O smaller\\O than\\O my\\O old\\O computer\\O which\\O is\\O a\\O huge\\O bonus\\O and\\O runs\\O very\\O quiet\\O ,\\O actually\\O the\\O fans\\O are\\B n't\\I audible\\I unlike\\O my\\O old\\O pc\\O", "sentiment": "positive"}]}, {"id": "786:867", "sentence": "I thought the transition would be difficult at best and would take some time to fully familiarize myself with the new Mac ecosystem .", "postag": ["PRP", "VBD", "DT", "NN", "MD", "VB", "JJ", "RB", "JJS", "CC", "MD", "VB", "DT", "NN", "TO", "RB", "VB", "PRP", "IN", "DT", "JJ", "NNP", "NN", "."], "head": [2, 0, 4, 7, 7, 7, 2, 9, 7, 12, 12, 7, 14, 12, 17, 17, 12, 17, 23, 23, 23, 23, 17, 2], "deprel": ["nsubj", "root", "det", "nsubj", "aux", "cop", "ccomp", "case", "obl", "cc", "aux", "conj", "det", "obj", "mark", "advmod", "advcl", "obj", "case", "det", "amod", "compound", "obl", "punct"], "triples": [{"uid": "786:867-0", "target_tags": "I\\O thought\\O the\\O transition\\O would\\O be\\O difficult\\O at\\O best\\O and\\O would\\O take\\O some\\O time\\O to\\O fully\\O familiarize\\O myself\\O with\\O the\\O new\\O Mac\\B ecosystem\\I .\\O", "opinion_tags": "I\\O thought\\O the\\O transition\\O would\\O be\\O difficult\\O at\\O best\\O and\\O would\\O take\\B some\\I time\\I to\\O fully\\O familiarize\\O myself\\O with\\O the\\O new\\O Mac\\O ecosystem\\O .\\O", "sentiment": "neutral"}]}, {"id": "533:1", "sentence": "It 's absolutely wonderful and worth the price !", "postag": ["PRP", "VBZ", "RB", "JJ", "CC", "JJ", "DT", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 4], "deprel": ["nsubj", "cop", "advmod", "root", "cc", "conj", "det", "obj", "punct"], "triples": [{"uid": "533:1-0", "target_tags": "It\\O 's\\O absolutely\\O wonderful\\O and\\O worth\\O the\\O price\\B !\\O", "opinion_tags": "It\\O 's\\O absolutely\\O wonderful\\B and\\O worth\\B the\\O price\\O !\\O", "sentiment": "positive"}]}, {"id": "1131:1", "sentence": "I am please with the products ease of use ; out of the box ready ; appearance and functionality .", "postag": ["PRP", "VBP", "UH", "IN", "DT", "NNS", "NN", "IN", "NN", ",", "IN", "IN", "DT", "NN", "JJ", ",", "NN", "CC", "NN", "."], "head": [2, 0, 2, 7, 7, 7, 2, 9, 7, 14, 14, 14, 14, 7, 14, 17, 14, 19, 17, 2], "deprel": ["nsubj", "root", "discourse", "case", "det", "compound", "obl", "case", "nmod", "punct", "case", "case", "det", "nmod", "amod", "punct", "appos", "cc", "conj", "punct"], "triples": [{"uid": "1131:1-0", "target_tags": "I\\O am\\O please\\O with\\O the\\O products\\O ease\\O of\\O use\\B ;\\O out\\O of\\O the\\O box\\O ready\\O ;\\O appearance\\O and\\O functionality\\O .\\O", "opinion_tags": "I\\O am\\O please\\B with\\O the\\O products\\O ease\\B of\\O use\\O ;\\O out\\O of\\O the\\O box\\O ready\\O ;\\O appearance\\O and\\O functionality\\O .\\O", "sentiment": "positive"}, {"uid": "1131:1-1", "target_tags": "I\\O am\\O please\\O with\\O the\\O products\\O ease\\O of\\O use\\O ;\\O out\\O of\\O the\\O box\\O ready\\O ;\\O appearance\\B and\\O functionality\\O .\\O", "opinion_tags": "I\\O am\\O please\\B with\\O the\\O products\\O ease\\O of\\O use\\O ;\\O out\\O of\\O the\\O box\\O ready\\O ;\\O appearance\\O and\\O functionality\\O .\\O", "sentiment": "positive"}, {"uid": "1131:1-2", "target_tags": "I\\O am\\O please\\O with\\O the\\O products\\O ease\\O of\\O use\\O ;\\O out\\O of\\O the\\O box\\O ready\\O ;\\O appearance\\O and\\O functionality\\B .\\O", "opinion_tags": "I\\O am\\O please\\B with\\O the\\O products\\O ease\\O of\\O use\\O ;\\O out\\O of\\O the\\O box\\O ready\\O ;\\O appearance\\O and\\O functionality\\O .\\O", "sentiment": "positive"}]}, {"id": "662:1", "sentence": "Perfect for all my graphic design classes I 'm taking this year in college : - )", "postag": ["JJ", "IN", "PDT", "PRP$", "JJ", "NN", "NNS", "PRP", "VBP", "VBG", "DT", "NN", "IN", "NN", ":", ",", "-RRB-"], "head": [0, 7, 7, 7, 7, 7, 1, 10, 10, 7, 12, 10, 14, 10, 1, 1, 1], "deprel": ["root", "case", "det:predet", "nmod:poss", "amod", "compound", "obl", "nsubj", "aux", "acl:relcl", "det", "obl:tmod", "case", "obl", "punct", "punct", "punct"], "triples": [{"uid": "662:1-0", "target_tags": "Perfect\\O for\\O all\\O my\\O graphic\\B design\\I classes\\O I\\O 'm\\O taking\\O this\\O year\\O in\\O college\\O :\\O -\\O )\\O", "opinion_tags": "Perfect\\B for\\O all\\O my\\O graphic\\O design\\O classes\\O I\\O 'm\\O taking\\O this\\O year\\O in\\O college\\O :\\O -\\O )\\O", "sentiment": "positive"}]}, {"id": "786:1473", "sentence": "I will not be using that slot again .", "postag": ["PRP", "MD", "RB", "VB", "VBG", "DT", "NN", "RB", "."], "head": [5, 5, 5, 5, 0, 7, 5, 5, 5], "deprel": ["nsubj", "aux", "advmod", "aux", "root", "det", "obj", "advmod", "punct"], "triples": [{"uid": "786:1473-0", "target_tags": "I\\O will\\O not\\O be\\O using\\O that\\O slot\\B again\\O .\\O", "opinion_tags": "I\\O will\\O not\\B be\\I using\\I that\\O slot\\O again\\O .\\O", "sentiment": "negative"}]}, {"id": "289:5", "sentence": "The OS is fast and fluid , everything is organized and it 's just beautiful .", "postag": ["DT", "NNP", "VBZ", "JJ", "CC", "NN", ",", "NN", "VBZ", "VBN", "CC", "PRP", "VBZ", "RB", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4, 10, 10, 4, 15, 15, 15, 15, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct", "nsubj:pass", "aux:pass", "parataxis", "cc", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "289:5-0", "target_tags": "The\\O OS\\B is\\O fast\\O and\\O fluid\\O ,\\O everything\\O is\\O organized\\O and\\O it\\O 's\\O just\\O beautiful\\O .\\O", "opinion_tags": "The\\O OS\\O is\\O fast\\B and\\O fluid\\B ,\\O everything\\O is\\O organized\\B and\\O it\\O 's\\O just\\O beautiful\\B .\\O", "sentiment": "positive"}]}, {"id": "843:3", "sentence": "They are simpler to use .", "postag": ["PRP", "VBP", "JJR", "TO", "VB", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "cop", "root", "mark", "xcomp", "punct"], "triples": [{"uid": "843:3-0", "target_tags": "They\\O are\\O simpler\\O to\\O use\\B .\\O", "opinion_tags": "They\\O are\\O simpler\\B to\\O use\\O .\\O", "sentiment": "positive"}]}, {"id": "938:1", "sentence": "! so nice .. stable .. fast .. now i got my SSD !", "postag": [".", "RB", "JJ", ",", "JJ", ",", "JJ", ",", "RB", "PRP", "VBD", "PRP$", "NN", "."], "head": [3, 3, 0, 3, 3, 3, 3, 3, 11, 11, 3, 13, 11, 3], "deprel": ["punct", "advmod", "root", "punct", "parataxis", "punct", "parataxis", "punct", "advmod", "nsubj", "parataxis", "nmod:poss", "obj", "punct"], "triples": [{"uid": "938:1-0", "target_tags": "!\\O so\\O nice\\O ..\\O stable\\O ..\\O fast\\O ..\\O now\\O i\\O got\\O my\\O SSD\\B !\\O", "opinion_tags": "!\\O so\\O nice\\B ..\\O stable\\B ..\\O fast\\B ..\\O now\\O i\\O got\\O my\\O SSD\\O !\\O", "sentiment": "positive"}]}, {"id": "29:742", "sentence": "I love the quick start up .", "postag": ["PRP", "VBP", "DT", "JJ", "NN", "RP", "."], "head": [2, 0, 5, 5, 2, 2, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "compound:prt", "punct"], "triples": [{"uid": "29:742-0", "target_tags": "I\\O love\\O the\\O quick\\O start\\B up\\I .\\O", "opinion_tags": "I\\O love\\B the\\O quick\\O start\\O up\\O .\\O", "sentiment": "positive"}]}, {"id": "294:0", "sentence": "You just can not beat the functionality of an Apple device .", "postag": ["PRP", "RB", "MD", "RB", "VB", "DT", "NN", "IN", "DT", "NNP", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 11, 11, 11, 7, 5], "deprel": ["nsubj", "advmod", "aux", "advmod", "root", "det", "obj", "case", "det", "compound", "nmod", "punct"], "triples": [{"uid": "294:0-0", "target_tags": "You\\O just\\O can\\O not\\O beat\\O the\\O functionality\\B of\\O an\\O Apple\\O device\\O .\\O", "opinion_tags": "You\\O just\\O can\\B not\\I beat\\I the\\O functionality\\O of\\O an\\O Apple\\O device\\O .\\O", "sentiment": "positive"}]}, {"id": "547:17", "sentence": "Yet my mac continues to function properly .", "postag": ["CC", "PRP$", "NN", "VBZ", "TO", "VB", "RB", "."], "head": [4, 3, 4, 0, 6, 4, 6, 4], "deprel": ["cc", "nmod:poss", "nsubj", "root", "mark", "xcomp", "advmod", "punct"], "triples": [{"uid": "547:17-0", "target_tags": "Yet\\O my\\O mac\\O continues\\O to\\O function\\B properly\\O .\\O", "opinion_tags": "Yet\\O my\\O mac\\O continues\\O to\\O function\\O properly\\B .\\O", "sentiment": "positive"}]}, {"id": "463:9", "sentence": "Graphics are much improved .", "postag": ["NNS", "VBP", "RB", "VBN", "."], "head": [4, 4, 4, 0, 4], "deprel": ["nsubj:pass", "aux:pass", "advmod", "root", "punct"], "triples": [{"uid": "463:9-0", "target_tags": "Graphics\\B are\\O much\\O improved\\O .\\O", "opinion_tags": "Graphics\\O are\\O much\\O improved\\B .\\O", "sentiment": "positive"}]}, {"id": "275:7", "sentence": "Here are the things that made me confident with my purchase : Build Quality - Seriously , you ca n't beat a unibody construction .", "postag": ["RB", "VBP", "DT", "NNS", "WDT", "VBD", "PRP", "JJ", "IN", "PRP$", "NN", ":", "VB", "NN", ",", "RB", ",", "PRP", "MD", "RB", "VB", "DT", "NN", "NN", "."], "head": [0, 1, 4, 1, 6, 4, 6, 6, 11, 11, 8, 13, 1, 13, 13, 13, 21, 21, 21, 21, 13, 24, 24, 21, 1], "deprel": ["root", "cop", "det", "nsubj", "nsubj", "acl:relcl", "obj", "xcomp", "case", "nmod:poss", "obl", "punct", "parataxis", "obj", "punct", "advmod", "punct", "nsubj", "aux", "advmod", "parataxis", "det", "compound", "obj", "punct"], "triples": [{"uid": "275:7-0", "target_tags": "Here\\O are\\O the\\O things\\O that\\O made\\O me\\O confident\\O with\\O my\\O purchase\\O :\\O Build\\B Quality\\I -\\O Seriously\\O ,\\O you\\O ca\\O n't\\O beat\\O a\\O unibody\\O construction\\O .\\O", "opinion_tags": "Here\\O are\\O the\\O things\\O that\\O made\\O me\\O confident\\B with\\O my\\O purchase\\O :\\O Build\\O Quality\\O -\\O Seriously\\O ,\\O you\\O ca\\O n't\\O beat\\O a\\O unibody\\O construction\\O .\\O", "sentiment": "positive"}, {"uid": "275:7-1", "target_tags": "Here\\O are\\O the\\O things\\O that\\O made\\O me\\O confident\\O with\\O my\\O purchase\\O :\\O Build\\O Quality\\O -\\O Seriously\\O ,\\O you\\O ca\\O n't\\O beat\\O a\\O unibody\\B construction\\I .\\O", "opinion_tags": "Here\\O are\\O the\\O things\\O that\\O made\\O me\\O confident\\O with\\O my\\O purchase\\O :\\O Build\\O Quality\\O -\\O Seriously\\O ,\\O you\\O ca\\B n't\\I beat\\I a\\O unibody\\O construction\\O .\\O", "sentiment": "positive"}]}, {"id": "286:3", "sentence": "It provides much more flexibility for connectivity .", "postag": ["PRP", "VBZ", "RB", "JJR", "NN", "IN", "NN", "."], "head": [2, 0, 4, 5, 2, 7, 5, 2], "deprel": ["nsubj", "root", "advmod", "amod", "obj", "case", "nmod", "punct"], "triples": [{"uid": "286:3-0", "target_tags": "It\\O provides\\O much\\O more\\O flexibility\\B for\\I connectivity\\I .\\O", "opinion_tags": "It\\O provides\\O much\\O more\\B flexibility\\O for\\O connectivity\\O .\\O", "sentiment": "positive"}]}, {"id": "29:172", "sentence": "Mac tutorials do help .", "postag": ["NNP", "NNS", "VBP", "VB", "."], "head": [2, 4, 4, 0, 4], "deprel": ["compound", "nsubj", "aux", "root", "punct"], "triples": [{"uid": "29:172-0", "target_tags": "Mac\\B tutorials\\I do\\O help\\O .\\O", "opinion_tags": "Mac\\O tutorials\\O do\\O help\\B .\\O", "sentiment": "positive"}]}, {"id": "1063:397", "sentence": "The technical support was not helpful as well .", "postag": ["DT", "JJ", "NN", "VBD", "RB", "JJ", "RB", "RB", "."], "head": [3, 3, 6, 6, 6, 0, 6, 7, 6], "deprel": ["det", "amod", "nsubj", "cop", "advmod", "root", "advmod", "fixed", "punct"], "triples": [{"uid": "1063:397-0", "target_tags": "The\\O technical\\B support\\I was\\O not\\O helpful\\O as\\O well\\O .\\O", "opinion_tags": "The\\O technical\\O support\\O was\\O not\\B helpful\\I as\\O well\\O .\\O", "sentiment": "negative"}]}, {"id": "1063:373", "sentence": "I got the new adapter and there was no change .", "postag": ["PRP", "VBD", "DT", "JJ", "NN", "CC", "EX", "VBD", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 10, 8, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "cc", "expl", "conj", "det", "nsubj", "punct"], "triples": [{"uid": "1063:373-0", "target_tags": "I\\O got\\O the\\O new\\O adapter\\B and\\O there\\O was\\O no\\O change\\O .\\O", "opinion_tags": "I\\O got\\O the\\O new\\B adapter\\O and\\O there\\O was\\O no\\B change\\I .\\O", "sentiment": "neutral"}]}, {"id": "26:4", "sentence": "Logic board utterly fried , cried , and laid down and died .", "postag": ["NN", "NN", "RB", "VBD", ",", "VBD", ",", "CC", "VBD", "RP", "CC", "VBD", "."], "head": [2, 4, 4, 0, 6, 4, 9, 9, 4, 9, 12, 9, 4], "deprel": ["compound", "nsubj", "advmod", "root", "punct", "conj", "punct", "cc", "conj", "compound:prt", "cc", "conj", "punct"], "triples": [{"uid": "26:4-0", "target_tags": "Logic\\B board\\I utterly\\O fried\\O ,\\O cried\\O ,\\O and\\O laid\\O down\\O and\\O died\\O .\\O", "opinion_tags": "Logic\\O board\\O utterly\\B fried\\I ,\\O cried\\B ,\\O and\\O laid\\B down\\I and\\O died\\B .\\O", "sentiment": "positive"}]}, {"id": "1063:396", "sentence": "The sound was crappy even when you turn up the volume still the same results .", "postag": ["DT", "NN", "VBD", "JJ", "RB", "WRB", "PRP", "VBP", "RP", "DT", "NN", "RB", "DT", "JJ", "NNS", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 8, 11, 8, 15, 15, 15, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "advmod", "mark", "nsubj", "advcl", "compound:prt", "det", "obj", "advmod", "det", "amod", "parataxis", "punct"], "triples": [{"uid": "1063:396-0", "target_tags": "The\\O sound\\B was\\O crappy\\O even\\O when\\O you\\O turn\\O up\\O the\\O volume\\O still\\O the\\O same\\O results\\O .\\O", "opinion_tags": "The\\O sound\\O was\\O crappy\\B even\\O when\\O you\\O turn\\O up\\O the\\O volume\\O still\\O the\\O same\\O results\\O .\\O", "sentiment": "negative"}, {"uid": "1063:396-1", "target_tags": "The\\O sound\\O was\\O crappy\\O even\\O when\\O you\\O turn\\O up\\O the\\O volume\\B still\\O the\\O same\\O results\\O .\\O", "opinion_tags": "The\\O sound\\O was\\O crappy\\B even\\O when\\O you\\O turn\\O up\\O the\\O volume\\O still\\O the\\O same\\O results\\O .\\O", "sentiment": "negative"}]}, {"id": "271:4", "sentence": "OSX Lion is a great performer..extremely fast and reliable .", "postag": ["NNP", "NNP", "VBZ", "DT", "JJ", "ADD", "JJ", "CC", "JJ", "."], "head": [2, 7, 7, 7, 7, 7, 0, 9, 7, 7], "deprel": ["compound", "nsubj", "cop", "det", "amod", "compound", "root", "cc", "conj", "punct"], "triples": [{"uid": "271:4-0", "target_tags": "OSX\\B Lion\\I is\\O a\\O great\\O performer..extremely\\O fast\\O and\\O reliable\\O .\\O", "opinion_tags": "OSX\\O Lion\\O is\\O a\\O great\\B performer..extremely\\O fast\\B and\\O reliable\\B .\\O", "sentiment": "positive"}]}, {"id": "479:5", "sentence": "Having heard from friends and family about how reliable a Mac product is , I never expected to have an application crash within the first month , but I did .", "postag": ["VBG", "VBN", "IN", "NNS", "CC", "NN", "IN", "WRB", "JJ", "DT", "NNP", "NN", "VBZ", ",", "PRP", "RB", "VBD", "TO", "VB", "DT", "NN", "NN", "IN", "DT", "JJ", "NN", ",", "CC", "PRP", "VBD", "."], "head": [2, 17, 4, 2, 6, 4, 9, 9, 2, 12, 12, 9, 9, 17, 17, 17, 0, 19, 17, 22, 22, 19, 26, 26, 26, 19, 30, 30, 30, 17, 17], "deprel": ["aux", "advcl", "case", "obl", "cc", "conj", "mark", "mark", "advcl", "det", "compound", "nsubj", "cop", "punct", "nsubj", "advmod", "root", "mark", "xcomp", "det", "compound", "obj", "case", "det", "amod", "obl", "punct", "cc", "nsubj", "conj", "punct"], "triples": [{"uid": "479:5-0", "target_tags": "Having\\O heard\\O from\\O friends\\O and\\O family\\O about\\O how\\O reliable\\O a\\O Mac\\O product\\O is\\O ,\\O I\\O never\\O expected\\O to\\O have\\O an\\O application\\B crash\\O within\\O the\\O first\\O month\\O ,\\O but\\O I\\O did\\O .\\O", "opinion_tags": "Having\\O heard\\O from\\O friends\\O and\\O family\\O about\\O how\\O reliable\\O a\\O Mac\\O product\\O is\\O ,\\O I\\O never\\O expected\\O to\\O have\\O an\\O application\\O crash\\B within\\O the\\O first\\O month\\O ,\\O but\\O I\\O did\\O .\\O", "sentiment": "negative"}]}, {"id": "462:6", "sentence": "The Macbook pro 's physical form is wonderful .", "postag": ["DT", "NNP", "NNP", "POS", "JJ", "NN", "VBZ", "JJ", "."], "head": [3, 3, 6, 3, 6, 8, 8, 0, 8], "deprel": ["det", "compound", "nmod:poss", "case", "amod", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "462:6-0", "target_tags": "The\\O Macbook\\O pro\\O 's\\O physical\\B form\\I is\\O wonderful\\O .\\O", "opinion_tags": "The\\O Macbook\\O pro\\O 's\\O physical\\O form\\O is\\O wonderful\\B .\\O", "sentiment": "positive"}]}, {"id": "0:3", "sentence": "The <PERSON> 's body has n't changed since late 2010- and for a good reason .", "postag": ["DT", "NNP", "POS", "NN", "VBZ", "RB", "VBN", "IN", "JJ", "CD", "CC", "IN", "DT", "JJ", "NN", "."], "head": [2, 4, 2, 7, 7, 7, 0, 10, 10, 7, 15, 15, 15, 15, 10, 7], "deprel": ["det", "nmod:poss", "case", "nsubj", "aux", "advmod", "root", "case", "amod", "obl", "cc", "case", "det", "amod", "conj", "punct"], "triples": [{"uid": "0:3-0", "target_tags": "The\\O Mini\\O 's\\O body\\B has\\O n't\\O changed\\O since\\O late\\O 2010-\\O and\\O for\\O a\\O good\\O reason\\O .\\O", "opinion_tags": "The\\O Mini\\O 's\\O body\\O has\\B n't\\I changed\\I since\\O late\\O 2010-\\O and\\O for\\O a\\O good\\B reason\\O .\\O", "sentiment": "positive"}]}, {"id": "461:17", "sentence": "The unibody construction really does feel lot more solid than Apple 's previous laptops .", "postag": ["DT", "NN", "NN", "RB", "VBZ", "VB", "NN", "RBR", "JJ", "IN", "NNP", "POS", "JJ", "NNS", "."], "head": [3, 3, 6, 6, 6, 0, 8, 9, 6, 14, 14, 11, 14, 9, 6], "deprel": ["det", "compound", "nsubj", "advmod", "aux", "root", "obl:npmod", "advmod", "xcomp", "case", "nmod:poss", "case", "amod", "obl", "punct"], "triples": [{"uid": "461:17-0", "target_tags": "The\\O unibody\\B construction\\I really\\O does\\O feel\\O lot\\O more\\O solid\\O than\\O Apple\\O 's\\O previous\\O laptops\\O .\\O", "opinion_tags": "The\\O unibody\\O construction\\O really\\O does\\O feel\\O lot\\O more\\O solid\\B than\\O Apple\\O 's\\O previous\\O laptops\\O .\\O", "sentiment": "positive"}]}, {"id": "0:14", "sentence": "3D rendering slows it down considerably .", "postag": ["NN", "NN", "VBZ", "PRP", "RP", "RB", "."], "head": [2, 3, 0, 3, 3, 3, 3], "deprel": ["compound", "nsubj", "root", "obj", "compound:prt", "advmod", "punct"], "triples": [{"uid": "0:14-0", "target_tags": "3D\\B rendering\\I slows\\O it\\O down\\O considerably\\O .\\O", "opinion_tags": "3D\\O rendering\\O slows\\B it\\I down\\I considerably\\O .\\O", "sentiment": "negative"}]}, {"id": "420:1", "sentence": "fast , great screen , beautiful apps for a laptop ; priced at 1100 on the apple website ; amazon had it for 1098+ tax - plus i had a 10 % off coupon from amazon-cost me 998 plus tax- 1070- OTD !", "postag": ["JJ", ",", "JJ", "NN", ",", "JJ", "NNS", "IN", "DT", "NN", ",", "VBN", "IN", "CD", "IN", "DT", "NNP", "NN", ",", "NNP", "VBD", "PRP", "IN", "CD", "NN", ",", "CC", "PRP", "VBD", "DT", "CD", "NN", "JJ", "NN", "IN", "VBG", "PRP", "CD", "CC", "$", "CD", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 10, 10, 7, 12, 4, 14, 12, 18, 18, 18, 12, 21, 21, 4, 21, 25, 25, 21, 4, 29, 29, 21, 34, 32, 33, 34, 29, 36, 29, 36, 42, 40, 38, 40, 36, 4], "deprel": ["amod", "punct", "amod", "root", "punct", "amod", "conj", "case", "det", "nmod", "punct", "parataxis", "case", "obl", "case", "det", "compound", "obl", "punct", "nsubj", "parataxis", "obj", "case", "nummod", "obl", "punct", "cc", "nsubj", "conj", "det", "nummod", "obl:npmod", "amod", "obj", "mark", "advcl", "obj", "nummod", "cc", "conj", "nummod", "obj", "punct"], "triples": [{"uid": "420:1-0", "target_tags": "fast\\O ,\\O great\\O screen\\B ,\\O beautiful\\O apps\\O for\\O a\\O laptop\\O ;\\O priced\\O at\\O 1100\\O on\\O the\\O apple\\O website\\O ;\\O amazon\\O had\\O it\\O for\\O 1098+\\O tax\\O -\\O plus\\O i\\O had\\O a\\O 10\\O %\\O off\\O coupon\\O from\\O amazon-cost\\O me\\O 998\\O plus\\O tax-\\O 1070-\\O OTD\\O !\\O", "opinion_tags": "fast\\B ,\\O great\\B screen\\O ,\\O beautiful\\O apps\\O for\\O a\\O laptop\\O ;\\O priced\\O at\\O 1100\\O on\\O the\\O apple\\O website\\O ;\\O amazon\\O had\\O it\\O for\\O 1098+\\O tax\\O -\\O plus\\O i\\O had\\O a\\O 10\\O %\\O off\\O coupon\\O from\\O amazon-cost\\O me\\O 998\\O plus\\O tax-\\O 1070-\\O OTD\\O !\\O", "sentiment": "positive"}, {"uid": "420:1-1", "target_tags": "fast\\O ,\\O great\\O screen\\O ,\\O beautiful\\O apps\\B for\\O a\\O laptop\\O ;\\O priced\\O at\\O 1100\\O on\\O the\\O apple\\O website\\O ;\\O amazon\\O had\\O it\\O for\\O 1098+\\O tax\\O -\\O plus\\O i\\O had\\O a\\O 10\\O %\\O off\\O coupon\\O from\\O amazon-cost\\O me\\O 998\\O plus\\O tax-\\O 1070-\\O OTD\\O !\\O", "opinion_tags": "fast\\O ,\\O great\\O screen\\O ,\\O beautiful\\B apps\\O for\\O a\\O laptop\\O ;\\O priced\\O at\\O 1100\\O on\\O the\\O apple\\O website\\O ;\\O amazon\\O had\\O it\\O for\\O 1098+\\O tax\\O -\\O plus\\O i\\O had\\O a\\O 10\\O %\\O off\\O coupon\\O from\\O amazon-cost\\O me\\O 998\\O plus\\O tax-\\O 1070-\\O OTD\\O !\\O", "sentiment": "positive"}]}, {"id": "731:5", "sentence": "All the ports are much needed since this is my main computer .", "postag": ["PDT", "DT", "NNS", "VBP", "RB", "VBN", "IN", "DT", "VBZ", "PRP$", "JJ", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 12, 12, 12, 12, 12, 6, 6], "deprel": ["det:predet", "det", "nsubj:pass", "aux:pass", "advmod", "root", "mark", "nsubj", "cop", "nmod:poss", "amod", "advcl", "punct"], "triples": [{"uid": "731:5-0", "target_tags": "All\\O the\\O ports\\B are\\O much\\O needed\\O since\\O this\\O is\\O my\\O main\\O computer\\O .\\O", "opinion_tags": "All\\O the\\O ports\\O are\\O much\\B needed\\I since\\O this\\O is\\O my\\O main\\O computer\\O .\\O", "sentiment": "neutral"}]}, {"id": "267:1", "sentence": "The Like New condition of the iMac MC309LL/A on Amazon is at $ 900+ level only , and it is a Quad-Core 2.5 GHz CPU ( similar to the $ 799 Mini ) , with Radeon HD 6750M 512MB graphic card ( this mini is integrated Intel 4000 card ) , and it even comes with wireless Apple Keyboard and Mouse , all put together in neat and nice package .", "postag": ["DT", "JJ", "JJ", "NN", "IN", "DT", "NNP", "NNP", "IN", "NNP", "VBZ", "IN", "$", "CD", "NN", "RB", ",", "CC", "PRP", "VBZ", "DT", "NNP", "CD", "NN", "NN", "-LRB-", "JJ", "IN", "DT", "$", "CD", "NN", "-RRB-", ",", "IN", "NNP", "NNP", "CD", "NN", "JJ", "NN", "-LRB-", "DT", "NN", "VBZ", "VBN", "NNP", "CD", "NN", "-RRB-", ",", "CC", "PRP", "RB", "VBZ", "IN", "JJ", "NNP", "NNP", "CC", "NNP", ",", "DT", "VBN", "RB", "IN", "JJ", "CC", "JJ", "NN", "."], "head": [4, 4, 4, 15, 8, 8, 8, 4, 10, 8, 15, 15, 15, 13, 0, 15, 25, 25, 25, 25, 25, 25, 24, 25, 15, 27, 25, 32, 32, 32, 30, 27, 27, 25, 41, 37, 41, 37, 41, 41, 25, 46, 44, 46, 46, 41, 49, 49, 46, 46, 55, 55, 55, 55, 15, 59, 59, 59, 55, 61, 59, 64, 64, 59, 64, 70, 70, 69, 67, 64, 15], "deprel": ["det", "amod", "amod", "nsubj", "case", "det", "compound", "nmod", "case", "nmod", "cop", "case", "compound", "nummod", "root", "advmod", "punct", "cc", "nsubj", "cop", "det", "compound", "nummod", "compound", "conj", "punct", "amod", "case", "det", "compound", "nummod", "obl", "punct", "punct", "case", "compound", "compound", "nummod", "compound", "amod", "nmod", "punct", "det", "nsubj:pass", "aux:pass", "acl:relcl", "compound", "nummod", "obj", "punct", "punct", "cc", "nsubj", "advmod", "conj", "case", "amod", "compound", "obl", "cc", "conj", "punct", "nsubj", "acl", "advmod", "case", "amod", "cc", "conj", "obl", "punct"], "triples": [{"uid": "267:1-0", "target_tags": "The\\O Like\\O New\\O condition\\O of\\O the\\O iMac\\O MC309LL/A\\O on\\O Amazon\\O is\\O at\\O $\\O 900+\\O level\\O only\\O ,\\O and\\O it\\O is\\O a\\O Quad-Core\\O 2.5\\O GHz\\O CPU\\O (\\O similar\\O to\\O the\\O $\\O 799\\O Mini\\O )\\O ,\\O with\\O Radeon\\O HD\\O 6750M\\O 512MB\\O graphic\\O card\\O (\\O this\\O mini\\O is\\O integrated\\O Intel\\O 4000\\O card\\O )\\O ,\\O and\\O it\\O even\\O comes\\O with\\O wireless\\O Apple\\O Keyboard\\O and\\O Mouse\\O ,\\O all\\O put\\O together\\O in\\O neat\\O and\\O nice\\O package\\B .\\O", "opinion_tags": "The\\O Like\\O New\\O condition\\O of\\O the\\O iMac\\O MC309LL/A\\O on\\O Amazon\\O is\\O at\\O $\\O 900+\\O level\\O only\\O ,\\O and\\O it\\O is\\O a\\O Quad-Core\\O 2.5\\O GHz\\O CPU\\O (\\O similar\\O to\\O the\\O $\\O 799\\O Mini\\O )\\O ,\\O with\\O Radeon\\O HD\\O 6750M\\O 512MB\\O graphic\\O card\\O (\\O this\\O mini\\O is\\O integrated\\O Intel\\O 4000\\O card\\O )\\O ,\\O and\\O it\\O even\\O comes\\O with\\O wireless\\O Apple\\O Keyboard\\O and\\O Mouse\\O ,\\O all\\O put\\O together\\O in\\O neat\\B and\\O nice\\B package\\O .\\O", "sentiment": "positive"}]}, {"id": "419:2", "sentence": "Put a cover on it and is a little better but that is my only complaint .", "postag": ["VB", "DT", "NN", "IN", "PRP", "CC", "VBZ", "DT", "JJ", "JJR", "CC", "DT", "VBZ", "PRP$", "JJ", "NN", "."], "head": [0, 3, 1, 5, 1, 10, 10, 9, 10, 1, 16, 16, 16, 16, 16, 1, 1], "deprel": ["root", "det", "obj", "case", "obl", "cc", "cop", "det", "obl:npmod", "conj", "cc", "nsubj", "cop", "nmod:poss", "amod", "conj", "punct"], "triples": [{"uid": "419:2-0", "target_tags": "Put\\O a\\O cover\\B on\\O it\\O and\\O is\\O a\\O little\\O better\\O but\\O that\\O is\\O my\\O only\\O complaint\\O .\\O", "opinion_tags": "Put\\O a\\O cover\\O on\\O it\\O and\\O is\\O a\\B little\\I better\\I but\\O that\\O is\\O my\\O only\\O complaint\\B .\\O", "sentiment": "neutral"}]}, {"id": "291:6", "sentence": "Within a few hours I was using the gestures unconsciously .", "postag": ["IN", "DT", "JJ", "NNS", "PRP", "VBD", "VBG", "DT", "NNS", "RB", "."], "head": [4, 4, 4, 7, 7, 7, 0, 9, 7, 7, 7], "deprel": ["case", "det", "amod", "obl", "nsubj", "aux", "root", "det", "obj", "advmod", "punct"], "triples": [{"uid": "291:6-0", "target_tags": "Within\\O a\\O few\\O hours\\O I\\O was\\O using\\O the\\O gestures\\B unconsciously\\O .\\O", "opinion_tags": "Within\\O a\\O few\\O hours\\O I\\O was\\O using\\O the\\O gestures\\O unconsciously\\B .\\O", "sentiment": "positive"}]}, {"id": "786:633", "sentence": "This mac does come with an extender cable and I 'm using mine right now hoping the cable will stay nice for the many years I plan on using this mac .", "postag": ["DT", "NN", "VBZ", "VB", "IN", "DT", "NN", "NN", "CC", "PRP", "VBP", "VBG", "PRP", "RB", "RB", "VBG", "DT", "NN", "MD", "VB", "JJ", "IN", "DT", "JJ", "NNS", "PRP", "VBP", "IN", "VBG", "DT", "NN", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 12, 12, 12, 4, 12, 15, 12, 12, 18, 20, 20, 16, 20, 25, 25, 25, 20, 27, 25, 29, 27, 31, 29, 4], "deprel": ["det", "nsubj", "aux", "root", "case", "det", "compound", "obl", "cc", "nsubj", "aux", "conj", "obj", "advmod", "advmod", "advcl", "det", "nsubj", "aux", "ccomp", "xcomp", "case", "det", "amod", "obl", "nsubj", "acl:relcl", "mark", "advcl", "det", "obj", "punct"], "triples": [{"uid": "786:633-0", "target_tags": "This\\O mac\\O does\\O come\\O with\\O an\\O extender\\O cable\\O and\\O I\\O 'm\\O using\\O mine\\O right\\O now\\O hoping\\O the\\O cable\\B will\\O stay\\O nice\\O for\\O the\\O many\\O years\\O I\\O plan\\O on\\O using\\O this\\O mac\\O .\\O", "opinion_tags": "This\\O mac\\O does\\O come\\O with\\O an\\O extender\\O cable\\O and\\O I\\O 'm\\O using\\O mine\\O right\\O now\\O hoping\\O the\\O cable\\O will\\O stay\\O nice\\B for\\O the\\O many\\O years\\O I\\O plan\\O on\\O using\\O this\\O mac\\O .\\O", "sentiment": "positive"}]}, {"id": "293:3", "sentence": "The 2.9ghz dual-core i7 chip really out does itself .", "postag": ["DT", "NN", "JJ", "NNP", "NN", "RB", "RB", "VBZ", "PRP", "."], "head": [5, 5, 5, 5, 8, 7, 8, 0, 8, 8], "deprel": ["det", "compound", "amod", "compound", "nsubj", "advmod", "advmod", "root", "obj", "punct"], "triples": [{"uid": "293:3-0", "target_tags": "The\\O 2.9ghz\\B dual-core\\I i7\\I chip\\I really\\O out\\O does\\O itself\\O .\\O", "opinion_tags": "The\\O 2.9ghz\\O dual-core\\O i7\\O chip\\O really\\B out\\O does\\O itself\\O .\\O", "sentiment": "positive"}]}, {"id": "786:1277", "sentence": "It is pretty snappy and starts up in about 30 seconds which is good enough for me .", "postag": ["PRP", "VBZ", "RB", "JJ", "CC", "VBZ", "RP", "IN", "RB", "CD", "NNS", "WDT", "VBZ", "JJ", "JJ", "IN", "PRP", "."], "head": [4, 4, 4, 0, 6, 4, 6, 11, 10, 11, 6, 15, 15, 15, 11, 17, 15, 4], "deprel": ["nsubj", "cop", "advmod", "root", "cc", "conj", "compound:prt", "case", "advmod", "nummod", "obl", "nsubj", "cop", "amod", "acl:relcl", "case", "obl", "punct"], "triples": [{"uid": "786:1277-0", "target_tags": "It\\O is\\O pretty\\O snappy\\O and\\O starts\\B up\\I in\\O about\\O 30\\O seconds\\O which\\O is\\O good\\O enough\\O for\\O me\\O .\\O", "opinion_tags": "It\\O is\\O pretty\\O snappy\\B and\\O starts\\O up\\O in\\O about\\O 30\\O seconds\\O which\\O is\\O good\\B enough\\O for\\O me\\O .\\O", "sentiment": "positive"}]}, {"id": "1101:1", "sentence": "Not sure on Windows 8 .", "postag": ["RB", "JJ", "IN", "NNS", "CD", "."], "head": [2, 0, 4, 2, 4, 2], "deprel": ["advmod", "root", "case", "obl", "nummod", "punct"], "triples": [{"uid": "1101:1-0", "target_tags": "Not\\O sure\\O on\\O Windows\\B 8\\I .\\O", "opinion_tags": "Not\\B sure\\I on\\O Windows\\O 8\\O .\\O", "sentiment": "neutral"}]}, {"id": "71:1", "sentence": "My one complaint is that there was no internal CD drive .", "postag": ["PRP$", "CD", "NN", "VBZ", "IN", "EX", "VBD", "DT", "JJ", "NN", "NN", "."], "head": [3, 3, 4, 0, 7, 7, 4, 11, 11, 11, 7, 4], "deprel": ["nmod:poss", "nummod", "nsubj", "root", "mark", "expl", "ccomp", "det", "amod", "compound", "nsubj", "punct"], "triples": [{"uid": "71:1-0", "target_tags": "My\\O one\\O complaint\\O is\\O that\\O there\\O was\\O no\\O internal\\B CD\\I drive\\I .\\O", "opinion_tags": "My\\O one\\O complaint\\B is\\O that\\O there\\O was\\O no\\B internal\\O CD\\O drive\\O .\\O", "sentiment": "negative"}]}, {"id": "997:1", "sentence": "This newer netbook has no hard drive or network lights .", "postag": ["DT", "JJR", "NN", "VBZ", "DT", "JJ", "NN", "CC", "NN", "NNS", "."], "head": [3, 3, 4, 0, 7, 7, 4, 10, 10, 7, 4], "deprel": ["det", "amod", "nsubj", "root", "det", "amod", "obj", "cc", "compound", "conj", "punct"], "triples": [{"uid": "997:1-0", "target_tags": "This\\O newer\\O netbook\\O has\\O no\\O hard\\B drive\\I or\\O network\\O lights\\O .\\O", "opinion_tags": "This\\O newer\\O netbook\\O has\\O no\\B hard\\O drive\\O or\\O network\\O lights\\O .\\O", "sentiment": "neutral"}, {"uid": "997:1-1", "target_tags": "This\\O newer\\O netbook\\O has\\O no\\O hard\\O drive\\O or\\O network\\B lights\\I .\\O", "opinion_tags": "This\\O newer\\O netbook\\O has\\O no\\B hard\\O drive\\O or\\O network\\O lights\\O .\\O", "sentiment": "neutral"}]}, {"id": "98:1", "sentence": "Not too expense and has enough storage for most users and many ports .", "postag": ["RB", "RB", "NN", "CC", "VBZ", "JJ", "NN", "IN", "JJS", "NNS", "CC", "JJ", "NNS", "."], "head": [3, 3, 0, 5, 3, 7, 5, 10, 10, 7, 13, 13, 10, 3], "deprel": ["advmod", "advmod", "root", "cc", "conj", "amod", "obj", "case", "amod", "nmod", "cc", "amod", "conj", "punct"], "triples": [{"uid": "98:1-0", "target_tags": "Not\\O too\\O expense\\O and\\O has\\O enough\\O storage\\B for\\O most\\O users\\O and\\O many\\O ports\\O .\\O", "opinion_tags": "Not\\O too\\O expense\\O and\\O has\\O enough\\B storage\\O for\\O most\\O users\\O and\\O many\\O ports\\O .\\O", "sentiment": "positive"}, {"uid": "98:1-1", "target_tags": "Not\\O too\\O expense\\O and\\O has\\O enough\\O storage\\O for\\O most\\O users\\O and\\O many\\O ports\\B .\\O", "opinion_tags": "Not\\O too\\O expense\\O and\\O has\\O enough\\B storage\\O for\\O most\\O users\\O and\\O many\\O ports\\O .\\O", "sentiment": "positive"}]}, {"id": "786:1193", "sentence": "The audio volume is quite low and virtually unusable in a room with any background activity .", "postag": ["DT", "NN", "NN", "VBZ", "RB", "JJ", "CC", "RB", "JJ", "IN", "DT", "NN", "IN", "DT", "NN", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 9, 9, 6, 12, 12, 9, 16, 16, 16, 12, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "cc", "advmod", "conj", "case", "det", "obl", "case", "det", "compound", "nmod", "punct"], "triples": [{"uid": "786:1193-0", "target_tags": "The\\O audio\\B volume\\I is\\O quite\\O low\\O and\\O virtually\\O unusable\\O in\\O a\\O room\\O with\\O any\\O background\\O activity\\O .\\O", "opinion_tags": "The\\O audio\\O volume\\O is\\O quite\\O low\\B and\\O virtually\\O unusable\\B in\\O a\\O room\\O with\\O any\\O background\\O activity\\O .\\O", "sentiment": "negative"}]}, {"id": "389:1", "sentence": "It is lightweight and the perfect size to carry to class .", "postag": ["PRP", "VBZ", "JJ", "CC", "DT", "JJ", "NN", "TO", "VB", "IN", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 9, 7, 11, 9, 3], "deprel": ["nsubj", "cop", "root", "cc", "det", "amod", "conj", "mark", "acl", "case", "obl", "punct"], "triples": [{"uid": "389:1-0", "target_tags": "It\\O is\\O lightweight\\O and\\O the\\O perfect\\O size\\B to\\O carry\\O to\\O class\\O .\\O", "opinion_tags": "It\\O is\\O lightweight\\O and\\O the\\O perfect\\B size\\O to\\O carry\\O to\\O class\\O .\\O", "sentiment": "positive"}]}, {"id": "314:67", "sentence": "The MBP is beautiful has many wonderful capabilities .", "postag": ["DT", "NNP", "VBZ", "JJ", "VBZ", "JJ", "JJ", "NNS", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 5], "deprel": ["det", "nsubj", "aux", "advmod", "root", "amod", "amod", "obj", "punct"], "triples": [{"uid": "314:67-0", "target_tags": "The\\O MBP\\O is\\O beautiful\\O has\\O many\\O wonderful\\O capabilities\\B .\\O", "opinion_tags": "The\\O MBP\\O is\\O beautiful\\O has\\O many\\O wonderful\\B capabilities\\O .\\O", "sentiment": "positive"}]}, {"id": "339:4", "sentence": "I thought that it will be fine , if i do some settings .", "postag": ["PRP", "VBD", "IN", "PRP", "MD", "VB", "JJ", ",", "IN", "PRP", "VBP", "DT", "NNS", "."], "head": [2, 0, 7, 7, 7, 7, 2, 2, 11, 11, 7, 13, 11, 2], "deprel": ["nsubj", "root", "mark", "nsubj", "aux", "cop", "ccomp", "punct", "mark", "nsubj", "advcl", "det", "obj", "punct"], "triples": [{"uid": "339:4-0", "target_tags": "I\\O thought\\O that\\O it\\O will\\O be\\O fine\\O ,\\O if\\O i\\O do\\O some\\O settings\\B .\\O", "opinion_tags": "I\\O thought\\O that\\O it\\O will\\O be\\O fine\\B ,\\O if\\O i\\O do\\O some\\O settings\\O .\\O", "sentiment": "neutral"}]}, {"id": "349:1", "sentence": "Runs very smoothly .", "postag": ["VBZ", "RB", "RB", "."], "head": [0, 3, 1, 1], "deprel": ["root", "advmod", "advmod", "punct"], "triples": [{"uid": "349:1-0", "target_tags": "Runs\\B very\\O smoothly\\O .\\O", "opinion_tags": "Runs\\O very\\O smoothly\\B .\\O", "sentiment": "positive"}]}, {"id": "786:1355", "sentence": "Boot-up slowed significantly after all Windows updates were installed .", "postag": ["NNP", "VBD", "RB", "IN", "DT", "NNS", "NNS", "VBD", "VBN", "."], "head": [2, 0, 2, 9, 7, 7, 9, 9, 2, 2], "deprel": ["nsubj", "root", "advmod", "mark", "det", "compound", "nsubj:pass", "aux:pass", "advcl", "punct"], "triples": [{"uid": "786:1355-0", "target_tags": "Boot-up\\B slowed\\O significantly\\O after\\O all\\O Windows\\O updates\\O were\\O installed\\O .\\O", "opinion_tags": "Boot-up\\O slowed\\B significantly\\O after\\O all\\O Windows\\O updates\\O were\\O installed\\O .\\O", "sentiment": "negative"}, {"uid": "786:1355-1", "target_tags": "Boot-up\\O slowed\\O significantly\\O after\\O all\\O Windows\\B updates\\I were\\O installed\\O .\\O", "opinion_tags": "Boot-up\\O slowed\\B significantly\\O after\\O all\\O Windows\\O updates\\O were\\O installed\\O .\\O", "sentiment": "negative"}]}, {"id": "1056:1", "sentence": "It was important that it was powerful enough to do all of the tasks he needed on the internet , word processing , graphic design and gaming .", "postag": ["PRP", "VBD", "JJ", "IN", "PRP", "VBD", "JJ", "JJ", "TO", "VB", "DT", "IN", "DT", "NNS", "PRP", "VBD", "IN", "DT", "NN", ",", "NN", "NN", ",", "JJ", "NN", "CC", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 7, 10, 7, 10, 14, 14, 11, 16, 14, 19, 19, 16, 22, 19, 19, 25, 25, 19, 27, 19, 3], "deprel": ["expl", "cop", "root", "mark", "expl", "cop", "csubj", "advmod", "mark", "csubj", "obj", "case", "det", "nmod", "nsubj", "acl:relcl", "case", "det", "obl", "punct", "conj", "conj", "punct", "amod", "conj", "cc", "conj", "punct"], "triples": [{"uid": "1056:1-0", "target_tags": "It\\O was\\O important\\O that\\O it\\O was\\O powerful\\O enough\\O to\\O do\\O all\\O of\\O the\\O tasks\\O he\\O needed\\O on\\O the\\O internet\\B ,\\O word\\O processing\\O ,\\O graphic\\O design\\O and\\O gaming\\O .\\O", "opinion_tags": "It\\O was\\O important\\O that\\O it\\O was\\O powerful\\B enough\\O to\\O do\\O all\\O of\\O the\\O tasks\\O he\\O needed\\O on\\O the\\O internet\\O ,\\O word\\O processing\\O ,\\O graphic\\O design\\O and\\O gaming\\O .\\O", "sentiment": "positive"}, {"uid": "1056:1-1", "target_tags": "It\\O was\\O important\\O that\\O it\\O was\\O powerful\\O enough\\O to\\O do\\O all\\O of\\O the\\O tasks\\O he\\O needed\\O on\\O the\\O internet\\O ,\\O word\\B processing\\I ,\\O graphic\\O design\\O and\\O gaming\\O .\\O", "opinion_tags": "It\\O was\\O important\\O that\\O it\\O was\\O powerful\\B enough\\O to\\O do\\O all\\O of\\O the\\O tasks\\O he\\O needed\\O on\\O the\\O internet\\O ,\\O word\\O processing\\O ,\\O graphic\\O design\\O and\\O gaming\\O .\\O", "sentiment": "positive"}, {"uid": "1056:1-2", "target_tags": "It\\O was\\O important\\O that\\O it\\O was\\O powerful\\O enough\\O to\\O do\\O all\\O of\\O the\\O tasks\\O he\\O needed\\O on\\O the\\O internet\\O ,\\O word\\O processing\\O ,\\O graphic\\B design\\I and\\O gaming\\O .\\O", "opinion_tags": "It\\O was\\O important\\O that\\O it\\O was\\O powerful\\B enough\\O to\\O do\\O all\\O of\\O the\\O tasks\\O he\\O needed\\O on\\O the\\O internet\\O ,\\O word\\O processing\\O ,\\O graphic\\O design\\O and\\O gaming\\O .\\O", "sentiment": "positive"}, {"uid": "1056:1-3", "target_tags": "It\\O was\\O important\\O that\\O it\\O was\\O powerful\\O enough\\O to\\O do\\O all\\O of\\O the\\O tasks\\O he\\O needed\\O on\\O the\\O internet\\O ,\\O word\\O processing\\O ,\\O graphic\\O design\\O and\\O gaming\\B .\\O", "opinion_tags": "It\\O was\\O important\\O that\\O it\\O was\\O powerful\\B enough\\O to\\O do\\O all\\O of\\O the\\O tasks\\O he\\O needed\\O on\\O the\\O internet\\O ,\\O word\\O processing\\O ,\\O graphic\\O design\\O and\\O gaming\\O .\\O", "sentiment": "positive"}]}, {"id": "227:1", "sentence": "I like the Mini Mac it was easy to setup and install , but I am learning as I go and could use a tutorial to learn how to use some of the features I was use to on the PC especially the right mouse click menu .", "postag": ["PRP", "VBP", "DT", "NNP", "NNP", "PRP", "VBD", "JJ", "TO", "VB", "CC", "VB", ",", "CC", "PRP", "VBP", "VBG", "IN", "PRP", "VBP", "CC", "MD", "VB", "DT", "NN", "TO", "VB", "WRB", "TO", "VB", "DT", "IN", "DT", "NNS", "PRP", "VBD", "VB", "IN", "IN", "DT", "NN", "RB", "DT", "JJ", "NN", "NN", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 10, 8, 12, 10, 17, 17, 17, 17, 2, 20, 20, 17, 23, 23, 17, 25, 23, 27, 23, 30, 30, 27, 30, 34, 34, 31, 37, 37, 34, 37, 41, 41, 37, 47, 47, 47, 46, 47, 37, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "expl", "cop", "ccomp", "mark", "csubj", "cc", "conj", "punct", "cc", "nsubj", "aux", "conj", "mark", "nsubj", "advcl", "cc", "aux", "conj", "det", "obj", "mark", "advcl", "mark", "mark", "ccomp", "obj", "case", "det", "nmod", "nsubj", "aux", "acl:relcl", "obl", "case", "det", "obl", "advmod", "det", "amod", "compound", "compound", "obl", "punct"], "triples": [{"uid": "227:1-0", "target_tags": "I\\O like\\O the\\O Mini\\O Mac\\O it\\O was\\O easy\\O to\\O setup\\B and\\O install\\O ,\\O but\\O I\\O am\\O learning\\O as\\O I\\O go\\O and\\O could\\O use\\O a\\O tutorial\\O to\\O learn\\O how\\O to\\O use\\O some\\O of\\O the\\O features\\O I\\O was\\O use\\O to\\O on\\O the\\O PC\\O especially\\O the\\O right\\O mouse\\O click\\O menu\\O .\\O", "opinion_tags": "I\\O like\\B the\\O Mini\\O Mac\\O it\\O was\\O easy\\B to\\O setup\\O and\\O install\\O ,\\O but\\O I\\O am\\O learning\\O as\\O I\\O go\\O and\\O could\\O use\\O a\\O tutorial\\O to\\O learn\\O how\\O to\\O use\\O some\\O of\\O the\\O features\\O I\\O was\\O use\\O to\\O on\\O the\\O PC\\O especially\\O the\\O right\\O mouse\\O click\\O menu\\O .\\O", "sentiment": "positive"}, {"uid": "227:1-1", "target_tags": "I\\O like\\O the\\O Mini\\O Mac\\O it\\O was\\O easy\\O to\\O setup\\O and\\O install\\B ,\\O but\\O I\\O am\\O learning\\O as\\O I\\O go\\O and\\O could\\O use\\O a\\O tutorial\\O to\\O learn\\O how\\O to\\O use\\O some\\O of\\O the\\O features\\O I\\O was\\O use\\O to\\O on\\O the\\O PC\\O especially\\O the\\O right\\O mouse\\O click\\O menu\\O .\\O", "opinion_tags": "I\\O like\\B the\\O Mini\\O Mac\\O it\\O was\\O easy\\B to\\O setup\\O and\\O install\\O ,\\O but\\O I\\O am\\O learning\\O as\\O I\\O go\\O and\\O could\\O use\\O a\\O tutorial\\O to\\O learn\\O how\\O to\\O use\\O some\\O of\\O the\\O features\\O I\\O was\\O use\\O to\\O on\\O the\\O PC\\O especially\\O the\\O right\\O mouse\\O click\\O menu\\O .\\O", "sentiment": "positive"}]}, {"id": "378:1", "sentence": "Runs real quick .", "postag": ["VBZ", "RB", "JJ", "."], "head": [0, 3, 1, 1], "deprel": ["root", "advmod", "xcomp", "punct"], "triples": [{"uid": "378:1-0", "target_tags": "Runs\\B real\\O quick\\O .\\O", "opinion_tags": "Runs\\O real\\O quick\\B .\\O", "sentiment": "positive"}]}, {"id": "786:1170", "sentence": "Since the machine 's slim profile is critical to me , that was a problem .", "postag": ["IN", "DT", "NN", "POS", "JJ", "NN", "VBZ", "JJ", "IN", "PRP", ",", "DT", "VBD", "DT", "NN", "."], "head": [8, 3, 6, 3, 6, 8, 8, 15, 10, 8, 15, 15, 15, 15, 0, 15], "deprel": ["mark", "det", "nmod:poss", "case", "amod", "nsubj", "cop", "advcl", "case", "obl", "punct", "nsubj", "cop", "det", "root", "punct"], "triples": [{"uid": "786:1170-0", "target_tags": "Since\\O the\\O machine\\O 's\\O slim\\O profile\\B is\\O critical\\O to\\O me\\O ,\\O that\\O was\\O a\\O problem\\O .\\O", "opinion_tags": "Since\\O the\\O machine\\O 's\\O slim\\B profile\\O is\\O critical\\B to\\O me\\O ,\\O that\\O was\\O a\\O problem\\O .\\O", "sentiment": "negative"}]}, {"id": "1077:1", "sentence": "WiFi capability , disk drive and multiple USB ports to connect scale and printers was all that was required .", "postag": ["NN", "NN", ",", "NN", "NN", "CC", "JJ", "NN", "NNS", "TO", "VB", "NN", "CC", "NNS", "VBD", "DT", "WDT", "VBD", "VBN", "."], "head": [2, 16, 5, 5, 2, 9, 9, 9, 2, 11, 9, 11, 14, 12, 16, 0, 19, 19, 16, 16], "deprel": ["compound", "nsubj", "punct", "compound", "conj", "cc", "amod", "compound", "conj", "mark", "acl", "obj", "cc", "conj", "cop", "root", "nsubj:pass", "aux:pass", "acl:relcl", "punct"], "triples": [{"uid": "1077:1-0", "target_tags": "WiFi\\O capability\\O ,\\O disk\\B drive\\I and\\O multiple\\O USB\\O ports\\O to\\O connect\\O scale\\O and\\O printers\\O was\\O all\\O that\\O was\\O required\\O .\\O", "opinion_tags": "WiFi\\O capability\\O ,\\O disk\\O drive\\O and\\O multiple\\O USB\\O ports\\O to\\O connect\\O scale\\O and\\O printers\\O was\\O all\\O that\\O was\\O required\\B .\\O", "sentiment": "positive"}, {"uid": "1077:1-1", "target_tags": "WiFi\\O capability\\O ,\\O disk\\O drive\\O and\\O multiple\\O USB\\B ports\\I to\\O connect\\O scale\\O and\\O printers\\O was\\O all\\O that\\O was\\O required\\O .\\O", "opinion_tags": "WiFi\\O capability\\O ,\\O disk\\O drive\\O and\\O multiple\\O USB\\O ports\\O to\\O connect\\O scale\\O and\\O printers\\O was\\O all\\O that\\O was\\O required\\B .\\O", "sentiment": "positive"}, {"uid": "1077:1-2", "target_tags": "WiFi\\B capability\\I ,\\O disk\\O drive\\O and\\O multiple\\O USB\\O ports\\O to\\O connect\\O scale\\O and\\O printers\\O was\\O all\\O that\\O was\\O required\\O .\\O", "opinion_tags": "WiFi\\O capability\\O ,\\O disk\\O drive\\O and\\O multiple\\O USB\\O ports\\O to\\O connect\\O scale\\O and\\O printers\\O was\\O all\\O that\\O was\\O required\\B .\\O", "sentiment": "positive"}]}, {"id": "1053:13", "sentence": "The SD card reader is slightly recessed and upside down ( the nail slot on the card can not be accessed ) , if this was a self ejecting slot this would not be an issue , but its not .", "postag": ["DT", "NN", "NN", "NN", "VBZ", "RB", "VBN", "CC", "VBN", "RP", "-LRB-", "DT", "NN", "NN", "IN", "DT", "NN", "MD", "RB", "VB", "VBN", "-RRB-", ",", "IN", "DT", "VBD", "DT", "NN", "NN", "NN", "DT", "MD", "RB", "VB", "DT", "NN", ",", "CC", "PRP$", "RB", "."], "head": [4, 3, 4, 7, 7, 7, 0, 9, 7, 9, 21, 14, 14, 21, 17, 17, 14, 21, 21, 21, 7, 21, 7, 30, 30, 30, 30, 29, 30, 36, 36, 36, 36, 36, 36, 7, 40, 40, 40, 36, 7], "deprel": ["det", "compound", "compound", "nsubj:pass", "aux:pass", "advmod", "root", "cc", "conj", "compound:prt", "punct", "det", "compound", "nsubj:pass", "case", "det", "nmod", "aux", "advmod", "aux:pass", "parataxis", "punct", "punct", "mark", "nsubj", "cop", "det", "compound", "compound", "advcl", "nsubj", "aux", "advmod", "cop", "det", "conj", "punct", "cc", "nsubj", "conj", "punct"], "triples": [{"uid": "1053:13-0", "target_tags": "The\\O SD\\B card\\I reader\\I is\\O slightly\\O recessed\\O and\\O upside\\O down\\O (\\O the\\O nail\\O slot\\O on\\O the\\O card\\O can\\O not\\O be\\O accessed\\O )\\O ,\\O if\\O this\\O was\\O a\\O self\\O ejecting\\O slot\\O this\\O would\\O not\\O be\\O an\\O issue\\O ,\\O but\\O its\\O not\\O .\\O", "opinion_tags": "The\\O SD\\O card\\O reader\\O is\\O slightly\\O recessed\\B and\\O upside\\B down\\I (\\O the\\O nail\\O slot\\O on\\O the\\O card\\O can\\O not\\O be\\O accessed\\O )\\O ,\\O if\\O this\\O was\\O a\\O self\\O ejecting\\O slot\\O this\\O would\\O not\\O be\\O an\\O issue\\O ,\\O but\\O its\\O not\\O .\\O", "sentiment": "negative"}, {"uid": "1053:13-1", "target_tags": "The\\O SD\\O card\\O reader\\O is\\O slightly\\O recessed\\O and\\O upside\\O down\\O (\\O the\\O nail\\B slot\\I on\\I the\\I card\\I can\\O not\\O be\\O accessed\\O )\\O ,\\O if\\O this\\O was\\O a\\O self\\O ejecting\\O slot\\O this\\O would\\O not\\O be\\O an\\O issue\\O ,\\O but\\O its\\O not\\O .\\O", "opinion_tags": "The\\O SD\\O card\\O reader\\O is\\O slightly\\O recessed\\O and\\O upside\\O down\\O (\\O the\\O nail\\O slot\\O on\\O the\\O card\\O can\\B not\\I be\\I accessed\\I )\\O ,\\O if\\O this\\O was\\O a\\O self\\O ejecting\\O slot\\O this\\O would\\O not\\O be\\O an\\O issue\\O ,\\O but\\O its\\O not\\O .\\O", "sentiment": "negative"}, {"uid": "1053:13-2", "target_tags": "The\\O SD\\O card\\O reader\\O is\\O slightly\\O recessed\\O and\\O upside\\O down\\O (\\O the\\O nail\\O slot\\O on\\O the\\O card\\O can\\O not\\O be\\O accessed\\O )\\O ,\\O if\\O this\\O was\\O a\\O self\\O ejecting\\O slot\\B this\\O would\\O not\\O be\\O an\\O issue\\O ,\\O but\\O its\\O not\\O .\\O", "opinion_tags": "The\\O SD\\O card\\O reader\\O is\\O slightly\\O recessed\\O and\\O upside\\O down\\O (\\O the\\O nail\\O slot\\O on\\O the\\O card\\O can\\O not\\O be\\O accessed\\O )\\O ,\\O if\\O this\\O was\\O a\\O self\\O ejecting\\O slot\\O this\\O would\\O not\\O be\\O an\\O issue\\B ,\\O but\\O its\\O not\\O .\\O", "sentiment": "negative"}]}, {"id": "615:1", "sentence": "Soft touch , anodized aluminum with laser cut precision and no flaws .", "postag": ["JJ", "NN", ",", "VBN", "NN", "IN", "NN", "NN", "NN", "CC", "DT", "NNS", "."], "head": [2, 0, 5, 5, 2, 9, 8, 9, 5, 12, 12, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "case", "compound", "compound", "nmod", "cc", "det", "conj", "punct"], "triples": [{"uid": "615:1-0", "target_tags": "Soft\\O touch\\B ,\\O anodized\\O aluminum\\O with\\O laser\\O cut\\O precision\\O and\\O no\\O flaws\\O .\\O", "opinion_tags": "Soft\\B touch\\O ,\\O anodized\\O aluminum\\O with\\O laser\\O cut\\O precision\\O and\\O no\\O flaws\\O .\\O", "sentiment": "positive"}, {"uid": "615:1-1", "target_tags": "Soft\\O touch\\O ,\\O anodized\\B aluminum\\I with\\O laser\\O cut\\O precision\\O and\\O no\\O flaws\\O .\\O", "opinion_tags": "Soft\\O touch\\O ,\\O anodized\\O aluminum\\O with\\O laser\\O cut\\O precision\\B and\\O no\\B flaws\\I .\\O", "sentiment": "positive"}]}, {"id": "787:873", "sentence": "Simple details , crafted aluminium and real glass make this laptop blow away the other plastic ridden , bulky sticker filled laptops .", "postag": ["JJ", "NNS", ",", "VBN", "NN", "CC", "JJ", "NN", "VBP", "DT", "NN", "VB", "RP", "DT", "JJ", "JJ", "VBN", ",", "JJ", "NN", "VBN", "NNS", "."], "head": [2, 9, 5, 5, 2, 8, 8, 2, 0, 11, 9, 9, 12, 22, 22, 17, 22, 22, 22, 21, 22, 12, 9], "deprel": ["amod", "nsubj", "punct", "amod", "conj", "cc", "amod", "conj", "root", "det", "obj", "xcomp", "compound:prt", "det", "amod", "compound", "amod", "punct", "amod", "obl:npmod", "amod", "obj", "punct"], "triples": [{"uid": "787:873-0", "target_tags": "Simple\\O details\\O ,\\O crafted\\O aluminium\\B and\\O real\\O glass\\O make\\O this\\O laptop\\O blow\\O away\\O the\\O other\\O plastic\\O ridden\\O ,\\O bulky\\O sticker\\O filled\\O laptops\\O .\\O", "opinion_tags": "Simple\\O details\\O ,\\O crafted\\B aluminium\\O and\\O real\\O glass\\O make\\O this\\O laptop\\O blow\\O away\\O the\\O other\\O plastic\\O ridden\\O ,\\O bulky\\O sticker\\O filled\\O laptops\\O .\\O", "sentiment": "positive"}, {"uid": "787:873-1", "target_tags": "Simple\\O details\\O ,\\O crafted\\O aluminium\\O and\\O real\\O glass\\B make\\O this\\O laptop\\O blow\\O away\\O the\\O other\\O plastic\\O ridden\\O ,\\O bulky\\O sticker\\O filled\\O laptops\\O .\\O", "opinion_tags": "Simple\\O details\\O ,\\O crafted\\O aluminium\\O and\\O real\\O glass\\O make\\O this\\O laptop\\O blow\\B away\\I the\\O other\\O plastic\\O ridden\\O ,\\O bulky\\O sticker\\O filled\\O laptops\\O .\\O", "sentiment": "positive"}]}, {"id": "261:0", "sentence": "First of all yes this is a mac and it has that nice brushed aluminum .", "postag": ["RB", "IN", "DT", "UH", "DT", "VBZ", "DT", "NN", "CC", "PRP", "VBZ", "DT", "JJ", "VBN", "NN", "."], "head": [8, 3, 1, 8, 8, 8, 8, 0, 11, 11, 8, 15, 15, 15, 11, 8], "deprel": ["advmod", "case", "obl", "discourse", "nsubj", "cop", "det", "root", "cc", "nsubj", "conj", "det", "amod", "amod", "obj", "punct"], "triples": [{"uid": "261:0-0", "target_tags": "First\\O of\\O all\\O yes\\O this\\O is\\O a\\O mac\\O and\\O it\\O has\\O that\\O nice\\O brushed\\O aluminum\\B .\\O", "opinion_tags": "First\\O of\\O all\\O yes\\O this\\O is\\O a\\O mac\\O and\\O it\\O has\\O that\\O nice\\B brushed\\O aluminum\\O .\\O", "sentiment": "positive"}]}, {"id": "754:11", "sentence": "No HDMI port .", "postag": ["DT", "NN", "NN", "."], "head": [3, 3, 0, 3], "deprel": ["det", "compound", "root", "punct"], "triples": [{"uid": "754:11-0", "target_tags": "No\\O HDMI\\B port\\I .\\O", "opinion_tags": "No\\B HDMI\\O port\\O .\\O", "sentiment": "neutral"}]}, {"id": "266:25", "sentence": "Customization on mac is impossible .", "postag": ["NN", "IN", "NN", "VBZ", "JJ", "."], "head": [5, 3, 1, 5, 0, 5], "deprel": ["nsubj", "case", "nmod", "cop", "root", "punct"], "triples": [{"uid": "266:25-0", "target_tags": "Customization\\B on\\O mac\\O is\\O impossible\\O .\\O", "opinion_tags": "Customization\\O on\\O mac\\O is\\O impossible\\B .\\O", "sentiment": "negative"}]}, {"id": "275:12", "sentence": "Plus two finger clicking as a replacement for the right click button is surprisingly intuitive .", "postag": ["CC", "CD", "NN", "VBG", "IN", "DT", "NN", "IN", "DT", "JJ", "NN", "NN", "VBZ", "RB", "JJ", "."], "head": [15, 3, 15, 3, 7, 7, 4, 12, 12, 12, 12, 7, 15, 15, 0, 15], "deprel": ["cc", "nummod", "nsubj", "acl", "case", "det", "obl", "case", "det", "amod", "compound", "nmod", "cop", "advmod", "root", "punct"], "triples": [{"uid": "275:12-0", "target_tags": "Plus\\O two\\B finger\\I clicking\\I as\\O a\\O replacement\\O for\\O the\\O right\\O click\\O button\\O is\\O surprisingly\\O intuitive\\O .\\O", "opinion_tags": "Plus\\O two\\O finger\\O clicking\\O as\\O a\\O replacement\\O for\\O the\\O right\\O click\\O button\\O is\\O surprisingly\\B intuitive\\O .\\O", "sentiment": "positive"}, {"uid": "275:12-1", "target_tags": "Plus\\O two\\O finger\\O clicking\\O as\\O a\\O replacement\\O for\\O the\\O right\\B click\\I button\\I is\\O surprisingly\\O intuitive\\O .\\O", "opinion_tags": "Plus\\O two\\O finger\\O clicking\\O as\\O a\\O replacement\\O for\\O the\\O right\\O click\\O button\\O is\\O surprisingly\\O intuitive\\B .\\O", "sentiment": "neutral"}]}, {"id": "397:17", "sentence": "The SuperDrive is quiet .", "postag": ["DT", "NNP", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "397:17-0", "target_tags": "The\\O SuperDrive\\B is\\O quiet\\O .\\O", "opinion_tags": "The\\O SuperDrive\\O is\\O quiet\\B .\\O", "sentiment": "positive"}]}, {"id": "1064:1", "sentence": "The battery was completely dead , in fact it had grown about a quarter inch thick lump on the underside .", "postag": ["DT", "NN", "VBD", "RB", "JJ", ",", "IN", "NN", "PRP", "VBD", "VBN", "IN", "DT", "JJ", "NN", "JJ", "NN", "IN", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 11, 8, 11, 11, 11, 5, 17, 17, 15, 16, 17, 11, 20, 20, 17, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "case", "obl", "nsubj", "aux", "conj", "case", "det", "amod", "obl:npmod", "amod", "obl", "case", "det", "nmod", "punct"], "triples": [{"uid": "1064:1-0", "target_tags": "The\\O battery\\B was\\O completely\\O dead\\O ,\\O in\\O fact\\O it\\O had\\O grown\\O about\\O a\\O quarter\\O inch\\O thick\\O lump\\O on\\O the\\O underside\\O .\\O", "opinion_tags": "The\\O battery\\O was\\O completely\\O dead\\B ,\\O in\\O fact\\O it\\O had\\O grown\\O about\\O a\\O quarter\\O inch\\O thick\\O lump\\O on\\O the\\O underside\\O .\\O", "sentiment": "negative"}]}, {"id": "429:0", "sentence": "if yo like practicality this is the laptop for you .", "postag": ["IN", "PRP$", "IN", "NN", "DT", "VBZ", "DT", "NN", "IN", "PRP", "."], "head": [8, 4, 4, 8, 8, 8, 8, 0, 10, 8, 8], "deprel": ["mark", "nmod:poss", "case", "obl", "nsubj", "cop", "det", "root", "case", "nmod", "punct"], "triples": [{"uid": "429:0-0", "target_tags": "if\\O yo\\O like\\O practicality\\B this\\O is\\O the\\O laptop\\O for\\O you\\O .\\O", "opinion_tags": "if\\O yo\\O like\\B practicality\\O this\\O is\\O the\\O laptop\\O for\\O you\\O .\\O", "sentiment": "positive"}]}, {"id": "398:2", "sentence": "The OS is great .", "postag": ["DT", "NNP", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "398:2-0", "target_tags": "The\\O OS\\B is\\O great\\O .\\O", "opinion_tags": "The\\O OS\\O is\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "497:3", "sentence": "CONS : Price is a bit ridiculous , kinda heavy .", "postag": ["NNS", ":", "NN", "VBZ", "DT", "NN", "JJ", ",", "RB", "JJ", "."], "head": [0, 1, 7, 7, 6, 7, 1, 10, 10, 7, 1], "deprel": ["root", "punct", "nsubj", "cop", "det", "obl:npmod", "appos", "punct", "advmod", "conj", "punct"], "triples": [{"uid": "497:3-0", "target_tags": "CONS\\O :\\O Price\\B is\\O a\\O bit\\O ridiculous\\O ,\\O kinda\\O heavy\\O .\\O", "opinion_tags": "CONS\\O :\\O Price\\O is\\O a\\O bit\\O ridiculous\\B ,\\O kinda\\O heavy\\B .\\O", "sentiment": "negative"}]}, {"id": "961:1", "sentence": "Which it did not have , only 3 USB 2 ports .", "postag": ["WDT", "PRP", "VBD", "RB", "VB", ",", "RB", "CD", "NNP", "CD", "NNS", "."], "head": [5, 5, 5, 5, 11, 11, 8, 9, 11, 11, 0, 11], "deprel": ["obj", "nsubj", "aux", "advmod", "acl", "punct", "advmod", "nummod", "compound", "nummod", "root", "punct"], "triples": [{"uid": "961:1-0", "target_tags": "Which\\O it\\O did\\O not\\O have\\O ,\\O only\\O 3\\O USB\\B 2\\I ports\\I .\\O", "opinion_tags": "Which\\O it\\O did\\O not\\B have\\I ,\\O only\\O 3\\O USB\\O 2\\O ports\\O .\\O", "sentiment": "neutral"}]}, {"id": "279:2", "sentence": "No startup disk was not included but that may be my fault .", "postag": ["DT", "NN", "NN", "VBD", "RB", "VBN", "CC", "DT", "MD", "VB", "PRP$", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 12, 12, 12, 12, 12, 6, 6], "deprel": ["det", "compound", "nsubj:pass", "aux:pass", "advmod", "root", "cc", "nsubj", "aux", "cop", "nmod:poss", "conj", "punct"], "triples": [{"uid": "279:2-0", "target_tags": "No\\O startup\\B disk\\I was\\O not\\O included\\O but\\O that\\O may\\O be\\O my\\O fault\\O .\\O", "opinion_tags": "No\\B startup\\O disk\\O was\\O not\\B included\\I but\\O that\\O may\\O be\\O my\\O fault\\B .\\O", "sentiment": "neutral"}]}, {"id": "29:1175", "sentence": "There is no `` tools '' menu .", "postag": ["EX", "VBZ", "DT", "``", "NNS", "''", "NN", "."], "head": [2, 0, 7, 7, 7, 7, 2, 2], "deprel": ["expl", "root", "det", "punct", "compound", "punct", "nsubj", "punct"], "triples": [{"uid": "29:1175-0", "target_tags": "There\\O is\\O no\\O ``\\B tools\\I ''\\I menu\\I .\\O", "opinion_tags": "There\\O is\\O no\\B ``\\O tools\\O ''\\O menu\\O .\\O", "sentiment": "neutral"}]}, {"id": "716:1", "sentence": "It is very fast and has everything that I need except for a word program .", "postag": ["PRP", "VBZ", "RB", "JJ", "CC", "VBZ", "NN", "WDT", "PRP", "VBP", "IN", "IN", "DT", "NN", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 6, 10, 10, 7, 15, 15, 15, 15, 10, 4], "deprel": ["nsubj", "cop", "advmod", "root", "cc", "conj", "obj", "obj", "nsubj", "acl:relcl", "case", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "716:1-0", "target_tags": "It\\O is\\O very\\O fast\\O and\\O has\\O everything\\O that\\O I\\O need\\O except\\O for\\O a\\O word\\B program\\I .\\O", "opinion_tags": "It\\O is\\O very\\O fast\\O and\\O has\\O everything\\O that\\O I\\O need\\O except\\B for\\I a\\O word\\O program\\O .\\O", "sentiment": "negative"}]}, {"id": "217:1", "sentence": "Needs a CD/DVD drive and a bigger power switch .", "postag": ["VBZ", "DT", "NN", "NN", "CC", "DT", "JJR", "NN", "NN", "."], "head": [0, 4, 4, 1, 9, 9, 9, 9, 4, 1], "deprel": ["root", "det", "compound", "obj", "cc", "det", "amod", "compound", "conj", "punct"], "triples": [{"uid": "217:1-0", "target_tags": "Needs\\O a\\O CD/DVD\\O drive\\O and\\O a\\O bigger\\O power\\B switch\\I .\\O", "opinion_tags": "Needs\\O a\\O CD/DVD\\O drive\\O and\\O a\\O bigger\\B power\\O switch\\O .\\O", "sentiment": "negative"}]}, {"id": "840:1", "sentence": "My laptop with Windows 7 crashed and I did not want Windows 8 .", "postag": ["PRP$", "NN", "IN", "NNP", "CD", "VBD", "CC", "PRP", "VBD", "RB", "VB", "NNP", "CD", "."], "head": [2, 6, 4, 2, 4, 0, 11, 11, 11, 11, 6, 11, 12, 6], "deprel": ["nmod:poss", "nsubj", "case", "nmod", "nummod", "root", "cc", "nsubj", "aux", "advmod", "conj", "obj", "nummod", "punct"], "triples": [{"uid": "840:1-0", "target_tags": "My\\O laptop\\O with\\O Windows\\B 7\\I crashed\\O and\\O I\\O did\\O not\\O want\\O Windows\\O 8\\O .\\O", "opinion_tags": "My\\O laptop\\O with\\O Windows\\O 7\\O crashed\\B and\\O I\\O did\\O not\\O want\\O Windows\\O 8\\O .\\O", "sentiment": "negative"}, {"uid": "840:1-1", "target_tags": "My\\O laptop\\O with\\O Windows\\O 7\\O crashed\\O and\\O I\\O did\\O not\\O want\\O Windows\\B 8\\I .\\O", "opinion_tags": "My\\O laptop\\O with\\O Windows\\O 7\\O crashed\\O and\\O I\\O did\\O not\\B want\\I Windows\\O 8\\O .\\O", "sentiment": "negative"}]}, {"id": "121:1", "sentence": "Easy to install also small to leave anywhere at your bedroom also very easy to configure for ADSl cable or wifi .", "postag": ["JJ", "TO", "VB", "RB", "JJ", "TO", "VB", "RB", "IN", "PRP$", "NN", "RB", "RB", "JJ", "TO", "VB", "IN", "NN", "NN", "CC", "NN", "."], "head": [0, 3, 1, 5, 1, 7, 5, 7, 11, 11, 8, 14, 14, 1, 16, 14, 19, 19, 16, 21, 19, 1], "deprel": ["root", "mark", "csubj", "advmod", "conj", "mark", "xcomp", "advmod", "case", "nmod:poss", "obl", "advmod", "advmod", "conj", "mark", "xcomp", "case", "compound", "obl", "cc", "conj", "punct"], "triples": [{"uid": "121:1-0", "target_tags": "Easy\\O to\\O install\\B also\\O small\\O to\\O leave\\O anywhere\\O at\\O your\\O bedroom\\O also\\O very\\O easy\\O to\\O configure\\O for\\O ADSl\\O cable\\O or\\O wifi\\O .\\O", "opinion_tags": "Easy\\B to\\O install\\O also\\O small\\O to\\O leave\\O anywhere\\O at\\O your\\O bedroom\\O also\\O very\\O easy\\O to\\O configure\\O for\\O ADSl\\O cable\\O or\\O wifi\\O .\\O", "sentiment": "positive"}, {"uid": "121:1-1", "target_tags": "Easy\\O to\\O install\\O also\\O small\\O to\\O leave\\O anywhere\\O at\\O your\\O bedroom\\O also\\O very\\O easy\\O to\\O configure\\B for\\I ADSl\\I cable\\I or\\I wifi\\I .\\O", "opinion_tags": "Easy\\O to\\O install\\O also\\O small\\O to\\O leave\\O anywhere\\O at\\O your\\O bedroom\\O also\\O very\\O easy\\B to\\O configure\\O for\\O ADSl\\O cable\\O or\\O wifi\\O .\\O", "sentiment": "positive"}]}, {"id": "688:1", "sentence": "Nice packing .", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "688:1-0", "target_tags": "Nice\\O packing\\B .\\O", "opinion_tags": "Nice\\B packing\\O .\\O", "sentiment": "positive"}]}, {"id": "62:1", "sentence": "I switched to this because I wanted something different , even though I miss windows .", "postag": ["PRP", "VBD", "IN", "DT", "IN", "PRP", "VBD", "NN", "JJ", ",", "RB", "IN", "PRP", "VBP", "NNS", "."], "head": [2, 0, 4, 2, 7, 7, 2, 7, 8, 7, 14, 14, 14, 7, 14, 2], "deprel": ["nsubj", "root", "case", "obl", "mark", "nsubj", "advcl", "obj", "amod", "punct", "advmod", "mark", "nsubj", "advcl", "obj", "punct"], "triples": [{"uid": "62:1-0", "target_tags": "I\\O switched\\O to\\O this\\O because\\O I\\O wanted\\O something\\O different\\O ,\\O even\\O though\\O I\\O miss\\O windows\\B .\\O", "opinion_tags": "I\\O switched\\O to\\O this\\O because\\O I\\O wanted\\O something\\O different\\O ,\\O even\\O though\\O I\\O miss\\B windows\\O .\\O", "sentiment": "positive"}]}, {"id": "464:4", "sentence": "Apple no longer includes iDVD with the computer and furthermore , Apple does n't even offer it anymore !", "postag": ["NNP", "RB", "RBR", "VBZ", "NN", "IN", "DT", "NN", "CC", "RB", ",", "NNP", "VBZ", "RB", "RB", "VB", "PRP", "RB", "."], "head": [4, 3, 4, 0, 4, 8, 8, 5, 16, 16, 16, 16, 16, 16, 16, 4, 16, 16, 4], "deprel": ["nsubj", "advmod", "advmod", "root", "obj", "case", "det", "nmod", "cc", "advmod", "punct", "nsubj", "aux", "advmod", "advmod", "conj", "obj", "advmod", "punct"], "triples": [{"uid": "464:4-0", "target_tags": "Apple\\O no\\O longer\\O includes\\O iDVD\\B with\\O the\\O computer\\O and\\O furthermore\\O ,\\O Apple\\O does\\O n't\\O even\\O offer\\O it\\O anymore\\O !\\O", "opinion_tags": "Apple\\O no\\B longer\\I includes\\I iDVD\\O with\\O the\\O computer\\O and\\O furthermore\\O ,\\O Apple\\O does\\O n't\\O even\\O offer\\O it\\O anymore\\O !\\O", "sentiment": "negative"}]}, {"id": "991:1", "sentence": "I also wanted Windows 7 , which this one has .", "postag": ["PRP", "RB", "VBD", "NNP", "CD", ",", "WDT", "DT", "NN", "VBZ", "."], "head": [3, 3, 0, 3, 4, 4, 10, 9, 10, 4, 3], "deprel": ["nsubj", "advmod", "root", "obj", "nummod", "punct", "obj", "det", "nsubj", "acl:relcl", "punct"], "triples": [{"uid": "991:1-0", "target_tags": "I\\O also\\O wanted\\O Windows\\B 7\\I ,\\O which\\O this\\O one\\O has\\O .\\O", "opinion_tags": "I\\O also\\O wanted\\B Windows\\O 7\\O ,\\O which\\O this\\O one\\O has\\O .\\O", "sentiment": "positive"}]}, {"id": "941:1", "sentence": "At first , I feel a little bit uncomfortable in using the Mac system .", "postag": ["RB", "RBS", ",", "PRP", "VBP", "DT", "JJ", "NN", "JJ", "IN", "VBG", "DT", "NNP", "NN", "."], "head": [2, 5, 5, 5, 0, 8, 8, 9, 5, 11, 9, 14, 14, 11, 5], "deprel": ["case", "obl", "punct", "nsubj", "root", "det", "amod", "obl:npmod", "xcomp", "mark", "advcl", "det", "compound", "obj", "punct"], "triples": [{"uid": "941:1-0", "target_tags": "At\\O first\\O ,\\O I\\O feel\\O a\\O little\\O bit\\O uncomfortable\\O in\\O using\\O the\\O Mac\\B system\\I .\\O", "opinion_tags": "At\\O first\\O ,\\O I\\O feel\\O a\\O little\\O bit\\O uncomfortable\\B in\\O using\\O the\\O Mac\\O system\\O .\\O", "sentiment": "negative"}]}, {"id": "626:1", "sentence": "It just works out of the box and you have a lot of cool software included with the OS .", "postag": ["PRP", "RB", "VBZ", "IN", "IN", "DT", "NN", "CC", "PRP", "VBP", "DT", "NN", "IN", "JJ", "NN", "VBN", "IN", "DT", "NNP", "."], "head": [3, 3, 0, 7, 4, 7, 3, 10, 10, 3, 12, 10, 15, 15, 12, 15, 19, 19, 16, 3], "deprel": ["nsubj", "advmod", "root", "case", "fixed", "det", "obl", "cc", "nsubj", "conj", "det", "obj", "case", "amod", "nmod", "acl", "case", "det", "obl", "punct"], "triples": [{"uid": "626:1-0", "target_tags": "It\\O just\\O works\\B out\\O of\\O the\\O box\\O and\\O you\\O have\\O a\\O lot\\O of\\O cool\\O software\\O included\\O with\\O the\\O OS\\O .\\O", "opinion_tags": "It\\O just\\O works\\O out\\B of\\I the\\I box\\I and\\O you\\O have\\O a\\O lot\\O of\\O cool\\O software\\O included\\O with\\O the\\O OS\\O .\\O", "sentiment": "positive"}, {"uid": "626:1-1", "target_tags": "It\\O just\\O works\\O out\\O of\\O the\\O box\\O and\\O you\\O have\\O a\\O lot\\O of\\O cool\\O software\\B included\\O with\\O the\\O OS\\O .\\O", "opinion_tags": "It\\O just\\O works\\O out\\O of\\O the\\O box\\O and\\O you\\O have\\O a\\O lot\\O of\\O cool\\B software\\O included\\O with\\O the\\O OS\\O .\\O", "sentiment": "positive"}, {"uid": "626:1-2", "target_tags": "It\\O just\\O works\\O out\\O of\\O the\\O box\\O and\\O you\\O have\\O a\\O lot\\O of\\O cool\\O software\\O included\\O with\\O the\\O OS\\B .\\O", "opinion_tags": "It\\O just\\O works\\O out\\O of\\O the\\O box\\O and\\O you\\O have\\O a\\O lot\\O of\\O cool\\B software\\O included\\O with\\O the\\O OS\\O .\\O", "sentiment": "neutral"}]}, {"id": "525:1", "sentence": "its as advertised on here ... .. it works great and is not a waste of money !", "postag": ["PRP$", "IN", "VBN", "IN", "RB", ",", ",", "PRP", "VBZ", "JJ", "CC", "VBZ", "RB", "DT", "NN", "IN", "NN", "."], "head": [3, 3, 9, 5, 3, 9, 9, 9, 0, 9, 15, 15, 15, 15, 9, 17, 15, 9], "deprel": ["nsubj", "mark", "advcl", "case", "obl", "punct", "punct", "nsubj", "root", "xcomp", "cc", "cop", "advmod", "det", "conj", "case", "nmod", "punct"], "triples": [{"uid": "525:1-0", "target_tags": "its\\O as\\O advertised\\O on\\O here\\O ...\\O ..\\O it\\O works\\B great\\O and\\O is\\O not\\O a\\O waste\\O of\\O money\\O !\\O", "opinion_tags": "its\\O as\\O advertised\\O on\\O here\\O ...\\O ..\\O it\\O works\\O great\\B and\\O is\\O not\\O a\\O waste\\O of\\O money\\O !\\O", "sentiment": "positive"}]}, {"id": "692:1", "sentence": "Premium price for the OS more than anything else .", "postag": ["NN", "NN", "IN", "DT", "NNP", "JJR", "IN", "NN", "JJ", "."], "head": [2, 0, 5, 5, 2, 2, 8, 6, 8, 2], "deprel": ["compound", "root", "case", "det", "nmod", "advmod", "case", "obl", "amod", "punct"], "triples": [{"uid": "692:1-0", "target_tags": "Premium\\O price\\B for\\O the\\O OS\\O more\\O than\\O anything\\O else\\O .\\O", "opinion_tags": "Premium\\B price\\O for\\O the\\O OS\\O more\\O than\\O anything\\O else\\O .\\O", "sentiment": "positive"}, {"uid": "692:1-1", "target_tags": "Premium\\O price\\O for\\O the\\O OS\\B more\\O than\\O anything\\O else\\O .\\O", "opinion_tags": "Premium\\B price\\O for\\O the\\O OS\\O more\\O than\\O anything\\O else\\O .\\O", "sentiment": "neutral"}]}, {"id": "976:1", "sentence": "I was a little concerned about the touch pad based on reviews , but I 've found it fine to work with .", "postag": ["PRP", "VBD", "DT", "JJ", "JJ", "IN", "DT", "NN", "NN", "VBN", "IN", "NNS", ",", "CC", "PRP", "VBP", "VBN", "PRP", "JJ", "TO", "VB", "IN", "."], "head": [5, 5, 4, 5, 0, 9, 9, 9, 5, 12, 12, 5, 17, 17, 17, 17, 5, 17, 17, 21, 19, 21, 5], "deprel": ["nsubj", "cop", "det", "obl:npmod", "root", "case", "det", "compound", "obl", "case", "case", "obl", "punct", "cc", "nsubj", "aux", "conj", "obj", "xcomp", "mark", "ccomp", "obl", "punct"], "triples": [{"uid": "976:1-0", "target_tags": "I\\O was\\O a\\O little\\O concerned\\O about\\O the\\O touch\\B pad\\I based\\O on\\O reviews\\O ,\\O but\\O I\\O 've\\O found\\O it\\O fine\\O to\\O work\\O with\\O .\\O", "opinion_tags": "I\\O was\\O a\\O little\\O concerned\\B about\\O the\\O touch\\O pad\\O based\\O on\\O reviews\\O ,\\O but\\O I\\O 've\\O found\\O it\\O fine\\B to\\O work\\O with\\O .\\O", "sentiment": "positive"}]}, {"id": "479:6", "sentence": "However , the experience was great since the OS does not become unstable and the application will simply shutdown and reopen .", "postag": ["RB", ",", "DT", "NN", "VBD", "JJ", "IN", "DT", "NNP", "VBZ", "RB", "VB", "JJ", "CC", "DT", "NN", "MD", "RB", "VB", "CC", "VB", "."], "head": [6, 6, 4, 6, 6, 0, 12, 9, 12, 12, 12, 6, 12, 19, 16, 19, 19, 19, 6, 21, 19, 6], "deprel": ["advmod", "punct", "det", "nsubj", "cop", "root", "mark", "det", "nsubj", "aux", "advmod", "advcl", "xcomp", "cc", "det", "nsubj", "aux", "advmod", "conj", "cc", "conj", "punct"], "triples": [{"uid": "479:6-0", "target_tags": "However\\O ,\\O the\\O experience\\O was\\O great\\O since\\O the\\O OS\\B does\\O not\\O become\\O unstable\\O and\\O the\\O application\\O will\\O simply\\O shutdown\\O and\\O reopen\\O .\\O", "opinion_tags": "However\\O ,\\O the\\O experience\\O was\\O great\\B since\\O the\\O OS\\O does\\O not\\B become\\I unstable\\I and\\O the\\O application\\O will\\O simply\\O shutdown\\O and\\O reopen\\O .\\O", "sentiment": "positive"}]}, {"id": "1044:1", "sentence": "The battery is not as shown in the product photos .", "postag": ["DT", "NN", "VBZ", "RB", "IN", "VBN", "IN", "DT", "NN", "NNS", "."], "head": [2, 3, 0, 6, 6, 3, 10, 10, 10, 6, 3], "deprel": ["det", "nsubj", "root", "advmod", "mark", "advcl", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "1044:1-0", "target_tags": "The\\O battery\\B is\\O not\\O as\\O shown\\O in\\O the\\O product\\O photos\\O .\\O", "opinion_tags": "The\\O battery\\O is\\O not\\B as\\I shown\\I in\\O the\\O product\\O photos\\O .\\O", "sentiment": "negative"}]}, {"id": "367:1", "sentence": "Shipping was quick and product described was the product sent and so much more ...", "postag": ["NN", "VBD", "JJ", "CC", "NN", "VBN", "VBD", "DT", "NN", "VBN", "CC", "RB", "RB", "JJR", "."], "head": [3, 3, 0, 5, 6, 3, 9, 9, 3, 9, 14, 13, 14, 9, 3], "deprel": ["nsubj", "cop", "root", "cc", "nsubj", "conj", "cop", "det", "conj", "acl", "cc", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "367:1-0", "target_tags": "Shipping\\B was\\O quick\\O and\\O product\\O described\\O was\\O the\\O product\\O sent\\O and\\O so\\O much\\O more\\O ...\\O", "opinion_tags": "Shipping\\O was\\O quick\\B and\\O product\\O described\\O was\\O the\\O product\\O sent\\O and\\O so\\O much\\O more\\O ...\\O", "sentiment": "positive"}]}, {"id": "718:1", "sentence": "the retina display display make pictures i took years ago jaw dropping .", "postag": ["DT", "NN", "NN", "NN", "VBP", "NNS", "PRP", "VBD", "NNS", "IN", "NN", "VBG", "."], "head": [4, 3, 4, 5, 0, 5, 8, 6, 8, 11, 12, 8, 5], "deprel": ["det", "compound", "compound", "nsubj", "root", "obj", "nsubj", "acl:relcl", "obj", "case", "compound", "advcl", "punct"], "triples": [{"uid": "718:1-0", "target_tags": "the\\O retina\\B display\\I display\\I make\\O pictures\\O i\\O took\\O years\\O ago\\O jaw\\O dropping\\O .\\O", "opinion_tags": "the\\O retina\\O display\\O display\\O make\\O pictures\\O i\\O took\\O years\\O ago\\O jaw\\O dropping\\B .\\O", "sentiment": "positive"}]}, {"id": "109:1", "sentence": "The Mac Mini is probably the simplest example of compact computing out there .", "postag": ["DT", "NNP", "NNP", "VBZ", "RB", "DT", "JJS", "NN", "IN", "JJ", "NN", "RB", "RB", "."], "head": [3, 3, 8, 8, 8, 8, 8, 0, 11, 11, 8, 13, 8, 8], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "det", "amod", "root", "case", "amod", "nmod", "advmod", "advmod", "punct"], "triples": [{"uid": "109:1-0", "target_tags": "The\\O Mac\\O Mini\\O is\\O probably\\O the\\O simplest\\O example\\O of\\O compact\\B computing\\I out\\O there\\O .\\O", "opinion_tags": "The\\O Mac\\O Mini\\O is\\O probably\\O the\\O simplest\\B example\\O of\\O compact\\O computing\\O out\\O there\\O .\\O", "sentiment": "positive"}]}, {"id": "325:1", "sentence": "Additionally , there is barely a ventilation system in the computer , and even the simple activity of watching videos let alone playing steam games causes the laptop to get very very hot , and in fact impossible to keep on lap .", "postag": ["RB", ",", "EX", "VBZ", "RB", "DT", "NN", "NN", "IN", "DT", "NN", ",", "CC", "RB", "DT", "JJ", "NN", "IN", "VBG", "NNS", "VB", "RB", "VBG", "NN", "NNS", "VBZ", "DT", "NN", "TO", "VB", "RB", "RB", "JJ", ",", "CC", "IN", "NN", "JJ", "TO", "VB", "IN", "NN", "."], "head": [4, 1, 4, 0, 4, 8, 8, 4, 11, 11, 8, 26, 26, 17, 17, 17, 26, 19, 17, 19, 19, 23, 19, 25, 23, 4, 28, 26, 30, 26, 33, 33, 30, 38, 38, 37, 38, 26, 40, 38, 42, 40, 4], "deprel": ["advmod", "punct", "expl", "root", "advmod", "det", "compound", "nsubj", "case", "det", "nmod", "punct", "cc", "advmod", "det", "amod", "nsubj", "mark", "acl", "obj", "xcomp", "advmod", "advcl", "compound", "obj", "conj", "det", "obj", "mark", "xcomp", "advmod", "advmod", "xcomp", "punct", "cc", "case", "obl", "conj", "mark", "xcomp", "case", "obl", "punct"], "triples": [{"uid": "325:1-0", "target_tags": "Additionally\\O ,\\O there\\O is\\O barely\\O a\\O ventilation\\B system\\I in\\O the\\O computer\\O ,\\O and\\O even\\O the\\O simple\\O activity\\O of\\O watching\\O videos\\O let\\O alone\\O playing\\O steam\\O games\\O causes\\O the\\O laptop\\O to\\O get\\O very\\O very\\O hot\\O ,\\O and\\O in\\O fact\\O impossible\\O to\\O keep\\O on\\O lap\\O .\\O", "opinion_tags": "Additionally\\O ,\\O there\\O is\\O barely\\B a\\O ventilation\\O system\\O in\\O the\\O computer\\O ,\\O and\\O even\\O the\\O simple\\O activity\\O of\\O watching\\O videos\\O let\\O alone\\O playing\\O steam\\O games\\O causes\\O the\\O laptop\\O to\\O get\\O very\\O very\\O hot\\B ,\\O and\\O in\\O fact\\O impossible\\O to\\O keep\\O on\\O lap\\O .\\O", "sentiment": "negative"}, {"uid": "325:1-1", "target_tags": "Additionally\\O ,\\O there\\O is\\O barely\\O a\\O ventilation\\O system\\O in\\O the\\O computer\\O ,\\O and\\O even\\O the\\O simple\\O activity\\O of\\O watching\\B videos\\I let\\O alone\\O playing\\O steam\\O games\\O causes\\O the\\O laptop\\O to\\O get\\O very\\O very\\O hot\\O ,\\O and\\O in\\O fact\\O impossible\\O to\\O keep\\O on\\O lap\\O .\\O", "opinion_tags": "Additionally\\O ,\\O there\\O is\\O barely\\O a\\O ventilation\\O system\\O in\\O the\\O computer\\O ,\\O and\\O even\\O the\\O simple\\B activity\\O of\\O watching\\O videos\\O let\\O alone\\O playing\\O steam\\O games\\O causes\\O the\\O laptop\\O to\\O get\\O very\\O very\\O hot\\B ,\\O and\\O in\\O fact\\O impossible\\O to\\O keep\\O on\\O lap\\O .\\O", "sentiment": "neutral"}, {"uid": "325:1-2", "target_tags": "Additionally\\O ,\\O there\\O is\\O barely\\O a\\O ventilation\\O system\\O in\\O the\\O computer\\O ,\\O and\\O even\\O the\\O simple\\O activity\\O of\\O watching\\O videos\\O let\\O alone\\O playing\\B steam\\I games\\I causes\\O the\\O laptop\\O to\\O get\\O very\\O very\\O hot\\O ,\\O and\\O in\\O fact\\O impossible\\O to\\O keep\\O on\\O lap\\O .\\O", "opinion_tags": "Additionally\\O ,\\O there\\O is\\O barely\\O a\\O ventilation\\O system\\O in\\O the\\O computer\\O ,\\O and\\O even\\O the\\O simple\\O activity\\O of\\O watching\\O videos\\O let\\O alone\\O playing\\O steam\\O games\\O causes\\O the\\O laptop\\O to\\O get\\O very\\O very\\O hot\\B ,\\O and\\O in\\O fact\\O impossible\\O to\\O keep\\O on\\O lap\\O .\\O", "sentiment": "neutral"}]}, {"id": "965:1", "sentence": "Chatting with Acer support , I was advised the problem was corrupted operating system files .", "postag": ["VBG", "IN", "NNP", "NN", ",", "PRP", "VBD", "VBN", "DT", "NN", "VBD", "VBN", "NN", "NN", "NNS", "."], "head": [8, 4, 4, 1, 1, 8, 8, 0, 10, 15, 15, 15, 15, 15, 8, 8], "deprel": ["advcl", "case", "compound", "obl", "punct", "nsubj:pass", "aux:pass", "root", "det", "nsubj", "cop", "amod", "compound", "compound", "ccomp", "punct"], "triples": [{"uid": "965:1-0", "target_tags": "Chatting\\O with\\O Acer\\O support\\O ,\\O I\\O was\\O advised\\O the\\O problem\\O was\\O corrupted\\O operating\\B system\\I files\\I .\\O", "opinion_tags": "Chatting\\O with\\O Acer\\O support\\O ,\\O I\\O was\\O advised\\O the\\O problem\\O was\\O corrupted\\B operating\\O system\\O files\\O .\\O", "sentiment": "neutral"}]}, {"id": "50:1", "sentence": "It 's been a couple weeks since the purchase and I 'm struggle with finding the correct keys ( but that was expected ) .", "postag": ["PRP", "VBZ", "VBN", "DT", "NN", "NNS", "IN", "DT", "NN", "CC", "PRP", "VBP", "JJ", "IN", "VBG", "DT", "JJ", "NNS", "-LRB-", "CC", "DT", "VBD", "VBN", "-RRB-", "."], "head": [6, 6, 6, 5, 6, 0, 9, 9, 6, 13, 13, 13, 6, 15, 13, 18, 18, 15, 23, 23, 23, 23, 13, 23, 6], "deprel": ["nsubj", "cop", "cop", "det", "compound", "root", "case", "det", "nmod", "cc", "nsubj", "cop", "conj", "mark", "advcl", "det", "amod", "obj", "punct", "cc", "nsubj:pass", "aux:pass", "conj", "punct", "punct"], "triples": [{"uid": "50:1-0", "target_tags": "It\\O 's\\O been\\O a\\O couple\\O weeks\\O since\\O the\\O purchase\\O and\\O I\\O 'm\\O struggle\\O with\\O finding\\O the\\O correct\\O keys\\B (\\O but\\O that\\O was\\O expected\\O )\\O .\\O", "opinion_tags": "It\\O 's\\O been\\O a\\O couple\\O weeks\\O since\\O the\\O purchase\\O and\\O I\\O 'm\\O struggle\\B with\\O finding\\O the\\O correct\\O keys\\O (\\O but\\O that\\O was\\O expected\\O )\\O .\\O", "sentiment": "neutral"}]}, {"id": "326:3", "sentence": "Many people complain about the new OS , and it 's urgent for Apple to fix it asap !", "postag": ["JJ", "NNS", "VBP", "IN", "DT", "JJ", "NNP", ",", "CC", "PRP", "VBZ", "JJ", "IN", "NNP", "TO", "VB", "PRP", "RB", "."], "head": [2, 3, 0, 7, 7, 7, 3, 12, 12, 12, 12, 3, 16, 16, 16, 12, 16, 16, 3], "deprel": ["amod", "nsubj", "root", "case", "det", "amod", "obl", "punct", "cc", "expl", "cop", "conj", "mark", "nsubj", "mark", "csubj", "obj", "advmod", "punct"], "triples": [{"uid": "326:3-0", "target_tags": "Many\\O people\\O complain\\O about\\O the\\O new\\O OS\\B ,\\O and\\O it\\O 's\\O urgent\\O for\\O Apple\\O to\\O fix\\O it\\O asap\\O !\\O", "opinion_tags": "Many\\O people\\O complain\\B about\\O the\\O new\\O OS\\O ,\\O and\\O it\\O 's\\O urgent\\O for\\O Apple\\O to\\O fix\\O it\\O asap\\O !\\O", "sentiment": "negative"}]}, {"id": "786:237", "sentence": "Now that I have upgraded to Lion I am much happier about MAC OS and have just bought an iMac for office .", "postag": ["RB", "IN", "PRP", "VBP", "VBN", "IN", "NNP", "PRP", "VBP", "RB", "JJR", "IN", "NNP", "NNP", "CC", "VBP", "RB", "VBN", "DT", "NNP", "IN", "NN", "."], "head": [11, 5, 5, 5, 1, 7, 5, 11, 11, 11, 0, 14, 14, 11, 18, 18, 18, 11, 20, 18, 22, 18, 11], "deprel": ["advmod", "mark", "nsubj", "aux", "ccomp", "case", "obl", "nsubj", "cop", "advmod", "root", "case", "compound", "obl", "cc", "aux", "advmod", "conj", "det", "obj", "case", "obl", "punct"], "triples": [{"uid": "786:237-0", "target_tags": "Now\\O that\\O I\\O have\\O upgraded\\O to\\O Lion\\B I\\O am\\O much\\O happier\\O about\\O MAC\\O OS\\O and\\O have\\O just\\O bought\\O an\\O iMac\\O for\\O office\\O .\\O", "opinion_tags": "Now\\O that\\O I\\O have\\O upgraded\\B to\\O Lion\\O I\\O am\\O much\\O happier\\O about\\O MAC\\O OS\\O and\\O have\\O just\\O bought\\O an\\O iMac\\O for\\O office\\O .\\O", "sentiment": "positive"}, {"uid": "786:237-1", "target_tags": "Now\\O that\\O I\\O have\\O upgraded\\O to\\O Lion\\O I\\O am\\O much\\O happier\\O about\\O MAC\\B OS\\I and\\O have\\O just\\O bought\\O an\\O iMac\\O for\\O office\\O .\\O", "opinion_tags": "Now\\O that\\O I\\O have\\O upgraded\\O to\\O Lion\\O I\\O am\\O much\\O happier\\B about\\O MAC\\O OS\\O and\\O have\\O just\\O bought\\O an\\O iMac\\O for\\O office\\O .\\O", "sentiment": "positive"}]}, {"id": "786:179", "sentence": "User upgradeable RAM and HDD .", "postag": ["NN", "JJ", "NN", "CC", "NN", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["compound", "amod", "root", "cc", "conj", "punct"], "triples": [{"uid": "786:179-0", "target_tags": "User\\O upgradeable\\O RAM\\B and\\O HDD\\O .\\O", "opinion_tags": "User\\O upgradeable\\B RAM\\O and\\O HDD\\O .\\O", "sentiment": "positive"}, {"uid": "786:179-1", "target_tags": "User\\O upgradeable\\O RAM\\O and\\O HDD\\B .\\O", "opinion_tags": "User\\O upgradeable\\B RAM\\O and\\O HDD\\O .\\O", "sentiment": "positive"}]}, {"id": "786:331", "sentence": "But I wanted the Pro for the CD/DVD player .", "postag": ["CC", "PRP", "VBD", "DT", "NNP", "IN", "DT", "NNP", "NN", "."], "head": [3, 3, 0, 5, 3, 9, 9, 9, 5, 3], "deprel": ["cc", "nsubj", "root", "det", "obj", "case", "det", "compound", "nmod", "punct"], "triples": [{"uid": "786:331-0", "target_tags": "But\\O I\\O wanted\\O the\\O Pro\\O for\\O the\\O CD/DVD\\B player\\I .\\O", "opinion_tags": "But\\O I\\O wanted\\B the\\O Pro\\O for\\O the\\O CD/DVD\\O player\\O .\\O", "sentiment": "positive"}]}, {"id": "500:1", "sentence": "I was a little worry at first because I do n't have a lot of experience with os.x and windows has always been second nature to me after many years of using windows .", "postag": ["PRP", "VBD", "DT", "JJ", "NN", "RB", "RBS", "IN", "PRP", "VBP", "RB", "VB", "DT", "NN", "IN", "NN", "IN", "NN", "CC", "NNS", "VBZ", "RB", "VBN", "JJ", "NN", "IN", "PRP", "IN", "JJ", "NNS", "IN", "VBG", "NNS", "."], "head": [5, 5, 4, 5, 0, 7, 5, 12, 12, 12, 12, 5, 14, 12, 16, 14, 18, 16, 25, 25, 25, 25, 25, 25, 5, 27, 25, 30, 30, 25, 32, 30, 32, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "case", "obl", "mark", "nsubj", "aux", "advmod", "advcl", "det", "obj", "case", "nmod", "case", "nmod", "cc", "nsubj", "aux", "advmod", "cop", "amod", "conj", "case", "nmod", "case", "amod", "obl", "mark", "acl", "obj", "punct"], "triples": [{"uid": "500:1-0", "target_tags": "I\\O was\\O a\\O little\\O worry\\O at\\O first\\O because\\O I\\O do\\O n't\\O have\\O a\\O lot\\O of\\O experience\\O with\\O os.x\\B and\\O windows\\O has\\O always\\O been\\O second\\O nature\\O to\\O me\\O after\\O many\\O years\\O of\\O using\\O windows\\O .\\O", "opinion_tags": "I\\O was\\O a\\O little\\O worry\\B at\\O first\\O because\\O I\\O do\\O n't\\O have\\O a\\O lot\\O of\\O experience\\O with\\O os.x\\O and\\O windows\\O has\\O always\\O been\\O second\\O nature\\O to\\O me\\O after\\O many\\O years\\O of\\O using\\O windows\\O .\\O", "sentiment": "neutral"}]}, {"id": "574:1", "sentence": "With the softwares supporting the use of other OS makes it much better .", "postag": ["IN", "DT", "NNS", "VBG", "DT", "NN", "IN", "JJ", "NNP", "VBZ", "PRP", "RB", "JJR", "."], "head": [3, 3, 10, 3, 6, 4, 9, 9, 6, 0, 10, 13, 10, 10], "deprel": ["case", "det", "obl", "acl", "det", "obj", "case", "amod", "nmod", "root", "obj", "advmod", "xcomp", "punct"], "triples": [{"uid": "574:1-0", "target_tags": "With\\O the\\O softwares\\O supporting\\O the\\O use\\O of\\O other\\O OS\\B makes\\O it\\O much\\O better\\O .\\O", "opinion_tags": "With\\O the\\O softwares\\O supporting\\O the\\O use\\O of\\O other\\O OS\\O makes\\O it\\O much\\O better\\B .\\O", "sentiment": "neutral"}, {"uid": "574:1-1", "target_tags": "With\\O the\\O softwares\\B supporting\\O the\\O use\\O of\\O other\\O OS\\O makes\\O it\\O much\\O better\\O .\\O", "opinion_tags": "With\\O the\\O softwares\\O supporting\\O the\\O use\\O of\\O other\\O OS\\O makes\\O it\\O much\\O better\\B .\\O", "sentiment": "neutral"}]}, {"id": "305:3", "sentence": "I then upgraded to Mac OS X 10.8 `` Mountain Lion '' .", "postag": ["PRP", "RB", "VBD", "IN", "NNP", "NNP", "HYPH", "CD", "``", "NNP", "NNP", "''", "."], "head": [3, 3, 0, 6, 6, 3, 6, 11, 11, 11, 6, 11, 3], "deprel": ["nsubj", "advmod", "root", "case", "compound", "obl", "punct", "nummod", "punct", "compound", "appos", "punct", "punct"], "triples": [{"uid": "305:3-0", "target_tags": "I\\O then\\O upgraded\\O to\\O Mac\\B OS\\I X\\I 10.8\\I ``\\I Mountain\\I Lion\\I ''\\I .\\O", "opinion_tags": "I\\O then\\O upgraded\\B to\\O Mac\\O OS\\O X\\O 10.8\\O ``\\O Mountain\\O Lion\\O ''\\O .\\O", "sentiment": "neutral"}]}, {"id": "898:1", "sentence": "I was considering buying the Air , but in reality , this one has a more powerful performance and seems much more convenient , even though it 's about .20 inch thicker and 2 lbs heavier .", "postag": ["PRP", "VBD", "VBG", "VBG", "DT", "NNP", ",", "CC", "IN", "NN", ",", "DT", "NN", "VBZ", "DT", "RBR", "JJ", "NN", "CC", "VBZ", "RB", "RBR", "JJ", ",", "RB", "IN", "PRP", "VBZ", "RB", "CD", "NN", "JJR", "CC", "CD", "NNS", "JJR", "."], "head": [3, 3, 0, 3, 6, 4, 14, 14, 10, 14, 14, 13, 14, 3, 18, 17, 18, 14, 20, 14, 22, 23, 20, 14, 32, 32, 32, 32, 30, 31, 32, 14, 36, 35, 36, 32, 3], "deprel": ["nsubj", "aux", "root", "xcomp", "det", "obj", "punct", "cc", "case", "obl", "punct", "det", "nsubj", "conj", "det", "advmod", "amod", "obj", "cc", "conj", "advmod", "advmod", "xcomp", "punct", "advmod", "mark", "nsubj", "cop", "advmod", "nummod", "obl:npmod", "advcl", "cc", "nummod", "obl:npmod", "conj", "punct"], "triples": [{"uid": "898:1-0", "target_tags": "I\\O was\\O considering\\O buying\\O the\\O Air\\O ,\\O but\\O in\\O reality\\O ,\\O this\\O one\\O has\\O a\\O more\\O powerful\\O performance\\B and\\O seems\\O much\\O more\\O convenient\\O ,\\O even\\O though\\O it\\O 's\\O about\\O .20\\O inch\\O thicker\\O and\\O 2\\O lbs\\O heavier\\O .\\O", "opinion_tags": "I\\O was\\O considering\\O buying\\O the\\O Air\\O ,\\O but\\O in\\O reality\\O ,\\O this\\O one\\O has\\O a\\O more\\O powerful\\B performance\\O and\\O seems\\O much\\O more\\O convenient\\O ,\\O even\\O though\\O it\\O 's\\O about\\O .20\\O inch\\O thicker\\O and\\O 2\\O lbs\\O heavier\\O .\\O", "sentiment": "positive"}]}, {"id": "891:1", "sentence": "At home and the office it gets plugged into an external 24 '' LCD screen , so built in screen size is not terribly important .", "postag": ["IN", "NN", "CC", "DT", "NN", "PRP", "VBZ", "VBN", "IN", "DT", "JJ", "CD", "''", "NN", "NN", ",", "RB", "VBN", "IN", "NN", "NN", "VBZ", "RB", "RB", "JJ", "."], "head": [2, 25, 5, 5, 2, 8, 8, 25, 15, 15, 15, 15, 15, 15, 8, 25, 18, 25, 21, 21, 18, 25, 25, 25, 0, 25], "deprel": ["case", "obl", "cc", "det", "conj", "nsubj:pass", "aux:pass", "advcl", "case", "det", "amod", "compound", "punct", "compound", "obl", "punct", "advmod", "csubj", "case", "compound", "obl", "cop", "advmod", "advmod", "root", "punct"], "triples": [{"uid": "891:1-0", "target_tags": "At\\O home\\O and\\O the\\O office\\O it\\O gets\\O plugged\\O into\\O an\\O external\\O 24\\O ''\\O LCD\\O screen\\O ,\\O so\\O built\\B in\\I screen\\I size\\I is\\O not\\O terribly\\O important\\O .\\O", "opinion_tags": "At\\O home\\O and\\O the\\O office\\O it\\O gets\\O plugged\\O into\\O an\\O external\\O 24\\O ''\\O LCD\\O screen\\O ,\\O so\\O built\\O in\\O screen\\O size\\O is\\O not\\B terribly\\I important\\I .\\O", "sentiment": "neutral"}]}, {"id": "65:1", "sentence": "Just beware no DVD slot so when I went to install software I had on CD I could n't .", "postag": ["RB", "VB", "DT", "NN", "NN", "RB", "WRB", "PRP", "VBD", "TO", "VB", "NN", "PRP", "VBD", "IN", "NNP", "PRP", "MD", "RB", "."], "head": [2, 0, 5, 5, 2, 18, 9, 9, 18, 11, 9, 11, 14, 2, 16, 14, 18, 2, 18, 2], "deprel": ["advmod", "root", "det", "compound", "obj", "advmod", "mark", "nsubj", "advcl", "mark", "xcomp", "obj", "nsubj", "parataxis", "case", "obl", "nsubj", "parataxis", "advmod", "punct"], "triples": [{"uid": "65:1-0", "target_tags": "Just\\O beware\\O no\\O DVD\\B slot\\I so\\O when\\O I\\O went\\O to\\O install\\O software\\O I\\O had\\O on\\O CD\\O I\\O could\\O n't\\O .\\O", "opinion_tags": "Just\\O beware\\O no\\B DVD\\O slot\\O so\\O when\\O I\\O went\\O to\\O install\\O software\\O I\\O had\\O on\\O CD\\O I\\O could\\O n't\\O .\\O", "sentiment": "neutral"}]}, {"id": "245:1", "sentence": "No Cd Rom in the new version there 's no way I 'm spending that kind of money on something has less features than the older version .", "postag": ["DT", "NNP", "NNP", "IN", "DT", "JJ", "NN", "EX", "VBZ", "DT", "NN", "PRP", "VBP", "VBG", "DT", "NN", "IN", "NN", "IN", "NN", "VBZ", "JJR", "NNS", "IN", "DT", "JJR", "NN", "."], "head": [3, 3, 9, 7, 7, 7, 3, 9, 0, 11, 9, 14, 14, 11, 16, 14, 18, 16, 20, 14, 9, 23, 21, 27, 27, 27, 23, 9], "deprel": ["det", "compound", "nsubj", "case", "det", "amod", "nmod", "expl", "root", "det", "nsubj", "nsubj", "aux", "acl:relcl", "det", "obj", "case", "nmod", "case", "obl", "parataxis", "amod", "obj", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "245:1-0", "target_tags": "No\\O Cd\\B Rom\\I in\\O the\\O new\\O version\\O there\\O 's\\O no\\O way\\O I\\O 'm\\O spending\\O that\\O kind\\O of\\O money\\O on\\O something\\O has\\O less\\O features\\O than\\O the\\O older\\O version\\O .\\O", "opinion_tags": "No\\B Cd\\O Rom\\O in\\O the\\O new\\O version\\O there\\O 's\\O no\\O way\\O I\\O 'm\\O spending\\O that\\O kind\\O of\\O money\\O on\\O something\\O has\\O less\\O features\\O than\\O the\\O older\\O version\\O .\\O", "sentiment": "neutral"}, {"uid": "245:1-1", "target_tags": "No\\O Cd\\O Rom\\O in\\O the\\O new\\O version\\O there\\O 's\\O no\\O way\\O I\\O 'm\\O spending\\O that\\O kind\\O of\\O money\\O on\\O something\\O has\\O less\\O features\\B than\\O the\\O older\\O version\\O .\\O", "opinion_tags": "No\\O Cd\\O Rom\\O in\\O the\\O new\\O version\\O there\\O 's\\O no\\O way\\O I\\O 'm\\O spending\\O that\\O kind\\O of\\O money\\O on\\O something\\O has\\O less\\B features\\O than\\O the\\O older\\O version\\O .\\O", "sentiment": "negative"}]}, {"id": "1063:403", "sentence": "the volume is really low to low for a laptopwas not expectin t volume to be so lowan i hate that about this computer", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "IN", "RB", "IN", "DT", "NN", "RB", "JJ", "NN", "NN", "TO", "VB", "RB", "JJ", "PRP", "VBP", "DT", "IN", "DT", "NN"], "head": [2, 5, 5, 5, 0, 7, 5, 10, 10, 5, 12, 14, 14, 10, 18, 18, 18, 14, 20, 5, 20, 24, 24, 20], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "obl", "case", "det", "obl", "advmod", "amod", "compound", "conj", "mark", "cop", "advmod", "acl", "nsubj", "parataxis", "obj", "case", "det", "obl"], "triples": [{"uid": "1063:403-0", "target_tags": "the\\O volume\\B is\\O really\\O low\\O to\\O low\\O for\\O a\\O laptopwas\\O not\\O expectin\\O t\\O volume\\O to\\O be\\O so\\O lowan\\O i\\O hate\\O that\\O about\\O this\\O computer\\O", "opinion_tags": "the\\O volume\\O is\\O really\\O low\\B to\\O low\\O for\\O a\\O laptopwas\\O not\\O expectin\\O t\\O volume\\O to\\O be\\O so\\O lowan\\O i\\O hate\\B that\\O about\\O this\\O computer\\O", "sentiment": "negative"}, {"uid": "1063:403-1", "target_tags": "the\\O volume\\B is\\O really\\O low\\O to\\O low\\O for\\O a\\O laptopwas\\O not\\O expectin\\O t\\O volume\\O to\\O be\\O so\\O lowan\\O i\\O hate\\O that\\O about\\O this\\O computer\\O", "opinion_tags": "the\\O volume\\O is\\O really\\O low\\B to\\O low\\O for\\O a\\O laptopwas\\O not\\O expectin\\O t\\O volume\\O to\\O be\\O so\\O lowan\\O i\\O hate\\B that\\O about\\O this\\O computer\\O", "sentiment": "negative"}]}, {"id": "786:842", "sentence": "and its not hard to accidentally bang it against something so i recommend getting a case to protect it .", "postag": ["CC", "PRP$", "RB", "JJ", "TO", "RB", "VB", "PRP", "IN", "NN", "RB", "PRP", "VBP", "VBG", "DT", "NN", "TO", "VB", "PRP", "."], "head": [13, 4, 4, 0, 7, 7, 4, 7, 10, 7, 13, 13, 4, 13, 16, 14, 18, 16, 18, 4], "deprel": ["cc", "nsubj", "advmod", "root", "mark", "advmod", "csubj", "obj", "case", "obl", "advmod", "nsubj", "parataxis", "xcomp", "det", "obj", "mark", "acl", "obj", "punct"], "triples": [{"uid": "786:842-0", "target_tags": "and\\O its\\O not\\O hard\\O to\\O accidentally\\O bang\\O it\\O against\\O something\\O so\\O i\\O recommend\\O getting\\O a\\O case\\B to\\O protect\\O it\\O .\\O", "opinion_tags": "and\\O its\\O not\\O hard\\O to\\O accidentally\\O bang\\O it\\O against\\O something\\O so\\O i\\O recommend\\B getting\\O a\\O case\\O to\\O protect\\O it\\O .\\O", "sentiment": "neutral"}]}, {"id": "285:1", "sentence": "I got this at an amazing price from Amazon and it arrived just in time .", "postag": ["PRP", "VBD", "DT", "IN", "DT", "JJ", "NN", "IN", "NNP", "CC", "PRP", "VBD", "RB", "IN", "NN", "."], "head": [2, 0, 2, 7, 7, 7, 2, 9, 7, 12, 12, 2, 15, 15, 12, 2], "deprel": ["nsubj", "root", "obj", "case", "det", "amod", "obl", "case", "nmod", "cc", "nsubj", "conj", "advmod", "case", "obl", "punct"], "triples": [{"uid": "285:1-0", "target_tags": "I\\O got\\O this\\O at\\O an\\O amazing\\O price\\B from\\O Amazon\\O and\\O it\\O arrived\\O just\\O in\\O time\\O .\\O", "opinion_tags": "I\\O got\\O this\\O at\\O an\\O amazing\\B price\\O from\\O Amazon\\O and\\O it\\O arrived\\O just\\O in\\O time\\O .\\O", "sentiment": "positive"}]}, {"id": "43:1", "sentence": "Every time I log into the system after a few hours , there is this endlessly frustrating process that I have to go through .", "postag": ["DT", "NN", "PRP", "VBP", "IN", "DT", "NN", "IN", "DT", "JJ", "NNS", ",", "EX", "VBZ", "DT", "RB", "JJ", "NN", "WDT", "PRP", "VBP", "TO", "VB", "IN", "."], "head": [2, 14, 4, 2, 7, 7, 4, 11, 11, 11, 4, 14, 14, 0, 18, 17, 18, 14, 21, 21, 18, 23, 21, 19, 14], "deprel": ["det", "obl:tmod", "nsubj", "acl:relcl", "case", "det", "obl", "case", "det", "amod", "obl", "punct", "expl", "root", "det", "advmod", "amod", "nsubj", "obj", "nsubj", "acl:relcl", "mark", "xcomp", "case", "punct"], "triples": [{"uid": "43:1-0", "target_tags": "Every\\O time\\O I\\O log\\B into\\I the\\I system\\I after\\O a\\O few\\O hours\\O ,\\O there\\O is\\O this\\O endlessly\\O frustrating\\O process\\O that\\O I\\O have\\O to\\O go\\O through\\O .\\O", "opinion_tags": "Every\\O time\\O I\\O log\\O into\\O the\\O system\\O after\\O a\\O few\\O hours\\O ,\\O there\\O is\\O this\\O endlessly\\O frustrating\\B process\\O that\\O I\\O have\\O to\\O go\\O through\\O .\\O", "sentiment": "negative"}]}, {"id": "123:1", "sentence": "Put a SSD and use a 21 '' LED screen , this set up is silky smooth !", "postag": ["VB", "DT", "NN", "CC", "VB", "DT", "CD", "''", "NN", "NN", ",", "DT", "VBN", "RP", "VBZ", "JJ", "JJ", "."], "head": [0, 3, 1, 5, 1, 10, 9, 9, 10, 5, 1, 13, 17, 13, 17, 17, 1, 1], "deprel": ["root", "det", "obj", "cc", "conj", "det", "nummod", "punct", "compound", "obj", "punct", "det", "nsubj", "compound:prt", "cop", "amod", "parataxis", "punct"], "triples": [{"uid": "123:1-0", "target_tags": "Put\\O a\\O SSD\\O and\\O use\\O a\\O 21\\O ''\\O LED\\O screen\\O ,\\O this\\O set\\B up\\I is\\O silky\\O smooth\\O !\\O", "opinion_tags": "Put\\O a\\O SSD\\O and\\O use\\O a\\O 21\\O ''\\O LED\\O screen\\O ,\\O this\\O set\\O up\\O is\\O silky\\B smooth\\I !\\O", "sentiment": "positive"}]}, {"id": "11:1", "sentence": "The case is now slightly larger than the previous generation , but the lack of an external power supply justifies the small increase in size .", "postag": ["DT", "NN", "VBZ", "RB", "RB", "JJR", "IN", "DT", "JJ", "NN", ",", "CC", "DT", "NN", "IN", "DT", "JJ", "NN", "NN", "VBZ", "DT", "JJ", "NN", "IN", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 10, 10, 10, 6, 20, 20, 14, 20, 19, 19, 19, 19, 14, 6, 23, 23, 20, 25, 23, 6], "deprel": ["det", "nsubj", "cop", "advmod", "advmod", "root", "case", "det", "amod", "obl", "punct", "cc", "det", "nsubj", "case", "det", "amod", "compound", "nmod", "conj", "det", "amod", "obj", "case", "nmod", "punct"], "triples": [{"uid": "11:1-0", "target_tags": "The\\O case\\B is\\O now\\O slightly\\O larger\\O than\\O the\\O previous\\O generation\\O ,\\O but\\O the\\O lack\\O of\\O an\\O external\\O power\\O supply\\O justifies\\O the\\O small\\O increase\\O in\\O size\\O .\\O", "opinion_tags": "The\\O case\\O is\\O now\\O slightly\\O larger\\B than\\O the\\O previous\\O generation\\O ,\\O but\\O the\\O lack\\O of\\O an\\O external\\O power\\O supply\\O justifies\\O the\\O small\\O increase\\O in\\O size\\O .\\O", "sentiment": "negative"}, {"uid": "11:1-2", "target_tags": "The\\O case\\O is\\O now\\O slightly\\O larger\\O than\\O the\\O previous\\O generation\\O ,\\O but\\O the\\O lack\\O of\\O an\\O external\\B power\\I supply\\I justifies\\O the\\O small\\O increase\\O in\\O size\\O .\\O", "opinion_tags": "The\\O case\\O is\\O now\\O slightly\\O larger\\O than\\O the\\O previous\\O generation\\O ,\\O but\\O the\\O lack\\B of\\O an\\O external\\O power\\O supply\\O justifies\\O the\\O small\\O increase\\O in\\O size\\O .\\O", "sentiment": "neutral"}]}, {"id": "1009:1", "sentence": "I had to buy a wireless mouse to go with it , as I am old school and hate the pad , but knew that before I bought it , now it works great , need to get adjusted to the key board , as I am used to a bigger one and pounding .", "postag": ["PRP", "VBD", "TO", "VB", "DT", "JJ", "NN", "TO", "VB", "IN", "PRP", ",", "IN", "PRP", "VBP", "JJ", "NN", "CC", "VB", "DT", "NN", ",", "CC", "VBD", "IN", "IN", "PRP", "VBD", "PRP", ",", "RB", "PRP", "VBZ", "JJ", ",", "VB", "TO", "VB", "VBN", "IN", "DT", "JJ", "NN", ",", "IN", "PRP", "VBP", "VBN", "IN", "DT", "JJR", "NN", "CC", "NN", "."], "head": [2, 0, 4, 2, 7, 7, 4, 9, 4, 11, 9, 17, 17, 17, 17, 17, 4, 19, 4, 21, 19, 24, 24, 2, 33, 28, 28, 33, 28, 33, 33, 33, 24, 33, 36, 33, 39, 39, 36, 43, 43, 43, 39, 36, 48, 48, 48, 39, 52, 52, 52, 48, 54, 52, 2], "deprel": ["nsubj", "root", "mark", "xcomp", "det", "amod", "obj", "mark", "advcl", "case", "obl", "punct", "mark", "nsubj", "cop", "amod", "advcl", "cc", "conj", "det", "obj", "punct", "cc", "conj", "mark", "mark", "nsubj", "advcl", "obj", "punct", "advmod", "nsubj", "ccomp", "xcomp", "punct", "conj", "mark", "aux:pass", "xcomp", "case", "det", "amod", "obl", "punct", "mark", "nsubj:pass", "aux:pass", "advcl", "case", "det", "amod", "obl", "cc", "conj", "punct"], "triples": [{"uid": "1009:1-0", "target_tags": "I\\O had\\O to\\O buy\\O a\\O wireless\\O mouse\\O to\\O go\\O with\\O it\\O ,\\O as\\O I\\O am\\O old\\O school\\O and\\O hate\\O the\\O pad\\B ,\\O but\\O knew\\O that\\O before\\O I\\O bought\\O it\\O ,\\O now\\O it\\O works\\O great\\O ,\\O need\\O to\\O get\\O adjusted\\O to\\O the\\O key\\O board\\O ,\\O as\\O I\\O am\\O used\\O to\\O a\\O bigger\\O one\\O and\\O pounding\\O .\\O", "opinion_tags": "I\\O had\\O to\\O buy\\O a\\O wireless\\O mouse\\O to\\O go\\O with\\O it\\O ,\\O as\\O I\\O am\\O old\\O school\\O and\\O hate\\B the\\O pad\\O ,\\O but\\O knew\\O that\\O before\\O I\\O bought\\O it\\O ,\\O now\\O it\\O works\\O great\\O ,\\O need\\O to\\O get\\O adjusted\\O to\\O the\\O key\\O board\\O ,\\O as\\O I\\O am\\O used\\O to\\O a\\O bigger\\O one\\O and\\O pounding\\O .\\O", "sentiment": "negative"}, {"uid": "1009:1-1", "target_tags": "I\\O had\\O to\\O buy\\O a\\O wireless\\O mouse\\O to\\O go\\O with\\O it\\O ,\\O as\\O I\\O am\\O old\\O school\\O and\\O hate\\O the\\O pad\\O ,\\O but\\O knew\\O that\\O before\\O I\\O bought\\O it\\O ,\\O now\\O it\\O works\\B great\\O ,\\O need\\O to\\O get\\O adjusted\\O to\\O the\\O key\\O board\\O ,\\O as\\O I\\O am\\O used\\O to\\O a\\O bigger\\O one\\O and\\O pounding\\O .\\O", "opinion_tags": "I\\O had\\O to\\O buy\\O a\\O wireless\\O mouse\\O to\\O go\\O with\\O it\\O ,\\O as\\O I\\O am\\O old\\O school\\O and\\O hate\\O the\\O pad\\O ,\\O but\\O knew\\O that\\O before\\O I\\O bought\\O it\\O ,\\O now\\O it\\O works\\O great\\B ,\\O need\\O to\\O get\\O adjusted\\O to\\O the\\O key\\O board\\O ,\\O as\\O I\\O am\\O used\\O to\\O a\\O bigger\\O one\\O and\\O pounding\\O .\\O", "sentiment": "positive"}]}, {"id": "1020:1", "sentence": "Has all the other features I wanted including a VGA port , HDMI , ethernet and 3 USB ports .", "postag": ["VBZ", "PDT", "DT", "JJ", "NNS", "PRP", "VBD", "VBG", "DT", "NN", "NN", ",", "NN", ",", "NN", "CC", "CD", "NN", "NNS", "."], "head": [0, 5, 5, 5, 1, 7, 5, 11, 11, 11, 5, 13, 11, 15, 11, 19, 19, 19, 11, 1], "deprel": ["root", "det:predet", "det", "amod", "obj", "nsubj", "acl:relcl", "case", "det", "compound", "nmod", "punct", "conj", "punct", "conj", "cc", "nummod", "compound", "conj", "punct"], "triples": [{"uid": "1020:1-0", "target_tags": "Has\\O all\\O the\\O other\\O features\\B I\\O wanted\\O including\\O a\\O VGA\\O port\\O ,\\O HDMI\\O ,\\O ethernet\\O and\\O 3\\O USB\\O ports\\O .\\O", "opinion_tags": "Has\\O all\\O the\\O other\\O features\\O I\\O wanted\\B including\\O a\\O VGA\\O port\\O ,\\O HDMI\\O ,\\O ethernet\\O and\\O 3\\O USB\\O ports\\O .\\O", "sentiment": "positive"}, {"uid": "1020:1-1", "target_tags": "Has\\O all\\O the\\O other\\O features\\O I\\O wanted\\O including\\O a\\O VGA\\B port\\I ,\\O HDMI\\O ,\\O ethernet\\O and\\O 3\\O USB\\O ports\\O .\\O", "opinion_tags": "Has\\O all\\O the\\O other\\O features\\O I\\O wanted\\B including\\O a\\O VGA\\O port\\O ,\\O HDMI\\O ,\\O ethernet\\O and\\O 3\\O USB\\O ports\\O .\\O", "sentiment": "neutral"}, {"uid": "1020:1-2", "target_tags": "Has\\O all\\O the\\O other\\O features\\O I\\O wanted\\O including\\O a\\O VGA\\O port\\O ,\\O HDMI\\B ,\\O ethernet\\O and\\O 3\\O USB\\O ports\\O .\\O", "opinion_tags": "Has\\O all\\O the\\O other\\O features\\O I\\O wanted\\B including\\O a\\O VGA\\O port\\O ,\\O HDMI\\O ,\\O ethernet\\O and\\O 3\\O USB\\O ports\\O .\\O", "sentiment": "neutral"}, {"uid": "1020:1-3", "target_tags": "Has\\O all\\O the\\O other\\O features\\O I\\O wanted\\O including\\O a\\O VGA\\O port\\O ,\\O HDMI\\O ,\\O ethernet\\B and\\O 3\\O USB\\O ports\\O .\\O", "opinion_tags": "Has\\O all\\O the\\O other\\O features\\O I\\O wanted\\B including\\O a\\O VGA\\O port\\O ,\\O HDMI\\O ,\\O ethernet\\O and\\O 3\\O USB\\O ports\\O .\\O", "sentiment": "neutral"}, {"uid": "1020:1-4", "target_tags": "Has\\O all\\O the\\O other\\O features\\O I\\O wanted\\O including\\O a\\O VGA\\O port\\O ,\\O HDMI\\O ,\\O ethernet\\O and\\O 3\\O USB\\B ports\\I .\\O", "opinion_tags": "Has\\O all\\O the\\O other\\O features\\O I\\O wanted\\B including\\O a\\O VGA\\O port\\O ,\\O HDMI\\O ,\\O ethernet\\O and\\O 3\\O USB\\O ports\\O .\\O", "sentiment": "neutral"}]}, {"id": "271:28", "sentence": "The only thing I dislike about this laptop are the rubber pads found on the bottom of the computer for grip .", "postag": ["DT", "JJ", "NN", "PRP", "VBP", "IN", "DT", "NN", "VBP", "DT", "NN", "NNS", "VBN", "IN", "DT", "NN", "IN", "DT", "NN", "IN", "NN", "."], "head": [3, 3, 12, 5, 3, 8, 8, 5, 12, 12, 12, 0, 12, 16, 16, 13, 19, 19, 16, 21, 16, 12], "deprel": ["det", "amod", "nsubj", "nsubj", "acl:relcl", "case", "det", "obl", "cop", "det", "compound", "root", "acl", "case", "det", "obl", "case", "det", "nmod", "case", "nmod", "punct"], "triples": [{"uid": "271:28-0", "target_tags": "The\\O only\\O thing\\O I\\O dislike\\O about\\O this\\O laptop\\O are\\O the\\O rubber\\B pads\\I found\\O on\\O the\\O bottom\\O of\\O the\\O computer\\O for\\O grip\\O .\\O", "opinion_tags": "The\\O only\\O thing\\O I\\O dislike\\B about\\O this\\O laptop\\O are\\O the\\O rubber\\O pads\\O found\\O on\\O the\\O bottom\\O of\\O the\\O computer\\O for\\O grip\\O .\\O", "sentiment": "negative"}]}, {"id": "1143:1", "sentence": "It 's a decent computer for the price and hopefully it will last a long time .", "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "IN", "DT", "NN", "CC", "RB", "PRP", "MD", "VB", "DT", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 13, 13, 13, 13, 5, 16, 16, 13, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "case", "det", "nmod", "cc", "advmod", "nsubj", "aux", "conj", "det", "amod", "obj", "punct"], "triples": [{"uid": "1143:1-0", "target_tags": "It\\O 's\\O a\\O decent\\O computer\\O for\\O the\\O price\\B and\\O hopefully\\O it\\O will\\O last\\O a\\O long\\O time\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O decent\\B computer\\O for\\O the\\O price\\O and\\O hopefully\\O it\\O will\\O last\\O a\\O long\\O time\\O .\\O", "sentiment": "neutral"}]}, {"id": "29:1027", "sentence": "The nicest part is the low heat output and ultra quiet operation .", "postag": ["DT", "JJS", "NN", "VBZ", "DT", "JJ", "NN", "NN", "CC", "JJ", "JJ", "NN", "."], "head": [3, 3, 8, 8, 8, 8, 8, 0, 12, 12, 12, 8, 8], "deprel": ["det", "amod", "nsubj", "cop", "det", "amod", "compound", "root", "cc", "amod", "amod", "conj", "punct"], "triples": [{"uid": "29:1027-0", "target_tags": "The\\O nicest\\O part\\O is\\O the\\O low\\O heat\\B output\\I and\\O ultra\\O quiet\\O operation\\O .\\O", "opinion_tags": "The\\O nicest\\B part\\O is\\O the\\O low\\B heat\\O output\\O and\\O ultra\\O quiet\\O operation\\O .\\O", "sentiment": "positive"}, {"uid": "29:1027-1", "target_tags": "The\\O nicest\\O part\\O is\\O the\\O low\\O heat\\O output\\O and\\O ultra\\O quiet\\O operation\\B .\\O", "opinion_tags": "The\\O nicest\\B part\\O is\\O the\\O low\\O heat\\O output\\O and\\O ultra\\O quiet\\B operation\\O .\\O", "sentiment": "positive"}]}, {"id": "75:1", "sentence": "this Mac Mini does not have a built-in mic , and it would seem that its Mac OS 10.9 does not handle external microphones properly .", "postag": ["DT", "NNP", "NNP", "VBZ", "RB", "VB", "DT", "JJ", "NN", ",", "CC", "PRP", "MD", "VB", "IN", "PRP$", "NNP", "NNP", "CD", "VBZ", "RB", "VB", "JJ", "NNS", "RB", "."], "head": [3, 3, 6, 6, 6, 0, 9, 9, 6, 14, 14, 14, 14, 6, 22, 18, 18, 22, 18, 22, 22, 14, 24, 22, 22, 6], "deprel": ["det", "compound", "nsubj", "aux", "advmod", "root", "det", "amod", "obj", "punct", "cc", "expl", "aux", "conj", "mark", "nmod:poss", "compound", "nsubj", "nummod", "aux", "advmod", "ccomp", "amod", "obj", "advmod", "punct"], "triples": [{"uid": "75:1-0", "target_tags": "this\\O Mac\\O Mini\\O does\\O not\\O have\\O a\\O built-in\\B mic\\I ,\\O and\\O it\\O would\\O seem\\O that\\O its\\O Mac\\O OS\\O 10.9\\O does\\O not\\O handle\\O external\\O microphones\\O properly\\O .\\O", "opinion_tags": "this\\O Mac\\O Mini\\O does\\O not\\B have\\I a\\O built-in\\O mic\\O ,\\O and\\O it\\O would\\O seem\\O that\\O its\\O Mac\\O OS\\O 10.9\\O does\\O not\\O handle\\O external\\O microphones\\O properly\\O .\\O", "sentiment": "neutral"}, {"uid": "75:1-1", "target_tags": "this\\O Mac\\O Mini\\O does\\O not\\O have\\O a\\O built-in\\O mic\\O ,\\O and\\O it\\O would\\O seem\\O that\\O its\\O Mac\\B OS\\I 10.9\\I does\\O not\\O handle\\O external\\O microphones\\O properly\\O .\\O", "opinion_tags": "this\\O Mac\\O Mini\\O does\\O not\\O have\\O a\\O built-in\\O mic\\O ,\\O and\\O it\\O would\\O seem\\O that\\O its\\O Mac\\O OS\\O 10.9\\O does\\O not\\B handle\\I external\\O microphones\\O properly\\O .\\O", "sentiment": "negative"}]}, {"id": "859:1", "sentence": "A lot of features and shortcuts on the MBP that I was never exposed to on a normal PC .", "postag": ["DT", "NN", "IN", "NNS", "CC", "NNS", "IN", "DT", "NN", "WDT", "PRP", "VBD", "RB", "VBN", "IN", "IN", "DT", "JJ", "NN", "."], "head": [2, 0, 4, 2, 6, 4, 9, 9, 2, 14, 14, 14, 14, 9, 14, 19, 19, 19, 14, 2], "deprel": ["det", "root", "case", "nmod", "cc", "conj", "case", "det", "nmod", "obl", "nsubj:pass", "aux:pass", "advmod", "acl:relcl", "obl", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "859:1-0", "target_tags": "A\\O lot\\O of\\O features\\B and\\O shortcuts\\O on\\O the\\O MBP\\O that\\O I\\O was\\O never\\O exposed\\O to\\O on\\O a\\O normal\\O PC\\O .\\O", "opinion_tags": "A\\O lot\\B of\\I features\\O and\\O shortcuts\\O on\\O the\\O MBP\\O that\\O I\\O was\\O never\\O exposed\\O to\\O on\\O a\\O normal\\O PC\\O .\\O", "sentiment": "neutral"}, {"uid": "859:1-1", "target_tags": "A\\O lot\\O of\\O features\\O and\\O shortcuts\\B on\\O the\\O MBP\\O that\\O I\\O was\\O never\\O exposed\\O to\\O on\\O a\\O normal\\O PC\\O .\\O", "opinion_tags": "A\\O lot\\B of\\I features\\O and\\O shortcuts\\O on\\O the\\O MBP\\O that\\O I\\O was\\O never\\O exposed\\O to\\O on\\O a\\O normal\\O PC\\O .\\O", "sentiment": "neutral"}]}, {"id": "768:1", "sentence": "Was n't sure if I was going to like it much less love it so I went to a local best buy and played around with the IOS system on a Mac Pro and it was totally unique and different .", "postag": ["VBD", "RB", "JJ", "IN", "PRP", "VBD", "VBG", "TO", "VB", "PRP", "RB", "RBR", "VB", "PRP", "RB", "PRP", "VBD", "IN", "DT", "JJ", "JJS", "NN", "CC", "VBD", "RB", "IN", "DT", "NNP", "NN", "IN", "DT", "NNP", "NNP", "CC", "PRP", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [3, 3, 0, 7, 7, 7, 3, 9, 7, 9, 12, 13, 9, 13, 17, 17, 3, 22, 22, 22, 22, 17, 24, 17, 24, 29, 29, 29, 24, 33, 33, 33, 24, 38, 38, 38, 38, 17, 40, 38, 3], "deprel": ["cop", "advmod", "root", "mark", "nsubj", "aux", "advcl", "mark", "xcomp", "obj", "advmod", "advmod", "xcomp", "obj", "advmod", "nsubj", "conj", "case", "det", "amod", "amod", "obl", "cc", "conj", "advmod", "case", "det", "compound", "obl", "case", "det", "compound", "obl", "cc", "nsubj", "cop", "advmod", "conj", "cc", "conj", "punct"], "triples": [{"uid": "768:1-0", "target_tags": "Was\\O n't\\O sure\\O if\\O I\\O was\\O going\\O to\\O like\\O it\\O much\\O less\\O love\\O it\\O so\\O I\\O went\\O to\\O a\\O local\\O best\\O buy\\O and\\O played\\O around\\O with\\O the\\O IOS\\B system\\I on\\O a\\O Mac\\O Pro\\O and\\O it\\O was\\O totally\\O unique\\O and\\O different\\O .\\O", "opinion_tags": "Was\\O n't\\O sure\\O if\\O I\\O was\\O going\\O to\\O like\\O it\\O much\\O less\\O love\\O it\\O so\\O I\\O went\\O to\\O a\\O local\\O best\\O buy\\O and\\O played\\O around\\O with\\O the\\O IOS\\O system\\O on\\O a\\O Mac\\O Pro\\O and\\O it\\O was\\O totally\\O unique\\B and\\O different\\B .\\O", "sentiment": "positive"}]}, {"id": "272:10", "sentence": "air has higher resolution but the fonts are small .", "postag": ["NN", "VBZ", "JJR", "NN", "CC", "DT", "NNS", "VBP", "JJ", "."], "head": [2, 0, 4, 2, 9, 7, 9, 9, 2, 2], "deprel": ["nsubj", "root", "amod", "obj", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "272:10-0", "target_tags": "air\\O has\\O higher\\O resolution\\B but\\O the\\O fonts\\O are\\O small\\O .\\O", "opinion_tags": "air\\O has\\O higher\\B resolution\\O but\\O the\\O fonts\\O are\\O small\\O .\\O", "sentiment": "positive"}, {"uid": "272:10-1", "target_tags": "air\\O has\\O higher\\O resolution\\O but\\O the\\O fonts\\B are\\O small\\O .\\O", "opinion_tags": "air\\O has\\O higher\\O resolution\\O but\\O the\\O fonts\\O are\\O small\\B .\\O", "sentiment": "negative"}]}, {"id": "862:1", "sentence": "working with Mac is so much easier , so many cool features .", "postag": ["VBG", "IN", "NNP", "VBZ", "RB", "RB", "JJR", ",", "RB", "JJ", "JJ", "NNS", "."], "head": [7, 3, 1, 7, 6, 7, 0, 7, 10, 12, 12, 7, 7], "deprel": ["csubj", "case", "obl", "cop", "advmod", "advmod", "root", "punct", "advmod", "amod", "amod", "parataxis", "punct"], "triples": [{"uid": "862:1-0", "target_tags": "working\\B with\\O Mac\\O is\\O so\\O much\\O easier\\O ,\\O so\\O many\\O cool\\O features\\O .\\O", "opinion_tags": "working\\O with\\O Mac\\O is\\O so\\O much\\O easier\\B ,\\O so\\O many\\O cool\\O features\\O .\\O", "sentiment": "positive"}, {"uid": "862:1-1", "target_tags": "working\\O with\\O Mac\\O is\\O so\\O much\\O easier\\O ,\\O so\\O many\\O cool\\O features\\B .\\O", "opinion_tags": "working\\O with\\O Mac\\O is\\O so\\O much\\O easier\\O ,\\O so\\O many\\O cool\\B features\\O .\\O", "sentiment": "positive"}]}, {"id": "643:1", "sentence": "I like the brightness and adjustments .", "postag": ["PRP", "VBP", "DT", "NN", "CC", "NNS", "."], "head": [2, 0, 4, 2, 6, 4, 2], "deprel": ["nsubj", "root", "det", "obj", "cc", "conj", "punct"], "triples": [{"uid": "643:1-0", "target_tags": "I\\O like\\O the\\O brightness\\B and\\O adjustments\\O .\\O", "opinion_tags": "I\\O like\\B the\\O brightness\\O and\\O adjustments\\O .\\O", "sentiment": "positive"}, {"uid": "643:1-1", "target_tags": "I\\O like\\O the\\O brightness\\O and\\O adjustments\\B .\\O", "opinion_tags": "I\\O like\\B the\\O brightness\\O and\\O adjustments\\O .\\O", "sentiment": "positive"}]}, {"id": "164:1", "sentence": "I only wish this mac had a CD/DVD player built in .", "postag": ["PRP", "RB", "VBP", "DT", "NN", "VBD", "DT", "NN", "NN", "VBN", "RP", "."], "head": [3, 3, 0, 5, 6, 3, 9, 9, 6, 6, 10, 3], "deprel": ["nsubj", "advmod", "root", "det", "nsubj", "ccomp", "det", "compound", "obj", "xcomp", "compound:prt", "punct"], "triples": [{"uid": "164:1-0", "target_tags": "I\\O only\\O wish\\O this\\O mac\\O had\\O a\\O CD/DVD\\B player\\I built\\O in\\O .\\O", "opinion_tags": "I\\O only\\O wish\\B this\\O mac\\O had\\O a\\O CD/DVD\\O player\\O built\\O in\\O .\\O", "sentiment": "neutral"}]}, {"id": "969:5", "sentence": "The only thing I miss is that my old Alienware laptop had backlit keys .", "postag": ["DT", "JJ", "NN", "PRP", "VBP", "VBZ", "IN", "PRP$", "JJ", "NNP", "NN", "VBD", "NN", "NNS", "."], "head": [3, 3, 6, 5, 3, 0, 12, 11, 11, 11, 12, 6, 14, 12, 6], "deprel": ["det", "amod", "nsubj", "nsubj", "acl:relcl", "root", "mark", "nmod:poss", "amod", "compound", "nsubj", "ccomp", "compound", "obj", "punct"], "triples": [{"uid": "969:5-0", "target_tags": "The\\O only\\O thing\\O I\\O miss\\O is\\O that\\O my\\O old\\O Alienware\\O laptop\\O had\\O backlit\\B keys\\I .\\O", "opinion_tags": "The\\O only\\O thing\\O I\\O miss\\B is\\O that\\O my\\O old\\O Alienware\\O laptop\\O had\\O backlit\\O keys\\O .\\O", "sentiment": "negative"}]}, {"id": "933:1", "sentence": "The only thing I miss are the `` Home/End '' type keys and other things that I grew accustomed to after so long .", "postag": ["DT", "JJ", "NN", "PRP", "VBP", "VBP", "DT", "``", "NNP", "''", "NN", "NNS", "CC", "JJ", "NNS", "WDT", "PRP", "VBD", "JJ", "IN", "IN", "RB", "RB", "."], "head": [3, 3, 12, 5, 3, 12, 12, 9, 12, 11, 12, 0, 15, 15, 12, 18, 18, 15, 18, 16, 23, 23, 19, 12], "deprel": ["det", "amod", "nsubj", "nsubj", "acl:relcl", "cop", "det", "punct", "compound", "punct", "compound", "root", "cc", "amod", "conj", "obj", "nsubj", "acl:relcl", "xcomp", "case", "case", "advmod", "obl", "punct"], "triples": [{"uid": "933:1-0", "target_tags": "The\\O only\\O thing\\O I\\O miss\\O are\\O the\\O ``\\B Home/End\\I ''\\I type\\I keys\\I and\\O other\\O things\\O that\\O I\\O grew\\O accustomed\\O to\\O after\\O so\\O long\\O .\\O", "opinion_tags": "The\\O only\\O thing\\O I\\O miss\\B are\\O the\\O ``\\O Home/End\\O ''\\O type\\O keys\\O and\\O other\\O things\\O that\\O I\\O grew\\O accustomed\\O to\\O after\\O so\\O long\\O .\\O", "sentiment": "neutral"}]}, {"id": "535:1", "sentence": "So happy with this purchase , I just wish it came with Microsoft Word .", "postag": ["RB", "JJ", "IN", "DT", "NN", ",", "PRP", "RB", "VBP", "PRP", "VBD", "IN", "NNP", "NNP", "."], "head": [2, 0, 5, 5, 2, 9, 9, 9, 2, 11, 9, 14, 14, 11, 2], "deprel": ["advmod", "root", "case", "det", "obl", "punct", "nsubj", "advmod", "parataxis", "nsubj", "ccomp", "case", "compound", "obl", "punct"], "triples": [{"uid": "535:1-0", "target_tags": "So\\O happy\\O with\\O this\\O purchase\\O ,\\O I\\O just\\O wish\\O it\\O came\\O with\\O Microsoft\\B Word\\I .\\O", "opinion_tags": "So\\O happy\\O with\\O this\\O purchase\\O ,\\O I\\O just\\O wish\\B it\\O came\\O with\\O Microsoft\\O Word\\O .\\O", "sentiment": "neutral"}]}, {"id": "1133:1", "sentence": "It has enough memory and speed to run my business with all the flexibility that comes with a laptop .", "postag": ["PRP", "VBZ", "JJ", "NN", "CC", "NN", "TO", "VB", "PRP$", "NN", "IN", "PDT", "DT", "NN", "WDT", "VBZ", "IN", "DT", "NN", "."], "head": [2, 0, 4, 2, 6, 4, 8, 4, 10, 8, 14, 14, 14, 8, 16, 14, 19, 19, 16, 2], "deprel": ["nsubj", "root", "amod", "obj", "cc", "conj", "mark", "acl", "nmod:poss", "obj", "case", "det:predet", "det", "obl", "nsubj", "acl:relcl", "case", "det", "obl", "punct"], "triples": [{"uid": "1133:1-0", "target_tags": "It\\O has\\O enough\\O memory\\B and\\O speed\\O to\\O run\\O my\\O business\\O with\\O all\\O the\\O flexibility\\O that\\O comes\\O with\\O a\\O laptop\\O .\\O", "opinion_tags": "It\\O has\\O enough\\B memory\\O and\\O speed\\O to\\O run\\O my\\O business\\O with\\O all\\O the\\O flexibility\\O that\\O comes\\O with\\O a\\O laptop\\O .\\O", "sentiment": "positive"}, {"uid": "1133:1-1", "target_tags": "It\\O has\\O enough\\O memory\\O and\\O speed\\B to\\O run\\O my\\O business\\O with\\O all\\O the\\O flexibility\\O that\\O comes\\O with\\O a\\O laptop\\O .\\O", "opinion_tags": "It\\O has\\O enough\\B memory\\O and\\O speed\\O to\\O run\\O my\\O business\\O with\\O all\\O the\\O flexibility\\O that\\O comes\\O with\\O a\\O laptop\\O .\\O", "sentiment": "positive"}]}, {"id": "390:1", "sentence": "The battery life is excellent , the display is excellent , and downloading apps is a breeze .", "postag": ["DT", "NN", "NN", "VBZ", "JJ", ",", "DT", "NN", "VBZ", "JJ", ",", "CC", "VBG", "NNS", "VBZ", "DT", "NN", "."], "head": [3, 3, 5, 5, 0, 10, 8, 10, 10, 5, 17, 17, 17, 17, 17, 17, 5, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "conj", "punct", "cc", "csubj", "nsubj", "cop", "det", "conj", "punct"], "triples": [{"uid": "390:1-0", "target_tags": "The\\O battery\\B life\\I is\\O excellent\\O ,\\O the\\O display\\O is\\O excellent\\O ,\\O and\\O downloading\\O apps\\O is\\O a\\O breeze\\O .\\O", "opinion_tags": "The\\O battery\\O life\\O is\\O excellent\\B ,\\O the\\O display\\O is\\O excellent\\O ,\\O and\\O downloading\\O apps\\O is\\O a\\O breeze\\O .\\O", "sentiment": "positive"}, {"uid": "390:1-1", "target_tags": "The\\O battery\\O life\\O is\\O excellent\\O ,\\O the\\O display\\B is\\O excellent\\O ,\\O and\\O downloading\\O apps\\O is\\O a\\O breeze\\O .\\O", "opinion_tags": "The\\O battery\\O life\\O is\\O excellent\\O ,\\O the\\O display\\O is\\O excellent\\B ,\\O and\\O downloading\\O apps\\O is\\O a\\O breeze\\O .\\O", "sentiment": "positive"}, {"uid": "390:1-2", "target_tags": "The\\O battery\\O life\\O is\\O excellent\\O ,\\O the\\O display\\O is\\O excellent\\O ,\\O and\\O downloading\\B apps\\I is\\O a\\O breeze\\O .\\O", "opinion_tags": "The\\O battery\\O life\\O is\\O excellent\\O ,\\O the\\O display\\O is\\O excellent\\O ,\\O and\\O downloading\\O apps\\O is\\O a\\O breeze\\B .\\O", "sentiment": "positive"}]}, {"id": "940:1", "sentence": "The screen , the software and the smoothness of the operating system .", "postag": ["DT", "NN", ",", "DT", "NN", "CC", "DT", "NN", "IN", "DT", "NN", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 12, 12, 12, 8, 2], "deprel": ["det", "root", "punct", "det", "conj", "cc", "det", "conj", "case", "det", "compound", "nmod", "punct"], "triples": [{"uid": "940:1-0", "target_tags": "The\\O screen\\O ,\\O the\\O software\\O and\\O the\\O smoothness\\O of\\O the\\O operating\\B system\\I .\\O", "opinion_tags": "The\\O screen\\O ,\\O the\\O software\\O and\\O the\\O smoothness\\B of\\O the\\O operating\\O system\\O .\\O", "sentiment": "positive"}]}, {"id": "311:18", "sentence": "But with this laptop , the bass is very weak and the sound comes out sounding tinny .", "postag": ["CC", "IN", "DT", "NN", ",", "DT", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NN", "VBZ", "RP", "VBG", "JJ", "."], "head": [14, 4, 4, 10, 10, 7, 10, 10, 10, 0, 14, 13, 14, 10, 14, 14, 16, 10], "deprel": ["cc", "case", "det", "obl", "punct", "det", "nsubj", "cop", "advmod", "root", "cc", "det", "nsubj", "conj", "compound:prt", "advcl", "xcomp", "punct"], "triples": [{"uid": "311:18-0", "target_tags": "But\\O with\\O this\\O laptop\\O ,\\O the\\O bass\\B is\\O very\\O weak\\O and\\O the\\O sound\\O comes\\O out\\O sounding\\O tinny\\O .\\O", "opinion_tags": "But\\O with\\O this\\O laptop\\O ,\\O the\\O bass\\O is\\O very\\O weak\\B and\\O the\\O sound\\O comes\\O out\\O sounding\\O tinny\\O .\\O", "sentiment": "negative"}, {"uid": "311:18-1", "target_tags": "But\\O with\\O this\\O laptop\\O ,\\O the\\O bass\\O is\\O very\\O weak\\O and\\O the\\O sound\\B comes\\O out\\O sounding\\O tinny\\O .\\O", "opinion_tags": "But\\O with\\O this\\O laptop\\O ,\\O the\\O bass\\O is\\O very\\O weak\\O and\\O the\\O sound\\O comes\\O out\\O sounding\\O tinny\\B .\\O", "sentiment": "negative"}]}, {"id": "339:1", "sentence": "The built quality is really good , I was so Happy and excited about this Product .", "postag": ["DT", "VBN", "NN", "VBZ", "RB", "JJ", ",", "PRP", "VBD", "RB", "JJ", "CC", "JJ", "IN", "DT", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 6, 11, 11, 11, 6, 13, 11, 16, 16, 11, 6], "deprel": ["det", "amod", "nsubj", "cop", "advmod", "root", "punct", "nsubj", "cop", "advmod", "parataxis", "cc", "conj", "case", "det", "obl", "punct"], "triples": [{"uid": "339:1-0", "target_tags": "The\\O built\\B quality\\I is\\O really\\O good\\O ,\\O I\\O was\\O so\\O Happy\\O and\\O excited\\O about\\O this\\O Product\\O .\\O", "opinion_tags": "The\\O built\\O quality\\O is\\O really\\O good\\B ,\\O I\\O was\\O so\\O Happy\\B and\\O excited\\B about\\O this\\O Product\\O .\\O", "sentiment": "positive"}]}, {"id": "484:1", "sentence": "I am loving the fast performance also .", "postag": ["PRP", "VBP", "VBG", "DT", "JJ", "NN", "RB", "."], "head": [3, 3, 0, 6, 6, 3, 3, 3], "deprel": ["nsubj", "aux", "root", "det", "amod", "obj", "advmod", "punct"], "triples": [{"uid": "484:1-0", "target_tags": "I\\O am\\O loving\\O the\\O fast\\O performance\\B also\\O .\\O", "opinion_tags": "I\\O am\\O loving\\B the\\O fast\\B performance\\O also\\O .\\O", "sentiment": "positive"}]}, {"id": "29:313", "sentence": "Further , this Mac Mini has a sloppy Bluetooth interface ( courtesy of the Mac OS ) and the range is poor .", "postag": ["RBR", ",", "DT", "NNP", "NNP", "VBZ", "DT", "JJ", "NN", "NN", "-LRB-", "NN", "IN", "DT", "NNP", "NN", "-RRB-", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [6, 6, 5, 5, 6, 0, 10, 10, 10, 6, 12, 10, 16, 16, 16, 12, 12, 22, 20, 22, 22, 6, 6], "deprel": ["advmod", "punct", "det", "compound", "nsubj", "root", "det", "amod", "compound", "obj", "punct", "appos", "case", "det", "compound", "nmod", "punct", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "29:313-0", "target_tags": "Further\\O ,\\O this\\O Mac\\O Mini\\O has\\O a\\O sloppy\\O Bluetooth\\B interface\\I (\\O courtesy\\O of\\O the\\O Mac\\O OS\\O )\\O and\\O the\\O range\\O is\\O poor\\O .\\O", "opinion_tags": "Further\\O ,\\O this\\O Mac\\O Mini\\O has\\O a\\O sloppy\\B Bluetooth\\O interface\\O (\\O courtesy\\O of\\O the\\O Mac\\O OS\\O )\\O and\\O the\\O range\\O is\\O poor\\O .\\O", "sentiment": "negative"}, {"uid": "29:313-1", "target_tags": "Further\\O ,\\O this\\O Mac\\O Mini\\O has\\O a\\O sloppy\\O Bluetooth\\O interface\\O (\\O courtesy\\O of\\O the\\O Mac\\B OS\\I )\\O and\\O the\\O range\\O is\\O poor\\O .\\O", "opinion_tags": "Further\\O ,\\O this\\O Mac\\O Mini\\O has\\O a\\O sloppy\\O Bluetooth\\O interface\\O (\\O courtesy\\B of\\O the\\O Mac\\O OS\\O )\\O and\\O the\\O range\\O is\\O poor\\O .\\O", "sentiment": "negative"}, {"uid": "29:313-2", "target_tags": "Further\\O ,\\O this\\O Mac\\O Mini\\O has\\O a\\O sloppy\\O Bluetooth\\O interface\\O (\\O courtesy\\O of\\O the\\O Mac\\O OS\\O )\\O and\\O the\\O range\\B is\\O poor\\O .\\O", "opinion_tags": "Further\\O ,\\O this\\O Mac\\O Mini\\O has\\O a\\O sloppy\\O Bluetooth\\O interface\\O (\\O courtesy\\O of\\O the\\O Mac\\O OS\\O )\\O and\\O the\\O range\\O is\\O poor\\B .\\O", "sentiment": "negative"}]}, {"id": "67:1", "sentence": "My only gripe would be the need to add more RAM .", "postag": ["PRP$", "JJ", "NN", "MD", "VB", "DT", "NN", "TO", "VB", "JJR", "NN", "."], "head": [3, 3, 7, 7, 7, 7, 0, 9, 7, 11, 9, 7], "deprel": ["nmod:poss", "amod", "nsubj", "aux", "cop", "det", "root", "mark", "acl", "amod", "obj", "punct"], "triples": [{"uid": "67:1-0", "target_tags": "My\\O only\\O gripe\\O would\\O be\\O the\\O need\\O to\\O add\\O more\\O RAM\\B .\\O", "opinion_tags": "My\\O only\\O gripe\\B would\\O be\\O the\\O need\\O to\\O add\\O more\\O RAM\\O .\\O", "sentiment": "negative"}]}, {"id": "1107:3", "sentence": "Fine if you have a touch screen .", "postag": ["UH", "IN", "PRP", "VBP", "DT", "NN", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 4], "deprel": ["discourse", "mark", "nsubj", "root", "det", "compound", "obj", "punct"], "triples": [{"uid": "1107:3-0", "target_tags": "Fine\\O if\\O you\\O have\\O a\\O touch\\B screen\\I .\\O", "opinion_tags": "Fine\\B if\\O you\\O have\\O a\\O touch\\O screen\\O .\\O", "sentiment": "neutral"}]}, {"id": "934:1", "sentence": "This by far beats any computer out on the market today built well , battery life AMAZING .", "postag": ["DT", "IN", "RB", "VBZ", "DT", "NN", "RB", "IN", "DT", "NN", "NN", "VBN", "RB", ",", "NN", "NN", "JJ", "."], "head": [4, 3, 4, 0, 6, 4, 4, 10, 10, 4, 4, 4, 12, 4, 16, 17, 12, 4], "deprel": ["nsubj", "case", "obl", "root", "det", "obj", "advmod", "case", "det", "obl", "obl:tmod", "advcl", "advmod", "punct", "compound", "nsubj", "xcomp", "punct"], "triples": [{"uid": "934:1-0", "target_tags": "This\\O by\\O far\\O beats\\O any\\O computer\\O out\\O on\\O the\\O market\\O today\\O built\\B well\\O ,\\O battery\\O life\\O AMAZING\\O .\\O", "opinion_tags": "This\\O by\\O far\\O beats\\O any\\O computer\\O out\\O on\\O the\\O market\\O today\\O built\\O well\\B ,\\O battery\\O life\\O AMAZING\\O .\\O", "sentiment": "positive"}, {"uid": "934:1-1", "target_tags": "This\\O by\\O far\\O beats\\O any\\O computer\\O out\\O on\\O the\\O market\\O today\\O built\\O well\\O ,\\O battery\\B life\\I AMAZING\\O .\\O", "opinion_tags": "This\\O by\\O far\\O beats\\O any\\O computer\\O out\\O on\\O the\\O market\\O today\\O built\\O well\\O ,\\O battery\\O life\\O AMAZING\\B .\\O", "sentiment": "positive"}]}, {"id": "787:543", "sentence": "The OS is easy , and offers all kinds of surprises .", "postag": ["DT", "NNP", "VBZ", "JJ", ",", "CC", "VBZ", "DT", "NNS", "IN", "NNS", "."], "head": [2, 4, 4, 0, 7, 7, 4, 9, 7, 11, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "conj", "det", "obj", "case", "nmod", "punct"], "triples": [{"uid": "787:543-0", "target_tags": "The\\O OS\\B is\\O easy\\O ,\\O and\\O offers\\O all\\O kinds\\O of\\O surprises\\O .\\O", "opinion_tags": "The\\O OS\\O is\\O easy\\B ,\\O and\\O offers\\O all\\O kinds\\O of\\O surprises\\B .\\O", "sentiment": "positive"}]}, {"id": "834:1", "sentence": "A veryimportant feature is Firewire 800 which in my experience works better then USB3 ( in PC enabled with USB3 ) I was not originally sold on the MAC OS I felt it was inferior in many ways To Windows 7 .", "postag": ["DT", "JJ", "NN", "VBZ", "NNP", "CD", "WDT", "IN", "PRP$", "NN", "VBZ", "JJR", "RB", "NNP", "-LRB-", "IN", "NNP", "VBN", "IN", "NNP", "-RRB-", "PRP", "VBD", "RB", "RB", "VBN", "IN", "DT", "NNP", "NNP", "PRP", "VBD", "PRP", "VBD", "JJ", "IN", "JJ", "NNS", "IN", "NNP", "CD", "."], "head": [3, 3, 5, 5, 0, 5, 11, 10, 10, 11, 5, 11, 14, 11, 17, 17, 11, 17, 20, 18, 17, 26, 26, 26, 26, 5, 30, 30, 30, 26, 32, 5, 35, 35, 32, 38, 38, 35, 40, 38, 40, 5], "deprel": ["det", "amod", "nsubj", "cop", "root", "nummod", "nsubj", "case", "nmod:poss", "obl", "acl:relcl", "advmod", "advmod", "obl:npmod", "punct", "case", "obl", "acl", "case", "obl", "punct", "nsubj:pass", "aux:pass", "advmod", "advmod", "parataxis", "case", "det", "compound", "obl", "nsubj", "parataxis", "nsubj", "cop", "ccomp", "case", "amod", "obl", "case", "nmod", "nummod", "punct"], "triples": [{"uid": "834:1-0", "target_tags": "A\\O veryimportant\\O feature\\O is\\O Firewire\\B 800\\I which\\O in\\O my\\O experience\\O works\\O better\\O then\\O USB3\\O (\\O in\\O PC\\O enabled\\O with\\O USB3\\O )\\O I\\O was\\O not\\O originally\\O sold\\O on\\O the\\O MAC\\O OS\\O I\\O felt\\O it\\O was\\O inferior\\O in\\O many\\O ways\\O To\\O Windows\\O 7\\O .\\O", "opinion_tags": "A\\O veryimportant\\B feature\\O is\\O Firewire\\O 800\\O which\\O in\\O my\\O experience\\O works\\O better\\B then\\O USB3\\O (\\O in\\O PC\\O enabled\\O with\\O USB3\\O )\\O I\\O was\\O not\\O originally\\O sold\\O on\\O the\\O MAC\\O OS\\O I\\O felt\\O it\\O was\\O inferior\\O in\\O many\\O ways\\O To\\O Windows\\O 7\\O .\\O", "sentiment": "positive"}, {"uid": "834:1-1", "target_tags": "A\\O veryimportant\\O feature\\O is\\O Firewire\\O 800\\O which\\O in\\O my\\O experience\\O works\\O better\\O then\\O USB3\\O (\\O in\\O PC\\O enabled\\O with\\O USB3\\O )\\O I\\O was\\O not\\O originally\\O sold\\O on\\O the\\O MAC\\B OS\\I I\\O felt\\O it\\O was\\O inferior\\O in\\O many\\O ways\\O To\\O Windows\\O 7\\O .\\O", "opinion_tags": "A\\O veryimportant\\O feature\\O is\\O Firewire\\O 800\\O which\\O in\\O my\\O experience\\O works\\O better\\O then\\O USB3\\O (\\O in\\O PC\\O enabled\\O with\\O USB3\\O )\\O I\\O was\\O not\\O originally\\O sold\\O on\\O the\\O MAC\\O OS\\O I\\O felt\\O it\\O was\\O inferior\\B in\\O many\\O ways\\O To\\O Windows\\O 7\\O .\\O", "sentiment": "negative"}]}, {"id": "146:1", "sentence": "I like iTunes , the apparent security , the Mini form factor , all the nice graphics stuff .", "postag": ["PRP", "VBP", "NNP", ",", "DT", "JJ", "NN", ",", "DT", "NN", "NN", "NN", ",", "PDT", "DT", "JJ", "NNS", "NN", "."], "head": [2, 0, 2, 3, 7, 7, 3, 3, 12, 11, 12, 3, 2, 18, 18, 18, 18, 3, 2], "deprel": ["nsubj", "root", "obj", "punct", "det", "amod", "appos", "punct", "det", "compound", "compound", "appos", "punct", "det:predet", "det", "amod", "compound", "appos", "punct"], "triples": [{"uid": "146:1-0", "target_tags": "I\\O like\\O iTunes\\B ,\\O the\\O apparent\\O security\\O ,\\O the\\O Mini\\O form\\O factor\\O ,\\O all\\O the\\O nice\\O graphics\\O stuff\\O .\\O", "opinion_tags": "I\\O like\\B iTunes\\O ,\\O the\\O apparent\\O security\\O ,\\O the\\O Mini\\O form\\O factor\\O ,\\O all\\O the\\O nice\\O graphics\\O stuff\\O .\\O", "sentiment": "positive"}, {"uid": "146:1-1", "target_tags": "I\\O like\\O iTunes\\O ,\\O the\\O apparent\\O security\\B ,\\O the\\O Mini\\O form\\O factor\\O ,\\O all\\O the\\O nice\\O graphics\\O stuff\\O .\\O", "opinion_tags": "I\\O like\\O iTunes\\O ,\\O the\\O apparent\\B security\\O ,\\O the\\O Mini\\O form\\O factor\\O ,\\O all\\O the\\O nice\\O graphics\\O stuff\\O .\\O", "sentiment": "positive"}, {"uid": "146:1-2", "target_tags": "I\\O like\\O iTunes\\O ,\\O the\\O apparent\\O security\\O ,\\O the\\O Mini\\O form\\O factor\\O ,\\O all\\O the\\O nice\\O graphics\\B stuff\\I .\\O", "opinion_tags": "I\\O like\\O iTunes\\O ,\\O the\\O apparent\\O security\\O ,\\O the\\O Mini\\O form\\O factor\\O ,\\O all\\O the\\O nice\\B graphics\\O stuff\\O .\\O", "sentiment": "positive"}]}, {"id": "755:1", "sentence": "After replacing the spinning hard disk with an ssd drive , my mac is just flying .", "postag": ["IN", "VBG", "DT", "VBG", "JJ", "NN", "IN", "DT", "NN", "NN", ",", "PRP$", "NN", "VBZ", "RB", "VBG", "."], "head": [2, 16, 6, 6, 6, 2, 10, 10, 10, 6, 16, 13, 16, 16, 16, 0, 16], "deprel": ["mark", "advcl", "det", "amod", "amod", "obj", "case", "det", "compound", "nmod", "punct", "nmod:poss", "nsubj", "aux", "advmod", "root", "punct"], "triples": [{"uid": "755:1-0", "target_tags": "After\\O replacing\\O the\\O spinning\\O hard\\O disk\\O with\\O an\\O ssd\\B drive\\I ,\\O my\\O mac\\O is\\O just\\O flying\\O .\\O", "opinion_tags": "After\\O replacing\\O the\\O spinning\\O hard\\O disk\\O with\\O an\\O ssd\\O drive\\O ,\\O my\\O mac\\O is\\O just\\O flying\\B .\\O", "sentiment": "positive"}]}, {"id": "55:1", "sentence": "I know some people complained about HDMI issues but they released a firmware patch to address that issue .", "postag": ["PRP", "VBP", "DT", "NNS", "VBD", "IN", "NNP", "NNS", "CC", "PRP", "VBD", "DT", "NN", "NN", "TO", "VB", "DT", "NN", "."], "head": [2, 0, 4, 5, 2, 8, 8, 5, 11, 11, 2, 14, 14, 11, 16, 11, 18, 16, 2], "deprel": ["nsubj", "root", "det", "nsubj", "ccomp", "case", "compound", "obl", "cc", "nsubj", "conj", "det", "compound", "obj", "mark", "advcl", "det", "obj", "punct"], "triples": [{"uid": "55:1-0", "target_tags": "I\\O know\\O some\\O people\\O complained\\O about\\O HDMI\\B issues\\O but\\O they\\O released\\O a\\O firmware\\O patch\\O to\\O address\\O that\\O issue\\O .\\O", "opinion_tags": "I\\O know\\O some\\O people\\O complained\\B about\\O HDMI\\O issues\\O but\\O they\\O released\\O a\\O firmware\\O patch\\O to\\O address\\O that\\O issue\\O .\\O", "sentiment": "neutral"}]}, {"id": "762:7", "sentence": "With the needs of a professional photographer I generally need to keep up with the best specs .", "postag": ["IN", "DT", "NNS", "IN", "DT", "JJ", "NN", "PRP", "RB", "VBP", "TO", "VB", "RP", "IN", "DT", "JJS", "NNS", "."], "head": [3, 3, 10, 7, 7, 7, 3, 10, 10, 0, 12, 10, 12, 17, 17, 17, 12, 10], "deprel": ["case", "det", "obl", "case", "det", "amod", "nmod", "nsubj", "advmod", "root", "mark", "xcomp", "compound:prt", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "762:7-0", "target_tags": "With\\O the\\O needs\\O of\\O a\\O professional\\O photographer\\O I\\O generally\\O need\\O to\\O keep\\O up\\O with\\O the\\O best\\O specs\\B .\\O", "opinion_tags": "With\\O the\\O needs\\O of\\O a\\O professional\\O photographer\\O I\\O generally\\O need\\O to\\O keep\\O up\\O with\\O the\\O best\\B specs\\O .\\O", "sentiment": "neutral"}]}, {"id": "685:1", "sentence": "packing and everything was perfect", "postag": ["NN", "CC", "NN", "VBD", "JJ"], "head": [5, 3, 1, 5, 0], "deprel": ["nsubj", "cc", "conj", "cop", "root"], "triples": [{"uid": "685:1-0", "target_tags": "packing\\B and\\O everything\\O was\\O perfect\\O", "opinion_tags": "packing\\O and\\O everything\\O was\\O perfect\\B", "sentiment": "positive"}]}, {"id": "1068:1", "sentence": "I called To<PERSON><PERSON> where I gave them the serial number and they informed me that they were having issues with the mother boards .", "postag": ["PRP", "VBD", "NNP", "WRB", "PRP", "VBD", "PRP", "DT", "NN", "NN", "CC", "PRP", "VBD", "PRP", "IN", "PRP", "VBD", "VBG", "NNS", "IN", "DT", "NN", "NNS", "."], "head": [2, 0, 2, 6, 6, 2, 6, 10, 10, 6, 13, 13, 2, 13, 18, 18, 18, 13, 18, 23, 23, 23, 19, 2], "deprel": ["nsubj", "root", "obj", "mark", "nsubj", "advcl", "i<PERSON><PERSON>", "det", "compound", "obj", "cc", "nsubj", "conj", "obj", "mark", "nsubj", "aux", "ccomp", "obj", "case", "det", "compound", "nmod", "punct"], "triples": [{"uid": "1068:1-0", "target_tags": "I\\O called\\O Toshiba\\O where\\O I\\O gave\\O them\\O the\\O serial\\O number\\O and\\O they\\O informed\\O me\\O that\\O they\\O were\\O having\\O issues\\O with\\O the\\O mother\\B boards\\I .\\O", "opinion_tags": "I\\O called\\O Toshiba\\O where\\O I\\O gave\\O them\\O the\\O serial\\O number\\O and\\O they\\O informed\\O me\\O that\\O they\\O were\\O having\\O issues\\B with\\O the\\O mother\\O boards\\O .\\O", "sentiment": "neutral"}]}, {"id": "474:1", "sentence": "I seem to be having repeat problems as the Mother Board in this one is diagnosed as faulty , related to the graphics card .", "postag": ["PRP", "VBP", "TO", "VB", "VBG", "JJ", "NNS", "IN", "DT", "NN", "NN", "IN", "DT", "NN", "VBZ", "VBN", "IN", "JJ", ",", "JJ", "IN", "DT", "NNS", "NN", "."], "head": [2, 0, 5, 5, 2, 7, 5, 16, 11, 11, 16, 14, 14, 11, 16, 5, 18, 16, 20, 16, 24, 24, 24, 20, 2], "deprel": ["nsubj", "root", "mark", "aux", "xcomp", "amod", "obj", "mark", "det", "compound", "nsubj:pass", "case", "det", "nmod", "aux:pass", "advcl", "case", "obl", "punct", "conj", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "474:1-0", "target_tags": "I\\O seem\\O to\\O be\\O having\\O repeat\\O problems\\O as\\O the\\O Mother\\B Board\\I in\\O this\\O one\\O is\\O diagnosed\\O as\\O faulty\\O ,\\O related\\O to\\O the\\O graphics\\O card\\O .\\O", "opinion_tags": "I\\O seem\\O to\\O be\\O having\\O repeat\\O problems\\O as\\O the\\O Mother\\O Board\\O in\\O this\\O one\\O is\\O diagnosed\\O as\\O faulty\\B ,\\O related\\O to\\O the\\O graphics\\O card\\O .\\O", "sentiment": "negative"}, {"uid": "474:1-1", "target_tags": "I\\O seem\\O to\\O be\\O having\\O repeat\\O problems\\O as\\O the\\O Mother\\O Board\\O in\\O this\\O one\\O is\\O diagnosed\\O as\\O faulty\\O ,\\O related\\O to\\O the\\O graphics\\B card\\I .\\O", "opinion_tags": "I\\O seem\\O to\\O be\\O having\\O repeat\\O problems\\O as\\O the\\O Mother\\O Board\\O in\\O this\\O one\\O is\\O diagnosed\\O as\\O faulty\\B ,\\O related\\O to\\O the\\O graphics\\O card\\O .\\O", "sentiment": "negative"}]}, {"id": "946:1", "sentence": "It also comes with 4G of RAM but if you 're like me you want to max that out so I immediately put 8G of RAM in her and I 've never used a computer that performs better .", "postag": ["PRP", "RB", "VBZ", "IN", "NNP", "IN", "NNP", "CC", "IN", "PRP", "VBP", "IN", "PRP", "PRP", "VBP", "TO", "VB", "DT", "RP", "RB", "PRP", "RB", "VBP", "NNP", "IN", "NNP", "IN", "PRP", "CC", "PRP", "VBP", "RB", "VBN", "DT", "NN", "WDT", "VBZ", "JJR", "."], "head": [3, 3, 0, 5, 3, 7, 5, 15, 13, 13, 13, 13, 15, 15, 3, 17, 15, 17, 17, 23, 23, 23, 3, 23, 26, 24, 28, 23, 33, 33, 33, 33, 3, 35, 33, 37, 35, 37, 3], "deprel": ["nsubj", "advmod", "root", "case", "obl", "case", "nmod", "cc", "mark", "nsubj", "cop", "case", "advcl", "nsubj", "conj", "mark", "xcomp", "obj", "compound:prt", "advmod", "nsubj", "advmod", "parataxis", "obj", "case", "nmod", "case", "obl", "cc", "nsubj", "aux", "advmod", "conj", "det", "obj", "nsubj", "acl:relcl", "advmod", "punct"], "triples": [{"uid": "946:1-0", "target_tags": "It\\O also\\O comes\\O with\\O 4G\\O of\\O RAM\\O but\\O if\\O you\\O 're\\O like\\O me\\O you\\O want\\O to\\O max\\O that\\O out\\O so\\O I\\O immediately\\O put\\O 8G\\O of\\O RAM\\O in\\O her\\O and\\O I\\O 've\\O never\\O used\\O a\\O computer\\O that\\O performs\\B better\\O .\\O", "opinion_tags": "It\\O also\\O comes\\O with\\O 4G\\O of\\O RAM\\O but\\O if\\O you\\O 're\\O like\\O me\\O you\\O want\\O to\\O max\\O that\\O out\\O so\\O I\\O immediately\\O put\\O 8G\\O of\\O RAM\\O in\\O her\\O and\\O I\\O 've\\O never\\O used\\O a\\O computer\\O that\\O performs\\O better\\B .\\O", "sentiment": "positive"}]}, {"id": "184:1", "sentence": "This computer is also awesome for my sons virtual home schooling .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "IN", "PRP$", "NNS", "JJ", "NN", "NN", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 11, 11, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "nmod:poss", "obl", "amod", "compound", "obl", "punct"], "triples": [{"uid": "184:1-0", "target_tags": "This\\O computer\\O is\\O also\\O awesome\\O for\\O my\\O sons\\O virtual\\B home\\I schooling\\I .\\O", "opinion_tags": "This\\O computer\\O is\\O also\\O awesome\\B for\\O my\\O sons\\O virtual\\O home\\O schooling\\O .\\O", "sentiment": "positive"}]}, {"id": "786:31", "sentence": "Cost is more as compared to other brands .", "postag": ["NN", "VBZ", "JJR", "IN", "VBN", "IN", "JJ", "NNS", "."], "head": [3, 3, 0, 5, 3, 8, 8, 5, 3], "deprel": ["nsubj", "cop", "root", "mark", "advcl", "case", "amod", "obl", "punct"], "triples": [{"uid": "786:31-0", "target_tags": "Cost\\B is\\O more\\O as\\O compared\\O to\\O other\\O brands\\O .\\O", "opinion_tags": "Cost\\O is\\O more\\B as\\O compared\\O to\\O other\\O brands\\O .\\O", "sentiment": "negative"}]}, {"id": "932:1", "sentence": "also ... - excellent operating system -- size and weight for optimal mobility -- excellent durability of the battery -- the functions provided by the trackpad is unmatched by any other brand-", "postag": ["RB", ",", ",", "JJ", "NN", "NN", ",", "NN", "CC", "NN", "IN", "JJ", "NN", ",", "JJ", "NN", "IN", "DT", "NN", ",", "DT", "NNS", "VBN", "IN", "DT", "NN", "VBZ", "JJ", "IN", "DT", "JJ", "."], "head": [28, 6, 6, 6, 6, 28, 6, 6, 10, 8, 13, 13, 8, 6, 16, 6, 19, 19, 16, 6, 22, 28, 22, 26, 26, 23, 28, 0, 31, 31, 28, 28], "deprel": ["advmod", "punct", "punct", "amod", "compound", "nsubj", "punct", "appos", "cc", "conj", "case", "amod", "nmod", "punct", "amod", "appos", "case", "det", "nmod", "punct", "det", "nsubj", "acl", "case", "det", "obl", "cop", "root", "case", "det", "obl", "punct"], "triples": [{"uid": "932:1-0", "target_tags": "also\\O ...\\O -\\O excellent\\O operating\\B system\\I --\\O size\\O and\\O weight\\O for\\O optimal\\O mobility\\O --\\O excellent\\O durability\\O of\\O the\\O battery\\O --\\O the\\O functions\\O provided\\O by\\O the\\O trackpad\\O is\\O unmatched\\O by\\O any\\O other\\O brand-\\O", "opinion_tags": "also\\O ...\\O -\\O excellent\\B operating\\O system\\O --\\O size\\O and\\O weight\\O for\\O optimal\\O mobility\\O --\\O excellent\\O durability\\O of\\O the\\O battery\\O --\\O the\\O functions\\O provided\\O by\\O the\\O trackpad\\O is\\O unmatched\\O by\\O any\\O other\\O brand-\\O", "sentiment": "positive"}, {"uid": "932:1-1", "target_tags": "also\\O ...\\O -\\O excellent\\O operating\\O system\\O --\\O size\\B and\\O weight\\O for\\O optimal\\O mobility\\O --\\O excellent\\O durability\\O of\\O the\\O battery\\O --\\O the\\O functions\\O provided\\O by\\O the\\O trackpad\\O is\\O unmatched\\O by\\O any\\O other\\O brand-\\O", "opinion_tags": "also\\O ...\\O -\\O excellent\\B operating\\O system\\O --\\O size\\O and\\O weight\\O for\\O optimal\\O mobility\\O --\\O excellent\\O durability\\O of\\O the\\O battery\\O --\\O the\\O functions\\O provided\\O by\\O the\\O trackpad\\O is\\O unmatched\\O by\\O any\\O other\\O brand-\\O", "sentiment": "positive"}, {"uid": "932:1-2", "target_tags": "also\\O ...\\O -\\O excellent\\O operating\\O system\\O --\\O size\\O and\\O weight\\B for\\O optimal\\O mobility\\O --\\O excellent\\O durability\\O of\\O the\\O battery\\O --\\O the\\O functions\\O provided\\O by\\O the\\O trackpad\\O is\\O unmatched\\O by\\O any\\O other\\O brand-\\O", "opinion_tags": "also\\O ...\\O -\\O excellent\\B operating\\O system\\O --\\O size\\O and\\O weight\\O for\\O optimal\\O mobility\\O --\\O excellent\\O durability\\O of\\O the\\O battery\\O --\\O the\\O functions\\O provided\\O by\\O the\\O trackpad\\O is\\O unmatched\\O by\\O any\\O other\\O brand-\\O", "sentiment": "positive"}, {"uid": "932:1-3", "target_tags": "also\\O ...\\O -\\O excellent\\O operating\\O system\\O --\\O size\\O and\\O weight\\O for\\O optimal\\O mobility\\B --\\O excellent\\O durability\\O of\\O the\\O battery\\O --\\O the\\O functions\\O provided\\O by\\O the\\O trackpad\\O is\\O unmatched\\O by\\O any\\O other\\O brand-\\O", "opinion_tags": "also\\O ...\\O -\\O excellent\\O operating\\O system\\O --\\O size\\O and\\O weight\\O for\\O optimal\\B mobility\\O --\\O excellent\\O durability\\O of\\O the\\O battery\\O --\\O the\\O functions\\O provided\\O by\\O the\\O trackpad\\O is\\O unmatched\\O by\\O any\\O other\\O brand-\\O", "sentiment": "positive"}, {"uid": "932:1-4", "target_tags": "also\\O ...\\O -\\O excellent\\O operating\\O system\\O --\\O size\\O and\\O weight\\O for\\O optimal\\O mobility\\O --\\O excellent\\O durability\\B of\\I the\\I battery\\I --\\O the\\O functions\\O provided\\O by\\O the\\O trackpad\\O is\\O unmatched\\O by\\O any\\O other\\O brand-\\O", "opinion_tags": "also\\O ...\\O -\\O excellent\\O operating\\O system\\O --\\O size\\O and\\O weight\\O for\\O optimal\\O mobility\\O --\\O excellent\\B durability\\O of\\O the\\O battery\\O --\\O the\\O functions\\O provided\\O by\\O the\\O trackpad\\O is\\O unmatched\\O by\\O any\\O other\\O brand-\\O", "sentiment": "positive"}, {"uid": "932:1-5", "target_tags": "also\\O ...\\O -\\O excellent\\O operating\\O system\\O --\\O size\\O and\\O weight\\O for\\O optimal\\O mobility\\O --\\O excellent\\O durability\\O of\\O the\\O battery\\O --\\O the\\O functions\\B provided\\I by\\I the\\I trackpad\\I is\\O unmatched\\O by\\O any\\O other\\O brand-\\O", "opinion_tags": "also\\O ...\\O -\\O excellent\\O operating\\O system\\O --\\O size\\O and\\O weight\\O for\\O optimal\\O mobility\\O --\\O excellent\\O durability\\O of\\O the\\O battery\\O --\\O the\\O functions\\O provided\\O by\\O the\\O trackpad\\O is\\O unmatched\\B by\\O any\\O other\\O brand-\\O", "sentiment": "positive"}]}, {"id": "256:1", "sentence": "This hardware seems to be better than the iMac in that it is n't $ 1400 and smaller .", "postag": ["DT", "NN", "VBZ", "TO", "VB", "JJR", "IN", "DT", "NNP", "IN", "IN", "PRP", "VBZ", "RB", "$", "CD", "CC", "JJR", "."], "head": [2, 3, 0, 6, 6, 3, 9, 9, 6, 15, 15, 15, 15, 15, 6, 15, 18, 15, 3], "deprel": ["det", "nsubj", "root", "mark", "cop", "xcomp", "case", "det", "obl", "mark", "mark", "nsubj", "cop", "advmod", "advcl", "nummod", "cc", "conj", "punct"], "triples": [{"uid": "256:1-0", "target_tags": "This\\O hardware\\B seems\\O to\\O be\\O better\\O than\\O the\\O iMac\\O in\\O that\\O it\\O is\\O n't\\O $\\O 1400\\O and\\O smaller\\O .\\O", "opinion_tags": "This\\O hardware\\O seems\\O to\\O be\\O better\\B than\\O the\\O iMac\\O in\\O that\\O it\\O is\\O n't\\O $\\O 1400\\O and\\O smaller\\O .\\O", "sentiment": "positive"}]}, {"id": "520:1", "sentence": "I 've had it for about 2 months now and found no issues with software or updates .", "postag": ["PRP", "VBP", "VBN", "PRP", "IN", "RB", "CD", "NNS", "RB", "CC", "VBD", "DT", "NNS", "IN", "NN", "CC", "NNS", "."], "head": [3, 3, 0, 3, 8, 7, 8, 3, 3, 11, 3, 13, 11, 15, 13, 17, 15, 3], "deprel": ["nsubj", "aux", "root", "obj", "case", "advmod", "nummod", "obl", "advmod", "cc", "conj", "det", "obj", "case", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "520:1-0", "target_tags": "I\\O 've\\O had\\O it\\O for\\O about\\O 2\\O months\\O now\\O and\\O found\\O no\\O issues\\O with\\O software\\B or\\O updates\\O .\\O", "opinion_tags": "I\\O 've\\O had\\O it\\O for\\O about\\O 2\\O months\\O now\\O and\\O found\\O no\\B issues\\I with\\O software\\O or\\O updates\\O .\\O", "sentiment": "neutral"}, {"uid": "520:1-1", "target_tags": "I\\O 've\\O had\\O it\\O for\\O about\\O 2\\O months\\O now\\O and\\O found\\O no\\O issues\\O with\\O software\\O or\\O updates\\B .\\O", "opinion_tags": "I\\O 've\\O had\\O it\\O for\\O about\\O 2\\O months\\O now\\O and\\O found\\O no\\B issues\\I with\\O software\\O or\\O updates\\O .\\O", "sentiment": "neutral"}]}, {"id": "306:2", "sentence": "the latest version does not have a disc drive .", "postag": ["DT", "JJS", "NN", "VBZ", "RB", "VB", "DT", "NN", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 9, 9, 6, 6], "deprel": ["det", "amod", "nsubj", "aux", "advmod", "root", "det", "compound", "obj", "punct"], "triples": [{"uid": "306:2-0", "target_tags": "the\\O latest\\O version\\O does\\O not\\O have\\O a\\O disc\\B drive\\I .\\O", "opinion_tags": "the\\O latest\\O version\\O does\\O not\\B have\\I a\\O disc\\O drive\\O .\\O", "sentiment": "neutral"}]}, {"id": "280:11", "sentence": "Screen - although some people might complain about low res which I think is ridiculous .", "postag": ["NN", ",", "IN", "DT", "NNS", "MD", "VB", "IN", "JJ", "NN", "WDT", "PRP", "VBP", "VBZ", "JJ", "."], "head": [0, 1, 7, 5, 7, 7, 1, 10, 10, 7, 13, 13, 10, 15, 13, 1], "deprel": ["root", "punct", "mark", "det", "nsubj", "aux", "appos", "case", "amod", "obl", "obj", "nsubj", "acl:relcl", "cop", "ccomp", "punct"], "triples": [{"uid": "280:11-0", "target_tags": "Screen\\B -\\O although\\O some\\O people\\O might\\O complain\\O about\\O low\\O res\\O which\\O I\\O think\\O is\\O ridiculous\\O .\\O", "opinion_tags": "Screen\\O -\\O although\\O some\\O people\\O might\\O complain\\B about\\O low\\O res\\O which\\O I\\O think\\O is\\O ridiculous\\B .\\O", "sentiment": "positive"}, {"uid": "280:11-1", "target_tags": "Screen\\O -\\O although\\O some\\O people\\O might\\O complain\\O about\\O low\\O res\\B which\\O I\\O think\\O is\\O ridiculous\\O .\\O", "opinion_tags": "Screen\\O -\\O although\\O some\\O people\\O might\\O complain\\O about\\O low\\B res\\O which\\O I\\O think\\O is\\O ridiculous\\B .\\O", "sentiment": "positive"}]}]