[{"id": "507937:1", "sentence": "delicious bagels , especially when right out of the oven .", "postag": ["JJ", "NNS", ",", "RB", "WRB", "RB", "IN", "IN", "DT", "NN", "."], "head": [2, 0, 2, 10, 10, 10, 10, 10, 10, 2, 2], "deprel": ["amod", "root", "punct", "advmod", "mark", "advmod", "case", "case", "det", "advcl", "punct"], "triples": [{"uid": "507937:1-0", "target_tags": "delicious\\O bagels\\B ,\\O especially\\O when\\O right\\O out\\O of\\O the\\O oven\\O .\\O", "opinion_tags": "delicious\\B bagels\\O ,\\O especially\\O when\\O right\\O out\\O of\\O the\\O oven\\O .\\O", "sentiment": "positive"}]}, {"id": "553097:4", "sentence": "But that was n't the icing on the cake : a tiramisu that resembled nothing I have ever had .", "postag": ["CC", "DT", "VBD", "RB", "DT", "NN", "IN", "DT", "NN", ":", "DT", "NN", "WDT", "VBD", "NN", "PRP", "VBP", "RB", "VBN", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 12, 12, 6, 14, 12, 14, 19, 19, 19, 15, 6], "deprel": ["cc", "nsubj", "cop", "advmod", "det", "root", "case", "det", "nmod", "punct", "det", "appos", "nsubj", "acl:relcl", "obj", "nsubj", "aux", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "553097:4-0", "target_tags": "But\\O that\\O was\\O n't\\O the\\O icing\\O on\\O the\\O cake\\O :\\O a\\O tiramisu\\B that\\O resembled\\O nothing\\O I\\O have\\O ever\\O had\\O .\\O", "opinion_tags": "But\\O that\\O was\\O n't\\O the\\O icing\\O on\\O the\\O cake\\O :\\O a\\O tiramisu\\O that\\O resembled\\O nothing\\B I\\O have\\O ever\\O had\\O .\\O", "sentiment": "negative"}]}, {"id": "680345:0", "sentence": "This is an amazing place to try some roti rolls .", "postag": ["DT", "VBZ", "DT", "JJ", "NN", "TO", "VB", "DT", "NN", "NNS", "."], "head": [5, 5, 5, 5, 0, 7, 5, 10, 10, 7, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "det", "compound", "obj", "punct"], "triples": [{"uid": "680345:0-0", "target_tags": "This\\O is\\O an\\O amazing\\O place\\O to\\O try\\O some\\O roti\\B rolls\\I .\\O", "opinion_tags": "This\\O is\\O an\\O amazing\\O place\\O to\\O try\\B some\\O roti\\O rolls\\O .\\O", "sentiment": "positive"}]}, {"id": "624715:4", "sentence": "Just straight up cheap , good food .", "postag": ["RB", "RB", "RP", "JJ", ",", "JJ", "NN", "."], "head": [7, 7, 7, 7, 7, 7, 0, 7], "deprel": ["advmod", "advmod", "advmod", "amod", "punct", "amod", "root", "punct"], "triples": [{"uid": "624715:4-0", "target_tags": "Just\\O straight\\O up\\O cheap\\O ,\\O good\\O food\\B .\\O", "opinion_tags": "Just\\O straight\\O up\\O cheap\\B ,\\O good\\B food\\O .\\O", "sentiment": "positive"}]}, {"id": "565344:2", "sentence": "The service was fast and friendly and the food was very tasty and they had the best hot sauce to add to your meals .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "JJ", "CC", "DT", "NN", "VBD", "RB", "JJ", "CC", "PRP", "VBD", "DT", "JJS", "JJ", "NN", "TO", "VB", "IN", "PRP$", "NNS", "."], "head": [2, 4, 4, 0, 6, 4, 12, 9, 12, 12, 12, 4, 15, 15, 12, 19, 19, 19, 15, 21, 19, 24, 24, 21, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "cc", "det", "nsubj", "cop", "advmod", "conj", "cc", "nsubj", "conj", "det", "amod", "amod", "obj", "mark", "acl", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "565344:2-0", "target_tags": "The\\O service\\B was\\O fast\\O and\\O friendly\\O and\\O the\\O food\\O was\\O very\\O tasty\\O and\\O they\\O had\\O the\\O best\\O hot\\O sauce\\O to\\O add\\O to\\O your\\O meals\\O .\\O", "opinion_tags": "The\\O service\\O was\\O fast\\B and\\O friendly\\B and\\O the\\O food\\O was\\O very\\O tasty\\O and\\O they\\O had\\O the\\O best\\O hot\\O sauce\\O to\\O add\\O to\\O your\\O meals\\O .\\O", "sentiment": "positive"}, {"uid": "565344:2-1", "target_tags": "The\\O service\\O was\\O fast\\O and\\O friendly\\O and\\O the\\O food\\B was\\O very\\O tasty\\O and\\O they\\O had\\O the\\O best\\O hot\\O sauce\\O to\\O add\\O to\\O your\\O meals\\O .\\O", "opinion_tags": "The\\O service\\O was\\O fast\\O and\\O friendly\\O and\\O the\\O food\\O was\\O very\\O tasty\\B and\\O they\\O had\\O the\\O best\\O hot\\O sauce\\O to\\O add\\O to\\O your\\O meals\\O .\\O", "sentiment": "positive"}, {"uid": "565344:2-2", "target_tags": "The\\O service\\O was\\O fast\\O and\\O friendly\\O and\\O the\\O food\\O was\\O very\\O tasty\\O and\\O they\\O had\\O the\\O best\\O hot\\B sauce\\I to\\O add\\O to\\O your\\O meals\\O .\\O", "opinion_tags": "The\\O service\\O was\\O fast\\O and\\O friendly\\O and\\O the\\O food\\O was\\O very\\O tasty\\O and\\O they\\O had\\O the\\O best\\B hot\\O sauce\\O to\\O add\\O to\\O your\\O meals\\O .\\O", "sentiment": "positive"}]}, {"id": "1496205:3", "sentence": "Try green curry with vegetables .", "postag": ["VB", "JJ", "NN", "IN", "NNS", "."], "head": [0, 3, 1, 5, 3, 1], "deprel": ["root", "amod", "obj", "case", "nmod", "punct"], "triples": [{"uid": "1496205:3-0", "target_tags": "Try\\O green\\B curry\\I with\\I vegetables\\I .\\O", "opinion_tags": "Try\\B green\\O curry\\O with\\O vegetables\\O .\\O", "sentiment": "positive"}]}, {"id": "957145:1", "sentence": "I have been coming here for years and have nothing but good things to say about the service and the great staff at La Lanterna .", "postag": ["PRP", "VBP", "VBN", "VBG", "RB", "IN", "NNS", "CC", "VBP", "NN", "CC", "JJ", "NNS", "TO", "VB", "IN", "DT", "NN", "CC", "DT", "JJ", "NN", "IN", "NNP", "NNP", "."], "head": [4, 4, 4, 0, 4, 7, 4, 9, 4, 9, 13, 13, 10, 15, 13, 18, 18, 15, 22, 22, 22, 18, 24, 22, 24, 4], "deprel": ["nsubj", "aux", "aux", "root", "advmod", "case", "obl", "cc", "conj", "obj", "cc", "amod", "conj", "mark", "acl", "case", "det", "obl", "cc", "det", "amod", "conj", "case", "nmod", "flat", "punct"], "triples": [{"uid": "957145:1-0", "target_tags": "I\\O have\\O been\\O coming\\O here\\O for\\O years\\O and\\O have\\O nothing\\O but\\O good\\O things\\O to\\O say\\O about\\O the\\O service\\B and\\O the\\O great\\O staff\\O at\\O La\\O Lanterna\\O .\\O", "opinion_tags": "I\\O have\\O been\\O coming\\O here\\O for\\O years\\O and\\O have\\O nothing\\O but\\O good\\B things\\O to\\O say\\O about\\O the\\O service\\O and\\O the\\O great\\O staff\\O at\\O La\\O Lanterna\\O .\\O", "sentiment": "positive"}, {"uid": "957145:1-1", "target_tags": "I\\O have\\O been\\O coming\\O here\\O for\\O years\\O and\\O have\\O nothing\\O but\\O good\\O things\\O to\\O say\\O about\\O the\\O service\\O and\\O the\\O great\\O staff\\B at\\O La\\O Lanterna\\O .\\O", "opinion_tags": "I\\O have\\O been\\O coming\\O here\\O for\\O years\\O and\\O have\\O nothing\\O but\\O good\\O things\\O to\\O say\\O about\\O the\\O service\\O and\\O the\\O great\\B staff\\O at\\O La\\O Lanterna\\O .\\O", "sentiment": "positive"}]}, {"id": "1649025:2", "sentence": "The food is so cheap and the waiters are nice .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NNS", "VBP", "JJ", "."], "head": [2, 5, 5, 5, 0, 10, 8, 10, 10, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "1649025:2-0", "target_tags": "The\\O food\\B is\\O so\\O cheap\\O and\\O the\\O waiters\\O are\\O nice\\O .\\O", "opinion_tags": "The\\O food\\O is\\O so\\O cheap\\B and\\O the\\O waiters\\O are\\O nice\\O .\\O", "sentiment": "positive"}, {"uid": "1649025:2-1", "target_tags": "The\\O food\\O is\\O so\\O cheap\\O and\\O the\\O waiters\\B are\\O nice\\O .\\O", "opinion_tags": "The\\O food\\O is\\O so\\O cheap\\O and\\O the\\O waiters\\O are\\O nice\\B .\\O", "sentiment": "positive"}]}, {"id": "1303888:0", "sentence": "All the staff is absolutely professional ! !", "postag": ["PDT", "DT", "NN", "VBZ", "RB", "JJ", ".", "."], "head": [3, 3, 6, 6, 6, 0, 6, 6], "deprel": ["det:predet", "det", "nsubj", "cop", "advmod", "root", "punct", "punct"], "triples": [{"uid": "1303888:0-0", "target_tags": "All\\O the\\O staff\\B is\\O absolutely\\O professional\\O !\\O !\\O", "opinion_tags": "All\\O the\\O staff\\O is\\O absolutely\\O professional\\B !\\O !\\O", "sentiment": "positive"}]}, {"id": "1058221:3", "sentence": "however , it 's the service that leaves a bad taste in my mouth .", "postag": ["RB", ",", "PRP", "VBZ", "DT", "NN", "WDT", "VBZ", "DT", "JJ", "NN", "IN", "PRP$", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 8, 6, 11, 11, 8, 14, 14, 8, 6], "deprel": ["advmod", "punct", "nsubj", "cop", "det", "root", "nsubj", "acl:relcl", "det", "amod", "obj", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "1058221:3-0", "target_tags": "however\\O ,\\O it\\O 's\\O the\\O service\\B that\\O leaves\\O a\\O bad\\O taste\\O in\\O my\\O mouth\\O .\\O", "opinion_tags": "however\\O ,\\O it\\O 's\\O the\\O service\\O that\\O leaves\\O a\\O bad\\B taste\\I in\\O my\\O mouth\\O .\\O", "sentiment": "negative"}]}, {"id": "1713185:3", "sentence": "If you want good authentic Thai this place is not the place to go .", "postag": ["IN", "PRP", "VBP", "JJ", "JJ", "NNP", "DT", "NN", "VBZ", "RB", "DT", "NN", "TO", "VB", "."], "head": [3, 3, 12, 6, 6, 3, 8, 12, 12, 12, 12, 0, 14, 12, 12], "deprel": ["mark", "nsubj", "advcl", "amod", "amod", "obj", "det", "nsubj", "cop", "advmod", "det", "root", "mark", "acl", "punct"], "triples": [{"uid": "1713185:3-0", "target_tags": "If\\O you\\O want\\O good\\O authentic\\O Thai\\B this\\O place\\O is\\O not\\O the\\O place\\O to\\O go\\O .\\O", "opinion_tags": "If\\O you\\O want\\O good\\B authentic\\B Thai\\O this\\O place\\O is\\O not\\O the\\O place\\O to\\O go\\O .\\O", "sentiment": "negative"}]}, {"id": "436868:3", "sentence": "Calling the place Hampton Chutney Co. does warn you that these folks offer more style than substance , but in this unattractive room with unhelpful clerks there was a dearth of the former too .", "postag": ["VBG", "DT", "NN", "NNP", "NNP", "NNP", "VBZ", "VB", "PRP", "IN", "DT", "NNS", "VBP", "JJR", "NN", "IN", "NN", ",", "CC", "IN", "DT", "JJ", "NN", "IN", "JJ", "NNS", "EX", "VBD", "DT", "NN", "IN", "DT", "JJ", "RB", "."], "head": [8, 3, 1, 6, 6, 3, 8, 0, 8, 13, 12, 13, 8, 15, 13, 17, 15, 28, 28, 23, 23, 23, 28, 26, 26, 23, 28, 8, 30, 28, 33, 33, 30, 28, 8], "deprel": ["csubj", "det", "obj", "compound", "compound", "appos", "aux", "root", "obj", "mark", "det", "nsubj", "ccomp", "amod", "obj", "case", "nmod", "punct", "cc", "case", "det", "amod", "obl", "case", "amod", "nmod", "expl", "conj", "det", "nsubj", "case", "det", "nmod", "advmod", "punct"], "triples": [{"uid": "436868:3-0", "target_tags": "Calling\\O the\\O place\\B Hampton\\O Chutney\\O Co.\\O does\\O warn\\O you\\O that\\O these\\O folks\\O offer\\O more\\O style\\O than\\O substance\\O ,\\O but\\O in\\O this\\O unattractive\\O room\\O with\\O unhelpful\\O clerks\\O there\\O was\\O a\\O dearth\\O of\\O the\\O former\\O too\\O .\\O", "opinion_tags": "Calling\\O the\\O place\\O Hampton\\O Chutney\\O Co.\\O does\\O warn\\O you\\O that\\O these\\O folks\\O offer\\O more\\O style\\O than\\O substance\\O ,\\O but\\O in\\O this\\O unattractive\\B room\\O with\\O unhelpful\\O clerks\\O there\\O was\\O a\\O dearth\\O of\\O the\\O former\\O too\\O .\\O", "sentiment": "negative"}, {"uid": "436868:3-1", "target_tags": "Calling\\O the\\O place\\O Hampton\\O Chutney\\O Co.\\O does\\O warn\\O you\\O that\\O these\\O folks\\O offer\\O more\\O style\\O than\\O substance\\O ,\\O but\\O in\\O this\\O unattractive\\O room\\B with\\O unhelpful\\O clerks\\O there\\O was\\O a\\O dearth\\O of\\O the\\O former\\O too\\O .\\O", "opinion_tags": "Calling\\O the\\O place\\O Hampton\\O Chutney\\O Co.\\O does\\O warn\\O you\\O that\\O these\\O folks\\O offer\\O more\\O style\\O than\\O substance\\O ,\\O but\\O in\\O this\\O unattractive\\B room\\O with\\O unhelpful\\O clerks\\O there\\O was\\O a\\O dearth\\O of\\O the\\O former\\O too\\O .\\O", "sentiment": "negative"}, {"uid": "436868:3-2", "target_tags": "Calling\\O the\\O place\\O Hampton\\O Chutney\\O Co.\\O does\\O warn\\O you\\O that\\O these\\O folks\\O offer\\O more\\O style\\O than\\O substance\\O ,\\O but\\O in\\O this\\O unattractive\\O room\\O with\\O unhelpful\\O clerks\\B there\\O was\\O a\\O dearth\\O of\\O the\\O former\\O too\\O .\\O", "opinion_tags": "Calling\\O the\\O place\\O Hampton\\O Chutney\\O Co.\\O does\\O warn\\O you\\O that\\O these\\O folks\\O offer\\O more\\O style\\O than\\O substance\\O ,\\O but\\O in\\O this\\O unattractive\\O room\\O with\\O unhelpful\\B clerks\\O there\\O was\\O a\\O dearth\\O of\\O the\\O former\\O too\\O .\\O", "sentiment": "negative"}]}, {"id": "521916:0", "sentence": "Downtown Dinner 2002 - Prixe fix : Appetizers were ok , waiter gave me poor suggestion ... try the potato stuff kanish best one .", "postag": ["NN", "NN", "CD", ",", "NNP", "NN", ":", "NNS", "VBD", "JJ", ",", "NN", "VBD", "PRP", "JJ", "NN", ",", "VB", "DT", "NN", "NN", "IN", "JJS", "NN", "."], "head": [2, 0, 2, 2, 6, 2, 2, 10, 10, 2, 10, 13, 10, 13, 16, 13, 10, 2, 21, 21, 18, 24, 24, 18, 2], "deprel": ["compound", "root", "nummod", "punct", "compound", "parataxis", "punct", "nsubj", "cop", "parataxis", "punct", "nsubj", "conj", "i<PERSON><PERSON>", "amod", "obj", "punct", "parataxis", "det", "compound", "obj", "case", "amod", "obl", "punct"], "triples": [{"uid": "521916:0-0", "target_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\B were\\O ok\\O ,\\O waiter\\O gave\\O me\\O poor\\O suggestion\\O ...\\O try\\O the\\O potato\\O stuff\\O kanish\\O best\\O one\\O .\\O", "opinion_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\O were\\O ok\\B ,\\O waiter\\O gave\\O me\\O poor\\O suggestion\\O ...\\O try\\O the\\O potato\\O stuff\\O kanish\\O best\\O one\\O .\\O", "sentiment": "neutral"}, {"uid": "521916:0-1", "target_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\O were\\O ok\\O ,\\O waiter\\B gave\\O me\\O poor\\O suggestion\\O ...\\O try\\O the\\O potato\\O stuff\\O kanish\\O best\\O one\\O .\\O", "opinion_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\O were\\O ok\\O ,\\O waiter\\O gave\\O me\\O poor\\B suggestion\\O ...\\O try\\O the\\O potato\\O stuff\\O kanish\\O best\\O one\\O .\\O", "sentiment": "negative"}, {"uid": "521916:0-2", "target_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\O were\\O ok\\O ,\\O waiter\\O gave\\O me\\O poor\\O suggestion\\O ...\\O try\\O the\\O potato\\B stuff\\I kanish\\I best\\O one\\O .\\O", "opinion_tags": "Downtown\\O Dinner\\O 2002\\O -\\O Prixe\\O fix\\O :\\O Appetizers\\O were\\O ok\\O ,\\O waiter\\O gave\\O me\\O poor\\O suggestion\\O ...\\O try\\B the\\O potato\\O stuff\\O kanish\\O best\\B one\\O .\\O", "sentiment": "positive"}]}, {"id": "1335154:4", "sentence": "I did n't complain , I liked the atmosphere so much .", "postag": ["PRP", "VBD", "RB", "VB", ",", "PRP", "VBD", "DT", "NN", "RB", "JJ", "."], "head": [4, 4, 4, 0, 4, 7, 4, 9, 7, 11, 7, 4], "deprel": ["nsubj", "aux", "advmod", "root", "punct", "nsubj", "parataxis", "det", "obj", "advmod", "advmod", "punct"], "triples": [{"uid": "1335154:4-0", "target_tags": "I\\O did\\O n't\\O complain\\O ,\\O I\\O liked\\O the\\O atmosphere\\B so\\O much\\O .\\O", "opinion_tags": "I\\O did\\O n't\\O complain\\O ,\\O I\\O liked\\B the\\O atmosphere\\O so\\O much\\O .\\O", "sentiment": "positive"}]}, {"id": "541532:2", "sentence": "Food was very good , but not what I would consider out of this world .", "postag": ["NN", "VBD", "RB", "JJ", ",", "CC", "RB", "WP", "PRP", "MD", "VB", "IN", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 11, 11, 8, 15, 15, 15, 11, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct", "cc", "cc", "conj", "nsubj", "aux", "acl:relcl", "case", "case", "det", "obl", "punct"], "triples": [{"uid": "541532:2-0", "target_tags": "Food\\B was\\O very\\O good\\O ,\\O but\\O not\\O what\\O I\\O would\\O consider\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "Food\\O was\\O very\\O good\\B ,\\O but\\O not\\O what\\O I\\O would\\O consider\\O out\\O of\\O this\\O world\\O .\\O", "sentiment": "neutral"}]}, {"id": "1131595:2", "sentence": "The hostess is rude to the point of being offensive .", "postag": ["DT", "NN", "VBZ", "JJ", "IN", "DT", "NN", "IN", "VBG", "JJ", "."], "head": [2, 4, 4, 0, 7, 7, 4, 10, 10, 7, 4], "deprel": ["det", "nsubj", "cop", "root", "case", "det", "obl", "mark", "cop", "acl", "punct"], "triples": [{"uid": "1131595:2-0", "target_tags": "The\\O hostess\\B is\\O rude\\O to\\O the\\O point\\O of\\O being\\O offensive\\O .\\O", "opinion_tags": "The\\O hostess\\O is\\O rude\\B to\\O the\\O point\\O of\\O being\\O offensive\\B .\\O", "sentiment": "negative"}]}, {"id": "1055910:3", "sentence": "Service friendly and attentive .", "postag": ["NN", "JJ", "CC", "JJ", "."], "head": [2, 0, 4, 2, 2], "deprel": ["nsubj", "root", "cc", "conj", "punct"], "triples": [{"uid": "1055910:3-0", "target_tags": "Service\\B friendly\\O and\\O attentive\\O .\\O", "opinion_tags": "Service\\O friendly\\B and\\O attentive\\B .\\O", "sentiment": "positive"}]}, {"id": "1710746:0", "sentence": "I got an excellent piece of cheesecake and we had several other nice pastries .", "postag": ["PRP", "VBD", "DT", "JJ", "NN", "IN", "NN", "CC", "PRP", "VBD", "JJ", "JJ", "JJ", "NNS", "."], "head": [2, 0, 5, 5, 2, 7, 5, 10, 10, 2, 14, 14, 14, 10, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "case", "nmod", "cc", "nsubj", "conj", "amod", "amod", "amod", "obj", "punct"], "triples": [{"uid": "1710746:0-0", "target_tags": "I\\O got\\O an\\O excellent\\O piece\\O of\\O cheesecake\\B and\\O we\\O had\\O several\\O other\\O nice\\O pastries\\O .\\O", "opinion_tags": "I\\O got\\O an\\O excellent\\B piece\\O of\\O cheesecake\\O and\\O we\\O had\\O several\\O other\\O nice\\O pastries\\O .\\O", "sentiment": "positive"}, {"uid": "1710746:0-1", "target_tags": "I\\O got\\O an\\O excellent\\O piece\\O of\\O cheesecake\\O and\\O we\\O had\\O several\\O other\\O nice\\O pastries\\B .\\O", "opinion_tags": "I\\O got\\O an\\O excellent\\O piece\\O of\\O cheesecake\\O and\\O we\\O had\\O several\\O other\\O nice\\B pastries\\O .\\O", "sentiment": "positive"}]}, {"id": "1253117:1", "sentence": "The pizza is delicious - they use fresh mozzarella instead of the cheap , frozen , shredded cheese common to most pizzaria 's .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "PRP", "VBP", "JJ", "NN", "RB", "IN", "DT", "JJ", ",", "JJ", ",", "VBN", "NN", "JJ", "IN", "JJS", "NN", "POS", "."], "head": [2, 4, 4, 0, 4, 7, 4, 9, 7, 18, 10, 18, 18, 15, 18, 18, 18, 7, 18, 22, 22, 19, 22, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "parataxis", "amod", "obj", "case", "fixed", "det", "amod", "punct", "amod", "punct", "amod", "obl", "amod", "case", "amod", "obl", "case", "punct"], "triples": [{"uid": "1253117:1-0", "target_tags": "The\\O pizza\\B is\\O delicious\\O -\\O they\\O use\\O fresh\\O mozzarella\\O instead\\O of\\O the\\O cheap\\O ,\\O frozen\\O ,\\O shredded\\O cheese\\O common\\O to\\O most\\O pizzaria\\O 's\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O delicious\\B -\\O they\\O use\\O fresh\\O mozzarella\\O instead\\O of\\O the\\O cheap\\O ,\\O frozen\\O ,\\O shredded\\O cheese\\O common\\O to\\O most\\O pizzaria\\O 's\\O .\\O", "sentiment": "positive"}]}, {"id": "1663111:0", "sentence": "I absolutely Loved this place .", "postag": ["PRP", "RB", "VBD", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "obj", "punct"], "triples": [{"uid": "1663111:0-0", "target_tags": "I\\O absolutely\\O Loved\\O this\\O place\\B .\\O", "opinion_tags": "I\\O absolutely\\O Loved\\B this\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "1341354:1", "sentence": "Enjoyed a very nice Caesar Salad while my wife had arugula and goat cheese ... .both very tasty .", "postag": ["VBD", "DT", "RB", "JJ", "NNP", "NN", "IN", "PRP$", "NN", "VBD", "NN", "CC", "NN", "NN", ",", "CC", "RB", "JJ", "."], "head": [0, 6, 4, 6, 6, 1, 10, 9, 10, 1, 10, 14, 14, 11, 1, 18, 18, 1, 1], "deprel": ["root", "det", "advmod", "amod", "compound", "obj", "mark", "nmod:poss", "nsubj", "advcl", "obj", "cc", "compound", "conj", "punct", "cc", "advmod", "parataxis", "punct"], "triples": [{"uid": "1341354:1-0", "target_tags": "Enjoyed\\O a\\O very\\O nice\\O Caesar\\B Salad\\I while\\O my\\O wife\\O had\\O arugula\\O and\\O goat\\O cheese\\O ...\\O .both\\O very\\O tasty\\O .\\O", "opinion_tags": "Enjoyed\\B a\\O very\\O nice\\B Caesar\\O Salad\\O while\\O my\\O wife\\O had\\O arugula\\O and\\O goat\\O cheese\\O ...\\O .both\\O very\\O tasty\\O .\\O", "sentiment": "positive"}, {"uid": "1341354:1-1", "target_tags": "Enjoyed\\O a\\O very\\O nice\\O Caesar\\O Salad\\O while\\O my\\O wife\\O had\\O arugula\\B and\\I goat\\I cheese\\I ...\\O .both\\O very\\O tasty\\O .\\O", "opinion_tags": "Enjoyed\\O a\\O very\\O nice\\O Caesar\\O Salad\\O while\\O my\\O wife\\O had\\O arugula\\O and\\O goat\\O cheese\\O ...\\O .both\\O very\\O tasty\\B .\\O", "sentiment": "positive"}]}, {"id": "584025:1", "sentence": "Warm and friendly in the winter and terrific outdoor seating in the warmer months .", "postag": ["JJ", "CC", "JJ", "IN", "DT", "NN", "CC", "JJ", "JJ", "NN", "IN", "DT", "JJR", "NNS", "."], "head": [0, 3, 1, 6, 6, 1, 10, 10, 10, 1, 14, 14, 14, 10, 1], "deprel": ["root", "cc", "conj", "case", "det", "obl", "cc", "amod", "amod", "conj", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "584025:1-0", "target_tags": "Warm\\O and\\O friendly\\O in\\O the\\O winter\\O and\\O terrific\\O outdoor\\B seating\\I in\\O the\\O warmer\\O months\\O .\\O", "opinion_tags": "Warm\\O and\\O friendly\\O in\\O the\\O winter\\O and\\O terrific\\B outdoor\\O seating\\O in\\O the\\O warmer\\O months\\O .\\O", "sentiment": "positive"}]}, {"id": "1671113:0", "sentence": "I have been to <PERSON> over 5 times and I have always had a great time there .", "postag": ["PRP", "VBP", "VBN", "IN", "NNP", "RB", "CD", "NNS", "CC", "PRP", "VBP", "RB", "VBN", "DT", "JJ", "NN", "RB", "."], "head": [5, 5, 5, 5, 0, 7, 8, 5, 13, 13, 13, 13, 5, 16, 16, 13, 13, 5], "deprel": ["nsubj", "aux", "cop", "case", "root", "advmod", "nummod", "obl:tmod", "cc", "nsubj", "aux", "advmod", "conj", "det", "amod", "obj", "advmod", "punct"], "triples": [{"uid": "1671113:0-0", "target_tags": "I\\O have\\O been\\O to\\O Casimir\\B over\\O 5\\O times\\O and\\O I\\O have\\O always\\O had\\O a\\O great\\O time\\O there\\O .\\O", "opinion_tags": "I\\O have\\O been\\O to\\O Casimir\\O over\\O 5\\O times\\O and\\O I\\O have\\O always\\O had\\O a\\O great\\B time\\I there\\O .\\O", "sentiment": "positive"}]}, {"id": "1500453:1", "sentence": "This quaint and romantic trattoria is at the top of my Manhattan restaurant list .", "postag": ["DT", "JJ", "CC", "JJ", "NN", "VBZ", "IN", "DT", "NN", "IN", "PRP$", "NNP", "NN", "NN", "."], "head": [5, 5, 4, 2, 9, 9, 9, 9, 0, 14, 14, 14, 14, 9, 9], "deprel": ["det", "amod", "cc", "conj", "nsubj", "cop", "case", "det", "root", "case", "nmod:poss", "compound", "compound", "nmod", "punct"], "triples": [{"uid": "1500453:1-0", "target_tags": "This\\O quaint\\O and\\O romantic\\O trattoria\\B is\\O at\\O the\\O top\\O of\\O my\\O Manhattan\\O restaurant\\O list\\O .\\O", "opinion_tags": "This\\O quaint\\B and\\O romantic\\B trattoria\\O is\\O at\\O the\\O top\\O of\\O my\\O Manhattan\\O restaurant\\O list\\O .\\O", "sentiment": "positive"}]}, {"id": "1153034:0", "sentence": "Great pizza and fantastic service .", "postag": ["JJ", "NN", "CC", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["amod", "root", "cc", "amod", "conj", "punct"], "triples": [{"uid": "1153034:0-0", "target_tags": "Great\\O pizza\\B and\\O fantastic\\O service\\O .\\O", "opinion_tags": "Great\\B pizza\\O and\\O fantastic\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "1153034:0-1", "target_tags": "Great\\O pizza\\O and\\O fantastic\\O service\\B .\\O", "opinion_tags": "Great\\O pizza\\O and\\O fantastic\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "1199480:2", "sentence": "I 'd highly recommend it for a special occasion -- it provides and intimate setting and nice service .", "postag": ["PRP", "MD", "RB", "VB", "PRP", "IN", "DT", "JJ", "NN", ",", "PRP", "VBZ", "CC", "JJ", "NN", "CC", "JJ", "NN", "."], "head": [4, 4, 4, 0, 4, 9, 9, 9, 4, 4, 12, 4, 14, 15, 12, 18, 18, 15, 4], "deprel": ["nsubj", "aux", "advmod", "root", "obj", "case", "det", "amod", "obl", "punct", "nsubj", "parataxis", "cc", "amod", "obj", "cc", "amod", "conj", "punct"], "triples": [{"uid": "1199480:2-0", "target_tags": "I\\O 'd\\O highly\\O recommend\\O it\\O for\\O a\\O special\\O occasion\\O --\\O it\\O provides\\O and\\O intimate\\O setting\\B and\\O nice\\O service\\O .\\O", "opinion_tags": "I\\O 'd\\O highly\\O recommend\\O it\\O for\\O a\\O special\\O occasion\\O --\\O it\\O provides\\O and\\O intimate\\B setting\\O and\\O nice\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "1199480:2-1", "target_tags": "I\\O 'd\\O highly\\O recommend\\O it\\O for\\O a\\O special\\O occasion\\O --\\O it\\O provides\\O and\\O intimate\\O setting\\O and\\O nice\\O service\\B .\\O", "opinion_tags": "I\\O 'd\\O highly\\O recommend\\O it\\O for\\O a\\O special\\O occasion\\O --\\O it\\O provides\\O and\\O intimate\\O setting\\O and\\O nice\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "1300636:0", "sentence": "This was my frist time at Cafe St. Bart 's and I must say how delicious the food and the service was .", "postag": ["DT", "VBD", "PRP$", "JJ", "NN", "IN", "NNP", "NNP", "NNP", "POS", "CC", "PRP", "MD", "VB", "WRB", "JJ", "DT", "NN", "CC", "DT", "NN", "VBD", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 8, 8, 14, 14, 14, 5, 16, 14, 18, 16, 21, 21, 18, 16, 5], "deprel": ["nsubj", "cop", "nmod:poss", "amod", "root", "case", "compound", "nmod", "flat", "case", "cc", "nsubj", "aux", "conj", "mark", "ccomp", "det", "nsubj", "cc", "det", "conj", "cop", "punct"], "triples": [{"uid": "1300636:0-0", "target_tags": "This\\O was\\O my\\O frist\\O time\\O at\\O Cafe\\O St.\\O Bart\\O 's\\O and\\O I\\O must\\O say\\O how\\O delicious\\O the\\O food\\B and\\O the\\O service\\O was\\O .\\O", "opinion_tags": "This\\O was\\O my\\O frist\\O time\\O at\\O Cafe\\O St.\\O Bart\\O 's\\O and\\O I\\O must\\O say\\O how\\O delicious\\B the\\O food\\O and\\O the\\O service\\O was\\O .\\O", "sentiment": "positive"}]}, {"id": "1086478:3", "sentence": "The kitchen however , is almost always slow .", "postag": ["DT", "NN", "RB", ",", "VBZ", "RB", "RB", "JJ", "."], "head": [2, 8, 8, 8, 8, 7, 8, 0, 8], "deprel": ["det", "nsubj", "advmod", "punct", "cop", "advmod", "advmod", "root", "punct"], "triples": [{"uid": "1086478:3-0", "target_tags": "The\\O kitchen\\B however\\O ,\\O is\\O almost\\O always\\O slow\\O .\\O", "opinion_tags": "The\\O kitchen\\O however\\O ,\\O is\\O almost\\O always\\O slow\\B .\\O", "sentiment": "negative"}]}, {"id": "1041457:4", "sentence": "I had never had Edamame pureed before but I thought it was innovative and tasty ( could 've used a bit more salt ) .", "postag": ["PRP", "VBD", "RB", "VBN", "NN", "VBN", "RB", "CC", "PRP", "VBD", "PRP", "VBD", "JJ", "CC", "JJ", "-LRB-", "MD", "VB", "VBN", "DT", "NN", "JJR", "NN", "-RRB-", "."], "head": [4, 4, 4, 0, 4, 4, 4, 10, 10, 4, 13, 13, 10, 15, 13, 19, 19, 19, 10, 21, 22, 23, 19, 19, 4], "deprel": ["nsubj", "aux", "advmod", "root", "obj", "xcomp", "advmod", "cc", "nsubj", "conj", "nsubj", "cop", "ccomp", "cc", "conj", "punct", "aux", "aux", "parataxis", "det", "obl:npmod", "amod", "obj", "punct", "punct"], "triples": [{"uid": "1041457:4-0", "target_tags": "I\\O had\\O never\\O had\\O Edamame\\B pureed\\I before\\O but\\O I\\O thought\\O it\\O was\\O innovative\\O and\\O tasty\\O (\\O could\\O 've\\O used\\O a\\O bit\\O more\\O salt\\O )\\O .\\O", "opinion_tags": "I\\O had\\O never\\O had\\O Edamame\\O pureed\\O before\\O but\\O I\\O thought\\O it\\O was\\O innovative\\B and\\O tasty\\B (\\O could\\O 've\\O used\\O a\\O bit\\O more\\O salt\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "1448587:4", "sentence": "Problem is nothing at <PERSON><PERSON><PERSON> is particularly memorable .", "postag": ["NN", "VBZ", "NN", "IN", "NNP", "VBZ", "RB", "JJ", "."], "head": [3, 3, 0, 5, 3, 8, 8, 3, 3], "deprel": ["nsubj", "cop", "root", "case", "nmod", "cop", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "1448587:4-0", "target_tags": "Problem\\O is\\O nothing\\O at\\O Prune\\B is\\O particularly\\O memorable\\O .\\O", "opinion_tags": "Problem\\O is\\O nothing\\O at\\O Prune\\O is\\O particularly\\O memorable\\B .\\O", "sentiment": "negative"}]}, {"id": "958809:2", "sentence": "We could have made a meal of the yummy dumplings from the dumpling menu .", "postag": ["PRP", "MD", "VB", "VBN", "DT", "NN", "IN", "DT", "JJ", "NNS", "IN", "DT", "NN", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 10, 10, 10, 6, 14, 14, 14, 4, 4], "deprel": ["nsubj", "aux", "aux", "root", "det", "obj", "case", "det", "amod", "nmod", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "958809:2-0", "target_tags": "We\\O could\\O have\\O made\\O a\\O meal\\O of\\O the\\O yummy\\O dumplings\\B from\\O the\\O dumpling\\O menu\\O .\\O", "opinion_tags": "We\\O could\\O have\\O made\\O a\\O meal\\O of\\O the\\O yummy\\B dumplings\\O from\\O the\\O dumpling\\O menu\\O .\\O", "sentiment": "positive"}]}, {"id": "1086478:2", "sentence": "The service varys from day to day- sometimes they 're very nice , and sometimes not .", "postag": ["DT", "NN", "VBZ", "IN", "NN", "IN", "NN", "RB", "PRP", "VBP", "RB", "JJ", ",", "CC", "RB", "RB", "."], "head": [2, 3, 0, 5, 3, 7, 3, 12, 12, 12, 12, 3, 16, 16, 16, 12, 3], "deprel": ["det", "nsubj", "root", "case", "obl", "case", "obl", "advmod", "nsubj", "cop", "advmod", "parataxis", "punct", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "1086478:2-0", "target_tags": "The\\O service\\B varys\\O from\\O day\\O to\\O day-\\O sometimes\\O they\\O 're\\O very\\O nice\\O ,\\O and\\O sometimes\\O not\\O .\\O", "opinion_tags": "The\\O service\\O varys\\B from\\O day\\O to\\O day-\\O sometimes\\O they\\O 're\\O very\\O nice\\O ,\\O and\\O sometimes\\O not\\O .\\O", "sentiment": "negative"}]}, {"id": "1064477:3", "sentence": "The rice to fish ration was also good -- they did n't try to overpack the rice .", "postag": ["DT", "NN", "IN", "NN", "NN", "VBD", "RB", "JJ", ",", "PRP", "VBD", "RB", "VB", "TO", "VB", "DT", "NN", "."], "head": [2, 8, 5, 5, 2, 8, 8, 0, 8, 13, 13, 13, 8, 15, 13, 17, 15, 8], "deprel": ["det", "nsubj", "case", "compound", "nmod", "cop", "advmod", "root", "punct", "nsubj", "aux", "advmod", "parataxis", "mark", "xcomp", "det", "obj", "punct"], "triples": [{"uid": "1064477:3-0", "target_tags": "The\\O rice\\B to\\I fish\\I ration\\I was\\O also\\O good\\O --\\O they\\O did\\O n't\\O try\\O to\\O overpack\\O the\\O rice\\O .\\O", "opinion_tags": "The\\O rice\\O to\\O fish\\O ration\\O was\\O also\\O good\\B --\\O they\\O did\\O n't\\O try\\O to\\O overpack\\O the\\O rice\\O .\\O", "sentiment": "positive"}]}, {"id": "1308557:1", "sentence": "It 's boring on the inside , and our sushi was pretty below average ... the tuna was soggy and the other rolls had no flavor .", "postag": ["PRP", "VBZ", "JJ", "IN", "DT", "NN", ",", "CC", "PRP$", "NN", "VBD", "RB", "RB", "JJ", ",", "DT", "NN", "VBD", "JJ", "CC", "DT", "JJ", "NNS", "VBD", "DT", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 13, 13, 10, 13, 13, 13, 3, 13, 3, 17, 19, 19, 3, 24, 23, 23, 24, 19, 26, 24, 3], "deprel": ["nsubj", "cop", "root", "case", "det", "obl", "punct", "cc", "nmod:poss", "nsubj", "cop", "advmod", "conj", "amod", "punct", "det", "nsubj", "cop", "parataxis", "cc", "det", "amod", "nsubj", "conj", "det", "obj", "punct"], "triples": [{"uid": "1308557:1-0", "target_tags": "It\\O 's\\O boring\\O on\\O the\\O inside\\O ,\\O and\\O our\\O sushi\\B was\\O pretty\\O below\\O average\\O ...\\O the\\O tuna\\O was\\O soggy\\O and\\O the\\O other\\O rolls\\O had\\O no\\O flavor\\O .\\O", "opinion_tags": "It\\O 's\\O boring\\O on\\O the\\O inside\\O ,\\O and\\O our\\O sushi\\O was\\O pretty\\O below\\B average\\I ...\\O the\\O tuna\\O was\\O soggy\\O and\\O the\\O other\\O rolls\\O had\\O no\\O flavor\\O .\\O", "sentiment": "negative"}, {"uid": "1308557:1-1", "target_tags": "It\\O 's\\O boring\\O on\\O the\\O inside\\O ,\\O and\\O our\\O sushi\\O was\\O pretty\\O below\\O average\\O ...\\O the\\O tuna\\B was\\O soggy\\O and\\O the\\O other\\O rolls\\O had\\O no\\O flavor\\O .\\O", "opinion_tags": "It\\O 's\\O boring\\O on\\O the\\O inside\\O ,\\O and\\O our\\O sushi\\O was\\O pretty\\O below\\O average\\O ...\\O the\\O tuna\\O was\\O soggy\\B and\\O the\\O other\\O rolls\\O had\\O no\\O flavor\\O .\\O", "sentiment": "negative"}, {"uid": "1308557:1-2", "target_tags": "It\\O 's\\O boring\\O on\\O the\\O inside\\O ,\\O and\\O our\\O sushi\\O was\\O pretty\\O below\\O average\\O ...\\O the\\O tuna\\O was\\O soggy\\O and\\O the\\O other\\O rolls\\B had\\O no\\O flavor\\O .\\O", "opinion_tags": "It\\O 's\\O boring\\O on\\O the\\O inside\\O ,\\O and\\O our\\O sushi\\O was\\O pretty\\O below\\O average\\O ...\\O the\\O tuna\\O was\\O soggy\\O and\\O the\\O other\\O rolls\\O had\\O no\\B flavor\\I .\\O", "sentiment": "negative"}]}, {"id": "1451109:2", "sentence": "The food was pretty tradional but it was hot and good with large portions .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "PRP", "VBD", "JJ", "CC", "JJ", "IN", "JJ", "NNS", "."], "head": [2, 5, 5, 5, 0, 9, 9, 9, 5, 11, 9, 14, 14, 9, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "nsubj", "cop", "conj", "cc", "conj", "case", "amod", "obl", "punct"], "triples": [{"uid": "1451109:2-0", "target_tags": "The\\O food\\B was\\O pretty\\O tradional\\O but\\O it\\O was\\O hot\\O and\\O good\\O with\\O large\\O portions\\O .\\O", "opinion_tags": "The\\O food\\O was\\O pretty\\O tradional\\B but\\O it\\O was\\O hot\\O and\\O good\\O with\\O large\\O portions\\O .\\O", "sentiment": "positive"}, {"uid": "1451109:2-1", "target_tags": "The\\O food\\O was\\O pretty\\O tradional\\O but\\O it\\O was\\O hot\\O and\\O good\\O with\\O large\\O portions\\B .\\O", "opinion_tags": "The\\O food\\O was\\O pretty\\O tradional\\O but\\O it\\O was\\O hot\\O and\\O good\\O with\\O large\\B portions\\O .\\O", "sentiment": "positive"}]}, {"id": "503807:6", "sentence": "The restaurant is a bit noisy but that is something that can be overlooked once you sit down and enjoy a great meal", "postag": ["DT", "NN", "VBZ", "DT", "NN", "JJ", "CC", "DT", "VBZ", "NN", "WDT", "MD", "VB", "VBN", "IN", "PRP", "VBP", "RP", "CC", "VB", "DT", "JJ", "NN"], "head": [2, 6, 6, 5, 6, 0, 10, 10, 10, 6, 14, 14, 14, 10, 17, 17, 14, 17, 20, 17, 23, 23, 20], "deprel": ["det", "nsubj", "cop", "det", "obl:npmod", "root", "cc", "nsubj", "cop", "conj", "nsubj:pass", "aux", "aux:pass", "acl:relcl", "mark", "nsubj", "advcl", "compound:prt", "cc", "conj", "det", "amod", "obj"], "triples": [{"uid": "503807:6-0", "target_tags": "The\\O restaurant\\O is\\O a\\O bit\\O noisy\\O but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\O a\\O great\\O meal\\B", "opinion_tags": "The\\O restaurant\\O is\\O a\\O bit\\O noisy\\O but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\B a\\O great\\B meal\\O", "sentiment": "positive"}, {"uid": "503807:6-1", "target_tags": "The\\O restaurant\\B is\\O a\\O bit\\O noisy\\O but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\O a\\O great\\O meal\\O", "opinion_tags": "The\\O restaurant\\O is\\O a\\O bit\\O noisy\\B but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\O a\\O great\\O meal\\O", "sentiment": "negative"}]}, {"id": "1655504:0", "sentence": "Have frequented 'ino for several years and the food remains excellent .", "postag": ["VBP", "VBN", "NN", "IN", "JJ", "NNS", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 0, 2, 6, 6, 2, 10, 9, 10, 2, 10, 2], "deprel": ["aux", "root", "obj", "case", "amod", "obl", "cc", "det", "nsubj", "conj", "xcomp", "punct"], "triples": [{"uid": "1655504:0-0", "target_tags": "Have\\O frequented\\O 'ino\\O for\\O several\\O years\\O and\\O the\\O food\\B remains\\O excellent\\O .\\O", "opinion_tags": "Have\\O frequented\\O 'ino\\O for\\O several\\O years\\O and\\O the\\O food\\O remains\\O excellent\\B .\\O", "sentiment": "positive"}]}, {"id": "1014458:1", "sentence": "Saul is the best restaurant on Smith Street and in Brooklyn .", "postag": ["NNP", "VBZ", "DT", "JJS", "NN", "IN", "NNP", "NNP", "CC", "IN", "NNP", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 11, 11, 5, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "case", "compound", "nmod", "cc", "case", "conj", "punct"], "triples": [{"uid": "1014458:1-0", "target_tags": "Saul\\B is\\O the\\O best\\O restaurant\\O on\\O Smith\\O Street\\O and\\O in\\O Brooklyn\\O .\\O", "opinion_tags": "Saul\\O is\\O the\\O best\\B restaurant\\O on\\O Smith\\O Street\\O and\\O in\\O Brooklyn\\O .\\O", "sentiment": "positive"}]}, {"id": "1655521:1", "sentence": "I almost hesititate to write a review because the atmosphere was so great and I would hate for it too become to crowded .", "postag": ["PRP", "RB", "VBD", "TO", "VB", "DT", "NN", "IN", "DT", "NN", "VBD", "RB", "JJ", "CC", "PRP", "MD", "VB", "IN", "PRP", "RB", "VB", "IN", "JJ", "."], "head": [3, 3, 0, 5, 3, 7, 5, 13, 10, 13, 13, 13, 5, 17, 17, 17, 13, 19, 17, 21, 17, 23, 21, 3], "deprel": ["nsubj", "advmod", "root", "mark", "xcomp", "det", "obj", "mark", "det", "nsubj", "cop", "advmod", "advcl", "cc", "nsubj", "aux", "conj", "case", "obl", "advmod", "xcomp", "case", "obl", "punct"], "triples": [{"uid": "1655521:1-0", "target_tags": "I\\O almost\\O hesititate\\O to\\O write\\O a\\O review\\O because\\O the\\O atmosphere\\B was\\O so\\O great\\O and\\O I\\O would\\O hate\\O for\\O it\\O too\\O become\\O to\\O crowded\\O .\\O", "opinion_tags": "I\\O almost\\O hesititate\\O to\\O write\\O a\\O review\\O because\\O the\\O atmosphere\\O was\\O so\\O great\\B and\\O I\\O would\\O hate\\O for\\O it\\O too\\O become\\O to\\O crowded\\O .\\O", "sentiment": "positive"}]}, {"id": "475512:3", "sentence": "If you like your music blasted and the system isnt that great and if you want to pay at least 100 dollar bottle minimun then you 'll love it here .", "postag": ["IN", "PRP", "VBP", "PRP$", "NN", "VBN", "CC", "DT", "NN", "VBZ", "RB", "JJ", "CC", "IN", "PRP", "VBP", "TO", "VB", "RB", "RBS", "CD", "NN", "NN", "NN", "RB", "PRP", "MD", "VB", "PRP", "RB", "."], "head": [3, 3, 12, 5, 3, 3, 12, 9, 12, 12, 12, 0, 28, 16, 16, 28, 18, 16, 20, 21, 24, 24, 24, 18, 28, 28, 28, 12, 28, 28, 12], "deprel": ["mark", "nsubj", "advcl", "nmod:poss", "obj", "xcomp", "cc", "det", "nsubj", "cop", "advmod", "root", "cc", "mark", "nsubj", "advcl", "mark", "xcomp", "case", "nmod", "nummod", "compound", "compound", "obj", "advmod", "nsubj", "aux", "conj", "obj", "advmod", "punct"], "triples": [{"uid": "475512:3-0", "target_tags": "If\\O you\\O like\\O your\\O music\\O blasted\\O and\\O the\\O system\\O isnt\\O that\\O great\\O and\\O if\\O you\\O want\\O to\\O pay\\O at\\O least\\O 100\\O dollar\\O bottle\\B minimun\\O then\\O you\\O 'll\\O love\\O it\\O here\\O .\\O", "opinion_tags": "If\\O you\\O like\\O your\\O music\\O blasted\\O and\\O the\\O system\\O isnt\\O that\\O great\\O and\\O if\\O you\\O want\\O to\\O pay\\O at\\O least\\O 100\\O dollar\\O bottle\\O minimun\\O then\\O you\\O 'll\\O love\\B it\\O here\\O .\\O", "sentiment": "negative"}, {"uid": "475512:3-1", "target_tags": "If\\O you\\O like\\O your\\O music\\B blasted\\O and\\O the\\O system\\O isnt\\O that\\O great\\O and\\O if\\O you\\O want\\O to\\O pay\\O at\\O least\\O 100\\O dollar\\O bottle\\O minimun\\O then\\O you\\O 'll\\O love\\O it\\O here\\O .\\O", "opinion_tags": "If\\O you\\O like\\B your\\O music\\O blasted\\O and\\O the\\O system\\O isnt\\O that\\O great\\O and\\O if\\O you\\O want\\O to\\O pay\\O at\\O least\\O 100\\O dollar\\O bottle\\O minimun\\O then\\O you\\O 'll\\O love\\O it\\O here\\O .\\O", "sentiment": "negative"}]}, {"id": "1623276:2", "sentence": "This is a nice restaurant if you are looking for a good place to host an intimate dinner meeting with business associates .", "postag": ["DT", "VBZ", "DT", "JJ", "NN", "IN", "PRP", "VBP", "VBG", "IN", "DT", "JJ", "NN", "TO", "VB", "DT", "JJ", "NN", "NN", "IN", "NN", "NNS", "."], "head": [5, 5, 5, 5, 0, 9, 9, 9, 5, 13, 13, 13, 9, 15, 13, 19, 19, 19, 15, 22, 22, 19, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "nsubj", "aux", "advcl", "case", "det", "amod", "obl", "mark", "acl", "det", "amod", "compound", "obj", "case", "compound", "nmod", "punct"], "triples": [{"uid": "1623276:2-0", "target_tags": "This\\O is\\O a\\O nice\\O restaurant\\B if\\O you\\O are\\O looking\\O for\\O a\\O good\\O place\\O to\\O host\\O an\\O intimate\\O dinner\\O meeting\\O with\\O business\\O associates\\O .\\O", "opinion_tags": "This\\O is\\O a\\O nice\\B restaurant\\O if\\O you\\O are\\O looking\\O for\\O a\\O good\\O place\\O to\\O host\\O an\\O intimate\\O dinner\\O meeting\\O with\\O business\\O associates\\O .\\O", "sentiment": "positive"}]}, {"id": "1710746:1", "sentence": "I would recommend Roxy 's for that , but not for their food .", "postag": ["PRP", "MD", "VB", "NNP", "POS", "IN", "DT", ",", "CC", "RB", "IN", "PRP$", "NN", "."], "head": [3, 3, 0, 3, 4, 7, 3, 13, 13, 13, 13, 13, 3, 3], "deprel": ["nsubj", "aux", "root", "obj", "case", "case", "obl", "punct", "cc", "advmod", "case", "nmod:poss", "conj", "punct"], "triples": [{"uid": "1710746:1-0", "target_tags": "I\\O would\\O recommend\\O Roxy\\O 's\\O for\\O that\\O ,\\O but\\O not\\O for\\O their\\O food\\B .\\O", "opinion_tags": "I\\O would\\O recommend\\B Roxy\\O 's\\O for\\O that\\O ,\\O but\\O not\\O for\\O their\\O food\\O .\\O", "sentiment": "negative"}]}, {"id": "1189674:1", "sentence": "The food was amazing , and the service was prompt and helpful , but not over-bearing or rushed .", "postag": ["DT", "NN", "VBD", "JJ", ",", "CC", "DT", "NN", "VBD", "JJ", "CC", "JJ", ",", "CC", "RB", "JJ", "CC", "VBN", "."], "head": [2, 4, 4, 0, 10, 10, 8, 10, 10, 4, 12, 10, 16, 16, 16, 10, 18, 16, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "det", "nsubj", "cop", "conj", "cc", "conj", "punct", "cc", "advmod", "conj", "cc", "conj", "punct"], "triples": [{"uid": "1189674:1-0", "target_tags": "The\\O food\\B was\\O amazing\\O ,\\O and\\O the\\O service\\O was\\O prompt\\O and\\O helpful\\O ,\\O but\\O not\\O over-bearing\\O or\\O rushed\\O .\\O", "opinion_tags": "The\\O food\\O was\\O amazing\\B ,\\O and\\O the\\O service\\O was\\O prompt\\O and\\O helpful\\O ,\\O but\\O not\\O over-bearing\\O or\\O rushed\\O .\\O", "sentiment": "positive"}, {"uid": "1189674:1-1", "target_tags": "The\\O food\\O was\\O amazing\\O ,\\O and\\O the\\O service\\B was\\O prompt\\O and\\O helpful\\O ,\\O but\\O not\\O over-bearing\\O or\\O rushed\\O .\\O", "opinion_tags": "The\\O food\\O was\\O amazing\\O ,\\O and\\O the\\O service\\O was\\O prompt\\B and\\O helpful\\B ,\\O but\\O not\\B over-bearing\\I or\\I rushed\\I .\\O", "sentiment": "positive"}]}, {"id": "1706644:2", "sentence": "I also ordered the Change Mojito , which was out of this world .", "postag": ["PRP", "RB", "VBD", "DT", "NNP", "NNP", ",", "WDT", "VBD", "IN", "IN", "DT", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 6, 13, 13, 13, 13, 13, 6, 3], "deprel": ["nsubj", "advmod", "root", "det", "compound", "obj", "punct", "nsubj", "cop", "case", "case", "det", "acl:relcl", "punct"], "triples": [{"uid": "1706644:2-0", "target_tags": "I\\O also\\O ordered\\O the\\O Change\\B Mojito\\I ,\\O which\\O was\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "I\\O also\\O ordered\\O the\\O Change\\O Mojito\\O ,\\O which\\O was\\O out\\B of\\I this\\I world\\I .\\O", "sentiment": "positive"}]}, {"id": "1615322:4", "sentence": "The seats are uncomfortable if you are sitting against the wall on wooden benches .", "postag": ["DT", "NNS", "VBP", "JJ", "IN", "PRP", "VBP", "VBG", "IN", "DT", "NN", "IN", "JJ", "NNS", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 11, 11, 8, 14, 14, 8, 4], "deprel": ["det", "nsubj", "cop", "root", "mark", "nsubj", "aux", "advcl", "case", "det", "obl", "case", "amod", "obl", "punct"], "triples": [{"uid": "1615322:4-0", "target_tags": "The\\O seats\\B are\\O uncomfortable\\O if\\O you\\O are\\O sitting\\O against\\O the\\O wall\\O on\\O wooden\\O benches\\O .\\O", "opinion_tags": "The\\O seats\\O are\\O uncomfortable\\B if\\O you\\O are\\O sitting\\O against\\O the\\O wall\\O on\\O wooden\\O benches\\O .\\O", "sentiment": "negative"}]}, {"id": "490565:6", "sentence": "You ca n't go wrong with this place .", "postag": ["PRP", "MD", "RB", "VB", "JJ", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 4, 8, 8, 4, 4], "deprel": ["nsubj", "aux", "advmod", "root", "advmod", "case", "det", "obl", "punct"], "triples": [{"uid": "490565:6-0", "target_tags": "You\\O ca\\O n't\\O go\\O wrong\\O with\\O this\\O place\\B .\\O", "opinion_tags": "You\\O ca\\B n't\\I go\\I wrong\\I with\\O this\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "608500:2", "sentence": "While the ambiance and atmosphere were great , the food and service could have been a lot better .", "postag": ["IN", "DT", "NN", "CC", "NN", "VBD", "JJ", ",", "DT", "NN", "CC", "NN", "MD", "VB", "VBN", "DT", "NN", "JJR", "."], "head": [7, 3, 7, 5, 3, 7, 18, 18, 10, 18, 12, 10, 18, 18, 18, 17, 18, 0, 18], "deprel": ["mark", "det", "nsubj", "cc", "conj", "cop", "advcl", "punct", "det", "nsubj", "cc", "conj", "aux", "aux", "cop", "det", "obl:npmod", "root", "punct"], "triples": [{"uid": "608500:2-0", "target_tags": "While\\O the\\O ambiance\\B and\\O atmosphere\\O were\\O great\\O ,\\O the\\O food\\O and\\O service\\O could\\O have\\O been\\O a\\O lot\\O better\\O .\\O", "opinion_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\O were\\O great\\B ,\\O the\\O food\\O and\\O service\\O could\\O have\\O been\\O a\\O lot\\O better\\O .\\O", "sentiment": "positive"}, {"uid": "608500:2-1", "target_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\B were\\O great\\O ,\\O the\\O food\\O and\\O service\\O could\\O have\\O been\\O a\\O lot\\O better\\O .\\O", "opinion_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\O were\\O great\\B ,\\O the\\O food\\O and\\O service\\O could\\O have\\O been\\O a\\O lot\\O better\\O .\\O", "sentiment": "positive"}, {"uid": "608500:2-2", "target_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\O were\\O great\\O ,\\O the\\O food\\B and\\O service\\O could\\O have\\O been\\O a\\O lot\\O better\\O .\\O", "opinion_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\O were\\O great\\O ,\\O the\\O food\\O and\\O service\\O could\\B have\\I been\\I a\\I lot\\I better\\I .\\O", "sentiment": "negative"}, {"uid": "608500:2-3", "target_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\O were\\O great\\O ,\\O the\\O food\\O and\\O service\\B could\\O have\\O been\\O a\\O lot\\O better\\O .\\O", "opinion_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\O were\\O great\\O ,\\O the\\O food\\O and\\O service\\O could\\B have\\I been\\I a\\I lot\\I better\\I .\\O", "sentiment": "negative"}]}, {"id": "455623:0", "sentence": "Love Pizza 33 ...", "postag": ["VBP", "NN", "CD", "."], "head": [0, 1, 2, 1], "deprel": ["root", "obj", "nummod", "punct"], "triples": [{"uid": "455623:0-0", "target_tags": "Love\\O Pizza\\B 33\\I ...\\O", "opinion_tags": "Love\\B Pizza\\O 33\\O ...\\O", "sentiment": "positive"}]}, {"id": "1661043:5", "sentence": "Service is average .", "postag": ["NN", "VBZ", "JJ", "."], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"], "triples": [{"uid": "1661043:5-0", "target_tags": "Service\\B is\\O average\\O .\\O", "opinion_tags": "Service\\O is\\O average\\B .\\O", "sentiment": "neutral"}]}, {"id": "720620:5", "sentence": "Simply some good tasting Chinese food at incredible prices ...", "postag": ["RB", "DT", "JJ", "NN", "JJ", "NN", "IN", "JJ", "NNS", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 6], "deprel": ["advmod", "det", "amod", "compound", "amod", "root", "case", "amod", "nmod", "punct"], "triples": [{"uid": "720620:5-0", "target_tags": "Simply\\O some\\O good\\O tasting\\O Chinese\\B food\\I at\\O incredible\\O prices\\O ...\\O", "opinion_tags": "Simply\\O some\\O good\\B tasting\\I Chinese\\O food\\O at\\O incredible\\O prices\\O ...\\O", "sentiment": "positive"}]}, {"id": "558423:5", "sentence": "Quick and friendly service .", "postag": ["JJ", "CC", "JJ", "NN", "."], "head": [4, 3, 1, 0, 4], "deprel": ["amod", "cc", "conj", "root", "punct"], "triples": [{"uid": "558423:5-0", "target_tags": "Quick\\O and\\O friendly\\O service\\B .\\O", "opinion_tags": "Quick\\B and\\O friendly\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "1706644:3", "sentence": "My friends settled for rice dishes , but we came back the following day to try the dim sum , which was good ... not outstanding , but good .", "postag": ["PRP$", "NNS", "VBD", "IN", "NN", "NNS", ",", "CC", "PRP", "VBD", "RB", "DT", "VBG", "NN", "TO", "VB", "DT", "JJ", "NN", ",", "WDT", "VBD", "JJ", ",", "RB", "JJ", ",", "CC", "JJ", "."], "head": [2, 3, 0, 6, 6, 3, 10, 10, 10, 3, 10, 14, 14, 10, 16, 10, 19, 19, 16, 19, 23, 23, 19, 26, 26, 23, 29, 29, 26, 3], "deprel": ["nmod:poss", "nsubj", "root", "case", "compound", "obl", "punct", "cc", "nsubj", "conj", "advmod", "det", "amod", "obl:tmod", "mark", "advcl", "det", "amod", "obj", "punct", "nsubj", "cop", "acl:relcl", "punct", "advmod", "conj", "punct", "cc", "conj", "punct"], "triples": [{"uid": "1706644:3-0", "target_tags": "My\\O friends\\O settled\\O for\\O rice\\O dishes\\O ,\\O but\\O we\\O came\\O back\\O the\\O following\\O day\\O to\\O try\\O the\\O dim\\B sum\\I ,\\O which\\O was\\O good\\O ...\\O not\\O outstanding\\O ,\\O but\\O good\\O .\\O", "opinion_tags": "My\\O friends\\O settled\\O for\\O rice\\O dishes\\O ,\\O but\\O we\\O came\\O back\\O the\\O following\\O day\\O to\\O try\\O the\\O dim\\O sum\\O ,\\O which\\O was\\O good\\B ...\\O not\\B outstanding\\I ,\\O but\\O good\\O .\\O", "sentiment": "neutral"}]}, {"id": "527109:4", "sentence": "Still , any quibbles about the bill were off-set by the pour-your-own measures of liquers which were courtesey of the house ...", "postag": ["RB", ",", "DT", "NNS", "IN", "DT", "NN", "VBD", "VBN", "IN", "DT", "JJ", "NNS", "IN", "NNS", "WDT", "VBD", "NN", "IN", "DT", "NN", "."], "head": [9, 1, 4, 9, 7, 7, 4, 9, 0, 13, 13, 13, 9, 15, 13, 18, 18, 13, 21, 21, 18, 9], "deprel": ["advmod", "punct", "det", "nsubj:pass", "case", "det", "nmod", "aux:pass", "root", "case", "det", "amod", "obl", "case", "nmod", "nsubj", "cop", "acl:relcl", "case", "det", "nmod", "punct"], "triples": [{"uid": "527109:4-0", "target_tags": "Still\\O ,\\O any\\O quibbles\\O about\\O the\\O bill\\O were\\O off-set\\O by\\O the\\O pour-your-own\\O measures\\B of\\I liquers\\I which\\O were\\O courtesey\\O of\\O the\\O house\\O ...\\O", "opinion_tags": "Still\\O ,\\O any\\O quibbles\\O about\\O the\\O bill\\O were\\O off-set\\O by\\O the\\O pour-your-own\\B measures\\O of\\O liquers\\O which\\O were\\O courtesey\\B of\\O the\\O house\\O ...\\O", "sentiment": "positive"}]}, {"id": "1609375:2", "sentence": "The svc can be a bit rude at times , esp if you have big group , but overall the restaurant is a must !", "postag": ["DT", "NN", "MD", "VB", "DT", "NN", "JJ", "IN", "NNS", ",", "RB", "IN", "PRP", "VBP", "JJ", "NN", ",", "CC", "RB", "DT", "NN", "VBZ", "DT", "NN", "."], "head": [2, 7, 7, 7, 6, 7, 0, 9, 7, 7, 14, 14, 14, 7, 16, 14, 24, 24, 24, 21, 24, 24, 24, 7, 7], "deprel": ["det", "nsubj", "aux", "cop", "det", "obl:npmod", "root", "case", "obl", "punct", "advmod", "mark", "nsubj", "advcl", "amod", "obj", "punct", "cc", "advmod", "det", "nsubj", "cop", "det", "conj", "punct"], "triples": [{"uid": "1609375:2-0", "target_tags": "The\\O svc\\B can\\O be\\O a\\O bit\\O rude\\O at\\O times\\O ,\\O esp\\O if\\O you\\O have\\O big\\O group\\O ,\\O but\\O overall\\O the\\O restaurant\\O is\\O a\\O must\\O !\\O", "opinion_tags": "The\\O svc\\O can\\O be\\O a\\O bit\\O rude\\B at\\O times\\O ,\\O esp\\O if\\O you\\O have\\O big\\O group\\O ,\\O but\\O overall\\O the\\O restaurant\\O is\\O a\\O must\\O !\\O", "sentiment": "negative"}, {"uid": "1609375:2-1", "target_tags": "The\\O svc\\O can\\O be\\O a\\O bit\\O rude\\O at\\O times\\O ,\\O esp\\O if\\O you\\O have\\O big\\O group\\O ,\\O but\\O overall\\O the\\O restaurant\\B is\\O a\\O must\\O !\\O", "opinion_tags": "The\\O svc\\O can\\O be\\O a\\O bit\\O rude\\O at\\O times\\O ,\\O esp\\O if\\O you\\O have\\O big\\O group\\O ,\\O but\\O overall\\O the\\O restaurant\\O is\\O a\\O must\\B !\\O", "sentiment": "positive"}]}, {"id": "1338124:2", "sentence": "The spicy Tuna roll is huge and probably the best that I 've had at this price range .", "postag": ["DT", "JJ", "NN", "NN", "VBZ", "JJ", "CC", "RB", "DT", "JJS", "WDT", "PRP", "VBP", "VBN", "IN", "DT", "NN", "NN", "."], "head": [4, 4, 4, 6, 6, 0, 10, 10, 10, 6, 14, 14, 14, 10, 18, 18, 18, 14, 6], "deprel": ["det", "amod", "compound", "nsubj", "cop", "root", "cc", "advmod", "det", "conj", "obj", "nsubj", "aux", "acl:relcl", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "1338124:2-0", "target_tags": "The\\O spicy\\B Tuna\\I roll\\I is\\O huge\\O and\\O probably\\O the\\O best\\O that\\O I\\O 've\\O had\\O at\\O this\\O price\\O range\\O .\\O", "opinion_tags": "The\\O spicy\\O Tuna\\O roll\\O is\\O huge\\B and\\O probably\\O the\\O best\\B that\\O I\\O 've\\O had\\O at\\O this\\O price\\O range\\O .\\O", "sentiment": "positive"}]}, {"id": "1125284:0", "sentence": "As a Japanese native , I 've lived in the Tristate area for over 8 years , but I was just so amazed at this place .", "postag": ["IN", "DT", "JJ", "NN", ",", "PRP", "VBP", "VBN", "IN", "DT", "NNP", "NN", "IN", "RB", "CD", "NNS", ",", "CC", "PRP", "VBD", "RB", "RB", "JJ", "IN", "DT", "NN", "."], "head": [4, 4, 4, 8, 8, 8, 8, 0, 12, 12, 12, 8, 16, 15, 16, 8, 23, 23, 23, 23, 23, 23, 8, 26, 26, 23, 8], "deprel": ["case", "det", "amod", "obl", "punct", "nsubj", "aux", "root", "case", "det", "compound", "obl", "case", "advmod", "nummod", "obl", "punct", "cc", "nsubj", "cop", "advmod", "advmod", "conj", "case", "det", "obl", "punct"], "triples": [{"uid": "1125284:0-0", "target_tags": "As\\O a\\O Japanese\\O native\\O ,\\O I\\O 've\\O lived\\O in\\O the\\O Tristate\\O area\\O for\\O over\\O 8\\O years\\O ,\\O but\\O I\\O was\\O just\\O so\\O amazed\\O at\\O this\\O place\\B .\\O", "opinion_tags": "As\\O a\\O Japanese\\O native\\O ,\\O I\\O 've\\O lived\\O in\\O the\\O Tristate\\O area\\O for\\O over\\O 8\\O years\\O ,\\O but\\O I\\O was\\O just\\O so\\O amazed\\B at\\O this\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "955711:3", "sentence": "The mussels were fantastic and so was the dessert ... definitely going to be back very soon .", "postag": ["DT", "NNS", "VBD", "JJ", "CC", "RB", "VBD", "DT", "NN", ",", "RB", "VBG", "TO", "VB", "RB", "RB", "RB", "."], "head": [2, 4, 4, 0, 6, 4, 6, 9, 6, 4, 12, 4, 14, 12, 14, 17, 14, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "cop", "det", "nsubj", "punct", "advmod", "parataxis", "mark", "xcomp", "advmod", "advmod", "advmod", "punct"], "triples": [{"uid": "955711:3-0", "target_tags": "The\\O mussels\\B were\\O fantastic\\O and\\O so\\O was\\O the\\O dessert\\O ...\\O definitely\\O going\\O to\\O be\\O back\\O very\\O soon\\O .\\O", "opinion_tags": "The\\O mussels\\O were\\O fantastic\\B and\\O so\\O was\\O the\\O dessert\\O ...\\O definitely\\O going\\O to\\O be\\O back\\O very\\O soon\\O .\\O", "sentiment": "positive"}, {"uid": "955711:3-1", "target_tags": "The\\O mussels\\O were\\O fantastic\\O and\\O so\\O was\\O the\\O dessert\\B ...\\O definitely\\O going\\O to\\O be\\O back\\O very\\O soon\\O .\\O", "opinion_tags": "The\\O mussels\\O were\\O fantastic\\B and\\O so\\O was\\O the\\O dessert\\O ...\\O definitely\\O going\\O to\\O be\\O back\\O very\\O soon\\O .\\O", "sentiment": "positive"}]}, {"id": "1448587:2", "sentence": "We ordered the special , grilled branzino , that was so infused with bone , it was difficult to eat .", "postag": ["PRP", "VBD", "DT", "JJ", ",", "JJ", "NN", ",", "WDT", "VBD", "RB", "VBN", "IN", "NN", ",", "PRP", "VBD", "JJ", "TO", "VB", "."], "head": [2, 0, 7, 7, 7, 7, 2, 7, 12, 12, 12, 7, 14, 12, 2, 18, 18, 2, 20, 18, 2], "deprel": ["nsubj", "root", "det", "amod", "punct", "amod", "obj", "punct", "nsubj:pass", "aux:pass", "advmod", "acl:relcl", "case", "obl", "punct", "nsubj", "cop", "parataxis", "mark", "csubj", "punct"], "triples": [{"uid": "1448587:2-0", "target_tags": "We\\O ordered\\O the\\O special\\O ,\\O grilled\\B branzino\\I ,\\O that\\O was\\O so\\O infused\\O with\\O bone\\O ,\\O it\\O was\\O difficult\\O to\\O eat\\O .\\O", "opinion_tags": "We\\O ordered\\O the\\O special\\O ,\\O grilled\\O branzino\\O ,\\O that\\O was\\O so\\O infused\\O with\\O bone\\O ,\\O it\\O was\\O difficult\\B to\\I eat\\I .\\O", "sentiment": "negative"}]}, {"id": "1606837:1", "sentence": "With the theater 2 blocks away we had a delicious meal in a beautiful room .", "postag": ["IN", "DT", "NN", "CD", "NNS", "RB", "PRP", "VBD", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", "."], "head": [3, 3, 8, 5, 6, 8, 8, 0, 11, 11, 8, 15, 15, 15, 8, 8], "deprel": ["case", "det", "obl", "nummod", "obl:npmod", "advmod", "nsubj", "root", "det", "amod", "obj", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "1606837:1-0", "target_tags": "With\\O the\\O theater\\O 2\\O blocks\\O away\\O we\\O had\\O a\\O delicious\\O meal\\B in\\O a\\O beautiful\\O room\\O .\\O", "opinion_tags": "With\\O the\\O theater\\O 2\\O blocks\\O away\\O we\\O had\\O a\\O delicious\\B meal\\O in\\O a\\O beautiful\\O room\\O .\\O", "sentiment": "positive"}, {"uid": "1606837:1-1", "target_tags": "With\\O the\\O theater\\O 2\\O blocks\\O away\\O we\\O had\\O a\\O delicious\\O meal\\O in\\O a\\O beautiful\\O room\\B .\\O", "opinion_tags": "With\\O the\\O theater\\O 2\\O blocks\\O away\\O we\\O had\\O a\\O delicious\\O meal\\O in\\O a\\O beautiful\\B room\\O .\\O", "sentiment": "positive"}]}, {"id": "1572033:2", "sentence": "This place is always packed .", "postag": ["DT", "NN", "VBZ", "RB", "VBN", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj:pass", "aux:pass", "advmod", "root", "punct"], "triples": [{"uid": "1572033:2-0", "target_tags": "This\\O place\\B is\\O always\\O packed\\O .\\O", "opinion_tags": "This\\O place\\O is\\O always\\O packed\\B .\\O", "sentiment": "neutral"}]}, {"id": "561054:1", "sentence": "When you 're sitting in their main dining room ( which has a spectacular , hand-painted high ceiling ) you 'd never know there was a world outside .", "postag": ["WRB", "PRP", "VBP", "VBG", "IN", "PRP$", "JJ", "NN", "NN", "-LRB-", "WDT", "VBZ", "DT", "JJ", ",", "JJ", "JJ", "NN", "-RRB-", "PRP", "MD", "RB", "VB", "EX", "VBD", "DT", "NN", "RB", "."], "head": [4, 4, 4, 23, 9, 9, 9, 9, 4, 12, 12, 9, 18, 18, 18, 18, 18, 12, 12, 23, 23, 23, 0, 25, 23, 27, 25, 25, 23], "deprel": ["mark", "nsubj", "aux", "advcl", "case", "nmod:poss", "amod", "compound", "obl", "punct", "nsubj", "acl:relcl", "det", "amod", "punct", "amod", "amod", "obj", "punct", "nsubj", "aux", "advmod", "root", "expl", "ccomp", "det", "nsubj", "advmod", "punct"], "triples": [{"uid": "561054:1-0", "target_tags": "When\\O you\\O 're\\O sitting\\O in\\O their\\O main\\B dining\\I room\\I (\\O which\\O has\\O a\\O spectacular\\O ,\\O hand-painted\\O high\\O ceiling\\O )\\O you\\O 'd\\O never\\O know\\O there\\O was\\O a\\O world\\O outside\\O .\\O", "opinion_tags": "When\\O you\\O 're\\O sitting\\O in\\O their\\O main\\O dining\\O room\\O (\\O which\\O has\\O a\\O spectacular\\B ,\\O hand-painted\\O high\\O ceiling\\O )\\O you\\O 'd\\O never\\O know\\O there\\O was\\O a\\O world\\O outside\\O .\\O", "sentiment": "positive"}, {"uid": "561054:1-1", "target_tags": "When\\O you\\O 're\\O sitting\\O in\\O their\\O main\\O dining\\O room\\O (\\O which\\O has\\O a\\O spectacular\\O ,\\O hand-painted\\O high\\O ceiling\\B )\\O you\\O 'd\\O never\\O know\\O there\\O was\\O a\\O world\\O outside\\O .\\O", "opinion_tags": "When\\O you\\O 're\\O sitting\\O in\\O their\\O main\\O dining\\O room\\O (\\O which\\O has\\O a\\O spectacular\\B ,\\O hand-painted\\B high\\B ceiling\\O )\\O you\\O 'd\\O never\\O know\\O there\\O was\\O a\\O world\\O outside\\O .\\O", "sentiment": "positive"}]}, {"id": "1287538:3", "sentence": "The food is a diamond in rough -- the food is delicious and homemade with the perfect balance of herbs and tomatoes .", "postag": ["DT", "NN", "VBZ", "DT", "NN", "IN", "JJ", ",", "DT", "NN", "VBZ", "JJ", "CC", "JJ", "IN", "DT", "JJ", "NN", "IN", "NNS", "CC", "NNS", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5, 10, 12, 12, 5, 14, 12, 18, 18, 18, 12, 20, 18, 22, 20, 5], "deprel": ["det", "nsubj", "cop", "det", "root", "case", "nmod", "punct", "det", "nsubj", "cop", "parataxis", "cc", "conj", "case", "det", "amod", "obl", "case", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "1287538:3-0", "target_tags": "The\\O food\\B is\\O a\\O diamond\\O in\\O rough\\O --\\O the\\O food\\O is\\O delicious\\O and\\O homemade\\O with\\O the\\O perfect\\O balance\\O of\\O herbs\\O and\\O tomatoes\\O .\\O", "opinion_tags": "The\\O food\\O is\\O a\\O diamond\\B in\\O rough\\O --\\O the\\O food\\O is\\O delicious\\O and\\O homemade\\O with\\O the\\O perfect\\O balance\\O of\\O herbs\\O and\\O tomatoes\\O .\\O", "sentiment": "positive"}, {"uid": "1287538:3-1", "target_tags": "The\\O food\\O is\\O a\\O diamond\\O in\\O rough\\O --\\O the\\O food\\O is\\O delicious\\O and\\O homemade\\O with\\O the\\O perfect\\O balance\\B of\\I herbs\\I and\\I tomatoes\\I .\\O", "opinion_tags": "The\\O food\\O is\\O a\\O diamond\\O in\\O rough\\O --\\O the\\O food\\O is\\O delicious\\O and\\O homemade\\O with\\O the\\O perfect\\B balance\\O of\\O herbs\\O and\\O tomatoes\\O .\\O", "sentiment": "positive"}]}, {"id": "1486041:0", "sentence": "The atmosphere is nothing special , but it feels like a Sushi establishment in Tokyo .", "postag": ["DT", "NN", "VBZ", "NN", "JJ", ",", "CC", "PRP", "VBZ", "IN", "DT", "NNP", "NN", "IN", "NNP", "."], "head": [2, 4, 4, 0, 4, 9, 9, 9, 4, 13, 13, 13, 9, 15, 13, 4], "deprel": ["det", "nsubj", "cop", "root", "amod", "punct", "cc", "nsubj", "conj", "case", "det", "compound", "obl", "case", "nmod", "punct"], "triples": [{"uid": "1486041:0-0", "target_tags": "The\\O atmosphere\\B is\\O nothing\\O special\\O ,\\O but\\O it\\O feels\\O like\\O a\\O Sushi\\O establishment\\O in\\O Tokyo\\O .\\O", "opinion_tags": "The\\O atmosphere\\O is\\O nothing\\B special\\I ,\\O but\\O it\\O feels\\O like\\O a\\O Sushi\\O establishment\\O in\\O Tokyo\\O .\\O", "sentiment": "positive"}]}, {"id": "1586174:6", "sentence": "the waitstaffs are nice though .", "postag": ["DT", "NNPS", "VBP", "JJ", "RB", "."], "head": [2, 4, 4, 0, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "advmod", "punct"], "triples": [{"uid": "1586174:6-0", "target_tags": "the\\O waitstaffs\\B are\\O nice\\O though\\O .\\O", "opinion_tags": "the\\O waitstaffs\\O are\\O nice\\B though\\O .\\O", "sentiment": "positive"}]}, {"id": "445960:3", "sentence": "The food is good , especially their more basic dishes , and the drinks are delicious .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "RB", "PRP$", "RBR", "JJ", "NNS", ",", "CC", "DT", "NNS", "VBP", "JJ", "."], "head": [2, 4, 4, 0, 4, 10, 10, 9, 10, 4, 16, 16, 14, 16, 16, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "advmod", "nmod:poss", "advmod", "amod", "parataxis", "punct", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "445960:3-0", "target_tags": "The\\O food\\B is\\O good\\O ,\\O especially\\O their\\O more\\O basic\\O dishes\\O ,\\O and\\O the\\O drinks\\O are\\O delicious\\O .\\O", "opinion_tags": "The\\O food\\O is\\O good\\B ,\\O especially\\O their\\O more\\O basic\\O dishes\\O ,\\O and\\O the\\O drinks\\O are\\O delicious\\O .\\O", "sentiment": "positive"}, {"uid": "445960:3-1", "target_tags": "The\\O food\\O is\\O good\\O ,\\O especially\\O their\\O more\\O basic\\B dishes\\I ,\\O and\\O the\\O drinks\\O are\\O delicious\\O .\\O", "opinion_tags": "The\\O food\\O is\\O good\\B ,\\O especially\\O their\\O more\\O basic\\O dishes\\O ,\\O and\\O the\\O drinks\\O are\\O delicious\\O .\\O", "sentiment": "positive"}, {"uid": "445960:3-2", "target_tags": "The\\O food\\O is\\O good\\O ,\\O especially\\O their\\O more\\O basic\\O dishes\\O ,\\O and\\O the\\O drinks\\B are\\O delicious\\O .\\O", "opinion_tags": "The\\O food\\O is\\O good\\O ,\\O especially\\O their\\O more\\O basic\\O dishes\\O ,\\O and\\O the\\O drinks\\O are\\O delicious\\B .\\O", "sentiment": "positive"}]}, {"id": "707917:2", "sentence": "Rao 's has the best service and atmosphere in NYC .", "postag": ["NNP", "POS", "VBZ", "DT", "JJS", "NN", "CC", "NN", "IN", "NNP", "."], "head": [3, 1, 0, 6, 6, 3, 8, 6, 10, 6, 3], "deprel": ["nsubj", "case", "root", "det", "amod", "obj", "cc", "conj", "case", "nmod", "punct"], "triples": [{"uid": "707917:2-0", "target_tags": "Rao\\O 's\\O has\\O the\\O best\\O service\\B and\\O atmosphere\\O in\\O NYC\\O .\\O", "opinion_tags": "Rao\\O 's\\O has\\O the\\O best\\B service\\O and\\O atmosphere\\O in\\O NYC\\O .\\O", "sentiment": "positive"}, {"uid": "707917:2-1", "target_tags": "Rao\\O 's\\O has\\O the\\O best\\O service\\O and\\O atmosphere\\B in\\O NYC\\O .\\O", "opinion_tags": "Rao\\O 's\\O has\\O the\\O best\\B service\\O and\\O atmosphere\\O in\\O NYC\\O .\\O", "sentiment": "positive"}]}, {"id": "1707201:1", "sentence": "The food was good .", "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "1707201:1-0", "target_tags": "The\\O food\\B was\\O good\\O .\\O", "opinion_tags": "The\\O food\\O was\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "1289424:5", "sentence": "I highly recommend visiting this restaurant and having dinner and drinks !", "postag": ["PRP", "RB", "VBP", "VBG", "DT", "NN", "CC", "VBG", "NN", "CC", "NNS", "."], "head": [3, 3, 0, 3, 6, 4, 8, 4, 8, 11, 9, 3], "deprel": ["nsubj", "advmod", "root", "xcomp", "det", "obj", "cc", "conj", "obj", "cc", "conj", "punct"], "triples": [{"uid": "1289424:5-0", "target_tags": "I\\O highly\\O recommend\\O visiting\\O this\\O restaurant\\B and\\O having\\O dinner\\O and\\O drinks\\O !\\O", "opinion_tags": "I\\O highly\\O recommend\\B visiting\\O this\\O restaurant\\O and\\O having\\O dinner\\O and\\O drinks\\O !\\O", "sentiment": "positive"}]}, {"id": "1338124:0", "sentence": "This is some really good , inexpensive sushi .", "postag": ["DT", "VBZ", "DT", "RB", "JJ", ",", "JJ", "NN", "."], "head": [8, 8, 8, 5, 8, 8, 8, 0, 8], "deprel": ["nsubj", "cop", "det", "advmod", "amod", "punct", "amod", "root", "punct"], "triples": [{"uid": "1338124:0-0", "target_tags": "This\\O is\\O some\\O really\\O good\\O ,\\O inexpensive\\O sushi\\B .\\O", "opinion_tags": "This\\O is\\O some\\O really\\O good\\B ,\\O inexpensive\\B sushi\\O .\\O", "sentiment": "positive"}]}, {"id": "620892:6", "sentence": "Food was just average ... if they lowered the prices just a bit , it would be a bigger draw .", "postag": ["NN", "VBD", "RB", "JJ", ",", "IN", "PRP", "VBD", "DT", "NNS", "RB", "DT", "NN", ",", "PRP", "MD", "VB", "DT", "JJR", "NN", "."], "head": [4, 4, 4, 0, 4, 8, 8, 20, 10, 8, 13, 13, 8, 20, 20, 20, 20, 20, 20, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct", "mark", "nsubj", "advcl", "det", "obj", "advmod", "det", "obl:npmod", "punct", "nsubj", "aux", "cop", "det", "amod", "parataxis", "punct"], "triples": [{"uid": "620892:6-0", "target_tags": "Food\\B was\\O just\\O average\\O ...\\O if\\O they\\O lowered\\O the\\O prices\\O just\\O a\\O bit\\O ,\\O it\\O would\\O be\\O a\\O bigger\\O draw\\O .\\O", "opinion_tags": "Food\\O was\\O just\\O average\\B ...\\O if\\O they\\O lowered\\O the\\O prices\\O just\\O a\\O bit\\O ,\\O it\\O would\\O be\\O a\\O bigger\\O draw\\O .\\O", "sentiment": "neutral"}]}, {"id": "757762:1", "sentence": "The only problem is that the manager is a complete incompetent .", "postag": ["DT", "JJ", "NN", "VBZ", "IN", "DT", "NN", "VBZ", "DT", "JJ", "JJ", "."], "head": [3, 3, 4, 0, 11, 7, 11, 11, 11, 11, 4, 4], "deprel": ["det", "amod", "nsubj", "root", "mark", "det", "nsubj", "cop", "det", "amod", "ccomp", "punct"], "triples": [{"uid": "757762:1-0", "target_tags": "The\\O only\\O problem\\O is\\O that\\O the\\O manager\\B is\\O a\\O complete\\O incompetent\\O .\\O", "opinion_tags": "The\\O only\\O problem\\O is\\O that\\O the\\O manager\\O is\\O a\\O complete\\O incompetent\\B .\\O", "sentiment": "negative"}]}, {"id": "610611:6", "sentence": "skip dessert .", "postag": ["VB", "NN", "."], "head": [0, 1, 1], "deprel": ["root", "obj", "punct"], "triples": [{"uid": "610611:6-0", "target_tags": "skip\\O dessert\\B .\\O", "opinion_tags": "skip\\B dessert\\O .\\O", "sentiment": "negative"}]}, {"id": "1609375:4", "sentence": "Just because it 's cheap does NOT mean the portions are small or the food is nasty , IT IS GREAT !", "postag": ["RB", "IN", "PRP", "VBZ", "JJ", "VBZ", "RB", "VB", "DT", "NNS", "VBP", "JJ", "CC", "DT", "NN", "VBZ", "JJ", ",", "PRP", "VBZ", "JJ", "."], "head": [8, 5, 5, 5, 8, 8, 8, 0, 10, 12, 12, 8, 17, 15, 17, 17, 12, 21, 21, 21, 8, 8], "deprel": ["advmod", "mark", "nsubj", "cop", "advcl", "aux", "advmod", "root", "det", "nsubj", "cop", "ccomp", "cc", "det", "nsubj", "cop", "conj", "punct", "nsubj", "cop", "parataxis", "punct"], "triples": [{"uid": "1609375:4-0", "target_tags": "Just\\O because\\O it\\O 's\\O cheap\\O does\\O NOT\\O mean\\O the\\O portions\\B are\\O small\\O or\\O the\\O food\\O is\\O nasty\\O ,\\O IT\\O IS\\O GREAT\\O !\\O", "opinion_tags": "Just\\O because\\O it\\O 's\\O cheap\\O does\\O NOT\\O mean\\O the\\O portions\\O are\\O small\\B or\\O the\\O food\\O is\\O nasty\\O ,\\O IT\\O IS\\O GREAT\\O !\\O", "sentiment": "positive"}, {"uid": "1609375:4-1", "target_tags": "Just\\O because\\O it\\O 's\\O cheap\\O does\\O NOT\\O mean\\O the\\O portions\\O are\\O small\\O or\\O the\\O food\\B is\\O nasty\\O ,\\O IT\\O IS\\O GREAT\\O !\\O", "opinion_tags": "Just\\O because\\O it\\O 's\\O cheap\\O does\\O NOT\\O mean\\O the\\O portions\\O are\\O small\\O or\\O the\\O food\\O is\\O nasty\\B ,\\O IT\\O IS\\O GREAT\\O !\\O", "sentiment": "positive"}]}, {"id": "1573534:3", "sentence": "I am happy i did the food was awsome .", "postag": ["PRP", "VBP", "JJ", "PRP", "VBD", "DT", "NN", "VBD", "JJ", "."], "head": [3, 3, 0, 9, 9, 7, 9, 9, 3, 3], "deprel": ["nsubj", "cop", "root", "nsubj", "aux", "det", "nsubj", "cop", "ccomp", "punct"], "triples": [{"uid": "1573534:3-0", "target_tags": "I\\O am\\O happy\\O i\\O did\\O the\\O food\\B was\\O awsome\\O .\\O", "opinion_tags": "I\\O am\\O happy\\O i\\O did\\O the\\O food\\O was\\O awsome\\B .\\O", "sentiment": "positive"}]}, {"id": "1572033:1", "sentence": "Decor is nice though service can be spotty .", "postag": ["NN", "VBZ", "JJ", "IN", "NN", "MD", "VB", "JJ", "."], "head": [3, 3, 0, 8, 8, 8, 8, 3, 3], "deprel": ["nsubj", "cop", "root", "mark", "nsubj", "aux", "cop", "advcl", "punct"], "triples": [{"uid": "1572033:1-0", "target_tags": "Decor\\B is\\O nice\\O though\\O service\\O can\\O be\\O spotty\\O .\\O", "opinion_tags": "Decor\\O is\\O nice\\B though\\O service\\O can\\O be\\O spotty\\O .\\O", "sentiment": "positive"}, {"uid": "1572033:1-1", "target_tags": "Decor\\O is\\O nice\\O though\\O service\\B can\\O be\\O spotty\\O .\\O", "opinion_tags": "Decor\\O is\\O nice\\O though\\O service\\O can\\O be\\O spotty\\B .\\O", "sentiment": "negative"}]}, {"id": "494850:3", "sentence": "The food now is inconsistent .", "postag": ["DT", "NN", "RB", "VBZ", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "advmod", "cop", "root", "punct"], "triples": [{"uid": "494850:3-0", "target_tags": "The\\O food\\B now\\O is\\O inconsistent\\O .\\O", "opinion_tags": "The\\O food\\O now\\O is\\O inconsistent\\B .\\O", "sentiment": "negative"}]}, {"id": "1632445:6", "sentence": "The lobster sandwich is $ 24 and although it was good it was not nearly enough to warrant that price .", "postag": ["DT", "NN", "NN", "VBZ", "$", "CD", "CC", "IN", "PRP", "VBD", "JJ", "PRP", "VBD", "RB", "RB", "JJ", "TO", "VB", "DT", "NN", "."], "head": [3, 3, 5, 5, 0, 5, 16, 11, 11, 11, 16, 16, 16, 16, 16, 5, 18, 16, 20, 18, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "nummod", "cc", "mark", "expl", "cop", "advcl", "nsubj", "cop", "advmod", "advmod", "conj", "mark", "csubj", "det", "obj", "punct"], "triples": [{"uid": "1632445:6-0", "target_tags": "The\\O lobster\\B sandwich\\I is\\O $\\O 24\\O and\\O although\\O it\\O was\\O good\\O it\\O was\\O not\\O nearly\\O enough\\O to\\O warrant\\O that\\O price\\O .\\O", "opinion_tags": "The\\O lobster\\O sandwich\\O is\\O $\\O 24\\O and\\O although\\O it\\O was\\O good\\B it\\O was\\O not\\B nearly\\I enough\\I to\\I warrant\\I that\\I price\\I .\\O", "sentiment": "negative"}]}, {"id": "397331:3", "sentence": "Not only is the cuisine the best around , the service has always been attentive and charming .", "postag": ["RB", "RB", "VBZ", "DT", "NN", "DT", "JJS", "RB", ",", "DT", "NN", "VBZ", "RB", "VBN", "JJ", "CC", "JJ", "."], "head": [2, 8, 8, 5, 8, 7, 8, 0, 8, 11, 15, 15, 15, 15, 8, 17, 15, 8], "deprel": ["advmod", "advmod", "cop", "det", "nsubj", "det", "amod", "root", "punct", "det", "nsubj", "aux", "advmod", "cop", "parataxis", "cc", "conj", "punct"], "triples": [{"uid": "397331:3-0", "target_tags": "Not\\O only\\O is\\O the\\O cuisine\\B the\\O best\\O around\\O ,\\O the\\O service\\O has\\O always\\O been\\O attentive\\O and\\O charming\\O .\\O", "opinion_tags": "Not\\O only\\O is\\O the\\O cuisine\\O the\\O best\\B around\\O ,\\O the\\O service\\O has\\O always\\O been\\O attentive\\O and\\O charming\\O .\\O", "sentiment": "positive"}, {"uid": "397331:3-1", "target_tags": "Not\\O only\\O is\\O the\\O cuisine\\O the\\O best\\O around\\O ,\\O the\\O service\\B has\\O always\\O been\\O attentive\\O and\\O charming\\O .\\O", "opinion_tags": "Not\\O only\\O is\\O the\\O cuisine\\O the\\O best\\O around\\O ,\\O the\\O service\\O has\\O always\\O been\\O attentive\\B and\\O charming\\B .\\O", "sentiment": "positive"}]}, {"id": "1457607:0", "sentence": "The place is a lot of fun .", "postag": ["DT", "NN", "VBZ", "DT", "NN", "IN", "NN", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "det", "root", "case", "nmod", "punct"], "triples": [{"uid": "1457607:0-0", "target_tags": "The\\O place\\B is\\O a\\O lot\\O of\\O fun\\O .\\O", "opinion_tags": "The\\O place\\O is\\O a\\O lot\\O of\\O fun\\B .\\O", "sentiment": "positive"}]}, {"id": "503807:4", "sentence": "I choose to go with one of the special , the braised lamb shank in red wine , which was excellent .", "postag": ["PRP", "VBP", "TO", "VB", "IN", "CD", "IN", "DT", "JJ", ",", "DT", "VBN", "NN", "NN", "IN", "JJ", "NN", ",", "WDT", "VBD", "JJ", "."], "head": [2, 0, 4, 2, 6, 4, 14, 14, 14, 14, 14, 14, 14, 6, 17, 17, 14, 6, 21, 21, 14, 2], "deprel": ["nsubj", "root", "mark", "xcomp", "case", "obl", "case", "det", "amod", "punct", "det", "amod", "compound", "appos", "case", "amod", "nmod", "punct", "nsubj", "cop", "acl:relcl", "punct"], "triples": [{"uid": "503807:4-0", "target_tags": "I\\O choose\\O to\\O go\\O with\\O one\\O of\\O the\\O special\\O ,\\O the\\O braised\\B lamb\\I shank\\I in\\I red\\I wine\\I ,\\O which\\O was\\O excellent\\O .\\O", "opinion_tags": "I\\O choose\\O to\\O go\\O with\\O one\\O of\\O the\\O special\\O ,\\O the\\O braised\\O lamb\\O shank\\O in\\O red\\O wine\\O ,\\O which\\O was\\O excellent\\B .\\O", "sentiment": "positive"}]}, {"id": "1632445:3", "sentence": "The service is good and ambience is good for a date or group outing .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "NN", "VBZ", "JJ", "IN", "DT", "NN", "CC", "NN", "NN", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 11, 11, 8, 14, 14, 11, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "nsubj", "cop", "conj", "case", "det", "obl", "cc", "compound", "conj", "punct"], "triples": [{"uid": "1632445:3-0", "target_tags": "The\\O service\\B is\\O good\\O and\\O ambience\\O is\\O good\\O for\\O a\\O date\\O or\\O group\\O outing\\O .\\O", "opinion_tags": "The\\O service\\O is\\O good\\B and\\O ambience\\O is\\O good\\O for\\O a\\O date\\O or\\O group\\O outing\\O .\\O", "sentiment": "positive"}, {"uid": "1632445:3-1", "target_tags": "The\\O service\\O is\\O good\\O and\\O ambience\\B is\\O good\\O for\\O a\\O date\\O or\\O group\\O outing\\O .\\O", "opinion_tags": "The\\O service\\O is\\O good\\O and\\O ambience\\O is\\O good\\B for\\O a\\O date\\O or\\O group\\O outing\\O .\\O", "sentiment": "positive"}]}, {"id": "1432551:1", "sentence": "The portions are large and the servers always surprise us with a different starter .", "postag": ["DT", "NNS", "VBP", "JJ", "CC", "DT", "NNS", "RB", "VBP", "PRP", "IN", "DT", "JJ", "NN", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 9, 14, 14, 14, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "advmod", "conj", "obj", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "1432551:1-0", "target_tags": "The\\O portions\\B are\\O large\\O and\\O the\\O servers\\O always\\O surprise\\O us\\O with\\O a\\O different\\O starter\\O .\\O", "opinion_tags": "The\\O portions\\O are\\O large\\B and\\O the\\O servers\\O always\\O surprise\\O us\\O with\\O a\\O different\\O starter\\O .\\O", "sentiment": "positive"}]}, {"id": "461928:1", "sentence": "The pizza is delicious and the proprietor is one of the nicest in NYC .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "CD", "IN", "DT", "JJS", "IN", "NNP", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 12, 12, 9, 14, 12, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "case", "det", "nmod", "case", "obl", "punct"], "triples": [{"uid": "461928:1-0", "target_tags": "The\\O pizza\\B is\\O delicious\\O and\\O the\\O proprietor\\O is\\O one\\O of\\O the\\O nicest\\O in\\O NYC\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O delicious\\B and\\O the\\O proprietor\\O is\\O one\\O of\\O the\\O nicest\\O in\\O NYC\\O .\\O", "sentiment": "positive"}, {"uid": "461928:1-1", "target_tags": "The\\O pizza\\O is\\O delicious\\O and\\O the\\O proprietor\\B is\\O one\\O of\\O the\\O nicest\\O in\\O NYC\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O delicious\\O and\\O the\\O proprietor\\O is\\O one\\O of\\O the\\O nicest\\B in\\O NYC\\O .\\O", "sentiment": "positive"}]}, {"id": "1471273:3", "sentence": "Prices too high for this cramped and unappealing resturant .", "postag": ["NNS", "RB", "JJ", "IN", "DT", "JJ", "CC", "JJ", "NN", "."], "head": [3, 3, 0, 9, 9, 9, 8, 6, 3, 3], "deprel": ["nsubj", "advmod", "root", "case", "det", "amod", "cc", "conj", "obl", "punct"], "triples": [{"uid": "1471273:3-0", "target_tags": "Prices\\O too\\O high\\O for\\O this\\O cramped\\O and\\O unappealing\\O resturant\\B .\\O", "opinion_tags": "Prices\\O too\\O high\\O for\\O this\\O cramped\\B and\\O unappealing\\B resturant\\O .\\O", "sentiment": "negative"}]}, {"id": "470488:3", "sentence": "The rice was poor quality and was cooked so badly it was hard .", "postag": ["DT", "NN", "VBD", "JJ", "NN", "CC", "VBD", "VBN", "RB", "RB", "PRP", "VBD", "JJ", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 10, 8, 13, 13, 5, 5], "deprel": ["det", "nsubj", "cop", "amod", "root", "cc", "aux:pass", "conj", "advmod", "advmod", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "470488:3-0", "target_tags": "The\\O rice\\B was\\O poor\\O quality\\O and\\O was\\O cooked\\O so\\O badly\\O it\\O was\\O hard\\O .\\O", "opinion_tags": "The\\O rice\\O was\\O poor\\B quality\\I and\\O was\\O cooked\\B so\\I badly\\I it\\O was\\O hard\\B .\\O", "sentiment": "negative"}]}, {"id": "1340075:5", "sentence": "The takeout is great too since they give high quality tupperware as well .", "postag": ["DT", "NN", "VBZ", "JJ", "RB", "IN", "PRP", "VBP", "JJ", "NN", "NN", "RB", "RB", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 11, 11, 8, 8, 12, 4], "deprel": ["det", "nsubj", "cop", "root", "advmod", "mark", "nsubj", "advcl", "amod", "compound", "obj", "advmod", "fixed", "punct"], "triples": [{"uid": "1340075:5-0", "target_tags": "The\\O takeout\\B is\\O great\\O too\\O since\\O they\\O give\\O high\\O quality\\O tupperware\\O as\\O well\\O .\\O", "opinion_tags": "The\\O takeout\\O is\\O great\\B too\\O since\\O they\\O give\\O high\\O quality\\O tupperware\\O as\\O well\\O .\\O", "sentiment": "positive"}]}, {"id": "1431154:1", "sentence": "If you like spicy food get the chicken vindaloo .", "postag": ["IN", "PRP", "VBP", "JJ", "NN", "VB", "DT", "NN", "NN", "."], "head": [3, 3, 6, 5, 3, 0, 9, 9, 6, 6], "deprel": ["mark", "nsubj", "advcl", "amod", "obj", "root", "det", "compound", "obj", "punct"], "triples": [{"uid": "1431154:1-0", "target_tags": "If\\O you\\O like\\O spicy\\O food\\O get\\O the\\O chicken\\B vindaloo\\I .\\O", "opinion_tags": "If\\O you\\O like\\O spicy\\O food\\O get\\B the\\O chicken\\O vindaloo\\O .\\O", "sentiment": "positive"}]}, {"id": "680345:1", "sentence": "I really recommend the very simple Unda ( Egg ) rolls .", "postag": ["PRP", "RB", "VBP", "DT", "RB", "JJ", "NN", "-LRB-", "NN", "-RRB-", "NNS", "."], "head": [3, 3, 0, 11, 6, 11, 11, 9, 11, 9, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "advmod", "amod", "compound", "punct", "compound", "punct", "obj", "punct"], "triples": [{"uid": "680345:1-0", "target_tags": "I\\O really\\O recommend\\O the\\O very\\O simple\\O Unda\\B (\\I Egg\\I )\\I rolls\\I .\\O", "opinion_tags": "I\\O really\\O recommend\\B the\\O very\\O simple\\B Unda\\O (\\O Egg\\O )\\O rolls\\O .\\O", "sentiment": "positive"}]}, {"id": "1451107:5", "sentence": "And really large portions .", "postag": ["CC", "RB", "JJ", "NNS", "."], "head": [4, 3, 4, 0, 4], "deprel": ["cc", "advmod", "amod", "root", "punct"], "triples": [{"uid": "1451107:5-0", "target_tags": "And\\O really\\O large\\O portions\\B .\\O", "opinion_tags": "And\\O really\\O large\\B portions\\O .\\O", "sentiment": "positive"}]}, {"id": "1340075:0", "sentence": "<PERSON> gets big Ups for a fine establishment .", "postag": ["NNP", "NNP", "VBZ", "JJ", "NNS", "IN", "DT", "JJ", "NN", "."], "head": [2, 3, 0, 5, 3, 9, 9, 9, 3, 3], "deprel": ["compound", "nsubj", "root", "amod", "obj", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "1340075:0-0", "target_tags": "Big\\B Wong\\I gets\\O big\\O Ups\\O for\\O a\\O fine\\O establishment\\O .\\O", "opinion_tags": "Big\\O Wong\\O gets\\O big\\B Ups\\I for\\O a\\O fine\\B establishment\\O .\\O", "sentiment": "positive"}]}, {"id": "490565:3", "sentence": "But the best part about LS is the late night atmosphere , delightfully free of the BTs .", "postag": ["CC", "DT", "JJS", "NN", "IN", "NNP", "VBZ", "DT", "JJ", "NN", "NN", ",", "RB", "JJ", "IN", "DT", "NNS", "."], "head": [11, 4, 4, 11, 6, 4, 11, 11, 11, 11, 0, 11, 14, 11, 17, 17, 14, 11], "deprel": ["cc", "det", "amod", "nsubj", "case", "nmod", "cop", "det", "amod", "compound", "root", "punct", "advmod", "amod", "case", "det", "obl", "punct"], "triples": [{"uid": "490565:3-0", "target_tags": "But\\O the\\O best\\O part\\O about\\O LS\\O is\\O the\\O late\\B night\\I atmosphere\\I ,\\O delightfully\\O free\\O of\\O the\\O BTs\\O .\\O", "opinion_tags": "But\\O the\\O best\\B part\\O about\\O LS\\O is\\O the\\O late\\O night\\O atmosphere\\O ,\\O delightfully\\O free\\O of\\O the\\O BTs\\O .\\O", "sentiment": "positive"}]}, {"id": "1253117:2", "sentence": "Two complaints -- their appetizer selection stinks , it would be nice to get some mozzarella sticks on the menu .", "postag": ["CD", "NNS", ",", "PRP$", "NN", "NN", "VBZ", ",", "PRP", "MD", "VB", "JJ", "TO", "VB", "DT", "NN", "NNS", "IN", "DT", "NN", "."], "head": [2, 0, 2, 6, 6, 7, 2, 2, 12, 12, 12, 2, 14, 12, 17, 17, 14, 20, 20, 14, 2], "deprel": ["nummod", "root", "punct", "nmod:poss", "compound", "nsubj", "parataxis", "punct", "expl", "aux", "cop", "parataxis", "mark", "csubj", "det", "compound", "obj", "case", "det", "obl", "punct"], "triples": [{"uid": "1253117:2-0", "target_tags": "Two\\O complaints\\O --\\O their\\O appetizer\\B selection\\I stinks\\O ,\\O it\\O would\\O be\\O nice\\O to\\O get\\O some\\O mozzarella\\O sticks\\O on\\O the\\O menu\\O .\\O", "opinion_tags": "Two\\O complaints\\B --\\O their\\O appetizer\\O selection\\O stinks\\O ,\\O it\\O would\\O be\\O nice\\O to\\O get\\O some\\O mozzarella\\O sticks\\O on\\O the\\O menu\\O .\\O", "sentiment": "negative"}]}, {"id": "709294:3", "sentence": "The portion sizes here are huge , and the sushi is good .", "postag": ["DT", "NN", "VBZ", "RB", "VBP", "JJ", ",", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 3, 6, 3, 6, 0, 12, 12, 10, 12, 12, 6, 6], "deprel": ["det", "nsubj", "csubj", "advmod", "cop", "root", "punct", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "709294:3-0", "target_tags": "The\\O portion\\B sizes\\I here\\O are\\O huge\\O ,\\O and\\O the\\O sushi\\O is\\O good\\O .\\O", "opinion_tags": "The\\O portion\\O sizes\\O here\\O are\\O huge\\B ,\\O and\\O the\\O sushi\\O is\\O good\\O .\\O", "sentiment": "positive"}, {"uid": "709294:3-1", "target_tags": "The\\O portion\\O sizes\\O here\\O are\\O huge\\O ,\\O and\\O the\\O sushi\\B is\\O good\\O .\\O", "opinion_tags": "The\\O portion\\O sizes\\O here\\O are\\O huge\\O ,\\O and\\O the\\O sushi\\O is\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "713152:1", "sentence": "I got the $ 10 10-piece dim sum combo , every bite of which was great .", "postag": ["PRP", "VBD", "DT", "$", "CD", "JJ", "JJ", "NN", "NN", ",", "DT", "NN", "IN", "WDT", "VBD", "JJ", "."], "head": [2, 0, 9, 9, 4, 9, 9, 9, 2, 2, 12, 16, 14, 12, 16, 9, 2], "deprel": ["nsubj", "root", "det", "compound", "nummod", "amod", "amod", "compound", "obj", "punct", "det", "nsubj", "case", "nmod", "cop", "acl:relcl", "punct"], "triples": [{"uid": "713152:1-0", "target_tags": "I\\O got\\O the\\O $\\B 10\\I 10-piece\\I dim\\I sum\\I combo\\I ,\\O every\\O bite\\O of\\O which\\O was\\O great\\O .\\O", "opinion_tags": "I\\O got\\O the\\O $\\O 10\\O 10-piece\\O dim\\O sum\\O combo\\O ,\\O every\\O bite\\O of\\O which\\O was\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "527109:2", "sentence": "The anti-pasta was excellent , especially the calamari , as were the filling pasta mains .", "postag": ["DT", "NN", "VBD", "JJ", ",", "RB", "DT", "NN", ",", "IN", "VBD", "DT", "VBG", "NN", "NNS", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 4, 15, 15, 15, 15, 15, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "advmod", "det", "parataxis", "punct", "mark", "cop", "det", "amod", "compound", "advcl", "punct"], "triples": [{"uid": "527109:2-0", "target_tags": "The\\O anti-pasta\\B was\\O excellent\\O ,\\O especially\\O the\\O calamari\\O ,\\O as\\O were\\O the\\O filling\\O pasta\\O mains\\O .\\O", "opinion_tags": "The\\O anti-pasta\\O was\\O excellent\\B ,\\O especially\\O the\\O calamari\\O ,\\O as\\O were\\O the\\O filling\\O pasta\\O mains\\O .\\O", "sentiment": "positive"}, {"uid": "527109:2-1", "target_tags": "The\\O anti-pasta\\O was\\O excellent\\O ,\\O especially\\O the\\O calamari\\B ,\\O as\\O were\\O the\\O filling\\O pasta\\O mains\\O .\\O", "opinion_tags": "The\\O anti-pasta\\O was\\O excellent\\B ,\\O especially\\O the\\O calamari\\O ,\\O as\\O were\\O the\\O filling\\O pasta\\O mains\\O .\\O", "sentiment": "positive"}, {"uid": "527109:2-2", "target_tags": "The\\O anti-pasta\\O was\\O excellent\\O ,\\O especially\\O the\\O calamari\\O ,\\O as\\O were\\O the\\O filling\\O pasta\\B mains\\I .\\O", "opinion_tags": "The\\O anti-pasta\\O was\\O excellent\\B ,\\O especially\\O the\\O calamari\\O ,\\O as\\O were\\O the\\O filling\\O pasta\\O mains\\O .\\O", "sentiment": "positive"}]}, {"id": "1726427:3", "sentence": "Definitely a great spot for a nice occasion or date .", "postag": ["RB", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", "CC", "NN", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 10, 8, 4], "deprel": ["advmod", "det", "amod", "root", "case", "det", "amod", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "1726427:3-0", "target_tags": "Definitely\\O a\\O great\\O spot\\B for\\O a\\O nice\\O occasion\\O or\\O date\\O .\\O", "opinion_tags": "Definitely\\O a\\O great\\B spot\\O for\\O a\\O nice\\O occasion\\O or\\O date\\O .\\O", "sentiment": "positive"}]}, {"id": "433878:0", "sentence": "This is a nice pizza place with good selection of thin crust pizza including the Basil slice .", "postag": ["DT", "VBZ", "DT", "JJ", "NN", "NN", "IN", "JJ", "NN", "IN", "JJ", "NN", "NN", "VBG", "DT", "NN", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 13, 13, 13, 9, 17, 17, 17, 13, 6], "deprel": ["nsubj", "cop", "det", "amod", "compound", "root", "case", "amod", "nmod", "case", "amod", "compound", "nmod", "case", "det", "compound", "nmod", "punct"], "triples": [{"uid": "433878:0-0", "target_tags": "This\\O is\\O a\\O nice\\O pizza\\O place\\O with\\O good\\O selection\\B of\\I thin\\I crust\\I pizza\\I including\\O the\\O Basil\\O slice\\O .\\O", "opinion_tags": "This\\O is\\O a\\O nice\\O pizza\\O place\\O with\\O good\\B selection\\O of\\O thin\\O crust\\O pizza\\O including\\O the\\O Basil\\O slice\\O .\\O", "sentiment": "positive"}, {"uid": "433878:0-1", "target_tags": "This\\O is\\O a\\O nice\\O pizza\\B place\\I with\\O good\\O selection\\O of\\O thin\\O crust\\O pizza\\O including\\O the\\O Basil\\O slice\\O .\\O", "opinion_tags": "This\\O is\\O a\\O nice\\B pizza\\O place\\O with\\O good\\O selection\\O of\\O thin\\O crust\\O pizza\\O including\\O the\\O Basil\\O slice\\O .\\O", "sentiment": "positive"}, {"uid": "433878:0-2", "target_tags": "This\\O is\\O a\\O nice\\O pizza\\O place\\O with\\O good\\O selection\\O of\\O thin\\O crust\\O pizza\\O including\\O the\\O Basil\\B slice\\I .\\O", "opinion_tags": "This\\O is\\O a\\O nice\\O pizza\\O place\\O with\\O good\\B selection\\O of\\O thin\\O crust\\O pizza\\O including\\O the\\O Basil\\O slice\\O .\\O", "sentiment": "positive"}]}, {"id": "1308557:0", "sentence": "The service was the only thing good about this restaurant .", "postag": ["DT", "NN", "VBD", "DT", "JJ", "NN", "JJ", "IN", "DT", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 6, 10, 10, 7, 6], "deprel": ["det", "nsubj", "cop", "det", "amod", "root", "amod", "case", "det", "obl", "punct"], "triples": [{"uid": "1308557:0-0", "target_tags": "The\\O service\\B was\\O the\\O only\\O thing\\O good\\O about\\O this\\O restaurant\\O .\\O", "opinion_tags": "The\\O service\\O was\\O the\\O only\\O thing\\O good\\B about\\O this\\O restaurant\\O .\\O", "sentiment": "positive"}]}, {"id": "1269216:4", "sentence": "Worth visiting the 1st Ave spot because it is the original store .", "postag": ["JJ", "VBG", "DT", "JJ", "NNP", "NN", "IN", "PRP", "VBZ", "DT", "JJ", "NN", "."], "head": [0, 1, 6, 5, 6, 2, 12, 12, 12, 12, 12, 2, 1], "deprel": ["root", "xcomp", "det", "amod", "compound", "obj", "mark", "nsubj", "cop", "det", "amod", "advcl", "punct"], "triples": [{"uid": "1269216:4-0", "target_tags": "Worth\\O visiting\\O the\\O 1st\\B Ave\\I spot\\I because\\O it\\O is\\O the\\O original\\O store\\O .\\O", "opinion_tags": "Worth\\B visiting\\I the\\O 1st\\O Ave\\O spot\\O because\\O it\\O is\\O the\\O original\\O store\\O .\\O", "sentiment": "positive"}]}, {"id": "1269216:1", "sentence": "The staff is no nonsense .", "postag": ["DT", "NN", "VBZ", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "det", "root", "punct"], "triples": [{"uid": "1269216:1-0", "target_tags": "The\\O staff\\B is\\O no\\O nonsense\\O .\\O", "opinion_tags": "The\\O staff\\O is\\O no\\B nonsense\\I .\\O", "sentiment": "positive"}]}, {"id": "1713185:0", "sentence": "This place is really trendi but they have forgotten about the most important part of a restaurant , the food .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "PRP", "VBP", "VBN", "IN", "DT", "RBS", "JJ", "NN", "IN", "DT", "NN", ",", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 9, 9, 9, 5, 14, 14, 13, 14, 9, 17, 17, 14, 20, 20, 14, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "nsubj", "aux", "conj", "case", "det", "advmod", "amod", "obl", "case", "det", "nmod", "punct", "det", "appos", "punct"], "triples": [{"uid": "1713185:0-0", "target_tags": "This\\O place\\O is\\O really\\O trendi\\O but\\O they\\O have\\O forgotten\\O about\\O the\\O most\\O important\\O part\\O of\\O a\\O restaurant\\O ,\\O the\\O food\\B .\\O", "opinion_tags": "This\\O place\\O is\\O really\\O trendi\\O but\\O they\\O have\\O forgotten\\B about\\O the\\O most\\O important\\O part\\O of\\O a\\O restaurant\\O ,\\O the\\O food\\O .\\O", "sentiment": "negative"}, {"uid": "1713185:0-1", "target_tags": "This\\O place\\B is\\O really\\O trendi\\O but\\O they\\O have\\O forgotten\\O about\\O the\\O most\\O important\\O part\\O of\\O a\\O restaurant\\O ,\\O the\\O food\\O .\\O", "opinion_tags": "This\\O place\\O is\\O really\\O trendi\\B but\\O they\\O have\\O forgotten\\O about\\O the\\O most\\O important\\O part\\O of\\O a\\O restaurant\\O ,\\O the\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "1357554:4", "sentence": "It 's a nice place to relax and have conversation .", "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "CC", "VB", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 9, 7, 9, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "cc", "conj", "obj", "punct"], "triples": [{"uid": "1357554:4-0", "target_tags": "It\\O 's\\O a\\O nice\\O place\\B to\\O relax\\O and\\O have\\O conversation\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O nice\\B place\\O to\\O relax\\O and\\O have\\O conversation\\O .\\O", "sentiment": "positive"}]}, {"id": "1303984:0", "sentence": "This restaurant was way overhyped .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "1303984:0-0", "target_tags": "This\\O restaurant\\B was\\O way\\O overhyped\\O .\\O", "opinion_tags": "This\\O restaurant\\O was\\O way\\O overhyped\\B .\\O", "sentiment": "negative"}]}, {"id": "1404042:5", "sentence": "One of us actually liked the expresso - that 's it .", "postag": ["CD", "IN", "PRP", "RB", "VBD", "DT", "NN", ",", "DT", "VBZ", "PRP", "."], "head": [5, 3, 1, 5, 0, 7, 5, 5, 11, 11, 5, 5], "deprel": ["nsubj", "case", "nmod", "advmod", "root", "det", "obj", "punct", "nsubj", "cop", "parataxis", "punct"], "triples": [{"uid": "1404042:5-0", "target_tags": "One\\O of\\O us\\O actually\\O liked\\O the\\O expresso\\B -\\O that\\O 's\\O it\\O .\\O", "opinion_tags": "One\\O of\\O us\\O actually\\O liked\\B the\\O expresso\\O -\\O that\\O 's\\O it\\O .\\O", "sentiment": "positive"}]}, {"id": "1328078:4", "sentence": "We had <PERSON> 's special fried fish and it was amazing .", "postag": ["PRP", "VBD", "NNP", "POS", "JJ", "JJ", "NN", "CC", "PRP", "VBD", "JJ", "."], "head": [2, 0, 7, 3, 7, 7, 2, 11, 11, 11, 2, 2], "deprel": ["nsubj", "root", "nmod:poss", "case", "amod", "amod", "obj", "cc", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "1328078:4-0", "target_tags": "We\\O had\\O Pam\\B 's\\I special\\I fried\\I fish\\I and\\O it\\O was\\O amazing\\O .\\O", "opinion_tags": "We\\O had\\O Pam\\O 's\\O special\\O fried\\O fish\\O and\\O it\\O was\\O amazing\\B .\\O", "sentiment": "positive"}]}, {"id": "1710746:2", "sentence": "My son and his girlfriend both wanted cheeseburgers and they were huge !", "postag": ["PRP$", "NN", "CC", "PRP$", "NN", "DT", "VBD", "NNS", "CC", "PRP", "VBD", "JJ", "."], "head": [2, 7, 5, 5, 2, 5, 0, 7, 12, 12, 12, 7, 7], "deprel": ["nmod:poss", "nsubj", "cc", "nmod:poss", "conj", "det", "root", "obj", "cc", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "1710746:2-0", "target_tags": "My\\O son\\O and\\O his\\O girlfriend\\O both\\O wanted\\O cheeseburgers\\B and\\O they\\O were\\O huge\\O !\\O", "opinion_tags": "My\\O son\\O and\\O his\\O girlfriend\\O both\\O wanted\\O cheeseburgers\\O and\\O they\\O were\\O huge\\B !\\O", "sentiment": "neutral"}]}, {"id": "1723658:2", "sentence": "The dishes offered were unique , very tasty and fresh from the lamb sausages , sardines with biscuits , large whole shrimp to the amazing pistachio ice cream ( the best and freshest I 've ever had ) .", "postag": ["DT", "NNS", "VBD", "VBD", "JJ", ",", "RB", "JJ", "CC", "JJ", "IN", "DT", "NN", "NNS", ",", "NNS", "IN", "NNS", ",", "JJ", "JJ", "NNS", "IN", "DT", "JJ", "NN", "NN", "NN", "-LRB-", "DT", "JJS", "CC", "JJS", "PRP", "VBP", "RB", "VBN", "-RRB-", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 10, 5, 14, 14, 14, 10, 16, 14, 18, 14, 22, 22, 22, 14, 28, 28, 28, 28, 28, 14, 31, 31, 14, 33, 31, 37, 37, 37, 31, 31, 5], "deprel": ["det", "nsubj", "csubj", "cop", "root", "punct", "advmod", "conj", "cc", "conj", "case", "det", "compound", "obl", "punct", "conj", "case", "nmod", "punct", "amod", "amod", "conj", "case", "det", "amod", "compound", "compound", "nmod", "punct", "det", "parataxis", "cc", "conj", "nsubj", "aux", "advmod", "acl:relcl", "punct", "punct"], "triples": [{"uid": "1723658:2-0", "target_tags": "The\\O dishes\\B offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\B ,\\O very\\O tasty\\B and\\O fresh\\B from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "1723658:2-1", "target_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\B ice\\I cream\\I (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\B pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "1297844:2", "sentence": "The bagel was huge .", "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "1297844:2-0", "target_tags": "The\\O bagel\\B was\\O huge\\O .\\O", "opinion_tags": "The\\O bagel\\O was\\O huge\\B .\\O", "sentiment": "positive"}]}, {"id": "1131595:0", "sentence": "This place is incredibly tiny .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "1131595:0-0", "target_tags": "This\\O place\\B is\\O incredibly\\O tiny\\O .\\O", "opinion_tags": "This\\O place\\O is\\O incredibly\\O tiny\\B .\\O", "sentiment": "negative"}]}, {"id": "1230993:4", "sentence": "The food was good too .", "postag": ["DT", "NN", "VBD", "JJ", "RB", "."], "head": [2, 4, 4, 0, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "advmod", "punct"], "triples": [{"uid": "1230993:4-0", "target_tags": "The\\O food\\B was\\O good\\O too\\O .\\O", "opinion_tags": "The\\O food\\O was\\O good\\B too\\O .\\O", "sentiment": "positive"}]}, {"id": "1404042:6", "sentence": "Skip this restaurant , it 's a big disappointment .", "postag": ["VB", "DT", "NN", ",", "PRP", "VBZ", "DT", "JJ", "NN", "."], "head": [0, 3, 1, 1, 9, 9, 9, 9, 1, 1], "deprel": ["root", "det", "obj", "punct", "nsubj", "cop", "det", "amod", "parataxis", "punct"], "triples": [{"uid": "1404042:6-0", "target_tags": "Skip\\O this\\O restaurant\\B ,\\O it\\O 's\\O a\\O big\\O disappointment\\O .\\O", "opinion_tags": "Skip\\B this\\O restaurant\\O ,\\O it\\O 's\\O a\\O big\\O disappointment\\B .\\O", "sentiment": "negative"}]}, {"id": "1748813:0", "sentence": "I stumbled upon this great pizzeria as I explored my new neighborhood .", "postag": ["PRP", "VBD", "IN", "DT", "JJ", "NN", "IN", "PRP", "VBD", "PRP$", "JJ", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 9, 9, 2, 12, 12, 9, 2], "deprel": ["nsubj", "root", "case", "det", "amod", "obl", "mark", "nsubj", "advcl", "nmod:poss", "amod", "obj", "punct"], "triples": [{"uid": "1748813:0-0", "target_tags": "I\\O stumbled\\O upon\\O this\\O great\\O pizzeria\\B as\\O I\\O explored\\O my\\O new\\O neighborhood\\O .\\O", "opinion_tags": "I\\O stumbled\\O upon\\O this\\O great\\B pizzeria\\O as\\O I\\O explored\\O my\\O new\\O neighborhood\\O .\\O", "sentiment": "positive"}]}, {"id": "929329:3", "sentence": "Interesting selection , good wines , service fine , fun decor .", "postag": ["JJ", "NN", ",", "JJ", "NNS", ",", "NN", "JJ", ",", "JJ", "NN", "."], "head": [2, 0, 2, 5, 2, 2, 8, 2, 2, 11, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "compound", "list", "punct", "amod", "list", "punct"], "triples": [{"uid": "929329:3-0", "target_tags": "Interesting\\O selection\\O ,\\O good\\O wines\\B ,\\O service\\O fine\\O ,\\O fun\\O decor\\O .\\O", "opinion_tags": "Interesting\\O selection\\O ,\\O good\\B wines\\O ,\\O service\\O fine\\O ,\\O fun\\O decor\\O .\\O", "sentiment": "positive"}, {"uid": "929329:3-1", "target_tags": "Interesting\\O selection\\O ,\\O good\\O wines\\O ,\\O service\\B fine\\O ,\\O fun\\O decor\\O .\\O", "opinion_tags": "Interesting\\O selection\\O ,\\O good\\O wines\\O ,\\O service\\O fine\\B ,\\O fun\\O decor\\O .\\O", "sentiment": "positive"}, {"uid": "929329:3-2", "target_tags": "Interesting\\O selection\\O ,\\O good\\O wines\\O ,\\O service\\O fine\\O ,\\O fun\\O decor\\B .\\O", "opinion_tags": "Interesting\\O selection\\O ,\\O good\\O wines\\O ,\\O service\\O fine\\O ,\\O fun\\B decor\\O .\\O", "sentiment": "positive"}, {"uid": "929329:3-3", "target_tags": "Interesting\\O selection\\B ,\\O good\\O wines\\O ,\\O service\\O fine\\O ,\\O fun\\O decor\\O .\\O", "opinion_tags": "Interesting\\B selection\\O ,\\O good\\O wines\\O ,\\O service\\O fine\\O ,\\O fun\\O decor\\O .\\O", "sentiment": "positive"}]}, {"id": "1496707:1", "sentence": "I had the best ravioli ever .", "postag": ["PRP", "VBD", "DT", "JJS", "NN", "RB", "."], "head": [2, 0, 5, 5, 2, 2, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "advmod", "punct"], "triples": [{"uid": "1496707:1-0", "target_tags": "I\\O had\\O the\\O best\\O ravioli\\B ever\\O .\\O", "opinion_tags": "I\\O had\\O the\\O best\\B ravioli\\O ever\\O .\\O", "sentiment": "positive"}]}, {"id": "1074868:4", "sentence": "The food was well prepared and the service impecable .", "postag": ["DT", "NN", "VBD", "RB", "VBN", "CC", "DT", "NN", "JJ", "."], "head": [2, 5, 5, 5, 0, 9, 8, 9, 5, 5], "deprel": ["det", "nsubj:pass", "aux:pass", "advmod", "root", "cc", "det", "nsubj", "conj", "punct"], "triples": [{"uid": "1074868:4-0", "target_tags": "The\\O food\\B was\\O well\\O prepared\\O and\\O the\\O service\\O impecable\\O .\\O", "opinion_tags": "The\\O food\\O was\\O well\\B prepared\\I and\\O the\\O service\\O impecable\\O .\\O", "sentiment": "positive"}, {"uid": "1074868:4-1", "target_tags": "The\\O food\\O was\\O well\\O prepared\\O and\\O the\\O service\\B impecable\\O .\\O", "opinion_tags": "The\\O food\\O was\\O well\\O prepared\\O and\\O the\\O service\\O impecable\\B .\\O", "sentiment": "positive"}]}, {"id": "1561259:1", "sentence": "But the staff was so horrible to us .", "postag": ["CC", "DT", "NN", "VBD", "RB", "JJ", "IN", "PRP", "."], "head": [6, 3, 6, 6, 6, 0, 8, 6, 6], "deprel": ["cc", "det", "nsubj", "cop", "advmod", "root", "case", "obl", "punct"], "triples": [{"uid": "1561259:1-0", "target_tags": "But\\O the\\O staff\\B was\\O so\\O horrible\\O to\\O us\\O .\\O", "opinion_tags": "But\\O the\\O staff\\O was\\O so\\O horrible\\B to\\O us\\O .\\O", "sentiment": "negative"}]}, {"id": "1496707:2", "sentence": "The wine the service was very good too .", "postag": ["DT", "NN", "DT", "NN", "VBD", "RB", "JJ", "RB", "."], "head": [2, 7, 4, 7, 7, 7, 0, 7, 7], "deprel": ["det", "nsubj", "det", "nsubj", "cop", "advmod", "root", "advmod", "punct"], "triples": [{"uid": "1496707:2-0", "target_tags": "The\\O wine\\B the\\O service\\O was\\O very\\O good\\O too\\O .\\O", "opinion_tags": "The\\O wine\\O the\\O service\\O was\\O very\\O good\\B too\\O .\\O", "sentiment": "positive"}, {"uid": "1496707:2-1", "target_tags": "The\\O wine\\O the\\O service\\B was\\O very\\O good\\O too\\O .\\O", "opinion_tags": "The\\O wine\\O the\\O service\\O was\\O very\\O good\\B too\\O .\\O", "sentiment": "positive"}]}, {"id": "1138996:6", "sentence": "Make sure you try this place as often as you can .", "postag": ["VB", "JJ", "PRP", "VB", "DT", "NN", "RB", "RB", "IN", "PRP", "MD", "."], "head": [0, 1, 4, 2, 6, 4, 8, 4, 11, 11, 8, 1], "deprel": ["root", "xcomp", "nsubj", "ccomp", "det", "obj", "advmod", "advmod", "mark", "nsubj", "advcl", "punct"], "triples": [{"uid": "1138996:6-0", "target_tags": "Make\\O sure\\O you\\O try\\O this\\O place\\B as\\O often\\O as\\O you\\O can\\O .\\O", "opinion_tags": "Make\\O sure\\O you\\O try\\B this\\O place\\O as\\O often\\O as\\O you\\O can\\O .\\O", "sentiment": "positive"}]}, {"id": "1086478:0", "sentence": "The food here is rather good , but only if you like to wait for it .", "postag": ["DT", "NN", "RB", "VBZ", "RB", "JJ", ",", "CC", "RB", "IN", "PRP", "VBP", "TO", "VB", "IN", "PRP", "."], "head": [2, 6, 2, 6, 6, 0, 12, 12, 12, 12, 12, 6, 14, 12, 16, 14, 6], "deprel": ["det", "nsubj", "advmod", "cop", "advmod", "root", "punct", "cc", "advmod", "mark", "nsubj", "conj", "mark", "xcomp", "case", "obl", "punct"], "triples": [{"uid": "1086478:0-0", "target_tags": "The\\O food\\B here\\O is\\O rather\\O good\\O ,\\O but\\O only\\O if\\O you\\O like\\O to\\O wait\\O for\\O it\\O .\\O", "opinion_tags": "The\\O food\\O here\\O is\\O rather\\O good\\B ,\\O but\\O only\\O if\\O you\\O like\\O to\\O wait\\O for\\O it\\O .\\O", "sentiment": "positive"}]}, {"id": "1413697:3", "sentence": "I highly recommend to anyone to give this place a try .", "postag": ["PRP", "RB", "VBP", "IN", "NN", "TO", "VB", "DT", "NN", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 3, 9, 7, 11, 7, 3], "deprel": ["nsubj", "advmod", "root", "case", "obl", "mark", "xcomp", "det", "i<PERSON><PERSON>", "det", "obj", "punct"], "triples": [{"uid": "1413697:3-0", "target_tags": "I\\O highly\\O recommend\\O to\\O anyone\\O to\\O give\\O this\\O place\\B a\\O try\\O .\\O", "opinion_tags": "I\\O highly\\O recommend\\B to\\O anyone\\O to\\O give\\O this\\O place\\O a\\O try\\O .\\O", "sentiment": "positive"}]}, {"id": "1573534:5", "sentence": "and yes <PERSON> is so dam good and so are all the kababs .", "postag": ["CC", "UH", "NNP", "NNP", "VBZ", "RB", "NN", "JJ", "CC", "RB", "VBP", "PDT", "DT", "NNS", "."], "head": [8, 8, 4, 8, 8, 8, 8, 0, 14, 14, 14, 14, 14, 8, 8], "deprel": ["cc", "discourse", "compound", "nsubj", "cop", "advmod", "obl:npmod", "root", "cc", "advmod", "cop", "det:predet", "det", "conj", "punct"], "triples": [{"uid": "1573534:5-0", "target_tags": "and\\O yes\\O Dal\\O Bukhara\\O is\\O so\\O dam\\O good\\O and\\O so\\O are\\O all\\O the\\O kababs\\B .\\O", "opinion_tags": "and\\O yes\\O Dal\\O Bukhara\\O is\\O so\\O dam\\O good\\B and\\O so\\O are\\O all\\O the\\O kababs\\O .\\O", "sentiment": "positive"}, {"uid": "1573534:5-1", "target_tags": "and\\O yes\\O Dal\\B Bukhara\\I is\\O so\\O dam\\O good\\O and\\O so\\O are\\O all\\O the\\O kababs\\O .\\O", "opinion_tags": "and\\O yes\\O Dal\\O Bukhara\\O is\\O so\\O dam\\O good\\B and\\O so\\O are\\O all\\O the\\O kababs\\O .\\O", "sentiment": "positive"}]}, {"id": "717570:1", "sentence": "The decor is very simple but comfortable .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"], "triples": [{"uid": "717570:1-0", "target_tags": "The\\O decor\\B is\\O very\\O simple\\O but\\O comfortable\\O .\\O", "opinion_tags": "The\\O decor\\O is\\O very\\O simple\\B but\\O comfortable\\B .\\O", "sentiment": "positive"}]}, {"id": "461928:0", "sentence": "This tiny Williamsburg spot is always pleasantly surprising .", "postag": ["DT", "JJ", "NNP", "NN", "VBZ", "RB", "RB", "JJ", "."], "head": [4, 4, 4, 8, 8, 8, 8, 0, 8], "deprel": ["det", "amod", "compound", "nsubj", "cop", "advmod", "advmod", "root", "punct"], "triples": [{"uid": "461928:0-0", "target_tags": "This\\O tiny\\O Williamsburg\\B spot\\I is\\O always\\O pleasantly\\O surprising\\O .\\O", "opinion_tags": "This\\O tiny\\O Williamsburg\\O spot\\O is\\O always\\O pleasantly\\O surprising\\B .\\O", "sentiment": "positive"}]}, {"id": "1126886:4", "sentence": "I 'm partial to the Gnocchi .", "postag": ["PRP", "VBP", "JJ", "IN", "DT", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 3], "deprel": ["nsubj", "cop", "root", "case", "det", "obl", "punct"], "triples": [{"uid": "1126886:4-0", "target_tags": "I\\O 'm\\O partial\\O to\\O the\\O <PERSON>cchi\\B .\\O", "opinion_tags": "I\\O 'm\\O partial\\B to\\O the\\O <PERSON>cchi\\O .\\O", "sentiment": "positive"}]}, {"id": "1032695:1", "sentence": "Everything is always cooked to perfection , the service is excellent , the decor cool and understated .", "postag": ["NN", "VBZ", "RB", "VBN", "IN", "NN", ",", "DT", "NN", "VBZ", "JJ", ",", "DT", "NN", "JJ", "CC", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 11, 9, 11, 11, 4, 15, 14, 15, 11, 17, 15, 4], "deprel": ["nsubj:pass", "aux:pass", "advmod", "root", "case", "obl", "punct", "det", "nsubj", "cop", "conj", "punct", "det", "nsubj", "conj", "cc", "conj", "punct"], "triples": [{"uid": "1032695:1-0", "target_tags": "Everything\\O is\\O always\\O cooked\\O to\\O perfection\\O ,\\O the\\O service\\B is\\O excellent\\O ,\\O the\\O decor\\O cool\\O and\\O understated\\O .\\O", "opinion_tags": "Everything\\O is\\O always\\O cooked\\O to\\O perfection\\O ,\\O the\\O service\\O is\\O excellent\\B ,\\O the\\O decor\\O cool\\O and\\O understated\\O .\\O", "sentiment": "positive"}, {"uid": "1032695:1-1", "target_tags": "Everything\\O is\\O always\\O cooked\\O to\\O perfection\\O ,\\O the\\O service\\O is\\O excellent\\O ,\\O the\\O decor\\B cool\\O and\\O understated\\O .\\O", "opinion_tags": "Everything\\O is\\O always\\O cooked\\O to\\O perfection\\O ,\\O the\\O service\\O is\\O excellent\\O ,\\O the\\O decor\\O cool\\B and\\O understated\\B .\\O", "sentiment": "positive"}]}, {"id": "1032695:2", "sentence": "I had the duck breast special on my last visit and it was incredible .", "postag": ["PRP", "VBD", "DT", "NN", "NN", "JJ", "IN", "PRP$", "JJ", "NN", "CC", "PRP", "VBD", "JJ", "."], "head": [2, 0, 5, 5, 6, 2, 10, 10, 10, 2, 14, 14, 14, 2, 2], "deprel": ["nsubj", "root", "det", "compound", "compound", "obj", "case", "nmod:poss", "amod", "obl", "cc", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "1032695:2-0", "target_tags": "I\\O had\\O the\\O duck\\B breast\\I special\\I on\\O my\\O last\\O visit\\O and\\O it\\O was\\O incredible\\O .\\O", "opinion_tags": "I\\O had\\O the\\O duck\\O breast\\O special\\O on\\O my\\O last\\O visit\\O and\\O it\\O was\\O incredible\\B .\\O", "sentiment": "positive"}]}, {"id": "1459569:1", "sentence": "Service was slow had to wait to order and get food although not crowded .", "postag": ["NN", "VBD", "JJ", "VBD", "TO", "VB", "TO", "VB", "CC", "VB", "NN", "IN", "RB", "JJ", "."], "head": [3, 3, 4, 0, 6, 4, 8, 6, 10, 8, 10, 14, 14, 10, 4], "deprel": ["nsubj", "cop", "advmod", "root", "mark", "xcomp", "mark", "advcl", "cc", "conj", "obj", "mark", "advmod", "advcl", "punct"], "triples": [{"uid": "1459569:1-0", "target_tags": "Service\\B was\\O slow\\O had\\O to\\O wait\\O to\\O order\\O and\\O get\\O food\\O although\\O not\\O crowded\\O .\\O", "opinion_tags": "Service\\O was\\O slow\\B had\\O to\\O wait\\O to\\O order\\O and\\O get\\O food\\O although\\O not\\O crowded\\O .\\O", "sentiment": "negative"}]}, {"id": "720620:8", "sentence": "Big Wong is a great place to eat and fill your stomach .", "postag": ["NNP", "NNP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "CC", "VB", "PRP$", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 8, 6, 10, 8, 12, 10, 6], "deprel": ["compound", "nsubj", "cop", "det", "amod", "root", "mark", "acl", "cc", "conj", "nmod:poss", "obj", "punct"], "triples": [{"uid": "720620:8-0", "target_tags": "Big\\B Wong\\I is\\O a\\O great\\O place\\O to\\O eat\\O and\\O fill\\O your\\O stomach\\O .\\O", "opinion_tags": "Big\\O Wong\\O is\\O a\\O great\\B place\\O to\\O eat\\O and\\O fill\\O your\\O stomach\\O .\\O", "sentiment": "positive"}]}, {"id": "483111:1", "sentence": "The food is reliable and the price is moderate .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "483111:1-0", "target_tags": "The\\O food\\B is\\O reliable\\O and\\O the\\O price\\O is\\O moderate\\O .\\O", "opinion_tags": "The\\O food\\O is\\O reliable\\B and\\O the\\O price\\O is\\O moderate\\O .\\O", "sentiment": "positive"}]}, {"id": "1623276:1", "sentence": "The food is tasty and portion sizes are appropriate .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "NN", "NNS", "VBP", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "compound", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "1623276:1-0", "target_tags": "The\\O food\\B is\\O tasty\\O and\\O portion\\O sizes\\O are\\O appropriate\\O .\\O", "opinion_tags": "The\\O food\\O is\\O tasty\\B and\\O portion\\O sizes\\O are\\O appropriate\\O .\\O", "sentiment": "positive"}, {"uid": "1623276:1-1", "target_tags": "The\\O food\\O is\\O tasty\\O and\\O portion\\B sizes\\I are\\O appropriate\\O .\\O", "opinion_tags": "The\\O food\\O is\\O tasty\\O and\\O portion\\O sizes\\O are\\O appropriate\\B .\\O", "sentiment": "positive"}]}, {"id": "1432551:0", "sentence": "Go to Volare for 1st class service and terrific food .", "postag": ["VB", "IN", "NNP", "IN", "JJ", "NN", "NN", "CC", "JJ", "NN", "."], "head": [0, 3, 1, 7, 6, 7, 1, 10, 10, 7, 1], "deprel": ["root", "case", "obl", "case", "amod", "compound", "obl", "cc", "amod", "conj", "punct"], "triples": [{"uid": "1432551:0-0", "target_tags": "Go\\O to\\O Volare\\O for\\O 1st\\O class\\O service\\B and\\O terrific\\O food\\O .\\O", "opinion_tags": "Go\\O to\\O Volare\\O for\\O 1st\\B class\\I service\\O and\\O terrific\\O food\\O .\\O", "sentiment": "positive"}, {"uid": "1432551:0-1", "target_tags": "Go\\O to\\O Volare\\O for\\O 1st\\O class\\O service\\O and\\O terrific\\O food\\B .\\O", "opinion_tags": "Go\\O to\\O Volare\\O for\\O 1st\\O class\\O service\\O and\\O terrific\\B food\\O .\\O", "sentiment": "positive"}]}, {"id": "1460715:0", "sentence": "Great pizza for lunch place .", "postag": ["JJ", "NN", "IN", "NN", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["amod", "root", "case", "compound", "nmod", "punct"], "triples": [{"uid": "1460715:0-0", "target_tags": "Great\\O pizza\\B for\\O lunch\\O place\\O .\\O", "opinion_tags": "Great\\B pizza\\O for\\O lunch\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "1572027:3", "sentence": "I ate here a week ago and found most dishes average at best and too expensive .", "postag": ["PRP", "VBD", "RB", "DT", "NN", "RB", "CC", "VBD", "JJS", "NNS", "JJ", "RB", "JJS", "CC", "RB", "JJ", "."], "head": [2, 0, 2, 5, 6, 2, 8, 2, 10, 8, 10, 13, 11, 16, 16, 11, 2], "deprel": ["nsubj", "root", "advmod", "det", "obl:npmod", "advmod", "cc", "conj", "amod", "obj", "amod", "case", "obl", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "1572027:3-0", "target_tags": "I\\O ate\\O here\\O a\\O week\\O ago\\O and\\O found\\O most\\O dishes\\B average\\O at\\O best\\O and\\O too\\O expensive\\O .\\O", "opinion_tags": "I\\O ate\\O here\\O a\\O week\\O ago\\O and\\O found\\O most\\O dishes\\O average\\B at\\O best\\O and\\O too\\B expensive\\I .\\O", "sentiment": "negative"}]}, {"id": "449802:0", "sentence": "Good spreads , great beverage selections and bagels really tasty .", "postag": ["JJ", "NNS", ",", "JJ", "NN", "NNS", "CC", "NNS", "RB", "JJ", "."], "head": [2, 0, 6, 6, 6, 2, 8, 2, 10, 2, 2], "deprel": ["amod", "root", "punct", "amod", "compound", "conj", "cc", "conj", "advmod", "conj", "punct"], "triples": [{"uid": "449802:0-0", "target_tags": "Good\\O spreads\\B ,\\O great\\O beverage\\O selections\\O and\\O bagels\\O really\\O tasty\\O .\\O", "opinion_tags": "Good\\B spreads\\O ,\\O great\\O beverage\\O selections\\O and\\O bagels\\O really\\O tasty\\O .\\O", "sentiment": "positive"}, {"uid": "449802:0-1", "target_tags": "Good\\O spreads\\O ,\\O great\\O beverage\\B selections\\I and\\O bagels\\O really\\O tasty\\O .\\O", "opinion_tags": "Good\\O spreads\\O ,\\O great\\B beverage\\O selections\\O and\\O bagels\\O really\\O tasty\\O .\\O", "sentiment": "positive"}, {"uid": "449802:0-2", "target_tags": "Good\\O spreads\\O ,\\O great\\O beverage\\O selections\\O and\\O bagels\\B really\\O tasty\\O .\\O", "opinion_tags": "Good\\O spreads\\O ,\\O great\\O beverage\\O selections\\O and\\O bagels\\O really\\O tasty\\B .\\O", "sentiment": "positive"}]}, {"id": "493013:2", "sentence": "I LOVE their Thai", "postag": ["PRP", "VBP", "PRP$", "NNP"], "head": [2, 0, 4, 2], "deprel": ["nsubj", "root", "nmod:poss", "obj"], "triples": [{"uid": "493013:2-0", "target_tags": "I\\O LOVE\\O their\\O Thai\\B", "opinion_tags": "I\\O LOVE\\B their\\O Thai\\O", "sentiment": "positive"}]}, {"id": "955711:0", "sentence": "This place blew me away ... by far my new favorite restaurant on the uppereast side .", "postag": ["DT", "NN", "VBD", "PRP", "RB", ",", "IN", "RB", "PRP$", "JJ", "JJ", "NN", "IN", "DT", "JJ", "NN", "."], "head": [2, 3, 0, 3, 3, 3, 8, 12, 12, 12, 12, 3, 16, 16, 16, 12, 3], "deprel": ["det", "nsubj", "root", "obj", "advmod", "punct", "case", "obl", "nmod:poss", "amod", "amod", "parataxis", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "955711:0-0", "target_tags": "This\\O place\\B blew\\O me\\O away\\O ...\\O by\\O far\\O my\\O new\\O favorite\\O restaurant\\O on\\O the\\O uppereast\\O side\\O .\\O", "opinion_tags": "This\\O place\\O blew\\O me\\O away\\O ...\\O by\\O far\\O my\\O new\\O favorite\\B restaurant\\O on\\O the\\O uppereast\\O side\\O .\\O", "sentiment": "positive"}]}, {"id": "513521:0", "sentence": "Great bagels made the old-fashioned way .", "postag": ["JJ", "NNS", "VBD", "DT", "JJ", "NN", "."], "head": [2, 3, 0, 6, 6, 3, 3], "deprel": ["amod", "nsubj", "root", "det", "amod", "obj", "punct"], "triples": [{"uid": "513521:0-0", "target_tags": "Great\\O bagels\\B made\\O the\\O old-fashioned\\O way\\O .\\O", "opinion_tags": "Great\\B bagels\\O made\\O the\\O old-fashioned\\O way\\O .\\O", "sentiment": "positive"}]}, {"id": "1090587:3", "sentence": "The lava cake dessert was incredible and I recommend it .", "postag": ["DT", "NN", "NN", "NN", "VBD", "JJ", "CC", "PRP", "VBP", "PRP", "."], "head": [4, 3, 4, 6, 6, 0, 9, 9, 6, 9, 6], "deprel": ["det", "compound", "compound", "nsubj", "cop", "root", "cc", "nsubj", "conj", "obj", "punct"], "triples": [{"uid": "1090587:3-0", "target_tags": "The\\O lava\\B cake\\I dessert\\I was\\O incredible\\O and\\O I\\O recommend\\O it\\O .\\O", "opinion_tags": "The\\O lava\\O cake\\O dessert\\O was\\O incredible\\B and\\O I\\O recommend\\B it\\O .\\O", "sentiment": "positive"}]}, {"id": "1486041:2", "sentence": "Despite a slightly limited menu , everything prepared is done to perfection , ultra fresh and a work of food art .", "postag": ["IN", "DT", "RB", "JJ", "NN", ",", "NN", "VBN", "VBZ", "VBN", "IN", "NN", ",", "NN", "JJ", "CC", "DT", "NN", "IN", "NN", "NN", "."], "head": [5, 5, 4, 5, 10, 5, 10, 7, 10, 0, 12, 10, 15, 15, 12, 18, 18, 12, 21, 21, 18, 10], "deprel": ["case", "det", "advmod", "amod", "obl", "punct", "nsubj:pass", "acl", "aux:pass", "root", "case", "obl", "punct", "compound", "amod", "cc", "det", "conj", "case", "compound", "nmod", "punct"], "triples": [{"uid": "1486041:2-0", "target_tags": "Despite\\O a\\O slightly\\O limited\\O menu\\B ,\\O everything\\O prepared\\O is\\O done\\O to\\O perfection\\O ,\\O ultra\\O fresh\\O and\\O a\\O work\\O of\\O food\\O art\\O .\\O", "opinion_tags": "Despite\\O a\\O slightly\\O limited\\B menu\\O ,\\O everything\\O prepared\\O is\\O done\\O to\\O perfection\\O ,\\O ultra\\O fresh\\O and\\O a\\O work\\O of\\O food\\O art\\O .\\O", "sentiment": "negative"}]}, {"id": "1014458:3", "sentence": "The wine list is interesting and has many good values .", "postag": ["DT", "NN", "NN", "VBZ", "JJ", "CC", "VBZ", "JJ", "JJ", "NNS", "."], "head": [3, 3, 5, 5, 0, 7, 5, 10, 10, 7, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "cc", "conj", "amod", "amod", "obj", "punct"], "triples": [{"uid": "1014458:3-0", "target_tags": "The\\O wine\\B list\\I is\\O interesting\\O and\\O has\\O many\\O good\\O values\\O .\\O", "opinion_tags": "The\\O wine\\O list\\O is\\O interesting\\B and\\O has\\O many\\O good\\B values\\I .\\O", "sentiment": "positive"}]}, {"id": "1138996:3", "sentence": "As always we had a great glass of wine while we waited .", "postag": ["IN", "RB", "PRP", "VBD", "DT", "JJ", "NN", "IN", "NN", "IN", "PRP", "VBD", "."], "head": [2, 4, 4, 0, 7, 7, 4, 9, 7, 12, 12, 4, 4], "deprel": ["mark", "advcl", "nsubj", "root", "det", "amod", "obj", "case", "nmod", "mark", "nsubj", "advcl", "punct"], "triples": [{"uid": "1138996:3-0", "target_tags": "As\\O always\\O we\\O had\\O a\\O great\\O glass\\B of\\I wine\\I while\\O we\\O waited\\O .\\O", "opinion_tags": "As\\O always\\O we\\O had\\O a\\O great\\B glass\\O of\\O wine\\O while\\O we\\O waited\\O .\\O", "sentiment": "positive"}]}, {"id": "1661043:1", "sentence": "Salads are a delicious way to begin the meal .", "postag": ["NNS", "VBP", "DT", "JJ", "NN", "TO", "VB", "DT", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 9, 7, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "det", "obj", "punct"], "triples": [{"uid": "1661043:1-0", "target_tags": "Salads\\B are\\O a\\O delicious\\O way\\O to\\O begin\\O the\\O meal\\O .\\O", "opinion_tags": "Salads\\O are\\O a\\O delicious\\B way\\O to\\O begin\\O the\\O meal\\O .\\O", "sentiment": "positive"}]}, {"id": "1719567:1", "sentence": "My husband said he could 've eaten several more , the portion was fine for me he even exclaimed that the french fries were the best he has had .", "postag": ["PRP$", "NN", "VBD", "PRP", "MD", "VB", "VBN", "JJ", "JJR", ",", "DT", "NN", "VBD", "JJ", "IN", "PRP", "PRP", "RB", "VBD", "IN", "DT", "JJ", "NNS", "VBD", "DT", "JJS", "PRP", "VBZ", "VBN", "."], "head": [2, 3, 0, 7, 7, 7, 3, 9, 7, 14, 12, 14, 14, 3, 16, 14, 19, 19, 3, 26, 23, 23, 26, 26, 26, 19, 29, 29, 26, 3], "deprel": ["nmod:poss", "nsubj", "root", "nsubj", "aux", "aux", "ccomp", "amod", "obj", "punct", "det", "nsubj", "cop", "parataxis", "case", "obl", "nsubj", "advmod", "parataxis", "mark", "det", "amod", "nsubj", "cop", "det", "ccomp", "nsubj", "aux", "acl:relcl", "punct"], "triples": [{"uid": "1719567:1-0", "target_tags": "My\\O husband\\O said\\O he\\O could\\O 've\\O eaten\\O several\\O more\\O ,\\O the\\O portion\\B was\\O fine\\O for\\O me\\O he\\O even\\O exclaimed\\O that\\O the\\O french\\O fries\\O were\\O the\\O best\\O he\\O has\\O had\\O .\\O", "opinion_tags": "My\\O husband\\O said\\O he\\O could\\O 've\\O eaten\\O several\\O more\\O ,\\O the\\O portion\\O was\\O fine\\B for\\O me\\O he\\O even\\O exclaimed\\O that\\O the\\O french\\O fries\\O were\\O the\\O best\\O he\\O has\\O had\\O .\\O", "sentiment": "positive"}, {"uid": "1719567:1-1", "target_tags": "My\\O husband\\O said\\O he\\O could\\O 've\\O eaten\\O several\\O more\\O ,\\O the\\O portion\\O was\\O fine\\O for\\O me\\O he\\O even\\O exclaimed\\O that\\O the\\O french\\B fries\\I were\\O the\\O best\\O he\\O has\\O had\\O .\\O", "opinion_tags": "My\\O husband\\O said\\O he\\O could\\O 've\\O eaten\\O several\\O more\\O ,\\O the\\O portion\\O was\\O fine\\O for\\O me\\O he\\O even\\O exclaimed\\O that\\O the\\O french\\O fries\\O were\\O the\\O best\\B he\\O has\\O had\\O .\\O", "sentiment": "positive"}]}, {"id": "1603698:6", "sentence": "I had their eggs benedict for brunch , which were the worst in my entire life , I tried removing the hollondaise sauce completely that was how failed it was .", "postag": ["PRP", "VBD", "PRP$", "NNS", "NN", "IN", "NN", ",", "WDT", "VBD", "DT", "JJS", "IN", "PRP$", "JJ", "NN", ",", "PRP", "VBD", "VBG", "DT", "NN", "NN", "RB", "DT", "VBD", "WRB", "VBD", "PRP", "VBD", "."], "head": [2, 0, 5, 5, 2, 7, 2, 7, 12, 12, 12, 7, 16, 16, 16, 12, 2, 19, 2, 19, 23, 23, 20, 26, 26, 2, 28, 26, 30, 28, 2], "deprel": ["nsubj", "root", "nmod:poss", "compound", "obj", "case", "obl", "punct", "nsubj", "cop", "det", "acl:relcl", "case", "nmod:poss", "amod", "obl", "punct", "nsubj", "parataxis", "xcomp", "det", "compound", "obj", "advmod", "nsubj", "parataxis", "mark", "ccomp", "nsubj", "ccomp", "punct"], "triples": [{"uid": "1603698:6-0", "target_tags": "I\\O had\\O their\\O eggs\\B benedict\\I for\\O brunch\\O ,\\O which\\O were\\O the\\O worst\\O in\\O my\\O entire\\O life\\O ,\\O I\\O tried\\O removing\\O the\\O hollondaise\\O sauce\\O completely\\O that\\O was\\O how\\O failed\\O it\\O was\\O .\\O", "opinion_tags": "I\\O had\\O their\\O eggs\\O benedict\\O for\\O brunch\\O ,\\O which\\O were\\O the\\O worst\\B in\\O my\\O entire\\O life\\O ,\\O I\\O tried\\O removing\\O the\\O hollondaise\\O sauce\\O completely\\O that\\O was\\O how\\O failed\\O it\\O was\\O .\\O", "sentiment": "negative"}]}, {"id": "1280179:1", "sentence": "To sum it up : Service varies from good to mediorce , depending on which waiter you get ; generally it is just average Ok .", "postag": ["TO", "VB", "PRP", "RP", ":", "NN", "VBZ", "IN", "JJ", "IN", "NN", ",", "VBG", "IN", "WDT", "NN", "PRP", "VBP", ",", "RB", "PRP", "VBZ", "RB", "JJ", "JJ", "."], "head": [2, 7, 2, 2, 7, 7, 0, 9, 7, 11, 7, 7, 16, 16, 16, 18, 18, 7, 7, 25, 25, 25, 25, 25, 7, 7], "deprel": ["mark", "advcl", "obj", "compound:prt", "punct", "nsubj", "root", "case", "obl", "case", "obl", "punct", "case", "case", "det", "obl", "nsubj", "advcl", "punct", "advmod", "nsubj", "cop", "advmod", "amod", "parataxis", "punct"], "triples": [{"uid": "1280179:1-0", "target_tags": "To\\O sum\\O it\\O up\\O :\\O Service\\B varies\\O from\\O good\\O to\\O mediorce\\O ,\\O depending\\O on\\O which\\O waiter\\O you\\O get\\O ;\\O generally\\O it\\O is\\O just\\O average\\O Ok\\O .\\O", "opinion_tags": "To\\O sum\\O it\\O up\\O :\\O Service\\O varies\\B from\\O good\\O to\\O mediorce\\O ,\\O depending\\O on\\O which\\O waiter\\O you\\O get\\O ;\\O generally\\O it\\O is\\O just\\O average\\O Ok\\O .\\O", "sentiment": "neutral"}]}, {"id": "1430994:7", "sentence": "We did n't want a bottle of bubbly on a weekday so we each got little bottles of Korbett it was just enough .", "postag": ["PRP", "VBD", "RB", "VB", "DT", "NN", "IN", "JJ", "IN", "DT", "NN", "RB", "PRP", "DT", "VBD", "JJ", "NNS", "IN", "NNP", "PRP", "VBD", "RB", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 11, 11, 4, 15, 15, 13, 4, 17, 15, 19, 17, 23, 23, 23, 4, 4], "deprel": ["nsubj", "aux", "advmod", "root", "det", "obj", "case", "nmod", "case", "det", "obl", "advmod", "nsubj", "det", "conj", "amod", "obj", "case", "nmod", "nsubj", "cop", "advmod", "parataxis", "punct"], "triples": [{"uid": "1430994:7-0", "target_tags": "We\\O did\\O n't\\O want\\O a\\O bottle\\O of\\O bubbly\\O on\\O a\\O weekday\\O so\\O we\\O each\\O got\\O little\\O bottles\\B of\\I Korbett\\I it\\O was\\O just\\O enough\\O .\\O", "opinion_tags": "We\\O did\\O n't\\O want\\O a\\O bottle\\O of\\O bubbly\\O on\\O a\\O weekday\\O so\\O we\\O each\\O got\\O little\\O bottles\\O of\\O Korbett\\O it\\O was\\O just\\O enough\\B .\\O", "sentiment": "positive"}]}, {"id": "553097:1", "sentence": "Too bad the food was n't of the same heritage .", "postag": ["RB", "JJ", "DT", "NN", "VBD", "RB", "IN", "DT", "JJ", "NN", "."], "head": [2, 0, 4, 10, 10, 10, 10, 10, 10, 2, 2], "deprel": ["advmod", "root", "det", "nsubj", "cop", "advmod", "case", "det", "amod", "ccomp", "punct"], "triples": [{"uid": "553097:1-0", "target_tags": "Too\\O bad\\O the\\O food\\B was\\O n't\\O of\\O the\\O same\\O heritage\\O .\\O", "opinion_tags": "Too\\O bad\\B the\\O food\\O was\\O n't\\O of\\O the\\O same\\O heritage\\O .\\O", "sentiment": "negative"}]}, {"id": "1205520:0", "sentence": "I 'm not sure where the other reviewers ate but it seems as if we visited two different restaurants because my friends and I all enjoy <PERSON><PERSON> very much ... and we 're repeat customers .", "postag": ["PRP", "VBP", "RB", "JJ", "WRB", "DT", "JJ", "NNS", "VBD", "CC", "PRP", "VBZ", "IN", "IN", "PRP", "VBD", "CD", "JJ", "NNS", "IN", "PRP$", "NNS", "CC", "PRP", "DT", "VBP", "NNP", "RB", "JJ", ",", "CC", "PRP", "VBP", "JJ", "NNS", "."], "head": [4, 4, 4, 0, 9, 8, 8, 9, 4, 12, 12, 4, 16, 16, 16, 12, 19, 19, 16, 22, 22, 16, 24, 22, 24, 4, 26, 29, 26, 4, 35, 35, 35, 35, 26, 4], "deprel": ["nsubj", "cop", "advmod", "root", "mark", "det", "amod", "nsubj", "ccomp", "cc", "expl", "conj", "mark", "mark", "nsubj", "advcl", "nummod", "amod", "obj", "case", "nmod:poss", "obl", "cc", "conj", "det", "conj", "obj", "advmod", "advmod", "punct", "cc", "nsubj", "cop", "amod", "conj", "punct"], "triples": [{"uid": "1205520:0-0", "target_tags": "I\\O 'm\\O not\\O sure\\O where\\O the\\O other\\O reviewers\\O ate\\O but\\O it\\O seems\\O as\\O if\\O we\\O visited\\O two\\O different\\O restaurants\\O because\\O my\\O friends\\O and\\O I\\O all\\O enjoy\\O Mizu\\B very\\O much\\O ...\\O and\\O we\\O 're\\O repeat\\O customers\\O .\\O", "opinion_tags": "I\\O 'm\\O not\\O sure\\O where\\O the\\O other\\O reviewers\\O ate\\O but\\O it\\O seems\\O as\\O if\\O we\\O visited\\O two\\O different\\O restaurants\\O because\\O my\\O friends\\O and\\O I\\O all\\O enjoy\\B Mizu\\O very\\O much\\O ...\\O and\\O we\\O 're\\O repeat\\O customers\\O .\\O", "sentiment": "positive"}]}, {"id": "1538149:0", "sentence": "I found the food , service and value exceptional everytime I have been there .", "postag": ["PRP", "VBD", "DT", "NN", ",", "NN", "CC", "VBP", "JJ", "RB", "PRP", "VBP", "VBN", "RB", "."], "head": [2, 0, 4, 2, 6, 2, 8, 2, 8, 8, 14, 14, 14, 8, 2], "deprel": ["nsubj", "root", "det", "obj", "punct", "obj", "cc", "conj", "obj", "advmod", "nsubj", "aux", "cop", "ccomp", "punct"], "triples": [{"uid": "1538149:0-0", "target_tags": "I\\O found\\O the\\O food\\B ,\\O service\\O and\\O value\\O exceptional\\O everytime\\O I\\O have\\O been\\O there\\O .\\O", "opinion_tags": "I\\O found\\O the\\O food\\O ,\\O service\\O and\\O value\\O exceptional\\B everytime\\O I\\O have\\O been\\O there\\O .\\O", "sentiment": "positive"}, {"uid": "1538149:0-1", "target_tags": "I\\O found\\O the\\O food\\O ,\\O service\\B and\\O value\\O exceptional\\O everytime\\O I\\O have\\O been\\O there\\O .\\O", "opinion_tags": "I\\O found\\O the\\O food\\O ,\\O service\\O and\\O value\\O exceptional\\B everytime\\O I\\O have\\O been\\O there\\O .\\O", "sentiment": "positive"}]}, {"id": "496132:3", "sentence": "The food is excellent !", "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "496132:3-0", "target_tags": "The\\O food\\B is\\O excellent\\O !\\O", "opinion_tags": "The\\O food\\O is\\O excellent\\B !\\O", "sentiment": "positive"}]}, {"id": "512925:0", "sentence": "it 's a perfect place to have a amazing indian food .", "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "DT", "JJ", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 11, 11, 11, 7, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "csubj", "det", "amod", "amod", "obj", "punct"], "triples": [{"uid": "512925:0-0", "target_tags": "it\\O 's\\O a\\O perfect\\O place\\O to\\O have\\O a\\O amazing\\O indian\\B food\\I .\\O", "opinion_tags": "it\\O 's\\O a\\O perfect\\O place\\O to\\O have\\O a\\O amazing\\B indian\\O food\\O .\\O", "sentiment": "positive"}]}]