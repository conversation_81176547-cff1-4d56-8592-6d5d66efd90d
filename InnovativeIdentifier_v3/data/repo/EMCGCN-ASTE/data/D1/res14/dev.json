[{"id": "1196", "sentence": "The service is awful -- the last time I was there ( and I do mean the last time ) we were told that they needed our table so we would have to leave .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "DT", "JJ", "NN", "PRP", "VBD", "RB", "-LRB-", "CC", "PRP", "VBP", "VB", "DT", "JJ", "NN", "-RRB-", "PRP", "VBD", "VBN", "IN", "PRP", "VBD", "PRP$", "NN", "RB", "PRP", "MD", "VB", "TO", "VB", "."], "head": [2, 4, 4, 0, 4, 8, 8, 23, 11, 11, 8, 16, 16, 16, 16, 4, 19, 19, 16, 16, 23, 23, 4, 26, 26, 23, 28, 26, 32, 32, 32, 23, 34, 32, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "amod", "obl:tmod", "nsubj", "cop", "acl:relcl", "punct", "cc", "nsubj", "aux", "conj", "det", "amod", "obl:tmod", "punct", "nsubj:pass", "aux:pass", "parataxis", "mark", "nsubj", "ccomp", "nmod:poss", "obj", "mark", "nsubj", "aux", "conj", "mark", "xcomp", "punct"], "triples": [{"uid": "1196-0", "target_tags": "The\\O service\\B is\\O awful\\O --\\O the\\O last\\O time\\O I\\O was\\O there\\O (\\O and\\O I\\O do\\O mean\\O the\\O last\\O time\\O )\\O we\\O were\\O told\\O that\\O they\\O needed\\O our\\O table\\O so\\O we\\O would\\O have\\O to\\O leave\\O .\\O", "opinion_tags": "The\\O service\\O is\\O awful\\B --\\O the\\O last\\O time\\O I\\O was\\O there\\O (\\O and\\O I\\O do\\O mean\\O the\\O last\\O time\\O )\\O we\\O were\\O told\\O that\\O they\\O needed\\O our\\O table\\O so\\O we\\O would\\O have\\O to\\O leave\\O .\\O", "sentiment": "negative"}]}, {"id": "3071", "sentence": "Cozy romantic atomosphere with only around 15 tables at most .", "postag": ["JJ", "JJ", "NN", "IN", "RB", "RB", "CD", "NNS", "RB", "JJS", "."], "head": [3, 3, 0, 8, 8, 7, 8, 3, 10, 8, 3], "deprel": ["amod", "amod", "root", "case", "advmod", "advmod", "nummod", "nmod", "case", "nmod", "punct"], "triples": [{"uid": "3071-0", "target_tags": "Cozy\\O romantic\\O atomosphere\\B with\\O only\\O around\\O 15\\O tables\\O at\\O most\\O .\\O", "opinion_tags": "Cozy\\B romantic\\B atomosphere\\O with\\O only\\O around\\O 15\\O tables\\O at\\O most\\O .\\O", "sentiment": "positive"}]}, {"id": "584", "sentence": "Rao 's has the best service and atmosphere in NYC .", "postag": ["NNP", "POS", "VBZ", "DT", "JJS", "NN", "CC", "NN", "IN", "NNP", "."], "head": [3, 1, 0, 6, 6, 3, 8, 6, 10, 6, 3], "deprel": ["nsubj", "case", "root", "det", "amod", "obj", "cc", "conj", "case", "nmod", "punct"], "triples": [{"uid": "584-0", "target_tags": "Rao\\O 's\\O has\\O the\\O best\\O service\\B and\\O atmosphere\\O in\\O NYC\\O .\\O", "opinion_tags": "Rao\\O 's\\O has\\O the\\O best\\B service\\O and\\O atmosphere\\O in\\O NYC\\O .\\O", "sentiment": "positive"}, {"uid": "584-1", "target_tags": "Rao\\O 's\\O has\\O the\\O best\\O service\\O and\\O atmosphere\\B in\\O NYC\\O .\\O", "opinion_tags": "Rao\\O 's\\O has\\O the\\O best\\B service\\O and\\O atmosphere\\O in\\O NYC\\O .\\O", "sentiment": "positive"}]}, {"id": "3015", "sentence": "After we got our sashimi order , I could not believe how small the portions were !", "postag": ["IN", "PRP", "VBD", "PRP$", "NN", "NN", ",", "PRP", "MD", "RB", "VB", "WRB", "JJ", "DT", "NNS", "VBD", "."], "head": [3, 3, 11, 6, 6, 3, 11, 11, 11, 11, 0, 13, 11, 15, 13, 13, 11], "deprel": ["mark", "nsubj", "advcl", "nmod:poss", "compound", "obj", "punct", "nsubj", "aux", "advmod", "root", "mark", "ccomp", "det", "nsubj", "cop", "punct"], "triples": [{"uid": "3015-0", "target_tags": "After\\O we\\O got\\O our\\O sashimi\\B order\\O ,\\O I\\O could\\O not\\O believe\\O how\\O small\\O the\\O portions\\O were\\O !\\O", "opinion_tags": "After\\O we\\O got\\O our\\O sashimi\\O order\\O ,\\O I\\O could\\O not\\O believe\\O how\\O small\\B the\\O portions\\O were\\O !\\O", "sentiment": "neutral"}, {"uid": "3015-1", "target_tags": "After\\O we\\O got\\O our\\O sashimi\\O order\\O ,\\O I\\O could\\O not\\O believe\\O how\\O small\\O the\\O portions\\B were\\O !\\O", "opinion_tags": "After\\O we\\O got\\O our\\O sashimi\\O order\\O ,\\O I\\O could\\O not\\O believe\\O how\\O small\\B the\\O portions\\O were\\O !\\O", "sentiment": "negative"}]}, {"id": "758", "sentence": "You will pay a lot for the decore , but the food is no better or worse than a lot of other Chinese and Asian fusion places in NY .", "postag": ["PRP", "MD", "VB", "DT", "NN", "IN", "DT", "NN", ",", "CC", "DT", "NN", "VBZ", "RB", "JJR", "CC", "JJR", "IN", "DT", "NN", "IN", "JJ", "JJ", "CC", "JJ", "NN", "NNS", "IN", "NNP", "."], "head": [3, 3, 0, 5, 3, 8, 8, 3, 15, 15, 12, 15, 15, 15, 3, 17, 15, 20, 20, 15, 27, 27, 27, 27, 26, 27, 20, 29, 27, 3], "deprel": ["nsubj", "aux", "root", "det", "obj", "case", "det", "obl", "punct", "cc", "det", "nsubj", "cop", "advmod", "conj", "cc", "conj", "case", "det", "obl", "case", "amod", "amod", "cc", "amod", "compound", "nmod", "case", "nmod", "punct"], "triples": [{"uid": "758-0", "target_tags": "You\\O will\\O pay\\O a\\O lot\\O for\\O the\\O decore\\B ,\\O but\\O the\\O food\\O is\\O no\\O better\\O or\\O worse\\O than\\O a\\O lot\\O of\\O other\\O Chinese\\O and\\O Asian\\O fusion\\O places\\O in\\O NY\\O .\\O", "opinion_tags": "You\\O will\\O pay\\B a\\I lot\\I for\\O the\\O decore\\O ,\\O but\\O the\\O food\\O is\\O no\\O better\\O or\\O worse\\O than\\O a\\O lot\\O of\\O other\\O Chinese\\O and\\O Asian\\O fusion\\O places\\O in\\O NY\\O .\\O", "sentiment": "negative"}, {"uid": "758-1", "target_tags": "You\\O will\\O pay\\O a\\O lot\\O for\\O the\\O decore\\O ,\\O but\\O the\\O food\\B is\\O no\\O better\\O or\\O worse\\O than\\O a\\O lot\\O of\\O other\\O Chinese\\O and\\O Asian\\O fusion\\O places\\O in\\O NY\\O .\\O", "opinion_tags": "You\\O will\\O pay\\O a\\O lot\\O for\\O the\\O decore\\O ,\\O but\\O the\\O food\\O is\\O no\\B better\\I or\\I worse\\I than\\O a\\O lot\\O of\\O other\\O Chinese\\O and\\O Asian\\O fusion\\O places\\O in\\O NY\\O .\\O", "sentiment": "neutral"}]}, {"id": "3263", "sentence": "Each bite of food at Kai was indeed delicious , fresh , and elegant .", "postag": ["DT", "NN", "IN", "NN", "IN", "NNP", "VBD", "RB", "JJ", ",", "JJ", ",", "CC", "JJ", "."], "head": [2, 9, 4, 2, 6, 4, 9, 9, 0, 11, 9, 14, 14, 9, 9], "deprel": ["det", "nsubj", "case", "nmod", "case", "nmod", "cop", "advmod", "root", "punct", "conj", "punct", "cc", "conj", "punct"], "triples": [{"uid": "3263-0", "target_tags": "Each\\O bite\\O of\\O food\\B at\\O Kai\\O was\\O indeed\\O delicious\\O ,\\O fresh\\O ,\\O and\\O elegant\\O .\\O", "opinion_tags": "Each\\O bite\\O of\\O food\\O at\\O Kai\\O was\\O indeed\\O delicious\\B ,\\O fresh\\B ,\\O and\\O elegant\\B .\\O", "sentiment": "positive"}]}, {"id": "2336", "sentence": "Ambiance is barely romantic but management tries .", "postag": ["NN", "VBZ", "RB", "JJ", "CC", "NN", "VBZ", "."], "head": [4, 4, 4, 0, 7, 7, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "cc", "nsubj", "conj", "punct"], "triples": [{"uid": "2336-0", "target_tags": "Ambiance\\B is\\O barely\\O romantic\\O but\\O management\\O tries\\O .\\O", "opinion_tags": "Ambiance\\O is\\O barely\\B romantic\\I but\\O management\\O tries\\O .\\O", "sentiment": "negative"}, {"uid": "2336-1", "target_tags": "Ambiance\\O is\\O barely\\O romantic\\O but\\O management\\B tries\\O .\\O", "opinion_tags": "Ambiance\\O is\\O barely\\O romantic\\O but\\O management\\O tries\\B .\\O", "sentiment": "positive"}]}, {"id": "2262", "sentence": "The spicy tuna roll was unusually good and the rock shrimp tempura was awesome , great appetizer to share !", "postag": ["DT", "JJ", "NN", "NN", "VBD", "RB", "JJ", "CC", "DT", "NN", "NN", "NN", "VBD", "JJ", ",", "JJ", "NN", "TO", "VB", "."], "head": [4, 4, 4, 7, 7, 7, 0, 14, 12, 12, 12, 14, 14, 7, 17, 17, 14, 19, 17, 7], "deprel": ["det", "amod", "compound", "nsubj", "cop", "advmod", "root", "cc", "det", "compound", "compound", "nsubj", "cop", "conj", "punct", "amod", "conj", "mark", "acl", "punct"], "triples": [{"uid": "2262-0", "target_tags": "The\\O spicy\\B tuna\\I roll\\I was\\O unusually\\O good\\O and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\O ,\\O great\\O appetizer\\O to\\O share\\O !\\O", "opinion_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\B and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\O ,\\O great\\O appetizer\\O to\\O share\\O !\\O", "sentiment": "positive"}, {"uid": "2262-1", "target_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\O and\\O the\\O rock\\B shrimp\\I tempura\\I was\\O awesome\\O ,\\O great\\O appetizer\\O to\\O share\\O !\\O", "opinion_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\O and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\B ,\\O great\\O appetizer\\O to\\O share\\O !\\O", "sentiment": "positive"}, {"uid": "2262-2", "target_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\O and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\O ,\\O great\\O appetizer\\B to\\O share\\O !\\O", "opinion_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\O and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\O ,\\O great\\B appetizer\\O to\\O share\\O !\\O", "sentiment": "positive"}]}, {"id": "2604", "sentence": "Right off the L in Brooklyn this is a nice cozy place with good pizza .", "postag": ["RB", "IN", "DT", "NNP", "IN", "NNP", "DT", "VBZ", "DT", "JJ", "JJ", "NN", "IN", "JJ", "NN", "."], "head": [4, 4, 4, 12, 6, 4, 12, 12, 12, 12, 12, 0, 15, 15, 12, 12], "deprel": ["advmod", "case", "det", "obl", "case", "nmod", "nsubj", "cop", "det", "amod", "amod", "root", "case", "amod", "nmod", "punct"], "triples": [{"uid": "2604-0", "target_tags": "Right\\O off\\O the\\O L\\O in\\O Brooklyn\\O this\\O is\\O a\\O nice\\O cozy\\O place\\O with\\O good\\O pizza\\B .\\O", "opinion_tags": "Right\\O off\\O the\\O L\\O in\\O Brooklyn\\O this\\O is\\O a\\O nice\\O cozy\\O place\\O with\\O good\\B pizza\\O .\\O", "sentiment": "positive"}, {"uid": "2604-1", "target_tags": "Right\\O off\\O the\\O L\\O in\\O Brooklyn\\O this\\O is\\O a\\O nice\\O cozy\\O place\\B with\\O good\\O pizza\\O .\\O", "opinion_tags": "Right\\O off\\O the\\O L\\O in\\O Brooklyn\\O this\\O is\\O a\\O nice\\B cozy\\B place\\O with\\O good\\O pizza\\O .\\O", "sentiment": "positive"}]}, {"id": "1836", "sentence": "Other than the crappy service from two individuals , it 's great .", "postag": ["JJ", "IN", "DT", "JJ", "NN", "IN", "CD", "NNS", ",", "PRP", "VBZ", "JJ", "."], "head": [12, 5, 5, 5, 12, 8, 8, 5, 12, 12, 12, 0, 12], "deprel": ["advmod", "case", "det", "amod", "obl", "case", "nummod", "nmod", "punct", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "1836-0", "target_tags": "Other\\O than\\O the\\O crappy\\O service\\B from\\O two\\O individuals\\O ,\\O it\\O 's\\O great\\O .\\O", "opinion_tags": "Other\\O than\\O the\\O crappy\\B service\\O from\\O two\\O individuals\\O ,\\O it\\O 's\\O great\\O .\\O", "sentiment": "negative"}]}, {"id": "2451", "sentence": "The prices were fantastic .", "postag": ["DT", "NNS", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "2451-0", "target_tags": "The\\O prices\\B were\\O fantastic\\O .\\O", "opinion_tags": "The\\O prices\\O were\\O fantastic\\B .\\O", "sentiment": "positive"}]}, {"id": "2079", "sentence": "The staff was accomodating , the food was absolutely delicious and the place is lovely .", "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "RB", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 10, 7, 10, 10, 10, 4, 15, 13, 15, 15, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "advmod", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "2079-0", "target_tags": "The\\O staff\\B was\\O accomodating\\O ,\\O the\\O food\\O was\\O absolutely\\O delicious\\O and\\O the\\O place\\O is\\O lovely\\O .\\O", "opinion_tags": "The\\O staff\\O was\\O accomodating\\B ,\\O the\\O food\\O was\\O absolutely\\O delicious\\O and\\O the\\O place\\O is\\O lovely\\O .\\O", "sentiment": "positive"}, {"uid": "2079-1", "target_tags": "The\\O staff\\O was\\O accomodating\\O ,\\O the\\O food\\B was\\O absolutely\\O delicious\\O and\\O the\\O place\\O is\\O lovely\\O .\\O", "opinion_tags": "The\\O staff\\O was\\O accomodating\\O ,\\O the\\O food\\O was\\O absolutely\\O delicious\\B and\\O the\\O place\\O is\\O lovely\\O .\\O", "sentiment": "positive"}, {"uid": "2079-2", "target_tags": "The\\O staff\\O was\\O accomodating\\O ,\\O the\\O food\\O was\\O absolutely\\O delicious\\O and\\O the\\O place\\B is\\O lovely\\O .\\O", "opinion_tags": "The\\O staff\\O was\\O accomodating\\O ,\\O the\\O food\\O was\\O absolutely\\O delicious\\O and\\O the\\O place\\O is\\O lovely\\B .\\O", "sentiment": "positive"}]}, {"id": "648", "sentence": "I just wonder how you can have such a delicious meal for such little money .", "postag": ["PRP", "RB", "VBP", "WRB", "PRP", "MD", "VB", "PDT", "DT", "JJ", "NN", "IN", "JJ", "JJ", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 11, 11, 11, 7, 15, 15, 15, 7, 3], "deprel": ["nsubj", "advmod", "root", "mark", "nsubj", "aux", "ccomp", "det:predet", "det", "amod", "obj", "case", "amod", "amod", "obl", "punct"], "triples": [{"uid": "648-0", "target_tags": "I\\O just\\O wonder\\O how\\O you\\O can\\O have\\O such\\O a\\O delicious\\O meal\\B for\\O such\\O little\\O money\\O .\\O", "opinion_tags": "I\\O just\\O wonder\\O how\\O you\\O can\\O have\\O such\\O a\\O delicious\\B meal\\O for\\O such\\O little\\O money\\O .\\O", "sentiment": "positive"}, {"uid": "648-1", "target_tags": "I\\O just\\O wonder\\O how\\O you\\O can\\O have\\O such\\O a\\O delicious\\O meal\\O for\\O such\\O little\\O money\\B .\\O", "opinion_tags": "I\\O just\\O wonder\\O how\\O you\\O can\\O have\\O such\\O a\\O delicious\\O meal\\O for\\O such\\O little\\B money\\O .\\O", "sentiment": "positive"}]}, {"id": "58", "sentence": "A cool bar with great food , and tons of excellent beer .", "postag": ["DT", "JJ", "NN", "IN", "JJ", "NN", ",", "CC", "NNS", "IN", "JJ", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 3, 12, 12, 9, 3], "deprel": ["det", "amod", "root", "case", "amod", "nmod", "punct", "cc", "conj", "case", "amod", "nmod", "punct"], "triples": [{"uid": "58-0", "target_tags": "A\\O cool\\O bar\\B with\\O great\\O food\\O ,\\O and\\O tons\\O of\\O excellent\\O beer\\O .\\O", "opinion_tags": "A\\O cool\\B bar\\O with\\O great\\O food\\O ,\\O and\\O tons\\O of\\O excellent\\O beer\\O .\\O", "sentiment": "positive"}, {"uid": "58-1", "target_tags": "A\\O cool\\O bar\\O with\\O great\\O food\\B ,\\O and\\O tons\\O of\\O excellent\\O beer\\O .\\O", "opinion_tags": "A\\O cool\\O bar\\O with\\O great\\B food\\O ,\\O and\\O tons\\O of\\O excellent\\O beer\\O .\\O", "sentiment": "positive"}, {"uid": "58-2", "target_tags": "A\\O cool\\O bar\\O with\\O great\\O food\\O ,\\O and\\O tons\\O of\\O excellent\\O beer\\B .\\O", "opinion_tags": "A\\O cool\\O bar\\O with\\O great\\O food\\O ,\\O and\\O tons\\O of\\O excellent\\B beer\\O .\\O", "sentiment": "positive"}]}, {"id": "97", "sentence": "The wine list is also really nice .", "postag": ["DT", "NN", "NN", "VBZ", "RB", "RB", "JJ", "."], "head": [3, 3, 7, 7, 7, 7, 0, 7], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "advmod", "root", "punct"], "triples": [{"uid": "97-0", "target_tags": "The\\O wine\\B list\\I is\\O also\\O really\\O nice\\O .\\O", "opinion_tags": "The\\O wine\\O list\\O is\\O also\\O really\\O nice\\B .\\O", "sentiment": "positive"}]}, {"id": "1818", "sentence": "Service -- friendly and attentive .", "postag": ["NN", ",", "JJ", "CC", "JJ", "."], "head": [0, 3, 1, 5, 3, 1], "deprel": ["root", "punct", "appos", "cc", "conj", "punct"], "triples": [{"uid": "1818-0", "target_tags": "Service\\B --\\O friendly\\O and\\O attentive\\O .\\O", "opinion_tags": "Service\\O --\\O friendly\\B and\\O attentive\\B .\\O", "sentiment": "positive"}]}, {"id": "2822", "sentence": "Other guests enjoyed pizza , santa fe chopped salad and fish and chips .", "postag": ["JJ", "NNS", "VBD", "NN", ",", "NNP", "NNP", "VBD", "NN", "CC", "NNS", "CC", "NNS", "."], "head": [2, 3, 0, 3, 8, 7, 8, 3, 8, 11, 9, 13, 9, 3], "deprel": ["amod", "nsubj", "root", "obj", "punct", "compound", "nsubj", "conj", "obj", "cc", "conj", "cc", "conj", "punct"], "triples": [{"uid": "2822-0", "target_tags": "Other\\O guests\\O enjoyed\\O pizza\\B ,\\O santa\\O fe\\O chopped\\O salad\\O and\\O fish\\O and\\O chips\\O .\\O", "opinion_tags": "Other\\O guests\\O enjoyed\\B pizza\\O ,\\O santa\\O fe\\O chopped\\O salad\\O and\\O fish\\O and\\O chips\\O .\\O", "sentiment": "positive"}, {"uid": "2822-1", "target_tags": "Other\\O guests\\O enjoyed\\O pizza\\O ,\\O santa\\B fe\\I chopped\\I salad\\I and\\O fish\\O and\\O chips\\O .\\O", "opinion_tags": "Other\\O guests\\O enjoyed\\B pizza\\O ,\\O santa\\O fe\\O chopped\\O salad\\O and\\O fish\\O and\\O chips\\O .\\O", "sentiment": "positive"}, {"uid": "2822-2", "target_tags": "Other\\O guests\\O enjoyed\\O pizza\\O ,\\O santa\\O fe\\O chopped\\O salad\\O and\\O fish\\B and\\I chips\\I .\\O", "opinion_tags": "Other\\O guests\\O enjoyed\\B pizza\\O ,\\O santa\\O fe\\O chopped\\O salad\\O and\\O fish\\O and\\O chips\\O .\\O", "sentiment": "positive"}]}, {"id": "1381", "sentence": "Tried the pad see ew on the recommendation of the last reviewer since it 's one of my favorite dishes .", "postag": ["VBD", "DT", "NN", "VB", "NN", "IN", "DT", "NN", "IN", "DT", "JJ", "NN", "IN", "PRP", "VBZ", "CD", "IN", "PRP$", "JJ", "NNS", "."], "head": [0, 3, 4, 1, 4, 8, 8, 4, 12, 12, 12, 8, 16, 16, 16, 4, 20, 20, 20, 16, 1], "deprel": ["root", "det", "nsubj", "ccomp", "obj", "case", "det", "obl", "case", "det", "amod", "nmod", "mark", "nsubj", "cop", "advcl", "case", "nmod:poss", "amod", "nmod", "punct"], "triples": [{"uid": "1381-0", "target_tags": "Tried\\O the\\O pad\\B see\\I ew\\I on\\O the\\O recommendation\\O of\\O the\\O last\\O reviewer\\O since\\O it\\O 's\\O one\\O of\\O my\\O favorite\\O dishes\\O .\\O", "opinion_tags": "Tried\\B the\\O pad\\O see\\O ew\\O on\\O the\\O recommendation\\O of\\O the\\O last\\O reviewer\\O since\\O it\\O 's\\O one\\O of\\O my\\O favorite\\B dishes\\O .\\O", "sentiment": "neutral"}, {"uid": "1381-1", "target_tags": "Tried\\O the\\O pad\\O see\\O ew\\O on\\O the\\O recommendation\\O of\\O the\\O last\\O reviewer\\O since\\O it\\O 's\\O one\\O of\\O my\\O favorite\\O dishes\\B .\\O", "opinion_tags": "Tried\\O the\\O pad\\O see\\O ew\\O on\\O the\\O recommendation\\O of\\O the\\O last\\O reviewer\\O since\\O it\\O 's\\O one\\O of\\O my\\O favorite\\B dishes\\O .\\O", "sentiment": "positive"}]}, {"id": "1782", "sentence": "Taxan delicious !", "postag": ["RB", "JJ", "."], "head": [2, 0, 2], "deprel": ["advmod", "root", "punct"], "triples": [{"uid": "1782-0", "target_tags": "Taxan\\B delicious\\O !\\O", "opinion_tags": "Taxan\\O delicious\\B !\\O", "sentiment": "positive"}]}, {"id": "1424", "sentence": "my picks are : - Scallion Pancake ( fried with vegetable juice , very special and tasty ) - Guizhou Chicken - Shredded Squid Family Style ( one of my personal favorites ) - Sichuan Spicy Soft Shell Crab - Shuizhu Fish ( this one is for hardcore Sichuan food fans , I would n't recommend to my American friends as it 's very spicy .", "postag": ["PRP$", "NNS", "VBP", ":", ",", "NN", "NN", "-LRB-", "VBN", "IN", "NN", "NN", ",", "RB", "JJ", "CC", "JJ", "-RRB-", ",", "NNP", "NNP", "HYPH", "VBN", "NNP", "NNP", "NN", "-LRB-", "CD", "IN", "PRP$", "JJ", "NNS", "-RRB-", ",", "NNP", "NNP", "NNP", "NNP", "NNP", ",", "NNP", "NNP", "-LRB-", "DT", "NN", "VBZ", "IN", "JJ", "NNP", "NN", "NNS", ",", "PRP", "MD", "RB", "VB", "IN", "PRP$", "JJ", "NNS", "IN", "PRP", "VBZ", "RB", "JJ", "."], "head": [2, 7, 7, 7, 7, 7, 0, 9, 7, 12, 12, 9, 15, 15, 12, 17, 15, 15, 7, 26, 23, 23, 26, 25, 26, 7, 28, 26, 32, 32, 32, 28, 28, 7, 39, 39, 39, 39, 7, 7, 42, 7, 51, 45, 51, 51, 51, 51, 51, 51, 7, 7, 56, 56, 56, 7, 60, 60, 60, 56, 65, 65, 65, 65, 56, 7], "deprel": ["nmod:poss", "nsubj", "cop", "punct", "punct", "compound", "root", "punct", "acl", "case", "compound", "obl", "punct", "advmod", "amod", "cc", "conj", "punct", "punct", "compound", "compound", "punct", "amod", "compound", "compound", "conj", "punct", "appos", "case", "nmod:poss", "amod", "nmod", "punct", "punct", "compound", "compound", "compound", "compound", "conj", "punct", "compound", "conj", "punct", "det", "nsubj", "cop", "case", "amod", "compound", "compound", "parataxis", "punct", "nsubj", "aux", "advmod", "parataxis", "case", "nmod:poss", "amod", "obl", "mark", "nsubj", "cop", "advmod", "advcl", "punct"], "triples": [{"uid": "1424-0", "target_tags": "my\\O picks\\O are\\O :\\O -\\O Scallion\\B Pancake\\I (\\O fried\\O with\\O vegetable\\O juice\\O ,\\O very\\O special\\O and\\O tasty\\O )\\O -\\O Guizhou\\O Chicken\\O -\\O Shredded\\O Squid\\O Family\\O Style\\O (\\O one\\O of\\O my\\O personal\\O favorites\\O )\\O -\\O Sichuan\\O Spicy\\O Soft\\O Shell\\O Crab\\O -\\O Shuizhu\\O Fish\\O (\\O this\\O one\\O is\\O for\\O hardcore\\O Sichuan\\O food\\O fans\\O ,\\O I\\O would\\O n't\\O recommend\\O to\\O my\\O American\\O friends\\O as\\O it\\O 's\\O very\\O spicy\\O .\\O", "opinion_tags": "my\\O picks\\O are\\O :\\O -\\O Scallion\\O Pancake\\O (\\O fried\\O with\\O vegetable\\O juice\\O ,\\O very\\O special\\B and\\O tasty\\B )\\O -\\O Guizhou\\O Chicken\\O -\\O Shredded\\O Squid\\O Family\\O Style\\O (\\O one\\O of\\O my\\O personal\\O favorites\\O )\\O -\\O Sichuan\\O Spicy\\O Soft\\O Shell\\O Crab\\O -\\O Shuizhu\\O Fish\\O (\\O this\\O one\\O is\\O for\\O hardcore\\O Sichuan\\O food\\O fans\\O ,\\O I\\O would\\O n't\\O recommend\\O to\\O my\\O American\\O friends\\O as\\O it\\O 's\\O very\\O spicy\\O .\\O", "sentiment": "positive"}, {"uid": "1424-1", "target_tags": "my\\O picks\\O are\\O :\\O -\\O Scallion\\O Pancake\\O (\\O fried\\O with\\O vegetable\\O juice\\O ,\\O very\\O special\\O and\\O tasty\\O )\\O -\\O Guizhou\\O Chicken\\O -\\O Shredded\\B Squid\\I Family\\I Style\\I (\\O one\\O of\\O my\\O personal\\O favorites\\O )\\O -\\O Sichuan\\O Spicy\\O Soft\\O Shell\\O Crab\\O -\\O Shuizhu\\O Fish\\O (\\O this\\O one\\O is\\O for\\O hardcore\\O Sichuan\\O food\\O fans\\O ,\\O I\\O would\\O n't\\O recommend\\O to\\O my\\O American\\O friends\\O as\\O it\\O 's\\O very\\O spicy\\O .\\O", "opinion_tags": "my\\O picks\\O are\\O :\\O -\\O Scallion\\O Pancake\\O (\\O fried\\O with\\O vegetable\\O juice\\O ,\\O very\\O special\\O and\\O tasty\\O )\\O -\\O Guizhou\\O Chicken\\O -\\O Shredded\\O Squid\\O Family\\O Style\\O (\\O one\\O of\\O my\\O personal\\O favorites\\B )\\O -\\O Sichuan\\O Spicy\\O Soft\\O Shell\\O Crab\\O -\\O Shuizhu\\O Fish\\O (\\O this\\O one\\O is\\O for\\O hardcore\\O Sichuan\\O food\\O fans\\O ,\\O I\\O would\\O n't\\O recommend\\O to\\O my\\O American\\O friends\\O as\\O it\\O 's\\O very\\O spicy\\O .\\O", "sentiment": "positive"}, {"uid": "1424-2", "target_tags": "my\\O picks\\O are\\O :\\O -\\O Scallion\\O Pancake\\O (\\O fried\\O with\\O vegetable\\O juice\\O ,\\O very\\O special\\O and\\O tasty\\O )\\O -\\O Guizhou\\O Chicken\\O -\\O Shredded\\O Squid\\O Family\\O Style\\O (\\O one\\O of\\O my\\O personal\\O favorites\\O )\\O -\\O Sichuan\\O Spicy\\O Soft\\O Shell\\O Crab\\O -\\O Shuizhu\\B Fish\\I (\\O this\\O one\\O is\\O for\\O hardcore\\O Sichuan\\O food\\O fans\\O ,\\O I\\O would\\O n't\\O recommend\\O to\\O my\\O American\\O friends\\O as\\O it\\O 's\\O very\\O spicy\\O .\\O", "opinion_tags": "my\\O picks\\O are\\O :\\O -\\O Scallion\\O Pancake\\O (\\O fried\\O with\\O vegetable\\O juice\\O ,\\O very\\O special\\O and\\O tasty\\O )\\O -\\O Guizhou\\O Chicken\\O -\\O Shredded\\O Squid\\O Family\\O Style\\O (\\O one\\O of\\O my\\O personal\\O favorites\\O )\\O -\\O Sichuan\\O Spicy\\O Soft\\O Shell\\O Crab\\O -\\O Shuizhu\\O Fish\\O (\\O this\\O one\\O is\\O for\\O hardcore\\O Sichuan\\O food\\O fans\\O ,\\O I\\O would\\O n't\\O recommend\\O to\\O my\\O American\\O friends\\O as\\O it\\O 's\\O very\\O spicy\\B .\\O", "sentiment": "positive"}]}, {"id": "3235", "sentence": "The staff has always been friendly without seeming grating , and the chef has greeted us on a couple of occasions .", "postag": ["DT", "NN", "VBZ", "RB", "VBN", "JJ", "IN", "VBG", "VBG", ",", "CC", "DT", "NN", "VBZ", "VBN", "PRP", "IN", "DT", "NN", "IN", "NNS", "."], "head": [2, 6, 6, 6, 6, 0, 8, 6, 8, 15, 15, 13, 15, 15, 6, 15, 19, 19, 15, 21, 19, 6], "deprel": ["det", "nsubj", "aux", "advmod", "cop", "root", "mark", "advcl", "xcomp", "punct", "cc", "det", "nsubj", "aux", "conj", "obj", "case", "det", "obl", "case", "nmod", "punct"], "triples": [{"uid": "3235-0", "target_tags": "The\\O staff\\B has\\O always\\O been\\O friendly\\O without\\O seeming\\O grating\\O ,\\O and\\O the\\O chef\\O has\\O greeted\\O us\\O on\\O a\\O couple\\O of\\O occasions\\O .\\O", "opinion_tags": "The\\O staff\\O has\\O always\\O been\\O friendly\\B without\\O seeming\\O grating\\O ,\\O and\\O the\\O chef\\O has\\O greeted\\O us\\O on\\O a\\O couple\\O of\\O occasions\\O .\\O", "sentiment": "positive"}]}, {"id": "878", "sentence": "So if you want a nice , enjoyable meal at Montparnasse , go early for the pre-theater prix-fixe .", "postag": ["RB", "IN", "PRP", "VBP", "DT", "JJ", ",", "JJ", "NN", "IN", "NNP", ",", "VB", "RB", "IN", "DT", "JJ", "NN", "."], "head": [13, 4, 4, 13, 9, 9, 9, 9, 4, 11, 9, 4, 0, 13, 18, 18, 18, 13, 13], "deprel": ["advmod", "mark", "nsubj", "advcl", "det", "amod", "punct", "amod", "obj", "case", "nmod", "punct", "root", "advmod", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "878-0", "target_tags": "So\\O if\\O you\\O want\\O a\\O nice\\O ,\\O enjoyable\\O meal\\B at\\O Montparnasse\\O ,\\O go\\O early\\O for\\O the\\O pre-theater\\O prix-fixe\\O .\\O", "opinion_tags": "So\\O if\\O you\\O want\\O a\\O nice\\B ,\\O enjoyable\\B meal\\O at\\O Montparnasse\\O ,\\O go\\O early\\O for\\O the\\O pre-theater\\O prix-fixe\\O .\\O", "sentiment": "positive"}, {"uid": "878-1", "target_tags": "So\\O if\\O you\\O want\\O a\\O nice\\O ,\\O enjoyable\\O meal\\O at\\O Montparnasse\\O ,\\O go\\O early\\O for\\O the\\O pre-theater\\B prix-fixe\\I .\\O", "opinion_tags": "So\\O if\\O you\\O want\\O a\\O nice\\B ,\\O enjoyable\\B meal\\O at\\O Montparnasse\\O ,\\O go\\O early\\O for\\O the\\O pre-theater\\O prix-fixe\\O .\\O", "sentiment": "positive"}]}, {"id": "2770", "sentence": "First of all , this place is *not* romantic , as claimed by Citysearch 's editorial review .", "postag": ["RB", "IN", "DT", ",", "DT", "NN", "VBZ", "RB", "JJ", ",", "IN", "VBN", "IN", "NNP", "POS", "JJ", "NN", "."], "head": [9, 3, 1, 9, 6, 9, 9, 9, 0, 9, 12, 9, 17, 17, 14, 17, 12, 9], "deprel": ["advmod", "case", "obl", "punct", "det", "nsubj", "cop", "advmod", "root", "punct", "mark", "advcl", "case", "nmod:poss", "case", "amod", "obl", "punct"], "triples": [{"uid": "2770-0", "target_tags": "First\\O of\\O all\\O ,\\O this\\O place\\B is\\O *not*\\O romantic\\O ,\\O as\\O claimed\\O by\\O Citysearch\\O 's\\O editorial\\O review\\O .\\O", "opinion_tags": "First\\O of\\O all\\O ,\\O this\\O place\\O is\\O *not*\\B romantic\\I ,\\O as\\O claimed\\O by\\O Citysearch\\O 's\\O editorial\\O review\\O .\\O", "sentiment": "negative"}]}, {"id": "1062", "sentence": "Only drawback - they wo n't toast your bagel , and they do n't make eggs for the bagel .", "postag": ["JJ", "NN", ",", "PRP", "MD", "RB", "VB", "PRP$", "NN", ",", "CC", "PRP", "VBP", "RB", "VB", "NNS", "IN", "DT", "NN", "."], "head": [2, 0, 2, 7, 7, 7, 2, 9, 7, 15, 15, 15, 15, 15, 7, 15, 19, 19, 15, 2], "deprel": ["amod", "root", "punct", "nsubj", "aux", "advmod", "parataxis", "nmod:poss", "obj", "punct", "cc", "nsubj", "aux", "advmod", "conj", "obj", "case", "det", "obl", "punct"], "triples": [{"uid": "1062-0", "target_tags": "Only\\O drawback\\O -\\O they\\O wo\\O n't\\O toast\\O your\\O bagel\\B ,\\O and\\O they\\O do\\O n't\\O make\\O eggs\\O for\\O the\\O bagel\\O .\\O", "opinion_tags": "Only\\O drawback\\B -\\O they\\O wo\\O n't\\O toast\\O your\\O bagel\\O ,\\O and\\O they\\O do\\O n't\\O make\\O eggs\\O for\\O the\\O bagel\\O .\\O", "sentiment": "negative"}]}, {"id": "2782", "sentence": "All in all the food was above average and I would return to see how they operate with four or less dinners .", "postag": ["DT", "IN", "PDT", "DT", "NN", "VBD", "IN", "NN", "CC", "PRP", "MD", "VB", "TO", "VB", "WRB", "PRP", "VBP", "IN", "CD", "CC", "JJR", "NNS", "."], "head": [8, 5, 5, 5, 1, 8, 8, 0, 12, 12, 12, 8, 14, 12, 17, 17, 14, 22, 22, 21, 19, 17, 8], "deprel": ["nsubj", "case", "det:predet", "det", "nmod", "cop", "case", "root", "cc", "nsubj", "aux", "conj", "mark", "advcl", "mark", "nsubj", "ccomp", "case", "nummod", "cc", "conj", "obl", "punct"], "triples": [{"uid": "2782-0", "target_tags": "All\\O in\\O all\\O the\\O food\\B was\\O above\\O average\\O and\\O I\\O would\\O return\\O to\\O see\\O how\\O they\\O operate\\O with\\O four\\O or\\O less\\O dinners\\O .\\O", "opinion_tags": "All\\O in\\O all\\O the\\O food\\O was\\O above\\B average\\I and\\O I\\O would\\O return\\O to\\O see\\O how\\O they\\O operate\\O with\\O four\\O or\\O less\\O dinners\\O .\\O", "sentiment": "positive"}]}, {"id": "3457", "sentence": "From the moment we walked in they were more than accomodating even though the place was packed .", "postag": ["IN", "DT", "NN", "PRP", "VBD", "IN", "PRP", "VBD", "JJR", "IN", "JJ", "RB", "IN", "DT", "NN", "VBD", "VBN", "."], "head": [3, 3, 9, 5, 3, 5, 9, 9, 0, 11, 9, 17, 17, 15, 17, 17, 9, 9], "deprel": ["case", "det", "obl", "nsubj", "acl:relcl", "obl", "nsubj", "cop", "root", "case", "obl", "advmod", "mark", "det", "nsubj:pass", "aux:pass", "advcl", "punct"], "triples": [{"uid": "3457-0", "target_tags": "From\\O the\\O moment\\O we\\O walked\\O in\\O they\\O were\\O more\\O than\\O accomodating\\O even\\O though\\O the\\O place\\B was\\O packed\\O .\\O", "opinion_tags": "From\\O the\\O moment\\O we\\O walked\\O in\\O they\\O were\\O more\\O than\\O accomodating\\O even\\O though\\O the\\O place\\O was\\O packed\\B .\\O", "sentiment": "negative"}]}, {"id": "3117", "sentence": "And the food was fantastic .", "postag": ["CC", "DT", "NN", "VBD", "JJ", "."], "head": [5, 3, 5, 5, 0, 5], "deprel": ["cc", "det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "3117-0", "target_tags": "And\\O the\\O food\\B was\\O fantastic\\O .\\O", "opinion_tags": "And\\O the\\O food\\O was\\O fantastic\\B .\\O", "sentiment": "positive"}]}, {"id": "1711", "sentence": "Diner food at bistro prices is a bummer ... .", "postag": ["NN", "NN", "IN", "NN", "NNS", "VBZ", "DT", "NN", ",", "."], "head": [2, 8, 5, 5, 2, 8, 8, 0, 8, 8], "deprel": ["compound", "nsubj", "case", "compound", "nmod", "cop", "det", "root", "punct", "punct"], "triples": [{"uid": "1711-0", "target_tags": "Diner\\O food\\B at\\O bistro\\O prices\\O is\\O a\\O bummer\\O ...\\O .\\O", "opinion_tags": "Diner\\O food\\O at\\O bistro\\O prices\\O is\\O a\\O bummer\\B ...\\O .\\O", "sentiment": "negative"}, {"uid": "1711-1", "target_tags": "Diner\\O food\\O at\\O bistro\\O prices\\B is\\O a\\O bummer\\O ...\\O .\\O", "opinion_tags": "Diner\\O food\\O at\\O bistro\\O prices\\O is\\O a\\O bummer\\B ...\\O .\\O", "sentiment": "negative"}]}, {"id": "1535", "sentence": "They might be all business at the counter when you give your order , but their food says I love you .", "postag": ["PRP", "MD", "VB", "DT", "NN", "IN", "DT", "NN", "WRB", "PRP", "VBP", "PRP$", "NN", ",", "CC", "PRP$", "NN", "VBZ", "PRP", "VBP", "PRP", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 11, 11, 5, 13, 11, 18, 18, 17, 18, 5, 20, 18, 20, 5], "deprel": ["nsubj", "aux", "cop", "det", "root", "case", "det", "nmod", "mark", "nsubj", "advcl", "nmod:poss", "obj", "punct", "cc", "nmod:poss", "nsubj", "conj", "nsubj", "ccomp", "obj", "punct"], "triples": [{"uid": "1535-0", "target_tags": "They\\O might\\O be\\O all\\O business\\O at\\O the\\O counter\\O when\\O you\\O give\\O your\\O order\\O ,\\O but\\O their\\O food\\B says\\O I\\O love\\O you\\O .\\O", "opinion_tags": "They\\O might\\O be\\O all\\O business\\O at\\O the\\O counter\\O when\\O you\\O give\\O your\\O order\\O ,\\O but\\O their\\O food\\O says\\O I\\O love\\B you\\O .\\O", "sentiment": "positive"}, {"uid": "1535-1", "target_tags": "They\\O might\\O be\\O all\\O business\\O at\\O the\\O counter\\B when\\O you\\O give\\O your\\O order\\O ,\\O but\\O their\\O food\\O says\\O I\\O love\\O you\\O .\\O", "opinion_tags": "They\\O might\\O be\\O all\\O business\\O at\\O the\\O counter\\O when\\O you\\O give\\O your\\O order\\O ,\\O but\\O their\\O food\\O says\\O I\\O love\\B you\\O .\\O", "sentiment": "negative"}]}, {"id": "2786", "sentence": "Its a great place for a casual date or to entertain clients for lunch .", "postag": ["PRP$", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", "CC", "TO", "VB", "NNS", "IN", "NN", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 11, 11, 4, 11, 14, 11, 4], "deprel": ["nmod:poss", "det", "amod", "root", "case", "det", "amod", "nmod", "cc", "mark", "conj", "obj", "case", "obl", "punct"], "triples": [{"uid": "2786-0", "target_tags": "Its\\O a\\O great\\O place\\O for\\O a\\O casual\\O date\\O or\\O to\\O entertain\\O clients\\O for\\O lunch\\B .\\O", "opinion_tags": "Its\\O a\\O great\\B place\\O for\\O a\\O casual\\O date\\O or\\O to\\O entertain\\O clients\\O for\\O lunch\\O .\\O", "sentiment": "neutral"}]}, {"id": "2132", "sentence": "The food , drinks and service are clearly among the best in the city .", "postag": ["DT", "NN", ",", "NNS", "CC", "NN", "VBP", "RB", "IN", "DT", "JJS", "IN", "DT", "NN", "."], "head": [2, 11, 4, 2, 6, 2, 11, 11, 11, 11, 0, 14, 14, 11, 11], "deprel": ["det", "nsubj", "punct", "conj", "cc", "conj", "cop", "advmod", "case", "det", "root", "case", "det", "obl", "punct"], "triples": [{"uid": "2132-0", "target_tags": "The\\O food\\B ,\\O drinks\\O and\\O service\\O are\\O clearly\\O among\\O the\\O best\\O in\\O the\\O city\\O .\\O", "opinion_tags": "The\\O food\\O ,\\O drinks\\O and\\O service\\O are\\O clearly\\O among\\O the\\O best\\B in\\O the\\O city\\O .\\O", "sentiment": "positive"}, {"uid": "2132-1", "target_tags": "The\\O food\\O ,\\O drinks\\B and\\O service\\O are\\O clearly\\O among\\O the\\O best\\O in\\O the\\O city\\O .\\O", "opinion_tags": "The\\O food\\O ,\\O drinks\\O and\\O service\\O are\\O clearly\\O among\\O the\\O best\\B in\\O the\\O city\\O .\\O", "sentiment": "positive"}, {"uid": "2132-2", "target_tags": "The\\O food\\O ,\\O drinks\\O and\\O service\\B are\\O clearly\\O among\\O the\\O best\\O in\\O the\\O city\\O .\\O", "opinion_tags": "The\\O food\\O ,\\O drinks\\O and\\O service\\O are\\O clearly\\O among\\O the\\O best\\B in\\O the\\O city\\O .\\O", "sentiment": "positive"}]}, {"id": "2059", "sentence": "and yes <PERSON> is so dam good and so are all the kababs .", "postag": ["CC", "UH", "NNP", "NNP", "VBZ", "RB", "NN", "JJ", "CC", "RB", "VBP", "PDT", "DT", "NNS", "."], "head": [8, 8, 4, 8, 8, 8, 8, 0, 14, 14, 14, 14, 14, 8, 8], "deprel": ["cc", "discourse", "compound", "nsubj", "cop", "advmod", "obl:npmod", "root", "cc", "advmod", "cop", "det:predet", "det", "conj", "punct"], "triples": [{"uid": "2059-0", "target_tags": "and\\O yes\\O Dal\\O Bukhara\\O is\\O so\\O dam\\O good\\O and\\O so\\O are\\O all\\O the\\O kababs\\B .\\O", "opinion_tags": "and\\O yes\\O Dal\\O Bukhara\\O is\\O so\\O dam\\O good\\B and\\O so\\O are\\O all\\O the\\O kababs\\O .\\O", "sentiment": "positive"}, {"uid": "2059-1", "target_tags": "and\\O yes\\O Dal\\B Bukhara\\I is\\O so\\O dam\\O good\\O and\\O so\\O are\\O all\\O the\\O kababs\\O .\\O", "opinion_tags": "and\\O yes\\O Dal\\O Bukhara\\O is\\O so\\O dam\\O good\\B and\\O so\\O are\\O all\\O the\\O kababs\\O .\\O", "sentiment": "positive"}]}, {"id": "1675", "sentence": "For the quality of food , a little too expensive .", "postag": ["IN", "DT", "NN", "IN", "NN", ",", "DT", "JJ", "RB", "JJ", "."], "head": [3, 3, 10, 5, 3, 10, 8, 10, 10, 0, 10], "deprel": ["case", "det", "obl", "case", "nmod", "punct", "det", "obl:npmod", "advmod", "root", "punct"], "triples": [{"uid": "1675-0", "target_tags": "For\\O the\\O quality\\B of\\I food\\I ,\\O a\\O little\\O too\\O expensive\\O .\\O", "opinion_tags": "For\\O the\\O quality\\O of\\O food\\O ,\\O a\\O little\\O too\\O expensive\\B .\\O", "sentiment": "negative"}]}, {"id": "1440", "sentence": "The hot and sour soup was unbearably hot and tasted of only pepper and nothing else .", "postag": ["DT", "JJ", "CC", "JJ", "NN", "VBD", "RB", "JJ", "CC", "VBD", "IN", "RB", "NN", "CC", "NN", "JJ", "."], "head": [5, 5, 4, 2, 8, 8, 8, 0, 10, 8, 13, 13, 10, 15, 13, 15, 8], "deprel": ["det", "amod", "cc", "conj", "nsubj", "cop", "advmod", "root", "cc", "conj", "case", "advmod", "obl", "cc", "conj", "amod", "punct"], "triples": [{"uid": "1440-0", "target_tags": "The\\O hot\\O and\\O sour\\O soup\\B was\\O unbearably\\O hot\\O and\\O tasted\\O of\\O only\\O pepper\\O and\\O nothing\\O else\\O .\\O", "opinion_tags": "The\\O hot\\O and\\O sour\\O soup\\O was\\O unbearably\\B hot\\I and\\O tasted\\O of\\O only\\O pepper\\O and\\O nothing\\O else\\O .\\O", "sentiment": "negative"}]}, {"id": "2529", "sentence": "As for the bar , this is another bad idea .", "postag": ["IN", "IN", "DT", "NN", ",", "DT", "VBZ", "DT", "JJ", "NN", "."], "head": [4, 1, 4, 10, 10, 10, 10, 10, 10, 0, 10], "deprel": ["case", "fixed", "det", "obl", "punct", "nsubj", "cop", "det", "amod", "root", "punct"], "triples": [{"uid": "2529-0", "target_tags": "As\\O for\\O the\\O bar\\B ,\\O this\\O is\\O another\\O bad\\O idea\\O .\\O", "opinion_tags": "As\\O for\\O the\\O bar\\O ,\\O this\\O is\\O another\\O bad\\B idea\\O .\\O", "sentiment": "negative"}]}, {"id": "2861", "sentence": "While the staff at this little bistro is very friendly , I have never experienced more incompetency .", "postag": ["IN", "DT", "NN", "IN", "DT", "JJ", "NN", "VBZ", "RB", "JJ", ",", "PRP", "VBP", "RB", "VBN", "JJR", "NN", "."], "head": [10, 3, 10, 7, 7, 7, 3, 10, 10, 15, 15, 15, 15, 15, 0, 17, 15, 15], "deprel": ["mark", "det", "nsubj", "case", "det", "amod", "nmod", "cop", "advmod", "advcl", "punct", "nsubj", "aux", "advmod", "root", "amod", "obj", "punct"], "triples": [{"uid": "2861-0", "target_tags": "While\\O the\\O staff\\B at\\O this\\O little\\O bistro\\O is\\O very\\O friendly\\O ,\\O I\\O have\\O never\\O experienced\\O more\\O incompetency\\O .\\O", "opinion_tags": "While\\O the\\O staff\\O at\\O this\\O little\\O bistro\\O is\\O very\\O friendly\\B ,\\O I\\O have\\O never\\O experienced\\O more\\O incompetency\\O .\\O", "sentiment": "positive"}]}, {"id": "2944", "sentence": "We 've been to Grocery three times and not once has an item on the menu disappointed .", "postag": ["PRP", "VBP", "VBN", "IN", "NNP", "CD", "NNS", "CC", "RB", "RB", "VBZ", "DT", "NN", "IN", "DT", "NN", "JJ", "."], "head": [5, 5, 5, 5, 0, 7, 5, 11, 10, 11, 5, 13, 11, 16, 16, 13, 11, 5], "deprel": ["nsubj", "aux", "cop", "case", "root", "nummod", "obl:tmod", "cc", "advmod", "advmod", "conj", "det", "obj", "case", "det", "nmod", "xcomp", "punct"], "triples": [{"uid": "2944-0", "target_tags": "We\\O 've\\O been\\O to\\O Grocery\\O three\\O times\\O and\\O not\\O once\\O has\\O an\\O item\\O on\\O the\\O menu\\B disappointed\\O .\\O", "opinion_tags": "We\\O 've\\O been\\O to\\O Grocery\\O three\\O times\\O and\\O not\\O once\\O has\\O an\\O item\\O on\\O the\\O menu\\O disappointed\\B .\\O", "sentiment": "positive"}]}, {"id": "1393", "sentence": "I really liked the noodle dishes at Rice Avenue compared to their Green Curry dish .", "postag": ["PRP", "RB", "VBD", "DT", "NN", "NNS", "IN", "NNP", "NNP", "VBN", "IN", "PRP$", "NNP", "NNP", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 3, 15, 15, 15, 14, 15, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "compound", "obj", "case", "compound", "obl", "case", "case", "nmod:poss", "compound", "compound", "obl", "punct"], "triples": [{"uid": "1393-0", "target_tags": "I\\O really\\O liked\\O the\\O noodle\\B dishes\\I at\\O Rice\\O Avenue\\O compared\\O to\\O their\\O Green\\O Curry\\O dish\\O .\\O", "opinion_tags": "I\\O really\\O liked\\B the\\O noodle\\O dishes\\O at\\O Rice\\O Avenue\\O compared\\O to\\O their\\O Green\\O Curry\\O dish\\O .\\O", "sentiment": "positive"}]}, {"id": "3145", "sentence": "The Dim Sum was so-so , but not spectacular .", "postag": ["DT", "NNP", "NN", "VBD", "JJ", ",", "CC", "RB", "JJ", "."], "head": [3, 3, 5, 5, 0, 9, 9, 9, 5, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "3145-0", "target_tags": "The\\O Dim\\B Sum\\I was\\O so-so\\O ,\\O but\\O not\\O spectacular\\O .\\O", "opinion_tags": "The\\O Dim\\O Sum\\O was\\O so-so\\B ,\\O but\\O not\\B spectacular\\I .\\O", "sentiment": "neutral"}]}, {"id": "3043", "sentence": "We usually just get some of the dinner specials and they are very reasonably priced and very tasty .", "postag": ["PRP", "RB", "RB", "VBP", "DT", "IN", "DT", "NN", "NNS", "CC", "PRP", "VBP", "RB", "RB", "JJ", "CC", "RB", "JJ", "."], "head": [4, 4, 4, 0, 4, 9, 9, 9, 5, 15, 15, 15, 14, 15, 4, 18, 18, 15, 4], "deprel": ["nsubj", "advmod", "advmod", "root", "obj", "case", "det", "compound", "nmod", "cc", "nsubj", "cop", "advmod", "advmod", "conj", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "3043-0", "target_tags": "We\\O usually\\O just\\O get\\O some\\O of\\O the\\O dinner\\B specials\\I and\\O they\\O are\\O very\\O reasonably\\O priced\\O and\\O very\\O tasty\\O .\\O", "opinion_tags": "We\\O usually\\O just\\O get\\O some\\O of\\O the\\O dinner\\O specials\\O and\\O they\\O are\\O very\\O reasonably\\B priced\\I and\\O very\\O tasty\\B .\\O", "sentiment": "positive"}, {"uid": "3043-1", "target_tags": "We\\O usually\\O just\\O get\\O some\\O of\\O the\\O dinner\\O specials\\O and\\O they\\O are\\O very\\O reasonably\\O priced\\B and\\O very\\O tasty\\O .\\O", "opinion_tags": "We\\O usually\\O just\\O get\\O some\\O of\\O the\\O dinner\\O specials\\O and\\O they\\O are\\O very\\O reasonably\\B priced\\O and\\O very\\O tasty\\O .\\O", "sentiment": "positive"}]}, {"id": "2961", "sentence": "Fresh , authentic , french cuisine in substantial portions .", "postag": ["JJ", ",", "JJ", ",", "JJ", "NN", "IN", "JJ", "NNS", "."], "head": [6, 3, 1, 6, 6, 0, 9, 9, 6, 6], "deprel": ["amod", "punct", "conj", "punct", "amod", "root", "case", "amod", "nmod", "punct"], "triples": [{"uid": "2961-0", "target_tags": "Fresh\\O ,\\O authentic\\O ,\\O french\\B cuisine\\I in\\O substantial\\O portions\\O .\\O", "opinion_tags": "Fresh\\B ,\\O authentic\\B ,\\O french\\O cuisine\\O in\\O substantial\\O portions\\O .\\O", "sentiment": "positive"}, {"uid": "2961-1", "target_tags": "Fresh\\O ,\\O authentic\\O ,\\O french\\O cuisine\\O in\\O substantial\\O portions\\B .\\O", "opinion_tags": "Fresh\\O ,\\O authentic\\O ,\\O french\\O cuisine\\O in\\O substantial\\B portions\\O .\\O", "sentiment": "positive"}]}, {"id": "759", "sentence": "For great chinese food nearby , you have Wu Liang Ye and Grand Sichuan just a block away .", "postag": ["IN", "JJ", "JJ", "NN", "RB", ",", "PRP", "VBP", "NNP", "NNP", "NNP", "CC", "NNP", "NNP", "RB", "DT", "NN", "RB", "."], "head": [4, 4, 4, 8, 4, 8, 8, 0, 11, 9, 8, 14, 14, 11, 18, 17, 18, 8, 8], "deprel": ["case", "amod", "amod", "obl", "advmod", "punct", "nsubj", "root", "compound", "flat", "obj", "cc", "compound", "conj", "advmod", "det", "obl:npmod", "advmod", "punct"], "triples": [{"uid": "759-0", "target_tags": "For\\O great\\O chinese\\B food\\I nearby\\O ,\\O you\\O have\\O Wu\\O Liang\\O Ye\\O and\\O Grand\\O Sichuan\\O just\\O a\\O block\\O away\\O .\\O", "opinion_tags": "For\\O great\\B chinese\\O food\\O nearby\\O ,\\O you\\O have\\O Wu\\O Liang\\O Ye\\O and\\O Grand\\O Sichuan\\O just\\O a\\O block\\O away\\O .\\O", "sentiment": "positive"}]}, {"id": "1514", "sentence": "And they have these home made potato chips at the bar that are the most delicious things in the world !", "postag": ["CC", "PRP", "VBP", "DT", "NN", "VBN", "NN", "NNS", "IN", "DT", "NN", "WDT", "VBP", "DT", "RBS", "JJ", "NNS", "IN", "DT", "NN", "."], "head": [3, 3, 0, 8, 8, 8, 8, 3, 11, 11, 8, 17, 17, 17, 16, 17, 11, 20, 20, 17, 3], "deprel": ["cc", "nsubj", "root", "det", "compound", "amod", "compound", "obj", "case", "det", "nmod", "nsubj", "cop", "det", "advmod", "amod", "acl:relcl", "case", "det", "nmod", "punct"], "triples": [{"uid": "1514-0", "target_tags": "And\\O they\\O have\\O these\\O home\\O made\\O potato\\B chips\\I at\\O the\\O bar\\O that\\O are\\O the\\O most\\O delicious\\O things\\O in\\O the\\O world\\O !\\O", "opinion_tags": "And\\O they\\O have\\O these\\O home\\O made\\O potato\\O chips\\O at\\O the\\O bar\\O that\\O are\\O the\\O most\\O delicious\\B things\\O in\\O the\\O world\\O !\\O", "sentiment": "positive"}, {"uid": "1514-1", "target_tags": "And\\O they\\O have\\O these\\O home\\O made\\O potato\\O chips\\O at\\O the\\O bar\\B that\\O are\\O the\\O most\\O delicious\\O things\\O in\\O the\\O world\\O !\\O", "opinion_tags": "And\\O they\\O have\\O these\\O home\\O made\\O potato\\O chips\\O at\\O the\\O bar\\O that\\O are\\O the\\O most\\O delicious\\B things\\O in\\O the\\O world\\O !\\O", "sentiment": "neutral"}]}, {"id": "1195", "sentence": "The sushi is average and the prices are anything but .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NNS", "VBP", "NN", "CC", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "advmod", "punct"], "triples": [{"uid": "1195-0", "target_tags": "The\\O sushi\\B is\\O average\\O and\\O the\\O prices\\O are\\O anything\\O but\\O .\\O", "opinion_tags": "The\\O sushi\\O is\\O average\\B and\\O the\\O prices\\O are\\O anything\\O but\\O .\\O", "sentiment": "neutral"}]}, {"id": "3035", "sentence": "As I made the title , it 's an affordable restaurant for great taste .", "postag": ["IN", "PRP", "VBD", "DT", "NN", ",", "PRP", "VBZ", "DT", "JJ", "NN", "IN", "JJ", "NN", "."], "head": [3, 3, 11, 5, 3, 11, 11, 11, 11, 11, 0, 14, 14, 11, 11], "deprel": ["mark", "nsubj", "advcl", "det", "obj", "punct", "nsubj", "cop", "det", "amod", "root", "case", "amod", "nmod", "punct"], "triples": [{"uid": "3035-0", "target_tags": "As\\O I\\O made\\O the\\O title\\O ,\\O it\\O 's\\O an\\O affordable\\O restaurant\\O for\\O great\\O taste\\B .\\O", "opinion_tags": "As\\O I\\O made\\O the\\O title\\O ,\\O it\\O 's\\O an\\O affordable\\O restaurant\\O for\\O great\\B taste\\O .\\O", "sentiment": "positive"}]}, {"id": "2150", "sentence": "Good drink .", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "2150-0", "target_tags": "Good\\O drink\\B .\\O", "opinion_tags": "Good\\B drink\\O .\\O", "sentiment": "positive"}]}, {"id": "1399", "sentence": "The food is consistently wonderful - I 've been coming here for years , and the owner has always been accomodating and friendly .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", ",", "PRP", "VBP", "VBN", "VBG", "RB", "IN", "NNS", ",", "CC", "DT", "NN", "VBZ", "RB", "VBN", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 5, 10, 10, 10, 5, 10, 13, 10, 21, 21, 17, 21, 21, 21, 21, 5, 23, 21, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "nsubj", "aux", "aux", "parataxis", "advmod", "case", "obl", "punct", "cc", "det", "nsubj", "aux", "advmod", "cop", "conj", "cc", "conj", "punct"], "triples": [{"uid": "1399-0", "target_tags": "The\\O food\\B is\\O consistently\\O wonderful\\O -\\O I\\O 've\\O been\\O coming\\O here\\O for\\O years\\O ,\\O and\\O the\\O owner\\O has\\O always\\O been\\O accomodating\\O and\\O friendly\\O .\\O", "opinion_tags": "The\\O food\\O is\\O consistently\\O wonderful\\B -\\O I\\O 've\\O been\\O coming\\O here\\O for\\O years\\O ,\\O and\\O the\\O owner\\O has\\O always\\O been\\O accomodating\\O and\\O friendly\\O .\\O", "sentiment": "positive"}, {"uid": "1399-1", "target_tags": "The\\O food\\O is\\O consistently\\O wonderful\\O -\\O I\\O 've\\O been\\O coming\\O here\\O for\\O years\\O ,\\O and\\O the\\O owner\\B has\\O always\\O been\\O accomodating\\O and\\O friendly\\O .\\O", "opinion_tags": "The\\O food\\O is\\O consistently\\O wonderful\\O -\\O I\\O 've\\O been\\O coming\\O here\\O for\\O years\\O ,\\O and\\O the\\O owner\\O has\\O always\\O been\\O accomodating\\B and\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "547", "sentence": "I 've had to wait only a few times during lunch but this place is definitely worth the wait .", "postag": ["PRP", "VBP", "VBN", "TO", "VB", "RB", "DT", "JJ", "NNS", "IN", "NN", "CC", "DT", "NN", "VBZ", "RB", "JJ", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 9, 9, 9, 5, 11, 5, 17, 14, 17, 17, 17, 3, 19, 17, 3], "deprel": ["nsubj", "aux", "root", "mark", "xcomp", "advmod", "det", "amod", "obl:tmod", "case", "obl", "cc", "det", "nsubj", "cop", "advmod", "conj", "det", "obj", "punct"], "triples": [{"uid": "547-0", "target_tags": "I\\O 've\\O had\\O to\\O wait\\O only\\O a\\O few\\O times\\O during\\O lunch\\O but\\O this\\O place\\O is\\O definitely\\O worth\\O the\\O wait\\B .\\O", "opinion_tags": "I\\O 've\\O had\\O to\\O wait\\O only\\O a\\O few\\O times\\O during\\O lunch\\O but\\O this\\O place\\O is\\O definitely\\O worth\\B the\\O wait\\O .\\O", "sentiment": "positive"}, {"uid": "547-1", "target_tags": "I\\O 've\\O had\\O to\\O wait\\B only\\O a\\O few\\O times\\O during\\O lunch\\O but\\O this\\O place\\O is\\O definitely\\O worth\\O the\\O wait\\O .\\O", "opinion_tags": "I\\O 've\\O had\\O to\\O wait\\O only\\O a\\O few\\O times\\O during\\O lunch\\O but\\O this\\O place\\O is\\O definitely\\O worth\\B the\\I wait\\I .\\O", "sentiment": "positive"}]}, {"id": "1639", "sentence": "The wine list is n't great , and the desserts are shipped in from Bruno 's down the street , which is not as good as it used to be .", "postag": ["DT", "NN", "NN", "VBZ", "RB", "JJ", ",", "CC", "DT", "NNS", "VBP", "VBN", "RP", "IN", "NNP", "POS", "IN", "DT", "NN", ",", "WDT", "VBZ", "RB", "RB", "JJ", "IN", "PRP", "VBD", "TO", "VB", "."], "head": [3, 3, 6, 6, 6, 0, 12, 12, 10, 12, 12, 6, 12, 19, 19, 15, 19, 19, 12, 19, 25, 25, 25, 25, 19, 28, 28, 25, 30, 28, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "punct", "cc", "det", "nsubj:pass", "aux:pass", "conj", "compound:prt", "case", "nmod:poss", "case", "case", "det", "obl", "punct", "nsubj", "cop", "advmod", "advmod", "acl:relcl", "mark", "nsubj", "advcl", "mark", "xcomp", "punct"], "triples": [{"uid": "1639-0", "target_tags": "The\\O wine\\B list\\I is\\O n't\\O great\\O ,\\O and\\O the\\O desserts\\O are\\O shipped\\O in\\O from\\O Bruno\\O 's\\O down\\O the\\O street\\O ,\\O which\\O is\\O not\\O as\\O good\\O as\\O it\\O used\\O to\\O be\\O .\\O", "opinion_tags": "The\\O wine\\O list\\O is\\B n't\\I great\\I ,\\O and\\O the\\O desserts\\O are\\O shipped\\O in\\O from\\O Bruno\\O 's\\O down\\O the\\O street\\O ,\\O which\\O is\\O not\\O as\\O good\\O as\\O it\\O used\\O to\\O be\\O .\\O", "sentiment": "negative"}, {"uid": "1639-1", "target_tags": "The\\O wine\\O list\\O is\\O n't\\O great\\O ,\\O and\\O the\\O desserts\\B are\\O shipped\\O in\\O from\\O Bruno\\O 's\\O down\\O the\\O street\\O ,\\O which\\O is\\O not\\O as\\O good\\O as\\O it\\O used\\O to\\O be\\O .\\O", "opinion_tags": "The\\O wine\\O list\\O is\\O n't\\O great\\O ,\\O and\\O the\\O desserts\\O are\\O shipped\\O in\\O from\\O Bruno\\O 's\\O down\\O the\\O street\\O ,\\O which\\O is\\O not\\B as\\I good\\I as\\O it\\O used\\O to\\O be\\O .\\O", "sentiment": "negative"}]}, {"id": "1972", "sentence": "I could have drank 4 glasses of water and still been parched - so watch out .", "postag": ["PRP", "MD", "VB", "VBN", "CD", "NNS", "IN", "NN", "CC", "RB", "VBN", "JJ", ",", "RB", "VB", "RP", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 12, 12, 12, 4, 4, 15, 4, 15, 4], "deprel": ["nsubj", "aux", "aux", "root", "nummod", "obj", "case", "nmod", "cc", "advmod", "cop", "conj", "punct", "advmod", "parataxis", "compound:prt", "punct"], "triples": [{"uid": "1972-0", "target_tags": "I\\O could\\O have\\O drank\\O 4\\O glasses\\B of\\I water\\I and\\O still\\O been\\O parched\\O -\\O so\\O watch\\O out\\O .\\O", "opinion_tags": "I\\O could\\O have\\O drank\\O 4\\O glasses\\O of\\O water\\O and\\O still\\O been\\O parched\\B -\\O so\\O watch\\O out\\O .\\O", "sentiment": "neutral"}]}, {"id": "1182", "sentence": "The food is very good for it 's price , better than most fried dumplings I 've had .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "IN", "PRP", "VBZ", "NN", ",", "JJR", "IN", "RBS", "VBN", "NNS", "PRP", "VBP", "VBN", "."], "head": [2, 5, 5, 5, 0, 9, 9, 9, 5, 11, 5, 15, 14, 15, 11, 18, 18, 15, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "nsubj", "cop", "obl", "punct", "parataxis", "case", "advmod", "amod", "obl", "nsubj", "aux", "acl:relcl", "punct"], "triples": [{"uid": "1182-0", "target_tags": "The\\O food\\B is\\O very\\O good\\O for\\O it\\O 's\\O price\\O ,\\O better\\O than\\O most\\O fried\\O dumplings\\O I\\O 've\\O had\\O .\\O", "opinion_tags": "The\\O food\\O is\\O very\\O good\\B for\\O it\\O 's\\O price\\O ,\\O better\\B than\\O most\\O fried\\O dumplings\\O I\\O 've\\O had\\O .\\O", "sentiment": "positive"}, {"uid": "1182-1", "target_tags": "The\\O food\\O is\\O very\\O good\\O for\\O it\\O 's\\O price\\O ,\\O better\\O than\\O most\\O fried\\B dumplings\\I I\\O 've\\O had\\O .\\O", "opinion_tags": "The\\O food\\O is\\O very\\O good\\O for\\O it\\O 's\\O price\\O ,\\O better\\B than\\O most\\O fried\\O dumplings\\O I\\O 've\\O had\\O .\\O", "sentiment": "negative"}]}, {"id": "1296", "sentence": "They charge different prices all the time .", "postag": ["PRP", "VBP", "JJ", "NNS", "PDT", "DT", "NN", "."], "head": [2, 0, 4, 2, 7, 7, 2, 2], "deprel": ["nsubj", "root", "amod", "obj", "det:predet", "det", "obl:tmod", "punct"], "triples": [{"uid": "1296-0", "target_tags": "They\\O charge\\O different\\O prices\\B all\\O the\\O time\\O .\\O", "opinion_tags": "They\\O charge\\O different\\B prices\\O all\\O the\\O time\\O .\\O", "sentiment": "negative"}]}, {"id": "443", "sentence": "Pizza - the only pizza in NYC that should not have additional toppings - the crust tastes like the best , freshly baked bread !", "postag": ["NN", ",", "DT", "JJ", "NN", "IN", "NNP", "WDT", "MD", "RB", "VB", "JJ", "NNS", ",", "DT", "NN", "VBZ", "IN", "DT", "JJS", ",", "RB", "VBN", "NN", "."], "head": [0, 1, 5, 5, 1, 7, 5, 11, 11, 11, 5, 13, 11, 1, 16, 17, 1, 24, 24, 24, 24, 23, 24, 17, 1], "deprel": ["root", "punct", "det", "amod", "appos", "case", "nmod", "nsubj", "aux", "advmod", "acl:relcl", "amod", "obj", "punct", "det", "nsubj", "parataxis", "case", "det", "amod", "punct", "advmod", "amod", "obl", "punct"], "triples": [{"uid": "443-0", "target_tags": "Pizza\\O -\\O the\\O only\\O pizza\\O in\\O NYC\\O that\\O should\\O not\\O have\\O additional\\O toppings\\O -\\O the\\O crust\\B tastes\\O like\\O the\\O best\\O ,\\O freshly\\O baked\\O bread\\O !\\O", "opinion_tags": "Pizza\\O -\\O the\\O only\\O pizza\\O in\\O NYC\\O that\\O should\\O not\\O have\\O additional\\O toppings\\O -\\O the\\O crust\\O tastes\\O like\\O the\\O best\\B ,\\O freshly\\O baked\\O bread\\O !\\O", "sentiment": "positive"}, {"uid": "443-1", "target_tags": "Pizza\\O -\\O the\\O only\\O pizza\\O in\\O NYC\\O that\\O should\\O not\\O have\\O additional\\O toppings\\O -\\O the\\O crust\\O tastes\\O like\\O the\\O best\\O ,\\O freshly\\O baked\\O bread\\B !\\O", "opinion_tags": "Pizza\\O -\\O the\\O only\\O pizza\\O in\\O NYC\\O that\\O should\\O not\\O have\\O additional\\O toppings\\O -\\O the\\O crust\\O tastes\\O like\\O the\\O best\\O ,\\O freshly\\B baked\\I bread\\O !\\O", "sentiment": "positive"}]}, {"id": "3634", "sentence": "I would highly recommend <PERSON> 's to anyone who wants to have a romantic dinner in a heart warming surrounding filled with candles and family pictures .", "postag": ["PRP", "MD", "RB", "VB", "NNP", "POS", "IN", "NN", "WP", "VBZ", "TO", "VB", "DT", "JJ", "NN", "IN", "DT", "NN", "NN", "VBG", "VBN", "IN", "NNS", "CC", "NN", "NNS", "."], "head": [4, 4, 4, 0, 4, 5, 8, 4, 10, 8, 12, 10, 15, 15, 12, 19, 19, 19, 12, 19, 20, 26, 26, 25, 23, 21, 4], "deprel": ["nsubj", "aux", "advmod", "root", "obj", "case", "case", "obl", "nsubj", "acl:relcl", "mark", "xcomp", "det", "amod", "obj", "case", "det", "compound", "obl", "acl", "xcomp", "case", "compound", "cc", "conj", "obl", "punct"], "triples": [{"uid": "3634-0", "target_tags": "I\\O would\\O highly\\O recommend\\O Nina\\O 's\\O to\\O anyone\\O who\\O wants\\O to\\O have\\O a\\O romantic\\O dinner\\B in\\O a\\O heart\\O warming\\O surrounding\\O filled\\O with\\O candles\\O and\\O family\\O pictures\\O .\\O", "opinion_tags": "I\\O would\\O highly\\O recommend\\O Nina\\O 's\\O to\\O anyone\\O who\\O wants\\O to\\O have\\O a\\O romantic\\B dinner\\O in\\O a\\O heart\\O warming\\O surrounding\\O filled\\O with\\O candles\\O and\\O family\\O pictures\\O .\\O", "sentiment": "positive"}, {"uid": "3634-1", "target_tags": "I\\O would\\O highly\\O recommend\\O Nina\\O 's\\O to\\O anyone\\O who\\O wants\\O to\\O have\\O a\\O romantic\\O dinner\\O in\\O a\\O heart\\O warming\\O surrounding\\B filled\\O with\\O candles\\O and\\O family\\O pictures\\O .\\O", "opinion_tags": "I\\O would\\O highly\\O recommend\\O Nina\\O 's\\O to\\O anyone\\O who\\O wants\\O to\\O have\\O a\\O romantic\\O dinner\\O in\\O a\\O heart\\B warming\\I surrounding\\O filled\\O with\\O candles\\O and\\O family\\O pictures\\O .\\O", "sentiment": "positive"}]}, {"id": "2325", "sentence": "The selection changes frequently but the basic dishes are always available .", "postag": ["DT", "NN", "VBZ", "RB", "CC", "DT", "JJ", "NNS", "VBP", "RB", "JJ", "."], "head": [2, 3, 0, 3, 11, 8, 8, 11, 11, 11, 3, 3], "deprel": ["det", "nsubj", "root", "advmod", "cc", "det", "amod", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "2325-0", "target_tags": "The\\O selection\\B changes\\O frequently\\O but\\O the\\O basic\\O dishes\\O are\\O always\\O available\\O .\\O", "opinion_tags": "The\\O selection\\O changes\\B frequently\\I but\\O the\\O basic\\O dishes\\O are\\O always\\O available\\O .\\O", "sentiment": "neutral"}, {"uid": "2325-1", "target_tags": "The\\O selection\\O changes\\O frequently\\O but\\O the\\O basic\\B dishes\\I are\\O always\\O available\\O .\\O", "opinion_tags": "The\\O selection\\O changes\\O frequently\\O but\\O the\\O basic\\O dishes\\O are\\O always\\O available\\B .\\O", "sentiment": "neutral"}]}, {"id": "1577", "sentence": "I think the stuff was better than Disney .", "postag": ["PRP", "VBP", "DT", "NN", "VBD", "JJR", "IN", "NNP", "."], "head": [2, 0, 4, 6, 6, 2, 8, 6, 2], "deprel": ["nsubj", "root", "det", "nsubj", "cop", "ccomp", "case", "obl", "punct"], "triples": [{"uid": "1577-0", "target_tags": "I\\O think\\O the\\O stuff\\B was\\O better\\O than\\O Disney\\O .\\O", "opinion_tags": "I\\O think\\O the\\O stuff\\O was\\O better\\B than\\O Disney\\O .\\O", "sentiment": "positive"}]}, {"id": "2469", "sentence": "A glass of Leaping Lizard , a glass of prosecco , and the mussels had everything happy .", "postag": ["DT", "NN", "IN", "NN", "NN", ",", "DT", "NN", "IN", "NN", ",", "CC", "DT", "NNS", "VBD", "NN", "JJ", "."], "head": [2, 15, 5, 5, 2, 8, 8, 5, 10, 8, 14, 14, 14, 5, 0, 15, 16, 15], "deprel": ["det", "nsubj", "case", "compound", "nmod", "punct", "det", "conj", "case", "nmod", "punct", "cc", "det", "conj", "root", "obj", "amod", "punct"], "triples": [{"uid": "2469-0", "target_tags": "A\\O glass\\O of\\O Leaping\\O Lizard\\O ,\\O a\\O glass\\B of\\I prosecco\\I ,\\O and\\O the\\O mussels\\O had\\O everything\\O happy\\O .\\O", "opinion_tags": "A\\O glass\\O of\\O Leaping\\O Lizard\\O ,\\O a\\O glass\\O of\\O prosecco\\O ,\\O and\\O the\\O mussels\\O had\\O everything\\O happy\\B .\\O", "sentiment": "positive"}, {"uid": "2469-1", "target_tags": "A\\O glass\\O of\\O Leaping\\O Lizard\\O ,\\O a\\O glass\\O of\\O prosecco\\O ,\\O and\\O the\\O mussels\\B had\\O everything\\O happy\\O .\\O", "opinion_tags": "A\\O glass\\O of\\O Leaping\\O Lizard\\O ,\\O a\\O glass\\O of\\O prosecco\\O ,\\O and\\O the\\O mussels\\O had\\O everything\\O happy\\B .\\O", "sentiment": "positive"}, {"uid": "2469-2", "target_tags": "A\\O glass\\B of\\I Leaping\\I Lizard\\I ,\\O a\\O glass\\O of\\O prosecco\\O ,\\O and\\O the\\O mussels\\O had\\O everything\\O happy\\O .\\O", "opinion_tags": "A\\O glass\\O of\\O Leaping\\O Lizard\\O ,\\O a\\O glass\\O of\\O prosecco\\O ,\\O and\\O the\\O mussels\\O had\\O everything\\O happy\\B .\\O", "sentiment": "positive"}]}, {"id": "2795", "sentence": "Priced at upper intermediate range .", "postag": ["VBN", "IN", "JJ", "JJ", "NN", "."], "head": [0, 5, 5, 5, 1, 1], "deprel": ["root", "case", "amod", "amod", "obl", "punct"], "triples": [{"uid": "2795-0", "target_tags": "Priced\\B at\\O upper\\O intermediate\\O range\\O .\\O", "opinion_tags": "Priced\\O at\\O upper\\B intermediate\\I range\\O .\\O", "sentiment": "negative"}]}, {"id": "271", "sentence": "These innovators of french indian fusion do a great job of making dishes as interesting as possible while still being accessible .", "postag": ["DT", "NNS", "IN", "JJ", "JJ", "NN", "VBP", "DT", "JJ", "NN", "IN", "VBG", "NNS", "RB", "JJ", "IN", "JJ", "IN", "RB", "VBG", "JJ", "."], "head": [2, 7, 6, 6, 6, 2, 0, 10, 10, 7, 12, 10, 12, 15, 12, 17, 15, 21, 21, 21, 14, 7], "deprel": ["det", "nsubj", "case", "amod", "amod", "nmod", "root", "det", "amod", "obj", "mark", "acl", "obj", "advmod", "xcomp", "mark", "advcl", "mark", "advmod", "cop", "advcl", "punct"], "triples": [{"uid": "271-0", "target_tags": "These\\O innovators\\O of\\O french\\B indian\\I fusion\\I do\\O a\\O great\\O job\\O of\\O making\\O dishes\\O as\\O interesting\\O as\\O possible\\O while\\O still\\O being\\O accessible\\O .\\O", "opinion_tags": "These\\O innovators\\O of\\O french\\O indian\\O fusion\\O do\\O a\\O great\\B job\\O of\\O making\\O dishes\\O as\\O interesting\\O as\\O possible\\O while\\O still\\O being\\O accessible\\O .\\O", "sentiment": "positive"}, {"uid": "271-1", "target_tags": "These\\O innovators\\O of\\O french\\O indian\\O fusion\\O do\\O a\\O great\\O job\\O of\\O making\\O dishes\\B as\\O interesting\\O as\\O possible\\O while\\O still\\O being\\O accessible\\O .\\O", "opinion_tags": "These\\O innovators\\O of\\O french\\O indian\\O fusion\\O do\\O a\\O great\\O job\\O of\\O making\\O dishes\\O as\\O interesting\\B as\\O possible\\O while\\O still\\O being\\O accessible\\B .\\O", "sentiment": "positive"}]}, {"id": "665", "sentence": "I would recommend putting your name down and then getting a drink at a local bar first though because of the wait time .", "postag": ["PRP", "MD", "VB", "VBG", "PRP$", "NN", "RP", "CC", "RB", "VBG", "DT", "NN", "IN", "DT", "JJ", "NN", "RB", "RB", "IN", "IN", "DT", "NN", "NN", "."], "head": [3, 3, 0, 3, 6, 4, 4, 10, 10, 4, 12, 10, 16, 16, 16, 10, 10, 10, 23, 19, 23, 23, 10, 3], "deprel": ["nsubj", "aux", "root", "xcomp", "nmod:poss", "obj", "compound:prt", "cc", "advmod", "conj", "det", "obj", "case", "det", "amod", "obl", "advmod", "advmod", "case", "fixed", "det", "compound", "obl", "punct"], "triples": [{"uid": "665-0", "target_tags": "I\\O would\\O recommend\\O putting\\O your\\O name\\O down\\O and\\O then\\O getting\\O a\\O drink\\B at\\O a\\O local\\O bar\\O first\\O though\\O because\\O of\\O the\\O wait\\O time\\O .\\O", "opinion_tags": "I\\O would\\O recommend\\B putting\\O your\\O name\\O down\\O and\\O then\\O getting\\O a\\O drink\\O at\\O a\\O local\\O bar\\O first\\O though\\O because\\O of\\O the\\O wait\\O time\\O .\\O", "sentiment": "neutral"}, {"uid": "665-1", "target_tags": "I\\O would\\O recommend\\O putting\\O your\\O name\\O down\\O and\\O then\\O getting\\O a\\O drink\\O at\\O a\\O local\\O bar\\B first\\O though\\O because\\O of\\O the\\O wait\\O time\\O .\\O", "opinion_tags": "I\\O would\\O recommend\\B putting\\O your\\O name\\O down\\O and\\O then\\O getting\\O a\\O drink\\O at\\O a\\O local\\O bar\\O first\\O though\\O because\\O of\\O the\\O wait\\O time\\O .\\O", "sentiment": "neutral"}]}, {"id": "3425", "sentence": "We had the scallops as an appetizer and they were delicious and the sauce was wonderful .", "postag": ["PRP", "VBD", "DT", "NNS", "IN", "DT", "NN", "CC", "PRP", "VBD", "JJ", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [2, 0, 4, 2, 7, 7, 2, 11, 11, 11, 2, 16, 14, 16, 16, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "case", "det", "obl", "cc", "nsubj", "cop", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "3425-0", "target_tags": "We\\O had\\O the\\O scallops\\B as\\O an\\O appetizer\\O and\\O they\\O were\\O delicious\\O and\\O the\\O sauce\\O was\\O wonderful\\O .\\O", "opinion_tags": "We\\O had\\O the\\O scallops\\O as\\O an\\O appetizer\\O and\\O they\\O were\\O delicious\\B and\\O the\\O sauce\\O was\\O wonderful\\O .\\O", "sentiment": "positive"}, {"uid": "3425-1", "target_tags": "We\\O had\\O the\\O scallops\\O as\\O an\\O appetizer\\B and\\O they\\O were\\O delicious\\O and\\O the\\O sauce\\O was\\O wonderful\\O .\\O", "opinion_tags": "We\\O had\\O the\\O scallops\\O as\\O an\\O appetizer\\O and\\O they\\O were\\O delicious\\B and\\O the\\O sauce\\O was\\O wonderful\\O .\\O", "sentiment": "positive"}, {"uid": "3425-2", "target_tags": "We\\O had\\O the\\O scallops\\O as\\O an\\O appetizer\\O and\\O they\\O were\\O delicious\\O and\\O the\\O sauce\\B was\\O wonderful\\O .\\O", "opinion_tags": "We\\O had\\O the\\O scallops\\O as\\O an\\O appetizer\\O and\\O they\\O were\\O delicious\\O and\\O the\\O sauce\\O was\\O wonderful\\B .\\O", "sentiment": "positive"}]}, {"id": "2337", "sentence": "<PERSON><PERSON><PERSON> also offers prix fixe lunch and buffet .", "postag": ["NNP", "RB", "VBZ", "NN", "NN", "NN", "CC", "NN", "."], "head": [3, 3, 0, 5, 6, 3, 8, 6, 3], "deprel": ["nsubj", "advmod", "root", "compound", "compound", "obj", "cc", "conj", "punct"], "triples": [{"uid": "2337-0", "target_tags": "<PERSON><PERSON><PERSON>\\O also\\O offers\\O prix\\B fixe\\I lunch\\I and\\O buffet\\O .\\O", "opinion_tags": "<PERSON><PERSON><PERSON>\\O also\\O offers\\O prix\\B fixe\\O lunch\\O and\\O buffet\\O .\\O", "sentiment": "positive"}, {"uid": "2337-1", "target_tags": "<PERSON><PERSON><PERSON>\\O also\\O offers\\O prix\\O fixe\\O lunch\\O and\\O buffet\\B .\\O", "opinion_tags": "<PERSON><PERSON><PERSON>\\O also\\O offers\\O prix\\B fixe\\O lunch\\O and\\O buffet\\O .\\O", "sentiment": "positive"}]}, {"id": "2831", "sentence": "When you 're sitting in their main dining room ( which has a spectacular , hand-painted high ceiling ) you 'd never know there was a world outside .", "postag": ["WRB", "PRP", "VBP", "VBG", "IN", "PRP$", "JJ", "NN", "NN", "-LRB-", "WDT", "VBZ", "DT", "JJ", ",", "JJ", "JJ", "NN", "-RRB-", "PRP", "MD", "RB", "VB", "EX", "VBD", "DT", "NN", "RB", "."], "head": [4, 4, 4, 23, 9, 9, 9, 9, 4, 12, 12, 9, 18, 18, 18, 18, 18, 12, 12, 23, 23, 23, 0, 25, 23, 27, 25, 25, 23], "deprel": ["mark", "nsubj", "aux", "advcl", "case", "nmod:poss", "amod", "compound", "obl", "punct", "nsubj", "acl:relcl", "det", "amod", "punct", "amod", "amod", "obj", "punct", "nsubj", "aux", "advmod", "root", "expl", "ccomp", "det", "nsubj", "advmod", "punct"], "triples": [{"uid": "2831-0", "target_tags": "When\\O you\\O 're\\O sitting\\O in\\O their\\O main\\B dining\\I room\\I (\\O which\\O has\\O a\\O spectacular\\O ,\\O hand-painted\\O high\\O ceiling\\O )\\O you\\O 'd\\O never\\O know\\O there\\O was\\O a\\O world\\O outside\\O .\\O", "opinion_tags": "When\\O you\\O 're\\O sitting\\O in\\O their\\O main\\O dining\\O room\\O (\\O which\\O has\\O a\\O spectacular\\B ,\\O hand-painted\\O high\\O ceiling\\O )\\O you\\O 'd\\O never\\O know\\O there\\O was\\O a\\O world\\O outside\\O .\\O", "sentiment": "positive"}, {"uid": "2831-1", "target_tags": "When\\O you\\O 're\\O sitting\\O in\\O their\\O main\\O dining\\O room\\O (\\O which\\O has\\O a\\O spectacular\\O ,\\O hand-painted\\O high\\O ceiling\\B )\\O you\\O 'd\\O never\\O know\\O there\\O was\\O a\\O world\\O outside\\O .\\O", "opinion_tags": "When\\O you\\O 're\\O sitting\\O in\\O their\\O main\\O dining\\O room\\O (\\O which\\O has\\O a\\O spectacular\\B ,\\O hand-painted\\B high\\B ceiling\\O )\\O you\\O 'd\\O never\\O know\\O there\\O was\\O a\\O world\\O outside\\O .\\O", "sentiment": "positive"}]}, {"id": "599", "sentence": "Best Italian food I ever had ( and being Italian , that means alot ) .", "postag": ["JJS", "JJ", "NN", "PRP", "RB", "VBD", "-LRB-", "CC", "VBG", "JJ", ",", "DT", "VBZ", "NN", "-RRB-", "."], "head": [3, 3, 0, 6, 6, 3, 3, 10, 10, 13, 13, 13, 3, 13, 13, 3], "deprel": ["amod", "amod", "root", "nsubj", "advmod", "acl:relcl", "punct", "cc", "cop", "advcl", "punct", "nsubj", "parataxis", "obj", "punct", "punct"], "triples": [{"uid": "599-0", "target_tags": "Best\\O Italian\\B food\\I I\\O ever\\O had\\O (\\O and\\O being\\O Italian\\O ,\\O that\\O means\\O alot\\O )\\O .\\O", "opinion_tags": "Best\\B Italian\\O food\\O I\\O ever\\O had\\O (\\O and\\O being\\O Italian\\O ,\\O that\\O means\\O alot\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "2811", "sentence": "Consistently good Japanese Tapas .", "postag": ["RB", "JJ", "JJ", "NN", "."], "head": [2, 4, 4, 0, 4], "deprel": ["advmod", "amod", "amod", "root", "punct"], "triples": [{"uid": "2811-0", "target_tags": "Consistently\\O good\\O Japanese\\B Tapas\\I .\\O", "opinion_tags": "Consistently\\O good\\B Japanese\\O Tapas\\O .\\O", "sentiment": "positive"}]}, {"id": "438", "sentence": "They pray to their Food Gods to make them into a good pizza like VT 's .", "postag": ["PRP", "VBP", "IN", "PRP$", "NN", "NNS", "TO", "VB", "PRP", "IN", "DT", "JJ", "NN", "IN", "NNP", "POS", "."], "head": [2, 0, 6, 6, 6, 2, 8, 2, 8, 13, 13, 13, 8, 15, 13, 15, 2], "deprel": ["nsubj", "root", "case", "nmod:poss", "compound", "obl", "mark", "advcl", "obj", "case", "det", "amod", "obl", "case", "nmod", "case", "punct"], "triples": [{"uid": "438-0", "target_tags": "They\\O pray\\O to\\O their\\O Food\\O Gods\\O to\\O make\\O them\\O into\\O a\\O good\\O pizza\\B like\\O VT\\O 's\\O .\\O", "opinion_tags": "They\\O pray\\O to\\O their\\O Food\\O Gods\\O to\\O make\\O them\\O into\\O a\\O good\\B pizza\\O like\\O VT\\O 's\\O .\\O", "sentiment": "negative"}]}, {"id": "2698", "sentence": "We were also seated promptly at the time of our reservation and the service was very quick and professional .", "postag": ["PRP", "VBD", "RB", "VBN", "RB", "IN", "DT", "NN", "IN", "PRP$", "NN", "CC", "DT", "NN", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [4, 4, 4, 0, 4, 8, 8, 4, 11, 11, 8, 17, 14, 17, 17, 17, 4, 19, 17, 4], "deprel": ["nsubj:pass", "aux:pass", "advmod", "root", "advmod", "case", "det", "obl", "case", "nmod:poss", "nmod", "cc", "det", "nsubj", "cop", "advmod", "conj", "cc", "conj", "punct"], "triples": [{"uid": "2698-0", "target_tags": "We\\O were\\O also\\O seated\\O promptly\\O at\\O the\\O time\\O of\\O our\\O reservation\\O and\\O the\\O service\\B was\\O very\\O quick\\O and\\O professional\\O .\\O", "opinion_tags": "We\\O were\\O also\\O seated\\O promptly\\O at\\O the\\O time\\O of\\O our\\O reservation\\O and\\O the\\O service\\O was\\O very\\O quick\\B and\\O professional\\B .\\O", "sentiment": "positive"}]}, {"id": "1344", "sentence": "A touch more jalapeno heat for contrast and it would have been very good indeed .", "postag": ["DT", "NN", "JJR", "NN", "NN", "IN", "NN", "CC", "PRP", "MD", "VB", "VBN", "RB", "JJ", "RB", "."], "head": [2, 0, 5, 5, 2, 7, 5, 14, 14, 14, 14, 14, 14, 2, 14, 2], "deprel": ["det", "root", "amod", "compound", "conj", "case", "nmod", "cc", "nsubj", "aux", "aux", "cop", "advmod", "conj", "advmod", "punct"], "triples": [{"uid": "1344-0", "target_tags": "A\\O touch\\O more\\O jalapeno\\B heat\\O for\\O contrast\\O and\\O it\\O would\\O have\\O been\\O very\\O good\\O indeed\\O .\\O", "opinion_tags": "A\\O touch\\O more\\O jalapeno\\O heat\\O for\\O contrast\\O and\\O it\\O would\\O have\\O been\\O very\\O good\\B indeed\\O .\\O", "sentiment": "positive"}]}, {"id": "3268", "sentence": "The ambience is very calm and quiet .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"], "triples": [{"uid": "3268-0", "target_tags": "The\\O ambience\\B is\\O very\\O calm\\O and\\O quiet\\O .\\O", "opinion_tags": "The\\O ambience\\O is\\O very\\O calm\\B and\\O quiet\\O .\\O", "sentiment": "positive"}]}, {"id": "208", "sentence": "The specials are usually quite good too .", "postag": ["DT", "NNS", "VBP", "RB", "RB", "JJ", "RB", "."], "head": [2, 6, 6, 6, 6, 0, 6, 6], "deprel": ["det", "nsubj", "cop", "advmod", "advmod", "root", "advmod", "punct"], "triples": [{"uid": "208-0", "target_tags": "The\\O specials\\B are\\O usually\\O quite\\O good\\O too\\O .\\O", "opinion_tags": "The\\O specials\\O are\\O usually\\O quite\\O good\\B too\\O .\\O", "sentiment": "positive"}]}, {"id": "2377", "sentence": "The food was just awful , ATROCIOUS actually .", "postag": ["DT", "NN", "VBD", "RB", "JJ", ",", "JJ", "RB", "."], "head": [2, 5, 5, 5, 0, 5, 5, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "conj", "advmod", "punct"], "triples": [{"uid": "2377-0", "target_tags": "The\\O food\\B was\\O just\\O awful\\O ,\\O ATROCIOUS\\O actually\\O .\\O", "opinion_tags": "The\\O food\\O was\\O just\\O awful\\B ,\\O ATROCIOUS\\B actually\\O .\\O", "sentiment": "negative"}]}, {"id": "2040", "sentence": "The makhani was OK -- the korma was bland .", "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4, 7, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "parataxis", "punct"], "triples": [{"uid": "2040-0", "target_tags": "The\\O makhani\\B was\\O OK\\O --\\O the\\O korma\\O was\\O bland\\O .\\O", "opinion_tags": "The\\O makhani\\O was\\O OK\\B --\\O the\\O korma\\O was\\O bland\\O .\\O", "sentiment": "neutral"}, {"uid": "2040-1", "target_tags": "The\\O makhani\\O was\\O OK\\O --\\O the\\O korma\\B was\\O bland\\O .\\O", "opinion_tags": "The\\O makhani\\O was\\O OK\\O --\\O the\\O korma\\O was\\O bland\\B .\\O", "sentiment": "negative"}]}, {"id": "2915", "sentence": "not the food , not the ambiance , not the service , I agree with the previous reviews you wait and wait , the wait staff are very rude and when you get in they are looking to get you right out .", "postag": ["RB", "DT", "NN", ",", "RB", "DT", "NN", ",", "RB", "DT", "NN", ",", "PRP", "VBP", "IN", "DT", "JJ", "NNS", "PRP", "VBP", "CC", "VBP", ",", "DT", "NN", "NN", "VBP", "RB", "JJ", "CC", "WRB", "PRP", "VBP", "IN", "PRP", "VBP", "VBG", "TO", "VB", "PRP", "RB", "RP", "."], "head": [3, 3, 0, 3, 7, 7, 3, 11, 11, 11, 3, 3, 14, 3, 18, 18, 18, 14, 20, 18, 22, 20, 3, 26, 26, 29, 29, 29, 3, 37, 33, 33, 37, 33, 37, 37, 29, 39, 37, 39, 42, 39, 3], "deprel": ["advmod", "det", "root", "punct", "advmod", "det", "conj", "punct", "advmod", "det", "conj", "punct", "nsubj", "parataxis", "case", "det", "amod", "obl", "nsubj", "acl:relcl", "cc", "conj", "punct", "det", "compound", "nsubj", "cop", "advmod", "parataxis", "cc", "mark", "nsubj", "advcl", "advmod", "nsubj", "aux", "conj", "mark", "xcomp", "obj", "advmod", "compound:prt", "punct"], "triples": [{"uid": "2915-0", "target_tags": "not\\O the\\O food\\O ,\\O not\\O the\\O ambiance\\O ,\\O not\\O the\\O service\\O ,\\O I\\O agree\\O with\\O the\\O previous\\O reviews\\O you\\O wait\\O and\\O wait\\O ,\\O the\\O wait\\B staff\\I are\\O very\\O rude\\O and\\O when\\O you\\O get\\O in\\O they\\O are\\O looking\\O to\\O get\\O you\\O right\\O out\\O .\\O", "opinion_tags": "not\\O the\\O food\\O ,\\O not\\O the\\O ambiance\\O ,\\O not\\O the\\O service\\O ,\\O I\\O agree\\O with\\O the\\O previous\\O reviews\\O you\\O wait\\O and\\O wait\\O ,\\O the\\O wait\\O staff\\O are\\O very\\O rude\\B and\\O when\\O you\\O get\\O in\\O they\\O are\\O looking\\O to\\O get\\O you\\O right\\O out\\O .\\O", "sentiment": "negative"}]}, {"id": "1664", "sentence": "Service was slow had to wait to order and get food although not crowded .", "postag": ["NN", "VBD", "JJ", "VBD", "TO", "VB", "TO", "VB", "CC", "VB", "NN", "IN", "RB", "JJ", "."], "head": [3, 3, 4, 0, 6, 4, 8, 6, 10, 8, 10, 14, 14, 10, 4], "deprel": ["nsubj", "cop", "advmod", "root", "mark", "xcomp", "mark", "advcl", "cc", "conj", "obj", "mark", "advmod", "advcl", "punct"], "triples": [{"uid": "1664-0", "target_tags": "Service\\B was\\O slow\\O had\\O to\\O wait\\O to\\O order\\O and\\O get\\O food\\O although\\O not\\O crowded\\O .\\O", "opinion_tags": "Service\\O was\\O slow\\B had\\O to\\O wait\\O to\\O order\\O and\\O get\\O food\\O although\\O not\\O crowded\\O .\\O", "sentiment": "negative"}]}, {"id": "2359", "sentence": "The $ 300 bill was a bit steep , but the experience was great .", "postag": ["DT", "$", "CD", "NN", "VBD", "DT", "NN", "JJ", ",", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [4, 4, 2, 8, 8, 7, 8, 0, 14, 14, 12, 14, 14, 8, 8], "deprel": ["det", "compound", "nummod", "nsubj", "cop", "det", "obl:npmod", "root", "punct", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "2359-0", "target_tags": "The\\O $\\O 300\\O bill\\B was\\O a\\O bit\\O steep\\O ,\\O but\\O the\\O experience\\O was\\O great\\O .\\O", "opinion_tags": "The\\O $\\O 300\\O bill\\O was\\O a\\O bit\\O steep\\B ,\\O but\\O the\\O experience\\O was\\O great\\O .\\O", "sentiment": "negative"}]}, {"id": "983", "sentence": "both are very reasonably priced ( around $ 8 for dinner and $ 5 for lunch ) , and are delicious and filling .", "postag": ["DT", "VBP", "RB", "RB", "VBN", "-LRB-", "RB", "$", "CD", "IN", "NN", "CC", "$", "CD", "IN", "NN", "-RRB-", ",", "CC", "VBP", "JJ", "CC", "VBG", "."], "head": [5, 5, 4, 5, 0, 8, 8, 5, 8, 11, 8, 13, 8, 13, 16, 13, 8, 21, 21, 21, 5, 23, 21, 5], "deprel": ["nsubj:pass", "aux:pass", "advmod", "advmod", "root", "punct", "advmod", "parataxis", "nummod", "case", "nmod", "cc", "conj", "nummod", "case", "nmod", "punct", "punct", "cc", "cop", "conj", "cc", "conj", "punct"], "triples": [{"uid": "983-0", "target_tags": "both\\O are\\O very\\O reasonably\\O priced\\B (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\O and\\O filling\\O .\\O", "opinion_tags": "both\\O are\\O very\\O reasonably\\B priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\O and\\O filling\\O .\\O", "sentiment": "positive"}, {"uid": "983-1", "target_tags": "both\\O are\\O very\\O reasonably\\O priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\B and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\O and\\O filling\\O .\\O", "opinion_tags": "both\\O are\\O very\\O reasonably\\O priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\B and\\O filling\\B .\\O", "sentiment": "positive"}, {"uid": "983-2", "target_tags": "both\\O are\\O very\\O reasonably\\O priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\B )\\O ,\\O and\\O are\\O delicious\\O and\\O filling\\O .\\O", "opinion_tags": "both\\O are\\O very\\O reasonably\\O priced\\O (\\O around\\O $\\O 8\\O for\\O dinner\\O and\\O $\\O 5\\O for\\O lunch\\O )\\O ,\\O and\\O are\\O delicious\\B and\\O filling\\B .\\O", "sentiment": "positive"}]}, {"id": "2272", "sentence": "They offer the same menu but have creative drinks that are loaded with alcohol and cheeky names -- but they do cost you .", "postag": ["PRP", "VBP", "DT", "JJ", "NN", "CC", "VBP", "JJ", "NNS", "WDT", "VBP", "VBN", "IN", "NN", "CC", "JJ", "NNS", ",", "CC", "PRP", "VBP", "VB", "PRP", "."], "head": [2, 0, 5, 5, 2, 7, 2, 9, 7, 12, 12, 9, 14, 12, 17, 17, 14, 2, 22, 22, 22, 2, 22, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "cc", "conj", "amod", "obj", "nsubj:pass", "aux:pass", "acl:relcl", "case", "obl", "cc", "amod", "conj", "punct", "cc", "nsubj", "aux", "conj", "obj", "punct"], "triples": [{"uid": "2272-0", "target_tags": "They\\O offer\\O the\\O same\\O menu\\B but\\O have\\O creative\\O drinks\\O that\\O are\\O loaded\\O with\\O alcohol\\O and\\O cheeky\\O names\\O --\\O but\\O they\\O do\\O cost\\O you\\O .\\O", "opinion_tags": "They\\O offer\\O the\\O same\\B menu\\O but\\O have\\O creative\\O drinks\\O that\\O are\\O loaded\\O with\\O alcohol\\O and\\O cheeky\\O names\\O --\\O but\\O they\\O do\\O cost\\O you\\O .\\O", "sentiment": "neutral"}, {"uid": "2272-1", "target_tags": "They\\O offer\\O the\\O same\\O menu\\O but\\O have\\O creative\\O drinks\\B that\\O are\\O loaded\\O with\\O alcohol\\O and\\O cheeky\\O names\\O --\\O but\\O they\\O do\\O cost\\O you\\O .\\O", "opinion_tags": "They\\O offer\\O the\\O same\\O menu\\O but\\O have\\O creative\\B drinks\\O that\\O are\\O loaded\\O with\\O alcohol\\O and\\O cheeky\\O names\\O --\\O but\\O they\\O do\\O cost\\O you\\O .\\O", "sentiment": "positive"}]}, {"id": "2243", "sentence": "Its location is good and the fact that Hutner College is near and their prices are very reasonable , makes students go back to Suan again and again .", "postag": ["PRP$", "NN", "VBZ", "JJ", "CC", "DT", "NN", "IN", "NNP", "NNP", "VBZ", "JJ", "CC", "PRP$", "NNS", "VBP", "RB", "JJ", ",", "VBZ", "NNS", "VB", "RB", "IN", "NNP", "RB", "CC", "RB", "."], "head": [2, 4, 4, 0, 20, 7, 4, 12, 10, 12, 12, 7, 18, 15, 18, 18, 18, 4, 20, 4, 20, 20, 22, 25, 23, 22, 28, 26, 4], "deprel": ["nmod:poss", "nsubj", "cop", "root", "cc", "det", "conj", "mark", "compound", "nsubj", "cop", "acl", "cc", "nmod:poss", "nsubj", "cop", "advmod", "conj", "punct", "conj", "obj", "xcomp", "advmod", "case", "obl", "advmod", "cc", "conj", "punct"], "triples": [{"uid": "2243-0", "target_tags": "Its\\O location\\B is\\O good\\O and\\O the\\O fact\\O that\\O Hutner\\O College\\O is\\O near\\O and\\O their\\O prices\\O are\\O very\\O reasonable\\O ,\\O makes\\O students\\O go\\O back\\O to\\O Suan\\O again\\O and\\O again\\O .\\O", "opinion_tags": "Its\\O location\\O is\\O good\\B and\\O the\\O fact\\O that\\O Hutner\\O College\\O is\\O near\\O and\\O their\\O prices\\O are\\O very\\O reasonable\\O ,\\O makes\\O students\\O go\\O back\\O to\\O Suan\\O again\\O and\\O again\\O .\\O", "sentiment": "positive"}, {"uid": "2243-1", "target_tags": "Its\\O location\\O is\\O good\\O and\\O the\\O fact\\O that\\O Hutner\\O College\\O is\\O near\\O and\\O their\\O prices\\B are\\O very\\O reasonable\\O ,\\O makes\\O students\\O go\\O back\\O to\\O Suan\\O again\\O and\\O again\\O .\\O", "opinion_tags": "Its\\O location\\O is\\O good\\O and\\O the\\O fact\\O that\\O Hutner\\O College\\O is\\O near\\O and\\O their\\O prices\\O are\\O very\\O reasonable\\B ,\\O makes\\O students\\O go\\O back\\O to\\O Suan\\O again\\O and\\O again\\O .\\O", "sentiment": "positive"}]}, {"id": "353", "sentence": "Their Margarita is best I 've had since I 've returned from Naples !", "postag": ["PRP$", "NNP", "VBZ", "JJS", "PRP", "VBP", "VBN", "IN", "PRP", "VBP", "VBN", "IN", "NNP", "."], "head": [2, 4, 4, 0, 7, 7, 4, 11, 11, 11, 7, 13, 11, 4], "deprel": ["nmod:poss", "nsubj", "cop", "root", "nsubj", "aux", "acl:relcl", "mark", "nsubj", "aux", "advcl", "case", "obl", "punct"], "triples": [{"uid": "353-0", "target_tags": "Their\\O Margarita\\B is\\O best\\O I\\O 've\\O had\\O since\\O I\\O 've\\O returned\\O from\\O Naples\\O !\\O", "opinion_tags": "Their\\O Margarita\\O is\\O best\\B I\\O 've\\O had\\O since\\O I\\O 've\\O returned\\O from\\O Naples\\O !\\O", "sentiment": "positive"}]}, {"id": "1808", "sentence": "however , it 's the service that leaves a bad taste in my mouth .", "postag": ["RB", ",", "PRP", "VBZ", "DT", "NN", "WDT", "VBZ", "DT", "JJ", "NN", "IN", "PRP$", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 8, 6, 11, 11, 8, 14, 14, 8, 6], "deprel": ["advmod", "punct", "nsubj", "cop", "det", "root", "nsubj", "acl:relcl", "det", "amod", "obj", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "1808-0", "target_tags": "however\\O ,\\O it\\O 's\\O the\\O service\\B that\\O leaves\\O a\\O bad\\O taste\\O in\\O my\\O mouth\\O .\\O", "opinion_tags": "however\\O ,\\O it\\O 's\\O the\\O service\\O that\\O leaves\\O a\\O bad\\B taste\\I in\\O my\\O mouth\\O .\\O", "sentiment": "negative"}]}, {"id": "1304", "sentence": "We actually gave 10 % tip ( which we have never done despite mediocre food and service ) , because we felt totally ripped off .", "postag": ["PRP", "RB", "VBD", "CD", "NN", "NN", "-LRB-", "WDT", "PRP", "VBP", "RB", "VBN", "IN", "JJ", "NN", "CC", "NN", "-RRB-", ",", "IN", "PRP", "VBD", "RB", "VBN", "RP", "."], "head": [3, 3, 0, 5, 6, 3, 6, 12, 12, 12, 12, 6, 15, 15, 12, 17, 15, 12, 3, 22, 22, 3, 24, 22, 24, 3], "deprel": ["nsubj", "advmod", "root", "nummod", "compound", "obj", "punct", "obj", "nsubj", "aux", "advmod", "acl:relcl", "case", "amod", "obl", "cc", "conj", "punct", "punct", "mark", "nsubj", "advcl", "advmod", "xcomp", "compound:prt", "punct"], "triples": [{"uid": "1304-0", "target_tags": "We\\O actually\\O gave\\O 10\\O %\\O tip\\O (\\O which\\O we\\O have\\O never\\O done\\O despite\\O mediocre\\O food\\B and\\O service\\O )\\O ,\\O because\\O we\\O felt\\O totally\\O ripped\\O off\\O .\\O", "opinion_tags": "We\\O actually\\O gave\\O 10\\O %\\O tip\\O (\\O which\\O we\\O have\\O never\\O done\\O despite\\O mediocre\\B food\\O and\\O service\\O )\\O ,\\O because\\O we\\O felt\\O totally\\O ripped\\O off\\O .\\O", "sentiment": "neutral"}, {"uid": "1304-1", "target_tags": "We\\O actually\\O gave\\O 10\\O %\\O tip\\O (\\O which\\O we\\O have\\O never\\O done\\O despite\\O mediocre\\O food\\O and\\O service\\B )\\O ,\\O because\\O we\\O felt\\O totally\\O ripped\\O off\\O .\\O", "opinion_tags": "We\\O actually\\O gave\\O 10\\O %\\O tip\\O (\\O which\\O we\\O have\\O never\\O done\\O despite\\O mediocre\\B food\\O and\\O service\\O )\\O ,\\O because\\O we\\O felt\\O totally\\O ripped\\O off\\O .\\O", "sentiment": "neutral"}]}, {"id": "191", "sentence": "Oh yes , and they lie on the phone , claiming they have seating in the garden , then of course the seats are not available .", "postag": ["UH", "UH", ",", "CC", "PRP", "VBP", "IN", "DT", "NN", ",", "VBG", "PRP", "VBP", "VBG", "IN", "DT", "NN", ",", "RB", "RB", "RB", "DT", "NNS", "VBP", "RB", "JJ", "."], "head": [6, 6, 6, 6, 6, 26, 9, 9, 6, 11, 6, 13, 11, 13, 17, 17, 14, 26, 26, 26, 20, 23, 26, 26, 26, 0, 26], "deprel": ["discourse", "discourse", "punct", "cc", "nsubj", "advcl", "case", "det", "obl", "punct", "advcl", "nsubj", "ccomp", "obj", "case", "det", "obl", "punct", "advmod", "advmod", "fixed", "det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "191-0", "target_tags": "Oh\\O yes\\O ,\\O and\\O they\\O lie\\O on\\O the\\O phone\\O ,\\O claiming\\O they\\O have\\O seating\\B in\\I the\\I garden\\I ,\\O then\\O of\\O course\\O the\\O seats\\O are\\O not\\O available\\O .\\O", "opinion_tags": "Oh\\O yes\\O ,\\O and\\O they\\O lie\\B on\\O the\\O phone\\O ,\\O claiming\\O they\\O have\\O seating\\O in\\O the\\O garden\\O ,\\O then\\O of\\O course\\O the\\O seats\\O are\\O not\\O available\\O .\\O", "sentiment": "neutral"}, {"uid": "191-1", "target_tags": "Oh\\O yes\\O ,\\O and\\O they\\O lie\\O on\\O the\\O phone\\O ,\\O claiming\\O they\\O have\\O seating\\O in\\O the\\O garden\\O ,\\O then\\O of\\O course\\O the\\O seats\\B are\\O not\\O available\\O .\\O", "opinion_tags": "Oh\\O yes\\O ,\\O and\\O they\\O lie\\O on\\O the\\O phone\\O ,\\O claiming\\O they\\O have\\O seating\\O in\\O the\\O garden\\O ,\\O then\\O of\\O course\\O the\\O seats\\O are\\O not\\B available\\I .\\O", "sentiment": "neutral"}]}, {"id": "1483", "sentence": "They 're also friendlier here , especially the owner , <PERSON> .", "postag": ["PRP", "VBP", "RB", "JJR", "RB", ",", "RB", "DT", "NN", ",", "NNP", "."], "head": [4, 4, 4, 0, 4, 9, 9, 9, 4, 9, 9, 4], "deprel": ["nsubj", "cop", "advmod", "root", "advmod", "punct", "advmod", "det", "parataxis", "punct", "appos", "punct"], "triples": [{"uid": "1483-0", "target_tags": "They\\O 're\\O also\\O friendlier\\O here\\O ,\\O especially\\O the\\O owner\\B ,\\O Kenny\\O .\\O", "opinion_tags": "They\\O 're\\O also\\O friendlier\\B here\\O ,\\O especially\\O the\\O owner\\O ,\\O Kenny\\O .\\O", "sentiment": "positive"}]}, {"id": "2454", "sentence": "I love the atmorphere @ peep !", "postag": ["PRP", "VBP", "DT", "NN", "IN", "NN", "."], "head": [2, 0, 4, 2, 6, 4, 2], "deprel": ["nsubj", "root", "det", "obj", "case", "nmod", "punct"], "triples": [{"uid": "2454-0", "target_tags": "I\\O love\\O the\\O atmorphere\\B @\\O peep\\O !\\O", "opinion_tags": "I\\O love\\B the\\O atmorphere\\O @\\O peep\\O !\\O", "sentiment": "positive"}]}, {"id": "2212", "sentence": "If you want good authentic Thai this place is not the place to go .", "postag": ["IN", "PRP", "VBP", "JJ", "JJ", "NNP", "DT", "NN", "VBZ", "RB", "DT", "NN", "TO", "VB", "."], "head": [3, 3, 12, 6, 6, 3, 8, 12, 12, 12, 12, 0, 14, 12, 12], "deprel": ["mark", "nsubj", "advcl", "amod", "amod", "obj", "det", "nsubj", "cop", "advmod", "det", "root", "mark", "acl", "punct"], "triples": [{"uid": "2212-0", "target_tags": "If\\O you\\O want\\O good\\O authentic\\O Thai\\B this\\O place\\O is\\O not\\O the\\O place\\O to\\O go\\O .\\O", "opinion_tags": "If\\O you\\O want\\O good\\B authentic\\B Thai\\O this\\O place\\O is\\O not\\O the\\O place\\O to\\O go\\O .\\O", "sentiment": "negative"}]}, {"id": "305", "sentence": "The sushi is also great !", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "305-0", "target_tags": "The\\O sushi\\B is\\O also\\O great\\O !\\O", "opinion_tags": "The\\O sushi\\O is\\O also\\O great\\B !\\O", "sentiment": "positive"}]}, {"id": "588", "sentence": "The service was excellent , the food was excellent , but the entire experience was very cool .", "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "JJ", ",", "CC", "DT", "JJ", "NN", "VBD", "RB", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 17, 17, 14, 14, 17, 17, 17, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "conj", "punct", "cc", "det", "amod", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "588-0", "target_tags": "The\\O service\\B was\\O excellent\\O ,\\O the\\O food\\O was\\O excellent\\O ,\\O but\\O the\\O entire\\O experience\\O was\\O very\\O cool\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\B ,\\O the\\O food\\O was\\O excellent\\O ,\\O but\\O the\\O entire\\O experience\\O was\\O very\\O cool\\O .\\O", "sentiment": "positive"}, {"uid": "588-1", "target_tags": "The\\O service\\O was\\O excellent\\O ,\\O the\\O food\\B was\\O excellent\\O ,\\O but\\O the\\O entire\\O experience\\O was\\O very\\O cool\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\O ,\\O the\\O food\\O was\\O excellent\\B ,\\O but\\O the\\O entire\\O experience\\O was\\O very\\O cool\\O .\\O", "sentiment": "positive"}]}, {"id": "3405", "sentence": "I really loved the different and inovated touch that 's the cheff gives to the food .", "postag": ["PRP", "RB", "VBD", "DT", "JJ", "CC", "JJ", "NN", "WDT", "VBZ", "DT", "NN", "VBZ", "IN", "DT", "NN", "."], "head": [3, 3, 0, 8, 8, 7, 5, 3, 13, 13, 12, 13, 8, 16, 16, 13, 3], "deprel": ["nsubj", "advmod", "root", "det", "amod", "cc", "conj", "obj", "obj", "aux", "det", "nsubj", "acl:relcl", "case", "det", "obl", "punct"], "triples": [{"uid": "3405-0", "target_tags": "I\\O really\\O loved\\O the\\O different\\O and\\O inovated\\O touch\\O that\\O 's\\O the\\O cheff\\O gives\\O to\\O the\\O food\\B .\\O", "opinion_tags": "I\\O really\\O loved\\O the\\O different\\B and\\O inovated\\B touch\\O that\\O 's\\O the\\O cheff\\O gives\\O to\\O the\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "2592", "sentence": "We went here for lunch a couple of weeks ago on a Saturday , and I was thoroughly impressed with the food .", "postag": ["PRP", "VBD", "RB", "IN", "NN", "DT", "NN", "IN", "NNS", "RB", "IN", "DT", "NNP", ",", "CC", "PRP", "VBD", "RB", "JJ", "IN", "DT", "NN", "."], "head": [2, 0, 2, 5, 2, 7, 10, 9, 7, 2, 13, 13, 2, 19, 19, 19, 19, 19, 2, 22, 22, 19, 2], "deprel": ["nsubj", "root", "advmod", "case", "obl", "det", "obl:npmod", "case", "nmod", "advmod", "case", "det", "obl", "punct", "cc", "nsubj", "cop", "advmod", "conj", "case", "det", "obl", "punct"], "triples": [{"uid": "2592-0", "target_tags": "We\\O went\\O here\\O for\\O lunch\\O a\\O couple\\O of\\O weeks\\O ago\\O on\\O a\\O Saturday\\O ,\\O and\\O I\\O was\\O thoroughly\\O impressed\\O with\\O the\\O food\\B .\\O", "opinion_tags": "We\\O went\\O here\\O for\\O lunch\\O a\\O couple\\O of\\O weeks\\O ago\\O on\\O a\\O Saturday\\O ,\\O and\\O I\\O was\\O thoroughly\\O impressed\\B with\\O the\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "3510", "sentence": "I was here a few weeks back and we had the worst customer service experience at a restaurant ever .", "postag": ["PRP", "VBD", "RB", "DT", "JJ", "NNS", "RB", "CC", "PRP", "VBD", "DT", "JJS", "NN", "NN", "NN", "IN", "DT", "NN", "RB", "."], "head": [3, 3, 0, 6, 6, 7, 3, 10, 10, 3, 15, 15, 14, 15, 10, 18, 18, 15, 18, 3], "deprel": ["nsubj", "cop", "root", "det", "amod", "obl:npmod", "advmod", "cc", "nsubj", "conj", "det", "amod", "compound", "compound", "obj", "case", "det", "nmod", "advmod", "punct"], "triples": [{"uid": "3510-0", "target_tags": "I\\O was\\O here\\O a\\O few\\O weeks\\O back\\O and\\O we\\O had\\O the\\O worst\\O customer\\B service\\I experience\\O at\\O a\\O restaurant\\O ever\\O .\\O", "opinion_tags": "I\\O was\\O here\\O a\\O few\\O weeks\\O back\\O and\\O we\\O had\\O the\\O worst\\B customer\\O service\\O experience\\O at\\O a\\O restaurant\\O ever\\O .\\O", "sentiment": "negative"}]}, {"id": "614", "sentence": "It is a lot of fun with live entertainment and all kinds of Disney type special effects .", "postag": ["PRP", "VBZ", "DT", "NN", "IN", "NN", "IN", "JJ", "NN", "CC", "DT", "NNS", "IN", "NNP", "NN", "JJ", "NNS", "."], "head": [4, 4, 4, 0, 6, 4, 9, 9, 4, 12, 12, 4, 17, 15, 17, 17, 12, 4], "deprel": ["nsubj", "cop", "det", "root", "case", "nmod", "case", "amod", "nmod", "cc", "det", "conj", "case", "compound", "compound", "amod", "nmod", "punct"], "triples": [{"uid": "614-0", "target_tags": "It\\O is\\O a\\O lot\\O of\\O fun\\O with\\O live\\B entertainment\\I and\\O all\\O kinds\\O of\\O Disney\\O type\\O special\\O effects\\O .\\O", "opinion_tags": "It\\O is\\O a\\O lot\\O of\\O fun\\B with\\O live\\O entertainment\\O and\\O all\\O kinds\\O of\\O Disney\\O type\\O special\\O effects\\O .\\O", "sentiment": "positive"}, {"uid": "614-1", "target_tags": "It\\O is\\O a\\O lot\\O of\\O fun\\O with\\O live\\O entertainment\\O and\\O all\\O kinds\\O of\\O Disney\\O type\\O special\\B effects\\I .\\O", "opinion_tags": "It\\O is\\O a\\O lot\\O of\\O fun\\B with\\O live\\O entertainment\\O and\\O all\\O kinds\\O of\\O Disney\\O type\\O special\\O effects\\O .\\O", "sentiment": "positive"}]}, {"id": "1830", "sentence": "Even after getting pushed out by the no-class Famous <PERSON> , <PERSON> has risen again to carry on his father 's uncle 's legacies with a smile , true love for his community , and let 's not forget the Outstanding Pizza !", "postag": ["RB", "IN", "VBG", "VBN", "RP", "IN", "DT", "JJ", "NNP", "NNP", "POS", ",", "NNP", "VBZ", "VBN", "RB", "TO", "VB", "IN", "PRP$", "NN", "POS", "NN", "POS", "NNS", "IN", "DT", "NN", ",", "JJ", "NN", "IN", "PRP$", "NN", ",", "CC", "VB", "PRP", "RB", "VB", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 15, 4, 10, 10, 10, 10, 4, 10, 4, 15, 15, 0, 15, 18, 15, 25, 21, 23, 21, 25, 21, 18, 28, 28, 25, 31, 31, 28, 34, 34, 31, 37, 37, 18, 37, 40, 37, 43, 43, 40, 15], "deprel": ["advmod", "mark", "aux:pass", "advcl", "compound:prt", "case", "det", "amod", "compound", "obl", "case", "punct", "nsubj", "aux", "root", "advmod", "mark", "xcomp", "case", "nmod:poss", "nmod:poss", "case", "nmod:poss", "case", "obl", "case", "det", "nmod", "punct", "amod", "conj", "case", "nmod:poss", "nmod", "punct", "cc", "conj", "obj", "advmod", "xcomp", "det", "amod", "obj", "punct"], "triples": [{"uid": "1830-0", "target_tags": "Even\\O after\\O getting\\O pushed\\O out\\O by\\O the\\O no-class\\O Famous\\O Ray\\O 's\\O ,\\O Sal\\O has\\O risen\\O again\\O to\\O carry\\O on\\O his\\O father\\O 's\\O uncle\\O 's\\O legacies\\O with\\O a\\O smile\\O ,\\O true\\O love\\O for\\O his\\O community\\O ,\\O and\\O let\\O 's\\O not\\O forget\\O the\\O Outstanding\\O Pizza\\B !\\O", "opinion_tags": "Even\\O after\\O getting\\O pushed\\O out\\O by\\O the\\O no-class\\O Famous\\O Ray\\O 's\\O ,\\O Sal\\O has\\O risen\\O again\\O to\\O carry\\O on\\O his\\O father\\O 's\\O uncle\\O 's\\O legacies\\O with\\O a\\O smile\\O ,\\O true\\O love\\O for\\O his\\O community\\O ,\\O and\\O let\\O 's\\O not\\O forget\\O the\\O Outstanding\\B Pizza\\O !\\O", "sentiment": "positive"}]}, {"id": "141", "sentence": "This dish is my favorite and I always get it when I go there and never get tired of it .", "postag": ["DT", "NN", "VBZ", "PRP$", "NN", "CC", "PRP", "RB", "VBP", "PRP", "WRB", "PRP", "VBP", "RB", "CC", "RB", "VBP", "JJ", "IN", "PRP", "."], "head": [2, 5, 5, 5, 0, 9, 9, 9, 5, 9, 13, 13, 9, 13, 17, 17, 9, 17, 20, 18, 5], "deprel": ["det", "nsubj", "cop", "nmod:poss", "root", "cc", "nsubj", "advmod", "conj", "obj", "mark", "nsubj", "advcl", "advmod", "cc", "advmod", "conj", "xcomp", "case", "obl", "punct"], "triples": [{"uid": "141-0", "target_tags": "This\\O dish\\B is\\O my\\O favorite\\O and\\O I\\O always\\O get\\O it\\O when\\O I\\O go\\O there\\O and\\O never\\O get\\O tired\\O of\\O it\\O .\\O", "opinion_tags": "This\\O dish\\O is\\O my\\O favorite\\B and\\O I\\O always\\O get\\O it\\O when\\O I\\O go\\O there\\O and\\O never\\O get\\O tired\\O of\\O it\\O .\\O", "sentiment": "positive"}]}, {"id": "3686", "sentence": "Eating in , the atmosphere saves it , but at your desk , it 's a very disappointing experience .", "postag": ["VBG", "RB", ",", "DT", "NN", "VBZ", "PRP", ",", "CC", "IN", "PRP$", "NN", ",", "PRP", "VBZ", "DT", "RB", "JJ", "NN", "."], "head": [6, 1, 6, 5, 6, 0, 6, 19, 19, 12, 12, 19, 19, 19, 19, 19, 18, 19, 6, 6], "deprel": ["advcl", "advmod", "punct", "det", "nsubj", "root", "obj", "punct", "cc", "case", "nmod:poss", "obl", "punct", "nsubj", "cop", "det", "advmod", "amod", "conj", "punct"], "triples": [{"uid": "3686-0", "target_tags": "Eating\\O in\\O ,\\O the\\O atmosphere\\B saves\\O it\\O ,\\O but\\O at\\O your\\O desk\\O ,\\O it\\O 's\\O a\\O very\\O disappointing\\O experience\\O .\\O", "opinion_tags": "Eating\\O in\\O ,\\O the\\O atmosphere\\O saves\\B it\\O ,\\O but\\O at\\O your\\O desk\\O ,\\O it\\O 's\\O a\\O very\\O disappointing\\O experience\\O .\\O", "sentiment": "positive"}]}, {"id": "1441", "sentence": "My entree of hot pot with seafood was full of imitation crabmeat with a couple pieces of shrimp and squid , and was unnecessarily heated with a burner .", "postag": ["PRP$", "NN", "IN", "JJ", "NN", "IN", "NN", "VBD", "JJ", "IN", "NN", "NN", "IN", "DT", "NN", "NNS", "IN", "NN", "CC", "NN", ",", "CC", "VBD", "RB", "VBN", "IN", "DT", "NN", "."], "head": [2, 9, 5, 5, 2, 7, 5, 9, 0, 12, 12, 9, 16, 16, 16, 9, 18, 16, 20, 18, 25, 25, 25, 25, 9, 28, 28, 25, 9], "deprel": ["nmod:poss", "nsubj", "case", "amod", "nmod", "case", "nmod", "cop", "root", "case", "compound", "obl", "case", "det", "compound", "obl", "case", "nmod", "cc", "conj", "punct", "cc", "aux:pass", "advmod", "conj", "case", "det", "obl", "punct"], "triples": [{"uid": "1441-0", "target_tags": "My\\O entree\\O of\\O hot\\O pot\\O with\\O seafood\\O was\\O full\\O of\\O imitation\\O crabmeat\\B with\\O a\\O couple\\O pieces\\O of\\O shrimp\\O and\\O squid\\O ,\\O and\\O was\\O unnecessarily\\O heated\\O with\\O a\\O burner\\O .\\O", "opinion_tags": "My\\O entree\\O of\\O hot\\O pot\\O with\\O seafood\\O was\\O full\\O of\\O imitation\\O crabmeat\\O with\\O a\\O couple\\O pieces\\O of\\O shrimp\\O and\\O squid\\O ,\\O and\\O was\\O unnecessarily\\B heated\\O with\\O a\\O burner\\O .\\O", "sentiment": "negative"}]}, {"id": "3228", "sentence": "If you love seafood , you would love this place !", "postag": ["IN", "PRP", "VBP", "NN", ",", "PRP", "MD", "VB", "DT", "NN", "."], "head": [3, 3, 8, 3, 8, 8, 8, 0, 10, 8, 8], "deprel": ["mark", "nsubj", "advcl", "obj", "punct", "nsubj", "aux", "root", "det", "obj", "punct"], "triples": [{"uid": "3228-0", "target_tags": "If\\O you\\O love\\O seafood\\B ,\\O you\\O would\\O love\\O this\\O place\\O !\\O", "opinion_tags": "If\\O you\\O love\\B seafood\\O ,\\O you\\O would\\O love\\O this\\O place\\O !\\O", "sentiment": "positive"}, {"uid": "3228-1", "target_tags": "If\\O you\\O love\\O seafood\\O ,\\O you\\O would\\O love\\O this\\O place\\B !\\O", "opinion_tags": "If\\O you\\O love\\O seafood\\O ,\\O you\\O would\\O love\\B this\\O place\\O !\\O", "sentiment": "positive"}]}, {"id": "2251", "sentence": "I was very impressed by this low-key upper eastsider and their authentically thai cuisine ! ! !", "postag": ["PRP", "VBD", "RB", "JJ", "IN", "DT", "JJ", "JJ", "NN", "CC", "PRP$", "RB", "JJ", "NN", ".", ".", "."], "head": [4, 4, 4, 0, 9, 9, 9, 9, 4, 14, 14, 13, 14, 9, 4, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "case", "det", "amod", "amod", "obl", "cc", "nmod:poss", "advmod", "amod", "conj", "punct", "punct", "punct"], "triples": [{"uid": "2251-0", "target_tags": "I\\O was\\O very\\O impressed\\O by\\O this\\O low-key\\O upper\\O eastsider\\O and\\O their\\O authentically\\O thai\\B cuisine\\I !\\O !\\O !\\O", "opinion_tags": "I\\O was\\O very\\O impressed\\O by\\O this\\O low-key\\O upper\\O eastsider\\O and\\O their\\O authentically\\B thai\\O cuisine\\O !\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "1673", "sentence": "Decent wine selection too .", "postag": ["JJ", "NN", "NN", "RB", "."], "head": [3, 3, 0, 3, 3], "deprel": ["amod", "compound", "root", "advmod", "punct"], "triples": [{"uid": "1673-0", "target_tags": "Decent\\O wine\\B selection\\I too\\O .\\O", "opinion_tags": "Decent\\B wine\\O selection\\O too\\O .\\O", "sentiment": "positive"}]}, {"id": "2159", "sentence": "The bagels always warm , soft on the inside , crispy on the outside and enormous in size .", "postag": ["DT", "NNS", "RB", "JJ", ",", "JJ", "IN", "DT", "JJ", ",", "JJ", "IN", "DT", "JJ", "CC", "JJ", "IN", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 9, 9, 6, 11, 4, 14, 14, 11, 16, 14, 18, 16, 4], "deprel": ["det", "nsubj", "advmod", "root", "punct", "conj", "case", "det", "obl", "punct", "conj", "case", "det", "obl", "cc", "conj", "case", "obl", "punct"], "triples": [{"uid": "2159-0", "target_tags": "The\\O bagels\\B always\\O warm\\O ,\\O soft\\O on\\O the\\O inside\\O ,\\O crispy\\O on\\O the\\O outside\\O and\\O enormous\\O in\\O size\\O .\\O", "opinion_tags": "The\\O bagels\\O always\\O warm\\B ,\\O soft\\B on\\O the\\O inside\\O ,\\O crispy\\B on\\O the\\O outside\\O and\\O enormous\\B in\\O size\\O .\\O", "sentiment": "positive"}]}, {"id": "603", "sentence": "The service was attentive and her suggestions of menu items was right on the mark .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "PRP$", "NNS", "IN", "NN", "NNS", "VBD", "RB", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 15, 7, 15, 10, 10, 7, 15, 15, 15, 15, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "nmod:poss", "nsubj", "case", "compound", "nmod", "cop", "advmod", "case", "det", "conj", "punct"], "triples": [{"uid": "603-0", "target_tags": "The\\O service\\B was\\O attentive\\O and\\O her\\O suggestions\\O of\\O menu\\O items\\O was\\O right\\O on\\O the\\O mark\\O .\\O", "opinion_tags": "The\\O service\\O was\\O attentive\\B and\\O her\\O suggestions\\O of\\O menu\\O items\\O was\\O right\\O on\\O the\\O mark\\O .\\O", "sentiment": "positive"}, {"uid": "603-1", "target_tags": "The\\O service\\O was\\O attentive\\O and\\O her\\O suggestions\\O of\\O menu\\B items\\I was\\O right\\O on\\O the\\O mark\\O .\\O", "opinion_tags": "The\\O service\\O was\\O attentive\\O and\\O her\\O suggestions\\O of\\O menu\\O items\\O was\\O right\\B on\\O the\\O mark\\O .\\O", "sentiment": "positive"}]}, {"id": "1080", "sentence": "The highly spiced chai tea was great too .", "postag": ["DT", "RB", "JJ", "NN", "NN", "VBD", "JJ", "RB", "."], "head": [5, 3, 5, 5, 7, 7, 0, 7, 7], "deprel": ["det", "advmod", "amod", "compound", "nsubj", "cop", "root", "advmod", "punct"], "triples": [{"uid": "1080-0", "target_tags": "The\\O highly\\O spiced\\O chai\\B tea\\I was\\O great\\O too\\O .\\O", "opinion_tags": "The\\O highly\\B spiced\\I chai\\O tea\\O was\\O great\\B too\\O .\\O", "sentiment": "positive"}]}, {"id": "2457", "sentence": "And the staff is also young , energeic and hot ! ! ! !", "postag": ["CC", "DT", "NN", "VBZ", "RB", "JJ", ",", "JJ", "CC", "JJ", ".", ".", ".", "."], "head": [6, 3, 6, 6, 6, 0, 8, 6, 10, 6, 6, 6, 6, 6], "deprel": ["cc", "det", "nsubj", "cop", "advmod", "root", "punct", "conj", "cc", "conj", "punct", "punct", "punct", "punct"], "triples": [{"uid": "2457-0", "target_tags": "And\\O the\\O staff\\B is\\O also\\O young\\O ,\\O energeic\\O and\\O hot\\O !\\O !\\O !\\O !\\O", "opinion_tags": "And\\O the\\O staff\\O is\\O also\\O young\\B ,\\O energeic\\B and\\O hot\\B !\\O !\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "1592", "sentence": "Our server was very helpful and friendly .", "postag": ["PRP$", "NN", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["nmod:poss", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"], "triples": [{"uid": "1592-0", "target_tags": "Our\\O server\\B was\\O very\\O helpful\\O and\\O friendly\\O .\\O", "opinion_tags": "Our\\O server\\O was\\O very\\O helpful\\B and\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "1951", "sentence": "Even though the restaurant was packed , we were seated promptly and even asked for a table upstairs with no problems .", "postag": ["RB", "IN", "DT", "NN", "VBD", "VBN", ",", "PRP", "VBD", "VBN", "RB", "CC", "RB", "VBD", "IN", "DT", "NN", "RB", "IN", "DT", "NNS", "."], "head": [6, 6, 4, 6, 6, 10, 10, 10, 10, 0, 10, 14, 14, 10, 17, 17, 14, 14, 21, 21, 14, 10], "deprel": ["advmod", "mark", "det", "nsubj:pass", "aux:pass", "advcl", "punct", "nsubj:pass", "aux:pass", "root", "advmod", "cc", "advmod", "conj", "case", "det", "obl", "advmod", "case", "det", "obl", "punct"], "triples": [{"uid": "1951-0", "target_tags": "Even\\O though\\O the\\O restaurant\\O was\\O packed\\O ,\\O we\\O were\\O seated\\B promptly\\O and\\O even\\O asked\\O for\\O a\\O table\\O upstairs\\O with\\O no\\O problems\\O .\\O", "opinion_tags": "Even\\O though\\O the\\O restaurant\\O was\\O packed\\O ,\\O we\\O were\\O seated\\O promptly\\B and\\O even\\O asked\\O for\\O a\\O table\\O upstairs\\O with\\O no\\O problems\\O .\\O", "sentiment": "positive"}]}, {"id": "2277", "sentence": "The cuisine from what I 've gathered is authentic Taiwanese , though its very different from what I 've been accustomed to in Taipei .", "postag": ["DT", "NN", "IN", "WP", "PRP", "VBP", "VBN", "VBZ", "JJ", "JJ", ",", "IN", "PRP$", "RB", "JJ", "IN", "WP", "PRP", "VBP", "VBN", "VBN", "IN", "IN", "NNP", "."], "head": [2, 10, 4, 2, 7, 7, 4, 10, 10, 0, 10, 15, 15, 15, 10, 17, 15, 21, 21, 21, 17, 21, 24, 21, 10], "deprel": ["det", "nsubj", "case", "nmod", "nsubj", "aux", "acl:relcl", "cop", "amod", "root", "punct", "mark", "nmod:poss", "advmod", "advcl", "case", "obl", "nsubj:pass", "aux", "aux:pass", "acl:relcl", "obl", "case", "obl", "punct"], "triples": [{"uid": "2277-0", "target_tags": "The\\O cuisine\\B from\\O what\\O I\\O 've\\O gathered\\O is\\O authentic\\O Taiwanese\\O ,\\O though\\O its\\O very\\O different\\O from\\O what\\O I\\O 've\\O been\\O accustomed\\O to\\O in\\O Taipei\\O .\\O", "opinion_tags": "The\\O cuisine\\O from\\O what\\O I\\O 've\\O gathered\\O is\\O authentic\\O Taiwanese\\O ,\\O though\\O its\\O very\\O different\\B from\\O what\\O I\\O 've\\O been\\O accustomed\\O to\\O in\\O Taipei\\O .\\O", "sentiment": "neutral"}]}, {"id": "2532", "sentence": "Your money could easily be better spent elsewhere ( Anywhere ) .", "postag": ["PRP$", "NN", "MD", "RB", "VB", "RBR", "VBN", "RB", "-LRB-", "RB", "-RRB-", "."], "head": [2, 7, 7, 6, 7, 7, 0, 7, 10, 7, 10, 7], "deprel": ["nmod:poss", "nsubj:pass", "aux", "advmod", "aux:pass", "advmod", "root", "advmod", "punct", "advmod", "punct", "punct"], "triples": [{"uid": "2532-0", "target_tags": "Your\\O money\\B could\\O easily\\O be\\O better\\O spent\\O elsewhere\\O (\\O Anywhere\\O )\\O .\\O", "opinion_tags": "Your\\O money\\O could\\O easily\\O be\\O better\\B spent\\O elsewhere\\O (\\O Anywhere\\O )\\O .\\O", "sentiment": "neutral"}]}, {"id": "173", "sentence": "Food is usually very good , though ocasionally I wondered about freshmess of raw vegatables in side orders .", "postag": ["NN", "VBZ", "RB", "RB", "JJ", ",", "IN", "RB", "PRP", "VBD", "IN", "NN", "IN", "JJ", "NNS", "IN", "NN", "NNS", "."], "head": [5, 5, 5, 5, 0, 5, 10, 10, 10, 5, 12, 10, 15, 15, 12, 18, 18, 15, 5], "deprel": ["nsubj", "cop", "advmod", "advmod", "root", "punct", "mark", "advmod", "nsubj", "advcl", "case", "obl", "case", "amod", "nmod", "case", "compound", "nmod", "punct"], "triples": [{"uid": "173-1", "target_tags": "Food\\O is\\O usually\\O very\\O good\\O ,\\O though\\O ocasionally\\O I\\O wondered\\O about\\O freshmess\\O of\\O raw\\B vegatables\\I in\\O side\\O orders\\O .\\O", "opinion_tags": "Food\\O is\\O usually\\O very\\O good\\O ,\\O though\\O ocasionally\\O I\\O wondered\\B about\\O freshmess\\O of\\O raw\\O vegatables\\O in\\O side\\O orders\\O .\\O", "sentiment": "negative"}]}, {"id": "2748", "sentence": "The service was the only thing good about this restaurant .", "postag": ["DT", "NN", "VBD", "DT", "JJ", "NN", "JJ", "IN", "DT", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 6, 10, 10, 7, 6], "deprel": ["det", "nsubj", "cop", "det", "amod", "root", "amod", "case", "det", "obl", "punct"], "triples": [{"uid": "2748-0", "target_tags": "The\\O service\\B was\\O the\\O only\\O thing\\O good\\O about\\O this\\O restaurant\\O .\\O", "opinion_tags": "The\\O service\\O was\\O the\\O only\\O thing\\O good\\B about\\O this\\O restaurant\\O .\\O", "sentiment": "positive"}]}, {"id": "2984", "sentence": "Would n't recomend it for dinner !", "postag": ["MD", "RB", "VB", "PRP", "IN", "NN", "."], "head": [3, 3, 0, 3, 6, 3, 3], "deprel": ["aux", "advmod", "root", "obj", "case", "obl", "punct"], "triples": [{"uid": "2984-0", "target_tags": "Would\\O n't\\O recomend\\O it\\O for\\O dinner\\B !\\O", "opinion_tags": "Would\\O n't\\O recomend\\B it\\O for\\O dinner\\O !\\O", "sentiment": "negative"}]}, {"id": "1609", "sentence": "Service was quick .", "postag": ["NN", "VBD", "JJ", "."], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"], "triples": [{"uid": "1609-0", "target_tags": "Service\\B was\\O quick\\O .\\O", "opinion_tags": "Service\\O was\\O quick\\B .\\O", "sentiment": "positive"}]}, {"id": "1456", "sentence": "You can certainly find restaurants that offer a superior fine dining experience , but for superb food at reasonable prices , La Villa ca n't be beat .", "postag": ["PRP", "MD", "RB", "VB", "NNS", "WDT", "VBP", "DT", "JJ", "JJ", "NN", "NN", ",", "CC", "IN", "JJ", "NN", "IN", "JJ", "NNS", ",", "NNP", "NNP", "MD", "RB", "VB", "VBN", "."], "head": [4, 4, 4, 0, 4, 7, 5, 12, 12, 12, 12, 7, 27, 27, 17, 17, 27, 20, 20, 17, 27, 27, 22, 27, 27, 27, 4, 4], "deprel": ["nsubj", "aux", "advmod", "root", "obj", "nsubj", "acl:relcl", "det", "amod", "amod", "compound", "obj", "punct", "cc", "case", "amod", "obl", "case", "amod", "nmod", "punct", "nsubj:pass", "flat", "aux", "advmod", "aux:pass", "conj", "punct"], "triples": [{"uid": "1456-0", "target_tags": "You\\O can\\O certainly\\O find\\O restaurants\\O that\\O offer\\O a\\O superior\\O fine\\O dining\\O experience\\O ,\\O but\\O for\\O superb\\O food\\B at\\O reasonable\\O prices\\O ,\\O La\\O Villa\\O ca\\O n't\\O be\\O beat\\O .\\O", "opinion_tags": "You\\O can\\O certainly\\O find\\O restaurants\\O that\\O offer\\O a\\O superior\\O fine\\O dining\\O experience\\O ,\\O but\\O for\\O superb\\B food\\O at\\O reasonable\\O prices\\O ,\\O La\\O Villa\\O ca\\O n't\\O be\\O beat\\O .\\O", "sentiment": "positive"}, {"uid": "1456-1", "target_tags": "You\\O can\\O certainly\\O find\\O restaurants\\O that\\O offer\\O a\\O superior\\O fine\\O dining\\O experience\\O ,\\O but\\O for\\O superb\\O food\\O at\\O reasonable\\O prices\\B ,\\O La\\O Villa\\O ca\\O n't\\O be\\O beat\\O .\\O", "opinion_tags": "You\\O can\\O certainly\\O find\\O restaurants\\O that\\O offer\\O a\\O superior\\O fine\\O dining\\O experience\\O ,\\O but\\O for\\O superb\\O food\\O at\\O reasonable\\B prices\\O ,\\O La\\O Villa\\O ca\\O n't\\O be\\O beat\\O .\\O", "sentiment": "positive"}]}, {"id": "3519", "sentence": "The prices and ambience are especially great considering it 's in the West Village .", "postag": ["DT", "NNS", "CC", "NN", "VBP", "RB", "JJ", "VBG", "PRP", "VBZ", "IN", "DT", "NNP", "NNP", "."], "head": [2, 7, 4, 2, 7, 7, 0, 7, 14, 14, 14, 14, 14, 8, 7], "deprel": ["det", "nsubj", "cc", "conj", "cop", "advmod", "root", "advcl", "nsubj", "cop", "case", "det", "compound", "ccomp", "punct"], "triples": [{"uid": "3519-0", "target_tags": "The\\O prices\\B and\\O ambience\\O are\\O especially\\O great\\O considering\\O it\\O 's\\O in\\O the\\O West\\O Village\\O .\\O", "opinion_tags": "The\\O prices\\O and\\O ambience\\O are\\O especially\\O great\\B considering\\O it\\O 's\\O in\\O the\\O West\\O Village\\O .\\O", "sentiment": "positive"}, {"uid": "3519-1", "target_tags": "The\\O prices\\O and\\O ambience\\B are\\O especially\\O great\\O considering\\O it\\O 's\\O in\\O the\\O West\\O Village\\O .\\O", "opinion_tags": "The\\O prices\\O and\\O ambience\\O are\\O especially\\O great\\B considering\\O it\\O 's\\O in\\O the\\O West\\O Village\\O .\\O", "sentiment": "positive"}]}, {"id": "1481", "sentence": "The ingredients taste fresher , the crust is thinner and crispier , the slice is less oily , and it 's never burnt like it occasionally is at Joe 's .", "postag": ["DT", "NNS", "VBP", "JJR", ",", "DT", "NN", "VBZ", "JJR", "CC", "JJR", ",", "DT", "NN", "VBZ", "RBR", "JJ", ",", "CC", "PRP", "VBZ", "RB", "JJ", "IN", "PRP", "RB", "VBZ", "IN", "NNP", "POS", "."], "head": [2, 3, 0, 3, 9, 7, 9, 9, 3, 11, 9, 17, 14, 17, 17, 17, 3, 23, 23, 23, 23, 23, 3, 29, 29, 29, 29, 29, 23, 29, 3], "deprel": ["det", "nsubj", "root", "xcomp", "punct", "det", "nsubj", "cop", "parataxis", "cc", "conj", "punct", "det", "nsubj", "cop", "advmod", "parataxis", "punct", "cc", "nsubj", "cop", "advmod", "conj", "mark", "nsubj", "advmod", "cop", "case", "advcl", "case", "punct"], "triples": [{"uid": "1481-0", "target_tags": "The\\O ingredients\\B taste\\O fresher\\O ,\\O the\\O crust\\O is\\O thinner\\O and\\O crispier\\O ,\\O the\\O slice\\O is\\O less\\O oily\\O ,\\O and\\O it\\O 's\\O never\\O burnt\\O like\\O it\\O occasionally\\O is\\O at\\O Joe\\O 's\\O .\\O", "opinion_tags": "The\\O ingredients\\O taste\\O fresher\\B ,\\O the\\O crust\\O is\\O thinner\\O and\\O crispier\\O ,\\O the\\O slice\\O is\\O less\\O oily\\O ,\\O and\\O it\\O 's\\O never\\O burnt\\O like\\O it\\O occasionally\\O is\\O at\\O Joe\\O 's\\O .\\O", "sentiment": "positive"}, {"uid": "1481-1", "target_tags": "The\\O ingredients\\O taste\\O fresher\\O ,\\O the\\O crust\\B is\\O thinner\\O and\\O crispier\\O ,\\O the\\O slice\\O is\\O less\\O oily\\O ,\\O and\\O it\\O 's\\O never\\O burnt\\O like\\O it\\O occasionally\\O is\\O at\\O Joe\\O 's\\O .\\O", "opinion_tags": "The\\O ingredients\\O taste\\O fresher\\O ,\\O the\\O crust\\O is\\O thinner\\B and\\O crispier\\B ,\\O the\\O slice\\O is\\O less\\O oily\\O ,\\O and\\O it\\O 's\\O never\\O burnt\\O like\\O it\\O occasionally\\O is\\O at\\O Joe\\O 's\\O .\\O", "sentiment": "positive"}, {"uid": "1481-2", "target_tags": "The\\O ingredients\\O taste\\O fresher\\O ,\\O the\\O crust\\O is\\O thinner\\O and\\O crispier\\O ,\\O the\\O slice\\B is\\O less\\O oily\\O ,\\O and\\O it\\O 's\\O never\\O burnt\\O like\\O it\\O occasionally\\O is\\O at\\O Joe\\O 's\\O .\\O", "opinion_tags": "The\\O ingredients\\O taste\\O fresher\\O ,\\O the\\O crust\\O is\\O thinner\\O and\\O crispier\\O ,\\O the\\O slice\\O is\\O less\\B oily\\I ,\\O and\\O it\\O 's\\O never\\O burnt\\O like\\O it\\O occasionally\\O is\\O at\\O Joe\\O 's\\O .\\O", "sentiment": "positive"}]}, {"id": "30", "sentence": "The bartender on my most recent visit was so incredibly rude that I will never go back .", "postag": ["DT", "NN", "IN", "PRP$", "RBS", "JJ", "NN", "VBD", "RB", "RB", "JJ", "IN", "PRP", "MD", "RB", "VB", "RB", "."], "head": [2, 11, 7, 7, 6, 7, 2, 11, 10, 11, 0, 16, 16, 16, 16, 11, 16, 11], "deprel": ["det", "nsubj", "case", "nmod:poss", "advmod", "amod", "nmod", "cop", "advmod", "advmod", "root", "mark", "nsubj", "aux", "advmod", "ccomp", "advmod", "punct"], "triples": [{"uid": "30-0", "target_tags": "The\\O bartender\\B on\\O my\\O most\\O recent\\O visit\\O was\\O so\\O incredibly\\O rude\\O that\\O I\\O will\\O never\\O go\\O back\\O .\\O", "opinion_tags": "The\\O bartender\\O on\\O my\\O most\\O recent\\O visit\\O was\\O so\\O incredibly\\O rude\\B that\\O I\\O will\\O never\\O go\\O back\\O .\\O", "sentiment": "negative"}]}, {"id": "185", "sentence": "Also , specify if you like your food spicy- its rather bland if you do n't .", "postag": ["RB", ",", "VB", "IN", "PRP", "VBP", "PRP$", "NN", "VB", "PRP$", "RB", "JJ", "IN", "PRP", "VBP", "RB", "."], "head": [3, 3, 0, 6, 6, 3, 8, 6, 6, 12, 12, 9, 15, 15, 9, 15, 3], "deprel": ["advmod", "punct", "root", "mark", "nsubj", "advcl", "nmod:poss", "obj", "xcomp", "nmod:poss", "advmod", "obj", "mark", "nsubj", "advcl", "advmod", "punct"], "triples": [{"uid": "185-0", "target_tags": "Also\\O ,\\O specify\\O if\\O you\\O like\\O your\\O food\\B spicy-\\O its\\O rather\\O bland\\O if\\O you\\O do\\O n't\\O .\\O", "opinion_tags": "Also\\O ,\\O specify\\O if\\O you\\O like\\O your\\O food\\O spicy-\\O its\\O rather\\O bland\\B if\\O you\\O do\\O n't\\O .\\O", "sentiment": "neutral"}]}, {"id": "2998", "sentence": "Even if the food was n't this good , the garden is a great place to sit outside and relax .", "postag": ["RB", "IN", "DT", "NN", "VBD", "RB", "DT", "JJ", ",", "DT", "NN", "VBZ", "DT", "JJ", "NN", "TO", "VB", "RB", "CC", "VB", "."], "head": [7, 7, 4, 7, 7, 7, 15, 7, 15, 11, 15, 15, 15, 15, 0, 17, 15, 17, 20, 17, 15], "deprel": ["advmod", "mark", "det", "nsubj", "cop", "advmod", "advcl", "advmod", "punct", "det", "nsubj", "cop", "det", "amod", "root", "mark", "acl", "advmod", "cc", "conj", "punct"], "triples": [{"uid": "2998-0", "target_tags": "Even\\O if\\O the\\O food\\B was\\O n't\\O this\\O good\\O ,\\O the\\O garden\\O is\\O a\\O great\\O place\\O to\\O sit\\O outside\\O and\\O relax\\O .\\O", "opinion_tags": "Even\\O if\\O the\\O food\\O was\\B n't\\I this\\I good\\I ,\\O the\\O garden\\O is\\O a\\O great\\O place\\O to\\O sit\\O outside\\O and\\O relax\\O .\\O", "sentiment": "positive"}, {"uid": "2998-1", "target_tags": "Even\\O if\\O the\\O food\\O was\\O n't\\O this\\O good\\O ,\\O the\\O garden\\B is\\O a\\O great\\O place\\O to\\O sit\\O outside\\O and\\O relax\\O .\\O", "opinion_tags": "Even\\O if\\O the\\O food\\O was\\O n't\\O this\\O good\\O ,\\O the\\O garden\\O is\\O a\\O great\\B place\\O to\\O sit\\O outside\\O and\\O relax\\O .\\O", "sentiment": "positive"}, {"uid": "2998-2", "target_tags": "Even\\O if\\O the\\O food\\O was\\O n't\\O this\\O good\\O ,\\O the\\O garden\\O is\\O a\\O great\\O place\\B to\\O sit\\O outside\\O and\\O relax\\O .\\O", "opinion_tags": "Even\\O if\\O the\\O food\\O was\\O n't\\O this\\O good\\O ,\\O the\\O garden\\O is\\O a\\O great\\B place\\O to\\O sit\\O outside\\O and\\O relax\\O .\\O", "sentiment": "positive"}]}, {"id": "1205", "sentence": "This is the best sushi in new york city - hands down .", "postag": ["DT", "VBZ", "DT", "JJS", "NN", "IN", "JJ", "NNP", "NN", "HYPH", "NNS", "RB", "."], "head": [5, 5, 5, 5, 0, 11, 11, 11, 11, 11, 5, 5, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "case", "amod", "compound", "compound", "punct", "nmod", "advmod", "punct"], "triples": [{"uid": "1205-0", "target_tags": "This\\O is\\O the\\O best\\O sushi\\B in\\O new\\O york\\O city\\O -\\O hands\\O down\\O .\\O", "opinion_tags": "This\\O is\\O the\\O best\\B sushi\\O in\\O new\\O york\\O city\\O -\\O hands\\O down\\O .\\O", "sentiment": "positive"}]}, {"id": "2848", "sentence": "The menu is Prix Fixe , so be prepared to spend at least $ 60 per person , but it is Well worth it superb food .", "postag": ["DT", "NN", "VBZ", "NN", "NN", ",", "RB", "VB", "VBN", "TO", "VB", "RB", "RBS", "$", "CD", "IN", "NN", ",", "CC", "PRP", "VBZ", "RB", "JJ", "PRP", "JJ", "NN", "."], "head": [2, 5, 5, 5, 0, 5, 9, 9, 5, 11, 9, 13, 14, 11, 14, 17, 14, 23, 23, 23, 23, 23, 5, 23, 26, 23, 5], "deprel": ["det", "nsubj", "cop", "compound", "root", "punct", "advmod", "aux:pass", "parataxis", "mark", "xcomp", "case", "nmod", "obj", "nummod", "case", "nmod", "punct", "cc", "nsubj", "cop", "advmod", "conj", "obj", "amod", "obj", "punct"], "triples": [{"uid": "2848-0", "target_tags": "The\\O menu\\B is\\O Prix\\O Fixe\\O ,\\O so\\O be\\O prepared\\O to\\O spend\\O at\\O least\\O $\\O 60\\O per\\O person\\O ,\\O but\\O it\\O is\\O Well\\O worth\\O it\\O superb\\O food\\O .\\O", "opinion_tags": "The\\O menu\\O is\\O Prix\\O Fixe\\O ,\\O so\\O be\\O prepared\\O to\\O spend\\O at\\O least\\O $\\O 60\\O per\\O person\\O ,\\O but\\O it\\O is\\O Well\\B worth\\I it\\O superb\\O food\\O .\\O", "sentiment": "negative"}, {"uid": "2848-1", "target_tags": "The\\O menu\\O is\\O Prix\\O Fixe\\O ,\\O so\\O be\\O prepared\\O to\\O spend\\O at\\O least\\O $\\O 60\\O per\\O person\\O ,\\O but\\O it\\O is\\O Well\\O worth\\O it\\O superb\\O food\\B .\\O", "opinion_tags": "The\\O menu\\O is\\O Prix\\O Fixe\\O ,\\O so\\O be\\O prepared\\O to\\O spend\\O at\\O least\\O $\\O 60\\O per\\O person\\O ,\\O but\\O it\\O is\\O Well\\O worth\\O it\\O superb\\B food\\O .\\O", "sentiment": "positive"}, {"uid": "2848-2", "target_tags": "The\\O menu\\O is\\O Prix\\B Fixe\\I ,\\O so\\O be\\O prepared\\O to\\O spend\\O at\\O least\\O $\\O 60\\O per\\O person\\O ,\\O but\\O it\\O is\\O Well\\O worth\\O it\\O superb\\O food\\O .\\O", "opinion_tags": "The\\O menu\\O is\\O Prix\\O Fixe\\O ,\\O so\\O be\\O prepared\\O to\\O spend\\O at\\O least\\O $\\O 60\\O per\\O person\\O ,\\O but\\O it\\O is\\O Well\\B worth\\I it\\O superb\\O food\\O .\\O", "sentiment": "neutral"}]}, {"id": "3557", "sentence": "head and shoulders above its neighboors on east 6 st , taj mahal is also very comparable , in food quality , to the much overpraised ( and underdeserving ) baluchi 's .", "postag": ["NN", "CC", "NNS", "IN", "PRP$", "NNS", "IN", "JJ", "CD", "NNP", ",", "NNP", "NN", "VBZ", "RB", "RB", "JJ", ",", "IN", "NN", "NN", ",", "IN", "DT", "RB", "VBN", "-LRB-", "CC", "NN", "-RRB-", "NN", "POS", "."], "head": [17, 3, 1, 6, 6, 1, 10, 10, 10, 6, 1, 13, 17, 17, 17, 17, 0, 17, 21, 21, 17, 31, 31, 31, 26, 31, 26, 29, 26, 29, 17, 31, 17], "deprel": ["nsubj", "cc", "conj", "case", "nmod:poss", "nmod", "case", "amod", "nummod", "nmod", "punct", "compound", "nsubj", "cop", "advmod", "advmod", "root", "punct", "case", "compound", "obl", "punct", "case", "det", "advmod", "amod", "punct", "cc", "conj", "punct", "obl", "case", "punct"], "triples": [{"uid": "3557-0", "target_tags": "head\\O and\\O shoulders\\O above\\O its\\O neighboors\\O on\\O east\\O 6\\O st\\O ,\\O taj\\O mahal\\O is\\O also\\O very\\O comparable\\O ,\\O in\\O food\\B quality\\I ,\\O to\\O the\\O much\\O overpraised\\O (\\O and\\O underdeserving\\O )\\O baluchi\\O 's\\O .\\O", "opinion_tags": "head\\O and\\O shoulders\\O above\\O its\\O neighboors\\O on\\O east\\O 6\\O st\\O ,\\O taj\\O mahal\\O is\\O also\\O very\\O comparable\\B ,\\O in\\O food\\O quality\\O ,\\O to\\O the\\O much\\O overpraised\\O (\\O and\\O underdeserving\\O )\\O baluchi\\O 's\\O .\\O", "sentiment": "positive"}]}, {"id": "296", "sentence": "They did not have mayonnaise , forgot our toast , left out ingredients ( ie cheese in an omelet ) , below hot temperatures and the bacon was so over cooked it crumbled on the plate when you touched it .", "postag": ["PRP", "VBD", "RB", "VB", "NN", ",", "VBD", "PRP$", "NN", ",", "VBD", "RP", "NNS", "-LRB-", "NN", "NN", "IN", "DT", "NN", "-RRB-", ",", "IN", "JJ", "NNS", "CC", "DT", "NN", "VBD", "RB", "RB", "VBN", "PRP", "VBD", "IN", "DT", "NN", "WRB", "PRP", "VBD", "PRP", "."], "head": [4, 4, 4, 0, 4, 7, 4, 9, 7, 11, 4, 11, 11, 16, 16, 13, 19, 19, 16, 16, 4, 24, 24, 11, 30, 27, 30, 30, 30, 4, 30, 33, 31, 36, 36, 33, 39, 39, 33, 39, 4], "deprel": ["nsubj", "aux", "advmod", "root", "obj", "punct", "conj", "nmod:poss", "obj", "punct", "conj", "compound:prt", "obj", "punct", "compound", "appos", "case", "det", "nmod", "punct", "punct", "case", "amod", "obl", "cc", "det", "nsubj", "cop", "advmod", "conj", "xcomp", "nsubj", "ccomp", "case", "det", "obl", "mark", "nsubj", "advcl", "obj", "punct"], "triples": [{"uid": "296-0", "target_tags": "They\\O did\\O not\\O have\\O mayonnaise\\O ,\\O forgot\\O our\\O toast\\B ,\\O left\\O out\\O ingredients\\O (\\O ie\\O cheese\\O in\\O an\\O omelet\\O )\\O ,\\O below\\O hot\\O temperatures\\O and\\O the\\O bacon\\O was\\O so\\O over\\O cooked\\O it\\O crumbled\\O on\\O the\\O plate\\O when\\O you\\O touched\\O it\\O .\\O", "opinion_tags": "They\\O did\\O not\\O have\\O mayonnaise\\O ,\\O forgot\\B our\\O toast\\O ,\\O left\\O out\\O ingredients\\O (\\O ie\\O cheese\\O in\\O an\\O omelet\\O )\\O ,\\O below\\O hot\\O temperatures\\O and\\O the\\O bacon\\O was\\O so\\O over\\O cooked\\O it\\O crumbled\\O on\\O the\\O plate\\O when\\O you\\O touched\\O it\\O .\\O", "sentiment": "negative"}, {"uid": "296-1", "target_tags": "They\\O did\\O not\\O have\\O mayonnaise\\O ,\\O forgot\\O our\\O toast\\O ,\\O left\\O out\\O ingredients\\O (\\O ie\\O cheese\\O in\\O an\\O omelet\\O )\\O ,\\O below\\O hot\\O temperatures\\O and\\O the\\O bacon\\B was\\O so\\O over\\O cooked\\O it\\O crumbled\\O on\\O the\\O plate\\O when\\O you\\O touched\\O it\\O .\\O", "opinion_tags": "They\\O did\\O not\\O have\\O mayonnaise\\O ,\\O forgot\\O our\\O toast\\O ,\\O left\\O out\\O ingredients\\O (\\O ie\\O cheese\\O in\\O an\\O omelet\\O )\\O ,\\O below\\O hot\\O temperatures\\O and\\O the\\O bacon\\O was\\O so\\O over\\B cooked\\I it\\O crumbled\\O on\\O the\\O plate\\O when\\O you\\O touched\\O it\\O .\\O", "sentiment": "negative"}, {"uid": "296-2", "target_tags": "They\\O did\\O not\\O have\\O mayonnaise\\O ,\\O forgot\\O our\\O toast\\O ,\\O left\\O out\\O ingredients\\O (\\O ie\\O cheese\\B in\\O an\\O omelet\\O )\\O ,\\O below\\O hot\\O temperatures\\O and\\O the\\O bacon\\O was\\O so\\O over\\O cooked\\O it\\O crumbled\\O on\\O the\\O plate\\O when\\O you\\O touched\\O it\\O .\\O", "opinion_tags": "They\\O did\\O not\\O have\\O mayonnaise\\O ,\\O forgot\\O our\\O toast\\O ,\\O left\\B out\\I ingredients\\O (\\O ie\\O cheese\\O in\\O an\\O omelet\\O )\\O ,\\O below\\O hot\\O temperatures\\O and\\O the\\O bacon\\O was\\O so\\O over\\O cooked\\O it\\O crumbled\\O on\\O the\\O plate\\O when\\O you\\O touched\\O it\\O .\\O", "sentiment": "neutral"}, {"uid": "296-3", "target_tags": "They\\O did\\O not\\O have\\O mayonnaise\\O ,\\O forgot\\O our\\O toast\\O ,\\O left\\O out\\O ingredients\\B (\\O ie\\O cheese\\O in\\O an\\O omelet\\O )\\O ,\\O below\\O hot\\O temperatures\\O and\\O the\\O bacon\\O was\\O so\\O over\\O cooked\\O it\\O crumbled\\O on\\O the\\O plate\\O when\\O you\\O touched\\O it\\O .\\O", "opinion_tags": "They\\O did\\O not\\O have\\O mayonnaise\\O ,\\O forgot\\O our\\O toast\\O ,\\O left\\B out\\I ingredients\\O (\\O ie\\O cheese\\O in\\O an\\O omelet\\O )\\O ,\\O below\\O hot\\O temperatures\\O and\\O the\\O bacon\\O was\\O so\\O over\\O cooked\\O it\\O crumbled\\O on\\O the\\O plate\\O when\\O you\\O touched\\O it\\O .\\O", "sentiment": "negative"}, {"uid": "296-4", "target_tags": "They\\O did\\O not\\O have\\O mayonnaise\\O ,\\O forgot\\O our\\O toast\\O ,\\O left\\O out\\O ingredients\\O (\\O ie\\O cheese\\O in\\O an\\O omelet\\O )\\O ,\\O below\\O hot\\O temperatures\\O and\\O the\\O bacon\\O was\\O so\\O over\\O cooked\\O it\\O crumbled\\O on\\O the\\O plate\\B when\\O you\\O touched\\O it\\O .\\O", "opinion_tags": "They\\O did\\O not\\O have\\O mayonnaise\\O ,\\O forgot\\O our\\O toast\\O ,\\O left\\O out\\O ingredients\\O (\\O ie\\O cheese\\O in\\O an\\O omelet\\O )\\O ,\\O below\\O hot\\O temperatures\\O and\\O the\\O bacon\\O was\\O so\\O over\\B cooked\\I it\\O crumbled\\O on\\O the\\O plate\\O when\\O you\\O touched\\O it\\O .\\O", "sentiment": "neutral"}, {"uid": "296-5", "target_tags": "They\\O did\\O not\\O have\\O mayonnaise\\O ,\\O forgot\\O our\\O toast\\O ,\\O left\\O out\\O ingredients\\O (\\O ie\\O cheese\\O in\\O an\\O omelet\\B )\\O ,\\O below\\O hot\\O temperatures\\O and\\O the\\O bacon\\O was\\O so\\O over\\O cooked\\O it\\O crumbled\\O on\\O the\\O plate\\O when\\O you\\O touched\\O it\\O .\\O", "opinion_tags": "They\\O did\\O not\\O have\\O mayonnaise\\O ,\\O forgot\\O our\\O toast\\O ,\\O left\\B out\\I ingredients\\O (\\O ie\\O cheese\\O in\\O an\\O omelet\\O )\\O ,\\O below\\O hot\\O temperatures\\O and\\O the\\O bacon\\O was\\O so\\O over\\O cooked\\O it\\O crumbled\\O on\\O the\\O plate\\O when\\O you\\O touched\\O it\\O .\\O", "sentiment": "neutral"}]}, {"id": "2940", "sentence": "they bring service up a notch by offerng complementary amuse bouche to all tables and gave us a small dessert for our celebration .", "postag": ["PRP", "VBP", "NN", "RP", "DT", "NN", "IN", "VBG", "JJ", "NN", "NN", "IN", "DT", "NNS", "CC", "VBD", "PRP", "DT", "JJ", "NN", "IN", "PRP$", "NN", "."], "head": [2, 0, 2, 2, 6, 2, 8, 2, 11, 11, 8, 14, 14, 8, 16, 2, 16, 20, 20, 16, 23, 23, 20, 2], "deprel": ["nsubj", "root", "obj", "compound:prt", "det", "obl:npmod", "mark", "advcl", "amod", "compound", "obj", "case", "det", "obl", "cc", "conj", "i<PERSON><PERSON>", "det", "amod", "obj", "case", "nmod:poss", "nmod", "punct"], "triples": [{"uid": "2940-0", "target_tags": "they\\O bring\\O service\\O up\\O a\\O notch\\O by\\O offerng\\O complementary\\O amuse\\B bouche\\I to\\O all\\O tables\\O and\\O gave\\O us\\O a\\O small\\O dessert\\O for\\O our\\O celebration\\O .\\O", "opinion_tags": "they\\O bring\\O service\\O up\\O a\\O notch\\O by\\O offerng\\O complementary\\B amuse\\O bouche\\O to\\O all\\O tables\\O and\\O gave\\O us\\O a\\O small\\O dessert\\O for\\O our\\O celebration\\O .\\O", "sentiment": "positive"}, {"uid": "2940-1", "target_tags": "they\\O bring\\O service\\O up\\O a\\O notch\\O by\\O offerng\\O complementary\\O amuse\\O bouche\\O to\\O all\\O tables\\O and\\O gave\\O us\\O a\\O small\\O dessert\\B for\\O our\\O celebration\\O .\\O", "opinion_tags": "they\\O bring\\O service\\O up\\O a\\O notch\\O by\\O offerng\\O complementary\\O amuse\\O bouche\\O to\\O all\\O tables\\O and\\O gave\\O us\\O a\\O small\\B dessert\\O for\\O our\\O celebration\\O .\\O", "sentiment": "positive"}]}, {"id": "3499", "sentence": "Not enough wines by the glass either .", "postag": ["RB", "JJ", "NNS", "IN", "DT", "NN", "RB", "."], "head": [2, 3, 0, 6, 6, 3, 3, 3], "deprel": ["advmod", "amod", "root", "case", "det", "nmod", "advmod", "punct"], "triples": [{"uid": "3499-0", "target_tags": "Not\\O enough\\O wines\\B by\\I the\\I glass\\I either\\O .\\O", "opinion_tags": "Not\\B enough\\I wines\\O by\\O the\\O glass\\O either\\O .\\O", "sentiment": "negative"}]}, {"id": "1293", "sentence": "Their calzones are horrific , bad , vomit-inducing , YUCK .", "postag": ["PRP$", "NNS", "VBP", "JJ", ",", "JJ", ",", "JJ", ",", "UH", "."], "head": [2, 4, 4, 0, 6, 4, 8, 4, 4, 4, 4], "deprel": ["nmod:poss", "nsubj", "cop", "root", "punct", "conj", "punct", "conj", "punct", "discourse", "punct"], "triples": [{"uid": "1293-0", "target_tags": "Their\\O calzones\\B are\\O horrific\\O ,\\O bad\\O ,\\O vomit-inducing\\O ,\\O YUCK\\O .\\O", "opinion_tags": "Their\\O calzones\\O are\\O horrific\\B ,\\O bad\\B ,\\O vomit-inducing\\B ,\\O YUCK\\B .\\O", "sentiment": "negative"}]}, {"id": "345", "sentence": "The service is awful .", "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "345-0", "target_tags": "The\\O service\\B is\\O awful\\O .\\O", "opinion_tags": "The\\O service\\O is\\O awful\\B .\\O", "sentiment": "negative"}]}, {"id": "2643", "sentence": "The crackling calamari salad , which is usually a cheap disaster at many restaurants , is crispy and lightly dressed .", "postag": ["DT", "VBG", "JJ", "NN", ",", "WDT", "VBZ", "RB", "DT", "JJ", "NN", "IN", "JJ", "NNS", ",", "VBZ", "JJ", "CC", "RB", "VBN", "."], "head": [4, 4, 4, 17, 4, 11, 11, 11, 11, 11, 4, 14, 14, 11, 4, 17, 0, 20, 20, 17, 17], "deprel": ["det", "amod", "amod", "nsubj", "punct", "nsubj", "cop", "advmod", "det", "amod", "acl:relcl", "case", "amod", "nmod", "punct", "cop", "root", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "2643-0", "target_tags": "The\\O crackling\\B calamari\\I salad\\I ,\\O which\\O is\\O usually\\O a\\O cheap\\O disaster\\O at\\O many\\O restaurants\\O ,\\O is\\O crispy\\O and\\O lightly\\O dressed\\O .\\O", "opinion_tags": "The\\O crackling\\O calamari\\O salad\\O ,\\O which\\O is\\O usually\\O a\\O cheap\\O disaster\\O at\\O many\\O restaurants\\O ,\\O is\\O crispy\\B and\\O lightly\\B dressed\\I .\\O", "sentiment": "positive"}]}, {"id": "2478", "sentence": "Just stick with the small dishes !", "postag": ["RB", "VB", "IN", "DT", "JJ", "NNS", "."], "head": [2, 0, 6, 6, 6, 2, 2], "deprel": ["advmod", "root", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "2478-0", "target_tags": "Just\\O stick\\O with\\O the\\O small\\O dishes\\B !\\O", "opinion_tags": "Just\\O stick\\O with\\O the\\O small\\B dishes\\O !\\O", "sentiment": "negative"}]}, {"id": "3304", "sentence": "My boyfriend had the New England Chowder it was good but I think the award should go to the Lobster Bisque .", "postag": ["PRP$", "NN", "VBD", "DT", "NNP", "NNP", "NNP", "PRP", "VBD", "JJ", "CC", "PRP", "VBP", "DT", "NN", "MD", "VB", "IN", "DT", "NNP", "NNP", "."], "head": [2, 3, 0, 7, 7, 7, 3, 10, 10, 3, 13, 13, 3, 15, 17, 17, 13, 21, 21, 21, 17, 3], "deprel": ["nmod:poss", "nsubj", "root", "det", "compound", "compound", "obj", "nsubj", "cop", "ccomp", "cc", "nsubj", "conj", "det", "nsubj", "aux", "ccomp", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "3304-0", "target_tags": "My\\O boyfriend\\O had\\O the\\O New\\B England\\I Chowder\\I it\\O was\\O good\\O but\\O I\\O think\\O the\\O award\\O should\\O go\\O to\\O the\\O Lobster\\O Bisque\\O .\\O", "opinion_tags": "My\\O boyfriend\\O had\\O the\\O New\\O England\\O Chowder\\O it\\O was\\O good\\B but\\O I\\O think\\O the\\O award\\O should\\O go\\O to\\O the\\O Lobster\\O Bisque\\O .\\O", "sentiment": "positive"}, {"uid": "3304-1", "target_tags": "My\\O boyfriend\\O had\\O the\\O New\\O England\\O Chowder\\O it\\O was\\O good\\O but\\O I\\O think\\O the\\O award\\O should\\O go\\O to\\O the\\O Lobster\\B Bisque\\I .\\O", "opinion_tags": "My\\O boyfriend\\O had\\O the\\O New\\O England\\O Chowder\\O it\\O was\\O good\\O but\\O I\\O think\\O the\\O award\\B should\\O go\\O to\\O the\\O Lobster\\O Bisque\\O .\\O", "sentiment": "positive"}]}, {"id": "1295", "sentence": "The counter service is bad .", "postag": ["DT", "NN", "NN", "VBZ", "JJ", "."], "head": [3, 3, 5, 5, 0, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "1295-0", "target_tags": "The\\O counter\\B service\\I is\\O bad\\O .\\O", "opinion_tags": "The\\O counter\\O service\\O is\\O bad\\B .\\O", "sentiment": "negative"}]}, {"id": "2548", "sentence": "Pizza and garlic knots are great as well , I order from them quite often and the delivery is always super quick !", "postag": ["NN", "CC", "NN", "NNS", "VBP", "JJ", "RB", "RB", ",", "PRP", "VBP", "IN", "PRP", "RB", "RB", "CC", "DT", "NN", "VBZ", "RB", "RB", "JJ", "."], "head": [6, 4, 4, 1, 6, 0, 6, 7, 6, 11, 6, 13, 11, 15, 11, 22, 18, 22, 22, 22, 22, 6, 6], "deprel": ["nsubj", "cc", "compound", "conj", "cop", "root", "advmod", "fixed", "punct", "nsubj", "parataxis", "case", "obl", "advmod", "advmod", "cc", "det", "nsubj", "cop", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "2548-0", "target_tags": "Pizza\\B and\\O garlic\\O knots\\O are\\O great\\O as\\O well\\O ,\\O I\\O order\\O from\\O them\\O quite\\O often\\O and\\O the\\O delivery\\O is\\O always\\O super\\O quick\\O !\\O", "opinion_tags": "Pizza\\O and\\O garlic\\O knots\\O are\\O great\\B as\\O well\\O ,\\O I\\O order\\O from\\O them\\O quite\\O often\\O and\\O the\\O delivery\\O is\\O always\\O super\\O quick\\O !\\O", "sentiment": "positive"}, {"uid": "2548-1", "target_tags": "Pizza\\O and\\O garlic\\O knots\\O are\\O great\\O as\\O well\\O ,\\O I\\O order\\O from\\O them\\O quite\\O often\\O and\\O the\\O delivery\\B is\\O always\\O super\\O quick\\O !\\O", "opinion_tags": "Pizza\\O and\\O garlic\\O knots\\O are\\O great\\O as\\O well\\O ,\\O I\\O order\\O from\\O them\\O quite\\O often\\O and\\O the\\O delivery\\O is\\O always\\O super\\B quick\\I !\\O", "sentiment": "positive"}, {"uid": "2548-2", "target_tags": "Pizza\\O and\\O garlic\\B knots\\I are\\O great\\O as\\O well\\O ,\\O I\\O order\\O from\\O them\\O quite\\O often\\O and\\O the\\O delivery\\O is\\O always\\O super\\O quick\\O !\\O", "opinion_tags": "Pizza\\O and\\O garlic\\O knots\\O are\\O great\\B as\\O well\\O ,\\O I\\O order\\O from\\O them\\O quite\\O often\\O and\\O the\\O delivery\\O is\\O always\\O super\\O quick\\O !\\O", "sentiment": "positive"}]}, {"id": "2595", "sentence": "THe back garden sitting area is very pleasant , where you can see their personal herb garden .", "postag": ["DT", "JJ", "NN", "NN", "NN", "VBZ", "RB", "JJ", ",", "WRB", "PRP", "MD", "VB", "PRP$", "JJ", "NN", "NN", "."], "head": [5, 3, 5, 5, 8, 8, 8, 0, 8, 13, 13, 13, 8, 17, 17, 17, 13, 8], "deprel": ["det", "amod", "compound", "compound", "nsubj", "cop", "advmod", "root", "punct", "mark", "nsubj", "aux", "advcl", "nmod:poss", "amod", "compound", "obj", "punct"], "triples": [{"uid": "2595-0", "target_tags": "THe\\O back\\B garden\\I sitting\\I area\\I is\\O very\\O pleasant\\O ,\\O where\\O you\\O can\\O see\\O their\\O personal\\O herb\\O garden\\O .\\O", "opinion_tags": "THe\\O back\\O garden\\O sitting\\O area\\O is\\O very\\O pleasant\\B ,\\O where\\O you\\O can\\O see\\O their\\O personal\\O herb\\O garden\\O .\\O", "sentiment": "positive"}]}, {"id": "1786", "sentence": "The spicy Tuna roll is huge and probably the best that I 've had at this price range .", "postag": ["DT", "JJ", "NN", "NN", "VBZ", "JJ", "CC", "RB", "DT", "JJS", "WDT", "PRP", "VBP", "VBN", "IN", "DT", "NN", "NN", "."], "head": [4, 4, 4, 6, 6, 0, 10, 10, 10, 6, 14, 14, 14, 10, 18, 18, 18, 14, 6], "deprel": ["det", "amod", "compound", "nsubj", "cop", "root", "cc", "advmod", "det", "conj", "obj", "nsubj", "aux", "acl:relcl", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "1786-0", "target_tags": "The\\O spicy\\O Tuna\\B roll\\I is\\O huge\\O and\\O probably\\O the\\O best\\O that\\O I\\O 've\\O had\\O at\\O this\\O price\\O range\\O .\\O", "opinion_tags": "The\\O spicy\\O Tuna\\O roll\\O is\\O huge\\B and\\O probably\\O the\\O best\\O that\\O I\\O 've\\O had\\O at\\O this\\O price\\O range\\O .\\O", "sentiment": "positive"}, {"uid": "1786-1", "target_tags": "The\\O spicy\\O Tuna\\O roll\\O is\\O huge\\O and\\O probably\\O the\\O best\\O that\\O I\\O 've\\O had\\O at\\O this\\O price\\B range\\I .\\O", "opinion_tags": "The\\O spicy\\O Tuna\\O roll\\O is\\O huge\\O and\\O probably\\O the\\O best\\B that\\O I\\O 've\\O had\\O at\\O this\\O price\\O range\\O .\\O", "sentiment": "positive"}]}, {"id": "395", "sentence": "The sake menu should not be overlooked !", "postag": ["DT", "NN", "NN", "MD", "RB", "VB", "VBN", "."], "head": [3, 3, 7, 7, 7, 7, 0, 7], "deprel": ["det", "compound", "nsubj:pass", "aux", "advmod", "aux:pass", "root", "punct"], "triples": [{"uid": "395-0", "target_tags": "The\\O sake\\B menu\\I should\\O not\\O be\\O overlooked\\O !\\O", "opinion_tags": "The\\O sake\\O menu\\O should\\O not\\O be\\O overlooked\\B !\\O", "sentiment": "positive"}]}, {"id": "3491", "sentence": "The food is authentic Italian - delicious !", "postag": ["DT", "NN", "VBZ", "JJ", "JJ", ",", "JJ", "."], "head": [2, 7, 7, 5, 7, 7, 0, 7], "deprel": ["det", "nsubj", "cop", "amod", "amod", "punct", "root", "punct"], "triples": [{"uid": "3491-0", "target_tags": "The\\O food\\B is\\O authentic\\O Italian\\O -\\O delicious\\O !\\O", "opinion_tags": "The\\O food\\O is\\O authentic\\B Italian\\I -\\O delicious\\B !\\O", "sentiment": "positive"}]}, {"id": "1006", "sentence": "I 've been to several places for Dim Sum and this has got to be the WORST .", "postag": ["PRP", "VBP", "VBN", "IN", "JJ", "NNS", "IN", "NNP", "NNP", "CC", "DT", "VBZ", "VBN", "TO", "VB", "DT", "JJS", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 13, 13, 13, 6, 17, 17, 17, 13, 6], "deprel": ["nsubj", "aux", "cop", "case", "amod", "root", "case", "compound", "nmod", "cc", "nsubj", "aux", "conj", "mark", "cop", "det", "xcomp", "punct"], "triples": [{"uid": "1006-0", "target_tags": "I\\O 've\\O been\\O to\\O several\\O places\\O for\\O Dim\\B Sum\\I and\\O this\\O has\\O got\\O to\\O be\\O the\\O WORST\\O .\\O", "opinion_tags": "I\\O 've\\O been\\O to\\O several\\O places\\O for\\O Dim\\O Sum\\O and\\O this\\O has\\O got\\O to\\O be\\O the\\O WORST\\B .\\I", "sentiment": "negative"}]}, {"id": "1459", "sentence": "The eggplant parmesan is also great , and my friend who grew up in Manhattan claims that no one serves a better baked ziti with meatsauce .", "postag": ["DT", "NN", "NN", "VBZ", "RB", "JJ", ",", "CC", "PRP$", "NN", "WP", "VBD", "RP", "IN", "NNP", "VBZ", "IN", "DT", "NN", "VBZ", "DT", "JJR", "VBN", "NN", "IN", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 16, 16, 10, 16, 12, 10, 12, 15, 12, 6, 20, 19, 20, 16, 24, 24, 24, 20, 26, 20, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "punct", "cc", "nmod:poss", "nsubj", "nsubj", "acl:relcl", "compound:prt", "case", "obl", "conj", "mark", "det", "nsubj", "ccomp", "det", "amod", "amod", "obj", "case", "obl", "punct"], "triples": [{"uid": "1459-0", "target_tags": "The\\O eggplant\\B parmesan\\I is\\O also\\O great\\O ,\\O and\\O my\\O friend\\O who\\O grew\\O up\\O in\\O Manhattan\\O claims\\O that\\O no\\O one\\O serves\\O a\\O better\\O baked\\O ziti\\O with\\O meatsauce\\O .\\O", "opinion_tags": "The\\O eggplant\\O parmesan\\O is\\O also\\O great\\B ,\\O and\\O my\\O friend\\O who\\O grew\\O up\\O in\\O Manhattan\\O claims\\O that\\O no\\O one\\O serves\\O a\\O better\\O baked\\O ziti\\O with\\O meatsauce\\O .\\O", "sentiment": "positive"}, {"uid": "1459-1", "target_tags": "The\\O eggplant\\O parmesan\\O is\\O also\\O great\\O ,\\O and\\O my\\O friend\\O who\\O grew\\O up\\O in\\O Manhattan\\O claims\\O that\\O no\\O one\\O serves\\O a\\O better\\O baked\\B ziti\\I with\\I meatsauce\\I .\\O", "opinion_tags": "The\\O eggplant\\O parmesan\\O is\\O also\\O great\\O ,\\O and\\O my\\O friend\\O who\\O grew\\O up\\O in\\O Manhattan\\O claims\\O that\\O no\\O one\\O serves\\O a\\O better\\B baked\\O ziti\\O with\\O meatsauce\\O .\\O", "sentiment": "positive"}]}, {"id": "717", "sentence": "We wo n't go to this place again for a good meal .", "postag": ["PRP", "MD", "RB", "VB", "IN", "DT", "NN", "RB", "IN", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 4, 12, 12, 12, 4, 4], "deprel": ["nsubj", "aux", "advmod", "root", "case", "det", "obl", "advmod", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "717-0", "target_tags": "We\\O wo\\O n't\\O go\\O to\\O this\\O place\\O again\\O for\\O a\\O good\\O meal\\B .\\O", "opinion_tags": "We\\O wo\\O n't\\O go\\O to\\O this\\O place\\O again\\O for\\O a\\O good\\B meal\\O .\\O", "sentiment": "negative"}]}, {"id": "94", "sentence": "Excellent atmosphere , delicious dishes good and friendly service .", "postag": ["JJ", "NN", ",", "JJ", "NNS", "JJ", "CC", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 2, 9, 9, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "conj", "cc", "amod", "conj", "punct"], "triples": [{"uid": "94-0", "target_tags": "Excellent\\O atmosphere\\B ,\\O delicious\\O dishes\\O good\\O and\\O friendly\\O service\\O .\\O", "opinion_tags": "Excellent\\B atmosphere\\O ,\\O delicious\\O dishes\\O good\\O and\\O friendly\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "94-1", "target_tags": "Excellent\\O atmosphere\\O ,\\O delicious\\O dishes\\B good\\O and\\O friendly\\O service\\O .\\O", "opinion_tags": "Excellent\\O atmosphere\\O ,\\O delicious\\B dishes\\O good\\O and\\O friendly\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "94-2", "target_tags": "Excellent\\O atmosphere\\O ,\\O delicious\\O dishes\\O good\\O and\\O friendly\\O service\\B .\\O", "opinion_tags": "Excellent\\O atmosphere\\O ,\\O delicious\\O dishes\\O good\\B and\\O friendly\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "1482", "sentence": "The plain slice is great and if you get toppings , the whole slice is topped with them , not sparsely sprinkled on like some places .", "postag": ["DT", "JJ", "NN", "VBZ", "JJ", "CC", "IN", "PRP", "VBP", "NNS", ",", "DT", "JJ", "NN", "VBZ", "VBN", "IN", "PRP", ",", "RB", "RB", "VBN", "IN", "IN", "DT", "NNS", "."], "head": [3, 3, 5, 5, 0, 16, 9, 9, 16, 9, 16, 14, 14, 16, 16, 5, 18, 16, 22, 22, 22, 16, 22, 26, 26, 22, 5], "deprel": ["det", "amod", "nsubj", "cop", "root", "cc", "mark", "nsubj", "advcl", "obj", "punct", "det", "amod", "nsubj:pass", "aux:pass", "conj", "case", "obl", "punct", "advmod", "advmod", "conj", "obl", "case", "det", "obl", "punct"], "triples": [{"uid": "1482-0", "target_tags": "The\\O plain\\B slice\\I is\\O great\\O and\\O if\\O you\\O get\\O toppings\\O ,\\O the\\O whole\\O slice\\O is\\O topped\\O with\\O them\\O ,\\O not\\O sparsely\\O sprinkled\\O on\\O like\\O some\\O places\\O .\\O", "opinion_tags": "The\\O plain\\O slice\\O is\\O great\\B and\\O if\\O you\\O get\\O toppings\\O ,\\O the\\O whole\\O slice\\O is\\O topped\\O with\\O them\\O ,\\O not\\O sparsely\\O sprinkled\\O on\\O like\\O some\\O places\\O .\\O", "sentiment": "positive"}]}, {"id": "1677", "sentence": "The only positive was the wait staff , which was prompt , knowledgable , and likeable .", "postag": ["DT", "JJ", "JJ", "VBD", "DT", "NN", "NN", ",", "WDT", "VBD", "JJ", ",", "JJ", ",", "CC", "JJ", "."], "head": [3, 3, 7, 7, 7, 7, 0, 7, 11, 11, 7, 13, 11, 16, 16, 11, 7], "deprel": ["det", "amod", "nsubj", "cop", "det", "compound", "root", "punct", "nsubj", "cop", "acl:relcl", "punct", "conj", "punct", "cc", "conj", "punct"], "triples": [{"uid": "1677-0", "target_tags": "The\\O only\\O positive\\O was\\O the\\O wait\\B staff\\I ,\\O which\\O was\\O prompt\\O ,\\O knowledgable\\O ,\\O and\\O likeable\\O .\\O", "opinion_tags": "The\\O only\\O positive\\B was\\O the\\O wait\\O staff\\O ,\\O which\\O was\\O prompt\\B ,\\O knowledgable\\B ,\\O and\\O likeable\\B .\\O", "sentiment": "positive"}]}, {"id": "2247", "sentence": "In summer-eat outside on a terrace ( another great feature of <PERSON><PERSON> ) ! ! !", "postag": ["IN", "JJ", "RB", "IN", "DT", "NN", "-LRB-", "DT", "JJ", "NN", "IN", "NNP", "-RRB-", ".", ".", "."], "head": [2, 3, 0, 6, 6, 3, 10, 10, 10, 6, 12, 10, 10, 10, 10, 10], "deprel": ["case", "obl", "root", "case", "det", "obl", "punct", "det", "amod", "appos", "case", "nmod", "punct", "punct", "punct", "punct"], "triples": [{"uid": "2247-0", "target_tags": "In\\O summer-eat\\O outside\\O on\\O a\\O terrace\\B (\\O another\\O great\\O feature\\O of\\O Suan\\O )\\O !\\O !\\O !\\O", "opinion_tags": "In\\O summer-eat\\O outside\\O on\\O a\\O terrace\\O (\\O another\\O great\\B feature\\O of\\O Suan\\O )\\O !\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "874", "sentence": "I went at 6:00 PM specifically for the pre-theater menu ( $ 19.95 for roasted tomato soup with chevre , steak frites , creme brulee ) and it was marvelous .", "postag": ["PRP", "VBD", "IN", "CD", "NN", "RB", "IN", "DT", "JJ", "NN", "-LRB-", "$", "CD", "IN", "VBN", "NN", "NN", "IN", "NN", ",", "NN", "NNS", ",", "NN", "NN", "-RRB-", "CC", "PRP", "VBD", "JJ", "."], "head": [2, 0, 5, 5, 2, 2, 10, 10, 10, 2, 12, 10, 12, 17, 17, 17, 12, 19, 17, 22, 22, 12, 25, 25, 12, 12, 30, 30, 30, 2, 2], "deprel": ["nsubj", "root", "case", "nummod", "obl", "advmod", "case", "det", "amod", "obl", "punct", "appos", "nummod", "case", "amod", "compound", "nmod", "case", "nmod", "punct", "compound", "conj", "punct", "compound", "conj", "punct", "cc", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "874-0", "target_tags": "I\\O went\\O at\\O 6:00\\O PM\\O specifically\\O for\\O the\\O pre-theater\\B menu\\I (\\O $\\O 19.95\\O for\\O roasted\\O tomato\\O soup\\O with\\O chevre\\O ,\\O steak\\O frites\\O ,\\O creme\\O brulee\\O )\\O and\\O it\\O was\\O marvelous\\O .\\O", "opinion_tags": "I\\O went\\O at\\O 6:00\\O PM\\O specifically\\O for\\O the\\O pre-theater\\O menu\\O (\\O $\\O 19.95\\O for\\O roasted\\O tomato\\O soup\\O with\\O chevre\\O ,\\O steak\\O frites\\O ,\\O creme\\O brulee\\O )\\O and\\O it\\O was\\O marvelous\\B .\\O", "sentiment": "positive"}, {"uid": "874-1", "target_tags": "I\\O went\\O at\\O 6:00\\O PM\\O specifically\\O for\\O the\\O pre-theater\\O menu\\O (\\O $\\O 19.95\\O for\\O roasted\\B tomato\\I soup\\I with\\I chevre\\I ,\\O steak\\O frites\\O ,\\O creme\\O brulee\\O )\\O and\\O it\\O was\\O marvelous\\O .\\O", "opinion_tags": "I\\O went\\O at\\O 6:00\\O PM\\O specifically\\O for\\O the\\O pre-theater\\O menu\\O (\\O $\\O 19.95\\O for\\O roasted\\O tomato\\O soup\\O with\\O chevre\\O ,\\O steak\\O frites\\O ,\\O creme\\O brulee\\O )\\O and\\O it\\O was\\O marvelous\\B .\\O", "sentiment": "positive"}, {"uid": "874-2", "target_tags": "I\\O went\\O at\\O 6:00\\O PM\\O specifically\\O for\\O the\\O pre-theater\\O menu\\O (\\O $\\O 19.95\\O for\\O roasted\\O tomato\\O soup\\O with\\O chevre\\O ,\\O steak\\B frites\\I ,\\O creme\\O brulee\\O )\\O and\\O it\\O was\\O marvelous\\O .\\O", "opinion_tags": "I\\O went\\O at\\O 6:00\\O PM\\O specifically\\O for\\O the\\O pre-theater\\O menu\\O (\\O $\\O 19.95\\O for\\O roasted\\O tomato\\O soup\\O with\\O chevre\\O ,\\O steak\\O frites\\O ,\\O creme\\O brulee\\O )\\O and\\O it\\O was\\O marvelous\\B .\\O", "sentiment": "positive"}, {"uid": "874-3", "target_tags": "I\\O went\\O at\\O 6:00\\O PM\\O specifically\\O for\\O the\\O pre-theater\\O menu\\O (\\O $\\O 19.95\\O for\\O roasted\\O tomato\\O soup\\O with\\O chevre\\O ,\\O steak\\O frites\\O ,\\O creme\\B brulee\\I )\\O and\\O it\\O was\\O marvelous\\O .\\O", "opinion_tags": "I\\O went\\O at\\O 6:00\\O PM\\O specifically\\O for\\O the\\O pre-theater\\O menu\\O (\\O $\\O 19.95\\O for\\O roasted\\O tomato\\O soup\\O with\\O chevre\\O ,\\O steak\\O frites\\O ,\\O creme\\O brulee\\O )\\O and\\O it\\O was\\O marvelous\\B .\\O", "sentiment": "positive"}]}, {"id": "3198", "sentence": "We had <PERSON> 's special fried fish and it was amazing .", "postag": ["PRP", "VBD", "NNP", "POS", "JJ", "JJ", "NN", "CC", "PRP", "VBD", "JJ", "."], "head": [2, 0, 7, 3, 7, 7, 2, 11, 11, 11, 2, 2], "deprel": ["nsubj", "root", "nmod:poss", "case", "amod", "amod", "obj", "cc", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "3198-0", "target_tags": "We\\O had\\O Pam\\B 's\\I special\\I fried\\I fish\\I and\\O it\\O was\\O amazing\\O .\\O", "opinion_tags": "We\\O had\\O Pam\\O 's\\O special\\O fried\\O fish\\O and\\O it\\O was\\O amazing\\B .\\O", "sentiment": "positive"}]}, {"id": "973", "sentence": "Ambiance and music funky , which I enjoy .", "postag": ["NN", "CC", "NN", "JJ", ",", "WDT", "PRP", "VBP", "."], "head": [0, 3, 1, 1, 1, 8, 8, 1, 1], "deprel": ["root", "cc", "conj", "amod", "punct", "obj", "nsubj", "acl:relcl", "punct"], "triples": [{"uid": "973-0", "target_tags": "Ambiance\\B and\\O music\\O funky\\O ,\\O which\\O I\\O enjoy\\O .\\O", "opinion_tags": "Ambiance\\O and\\O music\\O funky\\B ,\\O which\\O I\\O enjoy\\B .\\O", "sentiment": "positive"}, {"uid": "973-1", "target_tags": "Ambiance\\O and\\O music\\B funky\\O ,\\O which\\O I\\O enjoy\\O .\\O", "opinion_tags": "Ambiance\\O and\\O music\\O funky\\B ,\\O which\\O I\\O enjoy\\B .\\O", "sentiment": "positive"}]}, {"id": "1115", "sentence": "Please try the Filet Mignon , its just the most tender piece ever .", "postag": ["UH", "VB", "DT", "NNP", "NNP", ",", "PRP$", "RB", "DT", "RBS", "JJ", "NN", "RB", "."], "head": [2, 0, 4, 2, 4, 12, 12, 12, 12, 11, 12, 4, 12, 2], "deprel": ["discourse", "root", "det", "obj", "flat", "punct", "nmod:poss", "advmod", "det", "advmod", "amod", "appos", "advmod", "punct"], "triples": [{"uid": "1115-0", "target_tags": "Please\\O try\\O the\\O Filet\\B Mignon\\I ,\\O its\\O just\\O the\\O most\\O tender\\O piece\\O ever\\O .\\O", "opinion_tags": "Please\\O try\\O the\\O Filet\\O Mignon\\O ,\\O its\\O just\\O the\\O most\\O tender\\B piece\\O ever\\O .\\O", "sentiment": "positive"}]}, {"id": "579", "sentence": "We both opted for a pasta dish and they were served timely and fresh .", "postag": ["PRP", "DT", "VBD", "IN", "DT", "NN", "NN", "CC", "PRP", "VBD", "VBN", "JJ", "CC", "JJ", "."], "head": [3, 1, 0, 7, 7, 7, 3, 11, 11, 11, 3, 11, 14, 12, 3], "deprel": ["nsubj", "det", "root", "case", "det", "compound", "obl", "cc", "nsubj:pass", "aux:pass", "conj", "xcomp", "cc", "conj", "punct"], "triples": [{"uid": "579-0", "target_tags": "We\\O both\\O opted\\O for\\O a\\O pasta\\B dish\\I and\\O they\\O were\\O served\\O timely\\O and\\O fresh\\O .\\O", "opinion_tags": "We\\O both\\O opted\\O for\\O a\\O pasta\\O dish\\O and\\O they\\O were\\O served\\B timely\\I and\\O fresh\\B .\\O", "sentiment": "positive"}, {"uid": "579-1", "target_tags": "We\\O both\\O opted\\O for\\O a\\O pasta\\O dish\\O and\\O they\\O were\\O served\\B timely\\O and\\O fresh\\O .\\O", "opinion_tags": "We\\O both\\O opted\\O for\\O a\\O pasta\\O dish\\O and\\O they\\O were\\O served\\O timely\\B and\\O fresh\\B .\\O", "sentiment": "positive"}]}, {"id": "2588", "sentence": "First off , the waitress was completely unattentive the 2 times we saw her ( odd in a restaurant with 6 tables ) and got our order wrong .", "postag": ["RB", "RB", ",", "DT", "NN", "VBD", "RB", "JJ", "DT", "CD", "NNS", "PRP", "VBD", "PRP", "-LRB-", "JJ", "IN", "DT", "NN", "IN", "CD", "NNS", "-RRB-", "CC", "VBD", "PRP$", "NN", "JJ", "."], "head": [2, 8, 8, 5, 8, 8, 8, 0, 11, 11, 8, 13, 8, 13, 16, 8, 19, 19, 16, 22, 22, 16, 16, 25, 8, 27, 25, 25, 8], "deprel": ["advmod", "advmod", "punct", "det", "nsubj", "cop", "advmod", "root", "det", "nummod", "obl:tmod", "nsubj", "parataxis", "obj", "punct", "parataxis", "case", "det", "obl", "case", "nummod", "obl", "punct", "cc", "conj", "nmod:poss", "obj", "xcomp", "punct"], "triples": [{"uid": "2588-0", "target_tags": "First\\O off\\O ,\\O the\\O waitress\\B was\\O completely\\O unattentive\\O the\\O 2\\O times\\O we\\O saw\\O her\\O (\\O odd\\O in\\O a\\O restaurant\\O with\\O 6\\O tables\\O )\\O and\\O got\\O our\\O order\\O wrong\\O .\\O", "opinion_tags": "First\\O off\\O ,\\O the\\O waitress\\O was\\O completely\\O unattentive\\B the\\O 2\\O times\\O we\\O saw\\O her\\O (\\O odd\\O in\\O a\\O restaurant\\O with\\O 6\\O tables\\O )\\O and\\O got\\O our\\O order\\O wrong\\O .\\O", "sentiment": "negative"}]}, {"id": "2892", "sentence": "The staff has always been attentive and kind , and I 've always been amazed at how they 've handled all the various different group sizes that come in .", "postag": ["DT", "NN", "VBZ", "RB", "VBN", "JJ", "CC", "JJ", ",", "CC", "PRP", "VBP", "RB", "VBN", "VBN", "IN", "WRB", "PRP", "VBP", "VBN", "PDT", "DT", "JJ", "JJ", "NN", "NNS", "WDT", "VBP", "RB", "."], "head": [2, 6, 6, 6, 6, 0, 8, 6, 15, 15, 15, 15, 15, 15, 6, 20, 20, 20, 20, 15, 26, 26, 26, 26, 26, 20, 28, 26, 28, 6], "deprel": ["det", "nsubj", "aux", "advmod", "cop", "root", "cc", "conj", "punct", "cc", "nsubj:pass", "aux", "advmod", "aux:pass", "conj", "mark", "mark", "nsubj", "aux", "advcl", "det:predet", "det", "amod", "amod", "compound", "obj", "nsubj", "acl:relcl", "advmod", "punct"], "triples": [{"uid": "2892-0", "target_tags": "The\\O staff\\B has\\O always\\O been\\O attentive\\O and\\O kind\\O ,\\O and\\O I\\O 've\\O always\\O been\\O amazed\\O at\\O how\\O they\\O 've\\O handled\\O all\\O the\\O various\\O different\\O group\\O sizes\\O that\\O come\\O in\\O .\\O", "opinion_tags": "The\\O staff\\O has\\O always\\O been\\O attentive\\B and\\O kind\\B ,\\O and\\O I\\O 've\\O always\\O been\\O amazed\\B at\\O how\\O they\\O 've\\O handled\\O all\\O the\\O various\\O different\\O group\\O sizes\\O that\\O come\\O in\\O .\\O", "sentiment": "positive"}]}, {"id": "920", "sentence": "The food was good .", "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "920-0", "target_tags": "The\\O food\\B was\\O good\\O .\\O", "opinion_tags": "The\\O food\\O was\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "956", "sentence": "The food was exceptional .", "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "956-0", "target_tags": "The\\O food\\B was\\O exceptional\\O .\\O", "opinion_tags": "The\\O food\\O was\\O exceptional\\B .\\O", "sentiment": "positive"}]}, {"id": "3086", "sentence": "While the place is not a hotspot hangout , the drinks are unique and pack a lot of bang for the buck .", "postag": ["IN", "DT", "NN", "VBZ", "RB", "DT", "NN", "NN", ",", "DT", "NNS", "VBP", "JJ", "CC", "VB", "DT", "NN", "IN", "NN", "IN", "DT", "NN", "."], "head": [8, 3, 8, 8, 8, 8, 8, 13, 13, 11, 13, 13, 0, 15, 13, 17, 15, 19, 17, 22, 22, 15, 13], "deprel": ["mark", "det", "nsubj", "cop", "advmod", "det", "compound", "advcl", "punct", "det", "nsubj", "cop", "root", "cc", "conj", "det", "obj", "case", "nmod", "case", "det", "obl", "punct"], "triples": [{"uid": "3086-0", "target_tags": "While\\O the\\O place\\O is\\O not\\O a\\O hotspot\\O hangout\\O ,\\O the\\O drinks\\B are\\O unique\\O and\\O pack\\O a\\O lot\\O of\\O bang\\O for\\O the\\O buck\\O .\\O", "opinion_tags": "While\\O the\\O place\\O is\\O not\\O a\\O hotspot\\O hangout\\O ,\\O the\\O drinks\\O are\\O unique\\B and\\O pack\\O a\\O lot\\O of\\O bang\\O for\\O the\\O buck\\O .\\O", "sentiment": "positive"}]}, {"id": "1610", "sentence": "The pizza was great .", "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "1610-0", "target_tags": "The\\O pizza\\B was\\O great\\O .\\O", "opinion_tags": "The\\O pizza\\O was\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "694", "sentence": "I had the duck breast special on my last visit and it was incredible .", "postag": ["PRP", "VBD", "DT", "NN", "NN", "JJ", "IN", "PRP$", "JJ", "NN", "CC", "PRP", "VBD", "JJ", "."], "head": [2, 0, 5, 5, 6, 2, 10, 10, 10, 2, 14, 14, 14, 2, 2], "deprel": ["nsubj", "root", "det", "compound", "compound", "obj", "case", "nmod:poss", "amod", "obl", "cc", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "694-0", "target_tags": "I\\O had\\O the\\O duck\\B breast\\I special\\I on\\O my\\O last\\O visit\\O and\\O it\\O was\\O incredible\\O .\\O", "opinion_tags": "I\\O had\\O the\\O duck\\O breast\\O special\\O on\\O my\\O last\\O visit\\O and\\O it\\O was\\O incredible\\B .\\O", "sentiment": "positive"}]}, {"id": "328", "sentence": "Went on a 3 day oyster binge , with <PERSON> bringing up the closing , and I am so glad this was the place it O trip ended , because it was so great !", "postag": ["VBD", "IN", "DT", "CD", "NN", "NN", "NN", ",", "IN", "NNP", "VBG", "RP", "DT", "NN", ",", "CC", "PRP", "VBP", "RB", "JJ", "DT", "VBD", "DT", "NN", "PRP", "IN", "NN", "VBN", ",", "IN", "PRP", "VBD", "RB", "JJ", "."], "head": [0, 7, 7, 5, 7, 7, 1, 1, 11, 11, 1, 11, 14, 11, 20, 20, 20, 20, 20, 1, 24, 24, 24, 20, 28, 27, 24, 24, 34, 34, 34, 34, 34, 24, 1], "deprel": ["root", "case", "det", "nummod", "compound", "compound", "obl", "punct", "mark", "nsubj", "advcl", "compound:prt", "det", "obj", "punct", "cc", "nsubj", "cop", "advmod", "conj", "nsubj", "cop", "det", "ccomp", "nsubj", "case", "nmod", "acl:relcl", "punct", "mark", "nsubj", "cop", "advmod", "advcl", "punct"], "triples": [{"uid": "328-0", "target_tags": "Went\\O on\\O a\\O 3\\O day\\O oyster\\O binge\\O ,\\O with\\O Fish\\O bringing\\O up\\O the\\O closing\\O ,\\O and\\O I\\O am\\O so\\O glad\\O this\\O was\\O the\\O place\\B it\\O O\\O trip\\O ended\\O ,\\O because\\O it\\O was\\O so\\O great\\O !\\O", "opinion_tags": "Went\\O on\\O a\\O 3\\O day\\O oyster\\O binge\\O ,\\O with\\O Fish\\O bringing\\O up\\O the\\O closing\\O ,\\O and\\O I\\O am\\O so\\O glad\\O this\\O was\\O the\\O place\\O it\\O O\\O trip\\O ended\\O ,\\O because\\O it\\O was\\O so\\O great\\B !\\O", "sentiment": "positive"}]}, {"id": "2356", "sentence": "And the fried clams had just enough kick to them to make 'em worth eating .", "postag": ["CC", "DT", "JJ", "NNS", "VBD", "RB", "JJ", "NN", "IN", "PRP", "TO", "VB", "PRP", "JJ", "NN", "."], "head": [5, 4, 4, 5, 0, 7, 8, 5, 10, 8, 12, 5, 12, 12, 14, 5], "deprel": ["cc", "det", "amod", "nsubj", "root", "advmod", "amod", "obj", "case", "nmod", "mark", "advcl", "obj", "xcomp", "obj", "punct"], "triples": [{"uid": "2356-0", "target_tags": "And\\O the\\O fried\\B clams\\I had\\O just\\O enough\\O kick\\O to\\O them\\O to\\O make\\O 'em\\O worth\\O eating\\O .\\O", "opinion_tags": "And\\O the\\O fried\\O clams\\O had\\O just\\O enough\\B kick\\O to\\O them\\O to\\O make\\O 'em\\O worth\\O eating\\O .\\O", "sentiment": "positive"}]}, {"id": "1089", "sentence": "The sandwiches are dry , tasteless and way overpriced .", "postag": ["DT", "NNS", "VBP", "JJ", ",", "JJ", "CC", "RB", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "conj", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "1089-0", "target_tags": "The\\O sandwiches\\B are\\O dry\\O ,\\O tasteless\\O and\\O way\\O overpriced\\O .\\O", "opinion_tags": "The\\O sandwiches\\O are\\O dry\\B ,\\O tasteless\\B and\\O way\\O overpriced\\B .\\O", "sentiment": "negative"}]}, {"id": "511", "sentence": "Moules were excellent , lobster ravioli was VERY salty !", "postag": ["NNS", "VBD", "JJ", ",", "NN", "NN", "VBD", "RB", "JJ", "."], "head": [3, 3, 0, 3, 6, 9, 9, 9, 3, 3], "deprel": ["nsubj", "cop", "root", "punct", "compound", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "511-0", "target_tags": "Moules\\B were\\O excellent\\O ,\\O lobster\\O ravioli\\O was\\O VERY\\O salty\\O !\\O", "opinion_tags": "Moules\\O were\\O excellent\\B ,\\O lobster\\O ravioli\\O was\\O VERY\\O salty\\O !\\O", "sentiment": "positive"}, {"uid": "511-1", "target_tags": "Moules\\O were\\O excellent\\O ,\\O lobster\\B ravioli\\I was\\O VERY\\O salty\\O !\\O", "opinion_tags": "Moules\\O were\\O excellent\\O ,\\O lobster\\O ravioli\\O was\\O VERY\\O salty\\B !\\O", "sentiment": "negative"}]}, {"id": "685", "sentence": "Not sure where the previous reviewer , lonk , dined , but <PERSON> is in a great neighborhood and has great food !", "postag": ["RB", "JJ", "WRB", "DT", "JJ", "NN", ",", "NNP", ",", "VBD", ",", "CC", "NNP", "VBZ", "IN", "DT", "JJ", "NN", "CC", "VBZ", "JJ", "NN", "."], "head": [2, 0, 10, 6, 6, 10, 8, 6, 10, 2, 14, 18, 18, 18, 18, 18, 18, 10, 20, 18, 22, 20, 2], "deprel": ["advmod", "root", "mark", "det", "amod", "nsubj", "punct", "conj", "punct", "ccomp", "punct", "cc", "nsubj", "cop", "case", "det", "amod", "conj", "cc", "conj", "amod", "obj", "punct"], "triples": [{"uid": "685-0", "target_tags": "Not\\O sure\\O where\\O the\\O previous\\O reviewer\\O ,\\O lonk\\O ,\\O dined\\O ,\\O but\\O Saul\\O is\\O in\\O a\\O great\\O neighborhood\\B and\\O has\\O great\\O food\\O !\\O", "opinion_tags": "Not\\O sure\\O where\\O the\\O previous\\O reviewer\\O ,\\O lonk\\O ,\\O dined\\O ,\\O but\\O Saul\\O is\\O in\\O a\\O great\\B neighborhood\\O and\\O has\\O great\\O food\\O !\\O", "sentiment": "positive"}, {"uid": "685-1", "target_tags": "Not\\O sure\\O where\\O the\\O previous\\O reviewer\\O ,\\O lonk\\O ,\\O dined\\O ,\\O but\\O Saul\\O is\\O in\\O a\\O great\\O neighborhood\\O and\\O has\\O great\\O food\\B !\\O", "opinion_tags": "Not\\O sure\\O where\\O the\\O previous\\O reviewer\\O ,\\O lonk\\O ,\\O dined\\O ,\\O but\\O Saul\\O is\\O in\\O a\\O great\\O neighborhood\\O and\\O has\\O great\\B food\\O !\\O", "sentiment": "positive"}]}, {"id": "90", "sentence": "The Prix Fixe menu is worth every penny and you get more than enough ( both in quantity AND quality ) .", "postag": ["DT", "NNP", "NNP", "NN", "VBZ", "JJ", "DT", "NN", "CC", "PRP", "VBP", "JJR", "IN", "JJ", "-LRB-", "CC", "IN", "NN", "CC", "NN", "-RRB-", "."], "head": [4, 3, 4, 6, 6, 0, 8, 6, 11, 11, 6, 14, 12, 11, 18, 18, 18, 11, 20, 18, 18, 6], "deprel": ["det", "compound", "compound", "nsubj", "cop", "root", "det", "obj", "cc", "nsubj", "conj", "advmod", "fixed", "obj", "punct", "cc:preconj", "case", "obl", "cc", "conj", "punct", "punct"], "triples": [{"uid": "90-0", "target_tags": "The\\O Prix\\B Fixe\\I menu\\I is\\O worth\\O every\\O penny\\O and\\O you\\O get\\O more\\O than\\O enough\\O (\\O both\\O in\\O quantity\\O AND\\O quality\\O )\\O .\\O", "opinion_tags": "The\\O Prix\\O Fixe\\O menu\\O is\\O worth\\B every\\O penny\\O and\\O you\\O get\\O more\\O than\\O enough\\O (\\O both\\O in\\O quantity\\O AND\\O quality\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "90-1", "target_tags": "The\\O Prix\\O Fixe\\O menu\\O is\\O worth\\O every\\O penny\\O and\\O you\\O get\\O more\\O than\\O enough\\O (\\O both\\O in\\O quantity\\B AND\\O quality\\O )\\O .\\O", "opinion_tags": "The\\O Prix\\O Fixe\\O menu\\O is\\O worth\\O every\\O penny\\O and\\O you\\O get\\O more\\B than\\I enough\\I (\\O both\\O in\\O quantity\\O AND\\O quality\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "90-2", "target_tags": "The\\O Prix\\O Fixe\\O menu\\O is\\O worth\\O every\\O penny\\O and\\O you\\O get\\O more\\O than\\O enough\\O (\\O both\\O in\\O quantity\\O AND\\O quality\\B )\\O .\\O", "opinion_tags": "The\\O Prix\\O Fixe\\O menu\\O is\\O worth\\O every\\O penny\\O and\\O you\\O get\\O more\\B than\\I enough\\I (\\O both\\O in\\O quantity\\O AND\\O quality\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "1627", "sentence": "Volare virgins or weekly regulars , everyone gets treated the same and you ca n't ask for more than that when the service is this friendly .", "postag": ["NN", "NNS", "CC", "JJ", "NNS", ",", "NN", "VBZ", "VBN", "DT", "JJ", "CC", "PRP", "MD", "RB", "VB", "IN", "JJR", "IN", "DT", "WRB", "DT", "NN", "VBZ", "DT", "JJ", "."], "head": [2, 0, 5, 5, 2, 2, 9, 9, 2, 11, 9, 16, 16, 16, 16, 9, 20, 16, 20, 16, 26, 23, 26, 26, 26, 16, 9], "deprel": ["compound", "root", "cc", "amod", "conj", "punct", "nsubj:pass", "aux:pass", "parataxis", "det", "obj", "cc", "nsubj", "aux", "advmod", "conj", "case", "obl", "case", "obl", "mark", "det", "nsubj", "cop", "obl:npmod", "advcl", "punct"], "triples": [{"uid": "1627-0", "target_tags": "Volare\\O virgins\\O or\\O weekly\\O regulars\\O ,\\O everyone\\O gets\\O treated\\O the\\O same\\O and\\O you\\O ca\\O n't\\O ask\\O for\\O more\\O than\\O that\\O when\\O the\\O service\\B is\\O this\\O friendly\\O .\\O", "opinion_tags": "Volare\\O virgins\\O or\\O weekly\\O regulars\\O ,\\O everyone\\O gets\\O treated\\O the\\O same\\O and\\O you\\O ca\\O n't\\O ask\\O for\\O more\\O than\\O that\\O when\\O the\\O service\\O is\\O this\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "348", "sentence": "Great sushi experience .", "postag": ["JJ", "NN", "NN", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "compound", "root", "punct"], "triples": [{"uid": "348-0", "target_tags": "Great\\O sushi\\B experience\\O .\\O", "opinion_tags": "Great\\B sushi\\O experience\\O .\\O", "sentiment": "positive"}]}, {"id": "1909", "sentence": "The entire dining experience was wonderful !", "postag": ["DT", "JJ", "NN", "NN", "VBD", "JJ", "."], "head": [4, 4, 4, 6, 6, 0, 6], "deprel": ["det", "amod", "compound", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "1909-0", "target_tags": "The\\O entire\\O dining\\B experience\\I was\\O wonderful\\O !\\O", "opinion_tags": "The\\O entire\\O dining\\O experience\\O was\\O wonderful\\B !\\O", "sentiment": "positive"}]}, {"id": "1583", "sentence": "I LOVED THE SHOWS .", "postag": ["PRP", "VBD", "DT", "NNS", "."], "head": [2, 0, 4, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "punct"], "triples": [{"uid": "1583-0", "target_tags": "I\\O LOVED\\O THE\\O SHOWS\\B .\\O", "opinion_tags": "I\\O LOVED\\B THE\\O SHOWS\\O .\\O", "sentiment": "positive"}]}, {"id": "3291", "sentence": "Food is great .", "postag": ["NN", "VBZ", "JJ", "."], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"], "triples": [{"uid": "3291-0", "target_tags": "Food\\B is\\O great\\O .\\O", "opinion_tags": "Food\\O is\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "1614", "sentence": "Going to Volare is like going to your favorite aunt 's house for dinner , assuming that your aunt is a great Italian cook .", "postag": ["VBG", "IN", "NNP", "VBZ", "IN", "VBG", "IN", "PRP$", "JJ", "NN", "POS", "NN", "IN", "NN", ",", "VBG", "IN", "PRP$", "NN", "VBZ", "DT", "JJ", "JJ", "NN", "."], "head": [4, 3, 1, 0, 6, 4, 12, 10, 10, 12, 10, 6, 14, 6, 16, 6, 24, 19, 24, 24, 24, 24, 24, 16, 4], "deprel": ["csubj", "case", "obl", "root", "mark", "advcl", "case", "nmod:poss", "amod", "nmod:poss", "case", "obl", "case", "obl", "punct", "advcl", "mark", "nmod:poss", "nsubj", "cop", "det", "amod", "amod", "ccomp", "punct"], "triples": [{"uid": "1614-0", "target_tags": "Going\\O to\\O Volare\\O is\\O like\\O going\\O to\\O your\\O favorite\\O aunt\\O 's\\O house\\O for\\O dinner\\B ,\\O assuming\\O that\\O your\\O aunt\\O is\\O a\\O great\\O Italian\\O cook\\O .\\O", "opinion_tags": "Going\\O to\\O Volare\\O is\\O like\\O going\\O to\\O your\\O favorite\\O aunt\\O 's\\O house\\O for\\O dinner\\O ,\\O assuming\\O that\\O your\\O aunt\\O is\\O a\\O great\\B Italian\\O cook\\O .\\O", "sentiment": "neutral"}]}, {"id": "3156", "sentence": "Do n't expect to sit down inside though , there are only a few tables and they are always full .", "postag": ["VB", "RB", "VB", "TO", "VB", "RP", "RB", "RB", ",", "EX", "VBP", "RB", "DT", "JJ", "NNS", "CC", "PRP", "VBP", "RB", "JJ", "."], "head": [3, 3, 0, 5, 3, 5, 5, 5, 3, 11, 3, 15, 15, 15, 11, 20, 20, 20, 20, 11, 3], "deprel": ["aux", "advmod", "root", "mark", "xcomp", "compound:prt", "advmod", "advmod", "punct", "expl", "parataxis", "advmod", "det", "amod", "nsubj", "cc", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "3156-0", "target_tags": "Do\\O n't\\O expect\\O to\\O sit\\O down\\O inside\\O though\\O ,\\O there\\O are\\O only\\O a\\O few\\O tables\\B and\\O they\\O are\\O always\\O full\\O .\\O", "opinion_tags": "Do\\O n't\\O expect\\O to\\O sit\\O down\\O inside\\O though\\O ,\\O there\\O are\\O only\\O a\\O few\\B tables\\O and\\O they\\O are\\O always\\O full\\B .\\O", "sentiment": "negative"}]}, {"id": "3161", "sentence": "The Bagels have an outstanding taste with a terrific texture , both chewy yet not gummy .", "postag": ["DT", "NNPS", "VBP", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", ",", "CC", "JJ", "CC", "RB", "JJ", "."], "head": [2, 3, 0, 6, 6, 3, 10, 10, 10, 6, 13, 13, 10, 16, 16, 13, 3], "deprel": ["det", "nsubj", "root", "det", "amod", "obj", "case", "det", "amod", "nmod", "punct", "cc:preconj", "amod", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "3161-0", "target_tags": "The\\O Bagels\\B have\\O an\\O outstanding\\O taste\\O with\\O a\\O terrific\\O texture\\O ,\\O both\\O chewy\\O yet\\O not\\O gummy\\O .\\O", "opinion_tags": "The\\O Bagels\\O have\\O an\\O outstanding\\B taste\\O with\\O a\\O terrific\\B texture\\O ,\\O both\\O chewy\\B yet\\O not\\O gummy\\B .\\O", "sentiment": "positive"}]}, {"id": "698", "sentence": "The duck confit is always amazing and the foie gras terrine with figs was out of this world .", "postag": ["DT", "NN", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NN", "NN", "NN", "IN", "NNS", "VBD", "IN", "IN", "DT", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 18, 11, 11, 11, 18, 13, 11, 18, 18, 18, 18, 6, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "cc", "det", "compound", "compound", "nsubj", "case", "nmod", "cop", "case", "case", "det", "conj", "punct"], "triples": [{"uid": "698-0", "target_tags": "The\\O duck\\O confit\\O is\\O always\\O amazing\\O and\\O the\\O foie\\B gras\\I terrine\\I with\\I figs\\I was\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "The\\O duck\\O confit\\O is\\O always\\O amazing\\O and\\O the\\O foie\\O gras\\O terrine\\O with\\O figs\\O was\\O out\\B of\\I this\\I world\\I .\\O", "sentiment": "positive"}, {"uid": "698-1", "target_tags": "The\\O duck\\B confit\\I is\\O always\\O amazing\\O and\\O the\\O foie\\O gras\\O terrine\\O with\\O figs\\O was\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "The\\O duck\\O confit\\O is\\O always\\O amazing\\B and\\O the\\O foie\\O gras\\O terrine\\O with\\O figs\\O was\\O out\\O of\\O this\\O world\\O .\\O", "sentiment": "positive"}]}, {"id": "3186", "sentence": "The staff are attentive , and have smiles on their faces .", "postag": ["DT", "NN", "VBP", "JJ", ",", "CC", "VBP", "NNS", "IN", "PRP$", "NNS", "."], "head": [2, 4, 4, 0, 7, 7, 4, 7, 11, 11, 8, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "conj", "obj", "case", "nmod:poss", "nmod", "punct"], "triples": [{"uid": "3186-0", "target_tags": "The\\O staff\\B are\\O attentive\\O ,\\O and\\O have\\O smiles\\O on\\O their\\O faces\\O .\\O", "opinion_tags": "The\\O staff\\O are\\O attentive\\B ,\\O and\\O have\\O smiles\\O on\\O their\\O faces\\O .\\O", "sentiment": "positive"}]}, {"id": "3302", "sentence": "The staff offers impeccable service .", "postag": ["DT", "NN", "VBZ", "JJ", "NN", "."], "head": [2, 3, 0, 5, 3, 3], "deprel": ["det", "nsubj", "root", "amod", "obj", "punct"], "triples": [{"uid": "3302-0", "target_tags": "The\\O staff\\B offers\\O impeccable\\O service\\O .\\O", "opinion_tags": "The\\O staff\\O offers\\O impeccable\\B service\\O .\\O", "sentiment": "positive"}, {"uid": "3302-1", "target_tags": "The\\O staff\\O offers\\O impeccable\\O service\\B .\\O", "opinion_tags": "The\\O staff\\O offers\\O impeccable\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "285", "sentence": "The portions are now very small , the sauces are overly ambitious usually inedible while the service is still good , the restaurant , due to its popularity , seems frantic .", "postag": ["DT", "NNS", "VBP", "RB", "RB", "JJ", ",", "DT", "NNS", "VBP", "RB", "JJ", "RB", "JJ", "IN", "DT", "NN", "VBZ", "RB", "JJ", ",", "DT", "NN", ",", "IN", "IN", "PRP$", "NN", ",", "VBZ", "JJ", "."], "head": [2, 6, 6, 6, 6, 0, 6, 9, 12, 12, 12, 6, 14, 12, 20, 17, 20, 20, 20, 12, 30, 23, 30, 23, 28, 25, 28, 30, 30, 12, 30, 6], "deprel": ["det", "nsubj", "cop", "advmod", "advmod", "root", "punct", "det", "nsubj", "cop", "advmod", "parataxis", "advmod", "conj", "mark", "det", "nsubj", "cop", "advmod", "advcl", "punct", "det", "nsubj", "punct", "case", "fixed", "nmod:poss", "obl", "punct", "advcl", "xcomp", "punct"], "triples": [{"uid": "285-0", "target_tags": "The\\O portions\\B are\\O now\\O very\\O small\\O ,\\O the\\O sauces\\O are\\O overly\\O ambitious\\O usually\\O inedible\\O while\\O the\\O service\\O is\\O still\\O good\\O ,\\O the\\O restaurant\\O ,\\O due\\O to\\O its\\O popularity\\O ,\\O seems\\O frantic\\O .\\O", "opinion_tags": "The\\O portions\\O are\\O now\\O very\\O small\\B ,\\O the\\O sauces\\O are\\O overly\\O ambitious\\O usually\\O inedible\\O while\\O the\\O service\\O is\\O still\\O good\\O ,\\O the\\O restaurant\\O ,\\O due\\O to\\O its\\O popularity\\O ,\\O seems\\O frantic\\O .\\O", "sentiment": "negative"}, {"uid": "285-1", "target_tags": "The\\O portions\\O are\\O now\\O very\\O small\\O ,\\O the\\O sauces\\B are\\O overly\\O ambitious\\O usually\\O inedible\\O while\\O the\\O service\\O is\\O still\\O good\\O ,\\O the\\O restaurant\\O ,\\O due\\O to\\O its\\O popularity\\O ,\\O seems\\O frantic\\O .\\O", "opinion_tags": "The\\O portions\\O are\\O now\\O very\\O small\\O ,\\O the\\O sauces\\O are\\O overly\\O ambitious\\B usually\\O inedible\\B while\\O the\\O service\\O is\\O still\\O good\\O ,\\O the\\O restaurant\\O ,\\O due\\O to\\O its\\O popularity\\O ,\\O seems\\O frantic\\O .\\O", "sentiment": "negative"}, {"uid": "285-2", "target_tags": "The\\O portions\\O are\\O now\\O very\\O small\\O ,\\O the\\O sauces\\O are\\O overly\\O ambitious\\O usually\\O inedible\\O while\\O the\\O service\\B is\\O still\\O good\\O ,\\O the\\O restaurant\\O ,\\O due\\O to\\O its\\O popularity\\O ,\\O seems\\O frantic\\O .\\O", "opinion_tags": "The\\O portions\\O are\\O now\\O very\\O small\\O ,\\O the\\O sauces\\O are\\O overly\\O ambitious\\O usually\\O inedible\\O while\\O the\\O service\\O is\\O still\\O good\\B ,\\O the\\O restaurant\\O ,\\O due\\O to\\O its\\O popularity\\O ,\\O seems\\O frantic\\O .\\O", "sentiment": "positive"}]}, {"id": "1987", "sentence": "On the other hand , if you are not fooled easily , you will find hundreds of restaurants that will give you service and ambiance that is on par with <PERSON> , and food that will outshine in presentaion , taste , choice , quality and quantity .", "postag": ["IN", "DT", "JJ", "NN", ",", "IN", "PRP", "VBP", "RB", "VBN", "RB", ",", "PRP", "MD", "VB", "NNS", "IN", "NNS", "WDT", "MD", "VB", "PRP", "NN", "CC", "NN", "WDT", "VBZ", "IN", "NN", "IN", "NNP", "NNP", ",", "CC", "NN", "WDT", "MD", "VB", "IN", "NN", ",", "NN", ",", "NN", ",", "NN", "CC", "NN", "."], "head": [4, 4, 4, 15, 15, 10, 10, 10, 10, 15, 10, 15, 15, 15, 0, 15, 18, 16, 21, 21, 18, 21, 21, 25, 23, 29, 29, 29, 23, 31, 29, 31, 35, 35, 31, 38, 38, 35, 40, 38, 42, 40, 44, 40, 46, 40, 48, 40, 15], "deprel": ["case", "det", "amod", "obl", "punct", "mark", "nsubj:pass", "aux:pass", "advmod", "advcl", "advmod", "punct", "nsubj", "aux", "root", "obj", "case", "nmod", "nsubj", "aux", "acl:relcl", "i<PERSON><PERSON>", "obj", "cc", "conj", "nsubj", "cop", "case", "acl:relcl", "case", "nmod", "flat", "punct", "cc", "conj", "nsubj", "aux", "acl:relcl", "case", "obl", "punct", "conj", "punct", "conj", "punct", "conj", "cc", "conj", "punct"], "triples": [{"uid": "1987-0", "target_tags": "On\\O the\\O other\\O hand\\O ,\\O if\\O you\\O are\\O not\\O fooled\\O easily\\O ,\\O you\\O will\\O find\\O hundreds\\O of\\O restaurants\\O that\\O will\\O give\\O you\\O service\\B and\\O ambiance\\O that\\O is\\O on\\O par\\O with\\O Alain\\O Ducasse\\O ,\\O and\\O food\\O that\\O will\\O outshine\\O in\\O presentaion\\O ,\\O taste\\O ,\\O choice\\O ,\\O quality\\O and\\O quantity\\O .\\O", "opinion_tags": "On\\O the\\O other\\O hand\\O ,\\O if\\O you\\O are\\O not\\O fooled\\O easily\\O ,\\O you\\O will\\O find\\O hundreds\\O of\\O restaurants\\O that\\O will\\O give\\O you\\O service\\O and\\O ambiance\\O that\\O is\\O on\\B par\\I with\\O Alain\\O Ducasse\\O ,\\O and\\O food\\O that\\O will\\O outshine\\O in\\O presentaion\\O ,\\O taste\\O ,\\O choice\\O ,\\O quality\\O and\\O quantity\\O .\\O", "sentiment": "neutral"}, {"uid": "1987-1", "target_tags": "On\\O the\\O other\\O hand\\O ,\\O if\\O you\\O are\\O not\\O fooled\\O easily\\O ,\\O you\\O will\\O find\\O hundreds\\O of\\O restaurants\\O that\\O will\\O give\\O you\\O service\\O and\\O ambiance\\B that\\O is\\O on\\O par\\O with\\O Alain\\O Ducasse\\O ,\\O and\\O food\\O that\\O will\\O outshine\\O in\\O presentaion\\O ,\\O taste\\O ,\\O choice\\O ,\\O quality\\O and\\O quantity\\O .\\O", "opinion_tags": "On\\O the\\O other\\O hand\\O ,\\O if\\O you\\O are\\O not\\O fooled\\O easily\\O ,\\O you\\O will\\O find\\O hundreds\\O of\\O restaurants\\O that\\O will\\O give\\O you\\O service\\O and\\O ambiance\\O that\\O is\\O on\\B par\\I with\\O Alain\\O Ducasse\\O ,\\O and\\O food\\O that\\O will\\O outshine\\O in\\O presentaion\\O ,\\O taste\\O ,\\O choice\\O ,\\O quality\\O and\\O quantity\\O .\\O", "sentiment": "neutral"}, {"uid": "1987-2", "target_tags": "On\\O the\\O other\\O hand\\O ,\\O if\\O you\\O are\\O not\\O fooled\\O easily\\O ,\\O you\\O will\\O find\\O hundreds\\O of\\O restaurants\\O that\\O will\\O give\\O you\\O service\\O and\\O ambiance\\O that\\O is\\O on\\O par\\O with\\O Alain\\O Ducasse\\O ,\\O and\\O food\\B that\\O will\\O outshine\\O in\\O presentaion\\O ,\\O taste\\O ,\\O choice\\O ,\\O quality\\O and\\O quantity\\O .\\O", "opinion_tags": "On\\O the\\O other\\O hand\\O ,\\O if\\O you\\O are\\O not\\O fooled\\O easily\\O ,\\O you\\O will\\O find\\O hundreds\\O of\\O restaurants\\O that\\O will\\O give\\O you\\O service\\O and\\O ambiance\\O that\\O is\\O on\\O par\\O with\\O Alain\\O Ducasse\\O ,\\O and\\O food\\O that\\O will\\O outshine\\B in\\O presentaion\\O ,\\O taste\\O ,\\O choice\\O ,\\O quality\\O and\\O quantity\\O .\\O", "sentiment": "negative"}]}, {"id": "3507", "sentence": "His wife <PERSON> , the hostess , completes the comforting atmosphere by being delightfully warm and gracious .", "postag": ["PRP$", "NN", "NNP", ",", "DT", "NN", ",", "VBZ", "DT", "JJ", "NN", "IN", "VBG", "RB", "JJ", "CC", "JJ", "."], "head": [2, 8, 2, 6, 6, 3, 2, 0, 11, 11, 8, 15, 15, 15, 8, 17, 15, 8], "deprel": ["nmod:poss", "nsubj", "appos", "punct", "det", "appos", "punct", "root", "det", "amod", "obj", "mark", "cop", "advmod", "advcl", "cc", "conj", "punct"], "triples": [{"uid": "3507-0", "target_tags": "His\\O wife\\O Tanya\\O ,\\O the\\O hostess\\B ,\\O completes\\O the\\O comforting\\O atmosphere\\O by\\O being\\O delightfully\\O warm\\O and\\O gracious\\O .\\O", "opinion_tags": "His\\O wife\\O Tanya\\O ,\\O the\\O hostess\\O ,\\O completes\\O the\\O comforting\\O atmosphere\\O by\\O being\\O delightfully\\B warm\\I and\\O gracious\\B .\\O", "sentiment": "positive"}, {"uid": "3507-1", "target_tags": "His\\O wife\\O Tanya\\O ,\\O the\\O hostess\\O ,\\O completes\\O the\\O comforting\\O atmosphere\\B by\\O being\\O delightfully\\O warm\\O and\\O gracious\\O .\\O", "opinion_tags": "His\\O wife\\O Tanya\\O ,\\O the\\O hostess\\O ,\\O completes\\O the\\O comforting\\B atmosphere\\O by\\O being\\O delightfully\\O warm\\O and\\O gracious\\O .\\O", "sentiment": "positive"}]}, {"id": "131", "sentence": "<PERSON> gets big Ups for a fine establishment .", "postag": ["NNP", "NNP", "VBZ", "JJ", "NNS", "IN", "DT", "JJ", "NN", "."], "head": [2, 3, 0, 5, 3, 9, 9, 9, 3, 3], "deprel": ["compound", "nsubj", "root", "amod", "obj", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "131-0", "target_tags": "Big\\O Wong\\O gets\\O big\\O Ups\\O for\\O a\\O fine\\O establishment\\B .\\O", "opinion_tags": "Big\\O Wong\\O gets\\O big\\O Ups\\O for\\O a\\O fine\\B establishment\\O .\\O", "sentiment": "positive"}]}, {"id": "2300", "sentence": "less wait time for me !", "postag": ["RBR", "VB", "NN", "IN", "PRP", "."], "head": [2, 0, 2, 5, 2, 2], "deprel": ["advmod", "root", "obj", "case", "obl", "punct"], "triples": [{"uid": "2300-0", "target_tags": "less\\O wait\\B time\\I for\\O me\\O !\\O", "opinion_tags": "less\\B wait\\O time\\O for\\O me\\O !\\O", "sentiment": "positive"}]}, {"id": "2802", "sentence": "Good for casual dinner with jeans and sneakers .", "postag": ["JJ", "IN", "JJ", "NN", "IN", "NNS", "CC", "NNS", "."], "head": [0, 4, 4, 1, 6, 4, 8, 6, 1], "deprel": ["root", "case", "amod", "obl", "case", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "2802-0", "target_tags": "Good\\O for\\O casual\\B dinner\\I with\\O jeans\\O and\\O sneakers\\O .\\O", "opinion_tags": "Good\\B for\\O casual\\O dinner\\O with\\O jeans\\O and\\O sneakers\\O .\\O", "sentiment": "positive"}]}, {"id": "1989", "sentence": "The second you walk through the heavy vault like door , with people anticipating your arrival you get the sense that you are going to have the dining ride of a lifetime .", "postag": ["DT", "JJ", "PRP", "VBP", "IN", "DT", "JJ", "NN", "IN", "NN", ",", "IN", "NNS", "VBG", "PRP$", "NN", "PRP", "VBP", "DT", "NN", "WDT", "PRP", "VBP", "VBG", "TO", "VB", "DT", "NN", "NN", "IN", "DT", "NN", "."], "head": [2, 18, 4, 2, 8, 8, 8, 4, 10, 8, 18, 13, 4, 13, 16, 14, 18, 0, 20, 18, 24, 24, 24, 20, 26, 24, 29, 29, 26, 32, 32, 29, 18], "deprel": ["det", "obl:tmod", "nsubj", "acl:relcl", "case", "det", "amod", "obl", "case", "nmod", "punct", "case", "obl", "acl", "nmod:poss", "obj", "nsubj", "root", "det", "obj", "obj", "nsubj", "aux", "acl:relcl", "mark", "xcomp", "det", "compound", "obj", "case", "det", "nmod", "punct"], "triples": [{"uid": "1989-0", "target_tags": "The\\O second\\O you\\O walk\\O through\\O the\\O heavy\\O vault\\O like\\O door\\B ,\\O with\\O people\\O anticipating\\O your\\O arrival\\O you\\O get\\O the\\O sense\\O that\\O you\\O are\\O going\\O to\\O have\\O the\\O dining\\O ride\\O of\\O a\\O lifetime\\O .\\O", "opinion_tags": "The\\O second\\O you\\O walk\\O through\\O the\\O heavy\\B vault\\O like\\O door\\O ,\\O with\\O people\\O anticipating\\O your\\O arrival\\O you\\O get\\O the\\O sense\\O that\\O you\\O are\\O going\\O to\\O have\\O the\\O dining\\O ride\\O of\\O a\\O lifetime\\O .\\O", "sentiment": "neutral"}]}, {"id": "135", "sentence": "And it all comes at a very reasonable price ( congee , noodles , and rice dishes are no more than $ 3-6 each ) .", "postag": ["CC", "PRP", "DT", "VBZ", "IN", "DT", "RB", "JJ", "NN", "-LRB-", "NN", ",", "NNS", ",", "CC", "NN", "NNS", "VBP", "RB", "JJR", "IN", "$", "CD", "DT", "-RRB-", "."], "head": [4, 4, 4, 0, 9, 9, 8, 9, 4, 11, 9, 13, 11, 17, 17, 17, 11, 22, 20, 22, 20, 4, 22, 22, 22, 4], "deprel": ["cc", "nsubj", "advmod", "root", "case", "det", "advmod", "amod", "obl", "punct", "appos", "punct", "conj", "punct", "cc", "compound", "conj", "cop", "advmod", "advmod", "fixed", "parataxis", "compound", "advmod", "punct", "punct"], "triples": [{"uid": "135-0", "target_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\O price\\B (\\O congee\\O ,\\O noodles\\O ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O", "opinion_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\B price\\O (\\O congee\\O ,\\O noodles\\O ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "135-1", "target_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\O price\\O (\\O congee\\B ,\\O noodles\\O ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O", "opinion_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\B price\\O (\\O congee\\O ,\\O noodles\\O ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O", "sentiment": "neutral"}, {"uid": "135-2", "target_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\O price\\O (\\O congee\\O ,\\O noodles\\B ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O", "opinion_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\B price\\O (\\O congee\\O ,\\O noodles\\O ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O", "sentiment": "neutral"}, {"uid": "135-3", "target_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\O price\\O (\\O congee\\O ,\\O noodles\\O ,\\O and\\O rice\\B dishes\\I are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O", "opinion_tags": "And\\O it\\O all\\O comes\\O at\\O a\\O very\\O reasonable\\B price\\O (\\O congee\\O ,\\O noodles\\O ,\\O and\\O rice\\O dishes\\O are\\O no\\O more\\O than\\O $\\O 3-6\\O each\\O )\\O .\\O", "sentiment": "neutral"}]}, {"id": "2742", "sentence": "Plus they made a perfect martini .", "postag": ["CC", "PRP", "VBD", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 3], "deprel": ["cc", "nsubj", "root", "det", "amod", "obj", "punct"], "triples": [{"uid": "2742-0", "target_tags": "Plus\\O they\\O made\\O a\\O perfect\\O martini\\B .\\O", "opinion_tags": "Plus\\O they\\O made\\O a\\O perfect\\B martini\\O .\\O", "sentiment": "positive"}]}, {"id": "3608", "sentence": "So , a little inconsistency there but either way , both pizzas were really good .", "postag": ["RB", ",", "DT", "JJ", "NN", "RB", "CC", "DT", "NN", ",", "DT", "NNS", "VBD", "RB", "JJ", "."], "head": [5, 5, 5, 5, 0, 5, 15, 9, 15, 15, 12, 15, 15, 15, 5, 5], "deprel": ["advmod", "punct", "det", "amod", "root", "advmod", "cc", "det", "obl:tmod", "punct", "det", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "3608-0", "target_tags": "So\\O ,\\O a\\O little\\O inconsistency\\O there\\O but\\O either\\O way\\O ,\\O both\\O pizzas\\B were\\O really\\O good\\O .\\O", "opinion_tags": "So\\O ,\\O a\\O little\\O inconsistency\\O there\\O but\\O either\\O way\\O ,\\O both\\O pizzas\\O were\\O really\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "3637", "sentence": "We parked on the block of Nina 's the place looked nice , with people obviously enjoying their pizzas .", "postag": ["PRP", "VBD", "IN", "DT", "NN", "IN", "NNP", "POS", "DT", "NN", "VBD", "JJ", ",", "IN", "NNS", "RB", "VBG", "PRP$", "NNS", "."], "head": [2, 0, 5, 5, 2, 10, 10, 7, 10, 5, 2, 11, 11, 17, 17, 17, 11, 19, 17, 2], "deprel": ["nsubj", "root", "case", "det", "obl", "case", "nmod:poss", "case", "det", "nmod", "advcl", "xcomp", "punct", "mark", "nsubj", "advmod", "advcl", "nmod:poss", "obj", "punct"], "triples": [{"uid": "3637-0", "target_tags": "We\\O parked\\O on\\O the\\O block\\O of\\O Nina\\O 's\\O the\\O place\\B looked\\O nice\\O ,\\O with\\O people\\O obviously\\O enjoying\\O their\\O pizzas\\O .\\O", "opinion_tags": "We\\O parked\\O on\\O the\\O block\\O of\\O Nina\\O 's\\O the\\O place\\O looked\\O nice\\B ,\\O with\\O people\\O obviously\\O enjoying\\O their\\O pizzas\\O .\\O", "sentiment": "positive"}, {"uid": "3637-1", "target_tags": "We\\O parked\\O on\\O the\\O block\\O of\\O Nina\\O 's\\O the\\O place\\O looked\\O nice\\O ,\\O with\\O people\\O obviously\\O enjoying\\O their\\O pizzas\\B .\\O", "opinion_tags": "We\\O parked\\O on\\O the\\O block\\O of\\O Nina\\O 's\\O the\\O place\\O looked\\O nice\\O ,\\O with\\O people\\O obviously\\O enjoying\\B their\\O pizzas\\O .\\O", "sentiment": "positive"}]}, {"id": "1565", "sentence": "Do n't waste money on decor .", "postag": ["VB", "RB", "VB", "NN", "IN", "NN", "."], "head": [3, 3, 0, 3, 6, 3, 3], "deprel": ["aux", "advmod", "root", "obj", "case", "obl", "punct"], "triples": [{"uid": "1565-0", "target_tags": "Do\\O n't\\O waste\\O money\\O on\\O decor\\B .\\O", "opinion_tags": "Do\\O n't\\O waste\\B money\\O on\\O decor\\O .\\O", "sentiment": "neutral"}]}, {"id": "1733", "sentence": "<PERSON><PERSON> is charming .", "postag": ["NNP", "VBZ", "JJ", "."], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"], "triples": [{"uid": "1733-0", "target_tags": "Decor\\B is\\O charming\\O .\\O", "opinion_tags": "Decor\\O is\\O charming\\B .\\O", "sentiment": "positive"}]}, {"id": "2028", "sentence": "My friends and I experienced amazing cheese and a delicious , new summer menu at Artisanal last night .", "postag": ["PRP$", "NNS", "CC", "PRP", "VBD", "JJ", "NN", "CC", "DT", "JJ", ",", "JJ", "NN", "NN", "IN", "NNP", "JJ", "NN", "."], "head": [2, 5, 4, 2, 0, 7, 5, 14, 14, 14, 14, 14, 14, 7, 16, 14, 18, 5, 5], "deprel": ["nmod:poss", "nsubj", "cc", "conj", "root", "amod", "obj", "cc", "det", "amod", "punct", "amod", "compound", "conj", "case", "nmod", "amod", "obl:tmod", "punct"], "triples": [{"uid": "2028-0", "target_tags": "My\\O friends\\O and\\O I\\O experienced\\O amazing\\O cheese\\B and\\O a\\O delicious\\O ,\\O new\\O summer\\O menu\\O at\\O Artisanal\\O last\\O night\\O .\\O", "opinion_tags": "My\\O friends\\O and\\O I\\O experienced\\O amazing\\B cheese\\O and\\O a\\O delicious\\O ,\\O new\\O summer\\O menu\\O at\\O Artisanal\\O last\\O night\\O .\\O", "sentiment": "positive"}, {"uid": "2028-1", "target_tags": "My\\O friends\\O and\\O I\\O experienced\\O amazing\\O cheese\\O and\\O a\\O delicious\\O ,\\O new\\O summer\\O menu\\B at\\O Artisanal\\O last\\O night\\O .\\O", "opinion_tags": "My\\O friends\\O and\\O I\\O experienced\\O amazing\\O cheese\\O and\\O a\\O delicious\\B ,\\O new\\B summer\\O menu\\O at\\O Artisanal\\O last\\O night\\O .\\O", "sentiment": "positive"}]}, {"id": "1638", "sentence": "The prices are exceptionally reasonable for food of this caliber .", "postag": ["DT", "NNS", "VBP", "RB", "JJ", "IN", "NN", "IN", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 7, 5, 10, 10, 7, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "obl", "case", "det", "nmod", "punct"], "triples": [{"uid": "1638-0", "target_tags": "The\\O prices\\B are\\O exceptionally\\O reasonable\\O for\\O food\\O of\\O this\\O caliber\\O .\\O", "opinion_tags": "The\\O prices\\O are\\O exceptionally\\O reasonable\\B for\\O food\\O of\\O this\\O caliber\\O .\\O", "sentiment": "positive"}]}, {"id": "2263", "sentence": "We went around 9:30 on a Friday and it had died down a bit by then so the service was great !", "postag": ["PRP", "VBD", "RB", "CD", "IN", "DT", "NNP", "CC", "PRP", "VBD", "VBN", "RP", "DT", "NN", "IN", "RB", "RB", "DT", "NN", "VBD", "JJ", "."], "head": [2, 0, 4, 2, 7, 7, 2, 11, 11, 11, 2, 11, 14, 11, 16, 11, 21, 19, 21, 21, 2, 2], "deprel": ["nsubj", "root", "advmod", "obl:tmod", "case", "det", "obl", "cc", "nsubj", "aux", "conj", "compound:prt", "det", "obl:npmod", "case", "obl", "advmod", "det", "nsubj", "cop", "parataxis", "punct"], "triples": [{"uid": "2263-0", "target_tags": "We\\O went\\O around\\O 9:30\\O on\\O a\\O Friday\\O and\\O it\\O had\\O died\\O down\\O a\\O bit\\O by\\O then\\O so\\O the\\O service\\B was\\O great\\O !\\O", "opinion_tags": "We\\O went\\O around\\O 9:30\\O on\\O a\\O Friday\\O and\\O it\\O had\\O died\\O down\\O a\\O bit\\O by\\O then\\O so\\O the\\O service\\O was\\O great\\B !\\O", "sentiment": "positive"}]}, {"id": "3026", "sentence": "The blond wood decor is very soothing , the premium sake is excellent and the service is great .", "postag": ["DT", "JJ", "NN", "NN", "VBZ", "RB", "JJ", ",", "DT", "NN", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [4, 4, 4, 7, 7, 7, 0, 13, 11, 11, 13, 13, 7, 18, 16, 18, 18, 7, 7], "deprel": ["det", "amod", "compound", "nsubj", "cop", "advmod", "root", "punct", "det", "compound", "nsubj", "cop", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "3026-0", "target_tags": "The\\O blond\\B wood\\I decor\\I is\\O very\\O soothing\\O ,\\O the\\O premium\\O sake\\O is\\O excellent\\O and\\O the\\O service\\O is\\O great\\O .\\O", "opinion_tags": "The\\O blond\\O wood\\O decor\\O is\\O very\\O soothing\\B ,\\O the\\O premium\\O sake\\O is\\O excellent\\O and\\O the\\O service\\O is\\O great\\O .\\O", "sentiment": "positive"}, {"uid": "3026-1", "target_tags": "The\\O blond\\O wood\\O decor\\O is\\O very\\O soothing\\O ,\\O the\\O premium\\O sake\\B is\\O excellent\\O and\\O the\\O service\\O is\\O great\\O .\\O", "opinion_tags": "The\\O blond\\O wood\\O decor\\O is\\O very\\O soothing\\O ,\\O the\\O premium\\O sake\\O is\\O excellent\\B and\\O the\\O service\\O is\\O great\\O .\\O", "sentiment": "positive"}, {"uid": "3026-2", "target_tags": "The\\O blond\\O wood\\O decor\\O is\\O very\\O soothing\\O ,\\O the\\O premium\\O sake\\O is\\O excellent\\O and\\O the\\O service\\B is\\O great\\O .\\O", "opinion_tags": "The\\O blond\\O wood\\O decor\\O is\\O very\\O soothing\\O ,\\O the\\O premium\\O sake\\O is\\O excellent\\O and\\O the\\O service\\O is\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "2875", "sentence": "Decent wine at reasonable prices .", "postag": ["JJ", "NN", "IN", "JJ", "NNS", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["amod", "root", "case", "amod", "nmod", "punct"], "triples": [{"uid": "2875-0", "target_tags": "Decent\\O wine\\B at\\O reasonable\\O prices\\O .\\O", "opinion_tags": "Decent\\B wine\\O at\\O reasonable\\O prices\\O .\\O", "sentiment": "positive"}, {"uid": "2875-1", "target_tags": "Decent\\O wine\\O at\\O reasonable\\O prices\\B .\\O", "opinion_tags": "Decent\\O wine\\O at\\O reasonable\\B prices\\O .\\O", "sentiment": "positive"}]}, {"id": "3241", "sentence": "But the main hit was the whole grilled fish .", "postag": ["CC", "DT", "JJ", "NN", "VBD", "DT", "JJ", "VBN", "NN", "."], "head": [9, 4, 4, 9, 9, 9, 9, 9, 0, 9], "deprel": ["cc", "det", "amod", "nsubj", "cop", "det", "amod", "amod", "root", "punct"], "triples": [{"uid": "3241-0", "target_tags": "But\\O the\\O main\\O hit\\O was\\O the\\O whole\\B grilled\\I fish\\I .\\O", "opinion_tags": "But\\O the\\O main\\O hit\\B was\\O the\\O whole\\O grilled\\O fish\\O .\\O", "sentiment": "positive"}]}, {"id": "2994", "sentence": "Kind , attentive wait staff .", "postag": ["JJ", ",", "JJ", "NN", "NN", "."], "head": [5, 5, 5, 5, 0, 5], "deprel": ["amod", "punct", "amod", "compound", "root", "punct"], "triples": [{"uid": "2994-0", "target_tags": "Kind\\O ,\\O attentive\\O wait\\B staff\\I .\\O", "opinion_tags": "Kind\\B ,\\O attentive\\B wait\\O staff\\O .\\O", "sentiment": "positive"}]}, {"id": "1655", "sentence": "Not the greatest sushi place , but excellent for a $ 19.95 all you can eat .", "postag": ["RB", "DT", "JJS", "NN", "NN", ",", "CC", "JJ", "IN", "DT", "$", "CD", "DT", "PRP", "MD", "VB", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 11, 11, 8, 11, 11, 16, 16, 13, 5], "deprel": ["advmod", "det", "amod", "compound", "root", "punct", "cc", "conj", "case", "det", "obl", "nummod", "nmod", "nsubj", "aux", "acl:relcl", "punct"], "triples": [{"uid": "1655-0", "target_tags": "Not\\O the\\O greatest\\O sushi\\B place\\I ,\\O but\\O excellent\\O for\\O a\\O $\\O 19.95\\O all\\O you\\O can\\O eat\\O .\\O", "opinion_tags": "Not\\B the\\I greatest\\I sushi\\O place\\O ,\\O but\\O excellent\\B for\\O a\\O $\\O 19.95\\O all\\O you\\O can\\O eat\\O .\\O", "sentiment": "negative"}]}, {"id": "2297", "sentence": "By far , the best pizza in Manhattan .", "postag": ["IN", "RB", ",", "DT", "JJS", "NN", "IN", "NNP", "."], "head": [2, 6, 6, 6, 6, 0, 8, 6, 6], "deprel": ["case", "nmod", "punct", "det", "amod", "root", "case", "nmod", "punct"], "triples": [{"uid": "2297-0", "target_tags": "By\\O far\\O ,\\O the\\O best\\O pizza\\B in\\O Manhattan\\O .\\O", "opinion_tags": "By\\O far\\O ,\\O the\\O best\\B pizza\\O in\\O Manhattan\\O .\\O", "sentiment": "positive"}]}, {"id": "1211", "sentence": "While they keep the capex to a minimum , they do put some cash into the bagels , because they among the best in the city .", "postag": ["IN", "PRP", "VBP", "DT", "NN", "IN", "DT", "NN", ",", "PRP", "VBP", "VB", "DT", "NN", "IN", "DT", "NNS", ",", "IN", "PRP", "IN", "DT", "JJS", "IN", "DT", "NN", "."], "head": [3, 3, 12, 5, 3, 8, 8, 3, 12, 12, 12, 0, 14, 12, 17, 17, 12, 12, 23, 23, 23, 23, 12, 26, 26, 23, 12], "deprel": ["mark", "nsubj", "advcl", "det", "obj", "case", "det", "obl", "punct", "nsubj", "aux", "root", "det", "obj", "case", "det", "obl", "punct", "mark", "nsubj", "case", "det", "advcl", "case", "det", "obl", "punct"], "triples": [{"uid": "1211-0", "target_tags": "While\\O they\\O keep\\O the\\O capex\\O to\\O a\\O minimum\\O ,\\O they\\O do\\O put\\O some\\O cash\\O into\\O the\\O bagels\\B ,\\O because\\O they\\O among\\O the\\O best\\O in\\O the\\O city\\O .\\O", "opinion_tags": "While\\O they\\O keep\\O the\\O capex\\O to\\O a\\O minimum\\O ,\\O they\\O do\\O put\\O some\\O cash\\O into\\O the\\O bagels\\O ,\\O because\\O they\\O among\\O the\\O best\\B in\\O the\\O city\\O .\\O", "sentiment": "neutral"}]}, {"id": "2495", "sentence": "This place , however , has a lot less pretension than <PERSON><PERSON> and the Thai food is still above average .", "postag": ["DT", "NN", ",", "RB", ",", "VBZ", "DT", "NN", "JJR", "NN", "IN", "NNP", "CC", "DT", "JJ", "NN", "VBZ", "RB", "IN", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 8, 9, 10, 6, 12, 10, 20, 16, 16, 20, 20, 20, 20, 6, 6], "deprel": ["det", "nsubj", "punct", "advmod", "punct", "root", "det", "obl:npmod", "amod", "obj", "case", "nmod", "cc", "det", "amod", "nsubj", "cop", "advmod", "case", "conj", "punct"], "triples": [{"uid": "2495-0", "target_tags": "This\\O place\\O ,\\O however\\O ,\\O has\\O a\\O lot\\O less\\O pretension\\O than\\O Joya\\O and\\O the\\O Thai\\B food\\I is\\O still\\O above\\O average\\O .\\O", "opinion_tags": "This\\O place\\O ,\\O however\\O ,\\O has\\O a\\O lot\\O less\\O pretension\\O than\\O Joya\\O and\\O the\\O Thai\\O food\\O is\\O still\\O above\\B average\\I .\\O", "sentiment": "positive"}]}, {"id": "1185", "sentence": "It 's a great place to pick up a cheap lunch or dinner .", "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "RP", "DT", "JJ", "NN", "CC", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 7, 11, 11, 7, 13, 11, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "csubj", "compound:prt", "det", "amod", "obj", "cc", "conj", "punct"], "triples": [{"uid": "1185-0", "target_tags": "It\\O 's\\O a\\O great\\O place\\O to\\O pick\\O up\\O a\\O cheap\\O lunch\\B or\\O dinner\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O great\\O place\\O to\\O pick\\O up\\O a\\O cheap\\B lunch\\O or\\O dinner\\O .\\O", "sentiment": "positive"}]}, {"id": "3595", "sentence": "It was so bad I actually refused to pay for my food .", "postag": ["PRP", "VBD", "RB", "JJ", "PRP", "RB", "VBD", "TO", "VB", "IN", "PRP$", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 9, 7, 12, 12, 9, 4], "deprel": ["nsubj", "cop", "advmod", "root", "nsubj", "advmod", "ccomp", "mark", "xcomp", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "3595-0", "target_tags": "It\\O was\\O so\\O bad\\O I\\O actually\\O refused\\O to\\O pay\\O for\\O my\\O food\\B .\\O", "opinion_tags": "It\\O was\\O so\\O bad\\B I\\O actually\\O refused\\O to\\O pay\\O for\\O my\\O food\\O .\\O", "sentiment": "negative"}]}, {"id": "2555", "sentence": "Service is friendly , prices are good - delivery time was a little slow , but for the way this pizza tastes , I 'm willing to overlook it .", "postag": ["NN", "VBZ", "JJ", ",", "NNS", "VBP", "JJ", "HYPH", "NN", "NN", "VBD", "DT", "JJ", "JJ", ",", "CC", "IN", "DT", "NN", "DT", "NN", "VBZ", ",", "PRP", "VBP", "JJ", "TO", "VB", "PRP", "."], "head": [3, 3, 0, 3, 14, 10, 9, 9, 10, 14, 14, 13, 14, 3, 26, 26, 19, 19, 26, 21, 22, 19, 26, 26, 26, 3, 28, 26, 28, 3], "deprel": ["nsubj", "cop", "root", "punct", "nsubj", "cop", "amod", "punct", "compound", "nsubj", "cop", "det", "obl:npmod", "conj", "punct", "cc", "case", "det", "obl", "det", "nsubj", "acl:relcl", "punct", "nsubj", "cop", "conj", "mark", "xcomp", "obj", "punct"], "triples": [{"uid": "2555-0", "target_tags": "Service\\B is\\O friendly\\O ,\\O prices\\O are\\O good\\O -\\O delivery\\O time\\O was\\O a\\O little\\O slow\\O ,\\O but\\O for\\O the\\O way\\O this\\O pizza\\O tastes\\O ,\\O I\\O 'm\\O willing\\O to\\O overlook\\O it\\O .\\O", "opinion_tags": "Service\\O is\\O friendly\\B ,\\O prices\\O are\\O good\\O -\\O delivery\\O time\\O was\\O a\\O little\\O slow\\O ,\\O but\\O for\\O the\\O way\\O this\\O pizza\\O tastes\\O ,\\O I\\O 'm\\O willing\\O to\\O overlook\\O it\\O .\\O", "sentiment": "positive"}, {"uid": "2555-1", "target_tags": "Service\\O is\\O friendly\\O ,\\O prices\\B are\\O good\\O -\\O delivery\\O time\\O was\\O a\\O little\\O slow\\O ,\\O but\\O for\\O the\\O way\\O this\\O pizza\\O tastes\\O ,\\O I\\O 'm\\O willing\\O to\\O overlook\\O it\\O .\\O", "opinion_tags": "Service\\O is\\O friendly\\O ,\\O prices\\O are\\O good\\B -\\O delivery\\O time\\O was\\O a\\O little\\O slow\\O ,\\O but\\O for\\O the\\O way\\O this\\O pizza\\O tastes\\O ,\\O I\\O 'm\\O willing\\O to\\O overlook\\O it\\O .\\O", "sentiment": "positive"}, {"uid": "2555-2", "target_tags": "Service\\O is\\O friendly\\O ,\\O prices\\O are\\O good\\O -\\O delivery\\B time\\I was\\O a\\O little\\O slow\\O ,\\O but\\O for\\O the\\O way\\O this\\O pizza\\O tastes\\O ,\\O I\\O 'm\\O willing\\O to\\O overlook\\O it\\O .\\O", "opinion_tags": "Service\\O is\\O friendly\\O ,\\O prices\\O are\\O good\\O -\\O delivery\\O time\\O was\\O a\\O little\\O slow\\B ,\\O but\\O for\\O the\\O way\\O this\\O pizza\\O tastes\\O ,\\O I\\O 'm\\O willing\\O to\\O overlook\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "957", "sentence": "The menu consisted of standard brassiere food , better then places like Balthazar etc .", "postag": ["DT", "NN", "VBD", "IN", "JJ", "NN", "NN", ",", "JJR", "RB", "NNS", "IN", "NNP", "FW", "."], "head": [2, 3, 0, 7, 7, 7, 3, 11, 11, 11, 7, 13, 11, 13, 3], "deprel": ["det", "nsubj", "root", "case", "amod", "compound", "obl", "punct", "amod", "advmod", "conj", "case", "nmod", "advmod", "punct"], "triples": [{"uid": "957-0", "target_tags": "The\\O menu\\O consisted\\O of\\O standard\\O brassiere\\B food\\I ,\\O better\\O then\\O places\\O like\\O Balthazar\\O etc\\O .\\O", "opinion_tags": "The\\O menu\\O consisted\\O of\\O standard\\B brassiere\\O food\\O ,\\O better\\B then\\O places\\O like\\O Balthazar\\O etc\\O .\\O", "sentiment": "positive"}]}, {"id": "1014", "sentence": "The atmosphere is great ! ! !", "postag": ["DT", "NN", "VBZ", "JJ", ".", ".", "."], "head": [2, 4, 4, 0, 4, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "punct", "punct"], "triples": [{"uid": "1014-0", "target_tags": "The\\O atmosphere\\B is\\O great\\O !\\O !\\O !\\O", "opinion_tags": "The\\O atmosphere\\O is\\O great\\B !\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "943", "sentence": "Very good wine choices .", "postag": ["RB", "JJ", "NN", "NNS", "."], "head": [2, 4, 4, 0, 4], "deprel": ["advmod", "amod", "compound", "root", "punct"], "triples": [{"uid": "943-0", "target_tags": "Very\\O good\\O wine\\B choices\\I .\\O", "opinion_tags": "Very\\O good\\B wine\\O choices\\O .\\O", "sentiment": "positive"}]}, {"id": "2773", "sentence": "Slightly above average wines start at $ 70+ with only one selection listed at $ 30+ .", "postag": ["RB", "IN", "JJ", "NNS", "VBP", "IN", "$", "CD", "IN", "RB", "CD", "NN", "VBN", "IN", "$", "CD", "."], "head": [5, 4, 4, 5, 0, 7, 5, 7, 12, 12, 12, 5, 12, 15, 13, 15, 5], "deprel": ["advmod", "case", "amod", "nsubj", "root", "case", "obl", "nummod", "case", "advmod", "nummod", "obl", "acl", "case", "obl", "nummod", "punct"], "triples": [{"uid": "2773-0", "target_tags": "Slightly\\O above\\O average\\O wines\\B start\\O at\\O $\\O 70+\\O with\\O only\\O one\\O selection\\O listed\\O at\\O $\\O 30+\\O .\\O", "opinion_tags": "Slightly\\O above\\B average\\I wines\\O start\\O at\\O $\\O 70+\\O with\\O only\\O one\\O selection\\O listed\\O at\\O $\\O 30+\\O .\\O", "sentiment": "negative"}]}, {"id": "3127", "sentence": "This place is incredibly tiny .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "3127-0", "target_tags": "This\\O place\\B is\\O incredibly\\O tiny\\O .\\O", "opinion_tags": "This\\O place\\O is\\O incredibly\\O tiny\\B .\\O", "sentiment": "negative"}]}, {"id": "2827", "sentence": "The food was below average , the service was pathetic , there was no ambience at all .", "postag": ["DT", "NN", "VBD", "RB", "JJ", ",", "DT", "NN", "VBD", "JJ", ",", "EX", "VBD", "DT", "NN", "IN", "DT", "."], "head": [2, 4, 4, 0, 4, 4, 8, 10, 10, 4, 4, 13, 4, 15, 13, 17, 15, 4], "deprel": ["det", "nsubj", "cop", "root", "advmod", "punct", "det", "nsubj", "cop", "conj", "punct", "expl", "parataxis", "det", "nsubj", "case", "nmod", "punct"], "triples": [{"uid": "2827-0", "target_tags": "The\\O food\\B was\\O below\\O average\\O ,\\O the\\O service\\O was\\O pathetic\\O ,\\O there\\O was\\O no\\O ambience\\O at\\O all\\O .\\O", "opinion_tags": "The\\O food\\O was\\O below\\B average\\I ,\\O the\\O service\\O was\\O pathetic\\O ,\\O there\\O was\\O no\\O ambience\\O at\\O all\\O .\\O", "sentiment": "negative"}, {"uid": "2827-1", "target_tags": "The\\O food\\O was\\O below\\O average\\O ,\\O the\\O service\\B was\\O pathetic\\O ,\\O there\\O was\\O no\\O ambience\\O at\\O all\\O .\\O", "opinion_tags": "The\\O food\\O was\\O below\\O average\\O ,\\O the\\O service\\O was\\O pathetic\\B ,\\O there\\O was\\O no\\O ambience\\O at\\O all\\O .\\O", "sentiment": "negative"}, {"uid": "2827-2", "target_tags": "The\\O food\\O was\\O below\\O average\\O ,\\O the\\O service\\O was\\O pathetic\\O ,\\O there\\O was\\O no\\O ambience\\B at\\O all\\O .\\O", "opinion_tags": "The\\O food\\O was\\O below\\O average\\O ,\\O the\\O service\\O was\\O pathetic\\O ,\\O there\\O was\\O no\\B ambience\\O at\\O all\\O .\\O", "sentiment": "negative"}]}, {"id": "2487", "sentence": "You can get an excellent meal at most of the many Indian restaurants on nearby Lexington Avenue for the cost of one the dainty dishes here .", "postag": ["PRP", "MD", "VB", "DT", "JJ", "NN", "IN", "JJS", "IN", "DT", "JJ", "JJ", "NNS", "IN", "JJ", "NNP", "NNP", "IN", "DT", "NN", "IN", "CD", "DT", "JJ", "NNS", "RB", "."], "head": [3, 3, 0, 6, 6, 3, 8, 3, 13, 13, 13, 13, 8, 17, 17, 17, 13, 20, 20, 3, 22, 20, 25, 25, 20, 25, 3], "deprel": ["nsubj", "aux", "root", "det", "amod", "obj", "case", "obl", "case", "det", "amod", "amod", "nmod", "case", "amod", "compound", "nmod", "case", "det", "obl", "case", "nmod", "det", "amod", "nmod", "advmod", "punct"], "triples": [{"uid": "2487-0", "target_tags": "You\\O can\\O get\\O an\\O excellent\\O meal\\B at\\O most\\O of\\O the\\O many\\O Indian\\O restaurants\\O on\\O nearby\\O Lexington\\O Avenue\\O for\\O the\\O cost\\O of\\O one\\O the\\O dainty\\O dishes\\O here\\O .\\O", "opinion_tags": "You\\O can\\O get\\O an\\O excellent\\B meal\\O at\\O most\\O of\\O the\\O many\\O Indian\\O restaurants\\O on\\O nearby\\O Lexington\\O Avenue\\O for\\O the\\O cost\\O of\\O one\\O the\\O dainty\\O dishes\\O here\\O .\\O", "sentiment": "positive"}, {"uid": "2487-1", "target_tags": "You\\O can\\O get\\O an\\O excellent\\O meal\\O at\\O most\\O of\\O the\\O many\\O Indian\\O restaurants\\O on\\O nearby\\O Lexington\\O Avenue\\O for\\O the\\O cost\\O of\\O one\\O the\\O dainty\\O dishes\\B here\\O .\\O", "opinion_tags": "You\\O can\\O get\\O an\\O excellent\\O meal\\O at\\O most\\O of\\O the\\O many\\O Indian\\O restaurants\\O on\\O nearby\\O Lexington\\O Avenue\\O for\\O the\\O cost\\O of\\O one\\O the\\O dainty\\B dishes\\O here\\O .\\O", "sentiment": "positive"}]}, {"id": "2279", "sentence": "Overall , I 'm still impressed that the place even exists and the prices are quite decent but then again , its Chinatown .", "postag": ["RB", ",", "PRP", "VBP", "RB", "JJ", "IN", "DT", "NN", "RB", "VBZ", "CC", "DT", "NNS", "VBP", "RB", "JJ", "CC", "RB", "RB", ",", "PRP$", "NNP", "."], "head": [6, 6, 6, 6, 6, 0, 11, 9, 11, 11, 6, 17, 14, 17, 17, 17, 11, 23, 23, 23, 23, 23, 17, 6], "deprel": ["advmod", "punct", "nsubj", "cop", "advmod", "root", "mark", "det", "nsubj", "advmod", "ccomp", "cc", "det", "nsubj", "cop", "advmod", "conj", "cc", "advmod", "advmod", "punct", "nmod:poss", "conj", "punct"], "triples": [{"uid": "2279-0", "target_tags": "Overall\\O ,\\O I\\O 'm\\O still\\O impressed\\O that\\O the\\O place\\O even\\O exists\\O and\\O the\\O prices\\B are\\O quite\\O decent\\O but\\O then\\O again\\O ,\\O its\\O Chinatown\\O .\\O", "opinion_tags": "Overall\\O ,\\O I\\O 'm\\O still\\O impressed\\O that\\O the\\O place\\O even\\O exists\\O and\\O the\\O prices\\O are\\O quite\\O decent\\B but\\O then\\O again\\O ,\\O its\\O Chinatown\\O .\\O", "sentiment": "positive"}]}, {"id": "1345", "sentence": "Barbecued codfish was gorgeously moist - as if poached - yet the fabulous texture was let down by curiously bland seasoning - a spice rub might have overwhelmed , however herb mix or other sauce would have done much to enhance .", "postag": ["JJ", "NN", "VBD", "RB", "JJ", ",", "IN", "IN", "VBN", ",", "CC", "DT", "JJ", "NN", "VBD", "VBN", "RP", "IN", "RB", "JJ", "NN", ",", "DT", "NN", "NN", "MD", "VB", "VBN", ",", "RB", "NN", "NN", "CC", "JJ", "NN", "MD", "VB", "VBN", "JJ", "TO", "VB", "."], "head": [2, 5, 5, 5, 0, 5, 9, 9, 5, 5, 16, 14, 14, 16, 16, 5, 16, 21, 20, 21, 16, 5, 25, 25, 28, 28, 28, 5, 38, 38, 32, 38, 35, 35, 32, 38, 38, 5, 38, 41, 39, 5], "deprel": ["amod", "nsubj", "cop", "advmod", "root", "punct", "mark", "mark", "advcl", "punct", "cc", "det", "amod", "nsubj:pass", "aux:pass", "conj", "compound:prt", "case", "advmod", "amod", "obl", "punct", "det", "compound", "nsubj", "aux", "aux", "parataxis", "punct", "advmod", "compound", "nsubj", "cc", "amod", "conj", "aux", "aux", "parataxis", "obj", "mark", "advcl", "punct"], "triples": [{"uid": "1345-0", "target_tags": "Barbecued\\B codfish\\I was\\O gorgeously\\O moist\\O -\\O as\\O if\\O poached\\O -\\O yet\\O the\\O fabulous\\O texture\\O was\\O let\\O down\\O by\\O curiously\\O bland\\O seasoning\\O -\\O a\\O spice\\O rub\\O might\\O have\\O overwhelmed\\O ,\\O however\\O herb\\O mix\\O or\\O other\\O sauce\\O would\\O have\\O done\\O much\\O to\\O enhance\\O .\\O", "opinion_tags": "Barbecued\\O codfish\\O was\\O gorgeously\\O moist\\B -\\O as\\O if\\O poached\\O -\\O yet\\O the\\O fabulous\\O texture\\O was\\O let\\O down\\O by\\O curiously\\O bland\\O seasoning\\O -\\O a\\O spice\\O rub\\O might\\O have\\O overwhelmed\\O ,\\O however\\O herb\\O mix\\O or\\O other\\O sauce\\O would\\O have\\O done\\O much\\O to\\O enhance\\O .\\O", "sentiment": "positive"}, {"uid": "1345-1", "target_tags": "Barbecued\\O codfish\\O was\\O gorgeously\\O moist\\O -\\O as\\O if\\O poached\\O -\\O yet\\O the\\O fabulous\\O texture\\O was\\O let\\O down\\O by\\O curiously\\O bland\\O seasoning\\B -\\O a\\O spice\\O rub\\O might\\O have\\O overwhelmed\\O ,\\O however\\O herb\\O mix\\O or\\O other\\O sauce\\O would\\O have\\O done\\O much\\O to\\O enhance\\O .\\O", "opinion_tags": "Barbecued\\O codfish\\O was\\O gorgeously\\O moist\\O -\\O as\\O if\\O poached\\O -\\O yet\\O the\\O fabulous\\O texture\\O was\\O let\\O down\\O by\\O curiously\\O bland\\B seasoning\\O -\\O a\\O spice\\O rub\\O might\\O have\\O overwhelmed\\O ,\\O however\\O herb\\O mix\\O or\\O other\\O sauce\\O would\\O have\\O done\\O much\\O to\\O enhance\\O .\\O", "sentiment": "negative"}, {"uid": "1345-3", "target_tags": "Barbecued\\O codfish\\O was\\O gorgeously\\O moist\\O -\\O as\\O if\\O poached\\O -\\O yet\\O the\\O fabulous\\O texture\\O was\\O let\\O down\\O by\\O curiously\\O bland\\O seasoning\\O -\\O a\\O spice\\B rub\\I might\\O have\\O overwhelmed\\O ,\\O however\\O herb\\O mix\\O or\\O other\\O sauce\\O would\\O have\\O done\\O much\\O to\\O enhance\\O .\\O", "opinion_tags": "Barbecued\\O codfish\\O was\\O gorgeously\\O moist\\O -\\O as\\O if\\O poached\\O -\\O yet\\O the\\O fabulous\\O texture\\O was\\O let\\O down\\O by\\O curiously\\O bland\\O seasoning\\O -\\O a\\O spice\\O rub\\O might\\O have\\O overwhelmed\\B ,\\O however\\O herb\\O mix\\O or\\O other\\O sauce\\O would\\O have\\O done\\O much\\O to\\O enhance\\O .\\O", "sentiment": "negative"}, {"uid": "1345-4", "target_tags": "Barbecued\\O codfish\\O was\\O gorgeously\\O moist\\O -\\O as\\O if\\O poached\\O -\\O yet\\O the\\O fabulous\\O texture\\O was\\O let\\O down\\O by\\O curiously\\O bland\\O seasoning\\O -\\O a\\O spice\\O rub\\O might\\O have\\O overwhelmed\\O ,\\O however\\O herb\\B mix\\I or\\O other\\O sauce\\O would\\O have\\O done\\O much\\O to\\O enhance\\O .\\O", "opinion_tags": "Barbecued\\O codfish\\O was\\O gorgeously\\O moist\\O -\\O as\\O if\\O poached\\O -\\O yet\\O the\\O fabulous\\O texture\\O was\\O let\\O down\\O by\\O curiously\\O bland\\O seasoning\\O -\\O a\\O spice\\O rub\\O might\\O have\\O overwhelmed\\O ,\\O however\\O herb\\O mix\\O or\\O other\\O sauce\\O would\\O have\\O done\\O much\\O to\\B enhance\\I .\\O", "sentiment": "negative"}, {"uid": "1345-5", "target_tags": "Barbecued\\O codfish\\O was\\O gorgeously\\O moist\\O -\\O as\\O if\\O poached\\O -\\O yet\\O the\\O fabulous\\O texture\\O was\\O let\\O down\\O by\\O curiously\\O bland\\O seasoning\\O -\\O a\\O spice\\O rub\\O might\\O have\\O overwhelmed\\O ,\\O however\\O herb\\O mix\\O or\\O other\\O sauce\\B would\\O have\\O done\\O much\\O to\\O enhance\\O .\\O", "opinion_tags": "Barbecued\\O codfish\\O was\\O gorgeously\\O moist\\O -\\O as\\O if\\O poached\\O -\\O yet\\O the\\O fabulous\\O texture\\O was\\O let\\O down\\O by\\O curiously\\O bland\\O seasoning\\O -\\O a\\O spice\\O rub\\O might\\O have\\O overwhelmed\\O ,\\O however\\O herb\\O mix\\O or\\O other\\O sauce\\O would\\O have\\O done\\O much\\O to\\B enhance\\I .\\O", "sentiment": "negative"}]}, {"id": "3590", "sentence": "great food , lt 's of it , more then one person can eat !", "postag": ["JJ", "NN", ",", "PRP", "VBZ", "IN", "PRP", ",", "JJR", "RB", "CD", "NN", "MD", "VB", "."], "head": [2, 0, 2, 7, 7, 7, 2, 2, 14, 14, 12, 14, 14, 2, 2], "deprel": ["amod", "root", "punct", "nsubj", "cop", "case", "parataxis", "punct", "advmod", "advmod", "nummod", "nsubj", "aux", "parataxis", "punct"], "triples": [{"uid": "3590-0", "target_tags": "great\\O food\\B ,\\O lt\\O 's\\O of\\O it\\O ,\\O more\\O then\\O one\\O person\\O can\\O eat\\O !\\O", "opinion_tags": "great\\B food\\O ,\\O lt\\O 's\\O of\\O it\\O ,\\O more\\O then\\O one\\O person\\O can\\O eat\\O !\\O", "sentiment": "positive"}]}, {"id": "2317", "sentence": "I started out with a Bombay beer which was big enough for two .", "postag": ["PRP", "VBD", "RP", "IN", "DT", "NNP", "NN", "WDT", "VBD", "JJ", "JJ", "IN", "CD", "."], "head": [2, 0, 2, 7, 7, 7, 2, 11, 11, 11, 7, 13, 11, 2], "deprel": ["nsubj", "root", "compound:prt", "case", "det", "compound", "obl", "nsubj", "cop", "amod", "acl:relcl", "case", "obl", "punct"], "triples": [{"uid": "2317-0", "target_tags": "I\\O started\\O out\\O with\\O a\\O Bombay\\B beer\\I which\\O was\\O big\\O enough\\O for\\O two\\O .\\O", "opinion_tags": "I\\O started\\O out\\O with\\O a\\O Bombay\\O beer\\O which\\O was\\O big\\B enough\\O for\\O two\\O .\\O", "sentiment": "positive"}]}, {"id": "1171", "sentence": "Never in my life did I think that I could be satisfied both in taste and in quantity for $ 3.00 in NYC .", "postag": ["RB", "IN", "PRP$", "NN", "VBD", "PRP", "VB", "IN", "PRP", "MD", "VB", "JJ", "CC", "IN", "NN", "CC", "IN", "NN", "IN", "$", "CD", "IN", "NNP", "."], "head": [7, 4, 4, 7, 7, 7, 0, 12, 12, 12, 12, 7, 15, 15, 12, 18, 18, 12, 20, 18, 20, 23, 20, 7], "deprel": ["advmod", "case", "nmod:poss", "obl", "aux", "nsubj", "root", "mark", "nsubj", "aux", "cop", "ccomp", "cc:preconj", "case", "obl", "cc", "case", "conj", "case", "nmod", "nummod", "case", "nmod", "punct"], "triples": [{"uid": "1171-0", "target_tags": "Never\\O in\\O my\\O life\\O did\\O I\\O think\\O that\\O I\\O could\\O be\\O satisfied\\O both\\O in\\O taste\\B and\\O in\\O quantity\\O for\\O $\\O 3.00\\O in\\O NYC\\O .\\O", "opinion_tags": "Never\\O in\\O my\\O life\\O did\\O I\\O think\\O that\\O I\\O could\\O be\\O satisfied\\B both\\O in\\O taste\\O and\\O in\\O quantity\\O for\\O $\\O 3.00\\O in\\O NYC\\O .\\O", "sentiment": "positive"}, {"uid": "1171-1", "target_tags": "Never\\O in\\O my\\O life\\O did\\O I\\O think\\O that\\O I\\O could\\O be\\O satisfied\\O both\\O in\\O taste\\O and\\O in\\O quantity\\B for\\O $\\O 3.00\\O in\\O NYC\\O .\\O", "opinion_tags": "Never\\O in\\O my\\O life\\O did\\O I\\O think\\O that\\O I\\O could\\O be\\O satisfied\\B both\\O in\\O taste\\O and\\O in\\O quantity\\O for\\O $\\O 3.00\\O in\\O NYC\\O .\\O", "sentiment": "positive"}]}, {"id": "2309", "sentence": "There is a downside if you 're ordering in -- the delivery guys have MAJOR attitude .", "postag": ["EX", "VBZ", "DT", "NN", "IN", "PRP", "VBP", "VBG", "RB", ",", "DT", "NN", "NNS", "VBP", "JJ", "NN", "."], "head": [2, 0, 4, 2, 8, 8, 8, 2, 8, 2, 13, 13, 14, 2, 16, 14, 2], "deprel": ["expl", "root", "det", "nsubj", "mark", "nsubj", "aux", "advcl", "advmod", "punct", "det", "compound", "nsubj", "parataxis", "amod", "obj", "punct"], "triples": [{"uid": "2309-0", "target_tags": "There\\O is\\O a\\O downside\\O if\\O you\\O 're\\O ordering\\O in\\O --\\O the\\O delivery\\B guys\\I have\\O MAJOR\\O attitude\\O .\\O", "opinion_tags": "There\\O is\\O a\\O downside\\B if\\O you\\O 're\\O ordering\\O in\\O --\\O the\\O delivery\\O guys\\O have\\O MAJOR\\O attitude\\O .\\O", "sentiment": "negative"}]}, {"id": "3667", "sentence": "But when you are seated the waitresses are great , they explain everything on the menu , and the price of the food is really cheap for the service you get .", "postag": ["CC", "WRB", "PRP", "VBP", "VBN", "DT", "NNS", "VBP", "JJ", ",", "PRP", "VBP", "NN", "IN", "DT", "NN", ",", "CC", "DT", "NN", "IN", "DT", "NN", "VBZ", "RB", "JJ", "IN", "DT", "NN", "PRP", "VBP", "."], "head": [26, 5, 5, 5, 9, 7, 9, 9, 0, 12, 12, 9, 12, 16, 16, 13, 26, 26, 20, 26, 23, 23, 20, 26, 26, 12, 29, 29, 26, 31, 29, 9], "deprel": ["cc", "mark", "nsubj:pass", "aux:pass", "advcl", "det", "nsubj", "cop", "root", "punct", "nsubj", "parataxis", "obj", "case", "det", "nmod", "punct", "cc", "det", "nsubj", "case", "det", "nmod", "cop", "advmod", "conj", "case", "det", "obl", "nsubj", "acl:relcl", "punct"], "triples": [{"uid": "3667-0", "target_tags": "But\\O when\\O you\\O are\\O seated\\O the\\O waitresses\\B are\\O great\\O ,\\O they\\O explain\\O everything\\O on\\O the\\O menu\\O ,\\O and\\O the\\O price\\O of\\O the\\O food\\O is\\O really\\O cheap\\O for\\O the\\O service\\O you\\O get\\O .\\O", "opinion_tags": "But\\O when\\O you\\O are\\O seated\\O the\\O waitresses\\O are\\O great\\B ,\\O they\\O explain\\O everything\\O on\\O the\\O menu\\O ,\\O and\\O the\\O price\\O of\\O the\\O food\\O is\\O really\\O cheap\\O for\\O the\\O service\\O you\\O get\\O .\\O", "sentiment": "positive"}, {"uid": "3667-1", "target_tags": "But\\O when\\O you\\O are\\O seated\\O the\\O waitresses\\O are\\O great\\O ,\\O they\\O explain\\O everything\\O on\\O the\\O menu\\O ,\\O and\\O the\\O price\\B of\\O the\\O food\\O is\\O really\\O cheap\\O for\\O the\\O service\\O you\\O get\\O .\\O", "opinion_tags": "But\\O when\\O you\\O are\\O seated\\O the\\O waitresses\\O are\\O great\\O ,\\O they\\O explain\\O everything\\O on\\O the\\O menu\\O ,\\O and\\O the\\O price\\O of\\O the\\O food\\O is\\O really\\O cheap\\B for\\O the\\O service\\O you\\O get\\O .\\O", "sentiment": "positive"}, {"uid": "3667-2", "target_tags": "But\\O when\\O you\\O are\\O seated\\O the\\O waitresses\\O are\\O great\\O ,\\O they\\O explain\\O everything\\O on\\O the\\O menu\\O ,\\O and\\O the\\O price\\O of\\O the\\O food\\B is\\O really\\O cheap\\O for\\O the\\O service\\O you\\O get\\O .\\O", "opinion_tags": "But\\O when\\O you\\O are\\O seated\\O the\\O waitresses\\O are\\O great\\O ,\\O they\\O explain\\O everything\\O on\\O the\\O menu\\O ,\\O and\\O the\\O price\\O of\\O the\\O food\\O is\\O really\\O cheap\\B for\\O the\\O service\\O you\\O get\\O .\\O", "sentiment": "neutral"}]}, {"id": "3392", "sentence": "i recommend the thai popcorn : )", "postag": ["PRP", "VBP", "DT", "JJ", "NN", ":", "-RRB-"], "head": [2, 0, 5, 5, 2, 2, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "punct", "punct"], "triples": [{"uid": "3392-0", "target_tags": "i\\O recommend\\O the\\O thai\\B popcorn\\I :\\O )\\O", "opinion_tags": "i\\O recommend\\B the\\O thai\\O popcorn\\O :\\O )\\O", "sentiment": "positive"}]}, {"id": "3121", "sentence": "But the staff was so horrible to us .", "postag": ["CC", "DT", "NN", "VBD", "RB", "JJ", "IN", "PRP", "."], "head": [6, 3, 6, 6, 6, 0, 8, 6, 6], "deprel": ["cc", "det", "nsubj", "cop", "advmod", "root", "case", "obl", "punct"], "triples": [{"uid": "3121-0", "target_tags": "But\\O the\\O staff\\B was\\O so\\O horrible\\O to\\O us\\O .\\O", "opinion_tags": "But\\O the\\O staff\\O was\\O so\\O horrible\\B to\\O us\\O .\\O", "sentiment": "negative"}]}, {"id": "1596", "sentence": "Our son loves pizza and we have a certified Neapolitan pizzaria in our home city ( Seattle ) , we liked this nearly as much - and the differences were more about personal preference than any reflection on either restaurant .", "postag": ["PRP$", "NN", "VBZ", "NN", "CC", "PRP", "VBP", "DT", "VBN", "JJ", "NN", "IN", "PRP$", "NN", "NN", "-LRB-", "NNP", "-RRB-", ",", "PRP", "VBD", "DT", "RB", "RB", "JJ", ",", "CC", "DT", "NNS", "VBD", "JJR", "IN", "JJ", "NN", "IN", "DT", "NN", "IN", "DT", "NN", "."], "head": [2, 3, 0, 3, 7, 7, 3, 11, 11, 11, 7, 15, 15, 15, 11, 17, 15, 17, 21, 21, 3, 21, 25, 25, 21, 3, 31, 29, 31, 31, 21, 34, 34, 31, 37, 37, 31, 40, 40, 37, 3], "deprel": ["nmod:poss", "nsubj", "root", "obj", "cc", "nsubj", "conj", "det", "amod", "amod", "obj", "case", "nmod:poss", "compound", "nmod", "punct", "appos", "punct", "punct", "nsubj", "parataxis", "obj", "advmod", "advmod", "advmod", "punct", "cc", "det", "nsubj", "cop", "conj", "case", "amod", "obl", "case", "det", "obl", "case", "det", "nmod", "punct"], "triples": [{"uid": "1596-0", "target_tags": "Our\\O son\\O loves\\O pizza\\B and\\O we\\O have\\O a\\O certified\\O Neapolitan\\O pizzaria\\O in\\O our\\O home\\O city\\O (\\O Seattle\\O )\\O ,\\O we\\O liked\\O this\\O nearly\\O as\\O much\\O -\\O and\\O the\\O differences\\O were\\O more\\O about\\O personal\\O preference\\O than\\O any\\O reflection\\O on\\O either\\O restaurant\\O .\\O", "opinion_tags": "Our\\O son\\O loves\\B pizza\\O and\\O we\\O have\\O a\\O certified\\O Neapolitan\\O pizzaria\\O in\\O our\\O home\\O city\\O (\\O Seattle\\O )\\O ,\\O we\\O liked\\O this\\O nearly\\O as\\O much\\O -\\O and\\O the\\O differences\\O were\\O more\\O about\\O personal\\O preference\\O than\\O any\\O reflection\\O on\\O either\\O restaurant\\O .\\O", "sentiment": "positive"}]}, {"id": "3247", "sentence": "My wife and I also enjoyed the spinach , the Shanghai low mein , and other attractions .", "postag": ["PRP$", "NN", "CC", "PRP", "RB", "VBD", "DT", "NN", ",", "DT", "NNP", "JJ", "NN", ",", "CC", "JJ", "NNS", "."], "head": [2, 6, 4, 2, 6, 0, 8, 6, 13, 13, 13, 13, 8, 17, 17, 17, 8, 6], "deprel": ["nmod:poss", "nsubj", "cc", "conj", "advmod", "root", "det", "obj", "punct", "det", "compound", "amod", "conj", "punct", "cc", "amod", "conj", "punct"], "triples": [{"uid": "3247-0", "target_tags": "My\\O wife\\O and\\O I\\O also\\O enjoyed\\O the\\O spinach\\B ,\\O the\\O Shanghai\\O low\\O mein\\O ,\\O and\\O other\\O attractions\\O .\\O", "opinion_tags": "My\\O wife\\O and\\O I\\O also\\O enjoyed\\B the\\O spinach\\O ,\\O the\\O Shanghai\\O low\\O mein\\O ,\\O and\\O other\\O attractions\\O .\\O", "sentiment": "positive"}, {"uid": "3247-1", "target_tags": "My\\O wife\\O and\\O I\\O also\\O enjoyed\\O the\\O spinach\\O ,\\O the\\O Shanghai\\B low\\I mein\\I ,\\O and\\O other\\O attractions\\O .\\O", "opinion_tags": "My\\O wife\\O and\\O I\\O also\\O enjoyed\\B the\\O spinach\\O ,\\O the\\O Shanghai\\O low\\O mein\\O ,\\O and\\O other\\O attractions\\O .\\O", "sentiment": "positive"}]}, {"id": "2746", "sentence": "I 've never had bad service and the fish is fresh and delicious .", "postag": ["PRP", "VBP", "RB", "VBN", "JJ", "NN", "CC", "DT", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 11, 9, 11, 11, 4, 13, 11, 4], "deprel": ["nsubj", "aux", "advmod", "root", "amod", "obj", "cc", "det", "nsubj", "cop", "conj", "cc", "conj", "punct"], "triples": [{"uid": "2746-0", "target_tags": "I\\O 've\\O never\\O had\\O bad\\O service\\B and\\O the\\O fish\\O is\\O fresh\\O and\\O delicious\\O .\\O", "opinion_tags": "I\\O 've\\O never\\B had\\I bad\\I service\\O and\\O the\\O fish\\O is\\O fresh\\O and\\O delicious\\O .\\O", "sentiment": "positive"}, {"uid": "2746-1", "target_tags": "I\\O 've\\O never\\O had\\O bad\\O service\\O and\\O the\\O fish\\B is\\O fresh\\O and\\O delicious\\O .\\O", "opinion_tags": "I\\O 've\\O never\\O had\\O bad\\O service\\O and\\O the\\O fish\\O is\\O fresh\\B and\\O delicious\\B .\\O", "sentiment": "positive"}]}, {"id": "3582", "sentence": "But , they were too big for the bun .", "postag": ["CC", ",", "PRP", "VBD", "RB", "JJ", "IN", "DT", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 6], "deprel": ["cc", "punct", "nsubj", "cop", "advmod", "root", "case", "det", "obl", "punct"], "triples": [{"uid": "3582-0", "target_tags": "But\\O ,\\O they\\O were\\O too\\O big\\O for\\O the\\O bun\\B .\\O", "opinion_tags": "But\\O ,\\O they\\O were\\O too\\B big\\I for\\O the\\O bun\\O .\\O", "sentiment": "neutral"}]}, {"id": "2400", "sentence": "Their exotic salad is basic ly a delicious little green salad with a peanut sauce that is perfect before their sweet basil fried tofu .", "postag": ["PRP$", "JJ", "NN", "VBZ", "JJ", "IN", "DT", "JJ", "JJ", "JJ", "NN", "IN", "DT", "NN", "NN", "WDT", "VBZ", "JJ", "IN", "PRP$", "JJ", "NN", "VBN", "NN", "."], "head": [3, 3, 5, 5, 0, 11, 11, 11, 11, 11, 5, 15, 15, 15, 11, 18, 18, 15, 24, 24, 22, 24, 24, 18, 5], "deprel": ["nmod:poss", "amod", "nsubj", "cop", "root", "case", "det", "amod", "amod", "amod", "obl", "case", "det", "compound", "nmod", "nsubj", "cop", "acl:relcl", "case", "nmod:poss", "amod", "compound", "amod", "obl", "punct"], "triples": [{"uid": "2400-0", "target_tags": "Their\\O exotic\\B salad\\I is\\O basic\\O ly\\O a\\O delicious\\O little\\O green\\O salad\\O with\\O a\\O peanut\\O sauce\\O that\\O is\\O perfect\\O before\\O their\\O sweet\\O basil\\O fried\\O tofu\\O .\\O", "opinion_tags": "Their\\O exotic\\O salad\\O is\\O basic\\O ly\\O a\\O delicious\\B little\\O green\\O salad\\O with\\O a\\O peanut\\O sauce\\O that\\O is\\O perfect\\O before\\O their\\O sweet\\O basil\\O fried\\O tofu\\O .\\O", "sentiment": "positive"}, {"uid": "2400-1", "target_tags": "Their\\O exotic\\O salad\\O is\\O basic\\O ly\\O a\\O delicious\\O little\\O green\\B salad\\I with\\O a\\O peanut\\O sauce\\O that\\O is\\O perfect\\O before\\O their\\O sweet\\O basil\\O fried\\O tofu\\O .\\O", "opinion_tags": "Their\\O exotic\\O salad\\O is\\O basic\\O ly\\O a\\O delicious\\B little\\B green\\O salad\\O with\\O a\\O peanut\\O sauce\\O that\\O is\\O perfect\\O before\\O their\\O sweet\\O basil\\O fried\\O tofu\\O .\\O", "sentiment": "positive"}, {"uid": "2400-2", "target_tags": "Their\\O exotic\\O salad\\O is\\O basic\\O ly\\O a\\O delicious\\O little\\O green\\O salad\\O with\\O a\\O peanut\\B sauce\\I that\\O is\\O perfect\\O before\\O their\\O sweet\\O basil\\O fried\\O tofu\\O .\\O", "opinion_tags": "Their\\O exotic\\O salad\\O is\\O basic\\O ly\\O a\\O delicious\\O little\\O green\\O salad\\O with\\O a\\O peanut\\O sauce\\O that\\O is\\O perfect\\B before\\O their\\O sweet\\O basil\\O fried\\O tofu\\O .\\O", "sentiment": "positive"}]}, {"id": "1303", "sentence": "There are much better places in NY with better prices .", "postag": ["EX", "VBP", "RB", "JJR", "NNS", "IN", "NNP", "IN", "JJR", "NNS", "."], "head": [2, 0, 4, 5, 2, 7, 5, 10, 10, 2, 2], "deprel": ["expl", "root", "advmod", "amod", "nsubj", "case", "nmod", "case", "amod", "obl", "punct"], "triples": [{"uid": "1303-0", "target_tags": "There\\O are\\O much\\O better\\O places\\O in\\O NY\\O with\\O better\\O prices\\B .\\O", "opinion_tags": "There\\O are\\O much\\O better\\O places\\O in\\O NY\\O with\\O better\\B prices\\O .\\O", "sentiment": "negative"}]}, {"id": "3371", "sentence": "Even the pasta is delicious here ( a rarity in New York pizza restaurants ) .", "postag": ["RB", "DT", "NN", "VBZ", "JJ", "RB", "-LRB-", "DT", "NN", "IN", "NNP", "NNP", "NN", "NNS", "-RRB-", "."], "head": [3, 3, 5, 5, 0, 5, 9, 9, 5, 14, 12, 14, 14, 9, 9, 5], "deprel": ["advmod", "det", "nsubj", "cop", "root", "advmod", "punct", "det", "parataxis", "case", "compound", "compound", "compound", "nmod", "punct", "punct"], "triples": [{"uid": "3371-0", "target_tags": "Even\\O the\\O pasta\\B is\\O delicious\\O here\\O (\\O a\\O rarity\\O in\\O New\\O York\\O pizza\\O restaurants\\O )\\O .\\O", "opinion_tags": "Even\\O the\\O pasta\\O is\\O delicious\\B here\\O (\\O a\\O rarity\\O in\\O New\\O York\\O pizza\\O restaurants\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "2479", "sentence": "We visited Bread Bar during January restaurant week and were so pleased with the menu selections and service .", "postag": ["PRP", "VBD", "NNP", "NNP", "IN", "NNP", "NN", "NN", "CC", "VBD", "RB", "JJ", "IN", "DT", "NN", "NNS", "CC", "NN", "."], "head": [2, 0, 4, 2, 8, 8, 8, 2, 12, 12, 12, 2, 16, 16, 16, 12, 18, 16, 2], "deprel": ["nsubj", "root", "compound", "obj", "case", "compound", "compound", "obl", "cc", "cop", "advmod", "conj", "case", "det", "compound", "obl", "cc", "conj", "punct"], "triples": [{"uid": "2479-0", "target_tags": "We\\O visited\\O Bread\\O Bar\\O during\\O January\\O restaurant\\O week\\O and\\O were\\O so\\O pleased\\O with\\O the\\O menu\\B selections\\I and\\O service\\O .\\O", "opinion_tags": "We\\O visited\\O Bread\\O Bar\\O during\\O January\\O restaurant\\O week\\O and\\O were\\O so\\O pleased\\B with\\O the\\O menu\\O selections\\O and\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "2479-1", "target_tags": "We\\O visited\\O Bread\\O Bar\\O during\\O January\\O restaurant\\O week\\O and\\O were\\O so\\O pleased\\O with\\O the\\O menu\\O selections\\O and\\O service\\B .\\O", "opinion_tags": "We\\O visited\\O Bread\\O Bar\\O during\\O January\\O restaurant\\O week\\O and\\O were\\O so\\O pleased\\B with\\O the\\O menu\\O selections\\O and\\O service\\O .\\O", "sentiment": "positive"}]}, {"id": "585", "sentence": "Good luck getting a table .", "postag": ["JJ", "NN", "VBG", "DT", "NN", "."], "head": [2, 0, 2, 5, 3, 2], "deprel": ["amod", "root", "acl", "det", "obj", "punct"], "triples": [{"uid": "585-0", "target_tags": "Good\\O luck\\O getting\\B a\\I table\\I .\\O", "opinion_tags": "Good\\B luck\\I getting\\O a\\O table\\O .\\O", "sentiment": "negative"}]}, {"id": "2666", "sentence": "Overall a disappointing experience for that price category .", "postag": ["RB", "DT", "JJ", "NN", "IN", "DT", "NN", "NN", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 4], "deprel": ["advmod", "det", "amod", "root", "case", "det", "compound", "nmod", "punct"], "triples": [{"uid": "2666-0", "target_tags": "Overall\\O a\\O disappointing\\O experience\\O for\\O that\\O price\\B category\\O .\\O", "opinion_tags": "Overall\\O a\\O disappointing\\B experience\\O for\\O that\\O price\\O category\\O .\\O", "sentiment": "negative"}]}, {"id": "551", "sentence": "We were worried we would have trouble getting in , but somehow managed to have a short wait .", "postag": ["PRP", "VBD", "JJ", "PRP", "MD", "VB", "NN", "VBG", "RB", ",", "CC", "RB", "VBD", "TO", "VB", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 6, 7, 8, 13, 13, 13, 3, 15, 13, 18, 18, 15, 3], "deprel": ["nsubj", "cop", "root", "nsubj", "aux", "ccomp", "obj", "acl", "compound:prt", "punct", "cc", "advmod", "conj", "mark", "xcomp", "det", "amod", "obj", "punct"], "triples": [{"uid": "551-0", "target_tags": "We\\O were\\O worried\\O we\\O would\\O have\\O trouble\\O getting\\O in\\O ,\\O but\\O somehow\\O managed\\O to\\O have\\O a\\O short\\O wait\\B .\\O", "opinion_tags": "We\\O were\\O worried\\O we\\O would\\O have\\O trouble\\O getting\\O in\\O ,\\O but\\O somehow\\O managed\\O to\\O have\\O a\\O short\\B wait\\O .\\O", "sentiment": "positive"}]}, {"id": "939", "sentence": "The food was authentic .", "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "939-0", "target_tags": "The\\O food\\B was\\O authentic\\O .\\O", "opinion_tags": "The\\O food\\O was\\O authentic\\B .\\O", "sentiment": "positive"}]}, {"id": "427", "sentence": "The food 's as good as ever .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "IN", "RB", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "obl", "punct"], "triples": [{"uid": "427-0", "target_tags": "The\\O food\\B 's\\O as\\O good\\O as\\O ever\\O .\\O", "opinion_tags": "The\\O food\\O 's\\O as\\O good\\B as\\O ever\\O .\\O", "sentiment": "positive"}]}, {"id": "1124", "sentence": "Our waitress was sweet and accomodating , not overbearing .", "postag": ["PRP$", "NN", "VBD", "JJ", "CC", "JJ", ",", "RB", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 9, 9, 4, 4], "deprel": ["nmod:poss", "nsubj", "cop", "root", "cc", "conj", "punct", "advmod", "conj", "punct"], "triples": [{"uid": "1124-0", "target_tags": "Our\\O waitress\\B was\\O sweet\\O and\\O accomodating\\O ,\\O not\\O overbearing\\O .\\O", "opinion_tags": "Our\\O waitress\\O was\\O sweet\\B and\\O accomodating\\B ,\\O not\\O overbearing\\O .\\O", "sentiment": "positive"}]}, {"id": "1059", "sentence": "delicious bagels , especially when right out of the oven .", "postag": ["JJ", "NNS", ",", "RB", "WRB", "RB", "IN", "IN", "DT", "NN", "."], "head": [2, 0, 2, 10, 10, 10, 10, 10, 10, 2, 2], "deprel": ["amod", "root", "punct", "advmod", "mark", "advmod", "case", "case", "det", "advcl", "punct"], "triples": [{"uid": "1059-0", "target_tags": "delicious\\O bagels\\B ,\\O especially\\O when\\O right\\O out\\O of\\O the\\O oven\\O .\\O", "opinion_tags": "delicious\\B bagels\\O ,\\O especially\\O when\\O right\\O out\\O of\\O the\\O oven\\O .\\O", "sentiment": "positive"}]}, {"id": "1003", "sentence": "Wonderful strawberry daiquiries as well !", "postag": ["JJ", "NN", "NNS", "RB", "RB", "."], "head": [3, 3, 0, 3, 4, 3], "deprel": ["amod", "compound", "root", "advmod", "fixed", "punct"], "triples": [{"uid": "1003-0", "target_tags": "Wonderful\\O strawberry\\B daiquiries\\I as\\O well\\O !\\O", "opinion_tags": "Wonderful\\B strawberry\\O daiquiries\\O as\\O well\\O !\\O", "sentiment": "positive"}]}, {"id": "2197", "sentence": "They need a kick out of it but until then the sushi is pretty good and the place is consistent .", "postag": ["PRP", "VBP", "DT", "NN", "IN", "IN", "PRP", "CC", "IN", "RB", "DT", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 0, 4, 2, 7, 7, 2, 15, 10, 15, 12, 15, 15, 15, 2, 20, 18, 20, 20, 15, 2], "deprel": ["nsubj", "root", "det", "obj", "case", "case", "obl", "cc", "case", "obl", "det", "nsubj", "cop", "advmod", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "2197-0", "target_tags": "They\\O need\\O a\\O kick\\O out\\O of\\O it\\O but\\O until\\O then\\O the\\O sushi\\B is\\O pretty\\O good\\O and\\O the\\O place\\O is\\O consistent\\O .\\O", "opinion_tags": "They\\O need\\O a\\O kick\\O out\\O of\\O it\\O but\\O until\\O then\\O the\\O sushi\\O is\\O pretty\\O good\\B and\\O the\\O place\\O is\\O consistent\\O .\\O", "sentiment": "positive"}, {"uid": "2197-1", "target_tags": "They\\O need\\O a\\O kick\\O out\\O of\\O it\\O but\\O until\\O then\\O the\\O sushi\\O is\\O pretty\\O good\\O and\\O the\\O place\\B is\\O consistent\\O .\\O", "opinion_tags": "They\\O need\\O a\\O kick\\O out\\O of\\O it\\O but\\O until\\O then\\O the\\O sushi\\O is\\O pretty\\O good\\O and\\O the\\O place\\O is\\O consistent\\B .\\O", "sentiment": "positive"}]}, {"id": "2404", "sentence": "The place is sleek , modern and playful and i will return again frequently .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "JJ", "CC", "JJ", "CC", "PRP", "MD", "VB", "RB", "RB", "."], "head": [2, 4, 4, 0, 6, 4, 8, 4, 12, 12, 12, 4, 12, 12, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "conj", "cc", "conj", "cc", "nsubj", "aux", "conj", "advmod", "advmod", "punct"], "triples": [{"uid": "2404-0", "target_tags": "The\\O place\\B is\\O sleek\\O ,\\O modern\\O and\\O playful\\O and\\O i\\O will\\O return\\O again\\O frequently\\O .\\O", "opinion_tags": "The\\O place\\O is\\O sleek\\B ,\\O modern\\B and\\O playful\\B and\\O i\\O will\\O return\\O again\\O frequently\\O .\\O", "sentiment": "positive"}]}, {"id": "1780", "sentence": "Despite a slightly limited menu , everything prepared is done to perfection , ultra fresh and a work of food art .", "postag": ["IN", "DT", "RB", "JJ", "NN", ",", "NN", "VBN", "VBZ", "VBN", "IN", "NN", ",", "NN", "JJ", "CC", "DT", "NN", "IN", "NN", "NN", "."], "head": [5, 5, 4, 5, 10, 5, 10, 7, 10, 0, 12, 10, 15, 15, 12, 18, 18, 12, 21, 21, 18, 10], "deprel": ["case", "det", "advmod", "amod", "obl", "punct", "nsubj:pass", "acl", "aux:pass", "root", "case", "obl", "punct", "compound", "amod", "cc", "det", "conj", "case", "compound", "nmod", "punct"], "triples": [{"uid": "1780-0", "target_tags": "Despite\\O a\\O slightly\\O limited\\O menu\\B ,\\O everything\\O prepared\\O is\\O done\\O to\\O perfection\\O ,\\O ultra\\O fresh\\O and\\O a\\O work\\O of\\O food\\O art\\O .\\O", "opinion_tags": "Despite\\O a\\O slightly\\O limited\\B menu\\O ,\\O everything\\O prepared\\O is\\O done\\O to\\O perfection\\O ,\\O ultra\\O fresh\\O and\\O a\\O work\\O of\\O food\\O art\\O .\\O", "sentiment": "negative"}, {"uid": "1780-1", "target_tags": "Despite\\O a\\O slightly\\O limited\\O menu\\O ,\\O everything\\O prepared\\O is\\O done\\O to\\O perfection\\O ,\\O ultra\\O fresh\\O and\\O a\\O work\\O of\\O food\\B art\\I .\\O", "opinion_tags": "Despite\\O a\\O slightly\\O limited\\O menu\\O ,\\O everything\\O prepared\\O is\\O done\\O to\\O perfection\\O ,\\O ultra\\B fresh\\I and\\O a\\O work\\O of\\O food\\O art\\O .\\O", "sentiment": "positive"}]}, {"id": "1161", "sentence": "Great bar , most gorgeous bartenders you 've ever seen ( specifically the blond lady ) .", "postag": ["JJ", "NN", ",", "RBS", "JJ", "NNS", "PRP", "VBP", "RB", "VBN", "-LRB-", "RB", "DT", "JJ", "NN", "-RRB-", "."], "head": [2, 0, 6, 5, 6, 2, 10, 10, 10, 6, 15, 15, 15, 15, 2, 15, 2], "deprel": ["amod", "root", "punct", "advmod", "amod", "conj", "nsubj", "aux", "advmod", "acl:relcl", "punct", "advmod", "det", "amod", "parataxis", "punct", "punct"], "triples": [{"uid": "1161-0", "target_tags": "Great\\O bar\\B ,\\O most\\O gorgeous\\O bartenders\\O you\\O 've\\O ever\\O seen\\O (\\O specifically\\O the\\O blond\\O lady\\O )\\O .\\O", "opinion_tags": "Great\\B bar\\O ,\\O most\\O gorgeous\\O bartenders\\O you\\O 've\\O ever\\O seen\\O (\\O specifically\\O the\\O blond\\O lady\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "1161-1", "target_tags": "Great\\O bar\\O ,\\O most\\O gorgeous\\O bartenders\\B you\\O 've\\O ever\\O seen\\O (\\O specifically\\O the\\O blond\\O lady\\O )\\O .\\O", "opinion_tags": "Great\\O bar\\O ,\\O most\\O gorgeous\\B bartenders\\O you\\O 've\\O ever\\O seen\\O (\\O specifically\\O the\\O blond\\O lady\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "198", "sentence": "Odd for Ave B , not just odd , The place attracts an eclectic crowd to say the least .", "postag": ["JJ", "IN", "NNP", "NNP", ",", "RB", "RB", "JJ", ",", "DT", "NN", "VBZ", "DT", "JJ", "NN", "TO", "VB", "DT", "JJS", "."], "head": [0, 3, 1, 3, 1, 8, 8, 1, 1, 11, 12, 1, 15, 15, 12, 17, 12, 19, 17, 1], "deprel": ["root", "case", "obl", "flat", "punct", "advmod", "advmod", "conj", "punct", "det", "nsubj", "parataxis", "det", "amod", "obj", "mark", "advcl", "det", "obj", "punct"], "triples": [{"uid": "198-0", "target_tags": "Odd\\O for\\O Ave\\O B\\O ,\\O not\\O just\\O odd\\O ,\\O The\\O place\\B attracts\\O an\\O eclectic\\O crowd\\O to\\O say\\O the\\O least\\O .\\O", "opinion_tags": "Odd\\O for\\O Ave\\O B\\O ,\\O not\\O just\\O odd\\B ,\\O The\\O place\\O attracts\\O an\\O eclectic\\O crowd\\O to\\O say\\O the\\O least\\O .\\O", "sentiment": "positive"}]}, {"id": "1548", "sentence": "The food is terrible and overall , I would have to say avoid at all costs .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "JJ", ",", "PRP", "MD", "VB", "TO", "VB", "VB", "IN", "DT", "NNS", "."], "head": [2, 4, 4, 0, 6, 4, 4, 10, 10, 4, 12, 10, 12, 16, 16, 13, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct", "nsubj", "aux", "parataxis", "mark", "xcomp", "xcomp", "case", "det", "obl", "punct"], "triples": [{"uid": "1548-0", "target_tags": "The\\O food\\B is\\O terrible\\O and\\O overall\\O ,\\O I\\O would\\O have\\O to\\O say\\O avoid\\O at\\O all\\O costs\\O .\\O", "opinion_tags": "The\\O food\\O is\\O terrible\\B and\\O overall\\O ,\\O I\\O would\\O have\\O to\\O say\\O avoid\\O at\\O all\\O costs\\O .\\O", "sentiment": "negative"}]}, {"id": "3370", "sentence": "The pizza is delicious and the proprietor is one of the nicest in NYC .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "CD", "IN", "DT", "JJS", "IN", "NNP", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 12, 12, 9, 14, 12, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "case", "det", "nmod", "case", "obl", "punct"], "triples": [{"uid": "3370-0", "target_tags": "The\\O pizza\\B is\\O delicious\\O and\\O the\\O proprietor\\O is\\O one\\O of\\O the\\O nicest\\O in\\O NYC\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O delicious\\B and\\O the\\O proprietor\\O is\\O one\\O of\\O the\\O nicest\\O in\\O NYC\\O .\\O", "sentiment": "positive"}, {"uid": "3370-1", "target_tags": "The\\O pizza\\O is\\O delicious\\O and\\O the\\O proprietor\\B is\\O one\\O of\\O the\\O nicest\\O in\\O NYC\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O delicious\\O and\\O the\\O proprietor\\O is\\O one\\O of\\O the\\O nicest\\B in\\O NYC\\O .\\O", "sentiment": "positive"}]}, {"id": "908", "sentence": "Orsay , is without a doubt one of the best values for authentic French food in NYC .", "postag": ["NNP", ",", "VBZ", "IN", "DT", "NN", "CD", "IN", "DT", "JJS", "NNS", "IN", "JJ", "JJ", "NN", "IN", "NNP", "."], "head": [7, 7, 7, 6, 6, 7, 0, 11, 11, 11, 7, 15, 15, 15, 11, 17, 15, 7], "deprel": ["nsubj", "punct", "cop", "case", "det", "obl", "root", "case", "det", "amod", "nmod", "case", "amod", "amod", "nmod", "case", "nmod", "punct"], "triples": [{"uid": "908-0", "target_tags": "Orsay\\O ,\\O is\\O without\\O a\\O doubt\\O one\\O of\\O the\\O best\\O values\\O for\\O authentic\\O French\\B food\\I in\\O NYC\\O .\\O", "opinion_tags": "Orsay\\O ,\\O is\\O without\\O a\\O doubt\\O one\\O of\\O the\\O best\\O values\\O for\\O authentic\\B French\\O food\\O in\\O NYC\\O .\\O", "sentiment": "positive"}]}, {"id": "1065", "sentence": "Nothing fancy but really good food with pretty reasonable price .", "postag": ["NN", "JJ", "CC", "RB", "JJ", "NN", "IN", "RB", "JJ", "NN", "."], "head": [0, 1, 6, 5, 6, 1, 10, 9, 10, 6, 1], "deprel": ["root", "amod", "cc", "advmod", "amod", "conj", "case", "advmod", "amod", "nmod", "punct"], "triples": [{"uid": "1065-0", "target_tags": "Nothing\\O fancy\\O but\\O really\\O good\\O food\\B with\\O pretty\\O reasonable\\O price\\O .\\O", "opinion_tags": "Nothing\\O fancy\\O but\\O really\\O good\\B food\\O with\\O pretty\\O reasonable\\O price\\O .\\O", "sentiment": "positive"}, {"uid": "1065-1", "target_tags": "Nothing\\O fancy\\O but\\O really\\O good\\O food\\O with\\O pretty\\O reasonable\\O price\\B .\\O", "opinion_tags": "Nothing\\O fancy\\O but\\O really\\O good\\O food\\O with\\O pretty\\O reasonable\\B price\\O .\\O", "sentiment": "positive"}]}, {"id": "3332", "sentence": "However , in the summer of 2003 , it seems the management has changed and the great big door has been replaced for a glass front ridding itself of the dark romantic getup .", "postag": ["RB", ",", "IN", "DT", "NN", "IN", "CD", ",", "PRP", "VBZ", "DT", "NN", "VBZ", "VBN", "CC", "DT", "JJ", "JJ", "NN", "VBZ", "VBN", "VBN", "IN", "DT", "NN", "NN", "VBG", "PRP", "IN", "DT", "JJ", "JJ", "NN", "."], "head": [10, 10, 5, 5, 10, 7, 5, 10, 10, 0, 12, 14, 14, 10, 22, 19, 19, 19, 22, 22, 22, 10, 26, 26, 26, 22, 22, 27, 33, 33, 33, 33, 27, 10], "deprel": ["advmod", "punct", "case", "det", "obl", "case", "nmod", "punct", "nsubj", "root", "det", "nsubj", "aux", "ccomp", "cc", "det", "amod", "amod", "nsubj:pass", "aux", "aux:pass", "conj", "case", "det", "compound", "obl", "advcl", "obj", "case", "det", "amod", "amod", "obl", "punct"], "triples": [{"uid": "3332-0", "target_tags": "However\\O ,\\O in\\O the\\O summer\\O of\\O 2003\\O ,\\O it\\O seems\\O the\\O management\\B has\\O changed\\O and\\O the\\O great\\O big\\O door\\O has\\O been\\O replaced\\O for\\O a\\O glass\\O front\\O ridding\\O itself\\O of\\O the\\O dark\\O romantic\\O getup\\O .\\O", "opinion_tags": "However\\O ,\\O in\\O the\\O summer\\O of\\O 2003\\O ,\\O it\\O seems\\O the\\O management\\O has\\O changed\\B and\\O the\\O great\\O big\\O door\\O has\\O been\\O replaced\\O for\\O a\\O glass\\O front\\O ridding\\O itself\\O of\\O the\\O dark\\O romantic\\O getup\\O .\\O", "sentiment": "neutral"}, {"uid": "3332-1", "target_tags": "However\\O ,\\O in\\O the\\O summer\\O of\\O 2003\\O ,\\O it\\O seems\\O the\\O management\\O has\\O changed\\O and\\O the\\O great\\O big\\O door\\B has\\O been\\O replaced\\O for\\O a\\O glass\\O front\\O ridding\\O itself\\O of\\O the\\O dark\\O romantic\\O getup\\O .\\O", "opinion_tags": "However\\O ,\\O in\\O the\\O summer\\O of\\O 2003\\O ,\\O it\\O seems\\O the\\O management\\O has\\O changed\\O and\\O the\\O great\\B big\\I door\\O has\\O been\\O replaced\\O for\\O a\\O glass\\O front\\O ridding\\O itself\\O of\\O the\\O dark\\O romantic\\O getup\\O .\\O", "sentiment": "positive"}]}, {"id": "3177", "sentence": "We ate at this Thai place following the reviews but very unhappy with the foods .", "postag": ["PRP", "VBD", "IN", "DT", "JJ", "NN", "VBG", "DT", "NNS", "CC", "RB", "JJ", "IN", "DT", "NNS", "."], "head": [2, 0, 6, 6, 6, 2, 9, 9, 2, 12, 12, 9, 15, 15, 12, 2], "deprel": ["nsubj", "root", "case", "det", "amod", "obl", "case", "det", "obl", "cc", "advmod", "conj", "case", "det", "obl", "punct"], "triples": [{"uid": "3177-0", "target_tags": "We\\O ate\\O at\\O this\\O Thai\\O place\\O following\\O the\\O reviews\\O but\\O very\\O unhappy\\O with\\O the\\O foods\\B .\\O", "opinion_tags": "We\\O ate\\O at\\O this\\O Thai\\O place\\O following\\O the\\O reviews\\O but\\O very\\O unhappy\\B with\\O the\\O foods\\O .\\O", "sentiment": "negative"}]}, {"id": "1301", "sentence": "The food was absolutely horrible !", "postag": ["DT", "NN", "VBD", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "1301-0", "target_tags": "The\\O food\\B was\\O absolutely\\O horrible\\O !\\O", "opinion_tags": "The\\O food\\O was\\O absolutely\\O horrible\\B !\\O", "sentiment": "negative"}]}, {"id": "3539", "sentence": "Similar to other Indian restaurants , they use the dinner special to attract customers at the door .", "postag": ["JJ", "IN", "JJ", "JJ", "NNS", ",", "PRP", "VBP", "DT", "NN", "JJ", "TO", "VB", "NNS", "IN", "DT", "NN", "."], "head": [8, 5, 5, 5, 1, 1, 8, 0, 10, 8, 10, 13, 8, 13, 17, 17, 13, 8], "deprel": ["advmod", "case", "amod", "amod", "obl", "punct", "nsubj", "root", "det", "obj", "amod", "mark", "advcl", "obj", "case", "det", "obl", "punct"], "triples": [{"uid": "3539-0", "target_tags": "Similar\\O to\\O other\\O Indian\\O restaurants\\O ,\\O they\\O use\\O the\\O dinner\\B special\\I to\\O attract\\O customers\\O at\\O the\\O door\\O .\\O", "opinion_tags": "Similar\\O to\\O other\\O Indian\\O restaurants\\O ,\\O they\\O use\\O the\\O dinner\\O special\\O to\\O attract\\B customers\\O at\\O the\\O door\\O .\\O", "sentiment": "neutral"}]}, {"id": "2128", "sentence": "Highly recommend this as great value for excellent sushi and service .", "postag": ["RB", "VBP", "DT", "IN", "JJ", "NN", "IN", "JJ", "NN", "CC", "NN", "."], "head": [2, 0, 2, 6, 6, 2, 9, 9, 6, 11, 9, 2], "deprel": ["advmod", "root", "obj", "case", "amod", "obl", "case", "amod", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "2128-0", "target_tags": "Highly\\O recommend\\O this\\O as\\O great\\O value\\O for\\O excellent\\O sushi\\B and\\O service\\O .\\O", "opinion_tags": "Highly\\O recommend\\O this\\O as\\O great\\O value\\O for\\O excellent\\B sushi\\O and\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "2128-1", "target_tags": "Highly\\O recommend\\O this\\O as\\O great\\O value\\O for\\O excellent\\O sushi\\O and\\O service\\B .\\O", "opinion_tags": "Highly\\O recommend\\O this\\O as\\O great\\O value\\O for\\O excellent\\B sushi\\O and\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "2128-2", "target_tags": "Highly\\O recommend\\O this\\O as\\O great\\O value\\B for\\O excellent\\O sushi\\O and\\O service\\O .\\O", "opinion_tags": "Highly\\O recommend\\O this\\O as\\O great\\B value\\O for\\O excellent\\O sushi\\O and\\O service\\O .\\O", "sentiment": "positive"}]}, {"id": "1906", "sentence": "My husband and I enjoyed each of the 6 taste size portions and left completely full .", "postag": ["PRP$", "NN", "CC", "PRP", "VBD", "DT", "IN", "DT", "CD", "NN", "NN", "NNS", "CC", "VBD", "RB", "JJ", "."], "head": [2, 5, 4, 2, 0, 5, 12, 12, 11, 11, 12, 6, 14, 5, 16, 14, 5], "deprel": ["nmod:poss", "nsubj", "cc", "conj", "root", "obj", "case", "det", "nummod", "compound", "compound", "nmod", "cc", "conj", "advmod", "xcomp", "punct"], "triples": [{"uid": "1906-0", "target_tags": "My\\O husband\\O and\\O I\\O enjoyed\\O each\\O of\\O the\\O 6\\O taste\\O size\\O portions\\B and\\O left\\O completely\\O full\\O .\\O", "opinion_tags": "My\\O husband\\O and\\O I\\O enjoyed\\B each\\O of\\O the\\O 6\\O taste\\O size\\O portions\\O and\\O left\\O completely\\O full\\O .\\O", "sentiment": "positive"}]}, {"id": "1971", "sentence": "The sides were ok and incredibly salty .", "postag": ["DT", "NNS", "VBD", "JJ", "CC", "RB", "JJ", "."], "head": [2, 4, 4, 0, 7, 7, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "1971-0", "target_tags": "The\\O sides\\B were\\O ok\\O and\\O incredibly\\O salty\\O .\\O", "opinion_tags": "The\\O sides\\O were\\O ok\\B and\\O incredibly\\O salty\\B .\\O", "sentiment": "negative"}]}, {"id": "116", "sentence": "I have known about this secret for the last 13 years , <PERSON> ( the Godfather ) has continued to serve food and wine for the gods at mortal prices .", "postag": ["PRP", "VBP", "VBN", "IN", "DT", "NN", "IN", "DT", "JJ", "CD", "NNS", ",", "NNP", "-LRB-", "DT", "NNP", "-RRB-", "VBZ", "VBN", "TO", "VB", "NN", "CC", "NN", "IN", "DT", "NNS", "IN", "JJ", "NNS", "."], "head": [3, 3, 0, 6, 6, 3, 11, 11, 11, 11, 3, 19, 19, 16, 16, 13, 16, 19, 3, 21, 19, 21, 24, 22, 27, 27, 21, 30, 30, 27, 3], "deprel": ["nsubj", "aux", "root", "case", "det", "obl", "case", "det", "amod", "nummod", "obl", "punct", "nsubj", "punct", "det", "appos", "punct", "aux", "parataxis", "mark", "xcomp", "obj", "cc", "conj", "case", "det", "obl", "case", "amod", "nmod", "punct"], "triples": [{"uid": "116-0", "target_tags": "I\\O have\\O known\\O about\\O this\\O secret\\O for\\O the\\O last\\O 13\\O years\\O ,\\O Emilio\\O (\\O the\\O Godfather\\O )\\O has\\O continued\\O to\\O serve\\O food\\O and\\O wine\\O for\\O the\\O gods\\O at\\O mortal\\O prices\\B .\\O", "opinion_tags": "I\\O have\\O known\\O about\\O this\\O secret\\O for\\O the\\O last\\O 13\\O years\\O ,\\O Emilio\\O (\\O the\\O Godfather\\O )\\O has\\O continued\\O to\\O serve\\O food\\O and\\O wine\\O for\\O the\\O gods\\O at\\O mortal\\B prices\\O .\\O", "sentiment": "positive"}]}, {"id": "2637", "sentence": "I have to say I have never had a disapointing meal here .", "postag": ["PRP", "VBP", "TO", "VB", "PRP", "VBP", "RB", "VBN", "DT", "JJ", "NN", "RB", "."], "head": [2, 0, 4, 2, 8, 8, 8, 4, 11, 11, 8, 8, 2], "deprel": ["nsubj", "root", "mark", "xcomp", "nsubj", "aux", "advmod", "ccomp", "det", "amod", "obj", "advmod", "punct"], "triples": [{"uid": "2637-0", "target_tags": "I\\O have\\O to\\O say\\O I\\O have\\O never\\O had\\O a\\O disapointing\\O meal\\B here\\O .\\O", "opinion_tags": "I\\O have\\O to\\O say\\O I\\O have\\O never\\B had\\I a\\I disapointing\\I meal\\O here\\O .\\O", "sentiment": "positive"}]}, {"id": "2444", "sentence": "The secret is the lunch menu which offers a complimentary appetizer with every entree ordered .", "postag": ["DT", "NN", "VBZ", "DT", "NN", "NN", "WDT", "VBZ", "DT", "JJ", "NN", "IN", "DT", "NN", "VBN", "."], "head": [2, 6, 6, 6, 6, 0, 8, 6, 11, 11, 8, 14, 14, 11, 14, 6], "deprel": ["det", "nsubj", "cop", "det", "compound", "root", "nsubj", "acl:relcl", "det", "amod", "obj", "case", "det", "nmod", "acl", "punct"], "triples": [{"uid": "2444-0", "target_tags": "The\\O secret\\O is\\O the\\O lunch\\B menu\\I which\\O offers\\O a\\O complimentary\\O appetizer\\O with\\O every\\O entree\\O ordered\\O .\\O", "opinion_tags": "The\\O secret\\B is\\O the\\O lunch\\O menu\\O which\\O offers\\O a\\O complimentary\\O appetizer\\O with\\O every\\O entree\\O ordered\\O .\\O", "sentiment": "positive"}, {"uid": "2444-1", "target_tags": "The\\O secret\\O is\\O the\\O lunch\\O menu\\O which\\O offers\\O a\\O complimentary\\O appetizer\\B with\\O every\\O entree\\O ordered\\O .\\O", "opinion_tags": "The\\O secret\\O is\\O the\\O lunch\\O menu\\O which\\O offers\\O a\\O complimentary\\B appetizer\\O with\\O every\\O entree\\O ordered\\O .\\O", "sentiment": "positive"}, {"uid": "2444-2", "target_tags": "The\\O secret\\O is\\O the\\O lunch\\O menu\\O which\\O offers\\O a\\O complimentary\\O appetizer\\O with\\O every\\O entree\\B ordered\\O .\\O", "opinion_tags": "The\\O secret\\O is\\O the\\O lunch\\O menu\\O which\\O offers\\O a\\O complimentary\\B appetizer\\O with\\O every\\O entree\\O ordered\\O .\\O", "sentiment": "positive"}]}, {"id": "3191", "sentence": "Try green curry with vegetables .", "postag": ["VB", "JJ", "NN", "IN", "NNS", "."], "head": [0, 3, 1, 5, 3, 1], "deprel": ["root", "amod", "obj", "case", "nmod", "punct"], "triples": [{"uid": "3191-0", "target_tags": "Try\\O green\\B curry\\I with\\I vegetables\\I .\\O", "opinion_tags": "Try\\B green\\O curry\\O with\\O vegetables\\O .\\O", "sentiment": "positive"}]}, {"id": "676", "sentence": "The flavors are very fresh and pretty unobtrusive , nothing flashy .", "postag": ["DT", "NNS", "VBP", "RB", "JJ", "CC", "RB", "JJ", ",", "NN", "JJ", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 5, 5, 10, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "advmod", "conj", "punct", "parataxis", "amod", "punct"], "triples": [{"uid": "676-0", "target_tags": "The\\O flavors\\B are\\O very\\O fresh\\O and\\O pretty\\O unobtrusive\\O ,\\O nothing\\O flashy\\O .\\O", "opinion_tags": "The\\O flavors\\O are\\O very\\O fresh\\B and\\O pretty\\O unobtrusive\\B ,\\O nothing\\O flashy\\O .\\O", "sentiment": "positive"}]}, {"id": "377", "sentence": "We were a group of 8 and well seved .", "postag": ["PRP", "VBD", "DT", "NN", "IN", "CD", "CC", "RB", "VBN", "."], "head": [4, 4, 4, 0, 6, 4, 9, 9, 4, 4], "deprel": ["nsubj", "cop", "det", "root", "case", "nmod", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "377-0", "target_tags": "We\\O were\\O a\\O group\\O of\\O 8\\O and\\O well\\O seved\\B .\\O", "opinion_tags": "We\\O were\\O a\\O group\\O of\\O 8\\O and\\O well\\B seved\\O .\\O", "sentiment": "positive"}]}, {"id": "3004", "sentence": "Admittedly , this is not the place for gigantic pieces of fish overflowing the plate ( and thank goodness , in my opinion ) but for simple , elegant sushi there is no better place in New York or anywhere in the US .", "postag": ["RB", ",", "DT", "VBZ", "RB", "DT", "NN", "IN", "JJ", "NNS", "IN", "NN", "VBG", "DT", "NN", "-LRB-", "CC", "VBP", "NN", ",", "IN", "PRP$", "NN", "-RRB-", "CC", "IN", "JJ", ",", "JJ", "NN", "EX", "VBZ", "DT", "JJR", "NN", "IN", "NNP", "NNP", "CC", "RB", "IN", "DT", "NNP", "."], "head": [7, 7, 7, 7, 7, 7, 0, 10, 10, 7, 12, 10, 12, 15, 13, 18, 18, 7, 18, 18, 23, 23, 18, 18, 32, 30, 30, 30, 30, 32, 32, 7, 35, 35, 32, 38, 38, 35, 40, 38, 43, 43, 40, 7], "deprel": ["advmod", "punct", "nsubj", "cop", "advmod", "det", "root", "case", "amod", "nmod", "case", "nmod", "acl", "det", "obj", "punct", "cc", "conj", "obj", "punct", "case", "nmod:poss", "obl", "punct", "cc", "case", "amod", "punct", "amod", "obl", "expl", "conj", "det", "amod", "nsubj", "case", "compound", "nmod", "cc", "conj", "case", "det", "nmod", "punct"], "triples": [{"uid": "3004-0", "target_tags": "Admittedly\\O ,\\O this\\O is\\O not\\O the\\O place\\O for\\O gigantic\\O pieces\\O of\\O fish\\O overflowing\\O the\\O plate\\O (\\O and\\O thank\\O goodness\\O ,\\O in\\O my\\O opinion\\O )\\O but\\O for\\O simple\\O ,\\O elegant\\O sushi\\B there\\O is\\O no\\O better\\O place\\O in\\O New\\O York\\O or\\O anywhere\\O in\\O the\\O US\\O .\\O", "opinion_tags": "Admittedly\\O ,\\O this\\O is\\O not\\O the\\O place\\O for\\O gigantic\\O pieces\\O of\\O fish\\O overflowing\\O the\\O plate\\O (\\O and\\O thank\\O goodness\\O ,\\O in\\O my\\O opinion\\O )\\O but\\O for\\O simple\\B ,\\O elegant\\B sushi\\O there\\O is\\O no\\O better\\O place\\O in\\O New\\O York\\O or\\O anywhere\\O in\\O the\\O US\\O .\\O", "sentiment": "positive"}]}, {"id": "1232", "sentence": "big and soft as well as good lunch food .", "postag": ["JJ", "CC", "JJ", "RB", "RB", "IN", "JJ", "NN", "NN", "."], "head": [0, 3, 1, 9, 4, 4, 9, 9, 1, 1], "deprel": ["root", "cc", "conj", "cc", "fixed", "fixed", "amod", "compound", "conj", "punct"], "triples": [{"uid": "1232-0", "target_tags": "big\\O and\\O soft\\O as\\O well\\O as\\O good\\O lunch\\B food\\I .\\O", "opinion_tags": "big\\B and\\O soft\\B as\\O well\\O as\\O good\\B lunch\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "3662", "sentence": "The service was attentive without being overbearing and each dish we tried was wonderful from the spring rolls to the cod with pineapple tempura .", "postag": ["DT", "NN", "VBD", "JJ", "IN", "VBG", "JJ", "CC", "DT", "NN", "PRP", "VBD", "VBD", "JJ", "IN", "DT", "NN", "NNS", "IN", "DT", "NN", "IN", "NN", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 14, 10, 14, 12, 10, 14, 4, 18, 18, 18, 14, 21, 21, 14, 24, 24, 21, 4], "deprel": ["det", "nsubj", "cop", "root", "mark", "cop", "advcl", "cc", "det", "nsubj", "nsubj", "acl:relcl", "cop", "conj", "case", "det", "compound", "obl", "case", "det", "obl", "case", "compound", "nmod", "punct"], "triples": [{"uid": "3662-0", "target_tags": "The\\O service\\B was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\O from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O", "opinion_tags": "The\\O service\\O was\\O attentive\\B without\\B being\\I overbearing\\I and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\O from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O", "sentiment": "positive"}, {"uid": "3662-1", "target_tags": "The\\O service\\O was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\B we\\O tried\\O was\\O wonderful\\O from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O", "opinion_tags": "The\\O service\\O was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\B from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O", "sentiment": "positive"}, {"uid": "3662-2", "target_tags": "The\\O service\\O was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\O from\\O the\\O spring\\B rolls\\I to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O", "opinion_tags": "The\\O service\\O was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\B from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O", "sentiment": "positive"}, {"uid": "3662-3", "target_tags": "The\\O service\\O was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\O from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\B with\\I pineapple\\I tempura\\I .\\O", "opinion_tags": "The\\O service\\O was\\O attentive\\O without\\O being\\O overbearing\\O and\\O each\\O dish\\O we\\O tried\\O was\\O wonderful\\B from\\O the\\O spring\\O rolls\\O to\\O the\\O cod\\O with\\O pineapple\\O tempura\\O .\\O", "sentiment": "positive"}]}, {"id": "486", "sentence": "The corned beef was tender and melted in my mouth .", "postag": ["DT", "NN", "NN", "VBD", "JJ", "CC", "VBD", "IN", "PRP$", "NN", "."], "head": [3, 3, 5, 5, 0, 7, 5, 10, 10, 7, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "cc", "conj", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "486-0", "target_tags": "The\\O corned\\B beef\\I was\\O tender\\O and\\O melted\\O in\\O my\\O mouth\\O .\\O", "opinion_tags": "The\\O corned\\O beef\\O was\\O tender\\B and\\O melted\\B in\\O my\\O mouth\\O .\\O", "sentiment": "positive"}]}, {"id": "775", "sentence": "The only disappointment was the coat check girls who did n't seem to know what a customer is on a realtively non-busy night ( for the coat check girls ) .", "postag": ["DT", "JJ", "NN", "VBD", "DT", "NN", "NN", "NNS", "WP", "VBD", "RB", "VB", "TO", "VB", "WP", "DT", "NN", "VBZ", "IN", "DT", "RB", "JJ", "NN", "-LRB-", "IN", "DT", "NN", "NN", "NNS", "-RRB-", "."], "head": [3, 3, 8, 8, 8, 8, 8, 0, 12, 12, 12, 8, 14, 12, 14, 17, 15, 15, 23, 23, 22, 23, 15, 29, 29, 29, 29, 29, 23, 29, 8], "deprel": ["det", "amod", "nsubj", "cop", "det", "compound", "compound", "root", "nsubj", "aux", "advmod", "acl:relcl", "mark", "xcomp", "ccomp", "det", "nsubj", "cop", "case", "det", "advmod", "amod", "obl", "punct", "case", "det", "compound", "compound", "nmod", "punct", "punct"], "triples": [{"uid": "775-0", "target_tags": "The\\O only\\O disappointment\\O was\\O the\\O coat\\B check\\I girls\\I who\\O did\\O n't\\O seem\\O to\\O know\\O what\\O a\\O customer\\O is\\O on\\O a\\O realtively\\O non-busy\\O night\\O (\\O for\\O the\\O coat\\O check\\O girls\\O )\\O .\\O", "opinion_tags": "The\\O only\\O disappointment\\B was\\O the\\O coat\\O check\\O girls\\O who\\O did\\O n't\\O seem\\O to\\O know\\O what\\O a\\O customer\\O is\\O on\\O a\\O realtively\\O non-busy\\O night\\O (\\O for\\O the\\O coat\\O check\\O girls\\O )\\O .\\O", "sentiment": "negative"}]}, {"id": "2223", "sentence": "The food was spicy and delicious .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "2223-0", "target_tags": "The\\O food\\B was\\O spicy\\O and\\O delicious\\O .\\O", "opinion_tags": "The\\O food\\O was\\O spicy\\B and\\O delicious\\B .\\O", "sentiment": "positive"}]}, {"id": "3413", "sentence": "I can not imagine a friendlier staff working in a restaurant .", "postag": ["PRP", "MD", "RB", "VB", "DT", "JJR", "NN", "VBG", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 7, 11, 11, 8, 4], "deprel": ["nsubj", "aux", "advmod", "root", "det", "amod", "obj", "acl", "case", "det", "obl", "punct"], "triples": [{"uid": "3413-0", "target_tags": "I\\O can\\O not\\O imagine\\O a\\O friendlier\\O staff\\B working\\O in\\O a\\O restaurant\\O .\\O", "opinion_tags": "I\\O can\\O not\\O imagine\\O a\\O friendlier\\B staff\\O working\\O in\\O a\\O restaurant\\O .\\O", "sentiment": "positive"}]}, {"id": "536", "sentence": "Be sure to try the seasonal , and always delicious , specials .", "postag": ["VB", "JJ", "TO", "VB", "DT", "JJ", ",", "CC", "RB", "JJ", ",", "NNS", "."], "head": [2, 0, 4, 2, 12, 12, 12, 10, 10, 6, 12, 4, 2], "deprel": ["cop", "root", "mark", "xcomp", "det", "amod", "punct", "cc", "advmod", "conj", "punct", "obj", "punct"], "triples": [{"uid": "536-0", "target_tags": "Be\\O sure\\O to\\O try\\O the\\O seasonal\\O ,\\O and\\O always\\O delicious\\O ,\\O specials\\B .\\O", "opinion_tags": "Be\\O sure\\O to\\O try\\B the\\O seasonal\\B ,\\O and\\O always\\O delicious\\B ,\\O specials\\O .\\O", "sentiment": "positive"}]}, {"id": "1100", "sentence": "Both times I was extremely dissappointed by the service , which was boarderline rude .", "postag": ["DT", "NNS", "PRP", "VBD", "RB", "JJ", "IN", "DT", "NN", ",", "WDT", "VBD", "RBR", "JJ", "."], "head": [2, 6, 6, 6, 6, 0, 9, 9, 6, 9, 14, 14, 14, 9, 6], "deprel": ["det", "obl:tmod", "nsubj", "cop", "advmod", "root", "case", "det", "obl", "punct", "nsubj", "cop", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "1100-0", "target_tags": "Both\\O times\\O I\\O was\\O extremely\\O dissappointed\\O by\\O the\\O service\\B ,\\O which\\O was\\O boarderline\\O rude\\O .\\O", "opinion_tags": "Both\\O times\\O I\\O was\\O extremely\\O dissappointed\\B by\\O the\\O service\\O ,\\O which\\O was\\O boarderline\\O rude\\B .\\O", "sentiment": "negative"}]}, {"id": "1931", "sentence": "Truly the mark of an attentive waiter .", "postag": ["RB", "DT", "NN", "IN", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 3], "deprel": ["advmod", "det", "root", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "1931-0", "target_tags": "Truly\\O the\\O mark\\O of\\O an\\O attentive\\O waiter\\B .\\O", "opinion_tags": "Truly\\O the\\O mark\\O of\\O an\\O attentive\\B waiter\\O .\\O", "sentiment": "positive"}]}, {"id": "2642", "sentence": "We a menu that rarely changes , e xcept for one or two specials , the quality and care they put in thier food in evident .", "postag": ["PRP", "DT", "NN", "WDT", "RB", "VBZ", ",", "NN", "NN", "IN", "CD", "CC", "CD", "NNS", ",", "DT", "NN", "CC", "NN", "PRP", "VBD", "IN", "PRP$", "NN", "IN", "JJ", "."], "head": [3, 3, 0, 6, 6, 3, 8, 3, 8, 14, 14, 13, 11, 8, 17, 17, 14, 19, 17, 21, 17, 24, 24, 21, 26, 21, 3], "deprel": ["nsubj", "det", "root", "nsubj", "advmod", "acl:relcl", "punct", "parataxis", "flat", "case", "nummod", "cc", "conj", "nmod", "punct", "det", "conj", "cc", "conj", "nsubj", "acl:relcl", "case", "nmod:poss", "obl", "case", "obl", "punct"], "triples": [{"uid": "2642-0", "target_tags": "We\\O a\\O menu\\O that\\O rarely\\O changes\\O ,\\O e\\O xcept\\O for\\O one\\O or\\O two\\O specials\\O ,\\O the\\O quality\\B and\\O care\\O they\\O put\\O in\\O thier\\O food\\O in\\O evident\\O .\\O", "opinion_tags": "We\\O a\\O menu\\O that\\O rarely\\O changes\\O ,\\O e\\O xcept\\O for\\O one\\O or\\O two\\O specials\\O ,\\O the\\O quality\\O and\\O care\\O they\\O put\\O in\\O thier\\O food\\O in\\O evident\\B .\\O", "sentiment": "positive"}, {"uid": "2642-1", "target_tags": "We\\O a\\O menu\\O that\\O rarely\\O changes\\O ,\\O e\\O xcept\\O for\\O one\\O or\\O two\\O specials\\O ,\\O the\\O quality\\O and\\O care\\B they\\O put\\O in\\O thier\\O food\\O in\\O evident\\O .\\O", "opinion_tags": "We\\O a\\O menu\\O that\\O rarely\\O changes\\O ,\\O e\\O xcept\\O for\\O one\\O or\\O two\\O specials\\O ,\\O the\\O quality\\O and\\O care\\O they\\O put\\O in\\O thier\\O food\\O in\\O evident\\B .\\O", "sentiment": "positive"}]}, {"id": "1518", "sentence": "this is the best secret place in midtown ' , I heard that from the bartender , after having brilliant food ( try steak with portobello mushrooms ) and drinks on the bar last Tuesday .", "postag": ["DT", "VBZ", "DT", "JJS", "JJ", "NN", "IN", "NN", "''", ",", "PRP", "VBD", "IN", "IN", "DT", "NN", ",", "IN", "VBG", "JJ", "NN", "-LRB-", "VB", "NN", "IN", "NN", "NNS", "-RRB-", "CC", "NNS", "IN", "DT", "NN", "JJ", "NNP", "."], "head": [6, 6, 6, 6, 6, 0, 8, 6, 6, 6, 12, 6, 19, 16, 16, 12, 19, 19, 12, 21, 19, 23, 19, 23, 27, 27, 23, 23, 30, 23, 33, 33, 30, 35, 23, 6], "deprel": ["nsubj", "cop", "det", "amod", "amod", "root", "case", "nmod", "punct", "punct", "nsubj", "parataxis", "mark", "case", "det", "ccomp", "punct", "mark", "advcl", "amod", "obj", "punct", "parataxis", "obj", "case", "compound", "obl", "punct", "cc", "conj", "case", "det", "nmod", "amod", "obl:tmod", "punct"], "triples": [{"uid": "1518-0", "target_tags": "this\\O is\\O the\\O best\\O secret\\O place\\O in\\O midtown\\O '\\O ,\\O I\\O heard\\O that\\O from\\O the\\O bartender\\O ,\\O after\\O having\\O brilliant\\O food\\B (\\O try\\O steak\\O with\\O portobello\\O mushrooms\\O )\\O and\\O drinks\\O on\\O the\\O bar\\O last\\O Tuesday\\O .\\O", "opinion_tags": "this\\O is\\O the\\O best\\O secret\\O place\\O in\\O midtown\\O '\\O ,\\O I\\O heard\\O that\\O from\\O the\\O bartender\\O ,\\O after\\O having\\O brilliant\\B food\\O (\\O try\\O steak\\O with\\O portobello\\O mushrooms\\O )\\O and\\O drinks\\O on\\O the\\O bar\\O last\\O Tuesday\\O .\\O", "sentiment": "positive"}, {"uid": "1518-1", "target_tags": "this\\O is\\O the\\O best\\O secret\\O place\\O in\\O midtown\\O '\\O ,\\O I\\O heard\\O that\\O from\\O the\\O bartender\\O ,\\O after\\O having\\O brilliant\\O food\\O (\\O try\\O steak\\O with\\O portobello\\O mushrooms\\O )\\O and\\O drinks\\B on\\O the\\O bar\\O last\\O Tuesday\\O .\\O", "opinion_tags": "this\\O is\\O the\\O best\\O secret\\O place\\O in\\O midtown\\O '\\O ,\\O I\\O heard\\O that\\O from\\O the\\O bartender\\O ,\\O after\\O having\\O brilliant\\B food\\O (\\O try\\O steak\\O with\\O portobello\\O mushrooms\\O )\\O and\\O drinks\\O on\\O the\\O bar\\O last\\O Tuesday\\O .\\O", "sentiment": "positive"}, {"uid": "1518-2", "target_tags": "this\\O is\\O the\\O best\\O secret\\O place\\O in\\O midtown\\O '\\O ,\\O I\\O heard\\O that\\O from\\O the\\O bartender\\O ,\\O after\\O having\\O brilliant\\O food\\O (\\O try\\O steak\\B with\\I portobello\\I mushrooms\\I )\\O and\\O drinks\\O on\\O the\\O bar\\O last\\O Tuesday\\O .\\O", "opinion_tags": "this\\O is\\O the\\O best\\O secret\\O place\\O in\\O midtown\\O '\\O ,\\O I\\O heard\\O that\\O from\\O the\\O bartender\\O ,\\O after\\O having\\O brilliant\\B food\\O (\\O try\\B steak\\O with\\O portobello\\O mushrooms\\O )\\O and\\O drinks\\O on\\O the\\O bar\\O last\\O Tuesday\\O .\\O", "sentiment": "positive"}]}, {"id": "3295", "sentence": "I must say the view of NYC is so beautiful !", "postag": ["PRP", "MD", "VB", "DT", "NN", "IN", "NNP", "VBZ", "RB", "JJ", "."], "head": [3, 3, 0, 5, 10, 7, 5, 10, 10, 3, 3], "deprel": ["nsubj", "aux", "root", "det", "nsubj", "case", "nmod", "cop", "advmod", "ccomp", "punct"], "triples": [{"uid": "3295-0", "target_tags": "I\\O must\\O say\\O the\\O view\\B of\\O NYC\\O is\\O so\\O beautiful\\O !\\O", "opinion_tags": "I\\O must\\O say\\O the\\O view\\O of\\O NYC\\O is\\O so\\O beautiful\\B !\\O", "sentiment": "positive"}]}, {"id": "1969", "sentence": "The service , wine selection , ambiance are all outstanding and deserve recognition .", "postag": ["DT", "NN", ",", "NN", "NN", ",", "NN", "VBP", "RB", "JJ", "CC", "VBP", "NN", "."], "head": [2, 10, 5, 5, 2, 7, 2, 10, 10, 0, 12, 10, 12, 10], "deprel": ["det", "nsubj", "punct", "compound", "conj", "punct", "conj", "cop", "advmod", "root", "cc", "conj", "obj", "punct"], "triples": [{"uid": "1969-0", "target_tags": "The\\O service\\B ,\\O wine\\O selection\\O ,\\O ambiance\\O are\\O all\\O outstanding\\O and\\O deserve\\O recognition\\O .\\O", "opinion_tags": "The\\O service\\O ,\\O wine\\O selection\\O ,\\O ambiance\\O are\\O all\\O outstanding\\B and\\O deserve\\O recognition\\O .\\O", "sentiment": "positive"}, {"uid": "1969-1", "target_tags": "The\\O service\\O ,\\O wine\\B selection\\I ,\\O ambiance\\O are\\O all\\O outstanding\\O and\\O deserve\\O recognition\\O .\\O", "opinion_tags": "The\\O service\\O ,\\O wine\\O selection\\O ,\\O ambiance\\O are\\O all\\O outstanding\\B and\\O deserve\\O recognition\\O .\\O", "sentiment": "positive"}, {"uid": "1969-2", "target_tags": "The\\O service\\O ,\\O wine\\O selection\\O ,\\O ambiance\\B are\\O all\\O outstanding\\O and\\O deserve\\O recognition\\O .\\O", "opinion_tags": "The\\O service\\O ,\\O wine\\O selection\\O ,\\O ambiance\\O are\\O all\\O outstanding\\B and\\O deserve\\O recognition\\O .\\O", "sentiment": "positive"}]}, {"id": "3207", "sentence": "We always have a delicious meal and always leave feeling satisfied .", "postag": ["PRP", "RB", "VBP", "DT", "JJ", "NN", "CC", "RB", "VBP", "VBG", "JJ", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 3, 9, 10, 3], "deprel": ["nsubj", "advmod", "root", "det", "amod", "obj", "cc", "advmod", "conj", "xcomp", "xcomp", "punct"], "triples": [{"uid": "3207-0", "target_tags": "We\\O always\\O have\\O a\\O delicious\\O meal\\B and\\O always\\O leave\\O feeling\\O satisfied\\O .\\O", "opinion_tags": "We\\O always\\O have\\O a\\O delicious\\B meal\\O and\\O always\\O leave\\O feeling\\O satisfied\\O .\\O", "sentiment": "positive"}]}, {"id": "3647", "sentence": "We were seated and ignored by waitstaff .", "postag": ["PRP", "VBD", "VBN", "CC", "VBN", "IN", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 5, 3], "deprel": ["nsubj:pass", "aux:pass", "root", "cc", "conj", "case", "obl", "punct"], "triples": [{"uid": "3647-0", "target_tags": "We\\O were\\O seated\\O and\\O ignored\\O by\\O waitstaff\\B .\\O", "opinion_tags": "We\\O were\\O seated\\O and\\O ignored\\B by\\O waitstaff\\O .\\O", "sentiment": "negative"}]}, {"id": "1960", "sentence": "20 minutes for our reservation but it gave us time to have a few cocktails and enjoy our surroundings and each other .", "postag": ["CD", "NNS", "IN", "PRP$", "NN", "CC", "PRP", "VBD", "PRP", "NN", "TO", "VB", "DT", "JJ", "NNS", "CC", "VB", "PRP$", "NNS", "CC", "DT", "JJ", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 8, 8, 12, 10, 15, 15, 12, 17, 12, 19, 17, 22, 22, 19, 2], "deprel": ["nummod", "root", "case", "nmod:poss", "nmod", "cc", "nsubj", "conj", "i<PERSON><PERSON>", "obj", "mark", "acl", "det", "amod", "obj", "cc", "conj", "nmod:poss", "obj", "cc", "det", "conj", "punct"], "triples": [{"uid": "1960-0", "target_tags": "20\\O minutes\\O for\\O our\\O reservation\\O but\\O it\\O gave\\O us\\O time\\O to\\O have\\O a\\O few\\O cocktails\\O and\\O enjoy\\O our\\O surroundings\\B and\\O each\\O other\\O .\\O", "opinion_tags": "20\\O minutes\\O for\\O our\\O reservation\\O but\\O it\\O gave\\O us\\O time\\O to\\O have\\O a\\O few\\O cocktails\\O and\\O enjoy\\B our\\O surroundings\\O and\\O each\\O other\\O .\\O", "sentiment": "positive"}]}, {"id": "1002", "sentence": "The view is spectacular , and the food is great .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 10, 10, 8, 10, 10, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "1002-0", "target_tags": "The\\O view\\B is\\O spectacular\\O ,\\O and\\O the\\O food\\O is\\O great\\O .\\O", "opinion_tags": "The\\O view\\O is\\O spectacular\\B ,\\O and\\O the\\O food\\O is\\O great\\O .\\O", "sentiment": "positive"}, {"uid": "1002-1", "target_tags": "The\\O view\\O is\\O spectacular\\O ,\\O and\\O the\\O food\\B is\\O great\\O .\\O", "opinion_tags": "The\\O view\\O is\\O spectacular\\O ,\\O and\\O the\\O food\\O is\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "744", "sentence": "Service was prompt and courteous .", "postag": ["NN", "VBD", "JJ", "CC", "JJ", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "744-0", "target_tags": "Service\\B was\\O prompt\\O and\\O courteous\\O .\\O", "opinion_tags": "Service\\O was\\O prompt\\B and\\O courteous\\B .\\O", "sentiment": "positive"}]}, {"id": "3518", "sentence": "I recommend their Pad See Ew , Pork Chops or Tofu plates .", "postag": ["PRP", "VBP", "PRP$", "NN", "VB", "NN", ",", "NN", "NNS", "CC", "NN", "NNS", "."], "head": [2, 0, 4, 2, 2, 5, 9, 9, 6, 12, 12, 6, 2], "deprel": ["nsubj", "root", "nmod:poss", "obj", "xcomp", "obj", "punct", "compound", "conj", "cc", "compound", "conj", "punct"], "triples": [{"uid": "3518-0", "target_tags": "I\\O recommend\\O their\\O Pad\\B See\\I Ew\\I ,\\O Pork\\O Chops\\O or\\O Tofu\\O plates\\O .\\O", "opinion_tags": "I\\O recommend\\B their\\O Pad\\O See\\O Ew\\O ,\\O Pork\\O Chops\\O or\\O Tofu\\O plates\\O .\\O", "sentiment": "positive"}, {"uid": "3518-1", "target_tags": "I\\O recommend\\O their\\O Pad\\O See\\O Ew\\O ,\\O Pork\\B Chops\\I or\\O Tofu\\O plates\\O .\\O", "opinion_tags": "I\\O recommend\\B their\\O Pad\\O See\\O Ew\\O ,\\O Pork\\O Chops\\O or\\O Tofu\\O plates\\O .\\O", "sentiment": "positive"}, {"uid": "3518-2", "target_tags": "I\\O recommend\\O their\\O Pad\\O See\\O Ew\\O ,\\O Pork\\O Chops\\O or\\O Tofu\\B plates\\I .\\O", "opinion_tags": "I\\O recommend\\B their\\O Pad\\O See\\O Ew\\O ,\\O Pork\\O Chops\\O or\\O Tofu\\O plates\\O .\\O", "sentiment": "positive"}]}, {"id": "3201", "sentence": "The service was excellent and the food was delicious .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "3201-0", "target_tags": "The\\O service\\B was\\O excellent\\O and\\O the\\O food\\O was\\O delicious\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\B and\\O the\\O food\\O was\\O delicious\\O .\\O", "sentiment": "positive"}, {"uid": "3201-1", "target_tags": "The\\O service\\O was\\O excellent\\O and\\O the\\O food\\B was\\O delicious\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\O and\\O the\\O food\\O was\\O delicious\\B .\\O", "sentiment": "positive"}, {"uid": "3201-2", "target_tags": "The\\O service\\B was\\O excellent\\O and\\O the\\O food\\O was\\O delicious\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\B and\\O the\\O food\\O was\\O delicious\\O .\\O", "sentiment": "positive"}, {"uid": "3201-3", "target_tags": "The\\O service\\O was\\O excellent\\O and\\O the\\O food\\B was\\O delicious\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\O and\\O the\\O food\\O was\\O delicious\\B .\\O", "sentiment": "positive"}]}, {"id": "916", "sentence": "The food was average or above including some surprising tasty dishes .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "RB", "VBG", "DT", "JJ", "JJ", "NNS", "."], "head": [2, 4, 4, 0, 6, 4, 11, 11, 11, 11, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "case", "det", "amod", "amod", "obl", "punct"], "triples": [{"uid": "916-0", "target_tags": "The\\O food\\B was\\O average\\O or\\O above\\O including\\O some\\O surprising\\O tasty\\O dishes\\O .\\O", "opinion_tags": "The\\O food\\O was\\O average\\B or\\I above\\I including\\O some\\O surprising\\O tasty\\O dishes\\O .\\O", "sentiment": "positive"}, {"uid": "916-1", "target_tags": "The\\O food\\O was\\O average\\O or\\O above\\O including\\O some\\O surprising\\O tasty\\O dishes\\B .\\O", "opinion_tags": "The\\O food\\O was\\O average\\O or\\O above\\O including\\O some\\O surprising\\O tasty\\B dishes\\O .\\O", "sentiment": "positive"}]}, {"id": "46", "sentence": "The food is above average for midtown and sligtly better than some of the other Heartland Breweries in the city .", "postag": ["DT", "NN", "VBZ", "IN", "NN", "IN", "NN", "CC", "RB", "JJR", "IN", "DT", "IN", "DT", "JJ", "NNP", "NNPS", "IN", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 7, 5, 10, 10, 5, 12, 10, 17, 17, 17, 17, 12, 20, 20, 17, 5], "deprel": ["det", "nsubj", "cop", "case", "root", "case", "nmod", "cc", "advmod", "conj", "case", "obl", "case", "det", "amod", "compound", "nmod", "case", "det", "nmod", "punct"], "triples": [{"uid": "46-0", "target_tags": "The\\O food\\B is\\O above\\O average\\O for\\O midtown\\O and\\O sligtly\\O better\\O than\\O some\\O of\\O the\\O other\\O Heartland\\O Breweries\\O in\\O the\\O city\\O .\\O", "opinion_tags": "The\\O food\\O is\\O above\\B average\\I for\\O midtown\\O and\\O sligtly\\O better\\B than\\O some\\O of\\O the\\O other\\O Heartland\\O Breweries\\O in\\O the\\O city\\O .\\O", "sentiment": "positive"}]}, {"id": "1159", "sentence": "After dinner , take your date to the HUGE dance floor , probably one of the biggest you 'll see in NY .", "postag": ["IN", "NN", ",", "VB", "PRP$", "NN", "IN", "DT", "JJ", "NN", "NN", ",", "RB", "CD", "IN", "DT", "JJS", "PRP", "MD", "VB", "IN", "NNP", "."], "head": [2, 4, 2, 0, 6, 4, 11, 11, 11, 11, 4, 4, 14, 4, 17, 17, 14, 20, 20, 17, 22, 20, 4], "deprel": ["case", "obl", "punct", "root", "nmod:poss", "obj", "case", "det", "amod", "compound", "obl", "punct", "advmod", "parataxis", "case", "det", "nmod", "nsubj", "aux", "acl:relcl", "case", "obl", "punct"], "triples": [{"uid": "1159-0", "target_tags": "After\\O dinner\\O ,\\O take\\O your\\O date\\O to\\O the\\O HUGE\\O dance\\B floor\\I ,\\O probably\\O one\\O of\\O the\\O biggest\\O you\\O 'll\\O see\\O in\\O NY\\O .\\O", "opinion_tags": "After\\O dinner\\O ,\\O take\\O your\\O date\\O to\\O the\\O HUGE\\B dance\\O floor\\O ,\\O probably\\O one\\O of\\O the\\O biggest\\B you\\O 'll\\O see\\O in\\O NY\\O .\\O", "sentiment": "positive"}]}, {"id": "186", "sentence": "The ambience is pretty and nice for conversation , so a casual lunch here would probably be best .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "JJ", "IN", "NN", ",", "RB", "DT", "JJ", "NN", "RB", "MD", "RB", "VB", "JJS", "."], "head": [2, 4, 4, 0, 6, 4, 8, 4, 18, 18, 13, 13, 18, 13, 18, 18, 18, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "case", "obl", "punct", "advmod", "det", "amod", "nsubj", "advmod", "aux", "advmod", "cop", "conj", "punct"], "triples": [{"uid": "186-0", "target_tags": "The\\O ambience\\B is\\O pretty\\O and\\O nice\\O for\\O conversation\\O ,\\O so\\O a\\O casual\\O lunch\\O here\\O would\\O probably\\O be\\O best\\O .\\O", "opinion_tags": "The\\O ambience\\O is\\O pretty\\B and\\O nice\\B for\\O conversation\\O ,\\O so\\O a\\O casual\\O lunch\\O here\\O would\\O probably\\O be\\O best\\O .\\O", "sentiment": "positive"}, {"uid": "186-1", "target_tags": "The\\O ambience\\O is\\O pretty\\O and\\O nice\\O for\\O conversation\\O ,\\O so\\O a\\O casual\\O lunch\\B here\\O would\\O probably\\O be\\O best\\O .\\O", "opinion_tags": "The\\O ambience\\O is\\O pretty\\O and\\O nice\\O for\\O conversation\\O ,\\O so\\O a\\O casual\\B lunch\\O here\\O would\\O probably\\O be\\O best\\O .\\O", "sentiment": "positive"}]}, {"id": "2491", "sentence": "Aside from the rushed service , we were very impressed with the food and the drinks .", "postag": ["RB", "IN", "DT", "VBN", "NN", ",", "PRP", "VBD", "RB", "JJ", "IN", "DT", "NN", "CC", "DT", "NNS", "."], "head": [10, 5, 5, 5, 1, 10, 10, 10, 10, 0, 13, 13, 10, 16, 16, 13, 10], "deprel": ["advmod", "case", "det", "amod", "obl", "punct", "nsubj", "cop", "advmod", "root", "case", "det", "obl", "cc", "det", "conj", "punct"], "triples": [{"uid": "2491-0", "target_tags": "Aside\\O from\\O the\\O rushed\\O service\\B ,\\O we\\O were\\O very\\O impressed\\O with\\O the\\O food\\O and\\O the\\O drinks\\O .\\O", "opinion_tags": "Aside\\O from\\O the\\O rushed\\B service\\O ,\\O we\\O were\\O very\\O impressed\\O with\\O the\\O food\\O and\\O the\\O drinks\\O .\\O", "sentiment": "negative"}, {"uid": "2491-1", "target_tags": "Aside\\O from\\O the\\O rushed\\O service\\O ,\\O we\\O were\\O very\\O impressed\\O with\\O the\\O food\\B and\\O the\\O drinks\\O .\\O", "opinion_tags": "Aside\\O from\\O the\\O rushed\\O service\\O ,\\O we\\O were\\O very\\O impressed\\B with\\O the\\O food\\O and\\O the\\O drinks\\O .\\O", "sentiment": "positive"}, {"uid": "2491-2", "target_tags": "Aside\\O from\\O the\\O rushed\\O service\\O ,\\O we\\O were\\O very\\O impressed\\O with\\O the\\O food\\O and\\O the\\O drinks\\B .\\O", "opinion_tags": "Aside\\O from\\O the\\O rushed\\O service\\O ,\\O we\\O were\\O very\\O impressed\\B with\\O the\\O food\\O and\\O the\\O drinks\\O .\\O", "sentiment": "positive"}]}, {"id": "1010", "sentence": "And the bill was outrageous .", "postag": ["CC", "DT", "NN", "VBD", "JJ", "."], "head": [5, 3, 5, 5, 0, 5], "deprel": ["cc", "det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "1010-0", "target_tags": "And\\O the\\O bill\\B was\\O outrageous\\O .\\O", "opinion_tags": "And\\O the\\O bill\\O was\\O outrageous\\B .\\O", "sentiment": "negative"}]}, {"id": "997", "sentence": "This is such a lovely , peaceful place to eat outside .", "postag": ["DT", "VBZ", "PDT", "DT", "JJ", ",", "JJ", "NN", "TO", "VB", "RB", "."], "head": [8, 8, 8, 8, 8, 8, 8, 0, 10, 8, 10, 8], "deprel": ["nsubj", "cop", "det:predet", "det", "amod", "punct", "amod", "root", "mark", "acl", "advmod", "punct"], "triples": [{"uid": "997-0", "target_tags": "This\\O is\\O such\\O a\\O lovely\\O ,\\O peaceful\\O place\\B to\\O eat\\O outside\\O .\\O", "opinion_tags": "This\\O is\\O such\\O a\\O lovely\\B ,\\O peaceful\\B place\\O to\\O eat\\O outside\\O .\\O", "sentiment": "positive"}]}, {"id": "3642", "sentence": "The food is outstanding and the service is quick , friendly and very professional .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "JJ", ",", "JJ", "CC", "RB", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 11, 9, 14, 14, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "punct", "conj", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "3642-0", "target_tags": "The\\O food\\B is\\O outstanding\\O and\\O the\\O service\\O is\\O quick\\O ,\\O friendly\\O and\\O very\\O professional\\O .\\O", "opinion_tags": "The\\O food\\O is\\O outstanding\\B and\\O the\\O service\\O is\\O quick\\O ,\\O friendly\\O and\\O very\\O professional\\O .\\O", "sentiment": "positive"}, {"uid": "3642-1", "target_tags": "The\\O food\\O is\\O outstanding\\O and\\O the\\O service\\B is\\O quick\\O ,\\O friendly\\O and\\O very\\O professional\\O .\\O", "opinion_tags": "The\\O food\\O is\\O outstanding\\O and\\O the\\O service\\O is\\O quick\\B ,\\O friendly\\B and\\O very\\O professional\\B .\\O", "sentiment": "positive"}]}, {"id": "2990", "sentence": "My steak au poivre was one of the worst I 've had .", "postag": ["PRP$", "NN", "NN", "NN", "VBD", "CD", "IN", "DT", "JJS", "PRP", "VBP", "VBN", "."], "head": [4, 3, 4, 6, 6, 0, 9, 9, 6, 12, 12, 9, 6], "deprel": ["nmod:poss", "compound", "compound", "nsubj", "cop", "root", "case", "det", "nmod", "nsubj", "aux", "acl:relcl", "punct"], "triples": [{"uid": "2990-0", "target_tags": "My\\O steak\\B au\\I poivre\\I was\\O one\\O of\\O the\\O worst\\O I\\O 've\\O had\\O .\\O", "opinion_tags": "My\\O steak\\O au\\O poivre\\O was\\O one\\O of\\O the\\O worst\\B I\\O 've\\O had\\O .\\O", "sentiment": "negative"}]}, {"id": "2018", "sentence": "I was pretty much full after our fondue appetizer .", "postag": ["PRP", "VBD", "RB", "RB", "JJ", "IN", "PRP$", "NN", "NN", "."], "head": [5, 5, 4, 5, 0, 9, 9, 9, 5, 5], "deprel": ["nsubj", "cop", "advmod", "advmod", "root", "case", "nmod:poss", "compound", "obl", "punct"], "triples": [{"uid": "2018-0", "target_tags": "I\\O was\\O pretty\\O much\\O full\\O after\\O our\\O fondue\\B appetizer\\I .\\O", "opinion_tags": "I\\O was\\O pretty\\O much\\O full\\B after\\O our\\O fondue\\O appetizer\\O .\\O", "sentiment": "neutral"}]}, {"id": "503", "sentence": "Service was excellent , and the AC worked very well too ( thank God , it was hot ! ) .", "postag": ["NN", "VBD", "JJ", ",", "CC", "DT", "NNP", "VBD", "RB", "RB", "RB", "-LRB-", "VBP", "NNP", ",", "PRP", "VBD", "JJ", ".", "-RRB-", "."], "head": [3, 3, 0, 8, 8, 7, 8, 3, 10, 8, 8, 13, 3, 13, 13, 18, 18, 13, 13, 13, 3], "deprel": ["nsubj", "cop", "root", "punct", "cc", "det", "nsubj", "conj", "advmod", "advmod", "advmod", "punct", "parataxis", "obj", "punct", "nsubj", "cop", "parataxis", "punct", "punct", "punct"], "triples": [{"uid": "503-0", "target_tags": "Service\\B was\\O excellent\\O ,\\O and\\O the\\O AC\\O worked\\O very\\O well\\O too\\O (\\O thank\\O God\\O ,\\O it\\O was\\O hot\\O !\\O )\\O .\\O", "opinion_tags": "Service\\O was\\O excellent\\B ,\\O and\\O the\\O AC\\O worked\\O very\\O well\\O too\\O (\\O thank\\O God\\O ,\\O it\\O was\\O hot\\O !\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "503-1", "target_tags": "Service\\O was\\O excellent\\O ,\\O and\\O the\\O AC\\B worked\\O very\\O well\\O too\\O (\\O thank\\O God\\O ,\\O it\\O was\\O hot\\O !\\O )\\O .\\O", "opinion_tags": "Service\\O was\\O excellent\\O ,\\O and\\O the\\O AC\\O worked\\O very\\O well\\B too\\O (\\O thank\\O God\\O ,\\O it\\O was\\O hot\\O !\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "2987", "sentence": "The food was delicious , the atmosphere was relaxed , and we have now adopted Plate 347 as our Secret on Second !", "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "JJ", ",", "CC", "PRP", "VBP", "RB", "VBN", "NNP", "CD", "IN", "PRP$", "NN", "IN", "NNP", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 15, 15, 15, 15, 15, 4, 15, 16, 20, 20, 15, 22, 20, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "conj", "punct", "cc", "nsubj", "aux", "advmod", "conj", "obj", "nummod", "case", "nmod:poss", "obl", "case", "nmod", "punct"], "triples": [{"uid": "2987-0", "target_tags": "The\\O food\\B was\\O delicious\\O ,\\O the\\O atmosphere\\O was\\O relaxed\\O ,\\O and\\O we\\O have\\O now\\O adopted\\O Plate\\O 347\\O as\\O our\\O Secret\\O on\\O Second\\O !\\O", "opinion_tags": "The\\O food\\O was\\O delicious\\B ,\\O the\\O atmosphere\\O was\\O relaxed\\O ,\\O and\\O we\\O have\\O now\\O adopted\\O Plate\\O 347\\O as\\O our\\O Secret\\O on\\O Second\\O !\\O", "sentiment": "positive"}, {"uid": "2987-1", "target_tags": "The\\O food\\O was\\O delicious\\O ,\\O the\\O atmosphere\\B was\\O relaxed\\O ,\\O and\\O we\\O have\\O now\\O adopted\\O Plate\\O 347\\O as\\O our\\O Secret\\O on\\O Second\\O !\\O", "opinion_tags": "The\\O food\\O was\\O delicious\\O ,\\O the\\O atmosphere\\O was\\O relaxed\\B ,\\O and\\O we\\O have\\O now\\O adopted\\O Plate\\O 347\\O as\\O our\\O Secret\\O on\\O Second\\O !\\O", "sentiment": "positive"}]}, {"id": "2192", "sentence": "My GF and I still choose to eat there a lot because of diverse cocktails , the chill decor , and the decent sushi .", "postag": ["PRP$", "NN", "CC", "PRP", "RB", "VBP", "TO", "VB", "EX", "DT", "NN", "IN", "IN", "JJ", "NNS", ",", "DT", "NN", "NN", ",", "CC", "DT", "JJ", "NN", "."], "head": [2, 6, 4, 2, 6, 0, 8, 6, 8, 11, 8, 15, 12, 15, 8, 19, 19, 19, 15, 24, 24, 24, 24, 15, 6], "deprel": ["nmod:poss", "nsubj", "cc", "conj", "advmod", "root", "mark", "xcomp", "advmod", "det", "obj", "case", "fixed", "amod", "obl", "punct", "det", "compound", "conj", "punct", "cc", "det", "amod", "conj", "punct"], "triples": [{"uid": "2192-0", "target_tags": "My\\O GF\\O and\\O I\\O still\\O choose\\O to\\O eat\\O there\\O a\\O lot\\O because\\O of\\O diverse\\O cocktails\\B ,\\O the\\O chill\\O decor\\O ,\\O and\\O the\\O decent\\O sushi\\O .\\O", "opinion_tags": "My\\O GF\\O and\\O I\\O still\\O choose\\O to\\O eat\\O there\\O a\\O lot\\O because\\O of\\O diverse\\B cocktails\\O ,\\O the\\O chill\\O decor\\O ,\\O and\\O the\\O decent\\O sushi\\O .\\O", "sentiment": "positive"}, {"uid": "2192-1", "target_tags": "My\\O GF\\O and\\O I\\O still\\O choose\\O to\\O eat\\O there\\O a\\O lot\\O because\\O of\\O diverse\\O cocktails\\O ,\\O the\\O chill\\O decor\\B ,\\O and\\O the\\O decent\\O sushi\\O .\\O", "opinion_tags": "My\\O GF\\O and\\O I\\O still\\O choose\\O to\\O eat\\O there\\O a\\O lot\\O because\\O of\\O diverse\\O cocktails\\O ,\\O the\\O chill\\B decor\\O ,\\O and\\O the\\O decent\\O sushi\\O .\\O", "sentiment": "positive"}, {"uid": "2192-2", "target_tags": "My\\O GF\\O and\\O I\\O still\\O choose\\O to\\O eat\\O there\\O a\\O lot\\O because\\O of\\O diverse\\O cocktails\\O ,\\O the\\O chill\\O decor\\O ,\\O and\\O the\\O decent\\O sushi\\B .\\O", "opinion_tags": "My\\O GF\\O and\\O I\\O still\\O choose\\O to\\O eat\\O there\\O a\\O lot\\O because\\O of\\O diverse\\O cocktails\\O ,\\O the\\O chill\\O decor\\O ,\\O and\\O the\\O decent\\B sushi\\O .\\O", "sentiment": "positive"}]}, {"id": "3103", "sentence": "Ask for <PERSON><PERSON> , the nicest bartender in manhattan .", "postag": ["VB", "IN", "NNP", ",", "DT", "JJS", "NN", "IN", "NNP", "."], "head": [0, 3, 1, 7, 7, 7, 3, 9, 7, 1], "deprel": ["root", "case", "obl", "punct", "det", "amod", "appos", "case", "nmod", "punct"], "triples": [{"uid": "3103-0", "target_tags": "Ask\\O for\\O Usha\\O ,\\O the\\O nicest\\O bartender\\B in\\O manhattan\\O .\\O", "opinion_tags": "Ask\\O for\\O Usha\\O ,\\O the\\O nicest\\B bartender\\O in\\O manhattan\\O .\\O", "sentiment": "positive"}]}, {"id": "126", "sentence": "Seating is ok even though sometimes there 's alot of people .", "postag": ["NN", "VBZ", "JJ", "RB", "IN", "RB", "EX", "VBZ", "NN", "IN", "NNS", "."], "head": [3, 3, 0, 8, 8, 8, 8, 3, 8, 11, 9, 3], "deprel": ["nsubj", "cop", "root", "advmod", "mark", "advmod", "expl", "advcl", "nsubj", "case", "nmod", "punct"], "triples": [{"uid": "126-0", "target_tags": "Seating\\B is\\O ok\\O even\\O though\\O sometimes\\O there\\O 's\\O alot\\O of\\O people\\O .\\O", "opinion_tags": "Seating\\O is\\O ok\\B even\\O though\\O sometimes\\O there\\O 's\\O alot\\O of\\O people\\O .\\O", "sentiment": "neutral"}]}, {"id": "1191", "sentence": "Knowledge of the chef and the waitress are below average .", "postag": ["NN", "IN", "DT", "NN", "CC", "DT", "NN", "VBP", "RB", "NN", "."], "head": [10, 4, 4, 1, 7, 7, 4, 10, 10, 0, 10], "deprel": ["nsubj", "case", "det", "nmod", "cc", "det", "conj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "1191-0", "target_tags": "Knowledge\\O of\\O the\\O chef\\B and\\O the\\O waitress\\O are\\O below\\O average\\O .\\O", "opinion_tags": "Knowledge\\O of\\O the\\O chef\\O and\\O the\\O waitress\\O are\\O below\\B average\\I .\\O", "sentiment": "negative"}, {"uid": "1191-1", "target_tags": "Knowledge\\O of\\O the\\O chef\\O and\\O the\\O waitress\\B are\\O below\\O average\\O .\\O", "opinion_tags": "Knowledge\\O of\\O the\\O chef\\O and\\O the\\O waitress\\O are\\O below\\B average\\I .\\O", "sentiment": "negative"}]}, {"id": "3155", "sentence": "They are often crowded on the weekends but they are efficient and accurate with their service .", "postag": ["PRP", "VBP", "RB", "JJ", "IN", "DT", "NNS", "CC", "PRP", "VBP", "JJ", "CC", "JJ", "IN", "PRP$", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 11, 11, 11, 4, 13, 11, 16, 16, 11, 4], "deprel": ["nsubj", "cop", "advmod", "root", "case", "det", "obl", "cc", "nsubj", "cop", "conj", "cc", "conj", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "3155-0", "target_tags": "They\\O are\\O often\\O crowded\\O on\\O the\\O weekends\\O but\\O they\\O are\\O efficient\\O and\\O accurate\\O with\\O their\\O service\\B .\\O", "opinion_tags": "They\\O are\\O often\\O crowded\\O on\\O the\\O weekends\\O but\\O they\\O are\\O efficient\\B and\\O accurate\\B with\\O their\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "3155-1", "target_tags": "They\\O are\\O often\\O crowded\\B on\\O the\\O weekends\\O but\\O they\\O are\\O efficient\\O and\\O accurate\\O with\\O their\\O service\\O .\\O", "opinion_tags": "They\\O are\\O often\\O crowded\\B on\\O the\\O weekends\\O but\\O they\\O are\\O efficient\\O and\\O accurate\\O with\\O their\\O service\\O .\\O", "sentiment": "negative"}]}, {"id": "2399", "sentence": "They are served with a free appetizer and the portions are perfect for lunch .", "postag": ["PRP", "VBP", "VBN", "IN", "DT", "JJ", "NN", "CC", "DT", "NNS", "VBP", "JJ", "IN", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 12, 10, 12, 12, 3, 14, 12, 3], "deprel": ["nsubj:pass", "aux:pass", "root", "case", "det", "amod", "obl", "cc", "det", "nsubj", "cop", "conj", "case", "obl", "punct"], "triples": [{"uid": "2399-0", "target_tags": "They\\O are\\O served\\O with\\O a\\O free\\O appetizer\\B and\\O the\\O portions\\O are\\O perfect\\O for\\O lunch\\O .\\O", "opinion_tags": "They\\O are\\O served\\O with\\O a\\O free\\B appetizer\\O and\\O the\\O portions\\O are\\O perfect\\O for\\O lunch\\O .\\O", "sentiment": "positive"}, {"uid": "2399-1", "target_tags": "They\\O are\\O served\\O with\\O a\\O free\\O appetizer\\O and\\O the\\O portions\\B are\\O perfect\\O for\\O lunch\\O .\\O", "opinion_tags": "They\\O are\\O served\\O with\\O a\\O free\\O appetizer\\O and\\O the\\O portions\\O are\\O perfect\\B for\\O lunch\\O .\\O", "sentiment": "positive"}]}, {"id": "2526", "sentence": "Deliveries often take up to an hour and the prices are higher than most other pizzerias in the area .", "postag": ["NNS", "RB", "VBP", "RP", "IN", "DT", "NN", "CC", "DT", "NNS", "VBP", "JJR", "IN", "JJS", "JJ", "NNS", "IN", "DT", "NN", "."], "head": [3, 3, 0, 3, 7, 7, 3, 12, 10, 12, 12, 3, 16, 16, 16, 12, 19, 19, 16, 3], "deprel": ["nsubj", "advmod", "root", "compound:prt", "case", "det", "obl", "cc", "det", "nsubj", "cop", "conj", "case", "amod", "amod", "obl", "case", "det", "nmod", "punct"], "triples": [{"uid": "2526-0", "target_tags": "Deliveries\\O often\\O take\\O up\\O to\\O an\\O hour\\O and\\O the\\O prices\\B are\\O higher\\O than\\O most\\O other\\O pizzerias\\O in\\O the\\O area\\O .\\O", "opinion_tags": "Deliveries\\O often\\O take\\O up\\O to\\O an\\O hour\\O and\\O the\\O prices\\O are\\O higher\\B than\\O most\\O other\\O pizzerias\\O in\\O the\\O area\\O .\\O", "sentiment": "negative"}]}, {"id": "300", "sentence": "My friend ordered some of their special sushi rolls which had excellent presentation and tasted great !", "postag": ["PRP$", "NN", "VBD", "DT", "IN", "PRP$", "JJ", "NN", "NNS", "WDT", "VBD", "JJ", "NN", "CC", "VBD", "JJ", "."], "head": [2, 3, 0, 3, 9, 9, 9, 9, 4, 11, 9, 13, 11, 15, 11, 15, 3], "deprel": ["nmod:poss", "nsubj", "root", "obj", "case", "nmod:poss", "amod", "compound", "nmod", "nsubj", "acl:relcl", "amod", "obj", "cc", "conj", "xcomp", "punct"], "triples": [{"uid": "300-0", "target_tags": "My\\O friend\\O ordered\\O some\\O of\\O their\\O special\\O sushi\\B rolls\\I which\\O had\\O excellent\\O presentation\\O and\\O tasted\\O great\\O !\\O", "opinion_tags": "My\\O friend\\O ordered\\O some\\O of\\O their\\O special\\B sushi\\O rolls\\O which\\O had\\O excellent\\B presentation\\O and\\O tasted\\O great\\B !\\O", "sentiment": "positive"}]}, {"id": "1539", "sentence": "We also ordered two hot dogs thinking they would be pretty good since there is a whole section on the menu devoted to them .", "postag": ["PRP", "RB", "VBD", "CD", "JJ", "NNS", "VBG", "PRP", "MD", "VB", "RB", "JJ", "IN", "EX", "VBZ", "DT", "JJ", "NN", "IN", "DT", "NN", "VBN", "IN", "PRP", "."], "head": [3, 3, 0, 6, 6, 3, 6, 12, 12, 12, 12, 7, 15, 15, 12, 18, 18, 15, 21, 21, 18, 18, 24, 22, 3], "deprel": ["nsubj", "advmod", "root", "nummod", "amod", "obj", "acl", "nsubj", "aux", "cop", "advmod", "ccomp", "mark", "expl", "advcl", "det", "amod", "nsubj", "case", "det", "nmod", "acl", "case", "obl", "punct"], "triples": [{"uid": "1539-0", "target_tags": "We\\O also\\O ordered\\O two\\O hot\\B dogs\\I thinking\\O they\\O would\\O be\\O pretty\\O good\\O since\\O there\\O is\\O a\\O whole\\O section\\O on\\O the\\O menu\\O devoted\\O to\\O them\\O .\\O", "opinion_tags": "We\\O also\\O ordered\\O two\\O hot\\O dogs\\O thinking\\O they\\O would\\O be\\O pretty\\O good\\B since\\O there\\O is\\O a\\O whole\\O section\\O on\\O the\\O menu\\O devoted\\O to\\O them\\O .\\O", "sentiment": "neutral"}]}, {"id": "699", "sentence": "The wine list is interesting and has many good values .", "postag": ["DT", "NN", "NN", "VBZ", "JJ", "CC", "VBZ", "JJ", "JJ", "NNS", "."], "head": [3, 3, 5, 5, 0, 7, 5, 10, 10, 7, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "cc", "conj", "amod", "amod", "obj", "punct"], "triples": [{"uid": "699-0", "target_tags": "The\\O wine\\B list\\I is\\O interesting\\O and\\O has\\O many\\O good\\O values\\O .\\O", "opinion_tags": "The\\O wine\\O list\\O is\\O interesting\\B and\\O has\\O many\\O good\\B values\\I .\\O", "sentiment": "positive"}]}, {"id": "1853", "sentence": "I had the salmon dish and while it was fine , for the price paid , I expected it to have some type of flavor .", "postag": ["PRP", "VBD", "DT", "NN", "NN", "CC", "IN", "PRP", "VBD", "JJ", ",", "IN", "DT", "NN", "VBN", ",", "PRP", "VBD", "PRP", "TO", "VB", "DT", "NN", "IN", "NN", "."], "head": [2, 0, 5, 5, 2, 18, 10, 10, 10, 18, 18, 15, 14, 15, 10, 18, 18, 2, 18, 21, 18, 23, 21, 25, 23, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "cc", "mark", "nsubj", "cop", "advcl", "punct", "mark", "det", "nsubj", "advcl", "punct", "nsubj", "conj", "obj", "mark", "xcomp", "det", "obj", "case", "nmod", "punct"], "triples": [{"uid": "1853-1", "target_tags": "I\\O had\\O the\\O salmon\\O dish\\O and\\O while\\O it\\O was\\O fine\\O ,\\O for\\O the\\O price\\O paid\\O ,\\O I\\O expected\\O it\\O to\\O have\\O some\\O type\\O of\\O flavor\\B .\\O", "opinion_tags": "I\\O had\\O the\\O salmon\\O dish\\O and\\O while\\O it\\O was\\O fine\\B ,\\O for\\O the\\O price\\O paid\\O ,\\O I\\O expected\\O it\\O to\\O have\\O some\\O type\\O of\\O flavor\\O .\\O", "sentiment": "negative"}, {"uid": "1853-2", "target_tags": "I\\O had\\O the\\O salmon\\O dish\\O and\\O while\\O it\\O was\\O fine\\O ,\\O for\\O the\\O price\\B paid\\O ,\\O I\\O expected\\O it\\O to\\O have\\O some\\O type\\O of\\O flavor\\O .\\O", "opinion_tags": "I\\O had\\O the\\O salmon\\O dish\\O and\\O while\\O it\\O was\\O fine\\B ,\\O for\\O the\\O price\\O paid\\O ,\\O I\\O expected\\O it\\O to\\O have\\O some\\O type\\O of\\O flavor\\O .\\O", "sentiment": "neutral"}]}, {"id": "2434", "sentence": "This is an amazing place to try some roti rolls .", "postag": ["DT", "VBZ", "DT", "JJ", "NN", "TO", "VB", "DT", "NN", "NNS", "."], "head": [5, 5, 5, 5, 0, 7, 5, 10, 10, 7, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "det", "compound", "obj", "punct"], "triples": [{"uid": "2434-0", "target_tags": "This\\O is\\O an\\O amazing\\O place\\O to\\O try\\O some\\O roti\\B rolls\\I .\\O", "opinion_tags": "This\\O is\\O an\\O amazing\\O place\\O to\\O try\\B some\\O roti\\O rolls\\O .\\O", "sentiment": "positive"}]}, {"id": "2663", "sentence": "It is so easy to get a reservation at a top place in NYC with a week 's notice .", "postag": ["PRP", "VBZ", "RB", "JJ", "TO", "VB", "DT", "NN", "IN", "DT", "JJ", "NN", "IN", "NNP", "IN", "DT", "NN", "POS", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 12, 12, 12, 6, 14, 6, 19, 17, 19, 17, 6, 4], "deprel": ["expl", "cop", "advmod", "root", "mark", "csubj", "det", "obj", "case", "det", "amod", "obl", "case", "obl", "case", "det", "nmod:poss", "case", "obl", "punct"], "triples": [{"uid": "2663-0", "target_tags": "It\\O is\\O so\\O easy\\O to\\O get\\O a\\O reservation\\B at\\O a\\O top\\O place\\O in\\O NYC\\O with\\O a\\O week\\O 's\\O notice\\O .\\O", "opinion_tags": "It\\O is\\O so\\O easy\\B to\\O get\\O a\\O reservation\\O at\\O a\\O top\\O place\\O in\\O NYC\\O with\\O a\\O week\\O 's\\O notice\\O .\\O", "sentiment": "positive"}]}, {"id": "2278", "sentence": "The menu choices are similar but the taste lacked more flavor than it looked .", "postag": ["DT", "NN", "NNS", "VBP", "JJ", "CC", "DT", "NN", "VBD", "JJR", "NN", "IN", "PRP", "VBD", "."], "head": [3, 3, 5, 5, 0, 9, 8, 9, 5, 11, 9, 14, 14, 9, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "cc", "det", "nsubj", "conj", "amod", "obj", "mark", "nsubj", "advcl", "punct"], "triples": [{"uid": "2278-0", "target_tags": "The\\O menu\\O choices\\O are\\O similar\\O but\\O the\\O taste\\B lacked\\O more\\O flavor\\O than\\O it\\O looked\\O .\\O", "opinion_tags": "The\\O menu\\O choices\\O are\\O similar\\O but\\O the\\O taste\\O lacked\\B more\\O flavor\\O than\\O it\\O looked\\O .\\O", "sentiment": "negative"}, {"uid": "2278-1", "target_tags": "The\\O menu\\B choices\\I are\\O similar\\O but\\O the\\O taste\\O lacked\\O more\\O flavor\\O than\\O it\\O looked\\O .\\O", "opinion_tags": "The\\O menu\\O choices\\O are\\O similar\\B but\\O the\\O taste\\O lacked\\O more\\O flavor\\O than\\O it\\O looked\\O .\\O", "sentiment": "neutral"}, {"uid": "2278-2", "target_tags": "The\\O menu\\O choices\\O are\\O similar\\O but\\O the\\O taste\\O lacked\\O more\\O flavor\\B than\\O it\\O looked\\O .\\O", "opinion_tags": "The\\O menu\\O choices\\O are\\O similar\\O but\\O the\\O taste\\O lacked\\B more\\O flavor\\O than\\O it\\O looked\\O .\\O", "sentiment": "negative"}]}, {"id": "2161", "sentence": "The lox is always fresh too .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "RB", "."], "head": [2, 5, 5, 5, 0, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "advmod", "punct"], "triples": [{"uid": "2161-0", "target_tags": "The\\O lox\\B is\\O always\\O fresh\\O too\\O .\\O", "opinion_tags": "The\\O lox\\O is\\O always\\O fresh\\B too\\O .\\O", "sentiment": "positive"}]}, {"id": "163", "sentence": "We could n't carry our conversation as we were routinely interrupted by waitress and servants asking us to order and hinting that we 're taking too much time -- amazing , we just sat down .", "postag": ["PRP", "MD", "RB", "VB", "PRP$", "NN", "IN", "PRP", "VBD", "RB", "VBN", "IN", "NN", "CC", "NNS", "VBG", "PRP", "TO", "VB", "CC", "VBG", "IN", "PRP", "VBP", "VBG", "RB", "JJ", "NN", ",", "JJ", ",", "PRP", "RB", "VBD", "RB", "."], "head": [4, 4, 4, 0, 6, 4, 11, 11, 11, 11, 4, 13, 11, 15, 13, 11, 16, 19, 16, 21, 19, 25, 25, 25, 21, 27, 28, 25, 4, 25, 4, 34, 34, 4, 34, 4], "deprel": ["nsubj", "aux", "advmod", "root", "nmod:poss", "obj", "mark", "nsubj:pass", "aux:pass", "advmod", "advcl", "case", "obl", "cc", "conj", "advcl", "obj", "mark", "xcomp", "cc", "conj", "mark", "nsubj", "aux", "ccomp", "advmod", "amod", "obj", "punct", "advmod", "punct", "nsubj", "advmod", "parataxis", "advmod", "punct"], "triples": [{"uid": "163-0", "target_tags": "We\\O could\\O n't\\O carry\\O our\\O conversation\\O as\\O we\\O were\\O routinely\\O interrupted\\O by\\O waitress\\B and\\O servants\\O asking\\O us\\O to\\O order\\O and\\O hinting\\O that\\O we\\O 're\\O taking\\O too\\O much\\O time\\O --\\O amazing\\O ,\\O we\\O just\\O sat\\O down\\O .\\O", "opinion_tags": "We\\O could\\O n't\\O carry\\O our\\O conversation\\O as\\O we\\O were\\O routinely\\O interrupted\\B by\\O waitress\\O and\\O servants\\O asking\\O us\\O to\\O order\\O and\\O hinting\\O that\\O we\\O 're\\O taking\\O too\\O much\\O time\\O --\\O amazing\\O ,\\O we\\O just\\O sat\\O down\\O .\\O", "sentiment": "negative"}, {"uid": "163-1", "target_tags": "We\\O could\\O n't\\O carry\\O our\\O conversation\\O as\\O we\\O were\\O routinely\\O interrupted\\O by\\O waitress\\O and\\O servants\\B asking\\O us\\O to\\O order\\O and\\O hinting\\O that\\O we\\O 're\\O taking\\O too\\O much\\O time\\O --\\O amazing\\O ,\\O we\\O just\\O sat\\O down\\O .\\O", "opinion_tags": "We\\O could\\O n't\\O carry\\O our\\O conversation\\O as\\O we\\O were\\O routinely\\O interrupted\\B by\\O waitress\\O and\\O servants\\O asking\\O us\\O to\\O order\\O and\\O hinting\\O that\\O we\\O 're\\O taking\\O too\\O much\\O time\\O --\\O amazing\\O ,\\O we\\O just\\O sat\\O down\\O .\\O", "sentiment": "negative"}]}, {"id": "3666", "sentence": "Yes you have to wait to be seated and because its small there is no waiting area and the seat at the bar was all taken .", "postag": ["UH", "PRP", "VBP", "TO", "VB", "TO", "VB", "VBN", "CC", "IN", "PRP$", "JJ", "EX", "VBZ", "DT", "NN", "NN", "CC", "DT", "NN", "IN", "DT", "NN", "VBD", "RB", "VBN", "."], "head": [3, 3, 0, 5, 3, 8, 8, 5, 26, 14, 12, 14, 14, 5, 17, 17, 14, 26, 20, 26, 23, 23, 20, 26, 26, 3, 3], "deprel": ["discourse", "nsubj", "root", "mark", "xcomp", "mark", "aux:pass", "advcl", "cc", "mark", "nmod:poss", "nsubj", "expl", "advcl", "det", "compound", "nsubj", "cc", "det", "nsubj:pass", "case", "det", "nmod", "aux:pass", "advmod", "conj", "punct"], "triples": [{"uid": "3666-0", "target_tags": "Yes\\O you\\O have\\O to\\O wait\\O to\\O be\\O seated\\O and\\O because\\O its\\O small\\O there\\O is\\O no\\O waiting\\B area\\I and\\O the\\O seat\\O at\\O the\\O bar\\O was\\O all\\O taken\\O .\\O", "opinion_tags": "Yes\\O you\\O have\\O to\\O wait\\O to\\O be\\O seated\\O and\\O because\\O its\\O small\\O there\\O is\\O no\\B waiting\\O area\\O and\\O the\\O seat\\O at\\O the\\O bar\\O was\\O all\\O taken\\O .\\O", "sentiment": "negative"}, {"uid": "3666-1", "target_tags": "Yes\\O you\\O have\\O to\\O wait\\O to\\O be\\O seated\\O and\\O because\\O its\\O small\\O there\\O is\\O no\\O waiting\\O area\\O and\\O the\\O seat\\B at\\O the\\O bar\\O was\\O all\\O taken\\O .\\O", "opinion_tags": "Yes\\O you\\O have\\O to\\O wait\\O to\\O be\\O seated\\O and\\O because\\O its\\O small\\O there\\O is\\O no\\O waiting\\O area\\O and\\O the\\O seat\\O at\\O the\\O bar\\O was\\O all\\B taken\\I .\\O", "sentiment": "negative"}, {"uid": "3666-2", "target_tags": "Yes\\O you\\O have\\O to\\O wait\\O to\\O be\\O seated\\O and\\O because\\O its\\O small\\O there\\O is\\O no\\O waiting\\O area\\O and\\O the\\O seat\\O at\\O the\\O bar\\B was\\O all\\O taken\\O .\\O", "opinion_tags": "Yes\\O you\\O have\\O to\\O wait\\O to\\O be\\O seated\\O and\\O because\\O its\\O small\\B there\\O is\\O no\\O waiting\\O area\\O and\\O the\\O seat\\O at\\O the\\O bar\\O was\\O all\\O taken\\O .\\O", "sentiment": "neutral"}]}, {"id": "2193", "sentence": "But the thai is definitely not great -- bland and undistinguished .", "postag": ["CC", "DT", "NN", "VBZ", "RB", "RB", "JJ", ",", "JJ", "CC", "JJ", "."], "head": [7, 3, 7, 7, 7, 7, 0, 9, 7, 11, 9, 7], "deprel": ["cc", "det", "nsubj", "cop", "advmod", "advmod", "root", "punct", "parataxis", "cc", "conj", "punct"], "triples": [{"uid": "2193-0", "target_tags": "But\\O the\\O thai\\B is\\O definitely\\O not\\O great\\O --\\O bland\\O and\\O undistinguished\\O .\\O", "opinion_tags": "But\\O the\\O thai\\O is\\O definitely\\O not\\B great\\I --\\O bland\\B and\\O undistinguished\\B .\\O", "sentiment": "negative"}]}, {"id": "1094", "sentence": "The food is amazing ! ! ! !", "postag": ["DT", "NN", "VBZ", "JJ", ".", ".", ".", "."], "head": [2, 4, 4, 0, 4, 4, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "punct", "punct", "punct"], "triples": [{"uid": "1094-0", "target_tags": "The\\O food\\B is\\O amazing\\O !\\O !\\O !\\O !\\O", "opinion_tags": "The\\O food\\O is\\O amazing\\B !\\O !\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "1681", "sentence": "Most of the servers are very attentive , friendly and quite attractive .", "postag": ["JJS", "IN", "DT", "NNS", "VBP", "RB", "JJ", ",", "JJ", "CC", "RB", "JJ", "."], "head": [7, 4, 4, 1, 7, 7, 0, 9, 7, 12, 12, 7, 7], "deprel": ["nsubj", "case", "det", "nmod", "cop", "advmod", "root", "punct", "conj", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "1681-0", "target_tags": "Most\\O of\\O the\\O servers\\B are\\O very\\O attentive\\O ,\\O friendly\\O and\\O quite\\O attractive\\O .\\O", "opinion_tags": "Most\\O of\\O the\\O servers\\O are\\O very\\O attentive\\B ,\\O friendly\\B and\\O quite\\O attractive\\B .\\O", "sentiment": "positive"}]}, {"id": "209", "sentence": "Great romantic place for a date ( try to get the corner booth table for a little privacy and to sit close ! ) .", "postag": ["JJ", "JJ", "NN", "IN", "DT", "NN", "-LRB-", "VB", "TO", "VB", "DT", "NN", "NN", "NN", "IN", "DT", "JJ", "NN", "CC", "TO", "VB", "JJ", ".", "-RRB-", "."], "head": [3, 3, 0, 6, 6, 3, 8, 3, 10, 8, 14, 14, 14, 10, 18, 18, 18, 10, 21, 21, 10, 21, 8, 8, 3], "deprel": ["amod", "amod", "root", "case", "det", "nmod", "punct", "parataxis", "mark", "xcomp", "det", "compound", "compound", "obj", "case", "det", "amod", "obl", "cc", "mark", "conj", "xcomp", "punct", "punct", "punct"], "triples": [{"uid": "209-0", "target_tags": "Great\\O romantic\\O place\\O for\\O a\\O date\\O (\\O try\\O to\\O get\\O the\\O corner\\B booth\\I table\\I for\\O a\\O little\\O privacy\\O and\\O to\\O sit\\O close\\O !\\O )\\O .\\O", "opinion_tags": "Great\\O romantic\\O place\\O for\\O a\\O date\\O (\\O try\\O to\\O get\\O the\\O corner\\O booth\\O table\\O for\\O a\\O little\\O privacy\\B and\\O to\\O sit\\O close\\O !\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "209-1", "target_tags": "Great\\O romantic\\O place\\B for\\O a\\O date\\O (\\O try\\O to\\O get\\O the\\O corner\\O booth\\O table\\O for\\O a\\O little\\O privacy\\O and\\O to\\O sit\\O close\\O !\\O )\\O .\\O", "opinion_tags": "Great\\B romantic\\I place\\O for\\O a\\O date\\O (\\O try\\O to\\O get\\O the\\O corner\\O booth\\O table\\O for\\O a\\O little\\O privacy\\O and\\O to\\O sit\\O close\\O !\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "3085", "sentence": "The place is larger than most and features adequate seating unlike most joints , and has a bar which deserves a mention .", "postag": ["DT", "NN", "VBZ", "JJR", "IN", "JJS", "CC", "VBZ", "JJ", "NN", "IN", "JJS", "NNS", ",", "CC", "VBZ", "DT", "NN", "WDT", "VBZ", "DT", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 8, 4, 10, 8, 13, 13, 10, 16, 16, 4, 18, 16, 20, 18, 22, 20, 4], "deprel": ["det", "nsubj", "cop", "root", "case", "obl", "cc", "conj", "amod", "obj", "case", "amod", "nmod", "punct", "cc", "conj", "det", "obj", "nsubj", "acl:relcl", "det", "obj", "punct"], "triples": [{"uid": "3085-0", "target_tags": "The\\O place\\O is\\O larger\\O than\\O most\\O and\\O features\\O adequate\\O seating\\B unlike\\O most\\O joints\\O ,\\O and\\O has\\O a\\O bar\\O which\\O deserves\\O a\\O mention\\O .\\O", "opinion_tags": "The\\O place\\O is\\O larger\\O than\\O most\\O and\\O features\\O adequate\\B seating\\O unlike\\O most\\O joints\\O ,\\O and\\O has\\O a\\O bar\\O which\\O deserves\\O a\\O mention\\O .\\O", "sentiment": "positive"}, {"uid": "3085-1", "target_tags": "The\\O place\\O is\\O larger\\O than\\O most\\O and\\O features\\O adequate\\O seating\\O unlike\\O most\\O joints\\O ,\\O and\\O has\\O a\\O bar\\B which\\O deserves\\O a\\O mention\\O .\\O", "opinion_tags": "The\\O place\\O is\\O larger\\O than\\O most\\O and\\O features\\O adequate\\O seating\\O unlike\\O most\\O joints\\O ,\\O and\\O has\\O a\\O bar\\O which\\O deserves\\B a\\O mention\\O .\\O", "sentiment": "positive"}, {"uid": "3085-2", "target_tags": "The\\O place\\B is\\O larger\\O than\\O most\\O and\\O features\\O adequate\\O seating\\O unlike\\O most\\O joints\\O ,\\O and\\O has\\O a\\O bar\\O which\\O deserves\\O a\\O mention\\O .\\O", "opinion_tags": "The\\O place\\O is\\O larger\\B than\\O most\\O and\\O features\\O adequate\\O seating\\O unlike\\O most\\O joints\\O ,\\O and\\O has\\O a\\O bar\\O which\\O deserves\\O a\\O mention\\O .\\O", "sentiment": "positive"}]}, {"id": "2962", "sentence": "Three courses - choices include excellent mussels , puff pastry goat cheese and salad with a delicious dressing , and a hanger steak au poivre that is out of this world .", "postag": ["CD", "NNS", ",", "NNS", "VBP", "JJ", "NNS", ",", "NN", "NN", "NN", "NN", "CC", "NN", "IN", "DT", "JJ", "NN", ",", "CC", "DT", "NN", "NN", "NN", "NN", "WDT", "VBZ", "IN", "IN", "DT", "NN", "."], "head": [2, 5, 2, 5, 0, 7, 5, 12, 10, 12, 12, 7, 14, 7, 18, 18, 18, 7, 25, 25, 25, 25, 25, 25, 7, 31, 31, 31, 31, 31, 25, 5], "deprel": ["nummod", "nsubj", "punct", "nsubj", "root", "amod", "obj", "punct", "compound", "compound", "compound", "conj", "cc", "conj", "case", "det", "amod", "nmod", "punct", "cc", "det", "compound", "compound", "compound", "conj", "nsubj", "cop", "case", "case", "det", "acl:relcl", "punct"], "triples": [{"uid": "2962-0", "target_tags": "Three\\O courses\\O -\\O choices\\O include\\O excellent\\O mussels\\B ,\\O puff\\O pastry\\O goat\\O cheese\\O and\\O salad\\O with\\O a\\O delicious\\O dressing\\O ,\\O and\\O a\\O hanger\\O steak\\O au\\O poivre\\O that\\O is\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "Three\\O courses\\O -\\O choices\\O include\\O excellent\\B mussels\\O ,\\O puff\\O pastry\\O goat\\O cheese\\O and\\O salad\\O with\\O a\\O delicious\\O dressing\\O ,\\O and\\O a\\O hanger\\O steak\\O au\\O poivre\\O that\\O is\\O out\\O of\\O this\\O world\\O .\\O", "sentiment": "positive"}, {"uid": "2962-1", "target_tags": "Three\\O courses\\O -\\O choices\\O include\\O excellent\\O mussels\\O ,\\O puff\\B pastry\\I goat\\I cheese\\I and\\O salad\\O with\\O a\\O delicious\\O dressing\\O ,\\O and\\O a\\O hanger\\O steak\\O au\\O poivre\\O that\\O is\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "Three\\O courses\\O -\\O choices\\O include\\O excellent\\B mussels\\O ,\\O puff\\O pastry\\O goat\\O cheese\\O and\\O salad\\O with\\O a\\O delicious\\O dressing\\O ,\\O and\\O a\\O hanger\\O steak\\O au\\O poivre\\O that\\O is\\O out\\O of\\O this\\O world\\O .\\O", "sentiment": "positive"}, {"uid": "2962-2", "target_tags": "Three\\O courses\\O -\\O choices\\O include\\O excellent\\O mussels\\O ,\\O puff\\O pastry\\O goat\\O cheese\\O and\\O salad\\B with\\I a\\I delicious\\I dressing\\I ,\\O and\\O a\\O hanger\\O steak\\O au\\O poivre\\O that\\O is\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "Three\\O courses\\O -\\O choices\\O include\\O excellent\\O mussels\\O ,\\O puff\\O pastry\\O goat\\O cheese\\O and\\O salad\\O with\\O a\\O delicious\\B dressing\\O ,\\O and\\O a\\O hanger\\O steak\\O au\\O poivre\\O that\\O is\\O out\\O of\\O this\\O world\\O .\\O", "sentiment": "positive"}, {"uid": "2962-3", "target_tags": "Three\\O courses\\O -\\O choices\\O include\\O excellent\\O mussels\\O ,\\O puff\\O pastry\\O goat\\O cheese\\O and\\O salad\\O with\\O a\\O delicious\\O dressing\\O ,\\O and\\O a\\O hanger\\B steak\\I au\\I poivre\\I that\\O is\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "Three\\O courses\\O -\\O choices\\O include\\O excellent\\O mussels\\O ,\\O puff\\O pastry\\O goat\\O cheese\\O and\\O salad\\O with\\O a\\O delicious\\O dressing\\O ,\\O and\\O a\\O hanger\\O steak\\O au\\O poivre\\O that\\O is\\O out\\B of\\I this\\I world\\I .\\O", "sentiment": "positive"}]}, {"id": "666", "sentence": "Great service , great food .", "postag": ["JJ", "NN", ",", "JJ", "NN", "."], "head": [2, 0, 2, 5, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct"], "triples": [{"uid": "666-0", "target_tags": "Great\\O service\\B ,\\O great\\O food\\O .\\O", "opinion_tags": "Great\\B service\\O ,\\O great\\O food\\O .\\O", "sentiment": "positive"}, {"uid": "666-1", "target_tags": "Great\\O service\\O ,\\O great\\O food\\B .\\O", "opinion_tags": "Great\\O service\\O ,\\O great\\B food\\O .\\O", "sentiment": "positive"}]}, {"id": "752", "sentence": "I have been there many times , and food is good and consistent .", "postag": ["PRP", "VBP", "VBN", "RB", "JJ", "NNS", ",", "CC", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 11, 11, 11, 11, 4, 13, 11, 4], "deprel": ["nsubj", "aux", "cop", "root", "amod", "obl:tmod", "punct", "cc", "nsubj", "cop", "conj", "cc", "conj", "punct"], "triples": [{"uid": "752-0", "target_tags": "I\\O have\\O been\\O there\\O many\\O times\\O ,\\O and\\O food\\B is\\O good\\O and\\O consistent\\O .\\O", "opinion_tags": "I\\O have\\O been\\O there\\O many\\O times\\O ,\\O and\\O food\\O is\\O good\\B and\\O consistent\\B .\\O", "sentiment": "positive"}]}, {"id": "182", "sentence": "I like the somosas , chai , and the chole , but the dhosas and dhal were kinda disappointing .", "postag": ["PRP", "VBP", "DT", "NNS", ",", "NN", ",", "CC", "DT", "NN", ",", "CC", "DT", "NN", "CC", "NN", "VBD", "RB", "JJ", "."], "head": [2, 0, 4, 2, 6, 4, 10, 10, 10, 4, 19, 19, 14, 19, 16, 14, 19, 19, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "punct", "conj", "punct", "cc", "det", "conj", "punct", "cc", "det", "nsubj", "cc", "conj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "182-0", "target_tags": "I\\O like\\O the\\O somosas\\B ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O", "opinion_tags": "I\\O like\\B the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O", "sentiment": "positive"}, {"uid": "182-1", "target_tags": "I\\O like\\O the\\O somosas\\O ,\\O chai\\B ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O", "opinion_tags": "I\\O like\\B the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O", "sentiment": "positive"}, {"uid": "182-2", "target_tags": "I\\O like\\O the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\B ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O", "opinion_tags": "I\\O like\\B the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O", "sentiment": "positive"}, {"uid": "182-3", "target_tags": "I\\O like\\O the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\B and\\O dhal\\O were\\O kinda\\O disappointing\\O .\\O", "opinion_tags": "I\\O like\\O the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\B .\\O", "sentiment": "negative"}, {"uid": "182-4", "target_tags": "I\\O like\\O the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\B were\\O kinda\\O disappointing\\O .\\O", "opinion_tags": "I\\O like\\O the\\O somosas\\O ,\\O chai\\O ,\\O and\\O the\\O chole\\O ,\\O but\\O the\\O dhosas\\O and\\O dhal\\O were\\O kinda\\O disappointing\\B .\\O", "sentiment": "negative"}]}, {"id": "1215", "sentence": "Great bagels , spreads and a good place to hang out in .", "postag": ["JJ", "NNS", ",", "NNS", "CC", "DT", "JJ", "NN", "TO", "VB", "RP", "IN", "."], "head": [2, 0, 4, 2, 8, 8, 8, 2, 10, 8, 10, 10, 2], "deprel": ["amod", "root", "punct", "conj", "cc", "det", "amod", "conj", "mark", "acl", "compound:prt", "obl", "punct"], "triples": [{"uid": "1215-0", "target_tags": "Great\\O bagels\\B ,\\O spreads\\O and\\O a\\O good\\O place\\O to\\O hang\\O out\\O in\\O .\\O", "opinion_tags": "Great\\B bagels\\O ,\\O spreads\\O and\\O a\\O good\\O place\\O to\\O hang\\O out\\O in\\O .\\O", "sentiment": "positive"}, {"uid": "1215-1", "target_tags": "Great\\O bagels\\O ,\\O spreads\\B and\\O a\\O good\\O place\\O to\\O hang\\O out\\O in\\O .\\O", "opinion_tags": "Great\\B bagels\\O ,\\O spreads\\O and\\O a\\O good\\O place\\O to\\O hang\\O out\\O in\\O .\\O", "sentiment": "positive"}]}, {"id": "2896", "sentence": "When going out for a nice dinner , I like a nice ambiance as well as very good food .", "postag": ["WRB", "VBG", "RP", "IN", "DT", "JJ", "NN", ",", "PRP", "VBP", "DT", "JJ", "NN", "RB", "RB", "IN", "RB", "JJ", "NN", "."], "head": [2, 10, 2, 7, 7, 7, 2, 2, 10, 0, 13, 13, 10, 19, 14, 14, 18, 19, 13, 10], "deprel": ["mark", "advcl", "compound:prt", "case", "det", "amod", "obl", "punct", "nsubj", "root", "det", "amod", "obj", "cc", "fixed", "fixed", "advmod", "amod", "conj", "punct"], "triples": [{"uid": "2896-0", "target_tags": "When\\O going\\O out\\O for\\O a\\O nice\\O dinner\\B ,\\O I\\O like\\O a\\O nice\\O ambiance\\O as\\O well\\O as\\O very\\O good\\O food\\O .\\O", "opinion_tags": "When\\O going\\O out\\O for\\O a\\O nice\\B dinner\\O ,\\O I\\O like\\O a\\O nice\\O ambiance\\O as\\O well\\O as\\O very\\O good\\O food\\O .\\O", "sentiment": "positive"}, {"uid": "2896-1", "target_tags": "When\\O going\\O out\\O for\\O a\\O nice\\O dinner\\O ,\\O I\\O like\\O a\\O nice\\O ambiance\\B as\\O well\\O as\\O very\\O good\\O food\\O .\\O", "opinion_tags": "When\\O going\\O out\\O for\\O a\\O nice\\O dinner\\O ,\\O I\\O like\\O a\\O nice\\B ambiance\\O as\\O well\\O as\\O very\\O good\\O food\\O .\\O", "sentiment": "positive"}, {"uid": "2896-2", "target_tags": "When\\O going\\O out\\O for\\O a\\O nice\\O dinner\\O ,\\O I\\O like\\O a\\O nice\\O ambiance\\O as\\O well\\O as\\O very\\O good\\O food\\B .\\O", "opinion_tags": "When\\O going\\O out\\O for\\O a\\O nice\\O dinner\\O ,\\O I\\O like\\O a\\O nice\\O ambiance\\O as\\O well\\O as\\O very\\O good\\B food\\O .\\O", "sentiment": "positive"}]}, {"id": "2623", "sentence": "If you are looking for a good quality , cheap eats - this is the place .", "postag": ["IN", "PRP", "VBP", "VBG", "IN", "DT", "JJ", "NN", ",", "JJ", "NNS", ",", "DT", "VBZ", "DT", "NN", "."], "head": [4, 4, 4, 16, 8, 8, 8, 4, 11, 11, 8, 16, 16, 16, 16, 0, 16], "deprel": ["mark", "nsubj", "aux", "advcl", "case", "det", "amod", "obl", "punct", "amod", "appos", "punct", "nsubj", "cop", "det", "root", "punct"], "triples": [{"uid": "2623-0", "target_tags": "If\\O you\\O are\\O looking\\O for\\O a\\O good\\O quality\\B ,\\O cheap\\O eats\\O -\\O this\\O is\\O the\\O place\\O .\\O", "opinion_tags": "If\\O you\\O are\\O looking\\O for\\O a\\O good\\B quality\\O ,\\O cheap\\O eats\\O -\\O this\\O is\\O the\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "2647", "sentence": "Patroon features a nice cigar bar and has great staff .", "postag": ["NNP", "VBZ", "DT", "JJ", "NN", "NN", "CC", "VBZ", "JJ", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 8, 2, 10, 8, 2], "deprel": ["nsubj", "root", "det", "amod", "compound", "obj", "cc", "conj", "amod", "obj", "punct"], "triples": [{"uid": "2647-0", "target_tags": "Patroon\\O features\\O a\\O nice\\O cigar\\B bar\\I and\\O has\\O great\\O staff\\O .\\O", "opinion_tags": "Patroon\\O features\\O a\\O nice\\B cigar\\O bar\\O and\\O has\\O great\\O staff\\O .\\O", "sentiment": "positive"}, {"uid": "2647-1", "target_tags": "Patroon\\O features\\O a\\O nice\\O cigar\\O bar\\O and\\O has\\O great\\O staff\\B .\\O", "opinion_tags": "Patroon\\O features\\O a\\O nice\\O cigar\\O bar\\O and\\O has\\O great\\B staff\\O .\\O", "sentiment": "positive"}]}, {"id": "2768", "sentence": "Although the tables may be closely situated , the candle-light , food-quality and service overcompensate .", "postag": ["IN", "DT", "NNS", "MD", "VB", "RB", "VBN", ",", "DT", "NN", ",", "NN", "CC", "NN", "NN", "."], "head": [7, 3, 7, 7, 7, 7, 15, 15, 10, 15, 12, 10, 14, 10, 0, 15], "deprel": ["mark", "det", "nsubj:pass", "aux", "aux:pass", "advmod", "advcl", "punct", "det", "nsubj", "punct", "conj", "cc", "conj", "root", "punct"], "triples": [{"uid": "2768-0", "target_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\O situated\\O ,\\O the\\O candle-light\\B ,\\O food-quality\\O and\\O service\\O overcompensate\\O .\\O", "opinion_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\B situated\\I ,\\O the\\O candle-light\\O ,\\O food-quality\\O and\\O service\\O overcompensate\\O .\\O", "sentiment": "positive"}, {"uid": "2768-1", "target_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\O situated\\O ,\\O the\\O candle-light\\O ,\\O food-quality\\B and\\O service\\O overcompensate\\O .\\O", "opinion_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\O situated\\O ,\\O the\\O candle-light\\O ,\\O food-quality\\O and\\O service\\O overcompensate\\B .\\O", "sentiment": "positive"}, {"uid": "2768-2", "target_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\O situated\\O ,\\O the\\O candle-light\\O ,\\O food-quality\\O and\\O service\\B overcompensate\\O .\\O", "opinion_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\O situated\\O ,\\O the\\O candle-light\\O ,\\O food-quality\\O and\\O service\\O overcompensate\\B .\\O", "sentiment": "positive"}, {"uid": "2768-3", "target_tags": "Although\\O the\\O tables\\B may\\O be\\O closely\\O situated\\O ,\\O the\\O candle-light\\O ,\\O food-quality\\O and\\O service\\O overcompensate\\O .\\O", "opinion_tags": "Although\\O the\\O tables\\O may\\O be\\O closely\\B situated\\I ,\\O the\\O candle-light\\O ,\\O food-quality\\O and\\O service\\O overcompensate\\O .\\O", "sentiment": "negative"}]}, {"id": "2391", "sentence": "Nevertheless the food itself is pretty good .", "postag": ["RB", "DT", "NN", "PRP", "VBZ", "RB", "JJ", "."], "head": [7, 3, 7, 3, 7, 7, 0, 7], "deprel": ["advmod", "det", "nsubj", "nmod:npmod", "cop", "advmod", "root", "punct"], "triples": [{"uid": "2391-0", "target_tags": "Nevertheless\\O the\\O food\\B itself\\O is\\O pretty\\O good\\O .\\O", "opinion_tags": "Nevertheless\\O the\\O food\\O itself\\O is\\O pretty\\O good\\B .\\O", "sentiment": "positive"}]}]