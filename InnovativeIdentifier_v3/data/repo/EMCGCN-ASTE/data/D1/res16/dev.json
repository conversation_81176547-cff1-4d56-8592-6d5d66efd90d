[{"id": "513521:0", "sentence": "Great bagels made the old-fashioned way .", "postag": ["JJ", "NNS", "VBD", "DT", "JJ", "NN", "."], "head": [2, 3, 0, 6, 6, 3, 3], "deprel": ["amod", "nsubj", "root", "det", "amod", "obj", "punct"], "triples": [{"uid": "513521:0-0", "target_tags": "Great\\O bagels\\B made\\O the\\O old-fashioned\\O way\\O .\\O", "opinion_tags": "Great\\B bagels\\O made\\O the\\O old-fashioned\\O way\\O .\\O", "sentiment": "positive"}]}, {"id": "1395421:2", "sentence": "It is one the nicest outdoor restaurants I have ever seen in NY -- I am from Italy and this place rivals the ones in my country .", "postag": ["PRP", "VBZ", "CD", "DT", "JJS", "JJ", "NNS", "PRP", "VBP", "RB", "VBN", "IN", "NNP", ",", "PRP", "VBP", "IN", "NNP", "CC", "DT", "NN", "VBZ", "DT", "NNS", "IN", "PRP$", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 11, 11, 11, 7, 13, 11, 3, 18, 18, 18, 3, 22, 21, 22, 3, 24, 22, 27, 27, 22, 3], "deprel": ["nsubj", "cop", "root", "det", "amod", "amod", "nmod", "nsubj", "aux", "advmod", "acl:relcl", "case", "obl", "punct", "nsubj", "cop", "case", "parataxis", "cc", "det", "nsubj", "conj", "det", "obj", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "1395421:2-0", "target_tags": "It\\O is\\O one\\O the\\O nicest\\O outdoor\\B restaurants\\I I\\O have\\O ever\\O seen\\O in\\O NY\\O --\\O I\\O am\\O from\\O Italy\\O and\\O this\\O place\\O rivals\\O the\\O ones\\O in\\O my\\O country\\O .\\O", "opinion_tags": "It\\O is\\O one\\O the\\O nicest\\B outdoor\\O restaurants\\O I\\O have\\O ever\\O seen\\O in\\O NY\\O --\\O I\\O am\\O from\\O Italy\\O and\\O this\\O place\\O rivals\\O the\\O ones\\O in\\O my\\O country\\O .\\O", "sentiment": "positive"}]}, {"id": "494850:3", "sentence": "The food now is inconsistent .", "postag": ["DT", "NN", "RB", "VBZ", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "advmod", "cop", "root", "punct"], "triples": [{"uid": "494850:3-0", "target_tags": "The\\O food\\B now\\O is\\O inconsistent\\O .\\O", "opinion_tags": "The\\O food\\O now\\O is\\O inconsistent\\B .\\O", "sentiment": "negative"}]}, {"id": "Z#5:0", "sentence": "Amazing food .", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "Z#5:0-0", "target_tags": "Amazing\\O food\\B .\\O", "opinion_tags": "Amazing\\B food\\O .\\O", "sentiment": "positive"}]}, {"id": "717570:1", "sentence": "The decor is very simple but comfortable .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"], "triples": [{"uid": "717570:1-0", "target_tags": "The\\O decor\\B is\\O very\\O simple\\O but\\O comfortable\\O .\\O", "opinion_tags": "The\\O decor\\O is\\O very\\O simple\\B but\\O comfortable\\B .\\O", "sentiment": "positive"}]}, {"id": "1702257:3", "sentence": "Friendly staff that actually lets you enjoy your meal and the company you 're with .", "postag": ["JJ", "NN", "WDT", "RB", "VBZ", "PRP", "VB", "PRP$", "NN", "CC", "DT", "NN", "PRP", "VBP", "IN", "."], "head": [2, 0, 5, 5, 2, 5, 5, 9, 7, 12, 12, 9, 15, 12, 12, 2], "deprel": ["amod", "root", "nsubj", "advmod", "acl:relcl", "obj", "xcomp", "nmod:poss", "obj", "cc", "det", "conj", "nsubj", "acl:relcl", "acl:relcl", "punct"], "triples": [{"uid": "1702257:3-0", "target_tags": "Friendly\\O staff\\B that\\O actually\\O lets\\O you\\O enjoy\\O your\\O meal\\O and\\O the\\O company\\O you\\O 're\\O with\\O .\\O", "opinion_tags": "Friendly\\B staff\\O that\\O actually\\O lets\\O you\\O enjoy\\O your\\O meal\\O and\\O the\\O company\\O you\\O 're\\O with\\O .\\O", "sentiment": "positive"}]}, {"id": "FF#10:1", "sentence": "I generally like this place .", "postag": ["PRP", "RB", "VBP", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "obj", "punct"], "triples": [{"uid": "FF#10:1-0", "target_tags": "I\\O generally\\O like\\O this\\O place\\B .\\O", "opinion_tags": "I\\O generally\\O like\\B this\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "608500:2", "sentence": "While the ambiance and atmosphere were great , the food and service could have been a lot better .", "postag": ["IN", "DT", "NN", "CC", "NN", "VBD", "JJ", ",", "DT", "NN", "CC", "NN", "MD", "VB", "VBN", "DT", "NN", "JJR", "."], "head": [7, 3, 7, 5, 3, 7, 18, 18, 10, 18, 12, 10, 18, 18, 18, 17, 18, 0, 18], "deprel": ["mark", "det", "nsubj", "cc", "conj", "cop", "advcl", "punct", "det", "nsubj", "cc", "conj", "aux", "aux", "cop", "det", "obl:npmod", "root", "punct"], "triples": [{"uid": "608500:2-0", "target_tags": "While\\O the\\O ambiance\\B and\\O atmosphere\\O were\\O great\\O ,\\O the\\O food\\O and\\O service\\O could\\O have\\O been\\O a\\O lot\\O better\\O .\\O", "opinion_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\O were\\O great\\B ,\\O the\\O food\\O and\\O service\\O could\\O have\\O been\\O a\\O lot\\O better\\O .\\O", "sentiment": "positive"}, {"uid": "608500:2-1", "target_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\B were\\O great\\O ,\\O the\\O food\\O and\\O service\\O could\\O have\\O been\\O a\\O lot\\O better\\O .\\O", "opinion_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\O were\\O great\\B ,\\O the\\O food\\O and\\O service\\O could\\O have\\O been\\O a\\O lot\\O better\\O .\\O", "sentiment": "positive"}, {"uid": "608500:2-2", "target_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\O were\\O great\\O ,\\O the\\O food\\B and\\O service\\O could\\O have\\O been\\O a\\O lot\\O better\\O .\\O", "opinion_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\O were\\O great\\O ,\\O the\\O food\\O and\\O service\\O could\\B have\\I been\\I a\\I lot\\I better\\I .\\O", "sentiment": "negative"}, {"uid": "608500:2-3", "target_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\O were\\O great\\O ,\\O the\\O food\\O and\\O service\\B could\\O have\\O been\\O a\\O lot\\O better\\O .\\O", "opinion_tags": "While\\O the\\O ambiance\\O and\\O atmosphere\\O were\\O great\\O ,\\O the\\O food\\O and\\O service\\O could\\B have\\I been\\I a\\I lot\\I better\\I .\\O", "sentiment": "negative"}]}, {"id": "1586174:5", "sentence": "the only things u could really taste are the very salty soy sauce ( even its low sodium ) , the vinegar-soaked rice , and the scallion on top of the fish .", "postag": ["DT", "JJ", "NNS", "PRP", "MD", "RB", "VB", "VBP", "DT", "RB", "JJ", "NN", "NN", "-LRB-", "RB", "PRP$", "JJ", "NN", "-RRB-", ",", "DT", "JJ", "NN", ",", "CC", "DT", "NN", "IN", "NN", "IN", "DT", "NN", "."], "head": [3, 3, 13, 7, 7, 7, 3, 13, 13, 11, 13, 13, 0, 18, 18, 18, 18, 13, 18, 23, 23, 23, 13, 27, 27, 27, 13, 29, 27, 32, 32, 29, 13], "deprel": ["det", "amod", "nsubj", "nsubj", "aux", "advmod", "acl:relcl", "cop", "det", "advmod", "amod", "compound", "root", "punct", "advmod", "nmod:poss", "amod", "appos", "punct", "punct", "det", "amod", "conj", "punct", "cc", "det", "conj", "case", "nmod", "case", "det", "nmod", "punct"], "triples": [{"uid": "1586174:5-0", "target_tags": "the\\O only\\O things\\O u\\O could\\O really\\O taste\\O are\\O the\\O very\\O salty\\O soy\\B sauce\\I (\\O even\\O its\\O low\\O sodium\\O )\\O ,\\O the\\O vinegar-soaked\\O rice\\O ,\\O and\\O the\\O scallion\\O on\\O top\\O of\\O the\\O fish\\O .\\O", "opinion_tags": "the\\O only\\O things\\O u\\O could\\O really\\O taste\\O are\\O the\\O very\\O salty\\B soy\\O sauce\\O (\\O even\\O its\\O low\\O sodium\\O )\\O ,\\O the\\O vinegar-soaked\\O rice\\O ,\\O and\\O the\\O scallion\\O on\\O top\\O of\\O the\\O fish\\O .\\O", "sentiment": "negative"}, {"uid": "1586174:5-1", "target_tags": "the\\O only\\O things\\O u\\O could\\O really\\O taste\\O are\\O the\\O very\\O salty\\O soy\\O sauce\\O (\\O even\\O its\\O low\\O sodium\\O )\\O ,\\O the\\O vinegar-soaked\\O rice\\B ,\\O and\\O the\\O scallion\\O on\\O top\\O of\\O the\\O fish\\O .\\O", "opinion_tags": "the\\O only\\O things\\O u\\O could\\O really\\O taste\\O are\\O the\\O very\\O salty\\O soy\\O sauce\\O (\\O even\\O its\\O low\\O sodium\\O )\\O ,\\O the\\O vinegar-soaked\\B rice\\O ,\\O and\\O the\\O scallion\\O on\\O top\\O of\\O the\\O fish\\O .\\O", "sentiment": "negative"}]}, {"id": "1470286:1", "sentence": "Delivery is fast too .", "postag": ["NN", "VBZ", "JJ", "RB", "."], "head": [3, 3, 0, 3, 3], "deprel": ["nsubj", "cop", "root", "advmod", "punct"], "triples": [{"uid": "1470286:1-0", "target_tags": "Delivery\\B is\\O fast\\O too\\O .\\O", "opinion_tags": "Delivery\\O is\\O fast\\B too\\O .\\O", "sentiment": "positive"}]}, {"id": "BzG#4:0", "sentence": "This small Astoria souvlaki spot makes what many consider the best gyros in New York .", "postag": ["DT", "JJ", "NNP", "NN", "NN", "VBZ", "WP", "JJ", "VBP", "DT", "JJS", "NNS", "IN", "NNP", "NNP", "."], "head": [5, 5, 5, 5, 6, 0, 9, 9, 6, 12, 12, 9, 15, 15, 12, 6], "deprel": ["det", "amod", "compound", "compound", "nsubj", "root", "obj", "nsubj", "ccomp", "det", "amod", "obj", "case", "compound", "nmod", "punct"], "triples": [{"uid": "BzG#4:0-0", "target_tags": "This\\O small\\O Astoria\\O souvlaki\\O spot\\O makes\\O what\\O many\\O consider\\O the\\O best\\O gyros\\B in\\O New\\O York\\O .\\O", "opinion_tags": "This\\O small\\O Astoria\\O souvlaki\\O spot\\O makes\\O what\\O many\\O consider\\O the\\O best\\B gyros\\O in\\O New\\O York\\O .\\O", "sentiment": "positive"}]}, {"id": "1125284:1", "sentence": "This place is the most Japanese it can ever get .", "postag": ["DT", "NN", "VBZ", "DT", "RBS", "JJ", "PRP", "MD", "RB", "VB", "."], "head": [2, 6, 6, 6, 6, 0, 10, 10, 10, 6, 6], "deprel": ["det", "nsubj", "cop", "det", "advmod", "root", "nsubj", "aux", "advmod", "ccomp", "punct"], "triples": [{"uid": "1125284:1-0", "target_tags": "This\\O place\\B is\\O the\\O most\\O Japanese\\O it\\O can\\O ever\\O get\\O .\\O", "opinion_tags": "This\\O place\\O is\\O the\\O most\\O Japanese\\B it\\O can\\O ever\\O get\\O .\\O", "sentiment": "positive"}]}, {"id": "1561259:4", "sentence": "This place is pricey , and yes , the food is worth it ; but the service makes you feel like you should be paying a quater of the price .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "CC", "UH", ",", "DT", "NN", "VBZ", "JJ", "PRP", ",", "CC", "DT", "NN", "VBZ", "PRP", "VB", "IN", "PRP", "MD", "VB", "VBG", "DT", "NN", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 12, 12, 12, 12, 10, 12, 12, 4, 12, 4, 18, 17, 18, 4, 18, 18, 25, 25, 25, 25, 20, 27, 25, 30, 30, 27, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "discourse", "punct", "det", "nsubj", "cop", "conj", "obj", "punct", "cc", "det", "nsubj", "conj", "obj", "xcomp", "mark", "nsubj", "aux", "aux", "advcl", "det", "obj", "case", "det", "nmod", "punct"], "triples": [{"uid": "1561259:4-0", "target_tags": "This\\O place\\B is\\O pricey\\O ,\\O and\\O yes\\O ,\\O the\\O food\\O is\\O worth\\O it\\O ;\\O but\\O the\\O service\\O makes\\O you\\O feel\\O like\\O you\\O should\\O be\\O paying\\O a\\O quater\\O of\\O the\\O price\\O .\\O", "opinion_tags": "This\\O place\\O is\\O pricey\\B ,\\O and\\O yes\\O ,\\O the\\O food\\O is\\O worth\\O it\\O ;\\O but\\O the\\O service\\O makes\\O you\\O feel\\O like\\O you\\O should\\O be\\O paying\\O a\\O quater\\O of\\O the\\O price\\O .\\O", "sentiment": "negative"}, {"uid": "1561259:4-1", "target_tags": "This\\O place\\O is\\O pricey\\O ,\\O and\\O yes\\O ,\\O the\\O food\\B is\\O worth\\O it\\O ;\\O but\\O the\\O service\\O makes\\O you\\O feel\\O like\\O you\\O should\\O be\\O paying\\O a\\O quater\\O of\\O the\\O price\\O .\\O", "opinion_tags": "This\\O place\\O is\\O pricey\\O ,\\O and\\O yes\\O ,\\O the\\O food\\O is\\O worth\\B it\\O ;\\O but\\O the\\O service\\O makes\\O you\\O feel\\O like\\O you\\O should\\O be\\O paying\\O a\\O quater\\O of\\O the\\O price\\O .\\O", "sentiment": "positive"}]}, {"id": "1448587:3", "sentence": "The decor in this place is very diner-ish and the kind of place you expect in the East Village - not romantic , just simple , small and sparse .", "postag": ["DT", "NN", "IN", "DT", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NN", "IN", "NN", "PRP", "VBP", "IN", "DT", "NNP", "NNP", ",", "RB", "JJ", ",", "RB", "JJ", ",", "JJ", "CC", "JJ", "."], "head": [2, 8, 5, 5, 2, 8, 8, 0, 11, 11, 22, 13, 11, 15, 13, 19, 19, 19, 15, 22, 22, 8, 25, 25, 22, 27, 22, 29, 22, 8], "deprel": ["det", "nsubj", "case", "det", "nmod", "cop", "advmod", "root", "cc", "det", "nsubj", "case", "nmod", "nsubj", "acl:relcl", "case", "det", "compound", "obl", "punct", "advmod", "parataxis", "punct", "advmod", "conj", "punct", "conj", "cc", "conj", "punct"], "triples": [{"uid": "1448587:3-0", "target_tags": "The\\O decor\\B in\\O this\\O place\\O is\\O very\\O diner-ish\\O and\\O the\\O kind\\O of\\O place\\O you\\O expect\\O in\\O the\\O East\\O Village\\O -\\O not\\O romantic\\O ,\\O just\\O simple\\O ,\\O small\\O and\\O sparse\\O .\\O", "opinion_tags": "The\\O decor\\O in\\O this\\O place\\O is\\O very\\O diner-ish\\B and\\O the\\O kind\\O of\\O place\\O you\\O expect\\O in\\O the\\O East\\O Village\\O -\\O not\\O romantic\\O ,\\O just\\O simple\\O ,\\O small\\O and\\O sparse\\O .\\O", "sentiment": "neutral"}]}, {"id": "630914:2", "sentence": "The mussles were the fishiest things I 've ever tasted , the seabass was bland , the goat cheese salad was missing the goat cheese , the penne w/ chicken had bones in it ... It was disgusting .", "postag": ["DT", "NNS", "VBD", "DT", "JJS", "NNS", "PRP", "VBP", "RB", "VBN", ",", "DT", "NN", "VBD", "JJ", ",", "DT", "NN", "NN", "NN", "VBD", "VBG", "DT", "NN", "NN", ",", "DT", "NN", ",", "NN", "VBD", "NNS", "IN", "PRP", ",", "PRP", "VBD", "JJ", "."], "head": [2, 6, 6, 6, 6, 0, 10, 10, 10, 6, 6, 13, 15, 15, 6, 6, 20, 19, 20, 22, 22, 6, 25, 25, 22, 6, 28, 25, 6, 31, 6, 31, 34, 31, 6, 38, 38, 6, 6], "deprel": ["det", "nsubj", "cop", "det", "amod", "root", "nsubj", "aux", "advmod", "acl:relcl", "punct", "det", "nsubj", "cop", "parataxis", "punct", "det", "compound", "compound", "nsubj", "aux", "parataxis", "det", "compound", "obj", "punct", "det", "appos", "punct", "nsubj", "parataxis", "obj", "case", "obl", "punct", "nsubj", "cop", "parataxis", "punct"], "triples": [{"uid": "630914:2-0", "target_tags": "The\\O mussles\\B were\\O the\\O fishiest\\O things\\O I\\O 've\\O ever\\O tasted\\O ,\\O the\\O seabass\\O was\\O bland\\O ,\\O the\\O goat\\O cheese\\O salad\\O was\\O missing\\O the\\O goat\\O cheese\\O ,\\O the\\O penne\\O w/\\O chicken\\O had\\O bones\\O in\\O it\\O ...\\O It\\O was\\O disgusting\\O .\\O", "opinion_tags": "The\\O mussles\\O were\\O the\\O fishiest\\B things\\O I\\O 've\\O ever\\O tasted\\O ,\\O the\\O seabass\\O was\\O bland\\O ,\\O the\\O goat\\O cheese\\O salad\\O was\\O missing\\O the\\O goat\\O cheese\\O ,\\O the\\O penne\\O w/\\O chicken\\O had\\O bones\\O in\\O it\\O ...\\O It\\O was\\O disgusting\\O .\\O", "sentiment": "negative"}, {"uid": "630914:2-1", "target_tags": "The\\O mussles\\O were\\O the\\O fishiest\\O things\\O I\\O 've\\O ever\\O tasted\\O ,\\O the\\O seabass\\B was\\O bland\\O ,\\O the\\O goat\\O cheese\\O salad\\O was\\O missing\\O the\\O goat\\O cheese\\O ,\\O the\\O penne\\O w/\\O chicken\\O had\\O bones\\O in\\O it\\O ...\\O It\\O was\\O disgusting\\O .\\O", "opinion_tags": "The\\O mussles\\O were\\O the\\O fishiest\\O things\\O I\\O 've\\O ever\\O tasted\\O ,\\O the\\O seabass\\O was\\O bland\\B ,\\O the\\O goat\\O cheese\\O salad\\O was\\O missing\\O the\\O goat\\O cheese\\O ,\\O the\\O penne\\O w/\\O chicken\\O had\\O bones\\O in\\O it\\O ...\\O It\\O was\\O disgusting\\O .\\O", "sentiment": "negative"}, {"uid": "630914:2-2", "target_tags": "The\\O mussles\\O were\\O the\\O fishiest\\O things\\O I\\O 've\\O ever\\O tasted\\O ,\\O the\\O seabass\\O was\\O bland\\O ,\\O the\\O goat\\B cheese\\I salad\\I was\\O missing\\O the\\O goat\\O cheese\\O ,\\O the\\O penne\\O w/\\O chicken\\O had\\O bones\\O in\\O it\\O ...\\O It\\O was\\O disgusting\\O .\\O", "opinion_tags": "The\\O mussles\\O were\\O the\\O fishiest\\O things\\O I\\O 've\\O ever\\O tasted\\O ,\\O the\\O seabass\\O was\\O bland\\O ,\\O the\\O goat\\O cheese\\O salad\\O was\\O missing\\B the\\O goat\\O cheese\\O ,\\O the\\O penne\\O w/\\O chicken\\O had\\O bones\\O in\\O it\\O ...\\O It\\O was\\O disgusting\\O .\\O", "sentiment": "negative"}, {"uid": "630914:2-3", "target_tags": "The\\O mussles\\O were\\O the\\O fishiest\\O things\\O I\\O 've\\O ever\\O tasted\\O ,\\O the\\O seabass\\O was\\O bland\\O ,\\O the\\O goat\\O cheese\\O salad\\O was\\O missing\\O the\\O goat\\O cheese\\O ,\\O the\\O penne\\B w/\\I chicken\\I had\\O bones\\O in\\O it\\O ...\\O It\\O was\\O disgusting\\O .\\O", "opinion_tags": "The\\O mussles\\O were\\O the\\O fishiest\\O things\\O I\\O 've\\O ever\\O tasted\\O ,\\O the\\O seabass\\O was\\O bland\\O ,\\O the\\O goat\\O cheese\\O salad\\O was\\O missing\\O the\\O goat\\O cheese\\O ,\\O the\\O penne\\O w/\\O chicken\\O had\\O bones\\O in\\O it\\O ...\\O It\\O was\\O disgusting\\B .\\O", "sentiment": "negative"}]}, {"id": "Z#6:7", "sentence": "We started off with a delightful sashimi amuse bouche .", "postag": ["PRP", "VBD", "RP", "IN", "DT", "JJ", "NN", "NN", "NN", "."], "head": [2, 0, 2, 9, 9, 9, 9, 9, 2, 2], "deprel": ["nsubj", "root", "compound:prt", "case", "det", "amod", "compound", "compound", "obl", "punct"], "triples": [{"uid": "Z#6:7-0", "target_tags": "We\\O started\\O off\\O with\\O a\\O delightful\\O sashimi\\B amuse\\I bouche\\I .\\O", "opinion_tags": "We\\O started\\O off\\O with\\O a\\O delightful\\B sashimi\\O amuse\\O bouche\\O .\\O", "sentiment": "positive"}]}, {"id": "TM#5:3", "sentence": "Helpful service and average price per dish $ 10 .", "postag": ["JJ", "NN", "CC", "JJ", "NN", "IN", "NN", "$", "CD", "."], "head": [2, 0, 5, 5, 2, 7, 2, 2, 8, 2], "deprel": ["amod", "root", "cc", "amod", "conj", "case", "nmod", "appos", "nummod", "punct"], "triples": [{"uid": "TM#5:3-0", "target_tags": "Helpful\\O service\\B and\\O average\\O price\\O per\\O dish\\O $\\O 10\\O .\\O", "opinion_tags": "Helpful\\B service\\O and\\O average\\O price\\O per\\O dish\\O $\\O 10\\O .\\O", "sentiment": "positive"}]}, {"id": "1147082:1", "sentence": "Sauce was watery and the food did n't have much flavor .", "postag": ["NN", "VBD", "JJ", "CC", "DT", "NN", "VBD", "RB", "VB", "JJ", "NN", "."], "head": [3, 3, 0, 9, 6, 9, 9, 9, 3, 11, 9, 3], "deprel": ["nsubj", "cop", "root", "cc", "det", "nsubj", "aux", "advmod", "conj", "amod", "obj", "punct"], "triples": [{"uid": "1147082:1-0", "target_tags": "Sauce\\B was\\O watery\\O and\\O the\\O food\\O did\\O n't\\O have\\O much\\O flavor\\O .\\O", "opinion_tags": "Sauce\\O was\\O watery\\B and\\O the\\O food\\O did\\O n't\\O have\\O much\\O flavor\\O .\\O", "sentiment": "negative"}]}, {"id": "BzG#2:0", "sentence": "Great place , great value .", "postag": ["JJ", "NN", ",", "JJ", "NN", "."], "head": [2, 0, 2, 5, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct"], "triples": [{"uid": "BzG#2:0-0", "target_tags": "Great\\O place\\B ,\\O great\\O value\\O .\\O", "opinion_tags": "Great\\B place\\O ,\\O great\\O value\\O .\\O", "sentiment": "positive"}]}, {"id": "1280179:0", "sentence": "I 've been to at Cafe Spice probably 5-8 times , it is probably still the best Indian restaurant around Union Square .", "postag": ["PRP", "VBP", "VBN", "IN", "IN", "NNP", "NNP", "RB", "CD", "NNS", ",", "PRP", "VBZ", "RB", "RB", "DT", "JJS", "JJ", "NN", "IN", "NNP", "NNP", "."], "head": [4, 4, 4, 0, 7, 7, 4, 10, 10, 4, 4, 19, 19, 19, 19, 19, 19, 19, 4, 22, 22, 19, 4], "deprel": ["nsubj", "aux", "cop", "root", "case", "compound", "obl", "advmod", "nummod", "obl:tmod", "punct", "nsubj", "cop", "advmod", "advmod", "det", "amod", "amod", "parataxis", "case", "compound", "nmod", "punct"], "triples": [{"uid": "1280179:0-0", "target_tags": "I\\O 've\\O been\\O to\\O at\\O Cafe\\B Spice\\I probably\\O 5-8\\O times\\O ,\\O it\\O is\\O probably\\O still\\O the\\O best\\O Indian\\O restaurant\\O around\\O Union\\O Square\\O .\\O", "opinion_tags": "I\\O 've\\O been\\O to\\O at\\O Cafe\\O Spice\\O probably\\O 5-8\\O times\\O ,\\O it\\O is\\O probably\\O still\\O the\\O best\\B Indian\\O restaurant\\O around\\O Union\\O Square\\O .\\O", "sentiment": "positive"}]}, {"id": "758263:3", "sentence": "$ 20 for all you can eat sushi can not be beaten .", "postag": ["$", "CD", "IN", "DT", "PRP", "MD", "VB", "NN", "MD", "RB", "VB", "VBN", "."], "head": [12, 1, 4, 1, 7, 7, 4, 7, 12, 12, 12, 0, 1], "deprel": ["nsubj:pass", "nummod", "case", "nmod", "nsubj", "aux", "acl:relcl", "obj", "aux", "advmod", "aux:pass", "root", "punct"], "triples": [{"uid": "758263:3-0", "target_tags": "$\\O 20\\O for\\O all\\B you\\I can\\I eat\\I sushi\\I can\\O not\\O be\\O beaten\\O .\\O", "opinion_tags": "$\\O 20\\O for\\O all\\O you\\O can\\O eat\\O sushi\\O can\\O not\\O be\\O beaten\\B .\\O", "sentiment": "positive"}]}, {"id": "1448587:2", "sentence": "We ordered the special , grilled branzino , that was so infused with bone , it was difficult to eat .", "postag": ["PRP", "VBD", "DT", "JJ", ",", "JJ", "NN", ",", "WDT", "VBD", "RB", "VBN", "IN", "NN", ",", "PRP", "VBD", "JJ", "TO", "VB", "."], "head": [2, 0, 7, 7, 7, 7, 2, 7, 12, 12, 12, 7, 14, 12, 2, 18, 18, 2, 20, 18, 2], "deprel": ["nsubj", "root", "det", "amod", "punct", "amod", "obj", "punct", "nsubj:pass", "aux:pass", "advmod", "acl:relcl", "case", "obl", "punct", "nsubj", "cop", "parataxis", "mark", "csubj", "punct"], "triples": [{"uid": "1448587:2-0", "target_tags": "We\\O ordered\\O the\\O special\\O ,\\O grilled\\B branzino\\I ,\\O that\\O was\\O so\\O infused\\O with\\O bone\\O ,\\O it\\O was\\O difficult\\O to\\O eat\\O .\\O", "opinion_tags": "We\\O ordered\\O the\\O special\\O ,\\O grilled\\O branzino\\O ,\\O that\\O was\\O so\\O infused\\O with\\O bone\\O ,\\O it\\O was\\O difficult\\B to\\I eat\\I .\\O", "sentiment": "negative"}]}, {"id": "746254:4", "sentence": "With so many good restaurants on the UWS , I do n't need overpriced food , absurdly arrogant wait-staff who do n't recognize they work at a glorified diner , clumsy service , and management that does n't care .", "postag": ["IN", "RB", "JJ", "JJ", "NNS", "IN", "DT", "NNP", ",", "PRP", "VBP", "RB", "VB", "JJ", "NN", ",", "RB", "JJ", "NN", "WP", "VBP", "RB", "VB", "PRP", "VBP", "IN", "DT", "VBN", "NN", ",", "JJ", "NN", ",", "CC", "NN", "WDT", "VBZ", "RB", "VB", "."], "head": [5, 3, 5, 5, 13, 8, 8, 5, 13, 13, 13, 13, 0, 15, 13, 19, 18, 19, 15, 23, 23, 23, 19, 25, 23, 29, 29, 29, 25, 32, 32, 29, 35, 35, 29, 39, 39, 39, 35, 13], "deprel": ["case", "advmod", "amod", "amod", "obl", "case", "det", "nmod", "punct", "nsubj", "aux", "advmod", "root", "amod", "obj", "punct", "advmod", "amod", "appos", "nsubj", "aux", "advmod", "acl:relcl", "nsubj", "ccomp", "case", "det", "amod", "obl", "punct", "amod", "conj", "punct", "cc", "conj", "nsubj", "aux", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "746254:4-0", "target_tags": "With\\O so\\O many\\O good\\O restaurants\\O on\\O the\\O UWS\\O ,\\O I\\O do\\O n't\\O need\\O overpriced\\O food\\B ,\\O absurdly\\O arrogant\\O wait-staff\\O who\\O do\\O n't\\O recognize\\O they\\O work\\O at\\O a\\O glorified\\O diner\\O ,\\O clumsy\\O service\\O ,\\O and\\O management\\O that\\O does\\O n't\\O care\\O .\\O", "opinion_tags": "With\\O so\\O many\\O good\\O restaurants\\O on\\O the\\O UWS\\O ,\\O I\\O do\\O n't\\O need\\O overpriced\\B food\\O ,\\O absurdly\\O arrogant\\O wait-staff\\O who\\O do\\O n't\\O recognize\\O they\\O work\\O at\\O a\\O glorified\\O diner\\O ,\\O clumsy\\O service\\O ,\\O and\\O management\\O that\\O does\\O n't\\O care\\O .\\O", "sentiment": "negative"}, {"uid": "746254:4-1", "target_tags": "With\\O so\\O many\\O good\\O restaurants\\O on\\O the\\O UWS\\O ,\\O I\\O do\\O n't\\O need\\O overpriced\\O food\\O ,\\O absurdly\\O arrogant\\O wait-staff\\B who\\O do\\O n't\\O recognize\\O they\\O work\\O at\\O a\\O glorified\\O diner\\O ,\\O clumsy\\O service\\O ,\\O and\\O management\\O that\\O does\\O n't\\O care\\O .\\O", "opinion_tags": "With\\O so\\O many\\O good\\O restaurants\\O on\\O the\\O UWS\\O ,\\O I\\O do\\O n't\\O need\\O overpriced\\O food\\O ,\\O absurdly\\O arrogant\\B wait-staff\\O who\\O do\\O n't\\O recognize\\O they\\O work\\O at\\O a\\O glorified\\O diner\\O ,\\O clumsy\\O service\\O ,\\O and\\O management\\O that\\O does\\O n't\\O care\\O .\\O", "sentiment": "negative"}, {"uid": "746254:4-2", "target_tags": "With\\O so\\O many\\O good\\O restaurants\\O on\\O the\\O UWS\\O ,\\O I\\O do\\O n't\\O need\\O overpriced\\O food\\O ,\\O absurdly\\O arrogant\\O wait-staff\\O who\\O do\\O n't\\O recognize\\O they\\O work\\O at\\O a\\O glorified\\O diner\\O ,\\O clumsy\\O service\\B ,\\O and\\O management\\O that\\O does\\O n't\\O care\\O .\\O", "opinion_tags": "With\\O so\\O many\\O good\\O restaurants\\O on\\O the\\O UWS\\O ,\\O I\\O do\\O n't\\O need\\O overpriced\\O food\\O ,\\O absurdly\\O arrogant\\O wait-staff\\O who\\O do\\O n't\\O recognize\\O they\\O work\\O at\\O a\\O glorified\\O diner\\O ,\\O clumsy\\B service\\O ,\\O and\\O management\\O that\\O does\\O n't\\O care\\O .\\O", "sentiment": "negative"}]}, {"id": "P#9:1", "sentence": "Pacifico is a great place to casually hang out .", "postag": ["NNP", "VBZ", "DT", "JJ", "NN", "TO", "RB", "VB", "RP", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 8, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "advmod", "acl", "compound:prt", "punct"], "triples": [{"uid": "P#9:1-0", "target_tags": "Pacifico\\B is\\O a\\O great\\O place\\O to\\O casually\\O hang\\O out\\O .\\O", "opinion_tags": "Pacifico\\O is\\O a\\O great\\B place\\O to\\O casually\\O hang\\O out\\O .\\O", "sentiment": "positive"}]}, {"id": "1221938:0", "sentence": "The drinks are always well made and wine selection is fairly priced .", "postag": ["DT", "NNS", "VBP", "RB", "RB", "VBN", "CC", "NN", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 6, 6, 6, 6, 0, 12, 9, 12, 12, 12, 6, 6], "deprel": ["det", "nsubj:pass", "aux:pass", "advmod", "advmod", "root", "cc", "compound", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "1221938:0-0", "target_tags": "The\\O drinks\\B are\\O always\\O well\\O made\\O and\\O wine\\O selection\\O is\\O fairly\\O priced\\O .\\O", "opinion_tags": "The\\O drinks\\O are\\O always\\O well\\B made\\I and\\O wine\\O selection\\O is\\O fairly\\O priced\\O .\\O", "sentiment": "positive"}, {"uid": "1221938:0-1", "target_tags": "The\\O drinks\\O are\\O always\\O well\\O made\\O and\\O wine\\B selection\\I is\\O fairly\\O priced\\O .\\O", "opinion_tags": "The\\O drinks\\O are\\O always\\O well\\O made\\O and\\O wine\\O selection\\O is\\O fairly\\B priced\\I .\\O", "sentiment": "positive"}]}, {"id": "929329:2", "sentence": "La Rosa waltzes in , and I think they are doing it the best .", "postag": ["NNP", "NNP", "VBZ", "RB", ",", "CC", "PRP", "VBP", "PRP", "VBP", "VBG", "PRP", "DT", "JJS", "."], "head": [3, 1, 0, 3, 8, 8, 8, 3, 11, 11, 8, 11, 14, 11, 3], "deprel": ["nsubj", "flat", "root", "advmod", "punct", "cc", "nsubj", "conj", "nsubj", "aux", "ccomp", "obj", "det", "obl:npmod", "punct"], "triples": [{"uid": "929329:2-0", "target_tags": "La\\B Rosa\\I waltzes\\O in\\O ,\\O and\\O I\\O think\\O they\\O are\\O doing\\O it\\O the\\O best\\O .\\O", "opinion_tags": "La\\O Rosa\\O waltzes\\O in\\O ,\\O and\\O I\\O think\\O they\\O are\\O doing\\O it\\O the\\O best\\B .\\O", "sentiment": "positive"}]}, {"id": "BHD#4:4", "sentence": "Going to Bark is always worth the train ride , and will make your tongue and belly very happy !", "postag": ["VBG", "IN", "NNP", "VBZ", "RB", "JJ", "DT", "NN", "NN", ",", "CC", "MD", "VB", "PRP$", "NN", "CC", "NN", "RB", "JJ", "."], "head": [6, 3, 1, 6, 6, 0, 9, 9, 6, 13, 13, 13, 6, 15, 13, 17, 15, 19, 13, 6], "deprel": ["csubj", "case", "obl", "cop", "advmod", "root", "det", "compound", "obj", "punct", "cc", "aux", "conj", "nmod:poss", "obj", "cc", "conj", "advmod", "xcomp", "punct"], "triples": [{"uid": "BHD#4:4-0", "target_tags": "Going\\O to\\O Bark\\B is\\O always\\O worth\\O the\\O train\\O ride\\O ,\\O and\\O will\\O make\\O your\\O tongue\\O and\\O belly\\O very\\O happy\\O !\\O", "opinion_tags": "Going\\O to\\O Bark\\O is\\O always\\O worth\\B the\\O train\\O ride\\O ,\\O and\\O will\\O make\\O your\\O tongue\\O and\\O belly\\O very\\O happy\\O !\\O", "sentiment": "positive"}]}, {"id": "1340075:2", "sentence": "The atmosphere is noisy and the waiters are literally walking around doing things as fast as they can .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NNS", "VBP", "RB", "VBG", "IN", "VBG", "NNS", "RB", "RB", "IN", "PRP", "MD", "."], "head": [2, 4, 4, 0, 10, 7, 10, 10, 10, 4, 12, 10, 12, 15, 12, 18, 18, 15, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "aux", "advmod", "conj", "mark", "advcl", "obj", "advmod", "advmod", "mark", "nsubj", "advcl", "punct"], "triples": [{"uid": "1340075:2-0", "target_tags": "The\\O atmosphere\\B is\\O noisy\\O and\\O the\\O waiters\\O are\\O literally\\O walking\\O around\\O doing\\O things\\O as\\O fast\\O as\\O they\\O can\\O .\\O", "opinion_tags": "The\\O atmosphere\\O is\\O noisy\\B and\\O the\\O waiters\\O are\\O literally\\O walking\\O around\\O doing\\O things\\O as\\O fast\\O as\\O they\\O can\\O .\\O", "sentiment": "negative"}, {"uid": "1340075:2-1", "target_tags": "The\\O atmosphere\\O is\\O noisy\\O and\\O the\\O waiters\\B are\\O literally\\O walking\\O around\\O doing\\O things\\O as\\O fast\\O as\\O they\\O can\\O .\\O", "opinion_tags": "The\\O atmosphere\\O is\\O noisy\\O and\\O the\\O waiters\\O are\\O literally\\O walking\\O around\\O doing\\O things\\O as\\O fast\\B as\\O they\\O can\\O .\\O", "sentiment": "positive"}]}, {"id": "1727363:2", "sentence": "An excellent service", "postag": ["DT", "JJ", "NN"], "head": [3, 3, 0], "deprel": ["det", "amod", "root"], "triples": [{"uid": "1727363:2-0", "target_tags": "An\\O excellent\\O service\\B", "opinion_tags": "An\\O excellent\\B service\\O", "sentiment": "positive"}]}, {"id": "Z#5:2", "sentence": "Great food !", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "Z#5:2-0", "target_tags": "Great\\O food\\B !\\O", "opinion_tags": "Great\\B food\\O !\\O", "sentiment": "positive"}]}, {"id": "757762:1", "sentence": "The only problem is that the manager is a complete incompetent .", "postag": ["DT", "JJ", "NN", "VBZ", "IN", "DT", "NN", "VBZ", "DT", "JJ", "JJ", "."], "head": [3, 3, 4, 0, 11, 7, 11, 11, 11, 11, 4, 4], "deprel": ["det", "amod", "nsubj", "root", "mark", "det", "nsubj", "cop", "det", "amod", "ccomp", "punct"], "triples": [{"uid": "757762:1-0", "target_tags": "The\\O only\\O problem\\O is\\O that\\O the\\O manager\\B is\\O a\\O complete\\O incompetent\\O .\\O", "opinion_tags": "The\\O only\\O problem\\O is\\O that\\O the\\O manager\\O is\\O a\\O complete\\O incompetent\\B .\\O", "sentiment": "negative"}]}, {"id": "1289424:2", "sentence": "After really enjoying ourselves at the bar we sat down at a table and had dinner .", "postag": ["IN", "RB", "VBG", "PRP", "IN", "DT", "NN", "PRP", "VBD", "RP", "IN", "DT", "NN", "CC", "VBD", "NN", "."], "head": [3, 3, 9, 3, 7, 7, 3, 9, 0, 9, 13, 13, 9, 15, 9, 15, 9], "deprel": ["mark", "advmod", "advcl", "obj", "case", "det", "obl", "nsubj", "root", "compound:prt", "case", "det", "obl", "cc", "conj", "obj", "punct"], "triples": [{"uid": "1289424:2-0", "target_tags": "After\\O really\\O enjoying\\O ourselves\\O at\\O the\\O bar\\B we\\O sat\\O down\\O at\\O a\\O table\\O and\\O had\\O dinner\\O .\\O", "opinion_tags": "After\\O really\\O enjoying\\B ourselves\\O at\\O the\\O bar\\O we\\O sat\\O down\\O at\\O a\\O table\\O and\\O had\\O dinner\\O .\\O", "sentiment": "positive"}]}, {"id": "494850:1", "sentence": "During the course of the past 3 months , the chef and staff changed and it was not for the better .", "postag": ["IN", "DT", "NN", "IN", "DT", "JJ", "CD", "NNS", ",", "DT", "NN", "CC", "NN", "VBD", "CC", "PRP", "VBD", "RB", "IN", "DT", "JJR", "."], "head": [3, 3, 14, 8, 8, 8, 8, 3, 14, 11, 14, 13, 11, 0, 21, 21, 21, 21, 21, 21, 14, 14], "deprel": ["case", "det", "obl", "case", "det", "amod", "nummod", "nmod", "punct", "det", "nsubj", "cc", "conj", "root", "cc", "nsubj", "cop", "advmod", "case", "det", "conj", "punct"], "triples": [{"uid": "494850:1-0", "target_tags": "During\\O the\\O course\\O of\\O the\\O past\\O 3\\O months\\O ,\\O the\\O chef\\B and\\O staff\\O changed\\O and\\O it\\O was\\O not\\O for\\O the\\O better\\O .\\O", "opinion_tags": "During\\O the\\O course\\O of\\O the\\O past\\O 3\\O months\\O ,\\O the\\O chef\\O and\\O staff\\O changed\\B and\\O it\\O was\\O not\\O for\\O the\\O better\\O .\\O", "sentiment": "negative"}, {"uid": "494850:1-1", "target_tags": "During\\O the\\O course\\O of\\O the\\O past\\O 3\\O months\\O ,\\O the\\O chef\\O and\\O staff\\B changed\\O and\\O it\\O was\\O not\\O for\\O the\\O better\\O .\\O", "opinion_tags": "During\\O the\\O course\\O of\\O the\\O past\\O 3\\O months\\O ,\\O the\\O chef\\O and\\O staff\\O changed\\B and\\O it\\O was\\O not\\O for\\O the\\O better\\O .\\O", "sentiment": "negative"}]}, {"id": "397331:4", "sentence": "Warning : You may find it difficult to dine at other Japanese restaurants after a visit to Mizu !", "postag": ["NN", ":", "PRP", "MD", "VB", "PRP", "JJ", "TO", "VB", "IN", "JJ", "JJ", "NNS", "IN", "DT", "NN", "IN", "NNP", "."], "head": [0, 1, 5, 5, 1, 5, 5, 9, 7, 13, 13, 13, 9, 16, 16, 9, 18, 16, 1], "deprel": ["root", "punct", "nsubj", "aux", "appos", "obj", "xcomp", "mark", "xcomp", "case", "amod", "amod", "obl", "case", "det", "obl", "case", "nmod", "punct"], "triples": [{"uid": "397331:4-0", "target_tags": "Warning\\O :\\O You\\O may\\O find\\O it\\O difficult\\O to\\O dine\\O at\\O other\\O Japanese\\O restaurants\\O after\\O a\\O visit\\O to\\O Mizu\\B !\\O", "opinion_tags": "Warning\\O :\\O You\\O may\\O find\\O it\\O difficult\\B to\\O dine\\O at\\O other\\O Japanese\\O restaurants\\O after\\O a\\O visit\\O to\\O Mizu\\O !\\O", "sentiment": "positive"}]}, {"id": "ADLT#8:0", "sentence": "Love Al Di La", "postag": ["VBP", "NNP", "NNP", "NNP"], "head": [0, 4, 4, 1], "deprel": ["root", "compound", "compound", "obj"], "triples": [{"uid": "ADLT#8:0-0", "target_tags": "Love\\O Al\\B Di\\I La\\I", "opinion_tags": "Love\\B Al\\O Di\\O La\\O", "sentiment": "positive"}]}, {"id": "735448:3", "sentence": "even the wine by the glass was good .", "postag": ["RB", "DT", "NN", "IN", "DT", "NN", "VBD", "JJ", "."], "head": [3, 3, 8, 6, 6, 3, 8, 0, 8], "deprel": ["advmod", "det", "nsubj", "case", "det", "nmod", "cop", "root", "punct"], "triples": [{"uid": "735448:3-0", "target_tags": "even\\O the\\O wine\\B by\\I the\\I glass\\I was\\O good\\O .\\O", "opinion_tags": "even\\O the\\O wine\\O by\\O the\\O glass\\O was\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "CLF#3:21", "sentence": "We asked for sides which the waiter than admitted that he forgot to put in that part of our order .", "postag": ["PRP", "VBD", "IN", "NNS", "WDT", "DT", "NN", "IN", "VBD", "IN", "PRP", "VBD", "TO", "VB", "IN", "DT", "NN", "IN", "PRP$", "NN", "."], "head": [2, 0, 4, 2, 9, 7, 9, 9, 4, 12, 12, 9, 14, 12, 17, 17, 14, 20, 20, 17, 2], "deprel": ["nsubj", "root", "case", "obl", "obj", "det", "nsubj", "mark", "acl:relcl", "mark", "nsubj", "ccomp", "mark", "xcomp", "case", "det", "obl", "case", "nmod:poss", "nmod", "punct"], "triples": [{"uid": "CLF#3:21-0", "target_tags": "We\\O asked\\O for\\O sides\\O which\\O the\\O waiter\\B than\\O admitted\\O that\\O he\\O forgot\\O to\\O put\\O in\\O that\\O part\\O of\\O our\\O order\\O .\\O", "opinion_tags": "We\\O asked\\O for\\O sides\\O which\\O the\\O waiter\\O than\\O admitted\\O that\\O he\\O forgot\\B to\\O put\\O in\\O that\\O part\\O of\\O our\\O order\\O .\\O", "sentiment": "negative"}]}, {"id": "1189674:0", "sentence": "First went here to enjoy their garden terrace .", "postag": ["RB", "VBD", "RB", "TO", "VB", "PRP$", "NN", "NN", "."], "head": [2, 0, 2, 5, 2, 8, 8, 5, 2], "deprel": ["advmod", "root", "advmod", "mark", "advcl", "nmod:poss", "compound", "obj", "punct"], "triples": [{"uid": "1189674:0-0", "target_tags": "First\\O went\\O here\\O to\\O enjoy\\O their\\O garden\\B terrace\\I .\\O", "opinion_tags": "First\\O went\\O here\\O to\\O enjoy\\B their\\O garden\\O terrace\\O .\\O", "sentiment": "positive"}]}, {"id": "1131595:2", "sentence": "The hostess is rude to the point of being offensive .", "postag": ["DT", "NN", "VBZ", "JJ", "IN", "DT", "NN", "IN", "VBG", "JJ", "."], "head": [2, 4, 4, 0, 7, 7, 4, 10, 10, 7, 4], "deprel": ["det", "nsubj", "cop", "root", "case", "det", "obl", "mark", "cop", "acl", "punct"], "triples": [{"uid": "1131595:2-0", "target_tags": "The\\O hostess\\B is\\O rude\\O to\\O the\\O point\\O of\\O being\\O offensive\\O .\\O", "opinion_tags": "The\\O hostess\\O is\\O rude\\B to\\O the\\O point\\O of\\O being\\O offensive\\B .\\O", "sentiment": "negative"}]}, {"id": "1726427:3", "sentence": "Definitely a great spot for a nice occasion or date .", "postag": ["RB", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", "CC", "NN", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 10, 8, 4], "deprel": ["advmod", "det", "amod", "root", "case", "det", "amod", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "1726427:3-0", "target_tags": "Definitely\\O a\\O great\\O spot\\B for\\O a\\O nice\\O occasion\\O or\\O date\\O .\\O", "opinion_tags": "Definitely\\O a\\O great\\B spot\\O for\\O a\\O nice\\O occasion\\O or\\O date\\O .\\O", "sentiment": "positive"}]}, {"id": "NP#10:3", "sentence": "The veal was incredible last night .", "postag": ["DT", "NN", "VBD", "JJ", "JJ", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "amod", "obl:tmod", "punct"], "triples": [{"uid": "NP#10:3-0", "target_tags": "The\\O veal\\B was\\O incredible\\O last\\O night\\O .\\O", "opinion_tags": "The\\O veal\\O was\\O incredible\\B last\\O night\\O .\\O", "sentiment": "positive"}]}, {"id": "1632929:3", "sentence": "The food was delicious ( I had a halibut special , my husband had steak ) , and the service was top-notch .", "postag": ["DT", "NN", "VBD", "JJ", "-LRB-", "PRP", "VBD", "DT", "NN", "JJ", ",", "PRP$", "NN", "VBD", "NN", "-RRB-", ",", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 7, 7, 4, 9, 7, 7, 14, 13, 14, 7, 14, 7, 22, 22, 20, 22, 22, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "parataxis", "det", "obj", "obj", "punct", "nmod:poss", "nsubj", "parataxis", "obj", "punct", "punct", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "1632929:3-0", "target_tags": "The\\O food\\B was\\O delicious\\O (\\O I\\O had\\O a\\O halibut\\O special\\O ,\\O my\\O husband\\O had\\O steak\\O )\\O ,\\O and\\O the\\O service\\O was\\O top-notch\\O .\\O", "opinion_tags": "The\\O food\\O was\\O delicious\\B (\\O I\\O had\\O a\\O halibut\\O special\\O ,\\O my\\O husband\\O had\\O steak\\O )\\O ,\\O and\\O the\\O service\\O was\\O top-notch\\O .\\O", "sentiment": "positive"}, {"uid": "1632929:3-1", "target_tags": "The\\O food\\O was\\O delicious\\O (\\O I\\O had\\O a\\O halibut\\O special\\O ,\\O my\\O husband\\O had\\O steak\\O )\\O ,\\O and\\O the\\O service\\B was\\O top-notch\\O .\\O", "opinion_tags": "The\\O food\\O was\\O delicious\\O (\\O I\\O had\\O a\\O halibut\\O special\\O ,\\O my\\O husband\\O had\\O steak\\O )\\O ,\\O and\\O the\\O service\\O was\\O top-notch\\B .\\O", "sentiment": "positive"}]}, {"id": "1477631:2", "sentence": "<PERSON><PERSON><PERSON> is a must for anyone who loves <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> .", "postag": ["NNP", "VBZ", "DT", "NN", "IN", "NN", "WP", "VBZ", "NNP", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 8, 4], "deprel": ["nsubj", "cop", "det", "root", "case", "nmod", "nsubj", "acl:relcl", "obj", "punct"], "triples": [{"uid": "1477631:2-0", "target_tags": "Thius\\O is\\O a\\O must\\O for\\O anyone\\O who\\O loves\\O Shabu-Shabu\\B .\\O", "opinion_tags": "Thius\\O is\\O a\\O must\\O for\\O anyone\\O who\\O loves\\B Shabu-Shabu\\O .\\O", "sentiment": "positive"}]}, {"id": "1328078:3", "sentence": "the salads are delicious , both refreshing and very spicy .", "postag": ["DT", "NNS", "VBP", "JJ", ",", "CC", "JJ", "CC", "RB", "JJ", "."], "head": [2, 4, 4, 0, 7, 7, 4, 10, 10, 7, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc:preconj", "appos", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "1328078:3-0", "target_tags": "the\\O salads\\B are\\O delicious\\O ,\\O both\\O refreshing\\O and\\O very\\O spicy\\O .\\O", "opinion_tags": "the\\O salads\\O are\\O delicious\\B ,\\O both\\O refreshing\\B and\\O very\\O spicy\\B .\\O", "sentiment": "positive"}]}, {"id": "BHD#7:0", "sentence": "amazing fresh dogs but best of all endless toppings ! ! !", "postag": ["JJ", "JJ", "NNS", "CC", "JJS", "IN", "DT", "JJ", "NNS", ".", ".", "."], "head": [3, 3, 0, 5, 3, 9, 9, 9, 5, 3, 3, 3], "deprel": ["amod", "amod", "root", "cc", "conj", "case", "det", "amod", "obl", "punct", "punct", "punct"], "triples": [{"uid": "BHD#7:0-0", "target_tags": "amazing\\O fresh\\O dogs\\B but\\O best\\O of\\O all\\O endless\\O toppings\\O !\\O !\\O !\\O", "opinion_tags": "amazing\\B fresh\\I dogs\\O but\\O best\\O of\\O all\\O endless\\O toppings\\O !\\O !\\O !\\O", "sentiment": "positive"}, {"uid": "BHD#7:0-1", "target_tags": "amazing\\O fresh\\O dogs\\O but\\O best\\O of\\O all\\O endless\\O toppings\\B !\\O !\\O !\\O", "opinion_tags": "amazing\\O fresh\\O dogs\\O but\\O best\\B of\\O all\\O endless\\B toppings\\O !\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "1102681:2", "sentence": "The food was average to above-average ; the French Onion soup filling yet not overly impressive , and the desserts not brilliant in any way .", "postag": ["DT", "NN", "VBD", "JJ", "IN", "JJ", ",", "DT", "JJ", "NN", "NN", "VBG", "RB", "RB", "RB", "JJ", ",", "CC", "DT", "NNS", "RB", "JJ", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 4, 11, 11, 11, 4, 11, 16, 16, 16, 12, 22, 22, 20, 22, 22, 4, 25, 25, 22, 4], "deprel": ["det", "nsubj", "cop", "root", "case", "obl", "punct", "det", "amod", "compound", "parataxis", "acl", "advmod", "advmod", "advmod", "xcomp", "punct", "cc", "det", "nsubj", "advmod", "conj", "case", "det", "obl", "punct"], "triples": [{"uid": "1102681:2-0", "target_tags": "The\\O food\\B was\\O average\\O to\\O above-average\\O ;\\O the\\O French\\O Onion\\O soup\\O filling\\O yet\\O not\\O overly\\O impressive\\O ,\\O and\\O the\\O desserts\\O not\\O brilliant\\O in\\O any\\O way\\O .\\O", "opinion_tags": "The\\O food\\O was\\O average\\B to\\I above-average\\I ;\\O the\\O French\\O Onion\\O soup\\O filling\\O yet\\O not\\O overly\\O impressive\\O ,\\O and\\O the\\O desserts\\O not\\O brilliant\\O in\\O any\\O way\\O .\\O", "sentiment": "positive"}, {"uid": "1102681:2-1", "target_tags": "The\\O food\\O was\\O average\\O to\\O above-average\\O ;\\O the\\O French\\B Onion\\I soup\\I filling\\O yet\\O not\\O overly\\O impressive\\O ,\\O and\\O the\\O desserts\\O not\\O brilliant\\O in\\O any\\O way\\O .\\O", "opinion_tags": "The\\O food\\O was\\O average\\O to\\O above-average\\O ;\\O the\\O French\\O Onion\\O soup\\O filling\\O yet\\O not\\B overly\\I impressive\\I ,\\O and\\O the\\O desserts\\O not\\O brilliant\\O in\\O any\\O way\\O .\\O", "sentiment": "positive"}, {"uid": "1102681:2-2", "target_tags": "The\\O food\\O was\\O average\\O to\\O above-average\\O ;\\O the\\O French\\O Onion\\O soup\\O filling\\O yet\\O not\\O overly\\O impressive\\O ,\\O and\\O the\\O desserts\\B not\\O brilliant\\O in\\O any\\O way\\O .\\O", "opinion_tags": "The\\O food\\O was\\O average\\O to\\O above-average\\O ;\\O the\\O French\\O Onion\\O soup\\O filling\\O yet\\O not\\O overly\\O impressive\\O ,\\O and\\O the\\O desserts\\O not\\B brilliant\\I in\\O any\\O way\\O .\\O", "sentiment": "negative"}]}, {"id": "1413697:3", "sentence": "I highly recommend to anyone to give this place a try .", "postag": ["PRP", "RB", "VBP", "IN", "NN", "TO", "VB", "DT", "NN", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 3, 9, 7, 11, 7, 3], "deprel": ["nsubj", "advmod", "root", "case", "obl", "mark", "xcomp", "det", "i<PERSON><PERSON>", "det", "obj", "punct"], "triples": [{"uid": "1413697:3-0", "target_tags": "I\\O highly\\O recommend\\O to\\O anyone\\O to\\O give\\O this\\O place\\B a\\O try\\O .\\O", "opinion_tags": "I\\O highly\\O recommend\\B to\\O anyone\\O to\\O give\\O this\\O place\\O a\\O try\\O .\\O", "sentiment": "positive"}]}, {"id": "BFC#9:19", "sentence": "I heard the lobster roll was excellent .", "postag": ["PRP", "VBD", "DT", "NN", "NN", "VBD", "JJ", "."], "head": [2, 0, 5, 5, 7, 7, 2, 2], "deprel": ["nsubj", "root", "det", "compound", "nsubj", "cop", "ccomp", "punct"], "triples": [{"uid": "BFC#9:19-0", "target_tags": "I\\O heard\\O the\\O lobster\\B roll\\I was\\O excellent\\O .\\O", "opinion_tags": "I\\O heard\\O the\\O lobster\\O roll\\O was\\O excellent\\B .\\O", "sentiment": "positive"}]}, {"id": "1626564:3", "sentence": "Waitstaff are very friendly .", "postag": ["NNS", "VBP", "RB", "JJ", "."], "head": [4, 4, 4, 0, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "1626564:3-0", "target_tags": "Waitstaff\\B are\\O very\\O friendly\\O .\\O", "opinion_tags": "Waitstaff\\O are\\O very\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "TFS#10:6", "sentence": "The dessert ( we had a pear torte ) was good - but , once again , the staff was unable to provide appropriate drink suggestions .", "postag": ["DT", "NN", "-LRB-", "PRP", "VBD", "DT", "JJ", "NN", "-RRB-", "VBD", "JJ", ",", "CC", ",", "RB", "RB", ",", "DT", "NN", "VBD", "JJ", "TO", "VB", "JJ", "NN", "NNS", "."], "head": [2, 11, 5, 5, 2, 8, 8, 5, 5, 11, 0, 11, 21, 21, 16, 21, 16, 19, 21, 21, 11, 23, 21, 26, 26, 23, 11], "deprel": ["det", "nsubj", "punct", "nsubj", "parataxis", "det", "amod", "obj", "punct", "cop", "root", "punct", "cc", "punct", "advmod", "advmod", "punct", "det", "nsubj", "cop", "conj", "mark", "xcomp", "amod", "compound", "obj", "punct"], "triples": [{"uid": "TFS#10:6-0", "target_tags": "The\\O dessert\\O (\\O we\\O had\\O a\\O pear\\B torte\\I )\\O was\\O good\\O -\\O but\\O ,\\O once\\O again\\O ,\\O the\\O staff\\O was\\O unable\\O to\\O provide\\O appropriate\\O drink\\O suggestions\\O .\\O", "opinion_tags": "The\\O dessert\\O (\\O we\\O had\\O a\\O pear\\O torte\\O )\\O was\\O good\\B -\\O but\\O ,\\O once\\O again\\O ,\\O the\\O staff\\O was\\O unable\\O to\\O provide\\O appropriate\\O drink\\O suggestions\\O .\\O", "sentiment": "positive"}, {"uid": "TFS#10:6-1", "target_tags": "The\\O dessert\\O (\\O we\\O had\\O a\\O pear\\O torte\\O )\\O was\\O good\\O -\\O but\\O ,\\O once\\O again\\O ,\\O the\\O staff\\B was\\O unable\\O to\\O provide\\O appropriate\\O drink\\O suggestions\\O .\\O", "opinion_tags": "The\\O dessert\\O (\\O we\\O had\\O a\\O pear\\O torte\\O )\\O was\\O good\\O -\\O but\\O ,\\O once\\O again\\O ,\\O the\\O staff\\O was\\O unable\\B to\\I provide\\I appropriate\\O drink\\O suggestions\\O .\\O", "sentiment": "negative"}]}, {"id": "1090587:3", "sentence": "The lava cake dessert was incredible and I recommend it .", "postag": ["DT", "NN", "NN", "NN", "VBD", "JJ", "CC", "PRP", "VBP", "PRP", "."], "head": [4, 3, 4, 6, 6, 0, 9, 9, 6, 9, 6], "deprel": ["det", "compound", "compound", "nsubj", "cop", "root", "cc", "nsubj", "conj", "obj", "punct"], "triples": [{"uid": "1090587:3-0", "target_tags": "The\\O lava\\B cake\\I dessert\\I was\\O incredible\\O and\\O I\\O recommend\\O it\\O .\\O", "opinion_tags": "The\\O lava\\O cake\\O dessert\\O was\\O incredible\\B and\\O I\\O recommend\\B it\\O .\\O", "sentiment": "positive"}]}, {"id": "FF#3:11", "sentence": "So , I switch with my boyfriend again to see if maybe I could stomach the meat and spinach again , but the spinach was so undercooked that I just could not bite through it .", "postag": ["RB", ",", "PRP", "VBP", "IN", "PRP$", "NN", "RB", "TO", "VB", "IN", "RB", "PRP", "MD", "VB", "DT", "NN", "CC", "NN", "RB", ",", "CC", "DT", "NN", "VBD", "RB", "JJ", "IN", "PRP", "RB", "MD", "RB", "VB", "IN", "PRP", "."], "head": [4, 4, 4, 0, 7, 7, 4, 4, 10, 4, 15, 15, 15, 15, 10, 17, 15, 19, 17, 15, 27, 27, 24, 27, 27, 27, 4, 33, 33, 33, 33, 33, 27, 35, 33, 4], "deprel": ["advmod", "punct", "nsubj", "root", "case", "nmod:poss", "obl", "advmod", "mark", "advcl", "mark", "advmod", "nsubj", "aux", "advcl", "det", "obj", "cc", "conj", "advmod", "punct", "cc", "det", "nsubj", "cop", "advmod", "conj", "mark", "nsubj", "advmod", "aux", "advmod", "ccomp", "case", "obl", "punct"], "triples": [{"uid": "FF#3:11-0", "target_tags": "So\\O ,\\O I\\O switch\\O with\\O my\\O boyfriend\\O again\\O to\\O see\\O if\\O maybe\\O I\\O could\\O stomach\\O the\\O meat\\O and\\O spinach\\O again\\O ,\\O but\\O the\\O spinach\\B was\\O so\\O undercooked\\O that\\O I\\O just\\O could\\O not\\O bite\\O through\\O it\\O .\\O", "opinion_tags": "So\\O ,\\O I\\O switch\\O with\\O my\\O boyfriend\\O again\\O to\\O see\\O if\\O maybe\\O I\\O could\\O stomach\\O the\\O meat\\O and\\O spinach\\O again\\O ,\\O but\\O the\\O spinach\\O was\\O so\\O undercooked\\B that\\O I\\O just\\O could\\O not\\O bite\\O through\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "1014458:3", "sentence": "The wine list is interesting and has many good values .", "postag": ["DT", "NN", "NN", "VBZ", "JJ", "CC", "VBZ", "JJ", "JJ", "NNS", "."], "head": [3, 3, 5, 5, 0, 7, 5, 10, 10, 7, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "cc", "conj", "amod", "amod", "obj", "punct"], "triples": [{"uid": "1014458:3-0", "target_tags": "The\\O wine\\B list\\I is\\O interesting\\O and\\O has\\O many\\O good\\O values\\O .\\O", "opinion_tags": "The\\O wine\\O list\\O is\\O interesting\\B and\\O has\\O many\\O good\\B values\\I .\\O", "sentiment": "positive"}]}, {"id": "1227999:4", "sentence": "Rude service , medicore food ... there are tons of restaurants in NY ... stay away from this one", "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "EX", "VBP", "NNS", "IN", "NNS", "IN", "NNP", ",", "VB", "RB", "IN", "DT", "NN"], "head": [2, 0, 2, 5, 2, 2, 8, 2, 8, 11, 9, 13, 11, 2, 2, 15, 19, 19, 15], "deprel": ["amod", "root", "punct", "amod", "list", "punct", "expl", "parataxis", "nsubj", "case", "nmod", "case", "nmod", "punct", "parataxis", "advmod", "case", "det", "obl"], "triples": [{"uid": "1227999:4-0", "target_tags": "Rude\\O service\\B ,\\O medicore\\O food\\O ...\\O there\\O are\\O tons\\O of\\O restaurants\\O in\\O NY\\O ...\\O stay\\O away\\O from\\O this\\O one\\O", "opinion_tags": "Rude\\B service\\O ,\\O medicore\\O food\\O ...\\O there\\O are\\O tons\\O of\\O restaurants\\O in\\O NY\\O ...\\O stay\\O away\\O from\\O this\\O one\\O", "sentiment": "negative"}, {"uid": "1227999:4-1", "target_tags": "Rude\\O service\\O ,\\O medicore\\O food\\B ...\\O there\\O are\\O tons\\O of\\O restaurants\\O in\\O NY\\O ...\\O stay\\O away\\O from\\O this\\O one\\O", "opinion_tags": "Rude\\O service\\O ,\\O medicore\\B food\\O ...\\O there\\O are\\O tons\\O of\\O restaurants\\O in\\O NY\\O ...\\O stay\\O away\\O from\\O this\\O one\\O", "sentiment": "neutral"}]}, {"id": "TM#6:5", "sentence": "Even though the place is not beautiful , the food speaks for itself .", "postag": ["RB", "IN", "DT", "NN", "VBZ", "RB", "JJ", ",", "DT", "NN", "VBZ", "IN", "PRP", "."], "head": [7, 7, 4, 7, 7, 7, 11, 11, 10, 11, 0, 13, 11, 11], "deprel": ["advmod", "mark", "det", "nsubj", "cop", "advmod", "advcl", "punct", "det", "nsubj", "root", "case", "obl", "punct"], "triples": [{"uid": "TM#6:5-0", "target_tags": "Even\\O though\\O the\\O place\\B is\\O not\\O beautiful\\O ,\\O the\\O food\\O speaks\\O for\\O itself\\O .\\O", "opinion_tags": "Even\\O though\\O the\\O place\\O is\\O not\\B beautiful\\I ,\\O the\\O food\\O speaks\\O for\\O itself\\O .\\O", "sentiment": "negative"}, {"uid": "TM#6:5-1", "target_tags": "Even\\O though\\O the\\O place\\O is\\O not\\O beautiful\\O ,\\O the\\O food\\B speaks\\O for\\O itself\\O .\\O", "opinion_tags": "Even\\O though\\O the\\O place\\O is\\O not\\O beautiful\\O ,\\O the\\O food\\O speaks\\B for\\I itself\\I .\\O", "sentiment": "positive"}]}, {"id": "1264954:2", "sentence": "The chicken pot pie is exceptional , the cheeseburger huge and delictable , and the service professional wan warm .", "postag": ["DT", "NN", "NN", "NN", "VBZ", "JJ", ",", "DT", "NN", "JJ", "CC", "JJ", ",", "CC", "DT", "NN", "JJ", "NN", "JJ", "."], "head": [4, 3, 4, 6, 6, 0, 10, 9, 10, 6, 12, 10, 19, 19, 16, 19, 18, 19, 6, 6], "deprel": ["det", "compound", "compound", "nsubj", "cop", "root", "punct", "det", "nsubj", "conj", "cc", "conj", "punct", "cc", "det", "nsubj", "amod", "nsubj", "conj", "punct"], "triples": [{"uid": "1264954:2-0", "target_tags": "The\\O chicken\\B pot\\I pie\\I is\\O exceptional\\O ,\\O the\\O cheeseburger\\O huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\O professional\\O wan\\O warm\\O .\\O", "opinion_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\B ,\\O the\\O cheeseburger\\O huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\O professional\\O wan\\O warm\\O .\\O", "sentiment": "positive"}, {"uid": "1264954:2-1", "target_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\O ,\\O the\\O cheeseburger\\B huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\O professional\\O wan\\O warm\\O .\\O", "opinion_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\O ,\\O the\\O cheeseburger\\O huge\\B and\\O delictable\\B ,\\O and\\O the\\O service\\O professional\\O wan\\O warm\\O .\\O", "sentiment": "positive"}, {"uid": "1264954:2-2", "target_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\O ,\\O the\\O cheeseburger\\O huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\B professional\\O wan\\O warm\\O .\\O", "opinion_tags": "The\\O chicken\\O pot\\O pie\\O is\\O exceptional\\O ,\\O the\\O cheeseburger\\O huge\\O and\\O delictable\\O ,\\O and\\O the\\O service\\O professional\\B wan\\O warm\\B .\\O", "sentiment": "positive"}]}, {"id": "1460715:0", "sentence": "Great pizza for lunch place .", "postag": ["JJ", "NN", "IN", "NN", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["amod", "root", "case", "compound", "nmod", "punct"], "triples": [{"uid": "1460715:0-0", "target_tags": "Great\\O pizza\\B for\\O lunch\\O place\\O .\\O", "opinion_tags": "Great\\B pizza\\O for\\O lunch\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "1661043:1", "sentence": "Salads are a delicious way to begin the meal .", "postag": ["NNS", "VBP", "DT", "JJ", "NN", "TO", "VB", "DT", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 9, 7, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "det", "obj", "punct"], "triples": [{"uid": "1661043:1-0", "target_tags": "Salads\\B are\\O a\\O delicious\\O way\\O to\\O begin\\O the\\O meal\\O .\\O", "opinion_tags": "Salads\\O are\\O a\\O delicious\\B way\\O to\\O begin\\O the\\O meal\\O .\\O", "sentiment": "positive"}]}, {"id": "475298:2", "sentence": "Food is great and inexpensive .", "postag": ["NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "475298:2-0", "target_tags": "Food\\B is\\O great\\O and\\O inexpensive\\O .\\O", "opinion_tags": "Food\\O is\\O great\\B and\\O inexpensive\\B .\\O", "sentiment": "positive"}]}, {"id": "746254:2", "sentence": "One would think we 'd get an apology or complimentary drinks - instead , we got a snobby waiter would n't even take our order for 15 minutes and gave us lip when we asked him to do so .", "postag": ["PRP", "MD", "VB", "PRP", "MD", "VB", "DT", "NN", "CC", "JJ", "NNS", ",", "RB", ",", "PRP", "VBD", "DT", "JJ", "NN", "MD", "RB", "RB", "VB", "PRP$", "NN", "IN", "CD", "NNS", "CC", "VBD", "PRP", "NN", "WRB", "PRP", "VBD", "PRP", "TO", "VB", "RB", "."], "head": [3, 3, 0, 6, 6, 3, 8, 6, 11, 11, 8, 3, 16, 16, 16, 3, 19, 19, 16, 23, 23, 23, 16, 25, 23, 28, 28, 23, 30, 23, 30, 30, 35, 35, 30, 35, 38, 35, 38, 3], "deprel": ["nsubj", "aux", "root", "nsubj", "aux", "ccomp", "det", "obj", "cc", "amod", "conj", "punct", "advmod", "punct", "nsubj", "parataxis", "det", "amod", "obj", "aux", "advmod", "advmod", "parataxis", "nmod:poss", "obj", "case", "nummod", "obl", "cc", "conj", "i<PERSON><PERSON>", "obj", "mark", "nsubj", "advcl", "obj", "mark", "xcomp", "advmod", "punct"], "triples": [{"uid": "746254:2-0", "target_tags": "One\\O would\\O think\\O we\\O 'd\\O get\\O an\\O apology\\O or\\O complimentary\\O drinks\\O -\\O instead\\O ,\\O we\\O got\\O a\\O snobby\\O waiter\\B would\\O n't\\O even\\O take\\O our\\O order\\O for\\O 15\\O minutes\\O and\\O gave\\O us\\O lip\\O when\\O we\\O asked\\O him\\O to\\O do\\O so\\O .\\O", "opinion_tags": "One\\O would\\O think\\O we\\O 'd\\O get\\O an\\O apology\\O or\\O complimentary\\O drinks\\O -\\O instead\\O ,\\O we\\O got\\O a\\O snobby\\B waiter\\O would\\O n't\\O even\\O take\\O our\\O order\\O for\\O 15\\O minutes\\O and\\O gave\\O us\\O lip\\O when\\O we\\O asked\\O him\\O to\\O do\\O so\\O .\\O", "sentiment": "negative"}]}, {"id": "TVU#6:4", "sentence": "THE SERVICE IS AMAZING , i 've had different waiters and they were all nice , which is a rare thing in NYC .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "PRP", "VBP", "VBN", "JJ", "NNS", "CC", "PRP", "VBD", "RB", "JJ", ",", "WDT", "VBZ", "DT", "JJ", "NN", "IN", "NNP", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 10, 8, 15, 15, 15, 15, 4, 21, 21, 21, 21, 21, 15, 23, 21, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "aux", "parataxis", "amod", "obj", "cc", "nsubj", "cop", "advmod", "conj", "punct", "nsubj", "cop", "det", "amod", "parataxis", "case", "nmod", "punct"], "triples": [{"uid": "TVU#6:4-0", "target_tags": "THE\\O SERVICE\\B IS\\O AMAZING\\O ,\\O i\\O 've\\O had\\O different\\O waiters\\O and\\O they\\O were\\O all\\O nice\\O ,\\O which\\O is\\O a\\O rare\\O thing\\O in\\O NYC\\O .\\O", "opinion_tags": "THE\\O SERVICE\\O IS\\O AMAZING\\B ,\\O i\\O 've\\O had\\O different\\O waiters\\O and\\O they\\O were\\O all\\O nice\\O ,\\O which\\O is\\O a\\O rare\\O thing\\O in\\O NYC\\O .\\O", "sentiment": "positive"}, {"uid": "TVU#6:4-1", "target_tags": "THE\\O SERVICE\\O IS\\O AMAZING\\O ,\\O i\\O 've\\O had\\O different\\O waiters\\B and\\O they\\O were\\O all\\O nice\\O ,\\O which\\O is\\O a\\O rare\\O thing\\O in\\O NYC\\O .\\O", "opinion_tags": "THE\\O SERVICE\\O IS\\O AMAZING\\O ,\\O i\\O 've\\O had\\O different\\O waiters\\O and\\O they\\O were\\O all\\O nice\\B ,\\O which\\O is\\O a\\O rare\\O thing\\O in\\O NYC\\O .\\O", "sentiment": "positive"}]}, {"id": "1125006:1", "sentence": "Best <PERSON><PERSON>i I ever had and great portion without being ridiculous .", "postag": ["JJS", "NN", "PRP", "RB", "VBD", "CC", "JJ", "NN", "IN", "VBG", "JJ", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 11, 11, 5, 2], "deprel": ["amod", "root", "nsubj", "advmod", "acl:relcl", "cc", "amod", "conj", "mark", "cop", "advcl", "punct"], "triples": [{"uid": "1125006:1-0", "target_tags": "Best\\O Pastrami\\B I\\O ever\\O had\\O and\\O great\\O portion\\O without\\O being\\O ridiculous\\O .\\O", "opinion_tags": "Best\\B Pastrami\\O I\\O ever\\O had\\O and\\O great\\O portion\\O without\\O being\\O ridiculous\\O .\\O", "sentiment": "positive"}, {"uid": "1125006:1-1", "target_tags": "Best\\O Pastrami\\O I\\O ever\\O had\\O and\\O great\\O portion\\B without\\O being\\O ridiculous\\O .\\O", "opinion_tags": "Best\\O Pastrami\\O I\\O ever\\O had\\O and\\O great\\B portion\\O without\\O being\\O ridiculous\\O .\\O", "sentiment": "positive"}]}, {"id": "1730447:2", "sentence": "By far , the best pizza in Manhattan .", "postag": ["IN", "RB", ",", "DT", "JJS", "NN", "IN", "NNP", "."], "head": [2, 6, 6, 6, 6, 0, 8, 6, 6], "deprel": ["case", "nmod", "punct", "det", "amod", "root", "case", "nmod", "punct"], "triples": [{"uid": "1730447:2-0", "target_tags": "By\\O far\\O ,\\O the\\O best\\O pizza\\B in\\O Manhattan\\O .\\O", "opinion_tags": "By\\O far\\O ,\\O the\\O best\\B pizza\\O in\\O Manhattan\\O .\\O", "sentiment": "positive"}]}, {"id": "1632445:3", "sentence": "The service is good and ambience is good for a date or group outing .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "NN", "VBZ", "JJ", "IN", "DT", "NN", "CC", "NN", "NN", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 11, 11, 8, 14, 14, 11, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "nsubj", "cop", "conj", "case", "det", "obl", "cc", "compound", "conj", "punct"], "triples": [{"uid": "1632445:3-0", "target_tags": "The\\O service\\B is\\O good\\O and\\O ambience\\O is\\O good\\O for\\O a\\O date\\O or\\O group\\O outing\\O .\\O", "opinion_tags": "The\\O service\\O is\\O good\\B and\\O ambience\\O is\\O good\\O for\\O a\\O date\\O or\\O group\\O outing\\O .\\O", "sentiment": "positive"}, {"uid": "1632445:3-1", "target_tags": "The\\O service\\O is\\O good\\O and\\O ambience\\B is\\O good\\O for\\O a\\O date\\O or\\O group\\O outing\\O .\\O", "opinion_tags": "The\\O service\\O is\\O good\\O and\\O ambience\\O is\\O good\\B for\\O a\\O date\\O or\\O group\\O outing\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#2:0", "sentence": "Sushi experience was unbelievable with my fiance .", "postag": ["NN", "NN", "VBD", "JJ", "IN", "PRP$", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 4], "deprel": ["compound", "nsubj", "cop", "root", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "Z#2:0-0", "target_tags": "Sushi\\B experience\\O was\\O unbelievable\\O with\\O my\\O fiance\\O .\\O", "opinion_tags": "Sushi\\O experience\\O was\\O unbelievable\\B with\\O my\\O fiance\\O .\\O", "sentiment": "positive"}]}, {"id": "1004293:5", "sentence": "Avoid this place !", "postag": ["VB", "DT", "NN", "."], "head": [0, 3, 1, 1], "deprel": ["root", "det", "obj", "punct"], "triples": [{"uid": "1004293:5-0", "target_tags": "Avoid\\O this\\O place\\B !\\O", "opinion_tags": "Avoid\\B this\\O place\\O !\\O", "sentiment": "negative"}]}, {"id": "1189674:1", "sentence": "The food was amazing , and the service was prompt and helpful , but not over-bearing or rushed .", "postag": ["DT", "NN", "VBD", "JJ", ",", "CC", "DT", "NN", "VBD", "JJ", "CC", "JJ", ",", "CC", "RB", "JJ", "CC", "VBN", "."], "head": [2, 4, 4, 0, 10, 10, 8, 10, 10, 4, 12, 10, 16, 16, 16, 10, 18, 16, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "det", "nsubj", "cop", "conj", "cc", "conj", "punct", "cc", "advmod", "conj", "cc", "conj", "punct"], "triples": [{"uid": "1189674:1-0", "target_tags": "The\\O food\\B was\\O amazing\\O ,\\O and\\O the\\O service\\O was\\O prompt\\O and\\O helpful\\O ,\\O but\\O not\\O over-bearing\\O or\\O rushed\\O .\\O", "opinion_tags": "The\\O food\\O was\\O amazing\\B ,\\O and\\O the\\O service\\O was\\O prompt\\O and\\O helpful\\O ,\\O but\\O not\\O over-bearing\\O or\\O rushed\\O .\\O", "sentiment": "positive"}, {"uid": "1189674:1-1", "target_tags": "The\\O food\\O was\\O amazing\\O ,\\O and\\O the\\O service\\B was\\O prompt\\O and\\O helpful\\O ,\\O but\\O not\\O over-bearing\\O or\\O rushed\\O .\\O", "opinion_tags": "The\\O food\\O was\\O amazing\\O ,\\O and\\O the\\O service\\O was\\O prompt\\B and\\O helpful\\B ,\\O but\\O not\\B over-bearing\\I or\\I rushed\\I .\\O", "sentiment": "positive"}]}, {"id": "490565:5", "sentence": "The in-house lady DJ on Saturday nights has outrageously good taste in music , and moreover , takes requests .", "postag": ["DT", "JJ", "NN", "NNP", "IN", "NNP", "VBZ", "VBZ", "RB", "JJ", "NN", "IN", "NN", ",", "CC", "RB", ",", "VBZ", "NNS", "."], "head": [3, 3, 7, 3, 6, 4, 8, 0, 10, 11, 8, 13, 11, 18, 18, 18, 18, 8, 18, 8], "deprel": ["det", "amod", "nsubj", "appos", "case", "nmod", "aux", "root", "advmod", "amod", "obj", "case", "nmod", "punct", "cc", "advmod", "punct", "conj", "obj", "punct"], "triples": [{"uid": "490565:5-0", "target_tags": "The\\O in-house\\B lady\\I DJ\\I on\\O Saturday\\O nights\\O has\\O outrageously\\O good\\O taste\\O in\\O music\\O ,\\O and\\O moreover\\O ,\\O takes\\O requests\\O .\\O", "opinion_tags": "The\\O in-house\\O lady\\O DJ\\O on\\O Saturday\\O nights\\O has\\O outrageously\\O good\\B taste\\I in\\O music\\O ,\\O and\\O moreover\\O ,\\O takes\\O requests\\O .\\O", "sentiment": "positive"}]}, {"id": "624715:1", "sentence": "Authentic Pakistani food .", "postag": ["JJ", "JJ", "NN", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "amod", "root", "punct"], "triples": [{"uid": "624715:1-0", "target_tags": "Authentic\\O Pakistani\\B food\\I .\\O", "opinion_tags": "Authentic\\B Pakistani\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "1655521:1", "sentence": "I almost hesititate to write a review because the atmosphere was so great and I would hate for it too become to crowded .", "postag": ["PRP", "RB", "VBD", "TO", "VB", "DT", "NN", "IN", "DT", "NN", "VBD", "RB", "JJ", "CC", "PRP", "MD", "VB", "IN", "PRP", "RB", "VB", "IN", "JJ", "."], "head": [3, 3, 0, 5, 3, 7, 5, 13, 10, 13, 13, 13, 5, 17, 17, 17, 13, 19, 17, 21, 17, 23, 21, 3], "deprel": ["nsubj", "advmod", "root", "mark", "xcomp", "det", "obj", "mark", "det", "nsubj", "cop", "advmod", "advcl", "cc", "nsubj", "aux", "conj", "case", "obl", "advmod", "xcomp", "case", "obl", "punct"], "triples": [{"uid": "1655521:1-0", "target_tags": "I\\O almost\\O hesititate\\O to\\O write\\O a\\O review\\O because\\O the\\O atmosphere\\B was\\O so\\O great\\O and\\O I\\O would\\O hate\\O for\\O it\\O too\\O become\\O to\\O crowded\\O .\\O", "opinion_tags": "I\\O almost\\O hesititate\\O to\\O write\\O a\\O review\\O because\\O the\\O atmosphere\\O was\\O so\\O great\\B and\\O I\\O would\\O hate\\O for\\O it\\O too\\O become\\O to\\O crowded\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#10:0", "sentence": "Good Food", "postag": ["JJ", "NN"], "head": [2, 0], "deprel": ["amod", "root"], "triples": [{"uid": "Z#10:0-0", "target_tags": "Good\\O Food\\B", "opinion_tags": "Good\\B Food\\O", "sentiment": "positive"}]}, {"id": "Y#5:4", "sentence": "The waitress was not attentive at all .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "IN", "DT", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "obl", "punct"], "triples": [{"uid": "Y#5:4-0", "target_tags": "The\\O waitress\\B was\\O not\\O attentive\\O at\\O all\\O .\\O", "opinion_tags": "The\\O waitress\\O was\\O not\\B attentive\\I at\\O all\\O .\\O", "sentiment": "negative"}]}, {"id": "1642666:0", "sentence": "We ate at this Thai place following the reviews but very unhappy with the foods .", "postag": ["PRP", "VBD", "IN", "DT", "JJ", "NN", "VBG", "DT", "NNS", "CC", "RB", "JJ", "IN", "DT", "NNS", "."], "head": [2, 0, 6, 6, 6, 2, 9, 9, 2, 12, 12, 9, 15, 15, 12, 2], "deprel": ["nsubj", "root", "case", "det", "amod", "obl", "case", "det", "obl", "cc", "advmod", "conj", "case", "det", "obl", "punct"], "triples": [{"uid": "1642666:0-0", "target_tags": "We\\O ate\\O at\\O this\\O Thai\\O place\\O following\\O the\\O reviews\\O but\\O very\\O unhappy\\O with\\O the\\O foods\\B .\\O", "opinion_tags": "We\\O ate\\O at\\O this\\O Thai\\O place\\O following\\O the\\O reviews\\O but\\O very\\O unhappy\\B with\\O the\\O foods\\O .\\O", "sentiment": "negative"}]}, {"id": "BHD#6:1", "sentence": "the hot dogs were juicy and tender inside and had plenty of crunch and snap on the outside .", "postag": ["DT", "JJ", "NNS", "VBD", "JJ", "CC", "JJ", "RB", "CC", "VBD", "NN", "IN", "NN", "CC", "NN", "IN", "DT", "JJ", "."], "head": [3, 3, 5, 5, 0, 7, 5, 5, 10, 5, 10, 13, 11, 15, 13, 18, 18, 10, 5], "deprel": ["det", "amod", "nsubj", "cop", "root", "cc", "conj", "advmod", "cc", "conj", "obj", "case", "nmod", "cc", "conj", "case", "det", "obl", "punct"], "triples": [{"uid": "BHD#6:1-0", "target_tags": "the\\O hot\\B dogs\\I were\\O juicy\\O and\\O tender\\O inside\\O and\\O had\\O plenty\\O of\\O crunch\\O and\\O snap\\O on\\O the\\O outside\\O .\\O", "opinion_tags": "the\\O hot\\O dogs\\O were\\O juicy\\B and\\O tender\\O inside\\O and\\O had\\O plenty\\O of\\O crunch\\O and\\O snap\\O on\\O the\\O outside\\O .\\O", "sentiment": "positive"}]}, {"id": "430342:0", "sentence": "delicious simple food in nice outdoor atmosphere .", "postag": ["JJ", "JJ", "NN", "IN", "JJ", "JJ", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 3], "deprel": ["amod", "amod", "root", "case", "amod", "amod", "nmod", "punct"], "triples": [{"uid": "430342:0-0", "target_tags": "delicious\\O simple\\O food\\B in\\O nice\\O outdoor\\O atmosphere\\O .\\O", "opinion_tags": "delicious\\B simple\\B food\\O in\\O nice\\O outdoor\\O atmosphere\\O .\\O", "sentiment": "positive"}, {"uid": "430342:0-1", "target_tags": "delicious\\O simple\\O food\\O in\\O nice\\O outdoor\\B atmosphere\\I .\\O", "opinion_tags": "delicious\\O simple\\O food\\O in\\O nice\\B outdoor\\O atmosphere\\O .\\O", "sentiment": "positive"}]}, {"id": "447697:3", "sentence": "Try sushimi cucumber roll .", "postag": ["VB", "DT", "NN", "NN", "."], "head": [0, 4, 4, 1, 1], "deprel": ["root", "det", "compound", "obj", "punct"], "triples": [{"uid": "447697:3-0", "target_tags": "Try\\O sushimi\\B cucumber\\I roll\\I .\\O", "opinion_tags": "Try\\B sushimi\\O cucumber\\O roll\\O .\\O", "sentiment": "positive"}]}, {"id": "TR#2:4", "sentence": "The stuff tilapia was horrid ... tasted like cardboard .", "postag": ["DT", "NN", "NN", "VBD", "JJ", ",", "VBD", "IN", "NN", "."], "head": [3, 3, 5, 5, 0, 5, 5, 9, 7, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "parataxis", "case", "obl", "punct"], "triples": [{"uid": "TR#2:4-0", "target_tags": "The\\O stuff\\B tilapia\\I was\\O horrid\\O ...\\O tasted\\O like\\O cardboard\\O .\\O", "opinion_tags": "The\\O stuff\\O tilapia\\O was\\O horrid\\B ...\\O tasted\\O like\\O cardboard\\O .\\O", "sentiment": "negative"}]}, {"id": "1611489:3", "sentence": "Eating in , the atmosphere saves it , but at your desk , it 's a very disappointing experience .", "postag": ["VBG", "RB", ",", "DT", "NN", "VBZ", "PRP", ",", "CC", "IN", "PRP$", "NN", ",", "PRP", "VBZ", "DT", "RB", "JJ", "NN", "."], "head": [6, 1, 6, 5, 6, 0, 6, 19, 19, 12, 12, 19, 19, 19, 19, 19, 18, 19, 6, 6], "deprel": ["advcl", "advmod", "punct", "det", "nsubj", "root", "obj", "punct", "cc", "case", "nmod:poss", "obl", "punct", "nsubj", "cop", "det", "advmod", "amod", "conj", "punct"], "triples": [{"uid": "1611489:3-0", "target_tags": "Eating\\O in\\O ,\\O the\\O atmosphere\\B saves\\O it\\O ,\\O but\\O at\\O your\\O desk\\O ,\\O it\\O 's\\O a\\O very\\O disappointing\\O experience\\O .\\O", "opinion_tags": "Eating\\O in\\O ,\\O the\\O atmosphere\\O saves\\B it\\O ,\\O but\\O at\\O your\\O desk\\O ,\\O it\\O 's\\O a\\O very\\O disappointing\\O experience\\O .\\O", "sentiment": "positive"}]}, {"id": "527109:0", "sentence": "Volare virgins or weekly regulars , everyone gets treated the same and you ca n't ask for more than that when the service is this friendly .", "postag": ["NN", "NNS", "CC", "JJ", "NNS", ",", "NN", "VBZ", "VBN", "DT", "JJ", "CC", "PRP", "MD", "RB", "VB", "IN", "JJR", "IN", "DT", "WRB", "DT", "NN", "VBZ", "DT", "JJ", "."], "head": [2, 0, 5, 5, 2, 2, 9, 9, 2, 11, 9, 16, 16, 16, 16, 9, 20, 16, 20, 16, 26, 23, 26, 26, 26, 16, 9], "deprel": ["compound", "root", "cc", "amod", "conj", "punct", "nsubj:pass", "aux:pass", "parataxis", "det", "obj", "cc", "nsubj", "aux", "advmod", "conj", "case", "obl", "case", "obl", "mark", "det", "nsubj", "cop", "obl:npmod", "advcl", "punct"], "triples": [{"uid": "527109:0-0", "target_tags": "Volare\\O virgins\\O or\\O weekly\\O regulars\\O ,\\O everyone\\O gets\\O treated\\O the\\O same\\O and\\O you\\O ca\\O n't\\O ask\\O for\\O more\\O than\\O that\\O when\\O the\\O service\\B is\\O this\\O friendly\\O .\\O", "opinion_tags": "Volare\\O virgins\\O or\\O weekly\\O regulars\\O ,\\O everyone\\O gets\\O treated\\O the\\O same\\O and\\O you\\O ca\\O n't\\O ask\\O for\\O more\\O than\\O that\\O when\\O the\\O service\\O is\\O this\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "P#1:0", "sentence": "WORST PLACE ON <PERSON><PERSON><PERSON> STREET IN BROOKLYN", "postag": ["JJS", "NN", "IN", "NNP", "NNP", "IN", "NNP"], "head": [2, 0, 5, 5, 2, 7, 5], "deprel": ["amod", "root", "case", "compound", "nmod", "case", "nmod"], "triples": [{"uid": "P#1:0-0", "target_tags": "WORST\\O PLACE\\B ON\\O SMITH\\O STREET\\O IN\\O BROOKLYN\\O", "opinion_tags": "WORST\\B PLACE\\O ON\\O SMITH\\O STREET\\O IN\\O BROOKLYN\\O", "sentiment": "negative"}]}, {"id": "TM#4:2", "sentence": "The chicken lollipop is my favorite , most of the dishes ( I have to agree with a previous reviewer ) are quite oily and very spicy , espeically the Chilli Chicken .", "postag": ["DT", "NN", "NN", "VBZ", "PRP$", "NN", ",", "JJS", "IN", "DT", "NNS", "-LRB-", "PRP", "VBP", "TO", "VB", "IN", "DT", "JJ", "NN", "-RRB-", "VBP", "RB", "JJ", "CC", "RB", "JJ", ",", "RB", "DT", "NNP", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 6, 24, 11, 11, 8, 14, 14, 6, 16, 14, 20, 20, 20, 16, 14, 24, 24, 6, 27, 27, 24, 32, 32, 32, 32, 24, 6], "deprel": ["det", "compound", "nsubj", "cop", "nmod:poss", "root", "punct", "nsubj", "case", "det", "nmod", "punct", "nsubj", "parataxis", "mark", "xcomp", "case", "det", "amod", "obl", "punct", "cop", "advmod", "parataxis", "cc", "advmod", "conj", "punct", "advmod", "det", "compound", "conj", "punct"], "triples": [{"uid": "TM#4:2-0", "target_tags": "The\\O chicken\\B lollipop\\I is\\O my\\O favorite\\O ,\\O most\\O of\\O the\\O dishes\\O (\\O I\\O have\\O to\\O agree\\O with\\O a\\O previous\\O reviewer\\O )\\O are\\O quite\\O oily\\O and\\O very\\O spicy\\O ,\\O espeically\\O the\\O Chilli\\O Chicken\\O .\\O", "opinion_tags": "The\\O chicken\\O lollipop\\O is\\O my\\O favorite\\B ,\\O most\\O of\\O the\\O dishes\\O (\\O I\\O have\\O to\\O agree\\O with\\O a\\O previous\\O reviewer\\O )\\O are\\O quite\\O oily\\O and\\O very\\O spicy\\O ,\\O espeically\\O the\\O Chilli\\O Chicken\\O .\\O", "sentiment": "positive"}, {"uid": "TM#4:2-1", "target_tags": "The\\O chicken\\O lollipop\\O is\\O my\\O favorite\\O ,\\O most\\O of\\O the\\O dishes\\B (\\O I\\O have\\O to\\O agree\\O with\\O a\\O previous\\O reviewer\\O )\\O are\\O quite\\O oily\\O and\\O very\\O spicy\\O ,\\O espeically\\O the\\O Chilli\\O Chicken\\O .\\O", "opinion_tags": "The\\O chicken\\O lollipop\\O is\\O my\\O favorite\\O ,\\O most\\O of\\O the\\O dishes\\O (\\O I\\O have\\O to\\O agree\\O with\\O a\\O previous\\O reviewer\\O )\\O are\\O quite\\O oily\\B and\\O very\\O spicy\\B ,\\O espeically\\O the\\O Chilli\\O Chicken\\O .\\O", "sentiment": "negative"}, {"uid": "TM#4:2-2", "target_tags": "The\\O chicken\\O lollipop\\O is\\O my\\O favorite\\O ,\\O most\\O of\\O the\\O dishes\\O (\\O I\\O have\\O to\\O agree\\O with\\O a\\O previous\\O reviewer\\O )\\O are\\O quite\\O oily\\O and\\O very\\O spicy\\O ,\\O espeically\\O the\\O Chilli\\B Chicken\\I .\\O", "opinion_tags": "The\\O chicken\\O lollipop\\O is\\O my\\O favorite\\O ,\\O most\\O of\\O the\\O dishes\\O (\\O I\\O have\\O to\\O agree\\O with\\O a\\O previous\\O reviewer\\O )\\O are\\O quite\\O oily\\B and\\O very\\O spicy\\B ,\\O espeically\\O the\\O Chilli\\O Chicken\\O .\\O", "sentiment": "negative"}]}, {"id": "1730447:3", "sentence": "The crust is thin , the ingredients are fresh and the staff is friendly .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "DT", "NNS", "VBP", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 14, 12, 14, 14, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "1730447:3-0", "target_tags": "The\\O crust\\B is\\O thin\\O ,\\O the\\O ingredients\\O are\\O fresh\\O and\\O the\\O staff\\O is\\O friendly\\O .\\O", "opinion_tags": "The\\O crust\\O is\\O thin\\B ,\\O the\\O ingredients\\O are\\O fresh\\O and\\O the\\O staff\\O is\\O friendly\\O .\\O", "sentiment": "positive"}, {"uid": "1730447:3-1", "target_tags": "The\\O crust\\O is\\O thin\\O ,\\O the\\O ingredients\\O are\\O fresh\\O and\\O the\\O staff\\B is\\O friendly\\O .\\O", "opinion_tags": "The\\O crust\\O is\\O thin\\O ,\\O the\\O ingredients\\O are\\O fresh\\O and\\O the\\O staff\\O is\\O friendly\\B .\\O", "sentiment": "positive"}, {"uid": "1730447:3-2", "target_tags": "The\\O crust\\O is\\O thin\\O ,\\O the\\O ingredients\\B are\\O fresh\\O and\\O the\\O staff\\O is\\O friendly\\O .\\O", "opinion_tags": "The\\O crust\\O is\\O thin\\O ,\\O the\\O ingredients\\O are\\O fresh\\B and\\O the\\O staff\\O is\\O friendly\\O .\\O", "sentiment": "positive"}]}, {"id": "1126814:3", "sentence": "Decent wine at reasonable prices .", "postag": ["JJ", "NN", "IN", "JJ", "NNS", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["amod", "root", "case", "amod", "nmod", "punct"], "triples": [{"uid": "1126814:3-0", "target_tags": "Decent\\O wine\\B at\\O reasonable\\O prices\\O .\\O", "opinion_tags": "Decent\\B wine\\O at\\O reasonable\\O prices\\O .\\O", "sentiment": "positive"}]}, {"id": "TFS#2:0", "sentence": "The Four Seasons restaurant is a great experience .", "postag": ["DT", "CD", "NNPS", "NN", "VBZ", "DT", "JJ", "NN", "."], "head": [4, 3, 4, 8, 8, 8, 8, 0, 8], "deprel": ["det", "nummod", "compound", "nsubj", "cop", "det", "amod", "root", "punct"], "triples": [{"uid": "TFS#2:0-0", "target_tags": "The\\B Four\\I Seasons\\I restaurant\\I is\\O a\\O great\\O experience\\O .\\O", "opinion_tags": "The\\O Four\\O Seasons\\O restaurant\\O is\\O a\\O great\\B experience\\O .\\O", "sentiment": "positive"}]}, {"id": "1451107:0", "sentence": "This place is so much fun .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "NN", "."], "head": [2, 6, 6, 5, 6, 0, 6], "deprel": ["det", "nsubj", "cop", "advmod", "amod", "root", "punct"], "triples": [{"uid": "1451107:0-0", "target_tags": "This\\O place\\B is\\O so\\O much\\O fun\\O .\\O", "opinion_tags": "This\\O place\\O is\\O so\\O much\\O fun\\B .\\O", "sentiment": "positive"}]}, {"id": "1280166:2", "sentence": "Food was very good as well , considering that we tried the budget selection ( though I wish the pork belly that I ordered was roasted a bit longer , so that fat was more of a melt-in-your-mouth experience ) .", "postag": ["NN", "VBD", "RB", "JJ", "RB", "RB", ",", "VBG", "IN", "PRP", "VBD", "DT", "NN", "NN", "-LRB-", "IN", "PRP", "VBP", "DT", "NN", "NN", "WDT", "PRP", "VBD", "VBD", "VBN", "DT", "NN", "RBR", ",", "RB", "IN", "NN", "VBD", "JJR", "IN", "DT", "JJ", "NN", "-RRB-", "."], "head": [4, 4, 4, 0, 4, 5, 4, 4, 11, 11, 8, 14, 14, 11, 18, 18, 18, 8, 21, 21, 26, 24, 24, 21, 26, 18, 28, 29, 26, 35, 35, 35, 35, 35, 26, 39, 39, 39, 35, 18, 4], "deprel": ["nsubj", "cop", "advmod", "root", "advmod", "fixed", "punct", "advcl", "mark", "nsubj", "ccomp", "det", "compound", "obj", "punct", "mark", "nsubj", "advcl", "det", "compound", "nsubj:pass", "obj", "nsubj", "acl:relcl", "aux:pass", "ccomp", "det", "obl:npmod", "advmod", "punct", "advmod", "mark", "nsubj", "cop", "conj", "case", "det", "amod", "obl", "punct", "punct"], "triples": [{"uid": "1280166:2-0", "target_tags": "Food\\B was\\O very\\O good\\O as\\O well\\O ,\\O considering\\O that\\O we\\O tried\\O the\\O budget\\O selection\\O (\\O though\\O I\\O wish\\O the\\O pork\\O belly\\O that\\O I\\O ordered\\O was\\O roasted\\O a\\O bit\\O longer\\O ,\\O so\\O that\\O fat\\O was\\O more\\O of\\O a\\O melt-in-your-mouth\\O experience\\O )\\O .\\O", "opinion_tags": "Food\\O was\\O very\\O good\\B as\\O well\\O ,\\O considering\\O that\\O we\\O tried\\O the\\O budget\\O selection\\O (\\O though\\O I\\O wish\\O the\\O pork\\O belly\\O that\\O I\\O ordered\\O was\\O roasted\\O a\\O bit\\O longer\\O ,\\O so\\O that\\O fat\\O was\\O more\\O of\\O a\\O melt-in-your-mouth\\O experience\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "1367358:3", "sentence": "Ambience is delightful , service impeccable .", "postag": ["NN", "VBZ", "JJ", ",", "NN", "JJ", "."], "head": [3, 3, 0, 3, 6, 3, 3], "deprel": ["nsubj", "cop", "root", "punct", "nsubj", "conj", "punct"], "triples": [{"uid": "1367358:3-0", "target_tags": "Ambience\\B is\\O delightful\\O ,\\O service\\O impeccable\\O .\\O", "opinion_tags": "Ambience\\O is\\O delightful\\B ,\\O service\\O impeccable\\O .\\O", "sentiment": "positive"}, {"uid": "1367358:3-1", "target_tags": "Ambience\\O is\\O delightful\\O ,\\O service\\B impeccable\\O .\\O", "opinion_tags": "Ambience\\O is\\O delightful\\O ,\\O service\\O impeccable\\B .\\O", "sentiment": "positive"}]}, {"id": "490565:1", "sentence": "The wait staff is pleasant , fun , and for the most part gorgeous ( in the wonderful aesthetic beautification way , not in that she's-way-cuter-than-me-that-b @ # $ * way ) .", "postag": ["DT", "NN", "NN", "VBZ", "JJ", ",", "JJ", ",", "CC", "IN", "DT", "JJS", "NN", "JJ", "-LRB-", "IN", "DT", "JJ", "JJ", "NN", "NN", ",", "RB", "IN", "DT", "NN", "IN", "NN", "$", "NFP", "NN", "-RRB-", "."], "head": [3, 3, 5, 5, 0, 7, 5, 14, 31, 13, 13, 13, 31, 13, 21, 21, 21, 21, 21, 21, 14, 26, 26, 26, 26, 31, 31, 31, 31, 31, 5, 31, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "conj", "punct", "cc", "case", "det", "amod", "nmod", "amod", "punct", "case", "det", "amod", "amod", "compound", "obl", "punct", "advmod", "case", "det", "nmod", "case", "compound", "compound", "punct", "conj", "punct", "punct"], "triples": [{"uid": "490565:1-0", "target_tags": "The\\O wait\\B staff\\I is\\O pleasant\\O ,\\O fun\\O ,\\O and\\O for\\O the\\O most\\O part\\O gorgeous\\O (\\O in\\O the\\O wonderful\\O aesthetic\\O beautification\\O way\\O ,\\O not\\O in\\O that\\O she's-way-cuter-than-me-that-b\\O @\\O #\\O $\\O *\\O way\\O )\\O .\\O", "opinion_tags": "The\\O wait\\O staff\\O is\\O pleasant\\B ,\\O fun\\B ,\\O and\\O for\\O the\\O most\\O part\\O gorgeous\\B (\\O in\\O the\\O wonderful\\O aesthetic\\O beautification\\O way\\O ,\\O not\\O in\\O that\\O she's-way-cuter-than-me-that-b\\O @\\O #\\O $\\O *\\O way\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "NP#9:1", "sentence": "Mussles and calamari were superb Saturday evening .", "postag": ["NNS", "CC", "NN", "VBD", "JJ", "NNP", "NN", "."], "head": [5, 3, 1, 5, 0, 7, 5, 5], "deprel": ["nsubj", "cc", "conj", "cop", "root", "compound", "obl:tmod", "punct"], "triples": [{"uid": "NP#9:1-0", "target_tags": "Mussles\\B and\\O calamari\\O were\\O superb\\O Saturday\\O evening\\O .\\O", "opinion_tags": "Mussles\\O and\\O calamari\\O were\\O superb\\B Saturday\\O evening\\O .\\O", "sentiment": "positive"}, {"uid": "NP#9:1-1", "target_tags": "Mussles\\O and\\O calamari\\B were\\O superb\\O Saturday\\O evening\\O .\\O", "opinion_tags": "Mussles\\O and\\O calamari\\O were\\O superb\\B Saturday\\O evening\\O .\\O", "sentiment": "positive"}]}, {"id": "1289424:4", "sentence": "The place 's decor and hidden bathrooms made for a good laugh .", "postag": ["DT", "NN", "POS", "NN", "CC", "VBN", "NNS", "VBD", "IN", "DT", "JJ", "NN", "."], "head": [2, 4, 2, 8, 7, 7, 4, 0, 12, 12, 12, 8, 8], "deprel": ["det", "nmod:poss", "case", "nsubj", "cc", "amod", "conj", "root", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "1289424:4-0", "target_tags": "The\\O place\\O 's\\O decor\\B and\\O hidden\\O bathrooms\\O made\\O for\\O a\\O good\\O laugh\\O .\\O", "opinion_tags": "The\\O place\\O 's\\O decor\\O and\\O hidden\\O bathrooms\\O made\\O for\\O a\\O good\\B laugh\\I .\\O", "sentiment": "positive"}, {"uid": "1289424:4-1", "target_tags": "The\\O place\\O 's\\O decor\\O and\\O hidden\\B bathrooms\\I made\\O for\\O a\\O good\\O laugh\\O .\\O", "opinion_tags": "The\\O place\\O 's\\O decor\\O and\\O hidden\\O bathrooms\\O made\\O for\\O a\\O good\\B laugh\\I .\\O", "sentiment": "positive"}]}, {"id": "BFC#9:21", "sentence": "All in all the food was good - a little on the expensive side , but fresh .", "postag": ["DT", "IN", "PDT", "DT", "NN", "VBD", "JJ", ",", "DT", "JJ", "IN", "DT", "JJ", "NN", ",", "CC", "JJ", "."], "head": [7, 5, 5, 5, 1, 7, 0, 7, 10, 7, 14, 14, 14, 7, 17, 17, 7, 7], "deprel": ["nsubj", "case", "det:predet", "det", "nmod", "cop", "root", "punct", "det", "obl:npmod", "case", "det", "amod", "obl", "punct", "cc", "conj", "punct"], "triples": [{"uid": "BFC#9:21-0", "target_tags": "All\\O in\\O all\\O the\\O food\\B was\\O good\\O -\\O a\\O little\\O on\\O the\\O expensive\\O side\\O ,\\O but\\O fresh\\O .\\O", "opinion_tags": "All\\O in\\O all\\O the\\O food\\O was\\O good\\B -\\O a\\O little\\O on\\O the\\O expensive\\B side\\O ,\\O but\\O fresh\\B .\\O", "sentiment": "negative"}]}, {"id": "BFC#9:8", "sentence": "The appetizers we ordered were served quickly - an order of fried oysters and clams were delicious but a tiny portion ( maybe 3 of each ) .", "postag": ["DT", "NNS", "PRP", "VBD", "VBD", "VBN", "RB", ",", "DT", "NN", "IN", "JJ", "NNS", "CC", "NNS", "VBD", "JJ", "CC", "DT", "JJ", "NN", "-LRB-", "RB", "CD", "IN", "DT", "-RRB-", "."], "head": [2, 6, 4, 2, 6, 0, 6, 6, 10, 17, 13, 13, 10, 15, 13, 17, 6, 21, 21, 21, 17, 24, 24, 21, 26, 24, 24, 6], "deprel": ["det", "nsubj:pass", "nsubj", "acl:relcl", "aux:pass", "root", "advmod", "punct", "det", "nsubj", "case", "amod", "nmod", "cc", "conj", "cop", "conj", "cc", "det", "amod", "conj", "punct", "advmod", "appos", "case", "nmod", "punct", "punct"], "triples": [{"uid": "BFC#9:8-0", "target_tags": "The\\O appetizers\\O we\\O ordered\\O were\\O served\\O quickly\\O -\\O an\\O order\\O of\\O fried\\B oysters\\I and\\I clams\\I were\\O delicious\\O but\\O a\\O tiny\\O portion\\O (\\O maybe\\O 3\\O of\\O each\\O )\\O .\\O", "opinion_tags": "The\\O appetizers\\O we\\O ordered\\O were\\O served\\O quickly\\O -\\O an\\O order\\O of\\O fried\\O oysters\\O and\\O clams\\O were\\O delicious\\B but\\O a\\O tiny\\O portion\\O (\\O maybe\\O 3\\O of\\O each\\O )\\O .\\O", "sentiment": "negative"}]}, {"id": "DBG#2:11", "sentence": "My girlfriend , being slightly more aggressive , and having been equally disgusted causing her to throw out the remainder of her barely eaten meal , called back only to be informed that I was probably wrong and that it was most likely an oyster , and that we were also blacklisted from their restaurant .", "postag": ["PRP$", "NN", ",", "VBG", "RB", "RBR", "JJ", ",", "CC", "VBG", "VBN", "RB", "JJ", "VBG", "PRP", "TO", "VB", "RP", "DT", "NN", "IN", "PRP$", "RB", "VBN", "NN", ",", "VBN", "RB", "RB", "TO", "VB", "VBN", "IN", "PRP", "VBD", "RB", "JJ", "CC", "IN", "PRP", "VBD", "RBS", "RB", "DT", "NN", ",", "CC", "IN", "PRP", "VBD", "RB", "VBN", "IN", "PRP$", "NN", "."], "head": [2, 27, 7, 7, 6, 7, 2, 13, 13, 13, 13, 13, 7, 13, 14, 17, 14, 17, 20, 17, 25, 25, 24, 25, 20, 27, 0, 27, 32, 32, 32, 27, 37, 37, 37, 37, 32, 45, 45, 45, 45, 43, 45, 45, 37, 52, 52, 52, 52, 52, 52, 45, 55, 55, 52, 27], "deprel": ["nmod:poss", "nsubj", "punct", "cop", "advmod", "advmod", "acl", "punct", "cc", "aux", "cop", "advmod", "conj", "xcomp", "obj", "mark", "xcomp", "compound:prt", "det", "obj", "case", "nmod:poss", "advmod", "amod", "nmod", "punct", "root", "advmod", "advmod", "mark", "aux:pass", "advcl", "mark", "nsubj", "cop", "advmod", "ccomp", "cc", "mark", "nsubj", "cop", "advmod", "advmod", "det", "conj", "punct", "cc", "mark", "nsubj:pass", "aux:pass", "advmod", "conj", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "DBG#2:11-0", "target_tags": "My\\O girlfriend\\O ,\\O being\\O slightly\\O more\\O aggressive\\O ,\\O and\\O having\\O been\\O equally\\O disgusted\\O causing\\O her\\O to\\O throw\\O out\\O the\\O remainder\\O of\\O her\\O barely\\O eaten\\O meal\\B ,\\O called\\O back\\O only\\O to\\O be\\O informed\\O that\\O I\\O was\\O probably\\O wrong\\O and\\O that\\O it\\O was\\O most\\O likely\\O an\\O oyster\\O ,\\O and\\O that\\O we\\O were\\O also\\O blacklisted\\O from\\O their\\O restaurant\\O .\\O", "opinion_tags": "My\\O girlfriend\\O ,\\O being\\O slightly\\O more\\O aggressive\\O ,\\O and\\O having\\O been\\O equally\\O disgusted\\B causing\\O her\\O to\\O throw\\O out\\O the\\O remainder\\O of\\O her\\O barely\\O eaten\\O meal\\O ,\\O called\\O back\\O only\\O to\\O be\\O informed\\O that\\O I\\O was\\O probably\\O wrong\\O and\\O that\\O it\\O was\\O most\\O likely\\O an\\O oyster\\O ,\\O and\\O that\\O we\\O were\\O also\\O blacklisted\\O from\\O their\\O restaurant\\O .\\O", "sentiment": "negative"}]}, {"id": "P#9:3", "sentence": "The omlette for brunch is great ...", "postag": ["DT", "NN", "IN", "NN", "VBZ", "JJ", "."], "head": [2, 6, 4, 2, 6, 0, 6], "deprel": ["det", "nsubj", "case", "nmod", "cop", "root", "punct"], "triples": [{"uid": "P#9:3-0", "target_tags": "The\\O omlette\\B for\\I brunch\\I is\\O great\\O ...\\O", "opinion_tags": "The\\O omlette\\O for\\O brunch\\O is\\O great\\B ...\\O", "sentiment": "positive"}]}, {"id": "1718513:0", "sentence": "We went here for lunch a couple of weeks ago on a Saturday , and I was thoroughly impressed with the food .", "postag": ["PRP", "VBD", "RB", "IN", "NN", "DT", "NN", "IN", "NNS", "RB", "IN", "DT", "NNP", ",", "CC", "PRP", "VBD", "RB", "JJ", "IN", "DT", "NN", "."], "head": [2, 0, 2, 5, 2, 7, 10, 9, 7, 2, 13, 13, 2, 19, 19, 19, 19, 19, 2, 22, 22, 19, 2], "deprel": ["nsubj", "root", "advmod", "case", "obl", "det", "obl:npmod", "case", "nmod", "advmod", "case", "det", "obl", "punct", "cc", "nsubj", "cop", "advmod", "conj", "case", "det", "obl", "punct"], "triples": [{"uid": "1718513:0-0", "target_tags": "We\\O went\\O here\\O for\\O lunch\\O a\\O couple\\O of\\O weeks\\O ago\\O on\\O a\\O Saturday\\O ,\\O and\\O I\\O was\\O thoroughly\\O impressed\\O with\\O the\\O food\\B .\\O", "opinion_tags": "We\\O went\\O here\\O for\\O lunch\\O a\\O couple\\O of\\O weeks\\O ago\\O on\\O a\\O Saturday\\O ,\\O and\\O I\\O was\\O thoroughly\\O impressed\\B with\\O the\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "1649025:2", "sentence": "The food is so cheap and the waiters are nice .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NNS", "VBP", "JJ", "."], "head": [2, 5, 5, 5, 0, 10, 8, 10, 10, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "1649025:2-0", "target_tags": "The\\O food\\B is\\O so\\O cheap\\O and\\O the\\O waiters\\O are\\O nice\\O .\\O", "opinion_tags": "The\\O food\\O is\\O so\\O cheap\\B and\\O the\\O waiters\\O are\\O nice\\O .\\O", "sentiment": "positive"}, {"uid": "1649025:2-1", "target_tags": "The\\O food\\O is\\O so\\O cheap\\O and\\O the\\O waiters\\B are\\O nice\\O .\\O", "opinion_tags": "The\\O food\\O is\\O so\\O cheap\\O and\\O the\\O waiters\\O are\\O nice\\B .\\O", "sentiment": "positive"}]}, {"id": "505535:0", "sentence": "this little place has a cute interior decor and affordable city prices .", "postag": ["DT", "JJ", "NN", "VBZ", "DT", "JJ", "JJ", "NN", "CC", "JJ", "NN", "NNS", "."], "head": [3, 3, 4, 0, 8, 8, 8, 4, 12, 12, 12, 8, 4], "deprel": ["det", "amod", "nsubj", "root", "det", "amod", "amod", "obj", "cc", "amod", "compound", "conj", "punct"], "triples": [{"uid": "505535:0-0", "target_tags": "this\\O little\\O place\\O has\\O a\\O cute\\O interior\\B decor\\I and\\O affordable\\O city\\O prices\\O .\\O", "opinion_tags": "this\\O little\\O place\\O has\\O a\\O cute\\B interior\\O decor\\O and\\O affordable\\O city\\O prices\\O .\\O", "sentiment": "positive"}, {"uid": "505535:0-1", "target_tags": "this\\O little\\O place\\B has\\O a\\O cute\\O interior\\O decor\\O and\\O affordable\\O city\\O prices\\O .\\O", "opinion_tags": "this\\O little\\B place\\O has\\O a\\O cute\\O interior\\O decor\\O and\\O affordable\\O city\\O prices\\O .\\O", "sentiment": "positive"}]}, {"id": "1702257:0", "sentence": "Nice Family owned traditional restaurant .", "postag": ["NNP", "NNP", "VBN", "JJ", "NN", "."], "head": [2, 3, 5, 5, 0, 5], "deprel": ["compound", "compound", "amod", "amod", "root", "punct"], "triples": [{"uid": "1702257:0-0", "target_tags": "Nice\\O Family\\O owned\\O traditional\\O restaurant\\B .\\O", "opinion_tags": "Nice\\O Family\\O owned\\O traditional\\B restaurant\\O .\\O", "sentiment": "positive"}]}, {"id": "512925:1", "sentence": "I really loved the different and inovated touch that 's the cheff gives to the food .", "postag": ["PRP", "RB", "VBD", "DT", "JJ", "CC", "JJ", "NN", "WDT", "VBZ", "DT", "NN", "VBZ", "IN", "DT", "NN", "."], "head": [3, 3, 0, 8, 8, 7, 5, 3, 13, 13, 12, 13, 8, 16, 16, 13, 3], "deprel": ["nsubj", "advmod", "root", "det", "amod", "cc", "conj", "obj", "obj", "aux", "det", "nsubj", "acl:relcl", "case", "det", "obl", "punct"], "triples": [{"uid": "512925:1-0", "target_tags": "I\\O really\\O loved\\O the\\O different\\O and\\O inovated\\O touch\\O that\\O 's\\O the\\O cheff\\B gives\\O to\\O the\\O food\\O .\\O", "opinion_tags": "I\\O really\\O loved\\B the\\O different\\O and\\O inovated\\B touch\\O that\\O 's\\O the\\O cheff\\O gives\\O to\\O the\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#7:2", "sentence": "This place has totally weird decor , stairs going up with mirrored walls - I am surprised how no one yet broke their head or fall off the stairs - mirrored walls make you dizzy and delusional ...", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "NN", ",", "NNS", "VBG", "RP", "IN", "JJ", "NNS", ",", "PRP", "VBP", "JJ", "WRB", "DT", "NN", "RB", "VBD", "PRP$", "NN", "CC", "VB", "IN", "DT", "NNS", "HYPH", "VBN", "NNS", "VBP", "PRP", "JJ", "CC", "JJ", "."], "head": [2, 3, 0, 5, 6, 3, 8, 6, 8, 9, 13, 13, 9, 3, 17, 17, 3, 22, 20, 22, 22, 17, 24, 22, 26, 22, 32, 32, 31, 31, 32, 26, 17, 33, 33, 37, 35, 3], "deprel": ["det", "nsubj", "root", "advmod", "amod", "obj", "punct", "appos", "acl", "compound:prt", "case", "amod", "obl", "punct", "nsubj", "cop", "parataxis", "mark", "det", "nsubj", "advmod", "ccomp", "nmod:poss", "obj", "cc", "conj", "case", "det", "compound", "punct", "amod", "obl", "conj", "obj", "xcomp", "cc", "conj", "punct"], "triples": [{"uid": "Z#7:2-0", "target_tags": "This\\O place\\O has\\O totally\\O weird\\O decor\\B ,\\O stairs\\O going\\O up\\O with\\O mirrored\\O walls\\O -\\O I\\O am\\O surprised\\O how\\O no\\O one\\O yet\\O broke\\O their\\O head\\O or\\O fall\\O off\\O the\\O stairs\\O -\\O mirrored\\O walls\\O make\\O you\\O dizzy\\O and\\O delusional\\O ...\\O", "opinion_tags": "This\\O place\\O has\\O totally\\O weird\\B decor\\O ,\\O stairs\\O going\\O up\\O with\\O mirrored\\O walls\\O -\\O I\\O am\\O surprised\\O how\\O no\\O one\\O yet\\O broke\\O their\\O head\\O or\\O fall\\O off\\O the\\O stairs\\O -\\O mirrored\\O walls\\O make\\O you\\O dizzy\\O and\\O delusional\\O ...\\O", "sentiment": "negative"}, {"uid": "Z#7:2-1", "target_tags": "This\\O place\\O has\\O totally\\O weird\\O decor\\O ,\\O stairs\\O going\\O up\\O with\\O mirrored\\O walls\\O -\\O I\\O am\\O surprised\\O how\\O no\\O one\\O yet\\O broke\\O their\\O head\\O or\\O fall\\O off\\O the\\O stairs\\O -\\O mirrored\\B walls\\I make\\O you\\O dizzy\\O and\\O delusional\\O ...\\O", "opinion_tags": "This\\O place\\O has\\O totally\\O weird\\O decor\\O ,\\O stairs\\O going\\O up\\O with\\O mirrored\\O walls\\O -\\O I\\O am\\O surprised\\O how\\O no\\O one\\O yet\\O broke\\O their\\O head\\O or\\O fall\\O off\\O the\\O stairs\\O -\\O mirrored\\O walls\\O make\\O you\\O dizzy\\B and\\O delusional\\B ...\\O", "sentiment": "negative"}]}, {"id": "BzG#4:3", "sentence": "All the various Greek and Cypriot dishes are excellent , but the gyro is the reason to come -- if you do n't eat one your trip was wasted .", "postag": ["PDT", "DT", "JJ", "JJ", "CC", "JJ", "NNS", "VBP", "JJ", ",", "CC", "DT", "NN", "VBZ", "DT", "NN", "TO", "VB", ",", "IN", "PRP", "VBP", "RB", "VB", "CD", "PRP$", "NN", "VBD", "VBN", "."], "head": [7, 7, 7, 7, 6, 4, 9, 9, 0, 16, 16, 13, 16, 16, 16, 9, 18, 16, 9, 24, 24, 24, 24, 16, 24, 27, 29, 29, 24, 9], "deprel": ["det:predet", "det", "amod", "amod", "cc", "conj", "nsubj", "cop", "root", "punct", "cc", "det", "nsubj", "cop", "det", "conj", "mark", "acl", "punct", "mark", "nsubj", "aux", "advmod", "advcl", "obj", "nmod:poss", "nsubj:pass", "aux:pass", "ccomp", "punct"], "triples": [{"uid": "BzG#4:3-0", "target_tags": "All\\O the\\O various\\O Greek\\B and\\I Cypriot\\I dishes\\I are\\O excellent\\O ,\\O but\\O the\\O gyro\\O is\\O the\\O reason\\O to\\O come\\O --\\O if\\O you\\O do\\O n't\\O eat\\O one\\O your\\O trip\\O was\\O wasted\\O .\\O", "opinion_tags": "All\\O the\\O various\\O Greek\\O and\\O Cypriot\\O dishes\\O are\\O excellent\\B ,\\O but\\O the\\O gyro\\O is\\O the\\O reason\\O to\\O come\\O --\\O if\\O you\\O do\\O n't\\O eat\\O one\\O your\\O trip\\O was\\O wasted\\O .\\O", "sentiment": "positive"}]}, {"id": "535171:2", "sentence": "A cool bar with great food , and tons of excellent beer .", "postag": ["DT", "JJ", "NN", "IN", "JJ", "NN", ",", "CC", "NNS", "IN", "JJ", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 3, 12, 12, 9, 3], "deprel": ["det", "amod", "root", "case", "amod", "nmod", "punct", "cc", "conj", "case", "amod", "nmod", "punct"], "triples": [{"uid": "535171:2-0", "target_tags": "A\\O cool\\O bar\\B with\\O great\\O food\\O ,\\O and\\O tons\\O of\\O excellent\\O beer\\O .\\O", "opinion_tags": "A\\O cool\\B bar\\O with\\O great\\O food\\O ,\\O and\\O tons\\O of\\O excellent\\O beer\\O .\\O", "sentiment": "positive"}, {"uid": "535171:2-1", "target_tags": "A\\O cool\\O bar\\O with\\O great\\O food\\B ,\\O and\\O tons\\O of\\O excellent\\O beer\\O .\\O", "opinion_tags": "A\\O cool\\O bar\\O with\\O great\\B food\\O ,\\O and\\O tons\\O of\\O excellent\\O beer\\O .\\O", "sentiment": "positive"}, {"uid": "535171:2-2", "target_tags": "A\\O cool\\O bar\\O with\\O great\\O food\\O ,\\O and\\O tons\\O of\\O excellent\\O beer\\B .\\O", "opinion_tags": "A\\O cool\\O bar\\O with\\O great\\O food\\O ,\\O and\\O tons\\O of\\O excellent\\B beer\\O .\\O", "sentiment": "positive"}]}, {"id": "1058221:2", "sentence": "the food is decent .", "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "1058221:2-0", "target_tags": "the\\O food\\B is\\O decent\\O .\\O", "opinion_tags": "the\\O food\\O is\\O decent\\B .\\O", "sentiment": "neutral"}]}, {"id": "1335154:4", "sentence": "I did n't complain , I liked the atmosphere so much .", "postag": ["PRP", "VBD", "RB", "VB", ",", "PRP", "VBD", "DT", "NN", "RB", "JJ", "."], "head": [4, 4, 4, 0, 4, 7, 4, 9, 7, 11, 7, 4], "deprel": ["nsubj", "aux", "advmod", "root", "punct", "nsubj", "parataxis", "det", "obj", "advmod", "advmod", "punct"], "triples": [{"uid": "1335154:4-0", "target_tags": "I\\O did\\O n't\\O complain\\O ,\\O I\\O liked\\O the\\O atmosphere\\B so\\O much\\O .\\O", "opinion_tags": "I\\O did\\O n't\\O complain\\O ,\\O I\\O liked\\B the\\O atmosphere\\O so\\O much\\O .\\O", "sentiment": "positive"}]}, {"id": "BHD#1:0", "sentence": "The hot dogs are good , yes , but the reason to get over here is the fantastic pork croquette sandwich , perfect on its supermarket squishy bun .", "postag": ["DT", "JJ", "NNS", "VBP", "JJ", ",", "UH", ",", "CC", "DT", "NN", "TO", "VB", "IN", "RB", "VBZ", "DT", "JJ", "NN", "NN", "NN", ",", "JJ", "IN", "PRP$", "NN", "JJ", "NN", "."], "head": [3, 3, 5, 5, 0, 21, 21, 21, 21, 11, 21, 13, 11, 15, 13, 21, 21, 21, 20, 21, 5, 21, 21, 28, 28, 28, 28, 23, 5], "deprel": ["det", "amod", "nsubj", "cop", "root", "punct", "discourse", "punct", "cc", "det", "nsubj", "mark", "acl", "case", "obl", "cop", "det", "amod", "compound", "compound", "conj", "punct", "amod", "case", "nmod:poss", "compound", "amod", "obl", "punct"], "triples": [{"uid": "BHD#1:0-0", "target_tags": "The\\O hot\\B dogs\\I are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\O croquette\\O sandwich\\O ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O good\\B ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\O croquette\\O sandwich\\O ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O", "sentiment": "positive"}, {"uid": "BHD#1:0-1", "target_tags": "The\\O hot\\O dogs\\O are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\B croquette\\I sandwich\\I ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\B pork\\O croquette\\O sandwich\\O ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O", "sentiment": "positive"}, {"uid": "BHD#1:0-2", "target_tags": "The\\O hot\\O dogs\\O are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\O croquette\\O sandwich\\O ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\B .\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\O croquette\\O sandwich\\O ,\\O perfect\\B on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O", "sentiment": "positive"}]}, {"id": "ADLT#8:1", "sentence": "I recommend this place to everyone .", "postag": ["PRP", "VBP", "DT", "NN", "IN", "NN", "."], "head": [2, 0, 4, 2, 6, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "case", "obl", "punct"], "triples": [{"uid": "ADLT#8:1-0", "target_tags": "I\\O recommend\\O this\\O place\\B to\\O everyone\\O .\\O", "opinion_tags": "I\\O recommend\\B this\\O place\\O to\\O everyone\\O .\\O", "sentiment": "positive"}]}, {"id": "P#3:2", "sentence": "I highly recommend the fish tacos , everything else was ok .", "postag": ["PRP", "RB", "VBP", "DT", "NN", "NNS", ",", "NN", "JJ", "VBD", "JJ", "."], "head": [3, 3, 0, 6, 6, 3, 3, 11, 8, 11, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "compound", "obj", "punct", "nsubj", "amod", "cop", "parataxis", "punct"], "triples": [{"uid": "P#3:2-0", "target_tags": "I\\O highly\\O recommend\\O the\\O fish\\B tacos\\I ,\\O everything\\O else\\O was\\O ok\\O .\\O", "opinion_tags": "I\\O highly\\O recommend\\B the\\O fish\\O tacos\\O ,\\O everything\\O else\\O was\\O ok\\O .\\O", "sentiment": "positive"}]}, {"id": "1280179:2", "sentence": "Seating is always prompt , though the restaurant does fill up in the evening .", "postag": ["NN", "VBZ", "RB", "JJ", ",", "IN", "DT", "NN", "VBZ", "VB", "RP", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 4, 10, 8, 10, 10, 4, 10, 14, 14, 10, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct", "mark", "det", "nsubj", "aux", "advcl", "compound:prt", "case", "det", "obl", "punct"], "triples": [{"uid": "1280179:2-0", "target_tags": "Seating\\B is\\O always\\O prompt\\O ,\\O though\\O the\\O restaurant\\O does\\O fill\\O up\\O in\\O the\\O evening\\O .\\O", "opinion_tags": "Seating\\O is\\O always\\O prompt\\B ,\\O though\\O the\\O restaurant\\O does\\O fill\\O up\\O in\\O the\\O evening\\O .\\O", "sentiment": "positive"}]}, {"id": "1538149:0", "sentence": "I found the food , service and value exceptional everytime I have been there .", "postag": ["PRP", "VBD", "DT", "NN", ",", "NN", "CC", "VBP", "JJ", "RB", "PRP", "VBP", "VBN", "RB", "."], "head": [2, 0, 4, 2, 6, 2, 8, 2, 8, 8, 14, 14, 14, 8, 2], "deprel": ["nsubj", "root", "det", "obj", "punct", "obj", "cc", "conj", "obj", "advmod", "nsubj", "aux", "cop", "ccomp", "punct"], "triples": [{"uid": "1538149:0-0", "target_tags": "I\\O found\\O the\\O food\\B ,\\O service\\O and\\O value\\O exceptional\\O everytime\\O I\\O have\\O been\\O there\\O .\\O", "opinion_tags": "I\\O found\\O the\\O food\\O ,\\O service\\O and\\O value\\O exceptional\\B everytime\\O I\\O have\\O been\\O there\\O .\\O", "sentiment": "positive"}, {"uid": "1538149:0-1", "target_tags": "I\\O found\\O the\\O food\\O ,\\O service\\B and\\O value\\O exceptional\\O everytime\\O I\\O have\\O been\\O there\\O .\\O", "opinion_tags": "I\\O found\\O the\\O food\\O ,\\O service\\O and\\O value\\O exceptional\\B everytime\\O I\\O have\\O been\\O there\\O .\\O", "sentiment": "positive"}]}, {"id": "TM#7:0", "sentence": "Excellent food for great prices", "postag": ["JJ", "NN", "IN", "JJ", "NNS"], "head": [2, 0, 5, 5, 2], "deprel": ["amod", "root", "case", "amod", "nmod"], "triples": [{"uid": "TM#7:0-0", "target_tags": "Excellent\\O food\\B for\\O great\\O prices\\O", "opinion_tags": "Excellent\\B food\\O for\\O great\\O prices\\O", "sentiment": "positive"}]}, {"id": "680345:2", "sentence": "Delicate spices , onions , eggs and a kick-ass roti .", "postag": ["JJ", "NNS", ",", "NNS", ",", "NNS", "CC", "DT", "JJ", "NN", "."], "head": [2, 0, 4, 2, 6, 2, 10, 10, 10, 2, 2], "deprel": ["amod", "root", "punct", "conj", "punct", "conj", "cc", "det", "amod", "conj", "punct"], "triples": [{"uid": "680345:2-0", "target_tags": "Delicate\\O spices\\B ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O", "opinion_tags": "Delicate\\B spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O", "sentiment": "positive"}, {"uid": "680345:2-1", "target_tags": "Delicate\\O spices\\O ,\\O onions\\B ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O", "opinion_tags": "Delicate\\B spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O", "sentiment": "positive"}, {"uid": "680345:2-2", "target_tags": "Delicate\\O spices\\O ,\\O onions\\O ,\\O eggs\\B and\\O a\\O kick-ass\\O roti\\O .\\O", "opinion_tags": "Delicate\\B spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O", "sentiment": "positive"}, {"uid": "680345:2-3", "target_tags": "Delicate\\O spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\B .\\O", "opinion_tags": "Delicate\\O spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\B roti\\O .\\O", "sentiment": "positive"}]}, {"id": "TFS#10:8", "sentence": "Not what I would expect for the price and prestige of this location .", "postag": ["RB", "WP", "PRP", "MD", "VB", "IN", "DT", "NN", "CC", "NN", "IN", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 10, 8, 13, 13, 8, 1], "deprel": ["advmod", "obj", "nsubj", "aux", "root", "case", "det", "obl", "cc", "conj", "case", "det", "nmod", "punct"], "triples": [{"uid": "TFS#10:8-0", "target_tags": "Not\\O what\\O I\\O would\\O expect\\O for\\O the\\O price\\O and\\O prestige\\O of\\O this\\O location\\B .\\O", "opinion_tags": "Not\\O what\\O I\\O would\\O expect\\B for\\O the\\O price\\O and\\O prestige\\O of\\O this\\O location\\O .\\O", "sentiment": "neutral"}]}, {"id": "464215:2", "sentence": "The cream cheeses are out of this world and I love that coffee ! !", "postag": ["DT", "NN", "NNS", "VBP", "IN", "IN", "DT", "NN", "CC", "PRP", "VBP", "DT", "NN", ".", "."], "head": [3, 3, 8, 8, 8, 8, 8, 0, 11, 11, 8, 13, 11, 8, 8], "deprel": ["det", "compound", "nsubj", "cop", "case", "case", "det", "root", "cc", "nsubj", "conj", "det", "obj", "punct", "punct"], "triples": [{"uid": "464215:2-0", "target_tags": "The\\O cream\\B cheeses\\I are\\O out\\O of\\O this\\O world\\O and\\O I\\O love\\O that\\O coffee\\O !\\O !\\O", "opinion_tags": "The\\O cream\\O cheeses\\O are\\O out\\B of\\I this\\I world\\I and\\O I\\O love\\O that\\O coffee\\O !\\O !\\O", "sentiment": "positive"}, {"uid": "464215:2-1", "target_tags": "The\\O cream\\O cheeses\\O are\\O out\\O of\\O this\\O world\\O and\\O I\\O love\\O that\\O coffee\\B !\\O !\\O", "opinion_tags": "The\\O cream\\O cheeses\\O are\\O out\\O of\\O this\\O world\\O and\\O I\\O love\\B that\\O coffee\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "CLF#4:0", "sentence": "Single Worst Restaurant in Manhattan", "postag": ["NNP", "JJS", "NN", "IN", "NNP"], "head": [3, 3, 0, 5, 3], "deprel": ["amod", "amod", "root", "case", "nmod"], "triples": [{"uid": "CLF#4:0-0", "target_tags": "Single\\O Worst\\O Restaurant\\B in\\O Manhattan\\O", "opinion_tags": "Single\\O Worst\\B Restaurant\\O in\\O Manhattan\\O", "sentiment": "negative"}]}, {"id": "935633:3", "sentence": "I found it on a cold night , the perfect spot to warm up .", "postag": ["PRP", "VBD", "PRP", "IN", "DT", "JJ", "NN", ",", "DT", "JJ", "NN", "TO", "VB", "RP", "."], "head": [2, 0, 2, 7, 7, 7, 2, 11, 11, 11, 7, 13, 11, 13, 2], "deprel": ["nsubj", "root", "obj", "case", "det", "amod", "obl", "punct", "det", "amod", "appos", "mark", "acl", "compound:prt", "punct"], "triples": [{"uid": "935633:3-0", "target_tags": "I\\O found\\O it\\O on\\O a\\O cold\\O night\\O ,\\O the\\O perfect\\O spot\\B to\\O warm\\O up\\O .\\O", "opinion_tags": "I\\O found\\O it\\O on\\O a\\O cold\\O night\\O ,\\O the\\O perfect\\B spot\\O to\\O warm\\O up\\O .\\O", "sentiment": "positive"}]}, {"id": "1300636:0", "sentence": "This was my frist time at Cafe St. Bart 's and I must say how delicious the food and the service was .", "postag": ["DT", "VBD", "PRP$", "JJ", "NN", "IN", "NNP", "NNP", "NNP", "POS", "CC", "PRP", "MD", "VB", "WRB", "JJ", "DT", "NN", "CC", "DT", "NN", "VBD", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 8, 8, 14, 14, 14, 5, 16, 14, 18, 16, 21, 21, 18, 16, 5], "deprel": ["nsubj", "cop", "nmod:poss", "amod", "root", "case", "compound", "nmod", "flat", "case", "cc", "nsubj", "aux", "conj", "mark", "ccomp", "det", "nsubj", "cc", "det", "conj", "cop", "punct"], "triples": [{"uid": "1300636:0-0", "target_tags": "This\\O was\\O my\\O frist\\O time\\O at\\O Cafe\\O St.\\O Bart\\O 's\\O and\\O I\\O must\\O say\\O how\\O delicious\\O the\\O food\\B and\\O the\\O service\\O was\\O .\\O", "opinion_tags": "This\\O was\\O my\\O frist\\O time\\O at\\O Cafe\\O St.\\O Bart\\O 's\\O and\\O I\\O must\\O say\\O how\\O delicious\\B the\\O food\\O and\\O the\\O service\\O was\\O .\\O", "sentiment": "positive"}]}, {"id": "BHD#4:0", "sentence": "Great Hot Dogs !", "postag": ["JJ", "JJ", "NNS", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "amod", "root", "punct"], "triples": [{"uid": "BHD#4:0-0", "target_tags": "Great\\O Hot\\B Dogs\\I !\\O", "opinion_tags": "Great\\B Hot\\O Dogs\\O !\\O", "sentiment": "positive"}]}, {"id": "1303984:1", "sentence": "My chow fun and chow see was really bland and oily .", "postag": ["PRP$", "NN", "NN", "CC", "NN", "VBP", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [3, 3, 9, 5, 3, 3, 9, 9, 0, 11, 9, 9], "deprel": ["nmod:poss", "compound", "nsubj", "cc", "conj", "acl", "cop", "advmod", "root", "cc", "conj", "punct"], "triples": [{"uid": "1303984:1-0", "target_tags": "My\\O chow\\B fun\\I and\\I chow\\I see\\I was\\O really\\O bland\\O and\\O oily\\O .\\O", "opinion_tags": "My\\O chow\\O fun\\O and\\O chow\\O see\\O was\\O really\\O bland\\B and\\O oily\\B .\\O", "sentiment": "negative"}]}, {"id": "561054:2", "sentence": "The food is wonderful , tasty and filling , and the service is professional and friendly .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "JJ", "CC", "JJ", ",", "CC", "DT", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 8, 4, 14, 14, 12, 14, 14, 4, 16, 14, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "conj", "cc", "conj", "punct", "cc", "det", "nsubj", "cop", "conj", "cc", "conj", "punct"], "triples": [{"uid": "561054:2-0", "target_tags": "The\\O food\\B is\\O wonderful\\O ,\\O tasty\\O and\\O filling\\O ,\\O and\\O the\\O service\\O is\\O professional\\O and\\O friendly\\O .\\O", "opinion_tags": "The\\O food\\O is\\O wonderful\\B ,\\O tasty\\B and\\O filling\\B ,\\O and\\O the\\O service\\O is\\O professional\\O and\\O friendly\\O .\\O", "sentiment": "positive"}, {"uid": "561054:2-1", "target_tags": "The\\O food\\O is\\O wonderful\\O ,\\O tasty\\O and\\O filling\\O ,\\O and\\O the\\O service\\B is\\O professional\\O and\\O friendly\\O .\\O", "opinion_tags": "The\\O food\\O is\\O wonderful\\O ,\\O tasty\\O and\\O filling\\O ,\\O and\\O the\\O service\\O is\\O professional\\B and\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "BHD#4:3", "sentence": "The hot dogs are top notch , and they 're <PERSON><PERSON> is amazing !", "postag": ["DT", "JJ", "NNS", "VBP", "JJ", "NN", ",", "CC", "PRP", "VBP", "NNP", "VBZ", "JJ", "."], "head": [3, 3, 6, 6, 6, 0, 13, 13, 13, 13, 13, 13, 6, 6], "deprel": ["det", "amod", "nsubj", "cop", "amod", "root", "punct", "cc", "nsubj", "cop", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "BHD#4:3-0", "target_tags": "The\\O hot\\B dogs\\I are\\O top\\O notch\\O ,\\O and\\O they\\O 're\\O Slamwich\\O is\\O amazing\\O !\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O top\\B notch\\I ,\\O and\\O they\\O 're\\O Slamwich\\O is\\O amazing\\O !\\O", "sentiment": "positive"}, {"uid": "BHD#4:3-1", "target_tags": "The\\O hot\\O dogs\\O are\\O top\\O notch\\O ,\\O and\\O they\\O 're\\O Slamwich\\B is\\O amazing\\O !\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O top\\O notch\\O ,\\O and\\O they\\O 're\\O Slamwich\\O is\\O amazing\\B !\\O", "sentiment": "positive"}]}, {"id": "CLF#7:3", "sentence": "The service was spectacular as the waiter knew everything about the menu and his recommendations were amazing !", "postag": ["DT", "NN", "VBD", "JJ", "IN", "DT", "NN", "VBD", "NN", "IN", "DT", "NN", "CC", "PRP$", "NNS", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 8, 7, 8, 4, 8, 12, 12, 9, 17, 15, 17, 17, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "mark", "det", "nsubj", "advcl", "obj", "case", "det", "nmod", "cc", "nmod:poss", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "CLF#7:3-0", "target_tags": "The\\O service\\B was\\O spectacular\\O as\\O the\\O waiter\\O knew\\O everything\\O about\\O the\\O menu\\O and\\O his\\O recommendations\\O were\\O amazing\\O !\\O", "opinion_tags": "The\\O service\\O was\\O spectacular\\B as\\O the\\O waiter\\O knew\\O everything\\O about\\O the\\O menu\\O and\\O his\\O recommendations\\O were\\O amazing\\O !\\O", "sentiment": "positive"}, {"uid": "CLF#7:3-1", "target_tags": "The\\O service\\O was\\O spectacular\\O as\\O the\\O waiter\\B knew\\O everything\\O about\\O the\\O menu\\O and\\O his\\O recommendations\\O were\\O amazing\\O !\\O", "opinion_tags": "The\\O service\\O was\\O spectacular\\O as\\O the\\O waiter\\O knew\\O everything\\O about\\O the\\O menu\\O and\\O his\\O recommendations\\O were\\O amazing\\B !\\O", "sentiment": "positive"}]}, {"id": "1471273:3", "sentence": "Prices too high for this cramped and unappealing resturant .", "postag": ["NNS", "RB", "JJ", "IN", "DT", "JJ", "CC", "JJ", "NN", "."], "head": [3, 3, 0, 9, 9, 9, 8, 6, 3, 3], "deprel": ["nsubj", "advmod", "root", "case", "det", "amod", "cc", "conj", "obl", "punct"], "triples": [{"uid": "1471273:3-0", "target_tags": "Prices\\O too\\O high\\O for\\O this\\O cramped\\O and\\O unappealing\\O resturant\\B .\\O", "opinion_tags": "Prices\\O too\\O high\\O for\\O this\\O cramped\\B and\\O unappealing\\B resturant\\O .\\O", "sentiment": "negative"}]}, {"id": "1230993:0", "sentence": "I had a great time at Jekyll and Hyde !", "postag": ["PRP", "VBD", "DT", "JJ", "NN", "IN", "NNP", "CC", "NNP", "."], "head": [2, 0, 5, 5, 2, 7, 5, 9, 7, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "case", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "1230993:0-0", "target_tags": "I\\O had\\O a\\O great\\O time\\O at\\O Jekyll\\B and\\I Hyde\\I !\\O", "opinion_tags": "I\\O had\\O a\\O great\\B time\\I at\\O Jekyll\\O and\\O Hyde\\O !\\O", "sentiment": "positive"}]}, {"id": "1603698:5", "sentence": "The dinner was ok , nothing I would have again .", "postag": ["DT", "NN", "VBD", "JJ", ",", "NN", "PRP", "MD", "VB", "RB", "."], "head": [2, 4, 4, 0, 4, 4, 9, 9, 6, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "appos", "nsubj", "aux", "acl:relcl", "advmod", "punct"], "triples": [{"uid": "1603698:5-0", "target_tags": "The\\O dinner\\B was\\O ok\\O ,\\O nothing\\O I\\O would\\O have\\O again\\O .\\O", "opinion_tags": "The\\O dinner\\O was\\O ok\\B ,\\O nothing\\O I\\O would\\O have\\O again\\O .\\O", "sentiment": "negative"}]}, {"id": "519425:0", "sentence": "The food was absolutely amazing ! !", "postag": ["DT", "NN", "VBD", "RB", "JJ", ".", "."], "head": [2, 5, 5, 5, 0, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "punct"], "triples": [{"uid": "519425:0-0", "target_tags": "The\\O food\\B was\\O absolutely\\O amazing\\O !\\O !\\O", "opinion_tags": "The\\O food\\O was\\O absolutely\\O amazing\\B !\\O !\\O", "sentiment": "positive"}]}, {"id": "512294:1", "sentence": "If you are looking for a good quality , cheap eats - this is the place .", "postag": ["IN", "PRP", "VBP", "VBG", "IN", "DT", "JJ", "NN", ",", "JJ", "NNS", ",", "DT", "VBZ", "DT", "NN", "."], "head": [4, 4, 4, 16, 8, 8, 8, 4, 11, 11, 8, 16, 16, 16, 16, 0, 16], "deprel": ["mark", "nsubj", "aux", "advcl", "case", "det", "amod", "obl", "punct", "amod", "appos", "punct", "nsubj", "cop", "det", "root", "punct"], "triples": [{"uid": "512294:1-0", "target_tags": "If\\O you\\O are\\O looking\\O for\\O a\\O good\\O quality\\O ,\\O cheap\\O eats\\B -\\O this\\O is\\O the\\O place\\O .\\O", "opinion_tags": "If\\O you\\O are\\O looking\\O for\\O a\\O good\\B quality\\I ,\\O cheap\\B eats\\O -\\O this\\O is\\O the\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "PP#3:1", "sentence": "Hands down the best pizza on the planet .", "postag": ["NNS", "RP", "DT", "JJS", "NN", "IN", "DT", "NN", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 5], "deprel": ["nsubj", "case", "det", "amod", "root", "case", "det", "nmod", "punct"], "triples": [{"uid": "PP#3:1-0", "target_tags": "Hands\\O down\\O the\\O best\\O pizza\\B on\\O the\\O planet\\O .\\O", "opinion_tags": "Hands\\O down\\O the\\O best\\B pizza\\O on\\O the\\O planet\\O .\\O", "sentiment": "positive"}]}, {"id": "CLF#7:4", "sentence": "I completely recommend Casa La Femme for any special occasion and to REALLY impress your date .", "postag": ["PRP", "RB", "VBP", "NNP", "NNP", "NNP", "IN", "DT", "JJ", "NN", "CC", "TO", "RB", "VB", "PRP$", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 10, 10, 10, 3, 14, 14, 14, 3, 16, 14, 3], "deprel": ["nsubj", "advmod", "root", "compound", "compound", "obj", "case", "det", "amod", "obl", "cc", "mark", "advmod", "conj", "nmod:poss", "obj", "punct"], "triples": [{"uid": "CLF#7:4-0", "target_tags": "I\\O completely\\O recommend\\O Casa\\B La\\I Femme\\I for\\O any\\O special\\O occasion\\O and\\O to\\O REALLY\\O impress\\O your\\O date\\O .\\O", "opinion_tags": "I\\O completely\\O recommend\\B Casa\\O La\\O Femme\\O for\\O any\\O special\\O occasion\\O and\\O to\\O REALLY\\O impress\\O your\\O date\\O .\\O", "sentiment": "positive"}]}, {"id": "1014458:2", "sentence": "The duck confit is always amazing and the foie gras terrine with figs was out of this world .", "postag": ["DT", "NN", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NN", "NN", "NN", "IN", "NNS", "VBD", "IN", "IN", "DT", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 18, 11, 11, 11, 18, 13, 11, 18, 18, 18, 18, 6, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "cc", "det", "compound", "compound", "nsubj", "case", "nmod", "cop", "case", "case", "det", "conj", "punct"], "triples": [{"uid": "1014458:2-0", "target_tags": "The\\O duck\\O confit\\O is\\O always\\O amazing\\O and\\O the\\O foie\\B gras\\I terrine\\I with\\I figs\\I was\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "The\\O duck\\O confit\\O is\\O always\\O amazing\\O and\\O the\\O foie\\O gras\\O terrine\\O with\\O figs\\O was\\O out\\B of\\I this\\I world\\I .\\O", "sentiment": "positive"}, {"uid": "1014458:2-1", "target_tags": "The\\O duck\\B confit\\I is\\O always\\O amazing\\O and\\O the\\O foie\\O gras\\O terrine\\O with\\O figs\\O was\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "The\\O duck\\O confit\\O is\\O always\\O amazing\\B and\\O the\\O foie\\O gras\\O terrine\\O with\\O figs\\O was\\O out\\O of\\O this\\O world\\O .\\O", "sentiment": "positive"}]}, {"id": "485966:4", "sentence": "It is definitely a good spot for snacks and chat .", "postag": ["PRP", "VBZ", "RB", "DT", "JJ", "NN", "IN", "NNS", "CC", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 8, 6, 10, 8, 6], "deprel": ["nsubj", "cop", "advmod", "det", "amod", "root", "case", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "485966:4-0", "target_tags": "It\\O is\\O definitely\\O a\\O good\\O spot\\B for\\O snacks\\O and\\O chat\\O .\\O", "opinion_tags": "It\\O is\\O definitely\\O a\\O good\\B spot\\O for\\O snacks\\O and\\O chat\\O .\\O", "sentiment": "positive"}]}, {"id": "P#7:2", "sentence": "The atmosphere was great .", "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "P#7:2-0", "target_tags": "The\\O atmosphere\\B was\\O great\\O .\\O", "opinion_tags": "The\\O atmosphere\\O was\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "BFC#9:5", "sentence": "The menu is fairly simple without much descriptions .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "IN", "JJ", "NNS", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "amod", "obl", "punct"], "triples": [{"uid": "BFC#9:5-0", "target_tags": "The\\O menu\\B is\\O fairly\\O simple\\O without\\O much\\O descriptions\\O .\\O", "opinion_tags": "The\\O menu\\O is\\O fairly\\O simple\\B without\\O much\\O descriptions\\O .\\O", "sentiment": "neutral"}]}, {"id": "720620:1", "sentence": "The food here does a great service to the name ( Cantonese that is ... ) .", "postag": ["DT", "NN", "RB", "VBZ", "DT", "JJ", "NN", "IN", "DT", "NN", "-LRB-", "NNP", "WDT", "VBZ", ",", "-RRB-", "."], "head": [2, 4, 2, 0, 7, 7, 4, 10, 10, 4, 12, 4, 14, 12, 12, 12, 4], "deprel": ["det", "nsubj", "advmod", "root", "det", "amod", "obj", "case", "det", "obl", "punct", "parataxis", "nsubj", "acl:relcl", "punct", "punct", "punct"], "triples": [{"uid": "720620:1-0", "target_tags": "The\\O food\\B here\\O does\\O a\\O great\\O service\\O to\\O the\\O name\\O (\\O Cantonese\\O that\\O is\\O ...\\O )\\O .\\O", "opinion_tags": "The\\O food\\O here\\O does\\O a\\O great\\B service\\O to\\O the\\O name\\O (\\O Cantonese\\O that\\O is\\O ...\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "485966:3", "sentence": "I did not try the caviar but I tried their salmon and crab salad ( they are all good )", "postag": ["PRP", "VBD", "RB", "VB", "DT", "NN", "CC", "PRP", "VBD", "PRP$", "NN", "CC", "NN", "NN", "-LRB-", "PRP", "VBP", "RB", "JJ", "-RRB-"], "head": [4, 4, 4, 0, 6, 4, 9, 9, 4, 11, 9, 14, 14, 11, 19, 19, 19, 19, 4, 19], "deprel": ["nsubj", "aux", "advmod", "root", "det", "obj", "cc", "nsubj", "conj", "nmod:poss", "obj", "cc", "compound", "conj", "punct", "nsubj", "cop", "advmod", "parataxis", "punct"], "triples": [{"uid": "485966:3-0", "target_tags": "I\\O did\\O not\\O try\\O the\\O caviar\\O but\\O I\\O tried\\O their\\O salmon\\B and\\O crab\\O salad\\O (\\O they\\O are\\O all\\O good\\O )\\O", "opinion_tags": "I\\O did\\O not\\O try\\O the\\O caviar\\O but\\O I\\O tried\\O their\\O salmon\\O and\\O crab\\O salad\\O (\\O they\\O are\\O all\\O good\\B )\\O", "sentiment": "positive"}, {"uid": "485966:3-1", "target_tags": "I\\O did\\O not\\O try\\O the\\O caviar\\O but\\O I\\O tried\\O their\\O salmon\\O and\\O crab\\B salad\\I (\\O they\\O are\\O all\\O good\\O )\\O", "opinion_tags": "I\\O did\\O not\\O try\\O the\\O caviar\\O but\\O I\\O tried\\O their\\O salmon\\O and\\O crab\\O salad\\O (\\O they\\O are\\O all\\O good\\B )\\O", "sentiment": "positive"}]}, {"id": "FF#3:19", "sentence": "This is where it really really gets bad : the manager said , there is absolutely nothing we can do , it 's a matter of taste that she did n't like it , and I can not comp it .", "postag": ["DT", "VBZ", "WRB", "PRP", "RB", "RB", "VBZ", "JJ", ":", "DT", "NN", "VBD", ",", "EX", "VBZ", "RB", "NN", "PRP", "MD", "VB", ",", "PRP", "VBZ", "DT", "NN", "IN", "NN", "WDT", "PRP", "VBD", "RB", "VB", "PRP", ",", "CC", "PRP", "MD", "RB", "VB", "PRP", "."], "head": [3, 3, 0, 7, 7, 7, 3, 7, 3, 11, 12, 3, 15, 15, 12, 15, 15, 20, 20, 17, 3, 25, 25, 25, 3, 27, 25, 32, 32, 32, 32, 27, 32, 39, 39, 39, 39, 39, 3, 39, 3], "deprel": ["nsubj", "cop", "root", "nsubj", "advmod", "advmod", "acl:relcl", "xcomp", "punct", "det", "nsubj", "parataxis", "punct", "expl", "ccomp", "advmod", "nsubj", "nsubj", "aux", "acl:relcl", "punct", "nsubj", "cop", "det", "parataxis", "case", "nmod", "obj", "nsubj", "aux", "advmod", "acl:relcl", "obj", "punct", "cc", "nsubj", "aux", "advmod", "conj", "obj", "punct"], "triples": [{"uid": "FF#3:19-0", "target_tags": "This\\O is\\O where\\O it\\O really\\O really\\O gets\\O bad\\O :\\O the\\O manager\\B said\\O ,\\O there\\O is\\O absolutely\\O nothing\\O we\\O can\\O do\\O ,\\O it\\O 's\\O a\\O matter\\O of\\O taste\\O that\\O she\\O did\\O n't\\O like\\O it\\O ,\\O and\\O I\\O can\\O not\\O comp\\O it\\O .\\O", "opinion_tags": "This\\O is\\O where\\O it\\O really\\O really\\O gets\\O bad\\B :\\O the\\O manager\\O said\\O ,\\O there\\O is\\O absolutely\\O nothing\\O we\\O can\\O do\\O ,\\O it\\O 's\\O a\\O matter\\O of\\O taste\\O that\\O she\\O did\\O n't\\O like\\O it\\O ,\\O and\\O I\\O can\\O not\\O comp\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "TFS#11:0", "sentence": "I was speechless by the horrible food .", "postag": ["PRP", "VBD", "JJ", "IN", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 3], "deprel": ["nsubj", "cop", "root", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "TFS#11:0-0", "target_tags": "I\\O was\\O speechless\\O by\\O the\\O horrible\\O food\\B .\\O", "opinion_tags": "I\\O was\\O speechless\\B by\\O the\\O horrible\\B food\\O .\\O", "sentiment": "negative"}]}, {"id": "535248:1", "sentence": "The shrimp scampi was excellent and the antipasti were plentiful .", "postag": ["DT", "NN", "NN", "VBD", "JJ", "CC", "DT", "NNS", "VBD", "JJ", "."], "head": [3, 3, 5, 5, 0, 10, 8, 10, 10, 5, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "535248:1-0", "target_tags": "The\\O shrimp\\B scampi\\I was\\O excellent\\O and\\O the\\O antipasti\\O were\\O plentiful\\O .\\O", "opinion_tags": "The\\O shrimp\\O scampi\\O was\\O excellent\\B and\\O the\\O antipasti\\O were\\O plentiful\\O .\\O", "sentiment": "positive"}, {"uid": "535248:1-1", "target_tags": "The\\O shrimp\\O scampi\\O was\\O excellent\\O and\\O the\\O antipasti\\B were\\O plentiful\\O .\\O", "opinion_tags": "The\\O shrimp\\O scampi\\O was\\O excellent\\O and\\O the\\O antipasti\\O were\\O plentiful\\B .\\O", "sentiment": "positive"}]}, {"id": "1632445:6", "sentence": "The lobster sandwich is $ 24 and although it was good it was not nearly enough to warrant that price .", "postag": ["DT", "NN", "NN", "VBZ", "$", "CD", "CC", "IN", "PRP", "VBD", "JJ", "PRP", "VBD", "RB", "RB", "JJ", "TO", "VB", "DT", "NN", "."], "head": [3, 3, 5, 5, 0, 5, 16, 11, 11, 11, 16, 16, 16, 16, 16, 5, 18, 16, 20, 18, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "nummod", "cc", "mark", "expl", "cop", "advcl", "nsubj", "cop", "advmod", "advmod", "conj", "mark", "csubj", "det", "obj", "punct"], "triples": [{"uid": "1632445:6-0", "target_tags": "The\\O lobster\\B sandwich\\I is\\O $\\O 24\\O and\\O although\\O it\\O was\\O good\\O it\\O was\\O not\\O nearly\\O enough\\O to\\O warrant\\O that\\O price\\O .\\O", "opinion_tags": "The\\O lobster\\O sandwich\\O is\\O $\\O 24\\O and\\O although\\O it\\O was\\O good\\B it\\O was\\O not\\B nearly\\I enough\\I to\\I warrant\\I that\\I price\\I .\\O", "sentiment": "negative"}]}, {"id": "1062641:0", "sentence": "This place has got to be the best japanese restaurant in the new york area .", "postag": ["DT", "NN", "VBZ", "VBN", "TO", "VB", "DT", "JJS", "JJ", "NN", "IN", "DT", "JJ", "NNP", "NN", "."], "head": [2, 4, 4, 0, 10, 10, 10, 10, 10, 4, 15, 15, 15, 15, 10, 4], "deprel": ["det", "nsubj", "aux", "root", "mark", "cop", "det", "amod", "amod", "xcomp", "case", "det", "amod", "compound", "nmod", "punct"], "triples": [{"uid": "1062641:0-0", "target_tags": "This\\O place\\B has\\O got\\O to\\O be\\O the\\O best\\O japanese\\O restaurant\\O in\\O the\\O new\\O york\\O area\\O .\\O", "opinion_tags": "This\\O place\\O has\\O got\\O to\\O be\\O the\\O best\\B japanese\\O restaurant\\O in\\O the\\O new\\O york\\O area\\O .\\O", "sentiment": "positive"}]}, {"id": "1603698:2", "sentence": "Both times I was extremely dissappointed by the service , which was boarderline rude .", "postag": ["DT", "NNS", "PRP", "VBD", "RB", "JJ", "IN", "DT", "NN", ",", "WDT", "VBD", "RBR", "JJ", "."], "head": [2, 6, 6, 6, 6, 0, 9, 9, 6, 9, 14, 14, 14, 9, 6], "deprel": ["det", "obl:tmod", "nsubj", "cop", "advmod", "root", "case", "det", "obl", "punct", "nsubj", "cop", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "1603698:2-0", "target_tags": "Both\\O times\\O I\\O was\\O extremely\\O dissappointed\\O by\\O the\\O service\\B ,\\O which\\O was\\O boarderline\\O rude\\O .\\O", "opinion_tags": "Both\\O times\\O I\\O was\\O extremely\\O dissappointed\\B by\\O the\\O service\\O ,\\O which\\O was\\O boarderline\\O rude\\B .\\O", "sentiment": "negative"}]}, {"id": "571962:1", "sentence": "Good food .", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "571962:1-0", "target_tags": "Good\\O food\\B .\\O", "opinion_tags": "Good\\B food\\O .\\O", "sentiment": "positive"}]}, {"id": "519425:3", "sentence": "The waiter was attentive .", "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "519425:3-0", "target_tags": "The\\O waiter\\B was\\O attentive\\O .\\O", "opinion_tags": "The\\O waiter\\O was\\O attentive\\B .\\O", "sentiment": "positive"}]}, {"id": "1432551:0", "sentence": "Go to Volare for 1st class service and terrific food .", "postag": ["VB", "IN", "NNP", "IN", "JJ", "NN", "NN", "CC", "JJ", "NN", "."], "head": [0, 3, 1, 7, 6, 7, 1, 10, 10, 7, 1], "deprel": ["root", "case", "obl", "case", "amod", "compound", "obl", "cc", "amod", "conj", "punct"], "triples": [{"uid": "1432551:0-0", "target_tags": "Go\\O to\\O Volare\\O for\\O 1st\\O class\\O service\\B and\\O terrific\\O food\\O .\\O", "opinion_tags": "Go\\O to\\O Volare\\O for\\O 1st\\B class\\I service\\O and\\O terrific\\O food\\O .\\O", "sentiment": "positive"}, {"uid": "1432551:0-1", "target_tags": "Go\\O to\\O Volare\\O for\\O 1st\\O class\\O service\\O and\\O terrific\\O food\\B .\\O", "opinion_tags": "Go\\O to\\O Volare\\O for\\O 1st\\O class\\O service\\O and\\O terrific\\B food\\O .\\O", "sentiment": "positive"}]}, {"id": "1728733:5", "sentence": "We ended our great experience by having <PERSON><PERSON><PERSON> ( dessert ) recommended by the waiter .", "postag": ["PRP", "VBD", "PRP$", "JJ", "NN", "IN", "VBG", "NNP", "NNP", "-LRB-", "NN", "-RRB-", "VBN", "IN", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 7, 2, 9, 7, 11, 9, 11, 7, 16, 16, 13, 2], "deprel": ["nsubj", "root", "nmod:poss", "amod", "obj", "mark", "advcl", "compound", "obj", "punct", "appos", "punct", "xcomp", "case", "det", "obl", "punct"], "triples": [{"uid": "1728733:5-0", "target_tags": "We\\O ended\\O our\\O great\\O experience\\O by\\O having\\O Gulab\\B Jamun\\I (\\I dessert\\I )\\I recommended\\O by\\O the\\O waiter\\O .\\O", "opinion_tags": "We\\O ended\\O our\\O great\\B experience\\O by\\O having\\O Gulab\\O Jamun\\O (\\O dessert\\O )\\O recommended\\O by\\O the\\O waiter\\O .\\O", "sentiment": "positive"}]}, {"id": "1353599:1", "sentence": "Mine was a little burnt but still delicious with goat cheese and panchetta ( rad<PERSON>hio was kind of bitter though ) .", "postag": ["PRP", "VBD", "DT", "JJ", "JJ", "CC", "RB", "JJ", "IN", "NN", "NN", "CC", "NN", "-LRB-", "NN", "VBD", "RB", "RB", "JJ", "RB", "-RRB-", "."], "head": [5, 5, 4, 5, 0, 8, 8, 5, 11, 11, 8, 13, 11, 19, 19, 19, 19, 17, 5, 19, 19, 5], "deprel": ["nsubj", "cop", "det", "obl:npmod", "root", "cc", "advmod", "conj", "case", "compound", "obl", "cc", "conj", "punct", "nsubj", "cop", "advmod", "fixed", "parataxis", "advmod", "punct", "punct"], "triples": [{"uid": "1353599:1-0", "target_tags": "Mine\\O was\\O a\\O little\\O burnt\\O but\\O still\\O delicious\\O with\\O goat\\O cheese\\O and\\O panchetta\\O (\\O raddichio\\B was\\O kind\\O of\\O bitter\\O though\\O )\\O .\\O", "opinion_tags": "Mine\\O was\\O a\\O little\\O burnt\\O but\\O still\\O delicious\\O with\\O goat\\O cheese\\O and\\O panchetta\\O (\\O raddichio\\O was\\O kind\\O of\\O bitter\\B though\\O )\\O .\\O", "sentiment": "negative"}]}, {"id": "455623:0", "sentence": "Love Pizza 33 ...", "postag": ["VBP", "NN", "CD", "."], "head": [0, 1, 2, 1], "deprel": ["root", "obj", "nummod", "punct"], "triples": [{"uid": "455623:0-0", "target_tags": "Love\\O Pizza\\B 33\\I ...\\O", "opinion_tags": "Love\\B Pizza\\O 33\\O ...\\O", "sentiment": "positive"}]}, {"id": "1626564:1", "sentence": "Food is excellent .", "postag": ["NN", "VBZ", "JJ", "."], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"], "triples": [{"uid": "1626564:1-0", "target_tags": "Food\\B is\\O excellent\\O .\\O", "opinion_tags": "Food\\O is\\O excellent\\B .\\O", "sentiment": "positive"}]}, {"id": "FF#4:3", "sentence": "The servers at Flatbush Farm appear to have perfected that ghastly technique of making you feel guilty and ashamed for deigning to attract their attention .", "postag": ["DT", "NNS", "IN", "NNP", "NNP", "VBP", "TO", "VB", "VBN", "DT", "JJ", "NN", "IN", "VBG", "PRP", "VB", "JJ", "CC", "JJ", "IN", "VBG", "TO", "VB", "PRP$", "NN", "."], "head": [2, 6, 5, 5, 2, 0, 9, 9, 6, 12, 12, 9, 14, 12, 14, 14, 16, 19, 17, 21, 16, 23, 21, 25, 23, 6], "deprel": ["det", "nsubj", "case", "compound", "nmod", "root", "mark", "aux", "xcomp", "det", "amod", "obj", "mark", "acl", "obj", "xcomp", "xcomp", "cc", "conj", "mark", "advcl", "mark", "xcomp", "nmod:poss", "obj", "punct"], "triples": [{"uid": "FF#4:3-0", "target_tags": "The\\O servers\\B at\\O Flatbush\\O Farm\\O appear\\O to\\O have\\O perfected\\O that\\O ghastly\\O technique\\O of\\O making\\O you\\O feel\\O guilty\\O and\\O ashamed\\O for\\O deigning\\O to\\O attract\\O their\\O attention\\O .\\O", "opinion_tags": "The\\O servers\\O at\\O Flatbush\\O Farm\\O appear\\O to\\O have\\O perfected\\B that\\O ghastly\\O technique\\O of\\O making\\O you\\O feel\\O guilty\\O and\\O ashamed\\O for\\O deigning\\O to\\O attract\\O their\\O attention\\O .\\O", "sentiment": "negative"}]}, {"id": "ADLT#10:3", "sentence": "never swaying , never a bad meal , never bad service ...", "postag": ["RB", "VBG", ",", "RB", "DT", "JJ", "NN", ",", "RB", "JJ", "NN", "."], "head": [2, 0, 2, 7, 7, 7, 2, 11, 11, 11, 2, 2], "deprel": ["advmod", "root", "punct", "advmod", "det", "amod", "conj", "punct", "advmod", "amod", "parataxis", "punct"], "triples": [{"uid": "ADLT#10:3-0", "target_tags": "never\\O swaying\\O ,\\O never\\O a\\O bad\\O meal\\B ,\\O never\\O bad\\O service\\O ...\\O", "opinion_tags": "never\\O swaying\\O ,\\O never\\B a\\I bad\\I meal\\O ,\\O never\\O bad\\O service\\O ...\\O", "sentiment": "positive"}, {"uid": "ADLT#10:3-1", "target_tags": "never\\O swaying\\O ,\\O never\\O a\\O bad\\O meal\\O ,\\O never\\O bad\\O service\\B ...\\O", "opinion_tags": "never\\O swaying\\O ,\\O never\\O a\\O bad\\O meal\\O ,\\O never\\B bad\\I service\\O ...\\O", "sentiment": "positive"}]}, {"id": "1543345:3", "sentence": "Great staff .", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "1543345:3-0", "target_tags": "Great\\O staff\\B .\\O", "opinion_tags": "Great\\B staff\\O .\\O", "sentiment": "positive"}]}, {"id": "680390:1", "sentence": "The food 's as good as ever .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "IN", "RB", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "obl", "punct"], "triples": [{"uid": "680390:1-0", "target_tags": "The\\O food\\B 's\\O as\\O good\\O as\\O ever\\O .\\O", "opinion_tags": "The\\O food\\O 's\\O as\\O good\\B as\\O ever\\O .\\O", "sentiment": "positive"}]}, {"id": "BFC#9:18", "sentence": "Everyone seemed generally happy with their food , except my brother who had the grilled <PERSON><PERSON> Ma<PERSON> , seemingly drenched in Grapfruit Juice !", "postag": ["NN", "VBD", "RB", "JJ", "IN", "PRP$", "NN", ",", "IN", "PRP$", "NN", "WP", "VBD", "DT", "VBN", "NNP", "NN", ",", "RB", "VBN", "IN", "NNP", "NNP", "."], "head": [2, 0, 4, 2, 7, 7, 4, 11, 11, 11, 7, 13, 11, 17, 17, 17, 13, 20, 20, 17, 23, 23, 20, 2], "deprel": ["nsubj", "root", "advmod", "xcomp", "case", "nmod:poss", "obl", "punct", "case", "nmod:poss", "nmod", "nsubj", "acl:relcl", "det", "amod", "compound", "obj", "punct", "advmod", "acl", "case", "compound", "obl", "punct"], "triples": [{"uid": "BFC#9:18-0", "target_tags": "Everyone\\O seemed\\O generally\\O happy\\O with\\O their\\O food\\B ,\\O except\\O my\\O brother\\O who\\O had\\O the\\O grilled\\O Mahi\\O Mahi\\O ,\\O seemingly\\O drenched\\O in\\O Grapfruit\\O Juice\\O !\\O", "opinion_tags": "Everyone\\O seemed\\O generally\\O happy\\B with\\O their\\O food\\O ,\\O except\\O my\\O brother\\O who\\O had\\O the\\O grilled\\O Mahi\\O Mahi\\O ,\\O seemingly\\O drenched\\O in\\O Grapfruit\\O Juice\\O !\\O", "sentiment": "positive"}, {"uid": "BFC#9:18-1", "target_tags": "Everyone\\O seemed\\O generally\\O happy\\O with\\O their\\O food\\O ,\\O except\\O my\\O brother\\O who\\O had\\O the\\O grilled\\B Mahi\\I Mahi\\I ,\\O seemingly\\O drenched\\O in\\O Grapfruit\\O Juice\\O !\\O", "opinion_tags": "Everyone\\O seemed\\O generally\\O happy\\O with\\O their\\O food\\O ,\\O except\\O my\\O brother\\O who\\O had\\O the\\O grilled\\O Mahi\\O Mahi\\O ,\\O seemingly\\O drenched\\B in\\O Grapfruit\\O Juice\\O !\\O", "sentiment": "negative"}]}, {"id": "624715:0", "sentence": "This place is a great bargain .", "postag": ["DT", "NN", "VBZ", "DT", "JJ", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 6], "deprel": ["det", "nsubj", "cop", "det", "amod", "root", "punct"], "triples": [{"uid": "624715:0-0", "target_tags": "This\\O place\\B is\\O a\\O great\\O bargain\\O .\\O", "opinion_tags": "This\\O place\\O is\\O a\\O great\\B bargain\\I .\\O", "sentiment": "positive"}]}, {"id": "FF#10:4", "sentence": "But the service is HORRID !", "postag": ["CC", "DT", "NN", "VBZ", "JJ", "."], "head": [5, 3, 5, 5, 0, 5], "deprel": ["cc", "det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "FF#10:4-0", "target_tags": "But\\O the\\O service\\B is\\O HORRID\\O !\\O", "opinion_tags": "But\\O the\\O service\\O is\\O HORRID\\B !\\O", "sentiment": "negative"}]}, {"id": "1728733:4", "sentence": "The food looked very appetizing and delicious since it came on a variety of fancy plates .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "JJ", "IN", "PRP", "VBD", "IN", "DT", "NN", "IN", "JJ", "NNS", "."], "head": [2, 3, 0, 5, 3, 7, 5, 10, 10, 3, 13, 13, 10, 16, 16, 13, 3], "deprel": ["det", "nsubj", "root", "advmod", "xcomp", "cc", "conj", "mark", "nsubj", "advcl", "case", "det", "obl", "case", "amod", "nmod", "punct"], "triples": [{"uid": "1728733:4-0", "target_tags": "The\\O food\\B looked\\O very\\O appetizing\\O and\\O delicious\\O since\\O it\\O came\\O on\\O a\\O variety\\O of\\O fancy\\O plates\\O .\\O", "opinion_tags": "The\\O food\\O looked\\O very\\O appetizing\\B and\\O delicious\\B since\\O it\\O came\\O on\\O a\\O variety\\O of\\O fancy\\O plates\\O .\\O", "sentiment": "positive"}]}, {"id": "758263:0", "sentence": "I have never before eaten 40 pieces of relatively good nigiri .", "postag": ["PRP", "VBP", "RB", "RB", "VBN", "CD", "NNS", "IN", "RB", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 11, 10, 11, 7, 5], "deprel": ["nsubj", "aux", "advmod", "advmod", "root", "nummod", "obj", "case", "advmod", "amod", "nmod", "punct"], "triples": [{"uid": "758263:0-0", "target_tags": "I\\O have\\O never\\O before\\O eaten\\O 40\\O pieces\\O of\\O relatively\\O good\\O nigiri\\B .\\O", "opinion_tags": "I\\O have\\O never\\O before\\O eaten\\O 40\\O pieces\\O of\\O relatively\\O good\\B nigiri\\O .\\O", "sentiment": "neutral"}]}, {"id": "397331:2", "sentence": "Mizu is home to creative and unique rolls not to found anywhere else .", "postag": ["NNP", "VBZ", "NN", "IN", "JJ", "CC", "JJ", "NNS", "RB", "TO", "VB", "RB", "JJ", "."], "head": [3, 3, 0, 8, 8, 7, 5, 3, 11, 11, 3, 11, 12, 3], "deprel": ["nsubj", "cop", "root", "case", "amod", "cc", "conj", "nmod", "advmod", "mark", "advcl", "advmod", "amod", "punct"], "triples": [{"uid": "397331:2-0", "target_tags": "Mizu\\O is\\O home\\O to\\O creative\\O and\\O unique\\O rolls\\B not\\O to\\O found\\O anywhere\\O else\\O .\\O", "opinion_tags": "Mizu\\O is\\O home\\O to\\O creative\\O and\\O unique\\B rolls\\O not\\O to\\O found\\O anywhere\\O else\\O .\\O", "sentiment": "positive"}]}, {"id": "1162037:1", "sentence": "With the great variety on the menu , I eat here often and never get bored .", "postag": ["IN", "DT", "JJ", "NN", "IN", "DT", "NN", ",", "PRP", "VBP", "RB", "RB", "CC", "RB", "VB", "JJ", "."], "head": [4, 4, 4, 10, 7, 7, 4, 10, 10, 0, 10, 10, 15, 15, 10, 15, 10], "deprel": ["case", "det", "amod", "obl", "case", "det", "nmod", "punct", "nsubj", "root", "advmod", "advmod", "cc", "advmod", "conj", "xcomp", "punct"], "triples": [{"uid": "1162037:1-0", "target_tags": "With\\O the\\O great\\O variety\\O on\\O the\\O menu\\B ,\\O I\\O eat\\O here\\O often\\O and\\O never\\O get\\O bored\\O .\\O", "opinion_tags": "With\\O the\\O great\\B variety\\I on\\O the\\O menu\\O ,\\O I\\O eat\\O here\\O often\\O and\\O never\\O get\\O bored\\O .\\O", "sentiment": "positive"}]}, {"id": "680390:0", "sentence": "Toons has recently been redone , so it 's now a very attractive space .", "postag": ["NNS", "VBZ", "RB", "VBN", "VBN", ",", "RB", "PRP", "VBZ", "RB", "DT", "RB", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 5, 14, 14, 14, 14, 14, 13, 14, 5, 5], "deprel": ["nsubj:pass", "aux", "advmod", "aux:pass", "root", "punct", "advmod", "nsubj", "cop", "advmod", "det", "advmod", "amod", "advcl", "punct"], "triples": [{"uid": "680390:0-0", "target_tags": "Toons\\B has\\O recently\\O been\\O redone\\O ,\\O so\\O it\\O 's\\O now\\O a\\O very\\O attractive\\O space\\O .\\O", "opinion_tags": "Toons\\O has\\O recently\\O been\\O redone\\O ,\\O so\\O it\\O 's\\O now\\O a\\O very\\O attractive\\B space\\O .\\O", "sentiment": "positive"}]}, {"id": "Y#5:0", "sentence": "overpriced japanese food with mediocre service", "postag": ["JJ", "JJ", "NN", "IN", "JJ", "NN"], "head": [3, 3, 0, 6, 6, 3], "deprel": ["amod", "amod", "root", "case", "amod", "nmod"], "triples": [{"uid": "Y#5:0-0", "target_tags": "overpriced\\O japanese\\B food\\I with\\O mediocre\\O service\\O", "opinion_tags": "overpriced\\B japanese\\O food\\O with\\O mediocre\\O service\\O", "sentiment": "negative"}, {"uid": "Y#5:0-1", "target_tags": "overpriced\\O japanese\\O food\\O with\\O mediocre\\O service\\B", "opinion_tags": "overpriced\\O japanese\\O food\\O with\\O mediocre\\B service\\O", "sentiment": "neutral"}]}, {"id": "1398844:4", "sentence": "Service is not what one would expect from a joint in this price category .", "postag": ["NN", "VBZ", "RB", "WP", "PRP", "MD", "VB", "IN", "DT", "NN", "IN", "DT", "NN", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 10, 10, 7, 14, 14, 14, 10, 2], "deprel": ["nsubj", "cop", "advmod", "root", "nsubj", "aux", "acl:relcl", "case", "det", "obl", "case", "det", "compound", "nmod", "punct"], "triples": [{"uid": "1398844:4-0", "target_tags": "Service\\B is\\O not\\O what\\O one\\O would\\O expect\\O from\\O a\\O joint\\O in\\O this\\O price\\O category\\O .\\O", "opinion_tags": "Service\\O is\\O not\\B what\\I one\\I would\\I expect\\I from\\O a\\O joint\\O in\\O this\\O price\\O category\\O .\\O", "sentiment": "negative"}]}, {"id": "EVPK#4:1", "sentence": "Love this place , every time we are in the city this is one of the places we always go .", "postag": ["VBP", "DT", "NN", ",", "DT", "NN", "PRP", "VBP", "IN", "DT", "NN", "DT", "VBZ", "CD", "IN", "DT", "NNS", "PRP", "RB", "VBP", "."], "head": [0, 3, 1, 14, 6, 14, 11, 11, 11, 11, 6, 14, 14, 1, 17, 17, 14, 20, 20, 17, 1], "deprel": ["root", "det", "obj", "punct", "det", "obl:tmod", "nsubj", "cop", "case", "det", "acl:relcl", "nsubj", "cop", "parataxis", "case", "det", "nmod", "nsubj", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "EVPK#4:1-0", "target_tags": "Love\\O this\\O place\\B ,\\O every\\O time\\O we\\O are\\O in\\O the\\O city\\O this\\O is\\O one\\O of\\O the\\O places\\O we\\O always\\O go\\O .\\O", "opinion_tags": "Love\\B this\\O place\\O ,\\O every\\O time\\O we\\O are\\O in\\O the\\O city\\O this\\O is\\O one\\O of\\O the\\O places\\O we\\O always\\O go\\O .\\O", "sentiment": "positive"}]}, {"id": "430342:4", "sentence": "Delicious crab cakes too .", "postag": ["JJ", "NN", "NNS", "RB", "."], "head": [3, 3, 0, 3, 3], "deprel": ["amod", "compound", "root", "advmod", "punct"], "triples": [{"uid": "430342:4-0", "target_tags": "Delicious\\O crab\\B cakes\\I too\\O .\\O", "opinion_tags": "Delicious\\B crab\\O cakes\\O too\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#3:0", "sentence": "Excellent food , although the interior could use some help .", "postag": ["JJ", "NN", ",", "IN", "DT", "NN", "MD", "VB", "DT", "NN", "."], "head": [2, 0, 2, 8, 6, 8, 8, 2, 10, 8, 2], "deprel": ["amod", "root", "punct", "mark", "det", "nsubj", "aux", "advcl", "det", "obj", "punct"], "triples": [{"uid": "Z#3:0-0", "target_tags": "Excellent\\O food\\B ,\\O although\\O the\\O interior\\O could\\O use\\O some\\O help\\O .\\O", "opinion_tags": "Excellent\\B food\\O ,\\O although\\O the\\O interior\\O could\\O use\\O some\\O help\\O .\\O", "sentiment": "positive"}, {"uid": "Z#3:0-1", "target_tags": "Excellent\\O food\\O ,\\O although\\O the\\O interior\\B could\\O use\\O some\\O help\\O .\\O", "opinion_tags": "Excellent\\O food\\O ,\\O although\\O the\\O interior\\O could\\O use\\O some\\O help\\B .\\O", "sentiment": "negative"}]}, {"id": "EVPK#10:2", "sentence": "I LOOOVE their eggplant pizza , as well as their pastas !", "postag": ["PRP", "VBP", "PRP$", "NN", "NN", ",", "RB", "RB", "IN", "PRP$", "NNS", "."], "head": [2, 0, 5, 5, 2, 11, 11, 7, 7, 11, 5, 2], "deprel": ["nsubj", "root", "nmod:poss", "compound", "obj", "punct", "cc", "fixed", "fixed", "nmod:poss", "conj", "punct"], "triples": [{"uid": "EVPK#10:2-0", "target_tags": "I\\O LOOOVE\\O their\\O eggplant\\B pizza\\I ,\\O as\\O well\\O as\\O their\\O pastas\\O !\\O", "opinion_tags": "I\\O LOOOVE\\B their\\O eggplant\\O pizza\\O ,\\O as\\O well\\O as\\O their\\O pastas\\O !\\O", "sentiment": "positive"}, {"uid": "EVPK#10:2-1", "target_tags": "I\\O LOOOVE\\O their\\O eggplant\\O pizza\\O ,\\O as\\O well\\O as\\O their\\O pastas\\B !\\O", "opinion_tags": "I\\O LOOOVE\\B their\\O eggplant\\O pizza\\O ,\\O as\\O well\\O as\\O their\\O pastas\\O !\\O", "sentiment": "positive"}]}, {"id": "ADLT#9:6", "sentence": "The farro salad and the mashed yukon potatoes were also extremely tasty .", "postag": ["DT", "NN", "NN", "CC", "DT", "JJ", "NN", "NNS", "VBD", "RB", "RB", "JJ", "."], "head": [3, 3, 12, 8, 8, 8, 8, 3, 12, 12, 12, 0, 12], "deprel": ["det", "compound", "nsubj", "cc", "det", "amod", "compound", "conj", "cop", "advmod", "advmod", "root", "punct"], "triples": [{"uid": "ADLT#9:6-0", "target_tags": "The\\O farro\\B salad\\I and\\O the\\O mashed\\O yukon\\O potatoes\\O were\\O also\\O extremely\\O tasty\\O .\\O", "opinion_tags": "The\\O farro\\O salad\\O and\\O the\\O mashed\\O yukon\\O potatoes\\O were\\O also\\O extremely\\O tasty\\B .\\O", "sentiment": "positive"}, {"uid": "ADLT#9:6-1", "target_tags": "The\\O farro\\O salad\\O and\\O the\\O mashed\\B yukon\\I potatoes\\I were\\O also\\O extremely\\O tasty\\O .\\O", "opinion_tags": "The\\O farro\\O salad\\O and\\O the\\O mashed\\O yukon\\O potatoes\\O were\\O also\\O extremely\\O tasty\\B .\\O", "sentiment": "positive"}]}, {"id": "512294:0", "sentence": "Great food , great prices , great service .", "postag": ["JJ", "NN", ",", "JJ", "NNS", ",", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "amod", "conj", "punct"], "triples": [{"uid": "512294:0-0", "target_tags": "Great\\O food\\B ,\\O great\\O prices\\O ,\\O great\\O service\\O .\\O", "opinion_tags": "Great\\B food\\O ,\\O great\\O prices\\O ,\\O great\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "512294:0-1", "target_tags": "Great\\O food\\O ,\\O great\\O prices\\O ,\\O great\\O service\\B .\\O", "opinion_tags": "Great\\O food\\O ,\\O great\\O prices\\O ,\\O great\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "709294:1", "sentence": "We recently decided to try this location , and to our delight , they have outdoor seating , perfect since I had my yorkie with me .", "postag": ["PRP", "RB", "VBD", "TO", "VB", "DT", "NN", ",", "CC", "IN", "PRP$", "NN", ",", "PRP", "VBP", "JJ", "NN", ",", "JJ", "IN", "PRP", "VBD", "PRP$", "NN", "IN", "PRP", "."], "head": [3, 3, 0, 5, 3, 7, 5, 15, 15, 12, 12, 15, 15, 15, 3, 17, 15, 19, 17, 22, 22, 15, 24, 22, 26, 22, 3], "deprel": ["nsubj", "advmod", "root", "mark", "xcomp", "det", "obj", "punct", "cc", "case", "nmod:poss", "obl", "punct", "nsubj", "conj", "amod", "obj", "punct", "amod", "mark", "nsubj", "advcl", "nmod:poss", "obj", "case", "obl", "punct"], "triples": [{"uid": "709294:1-0", "target_tags": "We\\O recently\\O decided\\O to\\O try\\O this\\O location\\O ,\\O and\\O to\\O our\\O delight\\O ,\\O they\\O have\\O outdoor\\B seating\\I ,\\O perfect\\O since\\O I\\O had\\O my\\O yorkie\\O with\\O me\\O .\\O", "opinion_tags": "We\\O recently\\O decided\\O to\\O try\\O this\\O location\\O ,\\O and\\O to\\O our\\O delight\\O ,\\O they\\O have\\O outdoor\\O seating\\O ,\\O perfect\\B since\\O I\\O had\\O my\\O yorkie\\O with\\O me\\O .\\O", "sentiment": "positive"}]}, {"id": "1118167:2", "sentence": "The pizza was delivered cold and the cheese was n't even fully melted !", "postag": ["DT", "NN", "VBD", "VBN", "JJ", "CC", "DT", "NN", "VBD", "RB", "RB", "RB", "VBN", "."], "head": [2, 4, 4, 0, 4, 13, 8, 13, 13, 13, 13, 13, 4, 4], "deprel": ["det", "nsubj:pass", "aux:pass", "root", "xcomp", "cc", "det", "nsubj:pass", "aux:pass", "advmod", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "1118167:2-0", "target_tags": "The\\O pizza\\B was\\O delivered\\O cold\\O and\\O the\\O cheese\\O was\\O n't\\O even\\O fully\\O melted\\O !\\O", "opinion_tags": "The\\O pizza\\O was\\O delivered\\O cold\\B and\\O the\\O cheese\\O was\\O n't\\O even\\O fully\\O melted\\O !\\O", "sentiment": "negative"}, {"uid": "1118167:2-1", "target_tags": "The\\O pizza\\O was\\O delivered\\O cold\\O and\\O the\\O cheese\\B was\\O n't\\O even\\O fully\\O melted\\O !\\O", "opinion_tags": "The\\O pizza\\O was\\O delivered\\O cold\\O and\\O the\\O cheese\\O was\\B n't\\I even\\I fully\\I melted\\I !\\O", "sentiment": "negative"}]}, {"id": "1253117:1", "sentence": "The pizza is delicious - they use fresh mozzarella instead of the cheap , frozen , shredded cheese common to most pizzaria 's .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "PRP", "VBP", "JJ", "NN", "RB", "IN", "DT", "JJ", ",", "JJ", ",", "VBN", "NN", "JJ", "IN", "JJS", "NN", "POS", "."], "head": [2, 4, 4, 0, 4, 7, 4, 9, 7, 18, 10, 18, 18, 15, 18, 18, 18, 7, 18, 22, 22, 19, 22, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "parataxis", "amod", "obj", "case", "fixed", "det", "amod", "punct", "amod", "punct", "amod", "obl", "amod", "case", "amod", "obl", "case", "punct"], "triples": [{"uid": "1253117:1-0", "target_tags": "The\\O pizza\\B is\\O delicious\\O -\\O they\\O use\\O fresh\\O mozzarella\\O instead\\O of\\O the\\O cheap\\O ,\\O frozen\\O ,\\O shredded\\O cheese\\O common\\O to\\O most\\O pizzaria\\O 's\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O delicious\\B -\\O they\\O use\\O fresh\\O mozzarella\\O instead\\O of\\O the\\O cheap\\O ,\\O frozen\\O ,\\O shredded\\O cheese\\O common\\O to\\O most\\O pizzaria\\O 's\\O .\\O", "sentiment": "positive"}]}, {"id": "FF#10:2", "sentence": "The food is good .", "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "FF#10:2-0", "target_tags": "The\\O food\\B is\\O good\\O .\\O", "opinion_tags": "The\\O food\\O is\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "1586174:0", "sentence": "honestly the worst sushi my husband and i had in our entire lives .", "postag": ["RB", "DT", "JJS", "NN", "PRP$", "NN", "CC", "PRP", "VBD", "IN", "PRP$", "JJ", "NNS", "."], "head": [4, 4, 4, 0, 6, 9, 8, 6, 4, 13, 13, 13, 9, 4], "deprel": ["advmod", "det", "amod", "root", "nmod:poss", "nsubj", "cc", "conj", "acl:relcl", "case", "nmod:poss", "amod", "obl", "punct"], "triples": [{"uid": "1586174:0-0", "target_tags": "honestly\\O the\\O worst\\O sushi\\B my\\O husband\\O and\\O i\\O had\\O in\\O our\\O entire\\O lives\\O .\\O", "opinion_tags": "honestly\\O the\\O worst\\B sushi\\O my\\O husband\\O and\\O i\\O had\\O in\\O our\\O entire\\O lives\\O .\\O", "sentiment": "negative"}]}, {"id": "1145510:1", "sentence": "The pizza is yummy and I like the atmoshpere .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "PRP", "VBP", "DT", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 9, 7, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "nsubj", "conj", "det", "obj", "punct"], "triples": [{"uid": "1145510:1-0", "target_tags": "The\\O pizza\\B is\\O yummy\\O and\\O I\\O like\\O the\\O atmoshpere\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O yummy\\B and\\O I\\O like\\O the\\O atmoshpere\\O .\\O", "sentiment": "positive"}, {"uid": "1145510:1-1", "target_tags": "The\\O pizza\\O is\\O yummy\\O and\\O I\\O like\\O the\\O atmoshpere\\B .\\O", "opinion_tags": "The\\O pizza\\O is\\O yummy\\O and\\O I\\O like\\B the\\O atmoshpere\\O .\\O", "sentiment": "positive"}]}, {"id": "527109:4", "sentence": "Still , any quibbles about the bill were off-set by the pour-your-own measures of liquers which were courtesey of the house ...", "postag": ["RB", ",", "DT", "NNS", "IN", "DT", "NN", "VBD", "VBN", "IN", "DT", "JJ", "NNS", "IN", "NNS", "WDT", "VBD", "NN", "IN", "DT", "NN", "."], "head": [9, 1, 4, 9, 7, 7, 4, 9, 0, 13, 13, 13, 9, 15, 13, 18, 18, 13, 21, 21, 18, 9], "deprel": ["advmod", "punct", "det", "nsubj:pass", "case", "det", "nmod", "aux:pass", "root", "case", "det", "amod", "obl", "case", "nmod", "nsubj", "cop", "acl:relcl", "case", "det", "nmod", "punct"], "triples": [{"uid": "527109:4-0", "target_tags": "Still\\O ,\\O any\\O quibbles\\O about\\O the\\O bill\\O were\\O off-set\\O by\\O the\\O pour-your-own\\O measures\\B of\\I liquers\\I which\\O were\\O courtesey\\O of\\O the\\O house\\O ...\\O", "opinion_tags": "Still\\O ,\\O any\\O quibbles\\O about\\O the\\O bill\\O were\\O off-set\\O by\\O the\\O pour-your-own\\B measures\\O of\\O liquers\\O which\\O were\\O courtesey\\B of\\O the\\O house\\O ...\\O", "sentiment": "positive"}]}, {"id": "1655504:4", "sentence": "Wine list is extensive without being over-priced .", "postag": ["NN", "NN", "VBZ", "JJ", "IN", "VBG", "JJ", "."], "head": [2, 4, 4, 0, 7, 7, 4, 4], "deprel": ["compound", "nsubj", "cop", "root", "mark", "cop", "advcl", "punct"], "triples": [{"uid": "1655504:4-0", "target_tags": "Wine\\B list\\I is\\O extensive\\O without\\O being\\O over-priced\\O .\\O", "opinion_tags": "Wine\\O list\\O is\\O extensive\\B without\\B being\\I over-priced\\I .\\O", "sentiment": "positive"}]}, {"id": "1032695:1", "sentence": "Everything is always cooked to perfection , the service is excellent , the decor cool and understated .", "postag": ["NN", "VBZ", "RB", "VBN", "IN", "NN", ",", "DT", "NN", "VBZ", "JJ", ",", "DT", "NN", "JJ", "CC", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 11, 9, 11, 11, 4, 15, 14, 15, 11, 17, 15, 4], "deprel": ["nsubj:pass", "aux:pass", "advmod", "root", "case", "obl", "punct", "det", "nsubj", "cop", "conj", "punct", "det", "nsubj", "conj", "cc", "conj", "punct"], "triples": [{"uid": "1032695:1-0", "target_tags": "Everything\\O is\\O always\\O cooked\\O to\\O perfection\\O ,\\O the\\O service\\B is\\O excellent\\O ,\\O the\\O decor\\O cool\\O and\\O understated\\O .\\O", "opinion_tags": "Everything\\O is\\O always\\O cooked\\O to\\O perfection\\O ,\\O the\\O service\\O is\\O excellent\\B ,\\O the\\O decor\\O cool\\O and\\O understated\\O .\\O", "sentiment": "positive"}, {"uid": "1032695:1-1", "target_tags": "Everything\\O is\\O always\\O cooked\\O to\\O perfection\\O ,\\O the\\O service\\O is\\O excellent\\O ,\\O the\\O decor\\B cool\\O and\\O understated\\O .\\O", "opinion_tags": "Everything\\O is\\O always\\O cooked\\O to\\O perfection\\O ,\\O the\\O service\\O is\\O excellent\\O ,\\O the\\O decor\\O cool\\B and\\O understated\\B .\\O", "sentiment": "positive"}]}, {"id": "512925:0", "sentence": "it 's a perfect place to have a amazing indian food .", "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "DT", "JJ", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 11, 11, 11, 7, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "csubj", "det", "amod", "amod", "obj", "punct"], "triples": [{"uid": "512925:0-0", "target_tags": "it\\O 's\\O a\\O perfect\\O place\\O to\\O have\\O a\\O amazing\\O indian\\B food\\I .\\O", "opinion_tags": "it\\O 's\\O a\\O perfect\\O place\\O to\\O have\\O a\\O amazing\\B indian\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "680345:1", "sentence": "I really recommend the very simple Unda ( Egg ) rolls .", "postag": ["PRP", "RB", "VBP", "DT", "RB", "JJ", "NN", "-LRB-", "NN", "-RRB-", "NNS", "."], "head": [3, 3, 0, 11, 6, 11, 11, 9, 11, 9, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "advmod", "amod", "compound", "punct", "compound", "punct", "obj", "punct"], "triples": [{"uid": "680345:1-0", "target_tags": "I\\O really\\O recommend\\O the\\O very\\O simple\\O Unda\\B (\\I Egg\\I )\\I rolls\\I .\\O", "opinion_tags": "I\\O really\\O recommend\\B the\\O very\\O simple\\B Unda\\O (\\O Egg\\O )\\O rolls\\O .\\O", "sentiment": "positive"}]}, {"id": "1387314:2", "sentence": "The filet mignon dish was superb !", "postag": ["DT", "NN", "NN", "NN", "VBD", "JJ", "."], "head": [4, 3, 4, 6, 6, 0, 6], "deprel": ["det", "compound", "compound", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "1387314:2-0", "target_tags": "The\\O filet\\B mignon\\I dish\\I was\\O superb\\O !\\O", "opinion_tags": "The\\O filet\\O mignon\\O dish\\O was\\O superb\\B !\\O", "sentiment": "positive"}]}, {"id": "FF#3:7", "sentence": "It was served with skin , over a bed of extremely undercooked spinach and mashed potatoes .", "postag": ["PRP", "VBD", "VBN", "IN", "NN", ",", "IN", "DT", "NN", "IN", "RB", "JJ", "NN", "CC", "VBN", "NNS", "."], "head": [3, 3, 0, 5, 3, 9, 9, 9, 3, 13, 12, 13, 9, 16, 16, 13, 3], "deprel": ["nsubj:pass", "aux:pass", "root", "case", "obl", "punct", "case", "det", "obl", "case", "advmod", "amod", "nmod", "cc", "amod", "conj", "punct"], "triples": [{"uid": "FF#3:7-0", "target_tags": "It\\O was\\O served\\O with\\O skin\\O ,\\O over\\O a\\O bed\\O of\\O extremely\\O undercooked\\O spinach\\B and\\O mashed\\O potatoes\\O .\\O", "opinion_tags": "It\\O was\\O served\\O with\\O skin\\O ,\\O over\\O a\\O bed\\O of\\O extremely\\O undercooked\\B spinach\\O and\\O mashed\\O potatoes\\O .\\O", "sentiment": "negative"}]}, {"id": "1119578:2", "sentence": "Yes , they use fancy ingredients , but even fancy ingredients do n't make for good pizza unless someone knows how to get the crust right .", "postag": ["UH", ",", "PRP", "VBP", "JJ", "NNS", ",", "CC", "RB", "JJ", "NNS", "VBP", "RB", "VB", "IN", "JJ", "NN", "IN", "NN", "VBZ", "WRB", "TO", "VB", "DT", "NN", "RB", "."], "head": [4, 4, 4, 0, 6, 4, 14, 14, 11, 11, 14, 14, 14, 4, 17, 17, 14, 20, 20, 14, 23, 23, 20, 25, 23, 23, 4], "deprel": ["discourse", "punct", "nsubj", "root", "amod", "obj", "punct", "cc", "advmod", "amod", "nsubj", "aux", "advmod", "conj", "case", "amod", "obl", "mark", "nsubj", "advcl", "mark", "mark", "ccomp", "det", "obj", "advmod", "punct"], "triples": [{"uid": "1119578:2-0", "target_tags": "Yes\\O ,\\O they\\O use\\O fancy\\O ingredients\\B ,\\O but\\O even\\O fancy\\O ingredients\\O do\\O n't\\O make\\O for\\O good\\O pizza\\O unless\\O someone\\O knows\\O how\\O to\\O get\\O the\\O crust\\O right\\O .\\O", "opinion_tags": "Yes\\O ,\\O they\\O use\\O fancy\\B ingredients\\O ,\\O but\\O even\\O fancy\\O ingredients\\O do\\O n't\\O make\\O for\\O good\\O pizza\\O unless\\O someone\\O knows\\O how\\O to\\O get\\O the\\O crust\\O right\\O .\\O", "sentiment": "positive"}, {"uid": "1119578:2-1", "target_tags": "Yes\\O ,\\O they\\O use\\O fancy\\O ingredients\\O ,\\O but\\O even\\O fancy\\O ingredients\\O do\\O n't\\O make\\O for\\O good\\O pizza\\B unless\\O someone\\O knows\\O how\\O to\\O get\\O the\\O crust\\O right\\O .\\O", "opinion_tags": "Yes\\O ,\\O they\\O use\\O fancy\\O ingredients\\O ,\\O but\\O even\\O fancy\\O ingredients\\O do\\O n't\\O make\\O for\\O good\\B pizza\\O unless\\O someone\\O knows\\O how\\O to\\O get\\O the\\O crust\\O right\\O .\\O", "sentiment": "negative"}]}, {"id": "1189674:4", "sentence": "Great wine selection , Gigondas is worth the price , and the house champagne is a great value .", "postag": ["JJ", "NN", "NN", ",", "NNP", "VBZ", "JJ", "DT", "NN", ",", "CC", "DT", "NN", "NN", "VBZ", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 3, 7, 7, 3, 9, 7, 18, 18, 14, 14, 18, 18, 18, 18, 3, 3], "deprel": ["amod", "compound", "root", "punct", "nsubj", "cop", "parataxis", "det", "obj", "punct", "cc", "det", "compound", "nsubj", "cop", "det", "amod", "conj", "punct"], "triples": [{"uid": "1189674:4-0", "target_tags": "Great\\O wine\\B selection\\I ,\\O Gigondas\\O is\\O worth\\O the\\O price\\O ,\\O and\\O the\\O house\\O champagne\\O is\\O a\\O great\\O value\\O .\\O", "opinion_tags": "Great\\B wine\\O selection\\O ,\\O Gigondas\\O is\\O worth\\O the\\O price\\O ,\\O and\\O the\\O house\\O champagne\\O is\\O a\\O great\\O value\\O .\\O", "sentiment": "positive"}, {"uid": "1189674:4-1", "target_tags": "Great\\O wine\\O selection\\O ,\\O Gigondas\\B is\\O worth\\O the\\O price\\O ,\\O and\\O the\\O house\\O champagne\\O is\\O a\\O great\\O value\\O .\\O", "opinion_tags": "Great\\O wine\\O selection\\O ,\\O Gigondas\\O is\\O worth\\B the\\I price\\I ,\\O and\\O the\\O house\\O champagne\\O is\\O a\\O great\\O value\\O .\\O", "sentiment": "positive"}, {"uid": "1189674:4-2", "target_tags": "Great\\O wine\\O selection\\O ,\\O Gigondas\\O is\\O worth\\O the\\O price\\O ,\\O and\\O the\\O house\\B champagne\\I is\\O a\\O great\\O value\\O .\\O", "opinion_tags": "Great\\O wine\\O selection\\O ,\\O Gigondas\\O is\\O worth\\O the\\O price\\O ,\\O and\\O the\\O house\\O champagne\\O is\\O a\\O great\\B value\\I .\\O", "sentiment": "positive"}]}, {"id": "1417496:3", "sentence": "The service was impeccable and unobtrusive -- the staff knows what they are there to do -- to know their menu , present your meal , and attend to your needs .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "JJ", ",", "DT", "NN", "VBZ", "WP", "PRP", "VBP", "RB", "TO", "VB", ",", "TO", "VB", "PRP$", "NN", ",", "VB", "PRP$", "NN", ",", "CC", "VB", "IN", "PRP$", "NNS", "."], "head": [2, 4, 4, 0, 6, 4, 4, 9, 10, 4, 14, 14, 14, 10, 16, 14, 4, 19, 10, 21, 19, 23, 19, 25, 23, 28, 28, 19, 31, 31, 28, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct", "det", "nsubj", "parataxis", "obj", "nsubj", "cop", "ccomp", "mark", "xcomp", "punct", "mark", "xcomp", "nmod:poss", "obj", "punct", "conj", "nmod:poss", "obj", "punct", "cc", "conj", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "1417496:3-0", "target_tags": "The\\O service\\B was\\O impeccable\\O and\\O unobtrusive\\O --\\O the\\O staff\\O knows\\O what\\O they\\O are\\O there\\O to\\O do\\O --\\O to\\O know\\O their\\O menu\\O ,\\O present\\O your\\O meal\\O ,\\O and\\O attend\\O to\\O your\\O needs\\O .\\O", "opinion_tags": "The\\O service\\O was\\O impeccable\\B and\\O unobtrusive\\B --\\O the\\O staff\\O knows\\O what\\O they\\O are\\O there\\O to\\O do\\O --\\O to\\O know\\O their\\O menu\\O ,\\O present\\O your\\O meal\\O ,\\O and\\O attend\\O to\\O your\\O needs\\O .\\O", "sentiment": "positive"}]}, {"id": "FF#6:12", "sentence": "Shame on this place for the horrible rude staff and non-existent customer service .", "postag": ["NN", "IN", "DT", "NN", "IN", "DT", "JJ", "JJ", "NN", "CC", "JJ", "NN", "NN", "."], "head": [0, 4, 4, 1, 9, 9, 9, 9, 1, 13, 13, 13, 9, 1], "deprel": ["root", "case", "det", "nmod", "case", "det", "amod", "amod", "nmod", "cc", "amod", "compound", "conj", "punct"], "triples": [{"uid": "FF#6:12-0", "target_tags": "Shame\\O on\\O this\\O place\\O for\\O the\\O horrible\\O rude\\O staff\\B and\\O non-existent\\O customer\\O service\\O .\\O", "opinion_tags": "Shame\\O on\\O this\\O place\\O for\\O the\\O horrible\\O rude\\B staff\\O and\\O non-existent\\O customer\\O service\\O .\\O", "sentiment": "negative"}, {"uid": "FF#6:12-1", "target_tags": "Shame\\O on\\O this\\O place\\O for\\O the\\O horrible\\O rude\\O staff\\O and\\O non-existent\\O customer\\B service\\I .\\O", "opinion_tags": "Shame\\O on\\O this\\O place\\O for\\O the\\O horrible\\O rude\\O staff\\O and\\O non-existent\\B customer\\O service\\O .\\O", "sentiment": "negative"}]}, {"id": "1573534:3", "sentence": "I am happy i did the food was awsome .", "postag": ["PRP", "VBP", "JJ", "PRP", "VBD", "DT", "NN", "VBD", "JJ", "."], "head": [3, 3, 0, 9, 9, 7, 9, 9, 3, 3], "deprel": ["nsubj", "cop", "root", "nsubj", "aux", "det", "nsubj", "cop", "ccomp", "punct"], "triples": [{"uid": "1573534:3-0", "target_tags": "I\\O am\\O happy\\O i\\O did\\O the\\O food\\B was\\O awsome\\O .\\O", "opinion_tags": "I\\O am\\O happy\\O i\\O did\\O the\\O food\\O was\\O awsome\\B .\\O", "sentiment": "positive"}]}, {"id": "1496205:4", "sentence": "The quantity is also very good , you will come out satisfied .", "postag": ["DT", "NN", "VBZ", "RB", "RB", "JJ", ",", "PRP", "MD", "VB", "RB", "JJ", "."], "head": [2, 6, 6, 6, 6, 0, 6, 10, 10, 6, 10, 10, 6], "deprel": ["det", "nsubj", "cop", "advmod", "advmod", "root", "punct", "nsubj", "aux", "parataxis", "advmod", "xcomp", "punct"], "triples": [{"uid": "1496205:4-0", "target_tags": "The\\O quantity\\B is\\O also\\O very\\O good\\O ,\\O you\\O will\\O come\\O out\\O satisfied\\O .\\O", "opinion_tags": "The\\O quantity\\O is\\O also\\O very\\O good\\B ,\\O you\\O will\\O come\\O out\\O satisfied\\B .\\O", "sentiment": "positive"}]}, {"id": "BFC#7:11", "sentence": "Considering you will spend at least $ 60 a head , I expect better service .", "postag": ["VBG", "PRP", "MD", "VB", "RB", "RBS", "$", "CD", "DT", "NN", ",", "PRP", "VBP", "JJR", "NN", "."], "head": [4, 4, 4, 13, 6, 7, 4, 7, 10, 7, 4, 13, 0, 15, 13, 13], "deprel": ["mark", "nsubj", "aux", "advcl", "case", "nmod", "obj", "nummod", "det", "nmod:npmod", "punct", "nsubj", "root", "amod", "obj", "punct"], "triples": [{"uid": "BFC#7:11-0", "target_tags": "Considering\\O you\\O will\\O spend\\O at\\O least\\O $\\O 60\\O a\\O head\\O ,\\O I\\O expect\\O better\\O service\\B .\\O", "opinion_tags": "Considering\\O you\\O will\\O spend\\O at\\O least\\O $\\O 60\\O a\\O head\\O ,\\O I\\O expect\\B better\\I service\\O .\\O", "sentiment": "negative"}]}, {"id": "1138996:4", "sentence": "When we sat , we got great and fast service .", "postag": ["WRB", "PRP", "VBD", ",", "PRP", "VBD", "JJ", "CC", "JJ", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 10, 9, 7, 6, 6], "deprel": ["mark", "nsubj", "advcl", "punct", "nsubj", "root", "amod", "cc", "conj", "obj", "punct"], "triples": [{"uid": "1138996:4-0", "target_tags": "When\\O we\\O sat\\O ,\\O we\\O got\\O great\\O and\\O fast\\O service\\B .\\O", "opinion_tags": "When\\O we\\O sat\\O ,\\O we\\O got\\O great\\B and\\O fast\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "426805:1", "sentence": "Wonderful strawberry daiquiries as well !", "postag": ["JJ", "NN", "NNS", "RB", "RB", "."], "head": [3, 3, 0, 3, 4, 3], "deprel": ["amod", "compound", "root", "advmod", "fixed", "punct"], "triples": [{"uid": "426805:1-0", "target_tags": "Wonderful\\O strawberry\\B daiquiries\\I as\\O well\\O !\\O", "opinion_tags": "Wonderful\\B strawberry\\O daiquiries\\O as\\O well\\O !\\O", "sentiment": "positive"}]}, {"id": "ADLT#6:3", "sentence": "While finishing our meals which included a high-end bottle of wine , our son 's fiance joined us for a glass of wine and dessert .", "postag": ["IN", "VBG", "PRP$", "NNS", "WDT", "VBD", "DT", "JJ", "NN", "IN", "NN", ",", "PRP$", "NN", "POS", "NN", "VBD", "PRP", "IN", "DT", "NN", "IN", "NN", "CC", "NN", "."], "head": [2, 17, 4, 2, 6, 4, 9, 9, 6, 11, 9, 2, 14, 16, 14, 17, 0, 17, 21, 21, 17, 23, 21, 25, 23, 17], "deprel": ["mark", "advcl", "nmod:poss", "obj", "nsubj", "acl:relcl", "det", "amod", "obj", "case", "nmod", "punct", "nmod:poss", "nmod:poss", "case", "nsubj", "root", "obj", "case", "det", "obl", "case", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "ADLT#6:3-0", "target_tags": "While\\O finishing\\O our\\O meals\\O which\\O included\\O a\\O high-end\\O bottle\\B of\\I wine\\I ,\\O our\\O son\\O 's\\O fiance\\O joined\\O us\\O for\\O a\\O glass\\O of\\O wine\\O and\\O dessert\\O .\\O", "opinion_tags": "While\\O finishing\\O our\\O meals\\O which\\O included\\O a\\O high-end\\B bottle\\O of\\O wine\\O ,\\O our\\O son\\O 's\\O fiance\\O joined\\O us\\O for\\O a\\O glass\\O of\\O wine\\O and\\O dessert\\O .\\O", "sentiment": "positive"}]}, {"id": "1707741:1", "sentence": "The buffet had a nice selection .", "postag": ["DT", "NN", "VBD", "DT", "JJ", "NN", "."], "head": [2, 3, 0, 6, 6, 3, 3], "deprel": ["det", "nsubj", "root", "det", "amod", "obj", "punct"], "triples": [{"uid": "1707741:1-0", "target_tags": "The\\O buffet\\B had\\O a\\O nice\\O selection\\O .\\O", "opinion_tags": "The\\O buffet\\O had\\O a\\O nice\\B selection\\O .\\O", "sentiment": "positive"}]}, {"id": "1700205:3", "sentence": "I highly recommend <PERSON><PERSON><PERSON> to anyone who wants delicious top grade caviar and fantastic service .", "postag": ["PRP", "RB", "VBP", "NNP", "NNP", "IN", "NN", "WP", "VBZ", "JJ", "JJ", "NN", "JJ", "CC", "JJ", "NN", "."], "head": [3, 3, 0, 3, 4, 7, 3, 9, 7, 16, 12, 13, 16, 15, 13, 9, 3], "deprel": ["nsubj", "advmod", "root", "obj", "flat", "case", "obl", "nsubj", "acl:relcl", "amod", "amod", "compound", "amod", "cc", "conj", "obj", "punct"], "triples": [{"uid": "1700205:3-0", "target_tags": "I\\O highly\\O recommend\\O Caviar\\O Russe\\O to\\O anyone\\O who\\O wants\\O delicious\\O top\\O grade\\O caviar\\B and\\O fantastic\\O service\\O .\\O", "opinion_tags": "I\\O highly\\O recommend\\O Caviar\\O Russe\\O to\\O anyone\\O who\\O wants\\O delicious\\B top\\B grade\\I caviar\\O and\\O fantastic\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "1700205:3-1", "target_tags": "I\\O highly\\O recommend\\O Caviar\\O Russe\\O to\\O anyone\\O who\\O wants\\O delicious\\O top\\O grade\\O caviar\\O and\\O fantastic\\O service\\B .\\O", "opinion_tags": "I\\O highly\\O recommend\\O Caviar\\O Russe\\O to\\O anyone\\O who\\O wants\\O delicious\\O top\\O grade\\O caviar\\O and\\O fantastic\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "1470286:2", "sentence": "Great friendly service , Fast seating , Fast Delivery , Excellent sushi .", "postag": ["JJ", "JJ", "NN", ",", "JJ", "NN", ",", "JJ", "NN", ",", "JJ", "NN", "."], "head": [3, 3, 0, 3, 6, 3, 3, 9, 3, 3, 12, 3, 3], "deprel": ["amod", "amod", "root", "punct", "amod", "conj", "punct", "amod", "list", "punct", "amod", "list", "punct"], "triples": [{"uid": "1470286:2-0", "target_tags": "Great\\O friendly\\O service\\B ,\\O Fast\\O seating\\O ,\\O Fast\\O Delivery\\O ,\\O Excellent\\O sushi\\O .\\O", "opinion_tags": "Great\\B friendly\\B service\\O ,\\O Fast\\O seating\\O ,\\O Fast\\O Delivery\\O ,\\O Excellent\\O sushi\\O .\\O", "sentiment": "positive"}, {"uid": "1470286:2-1", "target_tags": "Great\\O friendly\\O service\\O ,\\O Fast\\O seating\\B ,\\O Fast\\O Delivery\\O ,\\O Excellent\\O sushi\\O .\\O", "opinion_tags": "Great\\O friendly\\O service\\O ,\\O Fast\\B seating\\O ,\\O Fast\\O Delivery\\O ,\\O Excellent\\O sushi\\O .\\O", "sentiment": "positive"}, {"uid": "1470286:2-2", "target_tags": "Great\\O friendly\\O service\\O ,\\O Fast\\O seating\\O ,\\O Fast\\O Delivery\\B ,\\O Excellent\\O sushi\\O .\\O", "opinion_tags": "Great\\O friendly\\O service\\O ,\\O Fast\\O seating\\O ,\\O Fast\\B Delivery\\O ,\\O Excellent\\O sushi\\O .\\O", "sentiment": "positive"}, {"uid": "1470286:2-3", "target_tags": "Great\\O friendly\\O service\\O ,\\O Fast\\O seating\\O ,\\O Fast\\O Delivery\\O ,\\O Excellent\\O sushi\\B .\\O", "opinion_tags": "Great\\O friendly\\O service\\O ,\\O Fast\\O seating\\O ,\\O Fast\\O Delivery\\O ,\\O Excellent\\B sushi\\O .\\O", "sentiment": "positive"}]}, {"id": "490565:2", "sentence": "The food is yummy , especially their cooked-to-perfection mussels in spicy tomato sauce and their shoestring crispy fries .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "RB", "PRP$", "NN", "NNS", "IN", "JJ", "NN", "NN", "CC", "PRP$", "NN", "JJ", "NNS", "."], "head": [2, 4, 4, 0, 9, 9, 9, 9, 4, 13, 13, 13, 9, 18, 18, 18, 18, 13, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "advmod", "nmod:poss", "compound", "parataxis", "case", "amod", "compound", "nmod", "cc", "nmod:poss", "compound", "amod", "conj", "punct"], "triples": [{"uid": "490565:2-0", "target_tags": "The\\O food\\B is\\O yummy\\O ,\\O especially\\O their\\O cooked-to-perfection\\O mussels\\O in\\O spicy\\O tomato\\O sauce\\O and\\O their\\O shoestring\\O crispy\\O fries\\O .\\O", "opinion_tags": "The\\O food\\O is\\O yummy\\B ,\\O especially\\O their\\O cooked-to-perfection\\O mussels\\O in\\O spicy\\O tomato\\O sauce\\O and\\O their\\O shoestring\\O crispy\\O fries\\O .\\O", "sentiment": "positive"}, {"uid": "490565:2-1", "target_tags": "The\\O food\\O is\\O yummy\\O ,\\O especially\\O their\\O cooked-to-perfection\\O mussels\\B in\\I spicy\\I tomato\\I sauce\\I and\\O their\\O shoestring\\O crispy\\O fries\\O .\\O", "opinion_tags": "The\\O food\\O is\\O yummy\\B ,\\O especially\\O their\\O cooked-to-perfection\\B mussels\\O in\\O spicy\\O tomato\\O sauce\\O and\\O their\\O shoestring\\O crispy\\O fries\\O .\\O", "sentiment": "positive"}, {"uid": "490565:2-2", "target_tags": "The\\O food\\O is\\O yummy\\O ,\\O especially\\O their\\O cooked-to-perfection\\O mussels\\O in\\O spicy\\O tomato\\O sauce\\O and\\O their\\O shoestring\\O crispy\\O fries\\B .\\O", "opinion_tags": "The\\O food\\O is\\O yummy\\B ,\\O especially\\O their\\O cooked-to-perfection\\O mussels\\O in\\O spicy\\O tomato\\O sauce\\O and\\O their\\O shoestring\\O crispy\\B fries\\O .\\O", "sentiment": "positive"}]}, {"id": "CLF#5:6", "sentence": "I never write on these sites but this restaurant is def worth commending !", "postag": ["PRP", "RB", "VBP", "IN", "DT", "NNS", "CC", "DT", "NN", "VBZ", "RB", "JJ", "VBG", "."], "head": [3, 3, 0, 6, 6, 3, 12, 9, 12, 12, 12, 3, 12, 3], "deprel": ["nsubj", "advmod", "root", "case", "det", "obl", "cc", "det", "nsubj", "cop", "advmod", "conj", "xcomp", "punct"], "triples": [{"uid": "CLF#5:6-0", "target_tags": "I\\O never\\O write\\O on\\O these\\O sites\\O but\\O this\\O restaurant\\B is\\O def\\O worth\\O commending\\O !\\O", "opinion_tags": "I\\O never\\O write\\O on\\O these\\O sites\\O but\\O this\\O restaurant\\O is\\O def\\O worth\\B commending\\O !\\O", "sentiment": "positive"}]}, {"id": "P#9:8", "sentence": "Also , I personally was n't a fan of the portobello and asparagus mole .", "postag": ["RB", ",", "PRP", "RB", "VBD", "RB", "DT", "NN", "IN", "DT", "NN", "CC", "NN", "NN", "."], "head": [8, 8, 8, 8, 8, 8, 8, 0, 11, 11, 8, 14, 14, 11, 8], "deprel": ["advmod", "punct", "nsubj", "advmod", "cop", "advmod", "det", "root", "case", "det", "nmod", "cc", "compound", "conj", "punct"], "triples": [{"uid": "P#9:8-0", "target_tags": "Also\\O ,\\O I\\O personally\\O was\\O n't\\O a\\O fan\\O of\\O the\\O portobello\\B and\\I asparagus\\I mole\\I .\\O", "opinion_tags": "Also\\O ,\\O I\\O personally\\O was\\O n't\\O a\\O fan\\B of\\O the\\O portobello\\O and\\O asparagus\\O mole\\O .\\O", "sentiment": "negative"}]}, {"id": "1410878:1", "sentence": "I 've never had bad service and the fish is fresh and delicious .", "postag": ["PRP", "VBP", "RB", "VBN", "JJ", "NN", "CC", "DT", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 11, 9, 11, 11, 4, 13, 11, 4], "deprel": ["nsubj", "aux", "advmod", "root", "amod", "obj", "cc", "det", "nsubj", "cop", "conj", "cc", "conj", "punct"], "triples": [{"uid": "1410878:1-0", "target_tags": "I\\O 've\\O never\\O had\\O bad\\O service\\B and\\O the\\O fish\\O is\\O fresh\\O and\\O delicious\\O .\\O", "opinion_tags": "I\\O 've\\O never\\B had\\I bad\\I service\\O and\\O the\\O fish\\O is\\O fresh\\O and\\O delicious\\O .\\O", "sentiment": "positive"}, {"uid": "1410878:1-1", "target_tags": "I\\O 've\\O never\\O had\\O bad\\O service\\O and\\O the\\O fish\\B is\\O fresh\\O and\\O delicious\\O .\\O", "opinion_tags": "I\\O 've\\O never\\O had\\O bad\\O service\\O and\\O the\\O fish\\O is\\O fresh\\B and\\O delicious\\B .\\O", "sentiment": "positive"}]}, {"id": "1710746:0", "sentence": "I got an excellent piece of cheesecake and we had several other nice pastries .", "postag": ["PRP", "VBD", "DT", "JJ", "NN", "IN", "NN", "CC", "PRP", "VBD", "JJ", "JJ", "JJ", "NNS", "."], "head": [2, 0, 5, 5, 2, 7, 5, 10, 10, 2, 14, 14, 14, 10, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "case", "nmod", "cc", "nsubj", "conj", "amod", "amod", "amod", "obj", "punct"], "triples": [{"uid": "1710746:0-0", "target_tags": "I\\O got\\O an\\O excellent\\O piece\\O of\\O cheesecake\\B and\\O we\\O had\\O several\\O other\\O nice\\O pastries\\O .\\O", "opinion_tags": "I\\O got\\O an\\O excellent\\B piece\\O of\\O cheesecake\\O and\\O we\\O had\\O several\\O other\\O nice\\O pastries\\O .\\O", "sentiment": "positive"}, {"uid": "1710746:0-1", "target_tags": "I\\O got\\O an\\O excellent\\O piece\\O of\\O cheesecake\\O and\\O we\\O had\\O several\\O other\\O nice\\O pastries\\B .\\O", "opinion_tags": "I\\O got\\O an\\O excellent\\O piece\\O of\\O cheesecake\\O and\\O we\\O had\\O several\\O other\\O nice\\B pastries\\O .\\O", "sentiment": "positive"}]}, {"id": "1496205:3", "sentence": "Try green curry with vegetables .", "postag": ["VB", "JJ", "NN", "IN", "NNS", "."], "head": [0, 3, 1, 5, 3, 1], "deprel": ["root", "amod", "obj", "case", "nmod", "punct"], "triples": [{"uid": "1496205:3-0", "target_tags": "Try\\O green\\B curry\\I with\\I vegetables\\I .\\O", "opinion_tags": "Try\\B green\\O curry\\O with\\O vegetables\\O .\\O", "sentiment": "positive"}]}, {"id": "TFS#2:1", "sentence": "The food is great and the environment is even better .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "RB", "JJR", "."], "head": [2, 4, 4, 0, 10, 7, 10, 10, 10, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "TFS#2:1-0", "target_tags": "The\\O food\\B is\\O great\\O and\\O the\\O environment\\O is\\O even\\O better\\O .\\O", "opinion_tags": "The\\O food\\O is\\O great\\B and\\O the\\O environment\\O is\\O even\\O better\\O .\\O", "sentiment": "positive"}, {"uid": "TFS#2:1-1", "target_tags": "The\\O food\\O is\\O great\\O and\\O the\\O environment\\B is\\O even\\O better\\O .\\O", "opinion_tags": "The\\O food\\O is\\O great\\O and\\O the\\O environment\\O is\\O even\\O better\\B .\\O", "sentiment": "positive"}]}, {"id": "1615322:4", "sentence": "The seats are uncomfortable if you are sitting against the wall on wooden benches .", "postag": ["DT", "NNS", "VBP", "JJ", "IN", "PRP", "VBP", "VBG", "IN", "DT", "NN", "IN", "JJ", "NNS", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 11, 11, 8, 14, 14, 8, 4], "deprel": ["det", "nsubj", "cop", "root", "mark", "nsubj", "aux", "advcl", "case", "det", "obl", "case", "amod", "obl", "punct"], "triples": [{"uid": "1615322:4-0", "target_tags": "The\\O seats\\B are\\O uncomfortable\\O if\\O you\\O are\\O sitting\\O against\\O the\\O wall\\O on\\O wooden\\O benches\\O .\\O", "opinion_tags": "The\\O seats\\O are\\O uncomfortable\\B if\\O you\\O are\\O sitting\\O against\\O the\\O wall\\O on\\O wooden\\O benches\\O .\\O", "sentiment": "negative"}]}, {"id": "875139:1", "sentence": "The pizza was pretty good and huge .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"], "triples": [{"uid": "875139:1-0", "target_tags": "The\\O pizza\\B was\\O pretty\\O good\\O and\\O huge\\O .\\O", "opinion_tags": "The\\O pizza\\O was\\O pretty\\O good\\B and\\O huge\\B .\\O", "sentiment": "positive"}]}, {"id": "496132:1", "sentence": "The setting is casual and romantic .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "496132:1-0", "target_tags": "The\\O setting\\B is\\O casual\\O and\\O romantic\\O .\\O", "opinion_tags": "The\\O setting\\O is\\O casual\\B and\\O romantic\\B .\\O", "sentiment": "positive"}]}, {"id": "FF#10:0", "sentence": "bad staff", "postag": ["JJ", "NN"], "head": [2, 0], "deprel": ["amod", "root"], "triples": [{"uid": "FF#10:0-0", "target_tags": "bad\\O staff\\B", "opinion_tags": "bad\\B staff\\O", "sentiment": "negative"}]}, {"id": "CLF#4:1", "sentence": "I 'll being with a couple of positives : cool decor , good pita and hummus , and grilled octopus that was actually pretty tasty .", "postag": ["PRP", "MD", "VBG", "IN", "DT", "NN", "IN", "NNS", ":", "JJ", "NN", ",", "JJ", "NN", "CC", "NN", ",", "CC", "JJ", "NN", "WDT", "VBD", "RB", "RB", "JJ", "."], "head": [3, 3, 0, 6, 6, 3, 8, 6, 11, 11, 6, 14, 14, 11, 16, 11, 20, 20, 20, 11, 25, 25, 25, 25, 20, 3], "deprel": ["nsubj", "aux", "root", "case", "det", "obl", "case", "nmod", "punct", "amod", "appos", "punct", "amod", "conj", "cc", "conj", "punct", "cc", "amod", "conj", "nsubj", "cop", "advmod", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "CLF#4:1-0", "target_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\B ,\\O good\\O pita\\O and\\O hummus\\O ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "opinion_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\B decor\\O ,\\O good\\O pita\\O and\\O hummus\\O ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "sentiment": "positive"}, {"uid": "CLF#4:1-1", "target_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\O ,\\O good\\O pita\\B and\\O hummus\\O ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "opinion_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\O ,\\O good\\B pita\\O and\\O hummus\\O ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "sentiment": "positive"}, {"uid": "CLF#4:1-2", "target_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\O ,\\O good\\O pita\\O and\\O hummus\\B ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "opinion_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\O ,\\O good\\B pita\\O and\\O hummus\\O ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "sentiment": "positive"}, {"uid": "CLF#4:1-3", "target_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\O ,\\O good\\O pita\\O and\\O hummus\\O ,\\O and\\O grilled\\B octopus\\I that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "opinion_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\O ,\\O good\\O pita\\O and\\O hummus\\O ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\B .\\O", "sentiment": "positive"}]}, {"id": "CLF#3:22", "sentence": "My chicken was inedible as there were so many fatty lumps which i had to keep spitting out into my napkin .", "postag": ["PRP$", "NN", "VBD", "JJ", "IN", "EX", "VBD", "RB", "JJ", "NN", "NNS", "WDT", "PRP", "VBD", "TO", "VB", "VBG", "RP", "IN", "PRP$", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 9, 11, 11, 7, 14, 14, 11, 16, 14, 16, 17, 21, 21, 17, 4], "deprel": ["nmod:poss", "nsubj", "cop", "root", "mark", "expl", "advcl", "advmod", "amod", "compound", "nsubj", "obj", "nsubj", "acl:relcl", "mark", "xcomp", "xcomp", "compound:prt", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "CLF#3:22-0", "target_tags": "My\\O chicken\\B was\\O inedible\\O as\\O there\\O were\\O so\\O many\\O fatty\\O lumps\\O which\\O i\\O had\\O to\\O keep\\O spitting\\O out\\O into\\O my\\O napkin\\O .\\O", "opinion_tags": "My\\O chicken\\O was\\O inedible\\B as\\O there\\O were\\O so\\O many\\O fatty\\O lumps\\O which\\O i\\O had\\O to\\O keep\\O spitting\\O out\\O into\\O my\\O napkin\\O .\\O", "sentiment": "negative"}]}, {"id": "BFC#9:17", "sentence": "I had the Thai style Fried Sea Bass ... which was very good .", "postag": ["PRP", "VBD", "DT", "JJ", "NN", "VBN", "NNP", "NNP", ",", "WDT", "VBD", "RB", "JJ", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 8, 13, 13, 13, 8, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "amod", "compound", "obj", "punct", "nsubj", "cop", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "BFC#9:17-0", "target_tags": "I\\O had\\O the\\O Thai\\B style\\I Fried\\I Sea\\I Bass\\I ...\\O which\\O was\\O very\\O good\\O .\\O", "opinion_tags": "I\\O had\\O the\\O Thai\\O style\\O Fried\\O Sea\\O Bass\\O ...\\O which\\O was\\O very\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "PP#4:2", "sentence": "The service was quick and friendly .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "PP#4:2-0", "target_tags": "The\\O service\\B was\\O quick\\O and\\O friendly\\O .\\O", "opinion_tags": "The\\O service\\O was\\O quick\\B and\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "Y#6:5", "sentence": "The Dancing , White River and Millenium rolls are musts .", "postag": ["DT", "NN", ",", "NNP", "NNP", "CC", "NNP", "NNS", "VBP", "NNS", "."], "head": [2, 10, 5, 5, 2, 8, 8, 2, 10, 0, 10], "deprel": ["det", "nsubj", "punct", "compound", "conj", "cc", "compound", "conj", "cop", "root", "punct"], "triples": [{"uid": "Y#6:5-0", "target_tags": "The\\O Dancing\\B ,\\I White\\I River\\I and\\I Millenium\\I rolls\\I are\\O musts\\O .\\O", "opinion_tags": "The\\O Dancing\\O ,\\O White\\O River\\O and\\O Millenium\\O rolls\\O are\\O musts\\B .\\O", "sentiment": "positive"}]}, {"id": "BzG#2:1", "sentence": "The food is flavorful , plentiful and reasonably priced .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "JJ", "CC", "RB", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "conj", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "BzG#2:1-0", "target_tags": "The\\O food\\B is\\O flavorful\\O ,\\O plentiful\\O and\\O reasonably\\O priced\\O .\\O", "opinion_tags": "The\\O food\\O is\\O flavorful\\B ,\\O plentiful\\B and\\O reasonably\\B priced\\I .\\O", "sentiment": "positive"}]}, {"id": "Y#9:3", "sentence": "One special roll and one regular roll is enough to fill you up , but save room for dessert !", "postag": ["CD", "JJ", "NN", "CC", "CD", "JJ", "NN", "VBZ", "JJ", "TO", "VB", "PRP", "RP", ",", "CC", "VB", "NN", "IN", "NN", "."], "head": [3, 3, 9, 7, 7, 7, 3, 9, 0, 11, 9, 11, 11, 16, 16, 11, 16, 19, 16, 9], "deprel": ["nummod", "amod", "nsubj", "cc", "nummod", "amod", "conj", "cop", "root", "mark", "xcomp", "obj", "compound:prt", "punct", "cc", "conj", "obj", "case", "obl", "punct"], "triples": [{"uid": "Y#9:3-0", "target_tags": "One\\O special\\O roll\\O and\\O one\\O regular\\O roll\\O is\\O enough\\O to\\O fill\\O you\\O up\\O ,\\O but\\O save\\O room\\O for\\O dessert\\B !\\O", "opinion_tags": "One\\O special\\O roll\\O and\\O one\\O regular\\O roll\\O is\\O enough\\O to\\O fill\\O you\\O up\\O ,\\O but\\O save\\B room\\I for\\O dessert\\O !\\O", "sentiment": "positive"}, {"uid": "Y#9:3-1", "target_tags": "One\\O special\\B roll\\I and\\O one\\O regular\\O roll\\O is\\O enough\\O to\\O fill\\O you\\O up\\O ,\\O but\\O save\\O room\\O for\\O dessert\\O !\\O", "opinion_tags": "One\\O special\\O roll\\O and\\O one\\O regular\\O roll\\O is\\O enough\\B to\\O fill\\O you\\O up\\O ,\\O but\\O save\\O room\\O for\\O dessert\\O !\\O", "sentiment": "positive"}, {"uid": "Y#9:3-2", "target_tags": "One\\O special\\O roll\\O and\\O one\\O regular\\B roll\\I is\\O enough\\O to\\O fill\\O you\\O up\\O ,\\O but\\O save\\O room\\O for\\O dessert\\O !\\O", "opinion_tags": "One\\O special\\O roll\\O and\\O one\\O regular\\O roll\\O is\\O enough\\B to\\O fill\\O you\\O up\\O ,\\O but\\O save\\O room\\O for\\O dessert\\O !\\O", "sentiment": "positive"}]}, {"id": "1655504:1", "sentence": "Cheese plate is a varied delight and great bargain at $ 10 .", "postag": ["NN", "NN", "VBZ", "DT", "JJ", "NN", "CC", "JJ", "NN", "IN", "$", "CD", "."], "head": [2, 6, 6, 6, 6, 0, 9, 9, 6, 11, 6, 11, 6], "deprel": ["compound", "nsubj", "cop", "det", "amod", "root", "cc", "amod", "conj", "case", "obl", "nummod", "punct"], "triples": [{"uid": "1655504:1-0", "target_tags": "Cheese\\B plate\\I is\\O a\\O varied\\O delight\\O and\\O great\\O bargain\\O at\\O $\\O 10\\O .\\O", "opinion_tags": "Cheese\\O plate\\O is\\O a\\O varied\\B delight\\I and\\O great\\B bargain\\I at\\O $\\O 10\\O .\\O", "sentiment": "positive"}]}, {"id": "558423:4", "sentence": "However , if you want great food at a great price and do n't mind the decor , you ca n't beat this place .", "postag": ["RB", ",", "IN", "PRP", "VBP", "JJ", "NN", "IN", "DT", "JJ", "NN", "CC", "VBP", "RB", "VB", "DT", "NN", ",", "PRP", "MD", "RB", "VB", "DT", "NN", "."], "head": [22, 22, 5, 5, 22, 7, 5, 11, 11, 11, 5, 15, 15, 15, 5, 17, 15, 22, 22, 22, 22, 0, 24, 22, 22], "deprel": ["advmod", "punct", "mark", "nsubj", "advcl", "amod", "obj", "case", "det", "amod", "obl", "cc", "aux", "advmod", "conj", "det", "obj", "punct", "nsubj", "aux", "advmod", "root", "det", "obj", "punct"], "triples": [{"uid": "558423:4-0", "target_tags": "However\\O ,\\O if\\O you\\O want\\O great\\O food\\B at\\O a\\O great\\O price\\O and\\O do\\O n't\\O mind\\O the\\O decor\\O ,\\O you\\O ca\\O n't\\O beat\\O this\\O place\\O .\\O", "opinion_tags": "However\\O ,\\O if\\O you\\O want\\O great\\B food\\O at\\O a\\O great\\O price\\O and\\O do\\O n't\\O mind\\O the\\O decor\\O ,\\O you\\O ca\\O n't\\O beat\\O this\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "509227:3", "sentence": "<PERSON> is a good restaurant , but it 's nothing special .", "postag": ["NNP", "VBZ", "DT", "JJ", "NN", ",", "CC", "PRP", "VBZ", "NN", "JJ", "."], "head": [5, 5, 5, 5, 0, 10, 10, 10, 10, 5, 10, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "punct", "cc", "nsubj", "cop", "conj", "amod", "punct"], "triples": [{"uid": "509227:3-0", "target_tags": "Rao\\B is\\O a\\O good\\O restaurant\\O ,\\O but\\O it\\O 's\\O nothing\\O special\\O .\\O", "opinion_tags": "Rao\\O is\\O a\\O good\\B restaurant\\O ,\\O but\\O it\\O 's\\O nothing\\O special\\O .\\O", "sentiment": "neutral"}]}, {"id": "1055910:4", "sentence": "Ambiance relaxed and stylish .", "postag": ["NN", "JJ", "CC", "JJ", "."], "head": [0, 1, 4, 2, 1], "deprel": ["root", "amod", "cc", "conj", "punct"], "triples": [{"uid": "1055910:4-0", "target_tags": "Ambiance\\B relaxed\\O and\\O stylish\\O .\\O", "opinion_tags": "Ambiance\\O relaxed\\B and\\O stylish\\B .\\O", "sentiment": "positive"}]}, {"id": "773720:2", "sentence": "Although they do the typical what kind of water would you like questions the service was good and overall very relaxing to place to eat .", "postag": ["IN", "PRP", "VBP", "DT", "JJ", "WDT", "NN", "IN", "NN", "MD", "PRP", "VB", "NNS", "DT", "NN", "VBD", "JJ", "CC", "RB", "RB", "JJ", "IN", "NN", "TO", "VB", "."], "head": [3, 3, 12, 5, 3, 7, 3, 9, 7, 12, 12, 0, 12, 15, 17, 17, 12, 21, 21, 21, 17, 23, 21, 25, 21, 12], "deprel": ["mark", "nsubj", "advcl", "det", "obj", "det", "obj", "case", "nmod", "aux", "nsubj", "root", "obj", "det", "nsubj", "cop", "ccomp", "cc", "advmod", "advmod", "conj", "case", "obl", "mark", "advcl", "punct"], "triples": [{"uid": "773720:2-0", "target_tags": "Although\\O they\\O do\\O the\\O typical\\O what\\O kind\\O of\\O water\\O would\\O you\\O like\\O questions\\O the\\O service\\B was\\O good\\O and\\O overall\\O very\\O relaxing\\O to\\O place\\O to\\O eat\\O .\\O", "opinion_tags": "Although\\O they\\O do\\O the\\O typical\\O what\\O kind\\O of\\O water\\O would\\O you\\O like\\O questions\\O the\\O service\\O was\\O good\\B and\\O overall\\O very\\O relaxing\\O to\\O place\\O to\\O eat\\O .\\O", "sentiment": "positive"}, {"uid": "773720:2-1", "target_tags": "Although\\O they\\O do\\O the\\O typical\\O what\\O kind\\O of\\O water\\O would\\O you\\O like\\O questions\\O the\\O service\\O was\\O good\\O and\\O overall\\O very\\O relaxing\\O to\\O place\\B to\\O eat\\O .\\O", "opinion_tags": "Although\\O they\\O do\\O the\\O typical\\O what\\O kind\\O of\\O water\\O would\\O you\\O like\\O questions\\O the\\O service\\O was\\O good\\O and\\O overall\\O very\\O relaxing\\B to\\O place\\O to\\O eat\\O .\\O", "sentiment": "positive"}]}]