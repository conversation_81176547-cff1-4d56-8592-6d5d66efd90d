[{"id": "en_BlueRibbonSushi_478218171:1", "sentence": "Serves really good sushi .", "postag": ["VBZ", "RB", "JJ", "NN", "."], "head": [0, 3, 4, 1, 1], "deprel": ["root", "advmod", "amod", "obj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218171:1-0", "target_tags": "Serves\\O really\\O good\\O sushi\\B .\\O", "opinion_tags": "Serves\\O really\\O good\\B sushi\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218171:2", "sentence": "Not the biggest portions but adequate .", "postag": ["RB", "DT", "JJS", "NNS", "CC", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 4], "deprel": ["advmod", "det", "amod", "root", "cc", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218171:2-0", "target_tags": "Not\\O the\\O biggest\\O portions\\B but\\O adequate\\O .\\O", "opinion_tags": "Not\\B the\\I biggest\\I portions\\O but\\O adequate\\B .\\O", "sentiment": "neutral"}]}, {"id": "en_BlueRibbonSushi_478218171:3", "sentence": "Green Tea creme brulee is a must !", "postag": ["JJ", "NN", "NN", "NN", "VBZ", "DT", "NN", "."], "head": [4, 3, 4, 7, 7, 7, 0, 7], "deprel": ["amod", "compound", "compound", "nsubj", "cop", "det", "root", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218171:3-0", "target_tags": "Green\\B Tea\\I creme\\I brulee\\I is\\O a\\O must\\O !\\O", "opinion_tags": "Green\\O Tea\\O creme\\O brulee\\O is\\O a\\O must\\B !\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218345:2", "sentence": "It has great sushi and even better service .", "postag": ["PRP", "VBZ", "JJ", "NN", "CC", "RB", "JJR", "NN", "."], "head": [2, 0, 4, 2, 8, 8, 8, 4, 2], "deprel": ["nsubj", "root", "amod", "obj", "cc", "advmod", "amod", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218345:2-0", "target_tags": "It\\O has\\O great\\O sushi\\B and\\O even\\O better\\O service\\O .\\O", "opinion_tags": "It\\O has\\O great\\B sushi\\O and\\O even\\O better\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218345:2-1", "target_tags": "It\\O has\\O great\\O sushi\\O and\\O even\\O better\\O service\\B .\\O", "opinion_tags": "It\\O has\\O great\\O sushi\\O and\\O even\\O better\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218345:3", "sentence": "The entire staff was extremely accomodating and tended to my every need .", "postag": ["DT", "JJ", "NN", "VBD", "RB", "JJ", "CC", "VBD", "IN", "PRP$", "DT", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 8, 6, 12, 12, 12, 8, 6], "deprel": ["det", "amod", "nsubj", "cop", "advmod", "root", "cc", "conj", "case", "nmod:poss", "det", "obl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218345:3-0", "target_tags": "The\\O entire\\O staff\\B was\\O extremely\\O accomodating\\O and\\O tended\\O to\\O my\\O every\\O need\\O .\\O", "opinion_tags": "The\\O entire\\O staff\\O was\\O extremely\\O accomodating\\B and\\O tended\\O to\\O my\\O every\\O need\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_477965690:2", "sentence": "The owner is belligerent to guests that have a complaint .", "postag": ["DT", "NN", "VBZ", "JJ", "IN", "NNS", "WDT", "VBP", "DT", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 8, 6, 10, 8, 4], "deprel": ["det", "nsubj", "cop", "root", "case", "obl", "nsubj", "acl:relcl", "det", "obj", "punct"], "triples": [{"uid": "en_SchoonerOrLater_477965690:2-0", "target_tags": "The\\O owner\\B is\\O belligerent\\O to\\O guests\\O that\\O have\\O a\\O complaint\\O .\\O", "opinion_tags": "The\\O owner\\O is\\O belligerent\\B to\\O guests\\O that\\O have\\O a\\O complaint\\O .\\O", "sentiment": "negative"}]}, {"id": "en_SchoonerOrLater_477965849:0", "sentence": "Good food !", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "en_SchoonerOrLater_477965849:0-0", "target_tags": "Good\\O food\\B !\\O", "opinion_tags": "Good\\B food\\O !\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_477965849:2", "sentence": "This is a great place to get a delicious meal .", "postag": ["DT", "VBZ", "DT", "JJ", "NN", "TO", "VB", "DT", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 10, 10, 7, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "det", "amod", "obj", "punct"], "triples": [{"uid": "en_SchoonerOrLater_477965849:2-0", "target_tags": "This\\O is\\O a\\O great\\O place\\O to\\O get\\O a\\O delicious\\O meal\\B .\\O", "opinion_tags": "This\\O is\\O a\\O great\\O place\\O to\\O get\\O a\\O delicious\\B meal\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_477965849:4", "sentence": "The staff is pretty friendly .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "en_SchoonerOrLater_477965849:4-0", "target_tags": "The\\O staff\\B is\\O pretty\\O friendly\\O .\\O", "opinion_tags": "The\\O staff\\O is\\O pretty\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_477965849:5", "sentence": "The onion rings are great !", "postag": ["DT", "NN", "NNS", "VBP", "JJ", "."], "head": [3, 3, 5, 5, 0, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_SchoonerOrLater_477965849:5-0", "target_tags": "The\\O onion\\B rings\\I are\\O great\\O !\\O", "opinion_tags": "The\\O onion\\O rings\\O are\\O great\\B !\\O", "sentiment": "positive"}]}, {"id": "en_PagodaRestaurant_478006817:2", "sentence": "The lemon chicken tasted like sticky sweet donuts and the honey walnut prawns , the few they actually give you ... ..were not good .", "postag": ["DT", "NN", "NN", "VBD", "IN", "JJ", "JJ", "NNS", "CC", "DT", "NN", "NN", "NNS", ",", "DT", "JJ", "PRP", "RB", "VBP", "PRP", ",", "CC", "RB", "JJ", "."], "head": [3, 3, 4, 0, 8, 8, 8, 4, 13, 13, 13, 13, 8, 19, 16, 19, 19, 19, 4, 19, 4, 24, 24, 4, 4], "deprel": ["det", "compound", "nsubj", "root", "case", "amod", "amod", "obl", "cc", "det", "compound", "compound", "conj", "punct", "det", "nsubj", "nsubj", "advmod", "parataxis", "obj", "punct", "punct", "advmod", "parataxis", "punct"], "triples": [{"uid": "en_PagodaRestaurant_478006817:2-0", "target_tags": "The\\O lemon\\B chicken\\I tasted\\O like\\O sticky\\O sweet\\O donuts\\O and\\O the\\O honey\\O walnut\\O prawns\\O ,\\O the\\O few\\O they\\O actually\\O give\\O you\\O ...\\O ..were\\O not\\O good\\O .\\O", "opinion_tags": "The\\O lemon\\O chicken\\O tasted\\O like\\O sticky\\B sweet\\B donuts\\O and\\O the\\O honey\\O walnut\\O prawns\\O ,\\O the\\O few\\O they\\O actually\\O give\\O you\\O ...\\O ..were\\O not\\O good\\O .\\O", "sentiment": "negative"}, {"uid": "en_PagodaRestaurant_478006817:2-1", "target_tags": "The\\O lemon\\O chicken\\O tasted\\O like\\O sticky\\O sweet\\O donuts\\O and\\O the\\O honey\\B walnut\\I prawns\\I ,\\O the\\O few\\O they\\O actually\\O give\\O you\\O ...\\O ..were\\O not\\O good\\O .\\O", "opinion_tags": "The\\O lemon\\O chicken\\O tasted\\O like\\O sticky\\O sweet\\O donuts\\O and\\O the\\O honey\\O walnut\\O prawns\\O ,\\O the\\O few\\O they\\O actually\\O give\\O you\\O ...\\O ..were\\O not\\B good\\I .\\O", "sentiment": "negative"}]}, {"id": "en_PagodaRestaurant_478006817:4", "sentence": "Nice ambience , but highly overrated place .", "postag": ["JJ", "NN", ",", "CC", "RB", "JJ", "NN", "."], "head": [2, 0, 7, 7, 6, 7, 2, 2], "deprel": ["amod", "root", "punct", "cc", "advmod", "amod", "conj", "punct"], "triples": [{"uid": "en_PagodaRestaurant_478006817:4-0", "target_tags": "Nice\\O ambience\\B ,\\O but\\O highly\\O overrated\\O place\\O .\\O", "opinion_tags": "Nice\\B ambience\\O ,\\O but\\O highly\\O overrated\\O place\\O .\\O", "sentiment": "positive"}, {"uid": "en_PagodaRestaurant_478006817:4-1", "target_tags": "Nice\\O ambience\\O ,\\O but\\O highly\\O overrated\\O place\\B .\\O", "opinion_tags": "Nice\\O ambience\\O ,\\O but\\O highly\\O overrated\\B place\\O .\\O", "sentiment": "negative"}]}, {"id": "en_ParkChaletGardenRestaurant_477778282:0", "sentence": "Worst Service I Ever Had", "postag": ["JJS", "NN", "PRP", "RB", "VBD"], "head": [2, 0, 5, 5, 2], "deprel": ["amod", "root", "nsubj", "advmod", "acl:relcl"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778282:0-0", "target_tags": "Worst\\O Service\\B I\\O Ever\\O Had\\O", "opinion_tags": "Worst\\B Service\\O I\\O Ever\\O Had\\O", "sentiment": "positive"}]}, {"id": "en_ParkChaletGardenRestaurant_477778282:2", "sentence": "Everyone that sat in the back outside agreed that it was the worst service we had ever received .", "postag": ["NN", "WDT", "VBD", "IN", "DT", "NN", "RB", "VBD", "IN", "PRP", "VBD", "DT", "JJS", "NN", "PRP", "VBD", "RB", "VBN", "."], "head": [8, 3, 1, 6, 6, 3, 6, 0, 14, 14, 14, 14, 14, 8, 18, 18, 18, 14, 8], "deprel": ["nsubj", "nsubj", "acl:relcl", "case", "det", "obl", "advmod", "root", "mark", "nsubj", "cop", "det", "amod", "ccomp", "nsubj", "aux", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778282:2-0", "target_tags": "Everyone\\O that\\O sat\\O in\\O the\\O back\\O outside\\O agreed\\O that\\O it\\O was\\O the\\O worst\\O service\\B we\\O had\\O ever\\O received\\O .\\O", "opinion_tags": "Everyone\\O that\\O sat\\O in\\O the\\O back\\O outside\\O agreed\\O that\\O it\\O was\\O the\\O worst\\B service\\O we\\O had\\O ever\\O received\\O .\\O", "sentiment": "negative"}]}, {"id": "en_ParkChaletGardenRestaurant_477778282:3", "sentence": "Our waiter was non-existent and after our food finally arrived over an hour after we ordered , we were not given any water or utensils .", "postag": ["PRP$", "NN", "VBD", "JJ", "CC", "IN", "PRP$", "NN", "RB", "VBD", "IN", "DT", "NN", "IN", "PRP", "VBD", ",", "PRP", "VBD", "RB", "VBN", "DT", "NN", "CC", "NNS", "."], "head": [2, 4, 4, 0, 21, 10, 8, 10, 10, 21, 13, 13, 10, 16, 16, 10, 21, 21, 21, 21, 4, 23, 21, 25, 23, 4], "deprel": ["nmod:poss", "nsubj", "cop", "root", "cc", "mark", "nmod:poss", "nsubj", "advmod", "advcl", "case", "det", "obl", "mark", "nsubj", "advcl", "punct", "nsubj:pass", "aux:pass", "advmod", "conj", "det", "obj", "cc", "conj", "punct"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778282:3-0", "target_tags": "Our\\O waiter\\B was\\O non-existent\\O and\\O after\\O our\\O food\\O finally\\O arrived\\O over\\O an\\O hour\\O after\\O we\\O ordered\\O ,\\O we\\O were\\O not\\O given\\O any\\O water\\O or\\O utensils\\O .\\O", "opinion_tags": "Our\\O waiter\\O was\\O non-existent\\B and\\O after\\O our\\O food\\O finally\\O arrived\\O over\\O an\\O hour\\O after\\O we\\O ordered\\O ,\\O we\\O were\\O not\\O given\\O any\\O water\\O or\\O utensils\\O .\\O", "sentiment": "negative"}]}, {"id": "en_ParkChaletGardenRestaurant_477778282:4", "sentence": "I complained to the manager , but he was not even apologetic .", "postag": ["PRP", "VBD", "IN", "DT", "NN", ",", "CC", "PRP", "VBD", "RB", "RB", "JJ", "."], "head": [2, 0, 5, 5, 2, 12, 12, 12, 12, 12, 12, 2, 2], "deprel": ["nsubj", "root", "case", "det", "obl", "punct", "cc", "nsubj", "cop", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778282:4-0", "target_tags": "I\\O complained\\O to\\O the\\O manager\\B ,\\O but\\O he\\O was\\O not\\O even\\O apologetic\\O .\\O", "opinion_tags": "I\\O complained\\O to\\O the\\O manager\\O ,\\O but\\O he\\O was\\O not\\B even\\I apologetic\\I .\\O", "sentiment": "negative"}]}, {"id": "en_MiopostoCaffe_478543071:0", "sentence": "Fabulous Italian Food !", "postag": ["JJ", "JJ", "NN", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "amod", "root", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478543071:0-0", "target_tags": "Fabulous\\O Italian\\B Food\\I !\\O", "opinion_tags": "Fabulous\\B Italian\\O Food\\O !\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478543071:1", "sentence": "– I highly recommend Miop<PERSON><PERSON> .", "postag": ["NFP", "PRP", "RB", "VBP", "NNP", "."], "head": [4, 4, 4, 0, 4, 4], "deprel": ["punct", "nsubj", "advmod", "root", "obj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478543071:1-0", "target_tags": "–\\O I\\O highly\\O recommend\\O Mioposto\\B .\\O", "opinion_tags": "–\\O I\\O highly\\O recommend\\B Mioposto\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478543071:2", "sentence": "I am so happy to have a wonderful Italian restaurant in my neighborhood .", "postag": ["PRP", "VBP", "RB", "JJ", "TO", "VB", "DT", "JJ", "JJ", "NN", "IN", "PRP$", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 10, 10, 10, 6, 13, 13, 10, 4], "deprel": ["nsubj", "cop", "advmod", "root", "mark", "xcomp", "det", "amod", "amod", "obj", "case", "nmod:poss", "nmod", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478543071:2-0", "target_tags": "I\\O am\\O so\\O happy\\O to\\O have\\O a\\O wonderful\\O Italian\\B restaurant\\I in\\O my\\O neighborhood\\O .\\O", "opinion_tags": "I\\O am\\O so\\O happy\\O to\\O have\\O a\\O wonderful\\B Italian\\O restaurant\\O in\\O my\\O neighborhood\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478543071:3", "sentence": "The wine list is wonderful and the food reminds me of my recent trip to Italy .", "postag": ["DT", "NN", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "PRP", "IN", "PRP$", "JJ", "NN", "IN", "NNP", "."], "head": [3, 3, 5, 5, 0, 9, 8, 9, 5, 9, 14, 14, 14, 9, 16, 14, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "cc", "det", "nsubj", "conj", "obj", "case", "nmod:poss", "amod", "obl", "case", "nmod", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478543071:3-0", "target_tags": "The\\O wine\\B list\\I is\\O wonderful\\O and\\O the\\O food\\O reminds\\O me\\O of\\O my\\O recent\\O trip\\O to\\O Italy\\O .\\O", "opinion_tags": "The\\O wine\\O list\\O is\\O wonderful\\B and\\O the\\O food\\O reminds\\O me\\O of\\O my\\O recent\\O trip\\O to\\O Italy\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'s_478075040:0", "sentence": "I love this restaurant", "postag": ["PRP", "VBP", "DT", "NN"], "head": [2, 0, 4, 2], "deprel": ["nsubj", "root", "det", "obj"], "triples": [{"uid": "en_<PERSON>'s_478075040:0-0", "target_tags": "I\\O love\\O this\\O restaurant\\B", "opinion_tags": "I\\O love\\B this\\O restaurant\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'s_478075040:1", "sentence": "– I will never forget the amazing meal , service , and ambiance I experience at this restaurant .", "postag": ["NFP", "PRP", "MD", "RB", "VB", "DT", "JJ", "NN", ",", "NN", ",", "CC", "NN", "PRP", "VBP", "IN", "DT", "NN", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 10, 8, 13, 13, 8, 15, 13, 18, 18, 15, 5], "deprel": ["punct", "nsubj", "aux", "advmod", "root", "det", "amod", "obj", "punct", "conj", "punct", "cc", "conj", "nsubj", "acl:relcl", "case", "det", "obl", "punct"], "triples": [{"uid": "en_<PERSON>'s_478075040:1-0", "target_tags": "–\\O I\\O will\\O never\\O forget\\O the\\O amazing\\O meal\\B ,\\O service\\O ,\\O and\\O ambiance\\O I\\O experience\\O at\\O this\\O restaurant\\O .\\O", "opinion_tags": "–\\O I\\O will\\O never\\O forget\\O the\\O amazing\\B meal\\O ,\\O service\\O ,\\O and\\O ambiance\\O I\\O experience\\O at\\O this\\O restaurant\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'s_478075040:1-1", "target_tags": "–\\O I\\O will\\O never\\O forget\\O the\\O amazing\\O meal\\O ,\\O service\\B ,\\O and\\O ambiance\\O I\\O experience\\O at\\O this\\O restaurant\\O .\\O", "opinion_tags": "–\\O I\\O will\\O never\\O forget\\O the\\O amazing\\B meal\\O ,\\O service\\O ,\\O and\\O ambiance\\O I\\O experience\\O at\\O this\\O restaurant\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'s_478075040:1-2", "target_tags": "–\\O I\\O will\\O never\\O forget\\O the\\O amazing\\O meal\\O ,\\O service\\O ,\\O and\\O ambiance\\B I\\O experience\\O at\\O this\\O restaurant\\O .\\O", "opinion_tags": "–\\O I\\O will\\O never\\O forget\\O the\\O amazing\\B meal\\O ,\\O service\\O ,\\O and\\O ambiance\\O I\\O experience\\O at\\O this\\O restaurant\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'s_478075040:3", "sentence": "The wine list is incredible and extensive and diverse , the food is all incredible and the staff was all very nice , good at their jobs and cultured .", "postag": ["DT", "NN", "NN", "VBZ", "JJ", "CC", "JJ", "CC", "JJ", ",", "DT", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NN", "VBD", "RB", "RB", "JJ", ",", "JJ", "IN", "PRP$", "NNS", "CC", "VBN", "."], "head": [3, 3, 5, 5, 0, 7, 5, 9, 5, 5, 12, 15, 15, 15, 5, 22, 18, 22, 22, 22, 22, 5, 24, 22, 27, 27, 22, 29, 27, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "cc", "conj", "cc", "conj", "punct", "det", "nsubj", "cop", "advmod", "conj", "cc", "det", "nsubj", "cop", "advmod", "advmod", "conj", "punct", "conj", "case", "nmod:poss", "obl", "cc", "conj", "punct"], "triples": [{"uid": "en_<PERSON>'s_478075040:3-0", "target_tags": "The\\O wine\\B list\\I is\\O incredible\\O and\\O extensive\\O and\\O diverse\\O ,\\O the\\O food\\O is\\O all\\O incredible\\O and\\O the\\O staff\\O was\\O all\\O very\\O nice\\O ,\\O good\\O at\\O their\\O jobs\\O and\\O cultured\\O .\\O", "opinion_tags": "The\\O wine\\O list\\O is\\O incredible\\B and\\O extensive\\B and\\O diverse\\B ,\\O the\\O food\\O is\\O all\\O incredible\\O and\\O the\\O staff\\O was\\O all\\O very\\O nice\\O ,\\O good\\O at\\O their\\O jobs\\O and\\O cultured\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'s_478075040:3-1", "target_tags": "The\\O wine\\O list\\O is\\O incredible\\O and\\O extensive\\O and\\O diverse\\O ,\\O the\\O food\\B is\\O all\\O incredible\\O and\\O the\\O staff\\O was\\O all\\O very\\O nice\\O ,\\O good\\O at\\O their\\O jobs\\O and\\O cultured\\O .\\O", "opinion_tags": "The\\O wine\\O list\\O is\\O incredible\\O and\\O extensive\\O and\\O diverse\\O ,\\O the\\O food\\O is\\O all\\O incredible\\B and\\O the\\O staff\\O was\\O all\\O very\\O nice\\O ,\\O good\\O at\\O their\\O jobs\\O and\\O cultured\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'s_478075040:3-2", "target_tags": "The\\O wine\\O list\\O is\\O incredible\\O and\\O extensive\\O and\\O diverse\\O ,\\O the\\O food\\O is\\O all\\O incredible\\O and\\O the\\O staff\\B was\\O all\\O very\\O nice\\O ,\\O good\\O at\\O their\\O jobs\\O and\\O cultured\\O .\\O", "opinion_tags": "The\\O wine\\O list\\O is\\O incredible\\O and\\O extensive\\O and\\O diverse\\O ,\\O the\\O food\\O is\\O all\\O incredible\\O and\\O the\\O staff\\O was\\O all\\O very\\O nice\\B ,\\O good\\O at\\O their\\O jobs\\O and\\O cultured\\O .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970939:3", "sentence": "The food was great !", "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_OpenSesame_477970939:3-0", "target_tags": "The\\O food\\B was\\O great\\O !\\O", "opinion_tags": "The\\O food\\O was\\O great\\B !\\O", "sentiment": "positive"}]}, {"id": "en_Sage_480875505:1", "sentence": "– In a age of incremental cost cutting in restaurants , its nice to see a place that bucks that trend , and just plain delivers high quality food and good service , period .", "postag": ["NFP", "IN", "DT", "NN", "IN", "JJ", "NN", "VBG", "IN", "NNS", ",", "PRP$", "JJ", "TO", "VB", "DT", "NN", "WDT", "VBZ", "DT", "NN", ",", "CC", "RB", "JJ", "VBZ", "JJ", "JJ", "NN", "CC", "JJ", "NN", ",", "NN", "."], "head": [13, 4, 4, 15, 7, 7, 4, 4, 10, 8, 13, 13, 0, 15, 13, 17, 15, 19, 17, 21, 19, 26, 26, 25, 26, 15, 29, 29, 26, 34, 32, 34, 34, 29, 13], "deprel": ["punct", "case", "det", "obl", "case", "amod", "nmod", "acl", "case", "obl", "punct", "nmod:poss", "root", "mark", "acl", "det", "obj", "nsubj", "acl:relcl", "det", "obj", "punct", "cc", "advmod", "advmod", "conj", "amod", "amod", "obj", "cc", "amod", "compound", "punct", "conj", "punct"], "triples": [{"uid": "en_Sage_480875505:1-0", "target_tags": "–\\O In\\O a\\O age\\O of\\O incremental\\O cost\\O cutting\\O in\\O restaurants\\O ,\\O its\\O nice\\O to\\O see\\O a\\O place\\O that\\O bucks\\O that\\O trend\\O ,\\O and\\O just\\O plain\\O delivers\\O high\\O quality\\O food\\B and\\O good\\O service\\O ,\\O period\\O .\\O", "opinion_tags": "–\\O In\\O a\\O age\\O of\\O incremental\\O cost\\O cutting\\O in\\O restaurants\\O ,\\O its\\O nice\\O to\\O see\\O a\\O place\\O that\\O bucks\\O that\\O trend\\O ,\\O and\\O just\\O plain\\O delivers\\O high\\B quality\\I food\\O and\\O good\\O service\\O ,\\O period\\O .\\O", "sentiment": "positive"}, {"uid": "en_Sage_480875505:1-1", "target_tags": "–\\O In\\O a\\O age\\O of\\O incremental\\O cost\\O cutting\\O in\\O restaurants\\O ,\\O its\\O nice\\O to\\O see\\O a\\O place\\O that\\O bucks\\O that\\O trend\\O ,\\O and\\O just\\O plain\\O delivers\\O high\\O quality\\O food\\O and\\O good\\O service\\B ,\\O period\\O .\\O", "opinion_tags": "–\\O In\\O a\\O age\\O of\\O incremental\\O cost\\O cutting\\O in\\O restaurants\\O ,\\O its\\O nice\\O to\\O see\\O a\\O place\\O that\\O bucks\\O that\\O trend\\O ,\\O and\\O just\\O plain\\O delivers\\O high\\O quality\\O food\\O and\\O good\\B service\\O ,\\O period\\O .\\O", "sentiment": "positive"}]}, {"id": "en_Sage_480875505:2", "sentence": "This is the place to relax and enjoy the finest quality food the industry can offer .", "postag": ["DT", "VBZ", "DT", "NN", "TO", "VB", "CC", "VB", "DT", "JJS", "JJ", "NN", "DT", "NN", "MD", "VB", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 12, 12, 12, 8, 14, 16, 16, 12, 4], "deprel": ["nsubj", "cop", "det", "root", "mark", "acl", "cc", "conj", "det", "amod", "amod", "obj", "det", "nsubj", "aux", "acl:relcl", "punct"], "triples": [{"uid": "en_Sage_480875505:2-0", "target_tags": "This\\O is\\O the\\O place\\B to\\O relax\\O and\\O enjoy\\O the\\O finest\\O quality\\O food\\O the\\O industry\\O can\\O offer\\O .\\O", "opinion_tags": "This\\O is\\O the\\O place\\O to\\O relax\\B and\\O enjoy\\O the\\O finest\\O quality\\O food\\O the\\O industry\\O can\\O offer\\O .\\O", "sentiment": "positive"}, {"uid": "en_Sage_480875505:2-1", "target_tags": "This\\O is\\O the\\O place\\O to\\O relax\\O and\\O enjoy\\O the\\O finest\\O quality\\O food\\B the\\O industry\\O can\\O offer\\O .\\O", "opinion_tags": "This\\O is\\O the\\O place\\O to\\O relax\\O and\\O enjoy\\O the\\O finest\\B quality\\I food\\O the\\O industry\\O can\\O offer\\O .\\O", "sentiment": "positive"}]}, {"id": "en_Sage_480875505:3", "sentence": "Caution - its real food for people who love the best .", "postag": ["NN", ",", "PRP$", "JJ", "NN", "IN", "NNS", "WP", "VBP", "DT", "JJS", "."], "head": [0, 1, 5, 5, 1, 7, 5, 9, 7, 11, 9, 1], "deprel": ["root", "punct", "nmod:poss", "amod", "parataxis", "case", "nmod", "nsubj", "acl:relcl", "det", "obj", "punct"], "triples": [{"uid": "en_Sage_480875505:3-0", "target_tags": "Caution\\O -\\O its\\O real\\O food\\B for\\O people\\O who\\O love\\O the\\O best\\O .\\O", "opinion_tags": "Caution\\O -\\O its\\O real\\B food\\O for\\O people\\O who\\O love\\O the\\O best\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218900:2", "sentence": "I liked the atmosphere very much but the food was not worth the price .", "postag": ["PRP", "VBD", "DT", "NN", "RB", "RB", "CC", "DT", "NN", "VBD", "RB", "JJ", "DT", "NN", "."], "head": [2, 0, 4, 2, 6, 2, 12, 9, 12, 12, 12, 2, 14, 12, 2], "deprel": ["nsubj", "root", "det", "obj", "advmod", "advmod", "cc", "det", "nsubj", "cop", "advmod", "conj", "det", "obj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218900:2-0", "target_tags": "I\\O liked\\O the\\O atmosphere\\B very\\O much\\O but\\O the\\O food\\O was\\O not\\O worth\\O the\\O price\\O .\\O", "opinion_tags": "I\\O liked\\B the\\O atmosphere\\O very\\O much\\O but\\O the\\O food\\O was\\O not\\O worth\\O the\\O price\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218900:2-1", "target_tags": "I\\O liked\\O the\\O atmosphere\\O very\\O much\\O but\\O the\\O food\\B was\\O not\\O worth\\O the\\O price\\O .\\O", "opinion_tags": "I\\O liked\\O the\\O atmosphere\\O very\\O much\\O but\\O the\\O food\\O was\\O not\\B worth\\I the\\I price\\I .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478218900:3", "sentence": "I may not be a sushi guru but I can tell you that the food here is just okay and that there is not much else to it .", "postag": ["PRP", "MD", "RB", "VB", "DT", "NN", "NN", "CC", "PRP", "MD", "VB", "PRP", "IN", "DT", "NN", "RB", "VBZ", "RB", "JJ", "CC", "IN", "EX", "VBZ", "RB", "JJ", "JJ", "IN", "PRP", "."], "head": [7, 7, 7, 7, 7, 7, 0, 11, 11, 11, 7, 11, 19, 15, 19, 15, 19, 19, 11, 23, 23, 23, 19, 23, 23, 25, 28, 23, 7], "deprel": ["nsubj", "aux", "advmod", "cop", "det", "compound", "root", "cc", "nsubj", "aux", "conj", "obj", "mark", "det", "nsubj", "advmod", "cop", "advmod", "ccomp", "cc", "mark", "expl", "conj", "advmod", "nsubj", "amod", "case", "obl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218900:3-0", "target_tags": "I\\O may\\O not\\O be\\O a\\O sushi\\O guru\\O but\\O I\\O can\\O tell\\O you\\O that\\O the\\O food\\B here\\O is\\O just\\O okay\\O and\\O that\\O there\\O is\\O not\\O much\\O else\\O to\\O it\\O .\\O", "opinion_tags": "I\\O may\\O not\\O be\\O a\\O sushi\\O guru\\O but\\O I\\O can\\O tell\\O you\\O that\\O the\\O food\\O here\\O is\\O just\\O okay\\B and\\O that\\O there\\O is\\O not\\O much\\O else\\O to\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478218900:4", "sentence": "Rice is too dry , tuna was n't so fresh either .", "postag": ["NNP", "VBZ", "RB", "JJ", ",", "NN", "VBD", "RB", "RB", "JJ", "RB", "."], "head": [4, 4, 4, 0, 4, 10, 10, 10, 10, 4, 10, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct", "nsubj", "cop", "advmod", "advmod", "parataxis", "advmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218900:4-0", "target_tags": "Rice\\B is\\O too\\O dry\\O ,\\O tuna\\O was\\O n't\\O so\\O fresh\\O either\\O .\\O", "opinion_tags": "Rice\\O is\\O too\\B dry\\I ,\\O tuna\\O was\\O n't\\O so\\O fresh\\O either\\O .\\O", "sentiment": "negative"}, {"uid": "en_BlueRibbonSushi_478218900:4-1", "target_tags": "Rice\\O is\\O too\\O dry\\O ,\\O tuna\\B was\\O n't\\O so\\O fresh\\O either\\O .\\O", "opinion_tags": "Rice\\O is\\O too\\O dry\\O ,\\O tuna\\O was\\B n't\\I so\\I fresh\\I either\\O .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478219453:0", "sentence": "I have eaten here three times and have found the quality and variety of the fish to be excellent .", "postag": ["PRP", "VBP", "VBN", "RB", "CD", "NNS", "CC", "VBP", "VBN", "DT", "NN", "CC", "NN", "IN", "DT", "NN", "TO", "VB", "JJ", "."], "head": [3, 3, 0, 3, 6, 3, 9, 9, 3, 11, 9, 13, 11, 16, 16, 13, 19, 19, 9, 3], "deprel": ["nsubj", "aux", "root", "advmod", "nummod", "obl:tmod", "cc", "aux", "conj", "det", "obj", "cc", "conj", "case", "det", "nmod", "mark", "cop", "advcl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478219453:0-0", "target_tags": "I\\O have\\O eaten\\O here\\O three\\O times\\O and\\O have\\O found\\O the\\O quality\\O and\\O variety\\O of\\O the\\O fish\\B to\\O be\\O excellent\\O .\\O", "opinion_tags": "I\\O have\\O eaten\\O here\\O three\\O times\\O and\\O have\\O found\\O the\\O quality\\O and\\O variety\\O of\\O the\\O fish\\O to\\O be\\O excellent\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478219453:1", "sentence": "However , the value and service are both severely lacking .", "postag": ["RB", ",", "DT", "NN", "CC", "NN", "VBP", "CC", "RB", "JJ", "."], "head": [10, 10, 4, 10, 6, 4, 10, 10, 10, 0, 10], "deprel": ["advmod", "punct", "det", "nsubj", "cc", "conj", "cop", "advmod", "advmod", "root", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478219453:1-0", "target_tags": "However\\O ,\\O the\\O value\\O and\\O service\\B are\\O both\\O severely\\O lacking\\O .\\O", "opinion_tags": "However\\O ,\\O the\\O value\\O and\\O service\\O are\\O both\\O severely\\O lacking\\B .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478219453:3", "sentence": "Furthermore , while the fish is unquestionably fresh , rolls tend to be inexplicably bland .", "postag": ["RB", ",", "IN", "DT", "NN", "VBZ", "RB", "JJ", ",", "NNS", "VBP", "TO", "VB", "RB", "JJ", "."], "head": [11, 11, 8, 5, 8, 8, 8, 11, 11, 11, 0, 15, 15, 15, 11, 11], "deprel": ["advmod", "punct", "mark", "det", "nsubj", "cop", "advmod", "advcl", "punct", "nsubj", "root", "mark", "cop", "advmod", "xcomp", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478219453:3-0", "target_tags": "Furthermore\\O ,\\O while\\O the\\O fish\\B is\\O unquestionably\\O fresh\\O ,\\O rolls\\O tend\\O to\\O be\\O inexplicably\\O bland\\O .\\O", "opinion_tags": "Furthermore\\O ,\\O while\\O the\\O fish\\O is\\O unquestionably\\O fresh\\B ,\\O rolls\\O tend\\O to\\O be\\O inexplicably\\O bland\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478219453:3-1", "target_tags": "Furthermore\\O ,\\O while\\O the\\O fish\\O is\\O unquestionably\\O fresh\\O ,\\O rolls\\B tend\\O to\\O be\\O inexplicably\\O bland\\O .\\O", "opinion_tags": "Furthermore\\O ,\\O while\\O the\\O fish\\O is\\O unquestionably\\O fresh\\O ,\\O rolls\\O tend\\O to\\O be\\O inexplicably\\O bland\\B .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478219453:4", "sentence": "The service ranges from mediocre to offensive .", "postag": ["DT", "NN", "VBZ", "IN", "JJ", "IN", "JJ", "."], "head": [2, 3, 0, 5, 3, 7, 3, 3], "deprel": ["det", "nsubj", "root", "case", "obl", "case", "obl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478219453:4-0", "target_tags": "The\\O service\\B ranges\\O from\\O mediocre\\O to\\O offensive\\O .\\O", "opinion_tags": "The\\O service\\O ranges\\O from\\O mediocre\\B to\\O offensive\\B .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478219453:5", "sentence": "On a recent trip , our waiter was extremely dismissive , while no less than three staff members waited hand-and-foot on a pair of Japanese girls seated nearby .", "postag": ["IN", "DT", "JJ", "NN", ",", "PRP$", "NN", "VBD", "RB", "JJ", ",", "IN", "RB", "JJR", "IN", "CD", "NN", "NNS", "VBD", "JJ", "IN", "DT", "NN", "IN", "JJ", "NNS", "VBN", "RB", "."], "head": [4, 4, 4, 10, 10, 7, 10, 10, 10, 0, 10, 19, 14, 16, 14, 18, 18, 19, 10, 19, 23, 23, 19, 26, 26, 23, 26, 27, 10], "deprel": ["case", "det", "amod", "obl", "punct", "nmod:poss", "nsubj", "cop", "advmod", "root", "punct", "mark", "advmod", "advmod", "fixed", "nummod", "compound", "nsubj", "advcl", "xcomp", "case", "det", "obl", "case", "amod", "nmod", "acl", "advmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478219453:5-0", "target_tags": "On\\O a\\O recent\\O trip\\O ,\\O our\\O waiter\\B was\\O extremely\\O dismissive\\O ,\\O while\\O no\\O less\\O than\\O three\\O staff\\O members\\O waited\\O hand-and-foot\\O on\\O a\\O pair\\O of\\O Japanese\\O girls\\O seated\\O nearby\\O .\\O", "opinion_tags": "On\\O a\\O recent\\O trip\\O ,\\O our\\O waiter\\O was\\O extremely\\O dismissive\\B ,\\O while\\O no\\O less\\O than\\O three\\O staff\\O members\\O waited\\O hand-and-foot\\O on\\O a\\O pair\\O of\\O Japanese\\O girls\\O seated\\O nearby\\O .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_479929856:0", "sentence": "Freshest sushi – I love this restaurant .", "postag": ["JJS", "NN", ",", "PRP", "VBP", "DT", "NN", "."], "head": [2, 0, 2, 5, 2, 7, 5, 2], "deprel": ["amod", "root", "punct", "nsubj", "parataxis", "det", "obj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_479929856:0-0", "target_tags": "Freshest\\O sushi\\B –\\O I\\O love\\O this\\O restaurant\\O .\\O", "opinion_tags": "Freshest\\B sushi\\O –\\O I\\O love\\O this\\O restaurant\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_479929856:0-1", "target_tags": "Freshest\\O sushi\\O –\\O I\\O love\\O this\\O restaurant\\B .\\O", "opinion_tags": "Freshest\\O sushi\\O –\\O I\\O love\\B this\\O restaurant\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_479929856:1", "sentence": "They pay such detail to everything from miso soup to complex rolls .", "postag": ["PRP", "VBP", "JJ", "NN", "IN", "NN", "IN", "NN", "NN", "IN", "JJ", "NNS", "."], "head": [2, 0, 4, 2, 6, 2, 9, 9, 6, 12, 12, 2, 2], "deprel": ["nsubj", "root", "amod", "obj", "case", "obl", "case", "compound", "nmod", "case", "amod", "obl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_479929856:1-0", "target_tags": "They\\O pay\\O such\\O detail\\O to\\O everything\\O from\\O miso\\O soup\\O to\\O complex\\O rolls\\B .\\O", "opinion_tags": "They\\O pay\\O such\\O detail\\O to\\O everything\\O from\\O miso\\O soup\\O to\\O complex\\B rolls\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_479929856:2", "sentence": "The sashimi was the freshest and most tender I have ever tasted .", "postag": ["DT", "NN", "VBD", "DT", "JJS", "CC", "RBS", "JJ", "PRP", "VBP", "RB", "VBN", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 12, 12, 12, 5, 5], "deprel": ["det", "nsubj", "cop", "det", "root", "cc", "advmod", "conj", "nsubj", "aux", "advmod", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_479929856:2-0", "target_tags": "The\\O sashimi\\B was\\O the\\O freshest\\O and\\O most\\O tender\\O I\\O have\\O ever\\O tasted\\O .\\O", "opinion_tags": "The\\O sashimi\\O was\\O the\\O freshest\\B and\\O most\\O tender\\B I\\O have\\O ever\\O tasted\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_479929856:3", "sentence": "Their apps are all delicious .", "postag": ["PRP$", "NNS", "VBP", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["nmod:poss", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_479929856:3-0", "target_tags": "Their\\O apps\\B are\\O all\\O delicious\\O .\\O", "opinion_tags": "Their\\O apps\\O are\\O all\\O delicious\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_479929856:4", "sentence": "The only drawback is that this place is really expensive and the portions are on the small side .", "postag": ["DT", "JJ", "NN", "VBZ", "IN", "DT", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NNS", "VBP", "IN", "DT", "JJ", "NN", "."], "head": [3, 3, 4, 0, 10, 7, 10, 10, 10, 4, 18, 13, 18, 18, 18, 18, 18, 10, 4], "deprel": ["det", "amod", "nsubj", "root", "mark", "det", "nsubj", "cop", "advmod", "ccomp", "cc", "det", "nsubj", "cop", "case", "det", "amod", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_479929856:4-0", "target_tags": "The\\O only\\O drawback\\O is\\O that\\O this\\O place\\B is\\O really\\O expensive\\O and\\O the\\O portions\\O are\\O on\\O the\\O small\\O side\\O .\\O", "opinion_tags": "The\\O only\\O drawback\\O is\\O that\\O this\\O place\\O is\\O really\\O expensive\\B and\\O the\\O portions\\O are\\O on\\O the\\O small\\O side\\O .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_479929856:5", "sentence": "But the space is small and lovely , and the service is helpful .", "postag": ["CC", "DT", "NN", "VBZ", "JJ", "CC", "JJ", ",", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [13, 3, 5, 5, 0, 7, 5, 13, 13, 11, 13, 13, 5, 5], "deprel": ["cc", "det", "nsubj", "cop", "root", "cc", "conj", "punct", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_479929856:5-0", "target_tags": "But\\O the\\O space\\B is\\O small\\O and\\O lovely\\O ,\\O and\\O the\\O service\\O is\\O helpful\\O .\\O", "opinion_tags": "But\\O the\\O space\\O is\\O small\\B and\\O lovely\\B ,\\O and\\O the\\O service\\O is\\O helpful\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_479929856:5-1", "target_tags": "But\\O the\\O space\\O is\\O small\\O and\\O lovely\\O ,\\O and\\O the\\O service\\B is\\O helpful\\O .\\O", "opinion_tags": "But\\O the\\O space\\O is\\O small\\O and\\O lovely\\O ,\\O and\\O the\\O service\\O is\\O helpful\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218901:1", "sentence": "– The food was not great & the waiters were rude .", "postag": ["NFP", "DT", "NN", "VBD", "RB", "JJ", "CC", "DT", "NNS", "VBD", "JJ", "."], "head": [6, 3, 6, 6, 6, 0, 11, 9, 11, 11, 6, 6], "deprel": ["punct", "det", "nsubj", "cop", "advmod", "root", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218901:1-0", "target_tags": "–\\O The\\O food\\B was\\O not\\O great\\O &\\O the\\O waiters\\O were\\O rude\\O .\\O", "opinion_tags": "–\\O The\\O food\\O was\\O not\\B great\\I &\\O the\\O waiters\\O were\\O rude\\O .\\O", "sentiment": "negative"}, {"uid": "en_BlueRibbonSushi_478218901:1-1", "target_tags": "–\\O The\\O food\\O was\\O not\\O great\\O &\\O the\\O waiters\\B were\\O rude\\O .\\O", "opinion_tags": "–\\O The\\O food\\O was\\O not\\O great\\O &\\O the\\O waiters\\O were\\O rude\\B .\\O", "sentiment": "negative"}]}, {"id": "en_MercedesRestaurant_478010605:0", "sentence": "great service", "postag": ["JJ", "NN"], "head": [2, 0], "deprel": ["amod", "root"], "triples": [{"uid": "en_MercedesRestaurant_478010605:0-0", "target_tags": "great\\O service\\B", "opinion_tags": "great\\B service\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010605:2", "sentence": "my service was stellar !", "postag": ["PRP$", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["nmod:poss", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010605:2-0", "target_tags": "my\\O service\\B was\\O stellar\\O !\\O", "opinion_tags": "my\\O service\\O was\\O stellar\\B !\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010605:4", "sentence": "food was fine , with a some little-tastier-than-normal salsa .", "postag": ["NN", "VBD", "JJ", ",", "IN", "DT", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 3, 9, 9, 9, 9, 3, 3], "deprel": ["nsubj", "cop", "root", "punct", "case", "det", "det", "amod", "obl", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010605:4-0", "target_tags": "food\\B was\\O fine\\O ,\\O with\\O a\\O some\\O little-tastier-than-normal\\O salsa\\O .\\O", "opinion_tags": "food\\O was\\O fine\\B ,\\O with\\O a\\O some\\O little-tastier-than-normal\\O salsa\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_480852899:1", "sentence": "the food was great , the margaritas too but the waitress was too busy being nice to her other larger party than to take better care of my friend and me .", "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NNS", "RB", "CC", "DT", "NN", "VBD", "RB", "JJ", "VBG", "JJ", "IN", "PRP$", "JJ", "JJR", "NN", "IN", "TO", "VB", "JJR", "NN", "IN", "PRP$", "NN", "CC", "PRP", "."], "head": [2, 4, 4, 0, 7, 7, 4, 4, 14, 11, 14, 14, 14, 4, 16, 14, 21, 21, 21, 21, 16, 24, 24, 14, 26, 24, 29, 29, 26, 31, 29, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "parataxis", "advmod", "cc", "det", "nsubj", "cop", "advmod", "conj", "cop", "advcl", "case", "nmod:poss", "amod", "amod", "obl", "mark", "mark", "advcl", "amod", "obj", "case", "nmod:poss", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "en_MercedesRestaurant_480852899:1-0", "target_tags": "the\\O food\\B was\\O great\\O ,\\O the\\O margaritas\\O too\\O but\\O the\\O waitress\\O was\\O too\\O busy\\O being\\O nice\\O to\\O her\\O other\\O larger\\O party\\O than\\O to\\O take\\O better\\O care\\O of\\O my\\O friend\\O and\\O me\\O .\\O", "opinion_tags": "the\\O food\\O was\\O great\\B ,\\O the\\O margaritas\\O too\\O but\\O the\\O waitress\\O was\\O too\\O busy\\O being\\O nice\\O to\\O her\\O other\\O larger\\O party\\O than\\O to\\O take\\O better\\O care\\O of\\O my\\O friend\\O and\\O me\\O .\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_480852899:1-1", "target_tags": "the\\O food\\O was\\O great\\O ,\\O the\\O margaritas\\B too\\O but\\O the\\O waitress\\O was\\O too\\O busy\\O being\\O nice\\O to\\O her\\O other\\O larger\\O party\\O than\\O to\\O take\\O better\\O care\\O of\\O my\\O friend\\O and\\O me\\O .\\O", "opinion_tags": "the\\O food\\O was\\O great\\B ,\\O the\\O margaritas\\O too\\O but\\O the\\O waitress\\O was\\O too\\O busy\\O being\\O nice\\O to\\O her\\O other\\O larger\\O party\\O than\\O to\\O take\\O better\\O care\\O of\\O my\\O friend\\O and\\O me\\O .\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_480852899:1-2", "target_tags": "the\\O food\\O was\\O great\\O ,\\O the\\O margaritas\\O too\\O but\\O the\\O waitress\\B was\\O too\\O busy\\O being\\O nice\\O to\\O her\\O other\\O larger\\O party\\O than\\O to\\O take\\O better\\O care\\O of\\O my\\O friend\\O and\\O me\\O .\\O", "opinion_tags": "the\\O food\\O was\\O great\\O ,\\O the\\O margaritas\\O too\\O but\\O the\\O waitress\\O was\\O too\\B busy\\I being\\O nice\\O to\\O her\\O other\\O larger\\O party\\O than\\O to\\O take\\O better\\O care\\O of\\O my\\O friend\\O and\\O me\\O .\\O", "sentiment": "negative"}]}, {"id": "en_<PERSON><PERSON>'sPizzeria_478231878:0", "sentence": "<PERSON> – I live in the neighborhood and feel lucky to live by such a great pizza place .", "postag": ["NNP", "NNP", ",", "PRP", "VBP", "IN", "DT", "NN", "CC", "VBP", "JJ", "TO", "VB", "IN", "PDT", "DT", "JJ", "NN", "NN", "."], "head": [2, 0, 2, 5, 2, 8, 8, 5, 10, 5, 10, 13, 11, 19, 19, 19, 19, 19, 13, 2], "deprel": ["compound", "root", "punct", "nsubj", "parataxis", "case", "det", "obl", "cc", "conj", "xcomp", "mark", "advcl", "case", "det:predet", "det", "amod", "compound", "obl", "punct"], "triples": [{"uid": "en_<PERSON><PERSON>'sPizzeria_478231878:0-0", "target_tags": "Mama\\O Mia\\O –\\O I\\O live\\O in\\O the\\O neighborhood\\O and\\O feel\\O lucky\\O to\\O live\\O by\\O such\\O a\\O great\\O pizza\\B place\\I .\\O", "opinion_tags": "Mama\\O Mia\\O –\\O I\\O live\\O in\\O the\\O neighborhood\\O and\\O feel\\O lucky\\O to\\O live\\O by\\O such\\O a\\O great\\B pizza\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218519:0", "sentence": "Best Sushi in town .", "postag": ["JJS", "NN", "IN", "NN", "."], "head": [2, 0, 4, 2, 2], "deprel": ["amod", "root", "case", "nmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218519:0-0", "target_tags": "Best\\O Sushi\\B in\\O town\\O .\\O", "opinion_tags": "Best\\B Sushi\\O in\\O town\\O .\\O", "sentiment": "positive"}]}, {"id": "en_Ray'sBoathouse_477775919:0", "sentence": "The best calamari in Seattle !", "postag": ["DT", "JJS", "NN", "IN", "NNP", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["det", "amod", "root", "case", "nmod", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775919:0-0", "target_tags": "The\\O best\\O calamari\\B in\\O Seattle\\O !\\O", "opinion_tags": "The\\O best\\B calamari\\O in\\O Seattle\\O !\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775919:1", "sentence": "– ... and the best summertime deck experience -- they will even bring you a blanket if you get cold in the Seattle evening weather .", "postag": ["NFP", ",", "CC", "DT", "JJS", "NN", "NN", "NN", ",", "PRP", "MD", "RB", "VB", "PRP", "DT", "NN", "IN", "PRP", "VBP", "JJ", "IN", "DT", "NNP", "NN", "NN", "."], "head": [8, 8, 8, 8, 8, 7, 8, 0, 8, 13, 13, 13, 8, 13, 16, 13, 19, 19, 13, 19, 25, 25, 25, 25, 20, 8], "deprel": ["punct", "punct", "cc", "det", "amod", "compound", "compound", "root", "punct", "nsubj", "aux", "advmod", "parataxis", "i<PERSON><PERSON>", "det", "obj", "mark", "nsubj", "advcl", "xcomp", "case", "det", "compound", "compound", "obl", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775919:1-0", "target_tags": "–\\O ...\\O and\\O the\\O best\\O summertime\\O deck\\B experience\\O --\\O they\\O will\\O even\\O bring\\O you\\O a\\O blanket\\O if\\O you\\O get\\O cold\\O in\\O the\\O Seattle\\O evening\\O weather\\O .\\O", "opinion_tags": "–\\O ...\\O and\\O the\\O best\\B summertime\\O deck\\O experience\\O --\\O they\\O will\\O even\\O bring\\O you\\O a\\O blanket\\O if\\O you\\O get\\O cold\\O in\\O the\\O Seattle\\O evening\\O weather\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775919:2", "sentence": "A perfect place to take out of town guests any time of the year .", "postag": ["DT", "JJ", "NN", "TO", "VB", "IN", "IN", "NN", "NNS", "DT", "NN", "IN", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 9, 9, 9, 5, 11, 5, 14, 14, 11, 3], "deprel": ["det", "amod", "root", "mark", "acl", "case", "case", "compound", "obl", "det", "obl:tmod", "case", "det", "nmod", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775919:2-0", "target_tags": "A\\O perfect\\O place\\B to\\O take\\O out\\O of\\O town\\O guests\\O any\\O time\\O of\\O the\\O year\\O .\\O", "opinion_tags": "A\\O perfect\\B place\\O to\\O take\\O out\\O of\\O town\\O guests\\O any\\O time\\O of\\O the\\O year\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON><PERSON>'s_478606540:0", "sentence": "Endless fun , awesome music , great staff ! ! !", "postag": ["JJ", "JJ", ",", "JJ", "NN", ",", "JJ", "NN", ".", ".", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 2, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "amod", "conj", "punct", "punct", "punct"], "triples": [{"uid": "en_<PERSON><PERSON><PERSON>'s_478606540:0-0", "target_tags": "Endless\\O fun\\O ,\\O awesome\\O music\\B ,\\O great\\O staff\\O !\\O !\\O !\\O", "opinion_tags": "Endless\\O fun\\O ,\\O awesome\\B music\\O ,\\O great\\O staff\\O !\\O !\\O !\\O", "sentiment": "positive"}, {"uid": "en_<PERSON><PERSON><PERSON>'s_478606540:0-1", "target_tags": "Endless\\O fun\\O ,\\O awesome\\O music\\O ,\\O great\\O staff\\B !\\O !\\O !\\O", "opinion_tags": "Endless\\O fun\\O ,\\O awesome\\O music\\O ,\\O great\\B staff\\O !\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON><PERSON>'s_478606540:1", "sentence": "– By far the best bar in the east village ...", "postag": ["NFP", "IN", "RB", "DT", "JJS", "NN", "IN", "DT", "JJ", "NN", "."], "head": [6, 3, 6, 6, 6, 0, 10, 10, 10, 6, 6], "deprel": ["punct", "case", "obl", "det", "amod", "root", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "en_<PERSON><PERSON><PERSON>'s_478606540:1-0", "target_tags": "–\\O By\\O far\\O the\\O best\\O bar\\B in\\O the\\O east\\O village\\O ...\\O", "opinion_tags": "–\\O By\\O far\\O the\\O best\\B bar\\O in\\O the\\O east\\O village\\O ...\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON><PERSON>'s_478606540:5", "sentence": "Great draft and bottle selection and the pizza rocks .", "postag": ["JJ", "NN", "CC", "NN", "NN", "CC", "DT", "NN", "NNS", "."], "head": [2, 0, 5, 5, 2, 9, 9, 9, 2, 2], "deprel": ["amod", "root", "cc", "compound", "conj", "cc", "det", "compound", "conj", "punct"], "triples": [{"uid": "en_<PERSON><PERSON><PERSON>'s_478606540:5-0", "target_tags": "Great\\O draft\\B and\\I bottle\\I selection\\I and\\O the\\O pizza\\O rocks\\O .\\O", "opinion_tags": "Great\\B draft\\O and\\O bottle\\O selection\\O and\\O the\\O pizza\\O rocks\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON><PERSON>'s_478606540:6", "sentence": "Definitely has one of the best jukebox 's i 've seen in a long long time .", "postag": ["RB", "VBZ", "CD", "IN", "DT", "JJS", "NN", "POS", "PRP", "VBP", "VBN", "IN", "DT", "JJ", "JJ", "NN", "."], "head": [2, 0, 2, 7, 7, 7, 3, 7, 11, 11, 3, 16, 16, 16, 16, 11, 2], "deprel": ["advmod", "root", "obj", "case", "det", "amod", "nmod", "case", "nsubj", "aux", "acl:relcl", "case", "det", "amod", "amod", "obl", "punct"], "triples": [{"uid": "en_<PERSON><PERSON><PERSON>'s_478606540:6-0", "target_tags": "Definitely\\O has\\O one\\O of\\O the\\O best\\O jukebox\\B 's\\I i\\O 've\\O seen\\O in\\O a\\O long\\O long\\O time\\O .\\O", "opinion_tags": "Definitely\\O has\\O one\\O of\\O the\\O best\\B jukebox\\O 's\\O i\\O 've\\O seen\\O in\\O a\\O long\\O long\\O time\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON><PERSON>'s_478606538:4", "sentence": "The food is great , the bartenders go that extra mile .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "DT", "NNS", "VBP", "DT", "JJ", "NN", "."], "head": [2, 4, 4, 0, 4, 7, 8, 4, 11, 11, 8, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "parataxis", "det", "amod", "obl:tmod", "punct"], "triples": [{"uid": "en_<PERSON><PERSON><PERSON>'s_478606538:4-0", "target_tags": "The\\O food\\B is\\O great\\O ,\\O the\\O bartenders\\O go\\O that\\O extra\\O mile\\O .\\O", "opinion_tags": "The\\O food\\O is\\O great\\B ,\\O the\\O bartenders\\O go\\O that\\O extra\\O mile\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON><PERSON>'s_478606538:6", "sentence": "The owners are great fun and the beer selection is worth staying for .", "postag": ["DT", "NNS", "VBP", "JJ", "NN", "CC", "DT", "NN", "NN", "VBZ", "JJ", "VBG", "IN", "."], "head": [2, 5, 5, 5, 0, 11, 9, 9, 11, 11, 5, 11, 12, 5], "deprel": ["det", "nsubj", "cop", "amod", "root", "cc", "det", "compound", "nsubj", "cop", "conj", "xcomp", "obl", "punct"], "triples": [{"uid": "en_<PERSON><PERSON><PERSON>'s_478606538:6-0", "target_tags": "The\\O owners\\B are\\O great\\O fun\\O and\\O the\\O beer\\O selection\\O is\\O worth\\O staying\\O for\\O .\\O", "opinion_tags": "The\\O owners\\O are\\O great\\B fun\\O and\\O the\\O beer\\O selection\\O is\\O worth\\O staying\\O for\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON><PERSON><PERSON>'s_478606538:6-1", "target_tags": "The\\O owners\\O are\\O great\\O fun\\O and\\O the\\O beer\\B selection\\I is\\O worth\\O staying\\O for\\O .\\O", "opinion_tags": "The\\O owners\\O are\\O great\\O fun\\O and\\O the\\O beer\\O selection\\O is\\O worth\\B staying\\I for\\I .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON><PERSON>'s_478606538:7", "sentence": "And the upstairs is a great place to hang out .", "postag": ["CC", "DT", "RB", "VBZ", "DT", "JJ", "NN", "TO", "VB", "RP", "."], "head": [7, 3, 7, 7, 7, 7, 0, 9, 7, 9, 7], "deprel": ["cc", "det", "advmod", "cop", "det", "amod", "root", "mark", "acl", "compound:prt", "punct"], "triples": [{"uid": "en_<PERSON><PERSON><PERSON>'s_478606538:7-0", "target_tags": "And\\O the\\O upstairs\\B is\\O a\\O great\\O place\\O to\\O hang\\O out\\O .\\O", "opinion_tags": "And\\O the\\O upstairs\\O is\\O a\\O great\\B place\\O to\\O hang\\O out\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON><PERSON>'s_478606538:8", "sentence": "Not alot of smoking places left in New York , but I have found my favorite smoking balconey in the city .", "postag": ["RB", "NN", "IN", "NN", "NNS", "VBN", "IN", "NNP", "NNP", ",", "CC", "PRP", "VBP", "VBN", "PRP$", "JJ", "NN", "NN", "IN", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 5, 9, 9, 6, 14, 14, 14, 14, 2, 18, 18, 18, 14, 21, 21, 14, 2], "deprel": ["advmod", "root", "case", "compound", "nmod", "acl", "case", "compound", "obl", "punct", "cc", "nsubj", "aux", "conj", "nmod:poss", "amod", "compound", "obj", "case", "det", "obl", "punct"], "triples": [{"uid": "en_<PERSON><PERSON><PERSON>'s_478606538:8-0", "target_tags": "Not\\O alot\\O of\\O smoking\\O places\\O left\\O in\\O New\\O York\\O ,\\O but\\O I\\O have\\O found\\O my\\O favorite\\O smoking\\O balconey\\B in\\O the\\O city\\O .\\O", "opinion_tags": "Not\\O alot\\O of\\O smoking\\O places\\O left\\O in\\O New\\O York\\O ,\\O but\\O I\\O have\\O found\\O my\\O favorite\\B smoking\\O balconey\\O in\\O the\\O city\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218520:1", "sentence": "The sushi here is delicious !", "postag": ["DT", "NN", "RB", "VBZ", "JJ", "."], "head": [2, 5, 2, 5, 0, 5], "deprel": ["det", "nsubj", "advmod", "cop", "root", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218520:1-0", "target_tags": "The\\O sushi\\B here\\O is\\O delicious\\O !\\O", "opinion_tags": "The\\O sushi\\O here\\O is\\O delicious\\B !\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218520:2", "sentence": "They have a wide variety of fish and they even list which oceans they come from ; Atlantic or Pacific .", "postag": ["PRP", "VBP", "DT", "JJ", "NN", "IN", "NNS", "CC", "PRP", "RB", "VBP", "WDT", "NNS", "PRP", "VBP", "IN", ",", "NNP", "CC", "NNP", "."], "head": [2, 0, 5, 5, 2, 7, 5, 11, 11, 11, 2, 13, 15, 15, 11, 15, 18, 11, 20, 18, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "case", "nmod", "cc", "nsubj", "advmod", "conj", "det", "obl", "nsubj", "ccomp", "obl", "punct", "obj", "cc", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218520:2-0", "target_tags": "They\\O have\\O a\\O wide\\O variety\\O of\\O fish\\B and\\O they\\O even\\O list\\O which\\O oceans\\O they\\O come\\O from\\O ;\\O Atlantic\\O or\\O Pacific\\O .\\O", "opinion_tags": "They\\O have\\O a\\O wide\\B variety\\I of\\O fish\\O and\\O they\\O even\\O list\\O which\\O oceans\\O they\\O come\\O from\\O ;\\O Atlantic\\O or\\O Pacific\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218520:3", "sentence": "I 've had the <PERSON><PERSON><PERSON> , <PERSON> Mackerel , Blue Fin Tuna and the Sake Ikura roll among others , and they were all good .", "postag": ["PRP", "VBP", "VBN", "DT", "NNP", ",", "NNP", "NNP", ",", "NNP", "NNP", "NNP", "CC", "DT", "NNP", "NNP", "NN", "IN", "NNS", ",", "CC", "PRP", "VBD", "RB", "JJ", "."], "head": [3, 3, 0, 5, 3, 8, 8, 5, 12, 12, 12, 5, 17, 17, 17, 17, 5, 19, 3, 25, 25, 25, 25, 25, 3, 3], "deprel": ["nsubj", "aux", "root", "det", "obj", "punct", "compound", "conj", "punct", "compound", "compound", "conj", "cc", "det", "compound", "compound", "conj", "case", "obl", "punct", "cc", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218520:3-0", "target_tags": "I\\O 've\\O had\\O the\\O Jellyfish\\B ,\\O Horse\\O Mackerel\\O ,\\O Blue\\O Fin\\O Tuna\\O and\\O the\\O Sake\\O Ikura\\O roll\\O among\\O others\\O ,\\O and\\O they\\O were\\O all\\O good\\O .\\O", "opinion_tags": "I\\O 've\\O had\\O the\\O Jellyfish\\O ,\\O Horse\\O Mackerel\\O ,\\O Blue\\O Fin\\O Tuna\\O and\\O the\\O Sake\\O Ikura\\O roll\\O among\\O others\\O ,\\O and\\O they\\O were\\O all\\O good\\B .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218520:3-1", "target_tags": "I\\O 've\\O had\\O the\\O Jellyfish\\O ,\\O Horse\\B Mackerel\\I ,\\O Blue\\O Fin\\O Tuna\\O and\\O the\\O Sake\\O Ikura\\O roll\\O among\\O others\\O ,\\O and\\O they\\O were\\O all\\O good\\O .\\O", "opinion_tags": "I\\O 've\\O had\\O the\\O Jellyfish\\O ,\\O Horse\\O Mackerel\\O ,\\O Blue\\O Fin\\O Tuna\\O and\\O the\\O Sake\\O Ikura\\O roll\\O among\\O others\\O ,\\O and\\O they\\O were\\O all\\O good\\B .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218520:3-2", "target_tags": "I\\O 've\\O had\\O the\\O Jellyfish\\O ,\\O Horse\\O Mackerel\\O ,\\O Blue\\B Fin\\I Tuna\\I and\\O the\\O Sake\\O Ikura\\O roll\\O among\\O others\\O ,\\O and\\O they\\O were\\O all\\O good\\O .\\O", "opinion_tags": "I\\O 've\\O had\\O the\\O Jellyfish\\O ,\\O Horse\\O Mackerel\\O ,\\O Blue\\O Fin\\O Tuna\\O and\\O the\\O Sake\\O Ikura\\O roll\\O among\\O others\\O ,\\O and\\O they\\O were\\O all\\O good\\B .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218520:3-3", "target_tags": "I\\O 've\\O had\\O the\\O Jellyfish\\O ,\\O Horse\\O Mackerel\\O ,\\O Blue\\O Fin\\O Tuna\\O and\\O the\\O Sake\\B Ikura\\I roll\\I among\\O others\\O ,\\O and\\O they\\O were\\O all\\O good\\O .\\O", "opinion_tags": "I\\O 've\\O had\\O the\\O Jellyfish\\O ,\\O Horse\\O Mackerel\\O ,\\O Blue\\O Fin\\O Tuna\\O and\\O the\\O Sake\\O Ikura\\O roll\\O among\\O others\\O ,\\O and\\O they\\O were\\O all\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218520:5", "sentence": "The decor is rustic , traditional Japanese .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "JJ", "JJ", "."], "head": [2, 4, 4, 0, 4, 7, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "advmod", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218520:5-0", "target_tags": "The\\O decor\\B is\\O rustic\\O ,\\O traditional\\O Japanese\\O .\\O", "opinion_tags": "The\\O decor\\O is\\O rustic\\B ,\\O traditional\\B Japanese\\I .\\O", "sentiment": "neutral"}]}, {"id": "en_BlueRibbonSushi_478218520:7", "sentence": "The service was courteous and attentive .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218520:7-0", "target_tags": "The\\O service\\B was\\O courteous\\O and\\O attentive\\O .\\O", "opinion_tags": "The\\O service\\O was\\O courteous\\B and\\O attentive\\B .\\O", "sentiment": "positive"}]}, {"id": "en_CatalRestaurant__UvaBar_477861889:0", "sentence": "Mediocre food", "postag": ["JJ", "NN"], "head": [2, 0], "deprel": ["amod", "root"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477861889:0-0", "target_tags": "Mediocre\\O food\\B", "opinion_tags": "Mediocre\\B food\\O", "sentiment": "neutral"}]}, {"id": "en_CatalRestaurant__UvaBar_477861889:2", "sentence": "The outside patio area has an abbreviated menu .", "postag": ["DT", "JJ", "NN", "NN", "VBZ", "DT", "VBN", "NN", "."], "head": [4, 4, 4, 5, 0, 8, 8, 5, 5], "deprel": ["det", "amod", "compound", "nsubj", "root", "det", "amod", "obj", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477861889:2-0", "target_tags": "The\\O outside\\O patio\\O area\\O has\\O an\\O abbreviated\\O menu\\B .\\O", "opinion_tags": "The\\O outside\\O patio\\O area\\O has\\O an\\O abbreviated\\B menu\\O .\\O", "sentiment": "neutral"}]}, {"id": "en_CatalRestaurant__UvaBar_477861889:4", "sentence": "My g/f and I both agreed the food was very mediocre especially considering the price .", "postag": ["PRP$", "NN", "CC", "PRP", "DT", "VBD", "DT", "NN", "VBD", "RB", "JJ", "RB", "VBG", "DT", "NN", "."], "head": [2, 6, 4, 2, 4, 0, 8, 11, 11, 11, 6, 13, 11, 15, 13, 6], "deprel": ["nmod:poss", "nsubj", "cc", "conj", "det", "root", "det", "nsubj", "cop", "advmod", "ccomp", "advmod", "advcl", "det", "obj", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477861889:4-0", "target_tags": "My\\O g/f\\O and\\O I\\O both\\O agreed\\O the\\O food\\B was\\O very\\O mediocre\\O especially\\O considering\\O the\\O price\\O .\\O", "opinion_tags": "My\\O g/f\\O and\\O I\\O both\\O agreed\\O the\\O food\\O was\\O very\\O mediocre\\B especially\\O considering\\O the\\O price\\O .\\O", "sentiment": "negative"}]}, {"id": "en_CatalRestaurant__UvaBar_477861889:5", "sentence": "We are locals , and get the feeling the only way this place survives with such average food is because most customers are probably one-time customer tourists .", "postag": ["PRP", "VBP", "NNS", ",", "CC", "VBP", "DT", "NN", "DT", "JJ", "NN", "DT", "NN", "VBZ", "IN", "JJ", "JJ", "NN", "VBZ", "IN", "JJS", "NNS", "VBP", "RB", "JJ", "NN", "NNS", "."], "head": [3, 3, 0, 6, 6, 3, 8, 6, 11, 11, 19, 13, 14, 11, 18, 18, 18, 14, 3, 27, 22, 27, 27, 27, 27, 27, 19, 3], "deprel": ["nsubj", "cop", "root", "punct", "cc", "conj", "det", "obj", "det", "amod", "nsubj", "det", "nsubj", "acl:relcl", "case", "amod", "amod", "obl", "conj", "mark", "amod", "nsubj", "cop", "advmod", "amod", "compound", "advcl", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477861889:5-0", "target_tags": "We\\O are\\O locals\\O ,\\O and\\O get\\O the\\O feeling\\O the\\O only\\O way\\O this\\O place\\O survives\\O with\\O such\\O average\\O food\\B is\\O because\\O most\\O customers\\O are\\O probably\\O one-time\\O customer\\O tourists\\O .\\O", "opinion_tags": "We\\O are\\O locals\\O ,\\O and\\O get\\O the\\O feeling\\O the\\O only\\O way\\O this\\O place\\O survives\\O with\\O such\\O average\\B food\\O is\\O because\\O most\\O customers\\O are\\O probably\\O one-time\\O customer\\O tourists\\O .\\O", "sentiment": "negative"}]}, {"id": "en_CatalRestaurant__UvaBar_477861889:6", "sentence": "Service was decent .", "postag": ["NN", "VBD", "JJ", "."], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477861889:6-0", "target_tags": "Service\\B was\\O decent\\O .\\O", "opinion_tags": "Service\\O was\\O decent\\B .\\O", "sentiment": "neutral"}]}, {"id": "en_CatalRestaurant__UvaBar_477861889:7", "sentence": "Drinks were good .", "postag": ["NNS", "VBD", "JJ", "."], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477861889:7-0", "target_tags": "Drinks\\B were\\O good\\O .\\O", "opinion_tags": "Drinks\\O were\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "en_CatalRestaurant__UvaBar_477861893:0", "sentence": "Excellent food , nice ambience , fairly expensive", "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "RB", "JJ"], "head": [2, 0, 5, 5, 2, 8, 8, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "advmod", "parataxis"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477861893:0-0", "target_tags": "Excellent\\O food\\B ,\\O nice\\O ambience\\O ,\\O fairly\\O expensive\\O", "opinion_tags": "Excellent\\B food\\O ,\\O nice\\O ambience\\O ,\\O fairly\\O expensive\\O", "sentiment": "positive"}, {"uid": "en_CatalRestaurant__UvaBar_477861893:0-1", "target_tags": "Excellent\\O food\\O ,\\O nice\\O ambience\\B ,\\O fairly\\O expensive\\O", "opinion_tags": "Excellent\\O food\\O ,\\O nice\\B ambience\\O ,\\O fairly\\O expensive\\O", "sentiment": "positive"}]}, {"id": "en_CatalRestaurant__UvaBar_477861893:1", "sentence": "– I loved the pumpkin ravioli and the goat cheese gnocchi ( 5 big ones to a plate instead of 20 or so little gnocchis ) and my sister loved her filet mignon on top of spinach and mashed potatoes .", "postag": ["NFP", "PRP", "VBD", "DT", "NN", "NN", "CC", "DT", "NN", "NN", "NN", "-LRB-", "CD", "JJ", "NNS", "IN", "DT", "NN", "RB", "IN", "CD", "CC", "RB", "JJ", "NN", "-RRB-", "CC", "PRP$", "NN", "VBD", "PRP$", "NN", "NN", "IN", "NN", "IN", "NN", "CC", "VBD", "NNS", "."], "head": [3, 3, 0, 6, 6, 3, 11, 11, 10, 11, 6, 15, 15, 15, 6, 18, 18, 15, 25, 19, 25, 23, 21, 25, 15, 15, 30, 29, 30, 3, 33, 33, 30, 35, 30, 37, 35, 39, 30, 39, 3], "deprel": ["punct", "nsubj", "root", "det", "compound", "obj", "cc", "det", "compound", "compound", "conj", "punct", "nummod", "amod", "appos", "case", "det", "nmod", "case", "fixed", "nummod", "cc", "conj", "amod", "nmod", "punct", "cc", "nmod:poss", "nsubj", "conj", "nmod:poss", "compound", "obj", "case", "obl", "case", "nmod", "cc", "conj", "obj", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477861893:1-0", "target_tags": "–\\O I\\O loved\\O the\\O pumpkin\\B ravioli\\I and\\O the\\O goat\\O cheese\\O gnocchi\\O (\\O 5\\O big\\O ones\\O to\\O a\\O plate\\O instead\\O of\\O 20\\O or\\O so\\O little\\O gnocchis\\O )\\O and\\O my\\O sister\\O loved\\O her\\O filet\\O mignon\\O on\\O top\\O of\\O spinach\\O and\\O mashed\\O potatoes\\O .\\O", "opinion_tags": "–\\O I\\O loved\\B the\\O pumpkin\\O ravioli\\O and\\O the\\O goat\\O cheese\\O gnocchi\\O (\\O 5\\O big\\O ones\\O to\\O a\\O plate\\O instead\\O of\\O 20\\O or\\O so\\O little\\O gnocchis\\O )\\O and\\O my\\O sister\\O loved\\O her\\O filet\\O mignon\\O on\\O top\\O of\\O spinach\\O and\\O mashed\\O potatoes\\O .\\O", "sentiment": "positive"}, {"uid": "en_CatalRestaurant__UvaBar_477861893:1-1", "target_tags": "–\\O I\\O loved\\O the\\O pumpkin\\O ravioli\\O and\\O the\\O goat\\B cheese\\I gnocchi\\I (\\O 5\\O big\\O ones\\O to\\O a\\O plate\\O instead\\O of\\O 20\\O or\\O so\\O little\\O gnocchis\\O )\\O and\\O my\\O sister\\O loved\\O her\\O filet\\O mignon\\O on\\O top\\O of\\O spinach\\O and\\O mashed\\O potatoes\\O .\\O", "opinion_tags": "–\\O I\\O loved\\B the\\O pumpkin\\O ravioli\\O and\\O the\\O goat\\O cheese\\O gnocchi\\O (\\O 5\\O big\\O ones\\O to\\O a\\O plate\\O instead\\O of\\O 20\\O or\\O so\\O little\\O gnocchis\\O )\\O and\\O my\\O sister\\O loved\\O her\\O filet\\O mignon\\O on\\O top\\O of\\O spinach\\O and\\O mashed\\O potatoes\\O .\\O", "sentiment": "positive"}, {"uid": "en_CatalRestaurant__UvaBar_477861893:1-2", "target_tags": "–\\O I\\O loved\\O the\\O pumpkin\\O ravioli\\O and\\O the\\O goat\\O cheese\\O gnocchi\\O (\\O 5\\O big\\O ones\\O to\\O a\\O plate\\O instead\\O of\\O 20\\O or\\O so\\O little\\O gnocchis\\O )\\O and\\O my\\O sister\\O loved\\O her\\O filet\\B mignon\\I on\\I top\\I of\\I spinach\\I and\\I mashed\\I potatoes\\I .\\O", "opinion_tags": "–\\O I\\O loved\\O the\\O pumpkin\\O ravioli\\O and\\O the\\O goat\\O cheese\\O gnocchi\\O (\\O 5\\O big\\O ones\\O to\\O a\\O plate\\O instead\\O of\\O 20\\O or\\O so\\O little\\O gnocchis\\O )\\O and\\O my\\O sister\\O loved\\B her\\O filet\\O mignon\\O on\\O top\\O of\\O spinach\\O and\\O mashed\\O potatoes\\O .\\O", "sentiment": "positive"}]}, {"id": "en_CatalRestaurant__UvaBar_477861893:2", "sentence": "The ambiance was a peaceful and relaxing break amongst all the kids running around in Downtown Disney .", "postag": ["DT", "NN", "VBD", "DT", "JJ", "CC", "JJ", "NN", "IN", "PDT", "DT", "NNS", "VBG", "RB", "IN", "NN", "NNP", "."], "head": [2, 8, 8, 8, 8, 7, 5, 0, 12, 12, 12, 8, 12, 13, 17, 17, 13, 8], "deprel": ["det", "nsubj", "cop", "det", "amod", "cc", "conj", "root", "case", "det:predet", "det", "nmod", "acl", "advmod", "case", "amod", "obl", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477861893:2-0", "target_tags": "The\\O ambiance\\B was\\O a\\O peaceful\\O and\\O relaxing\\O break\\O amongst\\O all\\O the\\O kids\\O running\\O around\\O in\\O Downtown\\O Disney\\O .\\O", "opinion_tags": "The\\O ambiance\\O was\\O a\\O peaceful\\B and\\O relaxing\\B break\\O amongst\\O all\\O the\\O kids\\O running\\O around\\O in\\O Downtown\\O Disney\\O .\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960693:0", "sentence": "Best Indian food in L.A .", "postag": ["JJS", "JJ", "NN", "IN", "NNP", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["amod", "amod", "root", "case", "nmod", "punct"], "triples": [{"uid": "en_India'sGrill_477960693:0-0", "target_tags": "Best\\O Indian\\B food\\I in\\O L.A\\O .\\O", "opinion_tags": "Best\\B Indian\\O food\\O in\\O L.A\\O .\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960693:3", "sentence": "The chicken curry and chicken tikka masala are my favorite meat dishes .", "postag": ["DT", "NN", "NN", "CC", "NN", "NN", "NN", "VBP", "PRP$", "JJ", "NN", "NNS", "."], "head": [3, 3, 12, 7, 6, 7, 3, 12, 12, 12, 12, 0, 12], "deprel": ["det", "compound", "nsubj", "cc", "compound", "compound", "conj", "cop", "nmod:poss", "amod", "compound", "root", "punct"], "triples": [{"uid": "en_India'sGrill_477960693:3-0", "target_tags": "The\\O chicken\\B curry\\I and\\O chicken\\O tikka\\O masala\\O are\\O my\\O favorite\\O meat\\O dishes\\O .\\O", "opinion_tags": "The\\O chicken\\O curry\\O and\\O chicken\\O tikka\\O masala\\O are\\O my\\O favorite\\B meat\\O dishes\\O .\\O", "sentiment": "positive"}, {"uid": "en_India'sGrill_477960693:3-1", "target_tags": "The\\O chicken\\O curry\\O and\\O chicken\\B tikka\\I masala\\I are\\O my\\O favorite\\O meat\\O dishes\\O .\\O", "opinion_tags": "The\\O chicken\\O curry\\O and\\O chicken\\O tikka\\O masala\\O are\\O my\\O favorite\\B meat\\O dishes\\O .\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960693:4", "sentence": "The chana masala ( garbanzo beans ) are also excellent .", "postag": ["DT", "NN", "NN", "-LRB-", "NN", "NNS", "-RRB-", "VBP", "RB", "JJ", "."], "head": [3, 3, 10, 6, 6, 3, 6, 10, 10, 0, 10], "deprel": ["det", "compound", "nsubj", "punct", "compound", "appos", "punct", "cop", "advmod", "root", "punct"], "triples": [{"uid": "en_India'sGrill_477960693:4-0", "target_tags": "The\\O chana\\B masala\\I (\\I garbanzo\\I beans\\I )\\I are\\O also\\O excellent\\O .\\O", "opinion_tags": "The\\O chana\\O masala\\O (\\O garbanzo\\O beans\\O )\\O are\\O also\\O excellent\\B .\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960693:5", "sentence": "It 's located in a strip mall near the Beverly Center , not the greatest location , but the food keeps me coming back for more .", "postag": ["PRP", "VBZ", "VBN", "IN", "DT", "NN", "NN", "IN", "DT", "NNP", "NNP", ",", "RB", "DT", "JJS", "NN", ",", "CC", "DT", "NN", "VBZ", "PRP", "VBG", "RB", "IN", "JJR", "."], "head": [3, 3, 0, 7, 7, 7, 3, 11, 11, 11, 7, 16, 16, 16, 16, 11, 21, 21, 20, 21, 3, 21, 21, 23, 26, 23, 3], "deprel": ["nsubj:pass", "aux:pass", "root", "case", "det", "compound", "obl", "case", "det", "compound", "nmod", "punct", "advmod", "det", "amod", "conj", "punct", "cc", "det", "nsubj", "conj", "obj", "xcomp", "advmod", "case", "obl", "punct"], "triples": [{"uid": "en_India'sGrill_477960693:5-0", "target_tags": "It\\O 's\\O located\\O in\\O a\\O strip\\O mall\\O near\\O the\\O Beverly\\O Center\\O ,\\O not\\O the\\O greatest\\O location\\B ,\\O but\\O the\\O food\\O keeps\\O me\\O coming\\O back\\O for\\O more\\O .\\O", "opinion_tags": "It\\O 's\\O located\\O in\\O a\\O strip\\O mall\\O near\\O the\\O Beverly\\O Center\\O ,\\O not\\B the\\I greatest\\I location\\O ,\\O but\\O the\\O food\\O keeps\\O me\\O coming\\O back\\O for\\O more\\O .\\O", "sentiment": "neutral"}]}, {"id": "en_India'sGrill_477960694:2", "sentence": "Never too crowded and always great service .", "postag": ["RB", "RB", "JJ", "CC", "RB", "JJ", "NN", "."], "head": [3, 3, 0, 7, 6, 7, 3, 3], "deprel": ["advmod", "advmod", "root", "cc", "advmod", "amod", "conj", "punct"], "triples": [{"uid": "en_India'sGrill_477960694:2-0", "target_tags": "Never\\O too\\O crowded\\O and\\O always\\O great\\O service\\B .\\O", "opinion_tags": "Never\\O too\\O crowded\\O and\\O always\\O great\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960694:4", "sentence": "I can highly recommend their various saag and paneer and korma .", "postag": ["PRP", "MD", "RB", "VB", "PRP$", "JJ", "NN", "CC", "NN", "CC", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 9, 7, 11, 7, 4], "deprel": ["nsubj", "aux", "advmod", "root", "nmod:poss", "amod", "obj", "cc", "conj", "cc", "conj", "punct"], "triples": [{"uid": "en_India'sGrill_477960694:4-0", "target_tags": "I\\O can\\O highly\\O recommend\\O their\\O various\\O saag\\B and\\O paneer\\O and\\O korma\\O .\\O", "opinion_tags": "I\\O can\\O highly\\O recommend\\B their\\O various\\O saag\\O and\\O paneer\\O and\\O korma\\O .\\O", "sentiment": "positive"}, {"uid": "en_India'sGrill_477960694:4-1", "target_tags": "I\\O can\\O highly\\O recommend\\O their\\O various\\O saag\\O and\\O paneer\\B and\\O korma\\O .\\O", "opinion_tags": "I\\O can\\O highly\\O recommend\\B their\\O various\\O saag\\O and\\O paneer\\O and\\O korma\\O .\\O", "sentiment": "positive"}, {"uid": "en_India'sGrill_477960694:4-2", "target_tags": "I\\O can\\O highly\\O recommend\\O their\\O various\\O saag\\O and\\O paneer\\O and\\O korma\\B .\\O", "opinion_tags": "I\\O can\\O highly\\O recommend\\B their\\O various\\O saag\\O and\\O paneer\\O and\\O korma\\O .\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960694:6", "sentence": "I appreciate their delivery too .", "postag": ["PRP", "VBP", "PRP$", "NN", "RB", "."], "head": [2, 0, 4, 2, 2, 2], "deprel": ["nsubj", "root", "nmod:poss", "obj", "advmod", "punct"], "triples": [{"uid": "en_India'sGrill_477960694:6-0", "target_tags": "I\\O appreciate\\O their\\O delivery\\B too\\O .\\O", "opinion_tags": "I\\O appreciate\\B their\\O delivery\\O too\\O .\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960698:0", "sentence": "Nice food but no spice !", "postag": ["JJ", "NN", "CC", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["amod", "root", "cc", "det", "conj", "punct"], "triples": [{"uid": "en_India'sGrill_477960698:0-0", "target_tags": "Nice\\O food\\B but\\O no\\O spice\\O !\\O", "opinion_tags": "Nice\\B food\\O but\\O no\\O spice\\O !\\O", "sentiment": "negative"}]}, {"id": "en_India'sGrill_477960698:1", "sentence": "– I really enjoyed my meal here .", "postag": [":", "PRP", "RB", "VBD", "PRP$", "NN", "RB", "."], "head": [4, 4, 4, 0, 6, 4, 4, 4], "deprel": ["punct", "nsubj", "advmod", "root", "nmod:poss", "obj", "advmod", "punct"], "triples": [{"uid": "en_India'sGrill_477960698:1-0", "target_tags": "–\\O I\\O really\\O enjoyed\\O my\\O meal\\B here\\O .\\O", "opinion_tags": "–\\O I\\O really\\O enjoyed\\B my\\O meal\\O here\\O .\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960698:2", "sentence": "I had yummy lamb korma , saag paneer , samosas , naan , etc .", "postag": ["PRP", "VBD", "JJ", "NN", "NN", ",", "NN", "NN", ",", "NNS", ",", "NN", ",", "FW", "."], "head": [2, 0, 5, 5, 2, 5, 8, 5, 5, 5, 5, 5, 5, 5, 2], "deprel": ["nsubj", "root", "amod", "compound", "obj", "punct", "compound", "list", "punct", "list", "punct", "list", "punct", "list", "punct"], "triples": [{"uid": "en_India'sGrill_477960698:2-0", "target_tags": "I\\O had\\O yummy\\O lamb\\B korma\\I ,\\O saag\\O paneer\\O ,\\O samosas\\O ,\\O naan\\O ,\\O etc\\O .\\O", "opinion_tags": "I\\O had\\O yummy\\B lamb\\O korma\\O ,\\O saag\\O paneer\\O ,\\O samosas\\O ,\\O naan\\O ,\\O etc\\O .\\O", "sentiment": "positive"}, {"uid": "en_India'sGrill_477960698:2-1", "target_tags": "I\\O had\\O yummy\\O lamb\\O korma\\O ,\\O saag\\B paneer\\I ,\\O samosas\\O ,\\O naan\\O ,\\O etc\\O .\\O", "opinion_tags": "I\\O had\\O yummy\\B lamb\\O korma\\O ,\\O saag\\O paneer\\O ,\\O samosas\\O ,\\O naan\\O ,\\O etc\\O .\\O", "sentiment": "positive"}, {"uid": "en_India'sGrill_477960698:2-2", "target_tags": "I\\O had\\O yummy\\O lamb\\O korma\\O ,\\O saag\\O paneer\\O ,\\O samosas\\B ,\\O naan\\O ,\\O etc\\O .\\O", "opinion_tags": "I\\O had\\O yummy\\B lamb\\O korma\\O ,\\O saag\\O paneer\\O ,\\O samosas\\O ,\\O naan\\O ,\\O etc\\O .\\O", "sentiment": "positive"}, {"uid": "en_India'sGrill_477960698:2-3", "target_tags": "I\\O had\\O yummy\\O lamb\\O korma\\O ,\\O saag\\O paneer\\O ,\\O samosas\\O ,\\O naan\\B ,\\O etc\\O .\\O", "opinion_tags": "I\\O had\\O yummy\\B lamb\\O korma\\O ,\\O saag\\O paneer\\O ,\\O samosas\\O ,\\O naan\\O ,\\O etc\\O .\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960698:3", "sentence": "The food was all good but it was way too mild .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "PRP", "VBD", "RB", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 11, 11, 11, 11, 11, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "nsubj", "cop", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "en_India'sGrill_477960698:3-0", "target_tags": "The\\O food\\B was\\O all\\O good\\O but\\O it\\O was\\O way\\O too\\O mild\\O .\\O", "opinion_tags": "The\\O food\\O was\\O all\\O good\\B but\\O it\\O was\\O way\\O too\\B mild\\I .\\O", "sentiment": "negative"}]}, {"id": "en_India'sGrill_477960698:7", "sentence": "The naan was some of the best I 've had and I really enjoyed the bhartha , not too tomatoey .", "postag": ["DT", "NN", "VBD", "DT", "IN", "DT", "JJS", "PRP", "VBP", "VBN", "CC", "PRP", "RB", "VBD", "DT", "NN", ",", "RB", "RB", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 10, 10, 7, 14, 14, 14, 4, 16, 14, 20, 20, 20, 16, 4], "deprel": ["det", "nsubj", "cop", "root", "case", "det", "nmod", "nsubj", "aux", "acl:relcl", "cc", "nsubj", "advmod", "conj", "det", "obj", "punct", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "en_India'sGrill_477960698:7-0", "target_tags": "The\\O naan\\B was\\O some\\O of\\O the\\O best\\O I\\O 've\\O had\\O and\\O I\\O really\\O enjoyed\\O the\\O bhartha\\O ,\\O not\\O too\\O tomatoey\\O .\\O", "opinion_tags": "The\\O naan\\O was\\O some\\O of\\O the\\O best\\B I\\O 've\\O had\\O and\\O I\\O really\\O enjoyed\\O the\\O bhartha\\O ,\\O not\\O too\\O tomatoey\\O .\\O", "sentiment": "positive"}, {"uid": "en_India'sGrill_477960698:7-1", "target_tags": "The\\O naan\\O was\\O some\\O of\\O the\\O best\\O I\\O 've\\O had\\O and\\O I\\O really\\O enjoyed\\O the\\O bhartha\\B ,\\O not\\O too\\O tomatoey\\O .\\O", "opinion_tags": "The\\O naan\\O was\\O some\\O of\\O the\\O best\\O I\\O 've\\O had\\O and\\O I\\O really\\O enjoyed\\B the\\O bhartha\\O ,\\O not\\O too\\O tomatoey\\O .\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960698:8", "sentence": "Even the chickpeas , which I normally find too dry , were good .", "postag": ["RB", "DT", "NNS", ",", "WDT", "PRP", "RB", "VBP", "RB", "JJ", ",", "VBD", "JJ", "."], "head": [3, 3, 13, 3, 8, 8, 8, 3, 10, 8, 13, 13, 0, 13], "deprel": ["advmod", "det", "nsubj", "punct", "obj", "nsubj", "advmod", "acl:relcl", "advmod", "xcomp", "punct", "cop", "root", "punct"], "triples": [{"uid": "en_India'sGrill_477960698:8-0", "target_tags": "Even\\O the\\O chickpeas\\B ,\\O which\\O I\\O normally\\O find\\O too\\O dry\\O ,\\O were\\O good\\O .\\O", "opinion_tags": "Even\\O the\\O chickpeas\\O ,\\O which\\O I\\O normally\\O find\\O too\\O dry\\O ,\\O were\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010599:1", "sentence": "– I do n't understand how I was a stranger to this place for so long ... the fajita salad , the colorado , the fajitas - EVERYTHING is delicious .", "postag": [":", "PRP", "VBP", "RB", "VB", "WRB", "PRP", "VBD", "DT", "NN", "IN", "DT", "NN", "IN", "RB", "RB", ",", "DT", "NN", "NN", ",", "DT", "NN", ",", "DT", "NNS", ",", "NN", "VBZ", "JJ", "."], "head": [5, 5, 5, 5, 0, 10, 10, 10, 10, 5, 13, 13, 10, 16, 16, 10, 5, 20, 20, 5, 20, 23, 20, 20, 26, 20, 5, 30, 30, 5, 5], "deprel": ["punct", "nsubj", "aux", "advmod", "root", "mark", "nsubj", "cop", "det", "ccomp", "case", "det", "nmod", "case", "advmod", "obl", "punct", "det", "compound", "parataxis", "punct", "det", "list", "punct", "det", "list", "punct", "nsubj", "cop", "parataxis", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010599:1-0", "target_tags": "–\\O I\\O do\\O n't\\O understand\\O how\\O I\\O was\\O a\\O stranger\\O to\\O this\\O place\\O for\\O so\\O long\\O ...\\O the\\O fajita\\B salad\\I ,\\O the\\O colorado\\O ,\\O the\\O fajitas\\O -\\O EVERYTHING\\O is\\O delicious\\O .\\O", "opinion_tags": "–\\O I\\O do\\O n't\\O understand\\O how\\O I\\O was\\O a\\O stranger\\O to\\O this\\O place\\O for\\O so\\O long\\O ...\\O the\\O fajita\\O salad\\O ,\\O the\\O colorado\\O ,\\O the\\O fajitas\\O -\\O EVERYTHING\\O is\\O delicious\\B .\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_478010599:1-1", "target_tags": "–\\O I\\O do\\O n't\\O understand\\O how\\O I\\O was\\O a\\O stranger\\O to\\O this\\O place\\O for\\O so\\O long\\O ...\\O the\\O fajita\\O salad\\O ,\\O the\\O colorado\\B ,\\O the\\O fajitas\\O -\\O EVERYTHING\\O is\\O delicious\\O .\\O", "opinion_tags": "–\\O I\\O do\\O n't\\O understand\\O how\\O I\\O was\\O a\\O stranger\\O to\\O this\\O place\\O for\\O so\\O long\\O ...\\O the\\O fajita\\O salad\\O ,\\O the\\O colorado\\O ,\\O the\\O fajitas\\O -\\O EVERYTHING\\O is\\O delicious\\B .\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_478010599:1-2", "target_tags": "–\\O I\\O do\\O n't\\O understand\\O how\\O I\\O was\\O a\\O stranger\\O to\\O this\\O place\\O for\\O so\\O long\\O ...\\O the\\O fajita\\O salad\\O ,\\O the\\O colorado\\O ,\\O the\\O fajitas\\B -\\O EVERYTHING\\O is\\O delicious\\O .\\O", "opinion_tags": "–\\O I\\O do\\O n't\\O understand\\O how\\O I\\O was\\O a\\O stranger\\O to\\O this\\O place\\O for\\O so\\O long\\O ...\\O the\\O fajita\\O salad\\O ,\\O the\\O colorado\\O ,\\O the\\O fajitas\\O -\\O EVERYTHING\\O is\\O delicious\\B .\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010599:2", "sentence": "I love the warm & cosy environment .", "postag": ["PRP", "VBP", "DT", "JJ", "CC", "JJ", "NN", "."], "head": [2, 0, 7, 7, 6, 4, 2, 2], "deprel": ["nsubj", "root", "det", "amod", "cc", "conj", "obj", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010599:2-0", "target_tags": "I\\O love\\O the\\O warm\\O &\\O cosy\\O environment\\B .\\O", "opinion_tags": "I\\O love\\O the\\O warm\\B &\\O cosy\\B environment\\O .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970770:0", "sentence": "best restaurant in the world , great decor , great customer service , friendly manager", "postag": ["JJS", "NN", "IN", "DT", "NN", ",", "JJ", "NN", ",", "JJ", "NN", "NN", ",", "JJ", "NN"], "head": [2, 0, 5, 5, 2, 2, 8, 2, 12, 12, 12, 2, 15, 15, 2], "deprel": ["amod", "root", "case", "det", "nmod", "punct", "amod", "list", "punct", "amod", "compound", "conj", "punct", "amod", "list"], "triples": [{"uid": "en_OpenSesame_477970770:0-0", "target_tags": "best\\O restaurant\\B in\\O the\\O world\\O ,\\O great\\O decor\\O ,\\O great\\O customer\\O service\\O ,\\O friendly\\O manager\\O", "opinion_tags": "best\\B restaurant\\O in\\O the\\O world\\O ,\\O great\\O decor\\O ,\\O great\\O customer\\O service\\O ,\\O friendly\\O manager\\O", "sentiment": "positive"}, {"uid": "en_OpenSesame_477970770:0-1", "target_tags": "best\\O restaurant\\O in\\O the\\O world\\O ,\\O great\\O decor\\B ,\\O great\\O customer\\O service\\O ,\\O friendly\\O manager\\O", "opinion_tags": "best\\O restaurant\\O in\\O the\\O world\\O ,\\O great\\B decor\\O ,\\O great\\O customer\\O service\\O ,\\O friendly\\O manager\\O", "sentiment": "positive"}, {"uid": "en_OpenSesame_477970770:0-2", "target_tags": "best\\O restaurant\\O in\\O the\\O world\\O ,\\O great\\O decor\\O ,\\O great\\O customer\\B service\\I ,\\O friendly\\O manager\\O", "opinion_tags": "best\\O restaurant\\O in\\O the\\O world\\O ,\\O great\\O decor\\O ,\\O great\\B customer\\O service\\O ,\\O friendly\\O manager\\O", "sentiment": "positive"}, {"uid": "en_OpenSesame_477970770:0-3", "target_tags": "best\\O restaurant\\O in\\O the\\O world\\O ,\\O great\\O decor\\O ,\\O great\\O customer\\O service\\O ,\\O friendly\\O manager\\B", "opinion_tags": "best\\O restaurant\\O in\\O the\\O world\\O ,\\O great\\O decor\\O ,\\O great\\O customer\\O service\\O ,\\O friendly\\B manager\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970770:3", "sentence": "i am never disappointed with there food .", "postag": ["PRP", "VBP", "RB", "JJ", "IN", "PRP$", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "en_OpenSesame_477970770:3-0", "target_tags": "i\\O am\\O never\\O disappointed\\O with\\O there\\O food\\B .\\O", "opinion_tags": "i\\O am\\O never\\B disappointed\\I with\\O there\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970770:4", "sentence": "the atmosphere is great .", "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_OpenSesame_477970770:4-0", "target_tags": "the\\O atmosphere\\B is\\O great\\O .\\O", "opinion_tags": "the\\O atmosphere\\O is\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010601:0", "sentence": "great lunch spot", "postag": ["JJ", "NN", "NN"], "head": [3, 3, 0], "deprel": ["amod", "compound", "root"], "triples": [{"uid": "en_MercedesRestaurant_478010601:0-0", "target_tags": "great\\O lunch\\B spot\\I", "opinion_tags": "great\\B lunch\\O spot\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010601:1", "sentence": "– Great financial district mexican spot .", "postag": ["NFP", "JJ", "JJ", "NN", "JJ", "NN", "."], "head": [6, 6, 4, 6, 6, 0, 6], "deprel": ["punct", "amod", "amod", "compound", "amod", "root", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010601:1-0", "target_tags": "–\\O Great\\O financial\\O district\\O mexican\\B spot\\I .\\O", "opinion_tags": "–\\O Great\\B financial\\O district\\O mexican\\O spot\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010601:2", "sentence": "Always busy , but they are good at seating you promptly and have quick service .", "postag": ["RB", "JJ", ",", "CC", "PRP", "VBP", "JJ", "IN", "VBG", "PRP", "RB", "CC", "VBP", "JJ", "NN", "."], "head": [2, 0, 7, 7, 7, 7, 2, 9, 7, 9, 9, 13, 7, 15, 13, 2], "deprel": ["advmod", "root", "punct", "cc", "nsubj", "cop", "conj", "mark", "advcl", "obj", "advmod", "cc", "conj", "amod", "obj", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010601:2-0", "target_tags": "Always\\O busy\\O ,\\O but\\O they\\O are\\O good\\O at\\O seating\\O you\\O promptly\\O and\\O have\\O quick\\O service\\B .\\O", "opinion_tags": "Always\\O busy\\O ,\\O but\\O they\\O are\\O good\\O at\\O seating\\O you\\O promptly\\O and\\O have\\O quick\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010601:3", "sentence": "Everything I 've had here is good , taco salads , burritos , enchiladas i love this place .", "postag": ["NN", "PRP", "VBP", "VBN", "RB", "VBZ", "JJ", ",", "NN", "NNS", ",", "NNS", ",", "NNS", "PRP", "VBP", "DT", "NN", "."], "head": [7, 4, 4, 1, 4, 7, 0, 10, 10, 7, 12, 10, 14, 10, 16, 7, 18, 16, 7], "deprel": ["nsubj", "nsubj", "aux", "acl:relcl", "advmod", "cop", "root", "punct", "compound", "conj", "punct", "conj", "punct", "conj", "nsubj", "parataxis", "det", "obj", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010601:3-0", "target_tags": "Everything\\O I\\O 've\\O had\\O here\\O is\\O good\\O ,\\O taco\\B salads\\I ,\\O burritos\\O ,\\O enchiladas\\O i\\O love\\O this\\O place\\O .\\O", "opinion_tags": "Everything\\O I\\O 've\\O had\\O here\\O is\\O good\\B ,\\O taco\\O salads\\O ,\\O burritos\\O ,\\O enchiladas\\O i\\O love\\O this\\O place\\O .\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_478010601:3-1", "target_tags": "Everything\\O I\\O 've\\O had\\O here\\O is\\O good\\O ,\\O taco\\O salads\\O ,\\O burritos\\B ,\\O enchiladas\\O i\\O love\\O this\\O place\\O .\\O", "opinion_tags": "Everything\\O I\\O 've\\O had\\O here\\O is\\O good\\B ,\\O taco\\O salads\\O ,\\O burritos\\O ,\\O enchiladas\\O i\\O love\\O this\\O place\\O .\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_478010601:3-2", "target_tags": "Everything\\O I\\O 've\\O had\\O here\\O is\\O good\\O ,\\O taco\\O salads\\O ,\\O burritos\\O ,\\O enchiladas\\B i\\O love\\O this\\O place\\O .\\O", "opinion_tags": "Everything\\O I\\O 've\\O had\\O here\\O is\\O good\\B ,\\O taco\\O salads\\O ,\\O burritos\\O ,\\O enchiladas\\O i\\O love\\O this\\O place\\O .\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_478010601:3-3", "target_tags": "Everything\\O I\\O 've\\O had\\O here\\O is\\O good\\O ,\\O taco\\O salads\\O ,\\O burritos\\O ,\\O enchiladas\\O i\\O love\\O this\\O place\\B .\\O", "opinion_tags": "Everything\\O I\\O 've\\O had\\O here\\O is\\O good\\O ,\\O taco\\O salads\\O ,\\O burritos\\O ,\\O enchiladas\\O i\\O love\\B this\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010601:4", "sentence": "Also have great margaritas !", "postag": ["RB", "VB", "JJ", "NNS", "."], "head": [2, 0, 4, 2, 2], "deprel": ["advmod", "root", "amod", "obj", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010601:4-0", "target_tags": "Also\\O have\\O great\\O margaritas\\B !\\O", "opinion_tags": "Also\\O have\\O great\\B margaritas\\O !\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970938:2", "sentence": "The food is simply unforgettable !", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "en_OpenSesame_477970938:2-0", "target_tags": "The\\O food\\B is\\O simply\\O unforgettable\\O !\\O", "opinion_tags": "The\\O food\\O is\\O simply\\O unforgettable\\B !\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970938:8", "sentence": "The staff are friendly and the decor was ethic and colorful .", "postag": ["DT", "NN", "VBP", "JJ", "CC", "DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 11, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "cc", "conj", "punct"], "triples": [{"uid": "en_OpenSesame_477970938:8-0", "target_tags": "The\\O staff\\B are\\O friendly\\O and\\O the\\O decor\\O was\\O ethic\\O and\\O colorful\\O .\\O", "opinion_tags": "The\\O staff\\O are\\O friendly\\B and\\O the\\O decor\\O was\\O ethic\\O and\\O colorful\\O .\\O", "sentiment": "positive"}, {"uid": "en_OpenSesame_477970938:8-1", "target_tags": "The\\O staff\\O are\\O friendly\\O and\\O the\\O decor\\B was\\O ethic\\O and\\O colorful\\O .\\O", "opinion_tags": "The\\O staff\\O are\\O friendly\\O and\\O the\\O decor\\O was\\O ethic\\B and\\O colorful\\B .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970940:0", "sentence": "Holy Hummus !", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "en_OpenSesame_477970940:0-0", "target_tags": "Holy\\O Hummus\\B !\\O", "opinion_tags": "Holy\\B Hummus\\O !\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970940:1", "sentence": "– The food is here is incredible , though the quality is inconsistent during lunch .", "postag": ["NFP", "DT", "NN", "VBZ", "RB", "VBZ", "JJ", ",", "IN", "DT", "NN", "VBZ", "JJ", "IN", "NN", "."], "head": [5, 3, 5, 5, 0, 7, 5, 5, 13, 11, 13, 13, 5, 15, 13, 5], "deprel": ["punct", "det", "nsubj", "cop", "root", "cop", "amod", "punct", "mark", "det", "nsubj", "cop", "advcl", "case", "obl", "punct"], "triples": [{"uid": "en_OpenSesame_477970940:1-0", "target_tags": "–\\O The\\O food\\B is\\O here\\O is\\O incredible\\O ,\\O though\\O the\\O quality\\O is\\O inconsistent\\O during\\O lunch\\O .\\O", "opinion_tags": "–\\O The\\O food\\O is\\O here\\O is\\O incredible\\B ,\\O though\\O the\\O quality\\O is\\O inconsistent\\O during\\O lunch\\O .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970940:2", "sentence": "Dinners have always been excellent , in terms of food quality .", "postag": ["NNS", "VBP", "RB", "VBN", "JJ", ",", "IN", "NNS", "IN", "NN", "NN", "."], "head": [5, 5, 5, 5, 0, 5, 8, 5, 11, 11, 8, 5], "deprel": ["nsubj", "aux", "advmod", "cop", "root", "punct", "case", "obl", "case", "compound", "nmod", "punct"], "triples": [{"uid": "en_OpenSesame_477970940:2-0", "target_tags": "Dinners\\B have\\O always\\O been\\O excellent\\O ,\\O in\\O terms\\O of\\O food\\O quality\\O .\\O", "opinion_tags": "Dinners\\O have\\O always\\O been\\O excellent\\B ,\\O in\\O terms\\O of\\O food\\O quality\\O .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970940:4", "sentence": "The side of potatoes is to die for , as is the labne ( yogurt dip ) .", "postag": ["DT", "NN", "IN", "NNS", "VBZ", "TO", "VB", "IN", ",", "IN", "VBZ", "DT", "NN", "-LRB-", "NN", "NN", "-RRB-", "."], "head": [2, 7, 4, 2, 7, 7, 0, 7, 7, 13, 13, 13, 7, 16, 16, 13, 16, 7], "deprel": ["det", "nsubj", "case", "nmod", "cop", "mark", "root", "obl", "punct", "mark", "cop", "det", "advcl", "punct", "compound", "appos", "punct", "punct"], "triples": [{"uid": "en_OpenSesame_477970940:4-0", "target_tags": "The\\O side\\B of\\I potatoes\\I is\\O to\\O die\\O for\\O ,\\O as\\O is\\O the\\O labne\\O (\\O yogurt\\O dip\\O )\\O .\\O", "opinion_tags": "The\\O side\\O of\\O potatoes\\O is\\O to\\O die\\B for\\I ,\\O as\\O is\\O the\\O labne\\O (\\O yogurt\\O dip\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "en_OpenSesame_477970940:4-1", "target_tags": "The\\O side\\O of\\O potatoes\\O is\\O to\\O die\\O for\\O ,\\O as\\O is\\O the\\O labne\\B (\\I yogurt\\I dip\\I )\\I .\\O", "opinion_tags": "The\\O side\\B of\\I potatoes\\I is\\O to\\O die\\O for\\O ,\\O as\\O is\\O the\\O labne\\O (\\O yogurt\\O dip\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970940:5", "sentence": "Also , they serve THE best hummus in America , with a drizzle of fragrant olive oil ( which , I believe is the traditional way ) !", "postag": ["RB", ",", "PRP", "VBP", "DT", "JJS", "NN", "IN", "NNP", ",", "IN", "DT", "NN", "IN", "JJ", "NN", "NN", "-LRB-", "WDT", ",", "PRP", "VBP", "VBZ", "DT", "JJ", "NN", "-RRB-", "."], "head": [4, 4, 4, 0, 7, 7, 4, 9, 7, 4, 13, 13, 4, 17, 17, 17, 13, 22, 22, 22, 22, 17, 26, 26, 26, 22, 22, 4], "deprel": ["advmod", "punct", "nsubj", "root", "det", "amod", "obj", "case", "nmod", "punct", "case", "det", "obl", "case", "amod", "compound", "nmod", "punct", "obj", "punct", "nsubj", "acl:relcl", "cop", "det", "amod", "ccomp", "punct", "punct"], "triples": [{"uid": "en_OpenSesame_477970940:5-0", "target_tags": "Also\\O ,\\O they\\O serve\\O THE\\O best\\O hummus\\B in\\O America\\O ,\\O with\\O a\\O drizzle\\O of\\O fragrant\\O olive\\O oil\\O (\\O which\\O ,\\O I\\O believe\\O is\\O the\\O traditional\\O way\\O )\\O !\\O", "opinion_tags": "Also\\O ,\\O they\\O serve\\O THE\\O best\\B hummus\\O in\\O America\\O ,\\O with\\O a\\O drizzle\\O of\\O fragrant\\O olive\\O oil\\O (\\O which\\O ,\\O I\\O believe\\O is\\O the\\O traditional\\O way\\O )\\O !\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970940:6", "sentence": "The only drawback is the crowded seating and the slow service .", "postag": ["DT", "JJ", "NN", "VBZ", "DT", "JJ", "NN", "CC", "DT", "JJ", "NN", "."], "head": [3, 3, 7, 7, 7, 7, 0, 11, 11, 11, 7, 7], "deprel": ["det", "amod", "nsubj", "cop", "det", "amod", "root", "cc", "det", "amod", "conj", "punct"], "triples": [{"uid": "en_OpenSesame_477970940:6-0", "target_tags": "The\\O only\\O drawback\\O is\\O the\\O crowded\\O seating\\B and\\O the\\O slow\\O service\\O .\\O", "opinion_tags": "The\\O only\\O drawback\\O is\\O the\\O crowded\\B seating\\O and\\O the\\O slow\\O service\\O .\\O", "sentiment": "negative"}, {"uid": "en_OpenSesame_477970940:6-1", "target_tags": "The\\O only\\O drawback\\O is\\O the\\O crowded\\O seating\\O and\\O the\\O slow\\O service\\B .\\O", "opinion_tags": "The\\O only\\O drawback\\O is\\O the\\O crowded\\O seating\\O and\\O the\\O slow\\B service\\O .\\O", "sentiment": "negative"}]}, {"id": "en_OpenSesame_477970940:8", "sentence": "However , this place is a gem , and I wo n't stop going back .", "postag": ["RB", ",", "DT", "NN", "VBZ", "DT", "NN", ",", "CC", "PRP", "MD", "RB", "VB", "VBG", "RB", "."], "head": [7, 7, 4, 7, 7, 7, 0, 13, 13, 13, 13, 13, 7, 13, 14, 7], "deprel": ["advmod", "punct", "det", "nsubj", "cop", "det", "root", "punct", "cc", "nsubj", "aux", "advmod", "conj", "xcomp", "advmod", "punct"], "triples": [{"uid": "en_OpenSesame_477970940:8-0", "target_tags": "However\\O ,\\O this\\O place\\B is\\O a\\O gem\\O ,\\O and\\O I\\O wo\\O n't\\O stop\\O going\\O back\\O .\\O", "opinion_tags": "However\\O ,\\O this\\O place\\O is\\O a\\O gem\\B ,\\O and\\O I\\O wo\\O n't\\O stop\\O going\\O back\\O .\\O", "sentiment": "positive"}]}, {"id": "en_ParkChaletGardenRestaurant_477778171:0", "sentence": "great beer", "postag": ["JJ", "NN"], "head": [2, 0], "deprel": ["amod", "root"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778171:0-0", "target_tags": "great\\O beer\\B", "opinion_tags": "great\\B beer\\O", "sentiment": "positive"}]}, {"id": "en_ParkChaletGardenRestaurant_477778171:3", "sentence": "I swore never to return for a warm beer and mediocre meal .", "postag": ["PRP", "VBD", "RB", "TO", "VB", "IN", "DT", "JJ", "NN", "CC", "JJ", "NN", "."], "head": [2, 0, 2, 5, 2, 9, 9, 9, 5, 12, 12, 9, 2], "deprel": ["nsubj", "root", "advmod", "mark", "xcomp", "case", "det", "amod", "obl", "cc", "amod", "conj", "punct"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778171:3-0", "target_tags": "I\\O swore\\O never\\O to\\O return\\O for\\O a\\O warm\\O beer\\B and\\O mediocre\\O meal\\O .\\O", "opinion_tags": "I\\O swore\\O never\\O to\\O return\\O for\\O a\\O warm\\B beer\\O and\\O mediocre\\O meal\\O .\\O", "sentiment": "negative"}, {"uid": "en_ParkChaletGardenRestaurant_477778171:3-1", "target_tags": "I\\O swore\\O never\\O to\\O return\\O for\\O a\\O warm\\O beer\\O and\\O mediocre\\O meal\\B .\\O", "opinion_tags": "I\\O swore\\O never\\O to\\O return\\O for\\O a\\O warm\\O beer\\O and\\O mediocre\\B meal\\O .\\O", "sentiment": "negative"}]}, {"id": "en_ParkChaletGardenRestaurant_477778171:5", "sentence": "The band was very good and the service was attentive .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [2, 5, 5, 5, 0, 10, 8, 10, 10, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778171:5-0", "target_tags": "The\\O band\\B was\\O very\\O good\\O and\\O the\\O service\\O was\\O attentive\\O .\\O", "opinion_tags": "The\\O band\\O was\\O very\\O good\\B and\\O the\\O service\\O was\\O attentive\\O .\\O", "sentiment": "positive"}, {"uid": "en_ParkChaletGardenRestaurant_477778171:5-1", "target_tags": "The\\O band\\O was\\O very\\O good\\O and\\O the\\O service\\B was\\O attentive\\O .\\O", "opinion_tags": "The\\O band\\O was\\O very\\O good\\O and\\O the\\O service\\O was\\O attentive\\B .\\O", "sentiment": "positive"}]}, {"id": "en_ParkChaletGardenRestaurant_477778171:7", "sentence": "We ordered a selection of the small plates , and the shoe string onions , goat cheese pizza , grilled asparagus and fried brie with fruit were all very good .", "postag": ["PRP", "VBD", "DT", "NN", "IN", "DT", "JJ", "NNS", ",", "CC", "DT", "NN", "NN", "NNS", ",", "NN", "NN", "NN", ",", "JJ", "NN", "CC", "JJ", "NN", "IN", "NN", "VBD", "RB", "RB", "JJ", "."], "head": [2, 0, 4, 2, 8, 8, 8, 4, 30, 30, 14, 14, 14, 4, 18, 17, 18, 14, 21, 21, 14, 24, 24, 14, 26, 24, 30, 30, 30, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "case", "det", "amod", "nmod", "punct", "cc", "det", "compound", "compound", "conj", "punct", "compound", "compound", "conj", "punct", "amod", "conj", "cc", "amod", "conj", "case", "nmod", "cop", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778171:7-0", "target_tags": "We\\O ordered\\O a\\O selection\\O of\\O the\\O small\\O plates\\O ,\\O and\\O the\\O shoe\\B string\\I onions\\I ,\\O goat\\O cheese\\O pizza\\O ,\\O grilled\\O asparagus\\O and\\O fried\\O brie\\O with\\O fruit\\O were\\O all\\O very\\O good\\O .\\O", "opinion_tags": "We\\O ordered\\O a\\O selection\\O of\\O the\\O small\\O plates\\O ,\\O and\\O the\\O shoe\\O string\\O onions\\O ,\\O goat\\O cheese\\O pizza\\O ,\\O grilled\\O asparagus\\O and\\O fried\\O brie\\O with\\O fruit\\O were\\O all\\O very\\O good\\B .\\O", "sentiment": "positive"}, {"uid": "en_ParkChaletGardenRestaurant_477778171:7-1", "target_tags": "We\\O ordered\\O a\\O selection\\O of\\O the\\O small\\O plates\\O ,\\O and\\O the\\O shoe\\O string\\O onions\\O ,\\O goat\\B cheese\\I pizza\\I ,\\O grilled\\O asparagus\\O and\\O fried\\O brie\\O with\\O fruit\\O were\\O all\\O very\\O good\\O .\\O", "opinion_tags": "We\\O ordered\\O a\\O selection\\O of\\O the\\O small\\O plates\\O ,\\O and\\O the\\O shoe\\O string\\O onions\\O ,\\O goat\\O cheese\\O pizza\\O ,\\O grilled\\O asparagus\\O and\\O fried\\O brie\\O with\\O fruit\\O were\\O all\\O very\\O good\\B .\\O", "sentiment": "positive"}, {"uid": "en_ParkChaletGardenRestaurant_477778171:7-2", "target_tags": "We\\O ordered\\O a\\O selection\\O of\\O the\\O small\\O plates\\O ,\\O and\\O the\\O shoe\\O string\\O onions\\O ,\\O goat\\O cheese\\O pizza\\O ,\\O grilled\\B asparagus\\I and\\O fried\\O brie\\O with\\O fruit\\O were\\O all\\O very\\O good\\O .\\O", "opinion_tags": "We\\O ordered\\O a\\O selection\\O of\\O the\\O small\\O plates\\O ,\\O and\\O the\\O shoe\\O string\\O onions\\O ,\\O goat\\O cheese\\O pizza\\O ,\\O grilled\\O asparagus\\O and\\O fried\\O brie\\O with\\O fruit\\O were\\O all\\O very\\O good\\B .\\O", "sentiment": "positive"}, {"uid": "en_ParkChaletGardenRestaurant_477778171:7-3", "target_tags": "We\\O ordered\\O a\\O selection\\O of\\O the\\O small\\O plates\\O ,\\O and\\O the\\O shoe\\O string\\O onions\\O ,\\O goat\\O cheese\\O pizza\\O ,\\O grilled\\O asparagus\\O and\\O fried\\B brie\\I with\\I fruit\\I were\\O all\\O very\\O good\\O .\\O", "opinion_tags": "We\\O ordered\\O a\\O selection\\O of\\O the\\O small\\O plates\\O ,\\O and\\O the\\O shoe\\O string\\O onions\\O ,\\O goat\\O cheese\\O pizza\\O ,\\O grilled\\O asparagus\\O and\\O fried\\O brie\\O with\\O fruit\\O were\\O all\\O very\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "en_Ray'sBoathouse_477775917:0", "sentence": "Seattle 's BEST Winelist", "postag": ["NNP", "POS", "JJS", "NNP"], "head": [4, 1, 4, 0], "deprel": ["nmod:poss", "case", "amod", "root"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775917:0-0", "target_tags": "Seattle\\O 's\\O BEST\\O Winelist\\B", "opinion_tags": "Seattle\\O 's\\O BEST\\B Winelist\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775917:2", "sentence": "The sommelier is fantastic , down-to-earth , & extremely knowlegable .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "JJ", ",", "CC", "RB", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 10, 10, 10, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "conj", "punct", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775917:2-0", "target_tags": "The\\O sommelier\\B is\\O fantastic\\O ,\\O down-to-earth\\O ,\\O &\\O extremely\\O knowlegable\\O .\\O", "opinion_tags": "The\\O sommelier\\O is\\O fantastic\\B ,\\O down-to-earth\\B ,\\O &\\O extremely\\O knowlegable\\B .\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_477965848:2", "sentence": "The food is not what it once was ( potions have seriously seen downsizing ) prices have gone up , and the service is the worst I have experienced anywhere ( including mainland Europe ) .", "postag": ["DT", "NN", "VBZ", "RB", "WP", "PRP", "RB", "VBD", "-LRB-", "NNS", "VBP", "RB", "VBN", "NN", "-RRB-", "NNS", "VBP", "VBN", "RP", ",", "CC", "DT", "NN", "VBZ", "DT", "JJS", "PRP", "VBP", "VBN", "RB", "-LRB-", "VBG", "JJ", "NNP", "-RRB-", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 13, 13, 13, 13, 5, 13, 13, 18, 18, 5, 18, 26, 26, 23, 26, 26, 26, 5, 29, 29, 26, 29, 34, 34, 34, 26, 34, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "nsubj", "advmod", "acl:relcl", "punct", "nsubj", "aux", "advmod", "parataxis", "obj", "punct", "nsubj", "aux", "parataxis", "advmod", "punct", "cc", "det", "nsubj", "cop", "det", "conj", "nsubj", "aux", "acl:relcl", "advmod", "punct", "case", "amod", "obl", "punct", "punct"], "triples": [{"uid": "en_SchoonerOrLater_477965848:2-0", "target_tags": "The\\O food\\O is\\O not\\O what\\O it\\O once\\O was\\O (\\O potions\\O have\\O seriously\\O seen\\O downsizing\\O )\\O prices\\O have\\O gone\\O up\\O ,\\O and\\O the\\O service\\B is\\O the\\O worst\\O I\\O have\\O experienced\\O anywhere\\O (\\O including\\O mainland\\O Europe\\O )\\O .\\O", "opinion_tags": "The\\O food\\O is\\O not\\O what\\O it\\O once\\O was\\O (\\O potions\\O have\\O seriously\\O seen\\O downsizing\\O )\\O prices\\O have\\O gone\\O up\\O ,\\O and\\O the\\O service\\O is\\O the\\O worst\\B I\\O have\\O experienced\\O anywhere\\O (\\O including\\O mainland\\O Europe\\O )\\O .\\O", "sentiment": "negative"}]}, {"id": "en_MiopostoCaffe_478542920:0", "sentence": "Open & Cool Place with the Best Pizza and Coffee", "postag": ["JJ", "CC", "JJ", "NN", "IN", "DT", "JJS", "NN", "CC", "NN"], "head": [4, 3, 1, 0, 8, 8, 8, 4, 10, 8], "deprel": ["amod", "cc", "conj", "root", "case", "det", "amod", "nmod", "cc", "conj"], "triples": [{"uid": "en_MiopostoCaffe_478542920:0-0", "target_tags": "Open\\O &\\O Cool\\O Place\\B with\\O the\\O Best\\O Pizza\\O and\\O Coffee\\O", "opinion_tags": "Open\\O &\\O Cool\\B Place\\O with\\O the\\O Best\\O Pizza\\O and\\O Coffee\\O", "sentiment": "positive"}, {"uid": "en_MiopostoCaffe_478542920:0-1", "target_tags": "Open\\O &\\O Cool\\O Place\\O with\\O the\\O Best\\O Pizza\\B and\\O Coffee\\O", "opinion_tags": "Open\\O &\\O Cool\\O Place\\O with\\O the\\O Best\\B Pizza\\O and\\O Coffee\\O", "sentiment": "positive"}, {"uid": "en_MiopostoCaffe_478542920:0-2", "target_tags": "Open\\O &\\O Cool\\O Place\\O with\\O the\\O Best\\O Pizza\\O and\\O Coffee\\B", "opinion_tags": "Open\\O &\\O Cool\\O Place\\O with\\O the\\O Best\\B Pizza\\O and\\O Coffee\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478542920:1", "sentence": "– Mioposto has a very creative & delicious pizza menu .", "postag": ["NFP", "NNP", "VBZ", "DT", "RB", "JJ", "CC", "JJ", "NN", "NN", "."], "head": [3, 3, 0, 10, 6, 10, 8, 6, 10, 3, 3], "deprel": ["punct", "nsubj", "root", "det", "advmod", "amod", "cc", "conj", "compound", "obj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542920:1-0", "target_tags": "–\\O Mioposto\\O has\\O a\\O very\\O creative\\O &\\O delicious\\O pizza\\B menu\\I .\\O", "opinion_tags": "–\\O Mioposto\\O has\\O a\\O very\\O creative\\B &\\O delicious\\B pizza\\O menu\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478542920:2", "sentence": "The coffe is very good , too .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", ",", "RB", "."], "head": [2, 5, 5, 5, 0, 5, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "advmod", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542920:2-0", "target_tags": "The\\O coffe\\B is\\O very\\O good\\O ,\\O too\\O .\\O", "opinion_tags": "The\\O coffe\\O is\\O very\\O good\\B ,\\O too\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478542920:3", "sentence": "Great open and friendly ambience .", "postag": ["JJ", "JJ", "CC", "JJ", "NN", "."], "head": [5, 5, 4, 2, 0, 5], "deprel": ["amod", "amod", "cc", "conj", "root", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542920:3-0", "target_tags": "Great\\O open\\O and\\O friendly\\O ambience\\B .\\O", "opinion_tags": "Great\\B open\\B and\\O friendly\\B ambience\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478542920:4", "sentence": "This place is charming and relaxing .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542920:4-0", "target_tags": "This\\O place\\B is\\O charming\\O and\\O relaxing\\O .\\O", "opinion_tags": "This\\O place\\O is\\O charming\\B and\\O relaxing\\B .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478542920:5", "sentence": "The servers behind the counter are always friendly and helpful .", "postag": ["DT", "NNS", "IN", "DT", "NN", "VBP", "RB", "JJ", "CC", "JJ", "."], "head": [2, 8, 5, 5, 2, 8, 8, 0, 10, 8, 8], "deprel": ["det", "nsubj", "case", "det", "nmod", "cop", "advmod", "root", "cc", "conj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542920:5-0", "target_tags": "The\\O servers\\B behind\\I the\\I counter\\I are\\O always\\O friendly\\O and\\O helpful\\O .\\O", "opinion_tags": "The\\O servers\\O behind\\O the\\O counter\\O are\\O always\\O friendly\\B and\\O helpful\\B .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478542920:7", "sentence": "It 's a great place to enjoy food and meet friends .", "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "NN", "CC", "VB", "NNS", "."], "head": [5, 5, 5, 5, 0, 7, 5, 7, 10, 7, 10, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "obj", "cc", "conj", "obj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542920:7-0", "target_tags": "It\\O 's\\O a\\O great\\O place\\O to\\O enjoy\\O food\\B and\\O meet\\O friends\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O great\\O place\\O to\\O enjoy\\B food\\O and\\O meet\\O friends\\O .\\O", "sentiment": "positive"}, {"uid": "en_MiopostoCaffe_478542920:7-1", "target_tags": "It\\O 's\\O a\\O great\\O place\\B to\\O enjoy\\O food\\O and\\O meet\\O friends\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O great\\B place\\O to\\O enjoy\\O food\\O and\\O meet\\O friends\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775916:2", "sentence": "Great food , spectacular location , and friendly service keep us coming back year after year .", "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "CC", "JJ", "NN", "VB", "PRP", "VBG", "RP", "NN", "IN", "NN", "."], "head": [2, 0, 5, 5, 2, 9, 9, 9, 2, 2, 10, 10, 12, 12, 16, 12, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "cc", "amod", "conj", "parataxis", "obj", "xcomp", "compound:prt", "obl:tmod", "case", "obl", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775916:2-0", "target_tags": "Great\\O food\\B ,\\O spectacular\\O location\\O ,\\O and\\O friendly\\O service\\O keep\\O us\\O coming\\O back\\O year\\O after\\O year\\O .\\O", "opinion_tags": "Great\\B food\\O ,\\O spectacular\\O location\\O ,\\O and\\O friendly\\O service\\O keep\\O us\\O coming\\O back\\O year\\O after\\O year\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'sBoathouse_477775916:2-1", "target_tags": "Great\\O food\\O ,\\O spectacular\\O location\\B ,\\O and\\O friendly\\O service\\O keep\\O us\\O coming\\O back\\O year\\O after\\O year\\O .\\O", "opinion_tags": "Great\\O food\\O ,\\O spectacular\\B location\\O ,\\O and\\O friendly\\O service\\O keep\\O us\\O coming\\O back\\O year\\O after\\O year\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'sBoathouse_477775916:2-2", "target_tags": "Great\\O food\\O ,\\O spectacular\\O location\\O ,\\O and\\O friendly\\O service\\B keep\\O us\\O coming\\O back\\O year\\O after\\O year\\O .\\O", "opinion_tags": "Great\\O food\\O ,\\O spectacular\\O location\\O ,\\O and\\O friendly\\B service\\O keep\\O us\\O coming\\O back\\O year\\O after\\O year\\O .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970937:0", "sentence": "Enjoyed the food", "postag": ["VBD", "DT", "NN"], "head": [0, 3, 1], "deprel": ["root", "det", "obj"], "triples": [{"uid": "en_OpenSesame_477970937:0-0", "target_tags": "Enjoyed\\O the\\O food\\B", "opinion_tags": "Enjoyed\\B the\\O food\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970937:5", "sentence": "Food was good and cheap .", "postag": ["NN", "VBD", "JJ", "CC", "JJ", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "en_OpenSesame_477970937:5-0", "target_tags": "Food\\B was\\O good\\O and\\O cheap\\O .\\O", "opinion_tags": "Food\\O was\\O good\\B and\\O cheap\\B .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970937:6", "sentence": "I had the kafta plate and I enjoyed it .", "postag": ["PRP", "VBD", "DT", "NN", "NN", "CC", "PRP", "VBD", "PRP", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 8, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "cc", "nsubj", "conj", "obj", "punct"], "triples": [{"uid": "en_OpenSesame_477970937:6-0", "target_tags": "I\\O had\\O the\\O kafta\\B plate\\I and\\O I\\O enjoyed\\O it\\O .\\O", "opinion_tags": "I\\O had\\O the\\O kafta\\O plate\\O and\\O I\\O enjoyed\\B it\\O .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970937:8", "sentence": "Atmosphere was nice .", "postag": ["NN", "VBD", "JJ", "."], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_OpenSesame_477970937:8-0", "target_tags": "Atmosphere\\B was\\O nice\\O .\\O", "opinion_tags": "Atmosphere\\O was\\O nice\\B .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970937:9", "sentence": "Service was kind of slow , our waitress took forever to give us our check even though it was n't that busy .", "postag": ["NN", "VBD", "RB", "RB", "JJ", ",", "PRP$", "NN", "VBD", "RB", "TO", "VB", "PRP", "PRP$", "NN", "RB", "IN", "PRP", "VBD", "RB", "RB", "JJ", "."], "head": [5, 5, 5, 3, 0, 5, 8, 9, 5, 9, 12, 9, 12, 15, 12, 22, 22, 22, 22, 22, 22, 12, 5], "deprel": ["nsubj", "cop", "advmod", "fixed", "root", "punct", "nmod:poss", "nsubj", "parataxis", "advmod", "mark", "advcl", "i<PERSON><PERSON>", "nmod:poss", "obj", "advmod", "mark", "nsubj", "cop", "advmod", "advmod", "advcl", "punct"], "triples": [{"uid": "en_OpenSesame_477970937:9-0", "target_tags": "Service\\B was\\O kind\\O of\\O slow\\O ,\\O our\\O waitress\\O took\\O forever\\O to\\O give\\O us\\O our\\O check\\O even\\O though\\O it\\O was\\O n't\\O that\\O busy\\O .\\O", "opinion_tags": "Service\\O was\\O kind\\O of\\O slow\\B ,\\O our\\O waitress\\O took\\O forever\\O to\\O give\\O us\\O our\\O check\\O even\\O though\\O it\\O was\\O n't\\O that\\O busy\\O .\\O", "sentiment": "negative"}]}, {"id": "en_OpenSesame_477970937:10", "sentence": "Still I would recommend this place .", "postag": ["RB", "PRP", "MD", "VB", "DT", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 4], "deprel": ["advmod", "nsubj", "aux", "root", "det", "obj", "punct"], "triples": [{"uid": "en_OpenSesame_477970937:10-0", "target_tags": "Still\\O I\\O would\\O recommend\\O this\\O place\\B .\\O", "opinion_tags": "Still\\O I\\O would\\O recommend\\B this\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "en_PizzeriaBianco_477908895:2", "sentence": "WHAT ELSE CAN YOU SAY NICE PEOPLE AMAZING FOOD WOW", "postag": ["WP", "JJ", "MD", "PRP", "VB", "JJ", "NNS", "JJ", "NN", "UH"], "head": [5, 1, 5, 5, 0, 7, 5, 9, 5, 5], "deprel": ["obj", "amod", "aux", "nsubj", "root", "amod", "obj", "amod", "obj", "discourse"], "triples": [{"uid": "en_PizzeriaBianco_477908895:2-0", "target_tags": "WHAT\\O ELSE\\O CAN\\O YOU\\O SAY\\O NICE\\O PEOPLE\\O AMAZING\\O FOOD\\B WOW\\O", "opinion_tags": "WHAT\\O ELSE\\O CAN\\O YOU\\O SAY\\O NICE\\O PEOPLE\\O AMAZING\\B FOOD\\O WOW\\O", "sentiment": "positive"}, {"uid": "en_PizzeriaBianco_477908895:2-1", "target_tags": "WHAT\\O ELSE\\O CAN\\O YOU\\O SAY\\O NICE\\O PEOPLE\\B AMAZING\\O FOOD\\O WOW\\O", "opinion_tags": "WHAT\\O ELSE\\O CAN\\O YOU\\O SAY\\O NICE\\B PEOPLE\\O AMAZING\\O FOOD\\O WOW\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_477965689:0", "sentence": "Great food with an awesome atmosphere !", "postag": ["JJ", "NN", "IN", "DT", "JJ", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 2], "deprel": ["amod", "root", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "en_SchoonerOrLater_477965689:0-0", "target_tags": "Great\\O food\\B with\\O an\\O awesome\\O atmosphere\\O !\\O", "opinion_tags": "Great\\B food\\O with\\O an\\O awesome\\O atmosphere\\O !\\O", "sentiment": "positive"}, {"uid": "en_SchoonerOrLater_477965689:0-1", "target_tags": "Great\\O food\\O with\\O an\\O awesome\\O atmosphere\\B !\\O", "opinion_tags": "Great\\O food\\O with\\O an\\O awesome\\B atmosphere\\O !\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_477965689:1", "sentence": "– Eggs , pancakes , potatoes , fresh fruit and yogurt -- everything they serve is delicious .", "postag": ["NFP", "NNS", ",", "NNS", ",", "NNS", ",", "JJ", "NN", "CC", "NN", ",", "NN", "PRP", "VBP", "VBZ", "JJ", "."], "head": [17, 17, 4, 2, 6, 2, 9, 9, 2, 11, 2, 17, 17, 15, 13, 17, 0, 17], "deprel": ["punct", "nsubj", "punct", "conj", "punct", "conj", "punct", "amod", "conj", "cc", "conj", "punct", "nsubj", "nsubj", "acl:relcl", "cop", "root", "punct"], "triples": [{"uid": "en_SchoonerOrLater_477965689:1-0", "target_tags": "–\\O Eggs\\B ,\\O pancakes\\O ,\\O potatoes\\O ,\\O fresh\\O fruit\\O and\\O yogurt\\O --\\O everything\\O they\\O serve\\O is\\O delicious\\O .\\O", "opinion_tags": "–\\O Eggs\\O ,\\O pancakes\\O ,\\O potatoes\\O ,\\O fresh\\O fruit\\O and\\O yogurt\\O --\\O everything\\O they\\O serve\\O is\\O delicious\\B .\\O", "sentiment": "positive"}, {"uid": "en_SchoonerOrLater_477965689:1-1", "target_tags": "–\\O Eggs\\O ,\\O pancakes\\B ,\\O potatoes\\O ,\\O fresh\\O fruit\\O and\\O yogurt\\O --\\O everything\\O they\\O serve\\O is\\O delicious\\O .\\O", "opinion_tags": "–\\O Eggs\\O ,\\O pancakes\\O ,\\O potatoes\\O ,\\O fresh\\O fruit\\O and\\O yogurt\\O --\\O everything\\O they\\O serve\\O is\\O delicious\\B .\\O", "sentiment": "positive"}, {"uid": "en_SchoonerOrLater_477965689:1-2", "target_tags": "–\\O Eggs\\O ,\\O pancakes\\O ,\\O potatoes\\B ,\\O fresh\\O fruit\\O and\\O yogurt\\O --\\O everything\\O they\\O serve\\O is\\O delicious\\O .\\O", "opinion_tags": "–\\O Eggs\\O ,\\O pancakes\\O ,\\O potatoes\\O ,\\O fresh\\O fruit\\O and\\O yogurt\\O --\\O everything\\O they\\O serve\\O is\\O delicious\\B .\\O", "sentiment": "positive"}, {"uid": "en_SchoonerOrLater_477965689:1-3", "target_tags": "–\\O Eggs\\O ,\\O pancakes\\O ,\\O potatoes\\O ,\\O fresh\\B fruit\\I and\\O yogurt\\O --\\O everything\\O they\\O serve\\O is\\O delicious\\O .\\O", "opinion_tags": "–\\O Eggs\\O ,\\O pancakes\\O ,\\O potatoes\\O ,\\O fresh\\O fruit\\O and\\O yogurt\\O --\\O everything\\O they\\O serve\\O is\\O delicious\\B .\\O", "sentiment": "positive"}, {"uid": "en_SchoonerOrLater_477965689:1-4", "target_tags": "–\\O Eggs\\O ,\\O pancakes\\O ,\\O potatoes\\O ,\\O fresh\\O fruit\\O and\\O yogurt\\B --\\O everything\\O they\\O serve\\O is\\O delicious\\O .\\O", "opinion_tags": "–\\O Eggs\\O ,\\O pancakes\\O ,\\O potatoes\\O ,\\O fresh\\O fruit\\O and\\O yogurt\\O --\\O everything\\O they\\O serve\\O is\\O delicious\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218333:0", "sentence": "great meal – the fish on the omikase platter was absolutely decadent -- there was none of the stringiness that sometimes accompanies fair sushi -- this fish was perfect ! ! ! !", "postag": ["JJ", "NN", ",", "DT", "NN", "IN", "DT", "NN", "NN", "VBD", "RB", "JJ", ",", "EX", "VBD", "NN", "IN", "DT", "NN", "WDT", "RB", "VBZ", "JJ", "NN", ",", "DT", "NN", "VBD", "JJ", ".", ".", ".", "."], "head": [2, 0, 2, 5, 12, 9, 9, 9, 5, 12, 12, 2, 12, 15, 12, 15, 19, 19, 16, 22, 22, 19, 24, 22, 2, 27, 29, 29, 12, 2, 2, 2, 2], "deprel": ["amod", "root", "punct", "det", "nsubj", "case", "det", "compound", "nmod", "cop", "advmod", "appos", "punct", "expl", "parataxis", "nsubj", "case", "det", "nmod", "nsubj", "advmod", "acl:relcl", "amod", "obj", "punct", "det", "nsubj", "cop", "parataxis", "punct", "punct", "punct", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218333:0-0", "target_tags": "great\\O meal\\B –\\O the\\O fish\\O on\\O the\\O omikase\\O platter\\O was\\O absolutely\\O decadent\\O --\\O there\\O was\\O none\\O of\\O the\\O stringiness\\O that\\O sometimes\\O accompanies\\O fair\\O sushi\\O --\\O this\\O fish\\O was\\O perfect\\O !\\O !\\O !\\O !\\O", "opinion_tags": "great\\B meal\\O –\\O the\\O fish\\O on\\O the\\O omikase\\O platter\\O was\\O absolutely\\O decadent\\O --\\O there\\O was\\O none\\O of\\O the\\O stringiness\\O that\\O sometimes\\O accompanies\\O fair\\O sushi\\O --\\O this\\O fish\\O was\\O perfect\\O !\\O !\\O !\\O !\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218333:0-1", "target_tags": "great\\O meal\\O –\\O the\\O fish\\B on\\I the\\I omikase\\I platter\\I was\\O absolutely\\O decadent\\O --\\O there\\O was\\O none\\O of\\O the\\O stringiness\\O that\\O sometimes\\O accompanies\\O fair\\O sushi\\O --\\O this\\O fish\\O was\\O perfect\\O !\\O !\\O !\\O !\\O", "opinion_tags": "great\\O meal\\O –\\O the\\O fish\\O on\\O the\\O omikase\\O platter\\O was\\O absolutely\\O decadent\\B --\\O there\\O was\\O none\\O of\\O the\\O stringiness\\O that\\O sometimes\\O accompanies\\O fair\\O sushi\\O --\\O this\\O fish\\O was\\O perfect\\B !\\O !\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218333:1", "sentence": "plus , i am allergic to rice , and the waitstaff was unbelievably accomodating -- did n't even bat an eye !", "postag": ["CC", ",", "PRP", "VBP", "JJ", "IN", "NN", ",", "CC", "DT", "NN", "VBD", "RB", "JJ", ",", "VBD", "RB", "RB", "VB", "DT", "NN", "."], "head": [14, 5, 5, 5, 0, 7, 5, 14, 14, 11, 14, 14, 14, 5, 5, 19, 19, 19, 5, 21, 19, 5], "deprel": ["cc", "punct", "nsubj", "cop", "root", "case", "obl", "punct", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct", "aux", "advmod", "advmod", "parataxis", "det", "obj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218333:1-0", "target_tags": "plus\\O ,\\O i\\O am\\O allergic\\O to\\O rice\\O ,\\O and\\O the\\O waitstaff\\B was\\O unbelievably\\O accomodating\\O --\\O did\\O n't\\O even\\O bat\\O an\\O eye\\O !\\O", "opinion_tags": "plus\\O ,\\O i\\O am\\O allergic\\O to\\O rice\\O ,\\O and\\O the\\O waitstaff\\O was\\O unbelievably\\O accomodating\\B --\\O did\\O n't\\O even\\O bat\\O an\\O eye\\O !\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218333:2", "sentence": "and the waiter suggested a perfect sake ! !", "postag": ["CC", "DT", "NN", "VBD", "DT", "JJ", "NN", ".", "."], "head": [4, 3, 4, 0, 7, 7, 4, 4, 4], "deprel": ["cc", "det", "nsubj", "root", "det", "amod", "obj", "punct", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218333:2-0", "target_tags": "and\\O the\\O waiter\\O suggested\\O a\\O perfect\\O sake\\B !\\O !\\O", "opinion_tags": "and\\O the\\O waiter\\O suggested\\O a\\O perfect\\B sake\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218340:0", "sentence": "Unbeatable sushi !", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218340:0-0", "target_tags": "Unbeatable\\O sushi\\B !\\O", "opinion_tags": "Unbeatable\\B sushi\\O !\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218340:4", "sentence": "Melt in your mouth nigiri and sashmi , and very tasty rolls too .", "postag": ["VB", "IN", "PRP$", "NN", "NN", "CC", "JJ", ",", "CC", "RB", "JJ", "NNS", "RB", "."], "head": [0, 5, 5, 5, 1, 7, 5, 12, 12, 11, 12, 5, 12, 1], "deprel": ["root", "case", "nmod:poss", "compound", "obl", "cc", "conj", "punct", "cc", "advmod", "amod", "conj", "advmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218340:4-0", "target_tags": "Melt\\O in\\O your\\O mouth\\O nigiri\\O and\\O sashmi\\O ,\\O and\\O very\\O tasty\\O rolls\\B too\\O .\\O", "opinion_tags": "Melt\\O in\\O your\\O mouth\\O nigiri\\O and\\O sashmi\\O ,\\O and\\O very\\O tasty\\B rolls\\O too\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218340:5", "sentence": "Be sure to try the oyster roll .", "postag": ["VB", "JJ", "TO", "VB", "DT", "NN", "NN", "."], "head": [2, 0, 4, 2, 7, 7, 4, 2], "deprel": ["cop", "root", "mark", "xcomp", "det", "compound", "obj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218340:5-0", "target_tags": "Be\\O sure\\O to\\O try\\O the\\O oyster\\B roll\\I .\\O", "opinion_tags": "Be\\O sure\\O to\\O try\\B the\\O oyster\\O roll\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218715:1", "sentence": "– How to describe the best sushi in NYC : hmmmm , delicious , amazing , fantastic , suculent , perfect , nah , all of the above .", "postag": ["NFP", "WRB", "TO", "VB", "DT", "JJS", "NN", "IN", "NNP", ":", "UH", ",", "JJ", ",", "JJ", ",", "JJ", ",", "JJ", ",", "JJ", ",", "UH", ",", "DT", "IN", "DT", "JJ", "."], "head": [4, 4, 4, 0, 7, 7, 4, 9, 4, 4, 4, 13, 11, 15, 11, 17, 11, 19, 11, 21, 11, 25, 13, 25, 13, 28, 28, 25, 4], "deprel": ["punct", "mark", "mark", "root", "det", "amod", "obj", "case", "obl", "punct", "parataxis", "punct", "conj", "punct", "conj", "punct", "conj", "punct", "conj", "punct", "conj", "punct", "list", "punct", "conj", "case", "det", "nmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218715:1-0", "target_tags": "–\\O How\\O to\\O describe\\O the\\O best\\O sushi\\B in\\O NYC\\O :\\O hmmmm\\O ,\\O delicious\\O ,\\O amazing\\O ,\\O fantastic\\O ,\\O suculent\\O ,\\O perfect\\O ,\\O nah\\O ,\\O all\\O of\\O the\\O above\\O .\\O", "opinion_tags": "–\\O How\\O to\\O describe\\O the\\O best\\B sushi\\O in\\O NYC\\O :\\O hmmmm\\O ,\\O delicious\\O ,\\O amazing\\O ,\\O fantastic\\O ,\\O suculent\\O ,\\O perfect\\O ,\\O nah\\O ,\\O all\\O of\\O the\\O above\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218715:3", "sentence": "The best <PERSON><PERSON><PERSON>shi I have ever had .", "postag": ["DT", "JJS", "NNP", "NNP", "PRP", "VBP", "RB", "VBN", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 4], "deprel": ["det", "amod", "compound", "root", "nsubj", "aux", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218715:3-0", "target_tags": "The\\O best\\O Chuwam\\B Mushi\\I I\\O have\\O ever\\O had\\O .\\O", "opinion_tags": "The\\O best\\B Chuwam\\O Mushi\\O I\\O have\\O ever\\O had\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218518:0", "sentence": "<PERSON>shi , High Price", "postag": ["JJ", "NN", ",", "JJ", "NN"], "head": [2, 0, 2, 5, 2], "deprel": ["amod", "root", "punct", "amod", "parataxis"], "triples": [{"uid": "en_BlueRibbonSushi_478218518:0-0", "target_tags": "Good\\O Sushi\\B ,\\O High\\O Price\\O", "opinion_tags": "Good\\B Sushi\\O ,\\O High\\O Price\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478218518:1", "sentence": "One of the best Sushi place in town .", "postag": ["CD", "IN", "DT", "JJS", "NNP", "NN", "IN", "NN", "."], "head": [0, 6, 6, 6, 6, 1, 8, 6, 1], "deprel": ["root", "case", "det", "amod", "compound", "nmod", "case", "nmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218518:1-0", "target_tags": "One\\O of\\O the\\O best\\O Sushi\\B place\\I in\\O town\\O .\\O", "opinion_tags": "One\\O of\\O the\\O best\\B Sushi\\O place\\O in\\O town\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218518:2", "sentence": "The house special roll is really good .", "postag": ["DT", "NN", "JJ", "NN", "VBZ", "RB", "JJ", "."], "head": [4, 4, 4, 7, 7, 7, 0, 7], "deprel": ["det", "compound", "amod", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218518:2-0", "target_tags": "The\\O house\\B special\\I roll\\I is\\O really\\O good\\O .\\O", "opinion_tags": "The\\O house\\O special\\O roll\\O is\\O really\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'s_478075041:1", "sentence": "– I ca n't believe <PERSON> '<PERSON> has been around for over 25 years , amazing .", "postag": ["NFP", "PRP", "MD", "RB", "VB", "NNP", "POS", "VBZ", "VBN", "RB", "IN", "RB", "CD", "NNS", ",", "JJ", "."], "head": [5, 5, 5, 5, 0, 10, 6, 10, 10, 5, 14, 13, 14, 10, 5, 5, 5], "deprel": ["punct", "nsubj", "aux", "advmod", "root", "nsubj", "case", "aux", "cop", "ccomp", "case", "advmod", "nummod", "obl", "punct", "parataxis", "punct"], "triples": [{"uid": "en_<PERSON>'s_478075041:1-0", "target_tags": "–\\O I\\O ca\\O n't\\O believe\\O Murphy\\B 's\\I has\\O been\\O around\\O for\\O over\\O 25\\O years\\O ,\\O amazing\\O .\\O", "opinion_tags": "–\\O I\\O ca\\O n't\\O believe\\O Murphy\\O 's\\O has\\O been\\O around\\O for\\O over\\O 25\\O years\\O ,\\O amazing\\B .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'s_478075041:3", "sentence": "Brunch at Murphy 's is to die for , my specialty ... egg white omelet , the food is always freshly prepared .", "postag": ["NN", "IN", "NNP", "POS", "VBZ", "TO", "VB", "IN", ",", "PRP$", "NN", ",", "NN", "JJ", "NN", ",", "DT", "NN", "VBZ", "RB", "RB", "VBN", "."], "head": [7, 3, 1, 3, 0, 7, 5, 11, 11, 11, 7, 7, 15, 15, 7, 7, 18, 22, 22, 22, 22, 7, 7], "deprel": ["nsubj", "case", "nmod", "case", "root", "mark", "ccomp", "case", "punct", "nmod:poss", "obl", "punct", "compound", "amod", "parataxis", "punct", "det", "nsubj:pass", "aux:pass", "advmod", "advmod", "parataxis", "punct"], "triples": [{"uid": "en_<PERSON>'s_478075041:3-0", "target_tags": "Brunch\\O at\\O Murphy\\O 's\\O is\\O to\\O die\\O for\\O ,\\O my\\O specialty\\O ...\\O egg\\O white\\O omelet\\O ,\\O the\\O food\\B is\\O always\\O freshly\\O prepared\\O .\\O", "opinion_tags": "Brunch\\O at\\O Murphy\\O 's\\O is\\O to\\O die\\O for\\O ,\\O my\\O specialty\\O ...\\O egg\\O white\\O omelet\\O ,\\O the\\O food\\O is\\O always\\O freshly\\B prepared\\I .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'s_478075041:4", "sentence": "It 's the perfect spot for a romantic date for 2 or a secret rendezvous !", "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", "IN", "CD", "CC", "DT", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 9, 9, 9, 5, 11, 15, 15, 15, 15, 9, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "case", "det", "amod", "nmod", "case", "nummod", "cc", "det", "amod", "nmod", "punct"], "triples": [{"uid": "en_<PERSON>'s_478075041:4-0", "target_tags": "It\\O 's\\O the\\O perfect\\O spot\\B for\\O a\\O romantic\\O date\\O for\\O 2\\O or\\O a\\O secret\\O rendezvous\\O !\\O", "opinion_tags": "It\\O 's\\O the\\O perfect\\B spot\\O for\\O a\\O romantic\\O date\\O for\\O 2\\O or\\O a\\O secret\\O rendezvous\\O !\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'s_478075041:5", "sentence": "Save room for scrumptious desserts .", "postag": ["VB", "NN", "IN", "JJ", "NNS", "."], "head": [0, 1, 5, 5, 1, 1], "deprel": ["root", "obj", "case", "amod", "obl", "punct"], "triples": [{"uid": "en_<PERSON>'s_478075041:5-0", "target_tags": "Save\\O room\\O for\\O scrumptious\\O desserts\\B .\\O", "opinion_tags": "Save\\O room\\O for\\O scrumptious\\B desserts\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'s_478075041:6", "sentence": "The restaurant offers an extensive wine list and an ambiance you wo n't forget !", "postag": ["DT", "NN", "VBZ", "DT", "JJ", "NN", "NN", "CC", "DT", "NN", "PRP", "MD", "RB", "VB", "."], "head": [2, 3, 0, 7, 7, 7, 3, 10, 10, 7, 14, 14, 14, 10, 3], "deprel": ["det", "nsubj", "root", "det", "amod", "compound", "obj", "cc", "det", "conj", "nsubj", "aux", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "en_<PERSON>'s_478075041:6-0", "target_tags": "The\\O restaurant\\O offers\\O an\\O extensive\\O wine\\B list\\I and\\O an\\O ambiance\\O you\\O wo\\O n't\\O forget\\O !\\O", "opinion_tags": "The\\O restaurant\\O offers\\O an\\O extensive\\B wine\\O list\\O and\\O an\\O ambiance\\O you\\O wo\\O n't\\O forget\\O !\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'s_478075041:6-1", "target_tags": "The\\O restaurant\\O offers\\O an\\O extensive\\O wine\\O list\\O and\\O an\\O ambiance\\B you\\O wo\\O n't\\O forget\\O !\\O", "opinion_tags": "The\\O restaurant\\O offers\\O an\\O extensive\\O wine\\O list\\O and\\O an\\O ambiance\\O you\\O wo\\B n't\\I forget\\I !\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010603:1", "sentence": "– Best Mexican place for lunch in the financial district .", "postag": ["NFP", "JJS", "JJ", "NN", "IN", "NN", "IN", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 10, 10, 10, 4, 4], "deprel": ["punct", "amod", "amod", "root", "case", "nmod", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010603:1-0", "target_tags": "–\\O Best\\O Mexican\\B place\\I for\\O lunch\\O in\\O the\\O financial\\O district\\O .\\O", "opinion_tags": "–\\O Best\\B Mexican\\O place\\O for\\O lunch\\O in\\O the\\O financial\\O district\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010603:2", "sentence": "Love the enchiladas and chicken soup - and be sure to check out their specials .", "postag": ["VBP", "DT", "NNS", "CC", "NN", "NN", ",", "CC", "VB", "JJ", "TO", "VB", "RP", "PRP$", "NNS", "."], "head": [0, 3, 1, 6, 6, 3, 1, 10, 10, 1, 12, 10, 12, 15, 12, 1], "deprel": ["root", "det", "obj", "cc", "compound", "conj", "punct", "cc", "cop", "conj", "mark", "xcomp", "compound:prt", "nmod:poss", "obj", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010603:2-0", "target_tags": "Love\\O the\\O enchiladas\\B and\\O chicken\\O soup\\O -\\O and\\O be\\O sure\\O to\\O check\\O out\\O their\\O specials\\O .\\O", "opinion_tags": "Love\\B the\\O enchiladas\\O and\\O chicken\\O soup\\O -\\O and\\O be\\O sure\\O to\\O check\\O out\\O their\\O specials\\O .\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_478010603:2-1", "target_tags": "Love\\O the\\O enchiladas\\O and\\O chicken\\B soup\\I -\\O and\\O be\\O sure\\O to\\O check\\O out\\O their\\O specials\\O .\\O", "opinion_tags": "Love\\B the\\O enchiladas\\O and\\O chicken\\O soup\\O -\\O and\\O be\\O sure\\O to\\O check\\O out\\O their\\O specials\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010603:4", "sentence": "Can get busy on Fridays for a table but once seated , the service is so efficient you can be in and out of there quickly .", "postag": ["MD", "VB", "JJ", "IN", "NNPS", "IN", "DT", "NN", "CC", "RB", "VBN", ",", "DT", "NN", "VBZ", "RB", "JJ", "PRP", "MD", "VB", "IN", "CC", "IN", "IN", "RB", "RB", "."], "head": [3, 3, 0, 5, 3, 8, 8, 3, 11, 11, 3, 17, 14, 17, 17, 17, 3, 20, 20, 17, 17, 25, 25, 25, 21, 21, 3], "deprel": ["aux", "aux", "root", "case", "obl", "case", "det", "obl", "cc", "advmod", "conj", "punct", "det", "nsubj", "cop", "advmod", "conj", "nsubj", "aux", "ccomp", "ccomp", "cc", "case", "case", "conj", "conj", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010603:4-0", "target_tags": "Can\\O get\\O busy\\O on\\O Fridays\\O for\\O a\\O table\\O but\\O once\\O seated\\O ,\\O the\\O service\\B is\\O so\\O efficient\\O you\\O can\\O be\\O in\\O and\\O out\\O of\\O there\\O quickly\\O .\\O", "opinion_tags": "Can\\O get\\O busy\\O on\\O Fridays\\O for\\O a\\O table\\O but\\O once\\O seated\\O ,\\O the\\O service\\O is\\O so\\O efficient\\B you\\O can\\O be\\O in\\O and\\O out\\O of\\O there\\O quickly\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218513:2", "sentence": "The sushi was excellent and the wait staff was quick .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "DT", "NN", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 10, 8, 8, 10, 10, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "compound", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218513:2-0", "target_tags": "The\\O sushi\\B was\\O excellent\\O and\\O the\\O wait\\O staff\\O was\\O quick\\O .\\O", "opinion_tags": "The\\O sushi\\O was\\O excellent\\B and\\O the\\O wait\\O staff\\O was\\O quick\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218513:2-1", "target_tags": "The\\O sushi\\O was\\O excellent\\O and\\O the\\O wait\\B staff\\I was\\O quick\\O .\\O", "opinion_tags": "The\\O sushi\\O was\\O excellent\\O and\\O the\\O wait\\O staff\\O was\\O quick\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218513:4", "sentence": "The atmosphere was just okay .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218513:4-0", "target_tags": "The\\O atmosphere\\B was\\O just\\O okay\\O .\\O", "opinion_tags": "The\\O atmosphere\\O was\\O just\\O okay\\B .\\O", "sentiment": "neutral"}]}, {"id": "en_BlueRibbonSushi_478218513:5", "sentence": "Space was limited , but the food made up for it .", "postag": ["NN", "VBD", "JJ", ",", "CC", "DT", "NN", "VBD", "RP", "IN", "PRP", "."], "head": [3, 3, 0, 8, 8, 7, 8, 3, 8, 11, 8, 3], "deprel": ["nsubj", "cop", "root", "punct", "cc", "det", "nsubj", "conj", "compound:prt", "case", "obl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218513:5-0", "target_tags": "Space\\B was\\O limited\\O ,\\O but\\O the\\O food\\O made\\O up\\O for\\O it\\O .\\O", "opinion_tags": "Space\\O was\\O limited\\B ,\\O but\\O the\\O food\\O made\\O up\\O for\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "en_MiopostoCaffe_478542919:4", "sentence": "We stood there for 10 minutes while employees walked back and forth ignoring us .", "postag": ["PRP", "VBD", "RB", "IN", "CD", "NNS", "IN", "NNS", "VBD", "RB", "CC", "RB", "VBG", "PRP", "."], "head": [2, 0, 2, 6, 6, 2, 9, 9, 2, 9, 12, 10, 9, 13, 2], "deprel": ["nsubj", "root", "advmod", "case", "nummod", "obl", "mark", "nsubj", "advcl", "advmod", "cc", "conj", "conj", "obj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542919:4-0", "target_tags": "We\\O stood\\O there\\O for\\O 10\\O minutes\\O while\\O employees\\B walked\\O back\\O and\\O forth\\O ignoring\\O us\\O .\\O", "opinion_tags": "We\\O stood\\O there\\O for\\O 10\\O minutes\\O while\\O employees\\O walked\\O back\\O and\\O forth\\O ignoring\\B us\\O .\\O", "sentiment": "negative"}]}, {"id": "en_MiopostoCaffe_478542919:8", "sentence": "The Caesar salad I ordered had so much lemon I could n't eat it .", "postag": ["DT", "NNP", "NN", "PRP", "VBD", "VBD", "RB", "JJ", "NN", "PRP", "MD", "RB", "VB", "PRP", "."], "head": [3, 3, 6, 5, 3, 0, 8, 9, 6, 13, 13, 13, 9, 13, 6], "deprel": ["det", "compound", "nsubj", "nsubj", "acl:relcl", "root", "advmod", "amod", "obj", "nsubj", "aux", "advmod", "acl:relcl", "obj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542919:8-0", "target_tags": "The\\O Caesar\\B salad\\I I\\O ordered\\O had\\O so\\O much\\O lemon\\O I\\O could\\O n't\\O eat\\O it\\O .\\O", "opinion_tags": "The\\O Caesar\\O salad\\O I\\O ordered\\O had\\O so\\O much\\O lemon\\O I\\O could\\B n't\\I eat\\I it\\O .\\O", "sentiment": "negative"}]}, {"id": "en_MercedesRestaurant_478010602:0", "sentence": "Great food , better Margaritas !", "postag": ["JJ", "NN", ",", "JJR", "NNS", "."], "head": [2, 0, 2, 5, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010602:0-0", "target_tags": "Great\\O food\\B ,\\O better\\O Margaritas\\O !\\O", "opinion_tags": "Great\\B food\\O ,\\O better\\O Margaritas\\O !\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_478010602:0-1", "target_tags": "Great\\O food\\O ,\\O better\\O Margaritas\\B !\\O", "opinion_tags": "Great\\O food\\O ,\\O better\\B Margaritas\\O !\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010602:1", "sentence": "– This is one of my top lunch spots , huge portions , fast service and amazing margaritas ! !", "postag": ["NFP", "DT", "VBZ", "CD", "IN", "PRP$", "JJ", "NN", "NNS", ",", "JJ", "NNS", ",", "JJ", "NN", "CC", "JJ", "NNS", ".", "."], "head": [4, 4, 4, 0, 9, 9, 9, 9, 4, 12, 12, 9, 15, 15, 9, 18, 18, 9, 4, 4], "deprel": ["punct", "nsubj", "cop", "root", "case", "nmod:poss", "amod", "compound", "nmod", "punct", "amod", "conj", "punct", "amod", "conj", "cc", "amod", "conj", "punct", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010602:1-0", "target_tags": "–\\O This\\O is\\O one\\O of\\O my\\O top\\O lunch\\O spots\\O ,\\O huge\\O portions\\B ,\\O fast\\O service\\O and\\O amazing\\O margaritas\\O !\\O !\\O", "opinion_tags": "–\\O This\\O is\\O one\\O of\\O my\\O top\\O lunch\\O spots\\O ,\\O huge\\B portions\\O ,\\O fast\\O service\\O and\\O amazing\\O margaritas\\O !\\O !\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_478010602:1-1", "target_tags": "–\\O This\\O is\\O one\\O of\\O my\\O top\\O lunch\\O spots\\O ,\\O huge\\O portions\\O ,\\O fast\\O service\\B and\\O amazing\\O margaritas\\O !\\O !\\O", "opinion_tags": "–\\O This\\O is\\O one\\O of\\O my\\O top\\O lunch\\O spots\\O ,\\O huge\\O portions\\O ,\\O fast\\B service\\O and\\O amazing\\O margaritas\\O !\\O !\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_478010602:1-2", "target_tags": "–\\O This\\O is\\O one\\O of\\O my\\O top\\O lunch\\O spots\\O ,\\O huge\\O portions\\O ,\\O fast\\O service\\O and\\O amazing\\O margaritas\\B !\\O !\\O", "opinion_tags": "–\\O This\\O is\\O one\\O of\\O my\\O top\\O lunch\\O spots\\O ,\\O huge\\O portions\\O ,\\O fast\\O service\\O and\\O amazing\\B margaritas\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010602:2", "sentence": "it gets really busy , so get there on the early side so you can grab a seat , if you do have to wait , its not bad because the service is quick !", "postag": ["PRP", "VBZ", "RB", "JJ", ",", "RB", "VB", "RB", "IN", "DT", "JJ", "NN", "RB", "PRP", "MD", "VB", "DT", "NN", ",", "IN", "PRP", "VBP", "VB", "TO", "VB", ",", "PRP$", "RB", "JJ", "IN", "DT", "NN", "VBZ", "JJ", "."], "head": [4, 4, 4, 0, 4, 7, 4, 7, 12, 12, 12, 7, 16, 16, 16, 4, 18, 16, 16, 23, 23, 23, 16, 25, 23, 29, 29, 29, 25, 34, 32, 34, 34, 25, 4], "deprel": ["nsubj", "aux", "advmod", "root", "punct", "advmod", "parataxis", "advmod", "case", "det", "amod", "obl", "advmod", "nsubj", "aux", "parataxis", "det", "obj", "punct", "mark", "nsubj", "aux", "advcl", "mark", "xcomp", "punct", "nmod:poss", "advmod", "conj", "mark", "det", "nsubj", "cop", "advcl", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010602:2-0", "target_tags": "it\\O gets\\O really\\O busy\\O ,\\O so\\O get\\O there\\O on\\O the\\O early\\O side\\O so\\O you\\O can\\O grab\\O a\\O seat\\O ,\\O if\\O you\\O do\\O have\\O to\\O wait\\O ,\\O its\\O not\\O bad\\O because\\O the\\O service\\B is\\O quick\\O !\\O", "opinion_tags": "it\\O gets\\O really\\O busy\\O ,\\O so\\O get\\O there\\O on\\O the\\O early\\O side\\O so\\O you\\O can\\O grab\\O a\\O seat\\O ,\\O if\\O you\\O do\\O have\\O to\\O wait\\O ,\\O its\\O not\\O bad\\O because\\O the\\O service\\O is\\O quick\\B !\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010602:3", "sentence": "Check out the art on the walls , very colorful !", "postag": ["VB", "RP", "DT", "NN", "IN", "DT", "NNS", ",", "RB", "JJ", "."], "head": [0, 1, 4, 1, 7, 7, 1, 10, 10, 1, 1], "deprel": ["root", "compound:prt", "det", "obj", "case", "det", "obl", "punct", "advmod", "advmod", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010602:3-0", "target_tags": "Check\\O out\\O the\\O art\\B on\\I the\\I walls\\I ,\\O very\\O colorful\\O !\\O", "opinion_tags": "Check\\O out\\O the\\O art\\O on\\O the\\O walls\\O ,\\O very\\O colorful\\B !\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960696:0", "sentence": "i love this place !", "postag": ["PRP", "VBP", "DT", "NN", "."], "head": [2, 0, 4, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "punct"], "triples": [{"uid": "en_India'sGrill_477960696:0-0", "target_tags": "i\\O love\\O this\\O place\\B !\\O", "opinion_tags": "i\\O love\\B this\\O place\\O !\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960696:1", "sentence": "– i have been eating at this place for over 8 years now and i have never had one bad meal .", "postag": ["NFP", "PRP", "VBP", "VBN", "VBG", "IN", "DT", "NN", "IN", "RB", "CD", "NNS", "RB", "CC", "PRP", "VBP", "RB", "VBN", "CD", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 12, 11, 12, 5, 5, 18, 18, 18, 18, 5, 21, 21, 18, 5], "deprel": ["punct", "nsubj", "aux", "aux", "root", "case", "det", "obl", "case", "advmod", "nummod", "obl", "advmod", "cc", "nsubj", "aux", "advmod", "conj", "nummod", "amod", "obj", "punct"], "triples": [{"uid": "en_India'sGrill_477960696:1-0", "target_tags": "–\\O i\\O have\\O been\\O eating\\O at\\O this\\O place\\O for\\O over\\O 8\\O years\\O now\\O and\\O i\\O have\\O never\\O had\\O one\\O bad\\O meal\\B .\\O", "opinion_tags": "–\\O i\\O have\\O been\\O eating\\O at\\O this\\O place\\O for\\O over\\O 8\\O years\\O now\\O and\\O i\\O have\\O never\\O had\\O one\\O bad\\B meal\\O .\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960696:3", "sentence": "The lunch menu is an awesome deal !", "postag": ["DT", "NN", "NN", "VBZ", "DT", "JJ", "NN", "."], "head": [3, 3, 7, 7, 7, 7, 0, 7], "deprel": ["det", "compound", "nsubj", "cop", "det", "amod", "root", "punct"], "triples": [{"uid": "en_India'sGrill_477960696:3-0", "target_tags": "The\\O lunch\\B menu\\I is\\O an\\O awesome\\O deal\\O !\\O", "opinion_tags": "The\\O lunch\\O menu\\O is\\O an\\O awesome\\B deal\\O !\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960696:4", "sentence": "plenty of food , trust me .", "postag": ["NN", "IN", "NN", ",", "VB", "PRP", "."], "head": [0, 3, 1, 1, 1, 5, 1], "deprel": ["root", "case", "nmod", "punct", "parataxis", "obj", "punct"], "triples": [{"uid": "en_India'sGrill_477960696:4-0", "target_tags": "plenty\\O of\\O food\\B ,\\O trust\\O me\\O .\\O", "opinion_tags": "plenty\\B of\\I food\\O ,\\O trust\\O me\\O .\\O", "sentiment": "positive"}]}, {"id": "en_India'sGrill_477960696:5", "sentence": "Fresh ingrediants and super tasty .", "postag": ["JJ", "NNS", "CC", "RB", "JJ", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["amod", "root", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "en_India'sGrill_477960696:5-0", "target_tags": "Fresh\\O ingrediants\\B and\\O super\\O tasty\\O .\\O", "opinion_tags": "Fresh\\B ingrediants\\O and\\O super\\O tasty\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218517:0", "sentence": "Best food , phenominal service", "postag": ["JJS", "NN", ",", "JJ", "NN"], "head": [2, 0, 2, 5, 2], "deprel": ["amod", "root", "punct", "amod", "parataxis"], "triples": [{"uid": "en_BlueRibbonSushi_478218517:0-0", "target_tags": "Best\\O food\\B ,\\O phenominal\\O service\\O", "opinion_tags": "Best\\B food\\O ,\\O phenominal\\O service\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218517:0-1", "target_tags": "Best\\O food\\O ,\\O phenominal\\O service\\B", "opinion_tags": "Best\\O food\\O ,\\O phenominal\\B service\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218517:1", "sentence": "For the finicky sushi eater and those who have sampled the best NYC has to offer , the fish is the freshest and the service is superb .", "postag": ["IN", "DT", "JJ", "NN", "NN", "CC", "DT", "WP", "VBP", "VBN", "DT", "JJS", "NNP", "VBZ", "TO", "VB", ",", "DT", "NN", "VBZ", "DT", "JJS", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [5, 5, 5, 5, 22, 7, 5, 10, 10, 7, 12, 10, 14, 22, 16, 14, 22, 19, 22, 22, 22, 0, 27, 25, 27, 27, 22, 22], "deprel": ["case", "det", "amod", "compound", "obl", "cc", "conj", "nsubj", "aux", "acl:relcl", "det", "obj", "nsubj", "advcl", "mark", "xcomp", "punct", "det", "nsubj", "cop", "det", "root", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218517:1-0", "target_tags": "For\\O the\\O finicky\\O sushi\\O eater\\O and\\O those\\O who\\O have\\O sampled\\O the\\O best\\O NYC\\O has\\O to\\O offer\\O ,\\O the\\O fish\\B is\\O the\\O freshest\\O and\\O the\\O service\\O is\\O superb\\O .\\O", "opinion_tags": "For\\O the\\O finicky\\O sushi\\O eater\\O and\\O those\\O who\\O have\\O sampled\\O the\\O best\\O NYC\\O has\\O to\\O offer\\O ,\\O the\\O fish\\O is\\O the\\O freshest\\B and\\O the\\O service\\O is\\O superb\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218517:1-1", "target_tags": "For\\O the\\O finicky\\O sushi\\O eater\\O and\\O those\\O who\\O have\\O sampled\\O the\\O best\\O NYC\\O has\\O to\\O offer\\O ,\\O the\\O fish\\O is\\O the\\O freshest\\O and\\O the\\O service\\B is\\O superb\\O .\\O", "opinion_tags": "For\\O the\\O finicky\\O sushi\\O eater\\O and\\O those\\O who\\O have\\O sampled\\O the\\O best\\O NYC\\O has\\O to\\O offer\\O ,\\O the\\O fish\\O is\\O the\\O freshest\\O and\\O the\\O service\\O is\\O superb\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218517:2", "sentence": "Not only can the selection be innovative , but there 's a nice balance of traditional sushi as well .", "postag": ["RB", "RB", "MD", "DT", "NN", "VB", "JJ", ",", "CC", "EX", "VBZ", "DT", "JJ", "NN", "IN", "JJ", "NN", "RB", "RB", "."], "head": [2, 7, 7, 5, 7, 7, 0, 11, 11, 11, 7, 14, 14, 11, 17, 17, 14, 11, 18, 7], "deprel": ["advmod", "advmod", "aux", "det", "nsubj", "cop", "root", "punct", "cc", "expl", "conj", "det", "amod", "nsubj", "case", "amod", "nmod", "advmod", "fixed", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218517:2-0", "target_tags": "Not\\O only\\O can\\O the\\O selection\\B be\\O innovative\\O ,\\O but\\O there\\O 's\\O a\\O nice\\O balance\\O of\\O traditional\\O sushi\\O as\\O well\\O .\\O", "opinion_tags": "Not\\O only\\O can\\O the\\O selection\\O be\\O innovative\\B ,\\O but\\O there\\O 's\\O a\\O nice\\O balance\\O of\\O traditional\\O sushi\\O as\\O well\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218517:2-1", "target_tags": "Not\\O only\\O can\\O the\\O selection\\O be\\O innovative\\O ,\\O but\\O there\\O 's\\O a\\O nice\\O balance\\O of\\O traditional\\O sushi\\B as\\O well\\O .\\O", "opinion_tags": "Not\\O only\\O can\\O the\\O selection\\O be\\O innovative\\O ,\\O but\\O there\\O 's\\O a\\O nice\\B balance\\O of\\O traditional\\B sushi\\O as\\O well\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218517:3", "sentence": "The nicest waiters in town .", "postag": ["DT", "JJS", "NNS", "IN", "NN", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["det", "amod", "root", "case", "nmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218517:3-0", "target_tags": "The\\O nicest\\O waiters\\B in\\O town\\O .\\O", "opinion_tags": "The\\O nicest\\B waiters\\O in\\O town\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'s_478075037:1", "sentence": "– This place is unbelievably over-rated .", "postag": ["NFP", "DT", "NN", "VBZ", "RB", "JJ", "."], "head": [6, 3, 6, 6, 6, 0, 6], "deprel": ["punct", "det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "en_<PERSON>'s_478075037:1-0", "target_tags": "–\\O This\\O place\\B is\\O unbelievably\\O over-rated\\O .\\O", "opinion_tags": "–\\O This\\O place\\O is\\O unbelievably\\O over-rated\\B .\\O", "sentiment": "negative"}]}, {"id": "en_<PERSON>'s_478075037:2", "sentence": "If I want to stand in line on Sunday for an hour to get average brunch food , then I would put Murphy 's at the top of the list .", "postag": ["IN", "PRP", "VBP", "TO", "VB", "IN", "NN", "IN", "NNP", "IN", "DT", "NN", "TO", "VB", "JJ", "NN", "NN", ",", "RB", "PRP", "MD", "VB", "NNP", "POS", "IN", "DT", "NN", "IN", "DT", "NN", "."], "head": [3, 3, 22, 5, 3, 7, 5, 9, 5, 12, 12, 5, 14, 5, 17, 17, 14, 22, 22, 22, 22, 0, 22, 23, 27, 27, 22, 30, 30, 27, 22], "deprel": ["mark", "nsubj", "advcl", "mark", "xcomp", "case", "obl", "case", "obl", "case", "det", "obl", "mark", "advcl", "amod", "compound", "obj", "punct", "advmod", "nsubj", "aux", "root", "obj", "case", "case", "det", "obl", "case", "det", "nmod", "punct"], "triples": [{"uid": "en_<PERSON>'s_478075037:2-0", "target_tags": "If\\O I\\O want\\O to\\O stand\\O in\\O line\\O on\\O Sunday\\O for\\O an\\O hour\\O to\\O get\\O average\\O brunch\\B food\\I ,\\O then\\O I\\O would\\O put\\O Murphy\\O 's\\O at\\O the\\O top\\O of\\O the\\O list\\O .\\O", "opinion_tags": "If\\O I\\O want\\O to\\O stand\\O in\\O line\\O on\\O Sunday\\O for\\O an\\O hour\\O to\\O get\\O average\\B brunch\\O food\\O ,\\O then\\O I\\O would\\O put\\O Murphy\\O 's\\O at\\O the\\O top\\O of\\O the\\O list\\O .\\O", "sentiment": "neutral"}]}, {"id": "en_<PERSON>'s_478075037:3", "sentence": "The regular menu here is slightly above average that is not worth the snotty attitude that you receive .", "postag": ["DT", "JJ", "NN", "RB", "VBZ", "RB", "JJ", "NN", "WDT", "VBZ", "RB", "JJ", "DT", "JJ", "NN", "WDT", "PRP", "VBP", "."], "head": [3, 3, 8, 3, 8, 7, 8, 0, 12, 12, 12, 8, 15, 15, 12, 18, 18, 15, 8], "deprel": ["det", "amod", "nsubj", "advmod", "cop", "advmod", "amod", "root", "nsubj", "cop", "advmod", "acl:relcl", "det", "amod", "obj", "obj", "nsubj", "acl:relcl", "punct"], "triples": [{"uid": "en_<PERSON>'s_478075037:3-0", "target_tags": "The\\O regular\\B menu\\I here\\O is\\O slightly\\O above\\O average\\O that\\O is\\O not\\O worth\\O the\\O snotty\\O attitude\\O that\\O you\\O receive\\O .\\O", "opinion_tags": "The\\O regular\\O menu\\O here\\O is\\O slightly\\O above\\B average\\I that\\O is\\O not\\B worth\\I the\\I snotty\\I attitude\\I that\\O you\\O receive\\O .\\O", "sentiment": "neutral"}]}, {"id": "en_BlueRibbonSushi_478218509:1", "sentence": "Your a sushi fan , you love expertly cut fish , great sake , a killer SOHO location , and of course : Salmon , Tuna , Fluke , Yellow Tail , Cod , Mackeral , Jellyfish , Sea Urchin , Shrimp , Lobster , Sea Bream , Trout , Milk Fish , Blue Fin Tuna , Eel , Crab , Sardine , Monk Fish , Roe , Scallop , Oysters , and a varity of Toro .", "postag": ["PRP$", "DT", "NN", "NN", ",", "PRP", "VBP", "RB", "VBN", "NN", ",", "JJ", "NN", ",", "DT", "NN", "NN", "NN", ",", "CC", "RB", "NN", ":", "NN", ",", "NN", ",", "NN", ",", "JJ", "NN", ",", "NN", ",", "NN", ",", "NN", ",", "NN", "NN", ",", "NN", ",", "NN", ",", "NN", "NN", ",", "NN", ",", "NN", "NN", ",", "JJ", "NN", "NN", ",", "NN", ",", "NN", ",", "NN", ",", "NN", "NN", ",", "NN", ",", "NN", ",", "NNS", ",", "CC", "DT", "NN", "IN", "NNP", "."], "head": [4, 4, 4, 0, 4, 7, 4, 9, 10, 7, 13, 13, 10, 18, 18, 18, 18, 10, 22, 22, 22, 10, 24, 10, 26, 24, 28, 24, 31, 31, 24, 33, 24, 35, 24, 37, 24, 40, 40, 24, 42, 24, 44, 24, 47, 47, 24, 49, 24, 52, 52, 24, 56, 56, 56, 24, 58, 24, 60, 24, 62, 24, 65, 65, 24, 67, 24, 69, 24, 71, 24, 75, 75, 75, 24, 77, 75, 4], "deprel": ["nmod:poss", "det", "compound", "root", "punct", "nsubj", "parataxis", "advmod", "amod", "obj", "punct", "amod", "conj", "punct", "det", "compound", "compound", "conj", "punct", "cc", "case", "conj", "punct", "conj", "punct", "conj", "punct", "conj", "punct", "amod", "conj", "punct", "conj", "punct", "conj", "punct", "conj", "punct", "compound", "conj", "punct", "conj", "punct", "conj", "punct", "compound", "conj", "punct", "conj", "punct", "compound", "conj", "punct", "amod", "compound", "conj", "punct", "conj", "punct", "conj", "punct", "conj", "punct", "compound", "conj", "punct", "conj", "punct", "conj", "punct", "conj", "punct", "cc", "det", "conj", "case", "nmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218509:1-0", "target_tags": "Your\\O a\\O sushi\\O fan\\O ,\\O you\\O love\\O expertly\\O cut\\O fish\\B ,\\O great\\O sake\\O ,\\O a\\O killer\\O SOHO\\O location\\O ,\\O and\\O of\\O course\\O :\\O Salmon\\O ,\\O Tuna\\O ,\\O Fluke\\O ,\\O Yellow\\O Tail\\O ,\\O Cod\\O ,\\O Mackeral\\O ,\\O Jellyfish\\O ,\\O Sea\\O Urchin\\O ,\\O Shrimp\\O ,\\O Lobster\\O ,\\O Sea\\O Bream\\O ,\\O Trout\\O ,\\O Milk\\O Fish\\O ,\\O Blue\\O Fin\\O Tuna\\O ,\\O Eel\\O ,\\O Crab\\O ,\\O Sardine\\O ,\\O Monk\\O Fish\\O ,\\O Roe\\O ,\\O Scallop\\O ,\\O Oysters\\O ,\\O and\\O a\\O varity\\O of\\O Toro\\O .\\O", "opinion_tags": "Your\\O a\\O sushi\\O fan\\O ,\\O you\\O love\\B expertly\\B cut\\I fish\\O ,\\O great\\O sake\\O ,\\O a\\O killer\\O SOHO\\O location\\O ,\\O and\\O of\\O course\\O :\\O Salmon\\O ,\\O Tuna\\O ,\\O Fluke\\O ,\\O Yellow\\O Tail\\O ,\\O Cod\\O ,\\O Mackeral\\O ,\\O Jellyfish\\O ,\\O Sea\\O Urchin\\O ,\\O Shrimp\\O ,\\O Lobster\\O ,\\O Sea\\O Bream\\O ,\\O Trout\\O ,\\O Milk\\O Fish\\O ,\\O Blue\\O Fin\\O Tuna\\O ,\\O Eel\\O ,\\O Crab\\O ,\\O Sardine\\O ,\\O Monk\\O Fish\\O ,\\O Roe\\O ,\\O Scallop\\O ,\\O Oysters\\O ,\\O and\\O a\\O varity\\O of\\O Toro\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218509:1-1", "target_tags": "Your\\O a\\O sushi\\O fan\\O ,\\O you\\O love\\O expertly\\O cut\\O fish\\O ,\\O great\\O sake\\B ,\\O a\\O killer\\O SOHO\\O location\\O ,\\O and\\O of\\O course\\O :\\O Salmon\\O ,\\O Tuna\\O ,\\O Fluke\\O ,\\O Yellow\\O Tail\\O ,\\O Cod\\O ,\\O Mackeral\\O ,\\O Jellyfish\\O ,\\O Sea\\O Urchin\\O ,\\O Shrimp\\O ,\\O Lobster\\O ,\\O Sea\\O Bream\\O ,\\O Trout\\O ,\\O Milk\\O Fish\\O ,\\O Blue\\O Fin\\O Tuna\\O ,\\O Eel\\O ,\\O Crab\\O ,\\O Sardine\\O ,\\O Monk\\O Fish\\O ,\\O Roe\\O ,\\O Scallop\\O ,\\O Oysters\\O ,\\O and\\O a\\O varity\\O of\\O Toro\\O .\\O", "opinion_tags": "Your\\O a\\O sushi\\O fan\\O ,\\O you\\O love\\O expertly\\O cut\\O fish\\O ,\\O great\\B sake\\O ,\\O a\\O killer\\O SOHO\\O location\\O ,\\O and\\O of\\O course\\O :\\O Salmon\\O ,\\O Tuna\\O ,\\O Fluke\\O ,\\O Yellow\\O Tail\\O ,\\O Cod\\O ,\\O Mackeral\\O ,\\O Jellyfish\\O ,\\O Sea\\O Urchin\\O ,\\O Shrimp\\O ,\\O Lobster\\O ,\\O Sea\\O Bream\\O ,\\O Trout\\O ,\\O Milk\\O Fish\\O ,\\O Blue\\O Fin\\O Tuna\\O ,\\O Eel\\O ,\\O Crab\\O ,\\O Sardine\\O ,\\O Monk\\O Fish\\O ,\\O Roe\\O ,\\O Scallop\\O ,\\O Oysters\\O ,\\O and\\O a\\O varity\\O of\\O Toro\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218509:1-2", "target_tags": "Your\\O a\\O sushi\\O fan\\O ,\\O you\\O love\\O expertly\\O cut\\O fish\\O ,\\O great\\O sake\\O ,\\O a\\O killer\\O SOHO\\B location\\I ,\\O and\\O of\\O course\\O :\\O Salmon\\O ,\\O Tuna\\O ,\\O Fluke\\O ,\\O Yellow\\O Tail\\O ,\\O Cod\\O ,\\O Mackeral\\O ,\\O Jellyfish\\O ,\\O Sea\\O Urchin\\O ,\\O Shrimp\\O ,\\O Lobster\\O ,\\O Sea\\O Bream\\O ,\\O Trout\\O ,\\O Milk\\O Fish\\O ,\\O Blue\\O Fin\\O Tuna\\O ,\\O Eel\\O ,\\O Crab\\O ,\\O Sardine\\O ,\\O Monk\\O Fish\\O ,\\O Roe\\O ,\\O Scallop\\O ,\\O Oysters\\O ,\\O and\\O a\\O varity\\O of\\O Toro\\O .\\O", "opinion_tags": "Your\\O a\\O sushi\\O fan\\O ,\\O you\\O love\\O expertly\\O cut\\O fish\\O ,\\O great\\O sake\\O ,\\O a\\O killer\\B SOHO\\O location\\O ,\\O and\\O of\\O course\\O :\\O Salmon\\O ,\\O Tuna\\O ,\\O Fluke\\O ,\\O Yellow\\O Tail\\O ,\\O Cod\\O ,\\O Mackeral\\O ,\\O Jellyfish\\O ,\\O Sea\\O Urchin\\O ,\\O Shrimp\\O ,\\O Lobster\\O ,\\O Sea\\O Bream\\O ,\\O Trout\\O ,\\O Milk\\O Fish\\O ,\\O Blue\\O Fin\\O Tuna\\O ,\\O Eel\\O ,\\O Crab\\O ,\\O Sardine\\O ,\\O Monk\\O Fish\\O ,\\O Roe\\O ,\\O Scallop\\O ,\\O Oysters\\O ,\\O and\\O a\\O varity\\O of\\O Toro\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218509:4", "sentence": "Bring your cell phone cause you may have to wait to get into the best sushi restaurant in the world : BLUE RIBBON SUSHI .", "postag": ["VB", "PRP$", "NN", "NN", "IN", "PRP", "MD", "VB", "TO", "VB", "TO", "VB", "IN", "DT", "JJS", "NN", "NN", "IN", "DT", "NN", ":", "NNP", "NNP", "NNP", "."], "head": [0, 4, 4, 1, 8, 8, 8, 1, 10, 8, 12, 10, 17, 17, 17, 17, 12, 20, 20, 17, 24, 24, 24, 17, 1], "deprel": ["root", "nmod:poss", "compound", "obj", "mark", "nsubj", "aux", "advcl", "mark", "xcomp", "mark", "advcl", "case", "det", "amod", "compound", "obl", "case", "det", "nmod", "punct", "compound", "compound", "appos", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218509:4-0", "target_tags": "Bring\\O your\\O cell\\O phone\\O cause\\O you\\O may\\O have\\O to\\O wait\\O to\\O get\\O into\\O the\\O best\\O sushi\\O restaurant\\O in\\O the\\O world\\O :\\O BLUE\\B RIBBON\\I SUSHI\\I .\\O", "opinion_tags": "Bring\\O your\\O cell\\O phone\\O cause\\O you\\O may\\O have\\O to\\O wait\\O to\\O get\\O into\\O the\\O best\\B sushi\\O restaurant\\O in\\O the\\O world\\O :\\O BLUE\\O RIBBON\\O SUSHI\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218342:3", "sentence": "Hands down , the best tuna I have ever had .", "postag": ["NNS", "RB", ",", "DT", "JJS", "NN", "PRP", "VBP", "RB", "VBN", "."], "head": [2, 6, 6, 6, 6, 0, 10, 10, 10, 6, 6], "deprel": ["nsubj", "advmod", "punct", "det", "amod", "root", "nsubj", "aux", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218342:3-0", "target_tags": "Hands\\O down\\O ,\\O the\\O best\\O tuna\\B I\\O have\\O ever\\O had\\O .\\O", "opinion_tags": "Hands\\O down\\O ,\\O the\\O best\\B tuna\\O I\\O have\\O ever\\O had\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218342:4", "sentence": "Blue Ribbon lives up to it 's fantastic reputation .", "postag": ["NNP", "NNP", "VBZ", "RP", "IN", "PRP", "VBZ", "JJ", "NN", "."], "head": [2, 3, 0, 3, 9, 9, 9, 9, 3, 3], "deprel": ["compound", "nsubj", "root", "compound:prt", "mark", "nsubj", "cop", "amod", "advcl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218342:4-0", "target_tags": "Blue\\B Ribbon\\I lives\\O up\\O to\\O it\\O 's\\O fantastic\\O reputation\\O .\\O", "opinion_tags": "Blue\\O Ribbon\\O lives\\O up\\O to\\O it\\O 's\\O fantastic\\B reputation\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218512:1", "sentence": "Great value sushi with high quality & nice setting .", "postag": ["JJ", "NN", "NN", "IN", "JJ", "JJ", "CC", "JJ", "NN", "."], "head": [3, 3, 0, 9, 6, 9, 8, 6, 3, 3], "deprel": ["amod", "compound", "root", "case", "amod", "amod", "cc", "conj", "nmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218512:1-0", "target_tags": "Great\\O value\\O sushi\\B with\\O high\\O quality\\O &\\O nice\\O setting\\O .\\O", "opinion_tags": "Great\\B value\\I sushi\\O with\\O high\\B quality\\I &\\O nice\\B setting\\I .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218512:1-1", "target_tags": "Great\\O value\\O sushi\\O with\\O high\\O quality\\O &\\O nice\\O setting\\B .\\O", "opinion_tags": "Great\\O value\\O sushi\\O with\\O high\\O quality\\O &\\O nice\\B setting\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218512:3", "sentence": "Try the Chef 's Choice for sushi as the smoked yellowtail was incredible and the rolls were also tasty .", "postag": ["VB", "DT", "NN", "POS", "NN", "IN", "NN", "IN", "DT", "JJ", "NN", "VBD", "JJ", "CC", "DT", "NNS", "VBD", "RB", "JJ", "."], "head": [0, 3, 5, 3, 1, 7, 5, 13, 11, 11, 13, 13, 1, 19, 16, 19, 19, 19, 1, 1], "deprel": ["root", "det", "nmod:poss", "case", "obj", "case", "nmod", "mark", "det", "amod", "nsubj", "cop", "advcl", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218512:3-0", "target_tags": "Try\\O the\\O Chef\\O 's\\O Choice\\O for\\O sushi\\O as\\O the\\O smoked\\O yellowtail\\O was\\O incredible\\O and\\O the\\O rolls\\B were\\O also\\O tasty\\O .\\O", "opinion_tags": "Try\\O the\\O Chef\\O 's\\O Choice\\O for\\O sushi\\O as\\O the\\O smoked\\O yellowtail\\O was\\O incredible\\O and\\O the\\O rolls\\O were\\O also\\O tasty\\B .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218512:3-1", "target_tags": "Try\\O the\\O Chef\\O 's\\O Choice\\O for\\O sushi\\O as\\O the\\O smoked\\B yellowtail\\I was\\O incredible\\O and\\O the\\O rolls\\O were\\O also\\O tasty\\O .\\O", "opinion_tags": "Try\\O the\\O Chef\\O 's\\O Choice\\O for\\O sushi\\O as\\O the\\O smoked\\O yellowtail\\O was\\O incredible\\B and\\O the\\O rolls\\O were\\O also\\O tasty\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478542921:0", "sentence": "Poor customer service / poor pizza .", "postag": ["JJ", "NN", "NN", ",", "JJ", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 3], "deprel": ["amod", "compound", "root", "cc", "amod", "conj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542921:0-0", "target_tags": "Poor\\O customer\\B service\\I /\\O poor\\O pizza\\O .\\O", "opinion_tags": "Poor\\B customer\\O service\\O /\\O poor\\O pizza\\O .\\O", "sentiment": "negative"}, {"uid": "en_MiopostoCaffe_478542921:0-1", "target_tags": "Poor\\O customer\\O service\\O /\\O poor\\O pizza\\B .\\O", "opinion_tags": "Poor\\B customer\\O service\\O /\\O poor\\O pizza\\O .\\O", "sentiment": "negative"}]}, {"id": "en_MiopostoCaffe_478542921:1", "sentence": "– As with most restaurants in Seattle , Mioposto 's service was bad and the food was overpriced .", "postag": ["NFP", "IN", "IN", "JJS", "NNS", "IN", "NNP", ",", "NNP", "POS", "NN", "VBD", "JJ", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [13, 5, 5, 5, 13, 7, 5, 13, 11, 9, 13, 13, 0, 18, 16, 18, 18, 13, 13], "deprel": ["punct", "case", "case", "amod", "obl", "case", "nmod", "punct", "nmod:poss", "case", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542921:1-0", "target_tags": "–\\O As\\O with\\O most\\O restaurants\\O in\\O Seattle\\O ,\\O Mioposto\\O 's\\O service\\B was\\O bad\\O and\\O the\\O food\\O was\\O overpriced\\O .\\O", "opinion_tags": "–\\O As\\O with\\O most\\O restaurants\\O in\\O Seattle\\O ,\\O Mioposto\\O 's\\O service\\O was\\O bad\\B and\\O the\\O food\\O was\\O overpriced\\O .\\O", "sentiment": "negative"}, {"uid": "en_MiopostoCaffe_478542921:1-1", "target_tags": "–\\O As\\O with\\O most\\O restaurants\\O in\\O Seattle\\O ,\\O Mioposto\\O 's\\O service\\O was\\O bad\\O and\\O the\\O food\\B was\\O overpriced\\O .\\O", "opinion_tags": "–\\O As\\O with\\O most\\O restaurants\\O in\\O Seattle\\O ,\\O Mioposto\\O 's\\O service\\O was\\O bad\\O and\\O the\\O food\\O was\\O overpriced\\B .\\O", "sentiment": "negative"}]}, {"id": "en_MiopostoCaffe_478542921:2", "sentence": "I know many people have their favorite types of pizza and pizza places , but Mioposto 's pizza lacks quality and good taste .", "postag": ["PRP", "VBP", "JJ", "NNS", "VBP", "PRP$", "JJ", "NNS", "IN", "NN", "CC", "NN", "NNS", ",", "CC", "NNP", "POS", "NN", "VBZ", "NN", "CC", "JJ", "NN", "."], "head": [2, 0, 4, 5, 2, 8, 8, 5, 10, 8, 13, 13, 10, 19, 19, 18, 16, 19, 2, 19, 23, 23, 20, 2], "deprel": ["nsubj", "root", "amod", "nsubj", "ccomp", "nmod:poss", "amod", "obj", "case", "nmod", "cc", "compound", "conj", "punct", "cc", "nmod:poss", "case", "nsubj", "conj", "obj", "cc", "amod", "conj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542921:2-0", "target_tags": "I\\O know\\O many\\O people\\O have\\O their\\O favorite\\O types\\O of\\O pizza\\O and\\O pizza\\O places\\O ,\\O but\\O Mioposto\\O 's\\O pizza\\B lacks\\O quality\\O and\\O good\\O taste\\O .\\O", "opinion_tags": "I\\O know\\O many\\O people\\O have\\O their\\O favorite\\O types\\O of\\O pizza\\O and\\O pizza\\O places\\O ,\\O but\\O Mioposto\\O 's\\O pizza\\O lacks\\B quality\\I and\\I good\\I taste\\I .\\O", "sentiment": "negative"}]}, {"id": "en_MiopostoCaffe_478542921:3", "sentence": "To be honest , I 've had better frozen pizza .", "postag": ["TO", "VB", "JJ", ",", "PRP", "VBP", "VBN", "JJR", "JJ", "NN", "."], "head": [3, 3, 7, 7, 7, 7, 0, 10, 10, 7, 7], "deprel": ["mark", "cop", "advcl", "punct", "nsubj", "aux", "root", "amod", "amod", "obj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542921:3-0", "target_tags": "To\\O be\\O honest\\O ,\\O I\\O 've\\O had\\O better\\O frozen\\O pizza\\B .\\O", "opinion_tags": "To\\O be\\O honest\\O ,\\O I\\O 've\\O had\\O better\\B frozen\\O pizza\\O .\\O", "sentiment": "negative"}]}, {"id": "en_MiopostoCaffe_478542921:5", "sentence": "The only positive thing about Mioposto is the nice location .", "postag": ["DT", "JJ", "JJ", "NN", "IN", "NNP", "VBZ", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 10, 6, 4, 10, 10, 10, 0, 10], "deprel": ["det", "amod", "amod", "nsubj", "case", "nmod", "cop", "det", "amod", "root", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542921:5-0", "target_tags": "The\\O only\\O positive\\O thing\\O about\\O Mioposto\\O is\\O the\\O nice\\O location\\B .\\O", "opinion_tags": "The\\O only\\O positive\\O thing\\O about\\O Mioposto\\O is\\O the\\O nice\\B location\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218339:1", "sentence": "I was frankly shocked when I read the bad reviews - this place is fantastic ; it has not let us down in any way , and we 've eaten here more than 10 times .", "postag": ["PRP", "VBD", "RB", "JJ", "WRB", "PRP", "VBD", "DT", "JJ", "NNS", ",", "DT", "NN", "VBZ", "JJ", ",", "PRP", "VBZ", "RB", "VB", "PRP", "RP", "IN", "DT", "NN", ",", "CC", "PRP", "VBP", "VBN", "RB", "JJR", "IN", "CD", "NNS", "."], "head": [4, 4, 4, 0, 7, 7, 4, 10, 10, 7, 4, 13, 15, 15, 4, 4, 20, 20, 20, 4, 20, 20, 25, 25, 20, 30, 30, 30, 30, 4, 30, 34, 32, 35, 30, 4], "deprel": ["nsubj", "cop", "advmod", "root", "mark", "nsubj", "advcl", "det", "amod", "obj", "punct", "det", "nsubj", "cop", "parataxis", "punct", "nsubj", "aux", "advmod", "parataxis", "obj", "compound:prt", "case", "det", "obl", "punct", "cc", "nsubj", "aux", "conj", "advmod", "advmod", "fixed", "nummod", "obl:tmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218339:1-0", "target_tags": "I\\O was\\O frankly\\O shocked\\O when\\O I\\O read\\O the\\O bad\\O reviews\\O -\\O this\\O place\\B is\\O fantastic\\O ;\\O it\\O has\\O not\\O let\\O us\\O down\\O in\\O any\\O way\\O ,\\O and\\O we\\O 've\\O eaten\\O here\\O more\\O than\\O 10\\O times\\O .\\O", "opinion_tags": "I\\O was\\O frankly\\O shocked\\O when\\O I\\O read\\O the\\O bad\\O reviews\\O -\\O this\\O place\\O is\\O fantastic\\B ;\\O it\\O has\\O not\\O let\\O us\\O down\\O in\\O any\\O way\\O ,\\O and\\O we\\O 've\\O eaten\\O here\\O more\\O than\\O 10\\O times\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218339:3", "sentence": "The food is fantastic , and the waiting staff has been perfect every single time we 've been there .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "CC", "DT", "NN", "NN", "VBZ", "VBN", "JJ", "DT", "JJ", "NN", "PRP", "VBP", "VBN", "RB", "."], "head": [2, 4, 4, 0, 12, 12, 9, 9, 12, 12, 12, 4, 15, 15, 12, 19, 19, 19, 15, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "det", "compound", "nsubj", "aux", "cop", "conj", "det", "amod", "obl:tmod", "nsubj", "aux", "cop", "acl:relcl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218339:3-0", "target_tags": "The\\O food\\B is\\O fantastic\\O ,\\O and\\O the\\O waiting\\O staff\\O has\\O been\\O perfect\\O every\\O single\\O time\\O we\\O 've\\O been\\O there\\O .\\O", "opinion_tags": "The\\O food\\O is\\O fantastic\\B ,\\O and\\O the\\O waiting\\O staff\\O has\\O been\\O perfect\\O every\\O single\\O time\\O we\\O 've\\O been\\O there\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218339:3-1", "target_tags": "The\\O food\\O is\\O fantastic\\O ,\\O and\\O the\\O waiting\\B staff\\I has\\O been\\O perfect\\O every\\O single\\O time\\O we\\O 've\\O been\\O there\\O .\\O", "opinion_tags": "The\\O food\\O is\\O fantastic\\O ,\\O and\\O the\\O waiting\\O staff\\O has\\O been\\O perfect\\B every\\O single\\O time\\O we\\O 've\\O been\\O there\\O .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538242:4", "sentence": "Seabass on lobster risotto was the best .", "postag": ["NN", "IN", "NN", "NN", "VBD", "DT", "JJS", "."], "head": [7, 4, 4, 1, 7, 7, 0, 7], "deprel": ["nsubj", "case", "compound", "nmod", "cop", "det", "root", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538242:4-0", "target_tags": "Seabass\\B on\\I lobster\\I risotto\\I was\\O the\\O best\\O .\\O", "opinion_tags": "Seabass\\O on\\O lobster\\O risotto\\O was\\O the\\O best\\B .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538242:5", "sentence": "Caesar salad was superb .", "postag": ["NNP", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["compound", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538242:5-0", "target_tags": "Caesar\\B salad\\I was\\O superb\\O .\\O", "opinion_tags": "Caesar\\O salad\\O was\\O superb\\B .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538242:6", "sentence": "Great bottle of wine .", "postag": ["JJ", "NN", "IN", "NN", "."], "head": [2, 0, 4, 2, 2], "deprel": ["amod", "root", "case", "nmod", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538242:6-0", "target_tags": "Great\\O bottle\\B of\\I wine\\I .\\O", "opinion_tags": "Great\\B bottle\\O of\\O wine\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_479842021:3", "sentence": "The food was ok , but the service was so poor that the food was cold buy the time everyone in my party was served .", "postag": ["DT", "NN", "VBD", "JJ", ",", "CC", "DT", "NN", "VBD", "RB", "JJ", "IN", "DT", "NN", "VBD", "JJ", "VB", "DT", "NN", "NN", "IN", "PRP$", "NN", "VBD", "VBN", "."], "head": [2, 4, 4, 0, 11, 11, 8, 11, 11, 11, 4, 16, 14, 16, 16, 11, 16, 19, 17, 25, 23, 23, 20, 25, 19, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "det", "nsubj", "cop", "advmod", "conj", "mark", "det", "nsubj", "cop", "ccomp", "xcomp", "det", "obj", "nsubj:pass", "case", "nmod:poss", "nmod", "aux:pass", "acl:relcl", "punct"], "triples": [{"uid": "en_SchoonerOrLater_479842021:3-0", "target_tags": "The\\O food\\B was\\O ok\\O ,\\O but\\O the\\O service\\O was\\O so\\O poor\\O that\\O the\\O food\\O was\\O cold\\O buy\\O the\\O time\\O everyone\\O in\\O my\\O party\\O was\\O served\\O .\\O", "opinion_tags": "The\\O food\\O was\\O ok\\B ,\\O but\\O the\\O service\\O was\\O so\\O poor\\O that\\O the\\O food\\O was\\O cold\\O buy\\O the\\O time\\O everyone\\O in\\O my\\O party\\O was\\O served\\O .\\O", "sentiment": "neutral"}, {"uid": "en_SchoonerOrLater_479842021:3-1", "target_tags": "The\\O food\\O was\\O ok\\O ,\\O but\\O the\\O service\\B was\\O so\\O poor\\O that\\O the\\O food\\O was\\O cold\\O buy\\O the\\O time\\O everyone\\O in\\O my\\O party\\O was\\O served\\O .\\O", "opinion_tags": "The\\O food\\O was\\O ok\\O ,\\O but\\O the\\O service\\O was\\O so\\O poor\\B that\\O the\\O food\\O was\\O cold\\O buy\\O the\\O time\\O everyone\\O in\\O my\\O party\\O was\\O served\\O .\\O", "sentiment": "negative"}]}, {"id": "en_ParkChaletGardenRestaurant_477778210:0", "sentence": "AVOID THE PLACE", "postag": ["VB", "DT", "NN"], "head": [0, 3, 1], "deprel": ["root", "det", "obj"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778210:0-0", "target_tags": "AVOID\\O THE\\O PLACE\\B", "opinion_tags": "AVOID\\B THE\\O PLACE\\O", "sentiment": "negative"}]}, {"id": "en_ParkChaletGardenRestaurant_477778210:3", "sentence": "When I got there I sat up stairs where the atmosphere was cozy & the service was horrible !", "postag": ["WRB", "PRP", "VBD", "RB", "PRP", "VBD", "RP", "NNS", "WRB", "DT", "NN", "VBD", "JJ", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [3, 3, 6, 3, 6, 0, 6, 6, 13, 11, 13, 13, 8, 18, 16, 18, 18, 6, 6], "deprel": ["mark", "nsubj", "advcl", "advmod", "nsubj", "root", "compound:prt", "obj", "mark", "det", "nsubj", "cop", "acl:relcl", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778210:3-0", "target_tags": "When\\O I\\O got\\O there\\O I\\O sat\\O up\\O stairs\\O where\\O the\\O atmosphere\\B was\\O cozy\\O &\\O the\\O service\\O was\\O horrible\\O !\\O", "opinion_tags": "When\\O I\\O got\\O there\\O I\\O sat\\O up\\O stairs\\O where\\O the\\O atmosphere\\O was\\O cozy\\B &\\O the\\O service\\O was\\O horrible\\O !\\O", "sentiment": "positive"}, {"uid": "en_ParkChaletGardenRestaurant_477778210:3-1", "target_tags": "When\\O I\\O got\\O there\\O I\\O sat\\O up\\O stairs\\O where\\O the\\O atmosphere\\O was\\O cozy\\O &\\O the\\O service\\B was\\O horrible\\O !\\O", "opinion_tags": "When\\O I\\O got\\O there\\O I\\O sat\\O up\\O stairs\\O where\\O the\\O atmosphere\\O was\\O cozy\\O &\\O the\\O service\\O was\\O horrible\\B !\\O", "sentiment": "negative"}]}, {"id": "en_ParkChaletGardenRestaurant_477778210:10", "sentence": "We left without ever getting service .", "postag": ["PRP", "VBD", "IN", "RB", "VBG", "NN", "."], "head": [2, 0, 5, 5, 2, 5, 2], "deprel": ["nsubj", "root", "mark", "advmod", "advcl", "obj", "punct"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778210:10-0", "target_tags": "We\\O left\\O without\\O ever\\O getting\\O service\\B .\\O", "opinion_tags": "We\\O left\\O without\\B ever\\I getting\\O service\\O .\\O", "sentiment": "negative"}]}, {"id": "en_Ray'sBoathouse_477775920:0", "sentence": "Best Crab Cakes in Town", "postag": ["JJS", "NN", "NNS", "IN", "NN"], "head": [3, 3, 0, 5, 3], "deprel": ["amod", "compound", "root", "case", "nmod"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775920:0-0", "target_tags": "Best\\O Crab\\B Cakes\\I in\\O Town\\O", "opinion_tags": "Best\\B Crab\\O Cakes\\O in\\O Town\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775920:3", "sentence": "Great seasonal fish and seafood , with a classy waterfront setting .", "postag": ["JJ", "JJ", "NN", "CC", "NN", ",", "IN", "DT", "JJ", "NN", "NN", "."], "head": [3, 3, 0, 5, 3, 3, 11, 11, 11, 11, 3, 3], "deprel": ["amod", "amod", "root", "cc", "conj", "punct", "case", "det", "amod", "compound", "nmod", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775920:3-0", "target_tags": "Great\\O seasonal\\B fish\\I and\\O seafood\\O ,\\O with\\O a\\O classy\\O waterfront\\O setting\\O .\\O", "opinion_tags": "Great\\B seasonal\\O fish\\O and\\O seafood\\O ,\\O with\\O a\\O classy\\O waterfront\\O setting\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'sBoathouse_477775920:3-1", "target_tags": "Great\\O seasonal\\O fish\\O and\\O seafood\\B ,\\O with\\O a\\O classy\\O waterfront\\O setting\\O .\\O", "opinion_tags": "Great\\B seasonal\\O fish\\O and\\O seafood\\O ,\\O with\\O a\\O classy\\O waterfront\\O setting\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'sBoathouse_477775920:3-2", "target_tags": "Great\\O seasonal\\O fish\\O and\\O seafood\\O ,\\O with\\O a\\O classy\\O waterfront\\B setting\\I .\\O", "opinion_tags": "Great\\O seasonal\\O fish\\O and\\O seafood\\O ,\\O with\\O a\\O classy\\B waterfront\\O setting\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478542922:0", "sentence": "Great Pizza , Poor Service", "postag": ["JJ", "NN", ",", "JJ", "NN"], "head": [2, 0, 2, 5, 2], "deprel": ["amod", "root", "punct", "amod", "parataxis"], "triples": [{"uid": "en_MiopostoCaffe_478542922:0-0", "target_tags": "Great\\O Pizza\\B ,\\O Poor\\O Service\\O", "opinion_tags": "Great\\B Pizza\\O ,\\O Poor\\O Service\\O", "sentiment": "positive"}, {"uid": "en_MiopostoCaffe_478542922:0-1", "target_tags": "Great\\O Pizza\\O ,\\O Poor\\O Service\\B", "opinion_tags": "Great\\O Pizza\\O ,\\O Poor\\B Service\\O", "sentiment": "negative"}]}, {"id": "en_MiopostoCaffe_478542922:1", "sentence": "– Love their pizza , especially the mushroom pizza .", "postag": ["NFP", "VB", "PRP$", "NN", ",", "RB", "DT", "NN", "NN", "."], "head": [2, 0, 4, 2, 9, 9, 9, 9, 2, 2], "deprel": ["punct", "root", "nmod:poss", "obj", "punct", "advmod", "det", "compound", "obj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542922:1-0", "target_tags": "–\\O Love\\O their\\O pizza\\B ,\\O especially\\O the\\O mushroom\\O pizza\\O .\\O", "opinion_tags": "–\\O Love\\B their\\O pizza\\O ,\\O especially\\O the\\O mushroom\\O pizza\\O .\\O", "sentiment": "positive"}, {"uid": "en_MiopostoCaffe_478542922:1-1", "target_tags": "–\\O Love\\O their\\O pizza\\O ,\\O especially\\O the\\O mushroom\\B pizza\\I .\\O", "opinion_tags": "–\\O Love\\B their\\O pizza\\O ,\\O especially\\O the\\O mushroom\\O pizza\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478542922:2", "sentence": "Also love their caeser salad .", "postag": ["RB", "VB", "PRP$", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["advmod", "root", "nmod:poss", "amod", "obj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542922:2-0", "target_tags": "Also\\O love\\O their\\O caeser\\B salad\\I .\\O", "opinion_tags": "Also\\O love\\B their\\O caeser\\O salad\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478542922:3", "sentence": "Prefer to order it and pick it up though because I do n't like the servers , one young woman in particular .", "postag": ["VB", "TO", "VB", "PRP", "CC", "VB", "PRP", "RP", "RB", "IN", "PRP", "VBP", "RB", "VB", "DT", "NNS", ",", "CD", "JJ", "NN", "IN", "JJ", "."], "head": [0, 3, 1, 3, 6, 3, 6, 6, 6, 14, 14, 14, 14, 6, 16, 14, 20, 20, 20, 16, 22, 20, 1], "deprel": ["root", "mark", "xcomp", "obj", "cc", "conj", "obj", "compound:prt", "advmod", "mark", "nsubj", "aux", "advmod", "advcl", "det", "obj", "punct", "nummod", "amod", "appos", "case", "nmod", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542922:3-0", "target_tags": "Prefer\\O to\\O order\\O it\\O and\\O pick\\O it\\O up\\O though\\O because\\O I\\O do\\O n't\\O like\\O the\\O servers\\B ,\\O one\\O young\\O woman\\O in\\O particular\\O .\\O", "opinion_tags": "Prefer\\O to\\O order\\O it\\O and\\O pick\\O it\\O up\\O though\\O because\\O I\\O do\\B n't\\I like\\I the\\O servers\\O ,\\O one\\O young\\O woman\\O in\\O particular\\O .\\O", "sentiment": "negative"}, {"uid": "en_MiopostoCaffe_478542922:3-1", "target_tags": "Prefer\\O to\\O order\\O it\\O and\\O pick\\O it\\O up\\O though\\O because\\O I\\O do\\O n't\\O like\\O the\\O servers\\O ,\\O one\\O young\\B woman\\I in\\O particular\\O .\\O", "opinion_tags": "Prefer\\O to\\O order\\O it\\O and\\O pick\\O it\\O up\\O though\\O because\\O I\\O do\\B n't\\I like\\I the\\O servers\\O ,\\O one\\O young\\O woman\\O in\\O particular\\O .\\O", "sentiment": "negative"}]}, {"id": "en_MiopostoCaffe_478542922:7", "sentence": "Many people talk about the great pizza and poor service , so it ca n't just be the rantings of a few dissatisfied customers .", "postag": ["JJ", "NNS", "VBP", "IN", "DT", "JJ", "NN", "CC", "JJ", "NN", ",", "RB", "PRP", "MD", "RB", "RB", "VB", "DT", "NNS", "IN", "DT", "JJ", "JJ", "NNS", "."], "head": [2, 3, 0, 7, 7, 7, 3, 10, 10, 7, 19, 19, 19, 19, 19, 19, 19, 19, 3, 24, 24, 24, 24, 19, 3], "deprel": ["amod", "nsubj", "root", "case", "det", "amod", "obl", "cc", "amod", "conj", "punct", "advmod", "nsubj", "aux", "advmod", "advmod", "cop", "det", "conj", "case", "det", "amod", "amod", "nmod", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542922:7-0", "target_tags": "Many\\O people\\O talk\\O about\\O the\\O great\\O pizza\\B and\\O poor\\O service\\O ,\\O so\\O it\\O ca\\O n't\\O just\\O be\\O the\\O rantings\\O of\\O a\\O few\\O dissatisfied\\O customers\\O .\\O", "opinion_tags": "Many\\O people\\O talk\\O about\\O the\\O great\\B pizza\\O and\\O poor\\O service\\O ,\\O so\\O it\\O ca\\O n't\\O just\\O be\\O the\\O rantings\\O of\\O a\\O few\\O dissatisfied\\O customers\\O .\\O", "sentiment": "positive"}, {"uid": "en_MiopostoCaffe_478542922:7-1", "target_tags": "Many\\O people\\O talk\\O about\\O the\\O great\\O pizza\\O and\\O poor\\O service\\B ,\\O so\\O it\\O ca\\O n't\\O just\\O be\\O the\\O rantings\\O of\\O a\\O few\\O dissatisfied\\O customers\\O .\\O", "opinion_tags": "Many\\O people\\O talk\\O about\\O the\\O great\\O pizza\\O and\\O poor\\B service\\O ,\\O so\\O it\\O ca\\O n't\\O just\\O be\\O the\\O rantings\\O of\\O a\\O few\\O dissatisfied\\O customers\\O .\\O", "sentiment": "negative"}]}, {"id": "en_MiopostoCaffe_478542922:8", "sentence": "It 's a great little place with tons of potential to be a neighborhood joint if the service were n't so impersonal and corporate-like .", "postag": ["PRP", "VBZ", "DT", "JJ", "JJ", "NN", "IN", "NNS", "IN", "NN", "TO", "VB", "DT", "NN", "NN", "IN", "DT", "NN", "VBD", "RB", "RB", "JJ", "CC", "JJ", "."], "head": [6, 6, 6, 6, 6, 0, 8, 6, 10, 8, 15, 15, 15, 15, 10, 22, 18, 22, 22, 22, 22, 15, 24, 22, 6], "deprel": ["nsubj", "cop", "det", "amod", "amod", "root", "case", "nmod", "case", "nmod", "mark", "cop", "det", "compound", "acl", "mark", "det", "nsubj", "cop", "advmod", "advmod", "advcl", "cc", "conj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478542922:8-0", "target_tags": "It\\O 's\\O a\\O great\\O little\\O place\\B with\\O tons\\O of\\O potential\\O to\\O be\\O a\\O neighborhood\\O joint\\O if\\O the\\O service\\O were\\O n't\\O so\\O impersonal\\O and\\O corporate-like\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O great\\B little\\B place\\O with\\O tons\\O of\\O potential\\O to\\O be\\O a\\O neighborhood\\O joint\\O if\\O the\\O service\\O were\\O n't\\O so\\O impersonal\\O and\\O corporate-like\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_479615721:0", "sentence": "Great Breakfast", "postag": ["JJ", "NN"], "head": [2, 0], "deprel": ["amod", "root"], "triples": [{"uid": "en_SchoonerOrLater_479615721:0-0", "target_tags": "Great\\O Breakfast\\B", "opinion_tags": "Great\\B Breakfast\\O", "sentiment": "positive"}, {"uid": "en_SchoonerOrLater_479615721:0-1", "target_tags": "Great\\O Breakfast\\B", "opinion_tags": "Great\\B Breakfast\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_479615721:3", "sentence": "The food is great and they make a mean bloody mary .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "PRP", "VBP", "DT", "JJ", "JJ", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 11, 11, 11, 7, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "nsubj", "conj", "det", "amod", "amod", "obj", "punct"], "triples": [{"uid": "en_SchoonerOrLater_479615721:3-0", "target_tags": "The\\O food\\B is\\O great\\O and\\O they\\O make\\O a\\O mean\\O bloody\\O mary\\O .\\O", "opinion_tags": "The\\O food\\O is\\O great\\B and\\O they\\O make\\O a\\O mean\\O bloody\\O mary\\O .\\O", "sentiment": "positive"}, {"uid": "en_SchoonerOrLater_479615721:3-1", "target_tags": "The\\O food\\O is\\O great\\O and\\O they\\O make\\O a\\O mean\\O bloody\\B mary\\I .\\O", "opinion_tags": "The\\O food\\O is\\O great\\O and\\O they\\O make\\O a\\O mean\\B bloody\\O mary\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_481226185:0", "sentence": "I love breakfast here .", "postag": ["PRP", "VBP", "NN", "RB", "."], "head": [2, 0, 2, 2, 2], "deprel": ["nsubj", "root", "obj", "advmod", "punct"], "triples": [{"uid": "en_SchoonerOrLater_481226185:0-0", "target_tags": "I\\O love\\O breakfast\\B here\\O .\\O", "opinion_tags": "I\\O love\\B breakfast\\O here\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_481226185:1", "sentence": "Their crab eggs benedict is addicting .", "postag": ["PRP$", "NN", "NNS", "NN", "VBZ", "JJ", "."], "head": [4, 3, 4, 6, 6, 0, 6], "deprel": ["nmod:poss", "compound", "compound", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_SchoonerOrLater_481226185:1-0", "target_tags": "Their\\O crab\\B eggs\\I benedict\\I is\\O addicting\\O .\\O", "opinion_tags": "Their\\O crab\\O eggs\\O benedict\\O is\\O addicting\\B .\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_481226185:2", "sentence": "All their menu items are a hit , and they serve mimosas .", "postag": ["PDT", "PRP$", "NN", "NNS", "VBP", "DT", "NN", ",", "CC", "PRP", "VBP", "NNS", "."], "head": [4, 4, 4, 7, 7, 7, 0, 11, 11, 11, 7, 11, 7], "deprel": ["det:predet", "nmod:poss", "compound", "nsubj", "cop", "det", "root", "punct", "cc", "nsubj", "conj", "obj", "punct"], "triples": [{"uid": "en_SchoonerOrLater_481226185:2-0", "target_tags": "All\\O their\\O menu\\B items\\I are\\O a\\O hit\\O ,\\O and\\O they\\O serve\\O mimosas\\O .\\O", "opinion_tags": "All\\O their\\O menu\\O items\\O are\\O a\\O hit\\B ,\\O and\\O they\\O serve\\O mimosas\\O .\\O", "sentiment": "positive"}]}, {"id": "en_PagodaRestaurant_479983203:0", "sentence": "best chinese food i have tasted in a long time", "postag": ["JJS", "JJ", "NN", "PRP", "VBP", "VBN", "IN", "DT", "JJ", "NN"], "head": [3, 3, 0, 6, 6, 3, 10, 10, 10, 6], "deprel": ["amod", "amod", "root", "nsubj", "aux", "acl:relcl", "case", "det", "amod", "obl"], "triples": [{"uid": "en_PagodaRestaurant_479983203:0-0", "target_tags": "best\\O chinese\\B food\\I i\\O have\\O tasted\\O in\\O a\\O long\\O time\\O", "opinion_tags": "best\\B chinese\\O food\\O i\\O have\\O tasted\\O in\\O a\\O long\\O time\\O", "sentiment": "positive"}]}, {"id": "en_PagodaRestaurant_479983203:4", "sentence": "the ambiance of the restaurant was nice and good for fine dinning .", "postag": ["DT", "NN", "IN", "DT", "NN", "VBD", "JJ", "CC", "JJ", "IN", "JJ", "NN", "."], "head": [2, 7, 5, 5, 2, 7, 0, 9, 7, 12, 12, 7, 7], "deprel": ["det", "nsubj", "case", "det", "nmod", "cop", "root", "cc", "conj", "case", "amod", "obl", "punct"], "triples": [{"uid": "en_PagodaRestaurant_479983203:4-0", "target_tags": "the\\O ambiance\\B of\\O the\\O restaurant\\O was\\O nice\\O and\\O good\\O for\\O fine\\O dinning\\O .\\O", "opinion_tags": "the\\O ambiance\\O of\\O the\\O restaurant\\O was\\O nice\\B and\\O good\\B for\\O fine\\O dinning\\O .\\O", "sentiment": "positive"}]}, {"id": "en_PagodaRestaurant_479983203:6", "sentence": "the staff was very nice and courteous and obviously chinese .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "JJ", "CC", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 10, 10, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "en_PagodaRestaurant_479983203:6-0", "target_tags": "the\\O staff\\B was\\O very\\O nice\\O and\\O courteous\\O and\\O obviously\\O chinese\\O .\\O", "opinion_tags": "the\\O staff\\O was\\O very\\O nice\\B and\\O courteous\\B and\\O obviously\\O chinese\\B .\\O", "sentiment": "positive"}]}, {"id": "en_PagodaRestaurant_479983203:9", "sentence": "so about the prawns , they were fresh and had a slight crispiness about the batter ... soooo good ... the walnuts were cut in smaller pieces and very crunchy and tasty .", "postag": ["RB", "IN", "DT", "NNS", ",", "PRP", "VBD", "JJ", "CC", "VBD", "DT", "JJ", "NN", "IN", "DT", "NN", ",", "RB", "JJ", ",", "DT", "NNS", "VBD", "VBN", "IN", "JJR", "NNS", "CC", "RB", "JJ", "CC", "JJ", "."], "head": [8, 4, 4, 1, 8, 8, 8, 0, 10, 8, 13, 13, 10, 16, 16, 13, 8, 19, 8, 19, 22, 24, 24, 8, 27, 27, 24, 30, 30, 24, 32, 30, 8], "deprel": ["advmod", "case", "det", "obl", "punct", "nsubj", "cop", "root", "cc", "conj", "det", "amod", "obj", "case", "det", "nmod", "punct", "advmod", "parataxis", "punct", "det", "nsubj:pass", "aux:pass", "parataxis", "case", "amod", "obl", "cc", "advmod", "conj", "cc", "conj", "punct"], "triples": [{"uid": "en_PagodaRestaurant_479983203:9-0", "target_tags": "so\\O about\\O the\\O prawns\\B ,\\O they\\O were\\O fresh\\O and\\O had\\O a\\O slight\\O crispiness\\O about\\O the\\O batter\\O ...\\O soooo\\O good\\O ...\\O the\\O walnuts\\O were\\O cut\\O in\\O smaller\\O pieces\\O and\\O very\\O crunchy\\O and\\O tasty\\O .\\O", "opinion_tags": "so\\O about\\O the\\O prawns\\O ,\\O they\\O were\\O fresh\\B and\\O had\\O a\\O slight\\O crispiness\\O about\\O the\\O batter\\O ...\\O soooo\\O good\\O ...\\O the\\O walnuts\\O were\\O cut\\O in\\O smaller\\O pieces\\O and\\O very\\O crunchy\\O and\\O tasty\\O .\\O", "sentiment": "positive"}, {"uid": "en_PagodaRestaurant_479983203:9-1", "target_tags": "so\\O about\\O the\\O prawns\\O ,\\O they\\O were\\O fresh\\O and\\O had\\O a\\O slight\\O crispiness\\O about\\O the\\O batter\\B ...\\O soooo\\O good\\O ...\\O the\\O walnuts\\O were\\O cut\\O in\\O smaller\\O pieces\\O and\\O very\\O crunchy\\O and\\O tasty\\O .\\O", "opinion_tags": "so\\O about\\O the\\O prawns\\O ,\\O they\\O were\\O fresh\\O and\\O had\\O a\\O slight\\O crispiness\\B about\\O the\\O batter\\O ...\\O soooo\\O good\\O ...\\O the\\O walnuts\\O were\\O cut\\O in\\O smaller\\O pieces\\O and\\O very\\O crunchy\\O and\\O tasty\\O .\\O", "sentiment": "positive"}, {"uid": "en_PagodaRestaurant_479983203:9-2", "target_tags": "so\\O about\\O the\\O prawns\\O ,\\O they\\O were\\O fresh\\O and\\O had\\O a\\O slight\\O crispiness\\O about\\O the\\O batter\\O ...\\O soooo\\O good\\O ...\\O the\\O walnuts\\B were\\O cut\\O in\\O smaller\\O pieces\\O and\\O very\\O crunchy\\O and\\O tasty\\O .\\O", "opinion_tags": "so\\O about\\O the\\O prawns\\O ,\\O they\\O were\\O fresh\\O and\\O had\\O a\\O slight\\O crispiness\\O about\\O the\\O batter\\O ...\\O soooo\\O good\\O ...\\O the\\O walnuts\\O were\\O cut\\O in\\O smaller\\O pieces\\O and\\O very\\O crunchy\\B and\\O tasty\\B .\\O", "sentiment": "positive"}]}, {"id": "en_PagodaRestaurant_479983203:11", "sentence": "best honey walnyt prawns that we have every tasted .", "postag": ["JJS", "NN", "NN", "NNS", "WDT", "PRP", "VBP", "DT", "VBN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 9, 7, 4], "deprel": ["amod", "compound", "compound", "root", "obj", "nsubj", "acl:relcl", "det", "xcomp", "punct"], "triples": [{"uid": "en_PagodaRestaurant_479983203:11-0", "target_tags": "best\\O honey\\B walnyt\\I prawns\\I that\\O we\\O have\\O every\\O tasted\\O .\\O", "opinion_tags": "best\\B honey\\O walnyt\\O prawns\\O that\\O we\\O have\\O every\\O tasted\\O .\\O", "sentiment": "positive"}]}, {"id": "en_PagodaRestaurant_479983203:13", "sentence": "the brocollis were so fresh and tasty .", "postag": ["DT", "NNS", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"], "triples": [{"uid": "en_PagodaRestaurant_479983203:13-0", "target_tags": "the\\O brocollis\\B were\\O so\\O fresh\\O and\\O tasty\\O .\\O", "opinion_tags": "the\\O brocollis\\O were\\O so\\O fresh\\B and\\O tasty\\B .\\O", "sentiment": "positive"}]}, {"id": "en_PagodaRestaurant_479983203:14", "sentence": "i would normally not finish the brocolli when i order these kinds of food but for the first time , every piece was as eventful as the first one ... the scallops and prawns was so fresh and nicely cooked .", "postag": ["PRP", "MD", "RB", "RB", "VB", "DT", "NN", "WRB", "PRP", "VBP", "DT", "NNS", "IN", "NN", "CC", "IN", "DT", "JJ", "NN", ",", "DT", "NN", "VBD", "RB", "JJ", "IN", "DT", "JJ", "NN", ",", "DT", "NNS", "CC", "NNS", "VBD", "RB", "JJ", "CC", "RB", "VBN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 10, 10, 5, 12, 10, 14, 12, 25, 19, 19, 19, 25, 25, 22, 25, 25, 25, 5, 29, 29, 29, 25, 5, 32, 37, 34, 32, 37, 37, 5, 40, 40, 37, 5], "deprel": ["nsubj", "aux", "advmod", "advmod", "root", "det", "obj", "mark", "nsubj", "advcl", "det", "obj", "case", "nmod", "cc", "case", "det", "amod", "obl", "punct", "det", "nsubj", "cop", "advmod", "conj", "case", "det", "amod", "obl", "punct", "det", "nsubj", "cc", "conj", "cop", "advmod", "parataxis", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "en_PagodaRestaurant_479983203:14-0", "target_tags": "i\\O would\\O normally\\O not\\O finish\\O the\\O brocolli\\O when\\O i\\O order\\O these\\O kinds\\O of\\O food\\O but\\O for\\O the\\O first\\O time\\O ,\\O every\\O piece\\O was\\O as\\O eventful\\O as\\O the\\O first\\O one\\O ...\\O the\\O scallops\\B and\\O prawns\\O was\\O so\\O fresh\\O and\\O nicely\\O cooked\\O .\\O", "opinion_tags": "i\\O would\\O normally\\O not\\O finish\\O the\\O brocolli\\O when\\O i\\O order\\O these\\O kinds\\O of\\O food\\O but\\O for\\O the\\O first\\O time\\O ,\\O every\\O piece\\O was\\O as\\O eventful\\O as\\O the\\O first\\O one\\O ...\\O the\\O scallops\\O and\\O prawns\\O was\\O so\\O fresh\\B and\\O nicely\\B cooked\\I .\\O", "sentiment": "positive"}, {"uid": "en_PagodaRestaurant_479983203:14-1", "target_tags": "i\\O would\\O normally\\O not\\O finish\\O the\\O brocolli\\O when\\O i\\O order\\O these\\O kinds\\O of\\O food\\O but\\O for\\O the\\O first\\O time\\O ,\\O every\\O piece\\O was\\O as\\O eventful\\O as\\O the\\O first\\O one\\O ...\\O the\\O scallops\\O and\\O prawns\\B was\\O so\\O fresh\\O and\\O nicely\\O cooked\\O .\\O", "opinion_tags": "i\\O would\\O normally\\O not\\O finish\\O the\\O brocolli\\O when\\O i\\O order\\O these\\O kinds\\O of\\O food\\O but\\O for\\O the\\O first\\O time\\O ,\\O every\\O piece\\O was\\O as\\O eventful\\O as\\O the\\O first\\O one\\O ...\\O the\\O scallops\\O and\\O prawns\\O was\\O so\\O fresh\\B and\\O nicely\\B cooked\\I .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478219459:0", "sentence": "Chintzy portions", "postag": ["JJ", "NNS"], "head": [2, 0], "deprel": ["amod", "root"], "triples": [{"uid": "en_BlueRibbonSushi_478219459:0-0", "target_tags": "Chintzy\\O portions\\B", "opinion_tags": "Chintzy\\B portions\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478219459:1", "sentence": "– The sushi here is perfectly good , but for $ 5 a piece , either the slices of fish should be larger , or there should be no pretense that this is a moderately priced restaurant ( even for NYC ) .", "postag": ["NFP", "DT", "NN", "RB", "VBZ", "RB", "JJ", ",", "CC", "IN", "$", "CD", "DT", "NN", ",", "CC", "DT", "NNS", "IN", "NN", "MD", "VB", "JJR", ",", "CC", "EX", "MD", "VB", "DT", "NN", "IN", "DT", "VBZ", "DT", "RB", "VBN", "NN", "-LRB-", "RB", "IN", "NNP", "-RRB-", "."], "head": [7, 3, 7, 3, 7, 7, 0, 23, 23, 11, 23, 11, 14, 11, 23, 18, 18, 23, 20, 18, 23, 23, 7, 28, 28, 28, 28, 7, 30, 28, 37, 37, 37, 37, 36, 37, 30, 41, 41, 41, 37, 41, 7], "deprel": ["punct", "det", "nsubj", "advmod", "cop", "advmod", "root", "punct", "cc", "case", "obl", "nummod", "det", "conj", "punct", "det:predet", "det", "nsubj", "case", "nmod", "aux", "cop", "conj", "punct", "cc", "expl", "aux", "conj", "det", "nsubj", "mark", "nsubj", "cop", "det", "advmod", "amod", "acl", "punct", "advmod", "case", "nmod", "punct", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478219459:1-0", "target_tags": "–\\O The\\O sushi\\B here\\O is\\O perfectly\\O good\\O ,\\O but\\O for\\O $\\O 5\\O a\\O piece\\O ,\\O either\\O the\\O slices\\O of\\O fish\\O should\\O be\\O larger\\O ,\\O or\\O there\\O should\\O be\\O no\\O pretense\\O that\\O this\\O is\\O a\\O moderately\\O priced\\O restaurant\\O (\\O even\\O for\\O NYC\\O )\\O .\\O", "opinion_tags": "–\\O The\\O sushi\\O here\\O is\\O perfectly\\O good\\B ,\\O but\\O for\\O $\\O 5\\O a\\O piece\\O ,\\O either\\O the\\O slices\\O of\\O fish\\O should\\O be\\O larger\\O ,\\O or\\O there\\O should\\O be\\O no\\O pretense\\O that\\O this\\O is\\O a\\O moderately\\O priced\\O restaurant\\O (\\O even\\O for\\O NYC\\O )\\O .\\O", "sentiment": "negative"}, {"uid": "en_BlueRibbonSushi_478219459:1-1", "target_tags": "–\\O The\\O sushi\\O here\\O is\\O perfectly\\O good\\O ,\\O but\\O for\\O $\\O 5\\O a\\O piece\\O ,\\O either\\O the\\O slices\\O of\\O fish\\O should\\O be\\O larger\\O ,\\O or\\O there\\O should\\O be\\O no\\O pretense\\O that\\O this\\O is\\O a\\O moderately\\O priced\\O restaurant\\B (\\O even\\O for\\O NYC\\O )\\O .\\O", "opinion_tags": "–\\O The\\O sushi\\O here\\O is\\O perfectly\\O good\\O ,\\O but\\O for\\O $\\O 5\\O a\\O piece\\O ,\\O either\\O the\\O slices\\O of\\O fish\\O should\\O be\\O larger\\O ,\\O or\\O there\\O should\\O be\\O no\\O pretense\\O that\\O this\\O is\\O a\\O moderately\\B priced\\I restaurant\\O (\\O even\\O for\\O NYC\\O )\\O .\\O", "sentiment": "negative"}]}, {"id": "en_ParkChaletGardenRestaurant_477778211:0", "sentence": "Terrible service , food ok , pricey", "postag": ["JJ", "NN", ",", "NN", "JJ", ",", "JJ"], "head": [2, 0, 5, 2, 2, 7, 2], "deprel": ["amod", "root", "punct", "conj", "conj", "punct", "parataxis"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778211:0-0", "target_tags": "Terrible\\O service\\B ,\\O food\\O ok\\O ,\\O pricey\\O", "opinion_tags": "Terrible\\B service\\O ,\\O food\\O ok\\O ,\\O pricey\\O", "sentiment": "negative"}, {"uid": "en_ParkChaletGardenRestaurant_477778211:0-1", "target_tags": "Terrible\\O service\\O ,\\O food\\B ok\\O ,\\O pricey\\O", "opinion_tags": "Terrible\\O service\\O ,\\O food\\O ok\\B ,\\O pricey\\O", "sentiment": "neutral"}]}, {"id": "en_ParkChaletGardenRestaurant_477778211:16", "sentence": "Food wise , its ok but a bit pricey for what you get considering the restaurant is n't a fancy place .", "postag": ["NN", "JJ", ",", "PRP$", "JJ", "CC", "DT", "NN", "JJ", "IN", "WP", "PRP", "VBP", "VBG", "DT", "NN", "VBZ", "RB", "DT", "JJ", "NN", "."], "head": [2, 0, 2, 5, 8, 9, 8, 9, 2, 11, 9, 13, 11, 21, 16, 21, 21, 21, 21, 21, 2, 2], "deprel": ["nsubj", "root", "punct", "nmod:poss", "amod", "cc", "det", "obl:npmod", "conj", "case", "obl", "nsubj", "acl:relcl", "mark", "det", "nsubj", "cop", "advmod", "det", "amod", "conj", "punct"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778211:16-0", "target_tags": "Food\\B wise\\O ,\\O its\\O ok\\O but\\O a\\O bit\\O pricey\\O for\\O what\\O you\\O get\\O considering\\O the\\O restaurant\\O is\\O n't\\O a\\O fancy\\O place\\O .\\O", "opinion_tags": "Food\\O wise\\O ,\\O its\\O ok\\B but\\O a\\O bit\\O pricey\\B for\\O what\\O you\\O get\\O considering\\O the\\O restaurant\\O is\\O n't\\O a\\O fancy\\O place\\O .\\O", "sentiment": "neutral"}, {"uid": "en_ParkChaletGardenRestaurant_477778211:16-1", "target_tags": "Food\\O wise\\O ,\\O its\\O ok\\O but\\O a\\O bit\\O pricey\\O for\\O what\\O you\\O get\\O considering\\O the\\O restaurant\\B is\\O n't\\O a\\O fancy\\O place\\O .\\O", "opinion_tags": "Food\\O wise\\O ,\\O its\\O ok\\O but\\O a\\O bit\\O pricey\\O for\\O what\\O you\\O get\\O considering\\O the\\O restaurant\\O is\\B n't\\I a\\I fancy\\I place\\O .\\O", "sentiment": "neutral"}]}, {"id": "en_ParkChaletGardenRestaurant_477778211:21", "sentence": "Another plus is the open feel of the restaurant with glass walls on all sides .", "postag": ["DT", "CC", "VBZ", "DT", "JJ", "NN", "IN", "DT", "NN", "IN", "NN", "NNS", "IN", "DT", "NNS", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 12, 12, 9, 15, 15, 12, 6], "deprel": ["nsubj", "cc", "cop", "det", "amod", "root", "case", "det", "nmod", "case", "compound", "nmod", "case", "det", "nmod", "punct"], "triples": [{"uid": "en_ParkChaletGardenRestaurant_477778211:21-0", "target_tags": "Another\\O plus\\O is\\O the\\O open\\O feel\\B of\\O the\\O restaurant\\O with\\O glass\\O walls\\O on\\O all\\O sides\\O .\\O", "opinion_tags": "Another\\O plus\\B is\\O the\\O open\\B feel\\O of\\O the\\O restaurant\\O with\\O glass\\O walls\\O on\\O all\\O sides\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218524:3", "sentence": "Amazing Spanish Mackeral special appetizer and perfect box sushi ( that eel with avodcao -- um um um ) .", "postag": ["JJ", "JJ", "NN", "JJ", "NN", "CC", "JJ", "NN", "NN", "-LRB-", "DT", "NN", "IN", "NN", ",", "UH", "UH", "UH", "-RRB-", "."], "head": [5, 5, 5, 5, 0, 9, 9, 9, 5, 12, 12, 5, 14, 12, 12, 12, 12, 12, 12, 5], "deprel": ["amod", "amod", "compound", "amod", "root", "cc", "amod", "compound", "conj", "punct", "det", "parataxis", "case", "nmod", "punct", "discourse", "discourse", "discourse", "punct", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218524:3-0", "target_tags": "Amazing\\O Spanish\\B Mackeral\\I special\\I appetizer\\I and\\O perfect\\O box\\O sushi\\O (\\O that\\O eel\\O with\\O avodcao\\O --\\O um\\O um\\O um\\O )\\O .\\O", "opinion_tags": "Amazing\\B Spanish\\O Mackeral\\O special\\O appetizer\\O and\\O perfect\\O box\\O sushi\\O (\\O that\\O eel\\O with\\O avodcao\\O --\\O um\\O um\\O um\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218524:3-1", "target_tags": "Amazing\\O Spanish\\O Mackeral\\O special\\O appetizer\\O and\\O perfect\\O box\\B sushi\\I (\\O that\\O eel\\O with\\O avodcao\\O --\\O um\\O um\\O um\\O )\\O .\\O", "opinion_tags": "Amazing\\O Spanish\\O Mackeral\\O special\\O appetizer\\O and\\O perfect\\B box\\O sushi\\O (\\O that\\O eel\\O with\\O avodcao\\O --\\O um\\O um\\O um\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218524:4", "sentence": "As usual the omikase did n't disappoint in freshness , although it scored low on creativity and selection .", "postag": ["IN", "JJ", "DT", "NN", "VBD", "RB", "VB", "IN", "NN", ",", "IN", "PRP", "VBD", "RB", "IN", "NN", "CC", "NN", "."], "head": [2, 7, 4, 7, 7, 7, 0, 9, 7, 7, 13, 13, 7, 13, 16, 13, 18, 16, 7], "deprel": ["case", "obl", "det", "nsubj", "aux", "advmod", "root", "case", "obl", "punct", "mark", "nsubj", "advcl", "advmod", "case", "obl", "cc", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218524:4-0", "target_tags": "As\\O usual\\O the\\O omikase\\B did\\O n't\\O disappoint\\O in\\O freshness\\O ,\\O although\\O it\\O scored\\O low\\O on\\O creativity\\O and\\O selection\\O .\\O", "opinion_tags": "As\\O usual\\O the\\O omikase\\O did\\B n't\\I disappoint\\I in\\O freshness\\O ,\\O although\\O it\\O scored\\B low\\I on\\I creativity\\I and\\I selection\\I .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478218524:5", "sentence": "Their specialty rolls are impressive , though I ca n't remember what we had .", "postag": ["PRP$", "NN", "NNS", "VBP", "JJ", ",", "IN", "PRP", "MD", "RB", "VB", "WP", "PRP", "VBD", "."], "head": [3, 3, 5, 5, 0, 5, 11, 11, 11, 11, 5, 14, 14, 11, 5], "deprel": ["nmod:poss", "compound", "nsubj", "cop", "root", "punct", "mark", "nsubj", "aux", "advmod", "advcl", "obj", "nsubj", "ccomp", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218524:5-0", "target_tags": "Their\\O specialty\\B rolls\\I are\\O impressive\\O ,\\O though\\O I\\O ca\\O n't\\O remember\\O what\\O we\\O had\\O .\\O", "opinion_tags": "Their\\O specialty\\O rolls\\O are\\O impressive\\B ,\\O though\\O I\\O ca\\O n't\\O remember\\O what\\O we\\O had\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218524:6", "sentence": "Great selection of sakes .", "postag": ["JJ", "NN", "IN", "NNS", "."], "head": [2, 0, 4, 2, 2], "deprel": ["amod", "root", "case", "nmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218524:6-0", "target_tags": "Great\\O selection\\B of\\I sakes\\I .\\O", "opinion_tags": "Great\\B selection\\O of\\O sakes\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218524:8", "sentence": "It is n't the cheapest sushi but has been worth it every time .", "postag": ["PRP", "VBZ", "RB", "DT", "JJS", "NN", "CC", "VBZ", "VBN", "JJ", "PRP", "DT", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 10, 10, 10, 6, 10, 13, 10, 6], "deprel": ["nsubj", "cop", "advmod", "det", "amod", "root", "cc", "aux", "cop", "conj", "obj", "det", "obl:tmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218524:8-0", "target_tags": "It\\O is\\O n't\\O the\\O cheapest\\O sushi\\B but\\O has\\O been\\O worth\\O it\\O every\\O time\\O .\\O", "opinion_tags": "It\\O is\\B n't\\I the\\I cheapest\\I sushi\\O but\\O has\\O been\\O worth\\B it\\O every\\O time\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SchoonerOrLater_477965850:0", "sentence": "Very poor customer service .", "postag": ["RB", "JJ", "NN", "NN", "."], "head": [2, 4, 4, 0, 4], "deprel": ["advmod", "amod", "compound", "root", "punct"], "triples": [{"uid": "en_SchoonerOrLater_477965850:0-0", "target_tags": "Very\\O poor\\O customer\\B service\\I .\\O", "opinion_tags": "Very\\O poor\\B customer\\O service\\O .\\O", "sentiment": "negative"}]}, {"id": "en_SchoonerOrLater_477965850:1", "sentence": "– Schooner or Later 's charming location along the marina in Long Beach and average food does not , unfortunately , compensate for its very poor customer service .", "postag": ["NFP", "NNP", "CC", "NNP", "POS", "JJ", "NN", "IN", "DT", "NN", "IN", "NNP", "NNP", "CC", "JJ", "NN", "VBZ", "RB", ",", "RB", ",", "VB", "IN", "PRP$", "RB", "JJ", "NN", "NN", "."], "head": [22, 7, 4, 2, 4, 7, 22, 10, 10, 7, 13, 13, 10, 16, 16, 13, 22, 22, 22, 22, 22, 0, 28, 28, 26, 28, 28, 22, 22], "deprel": ["punct", "nmod:poss", "cc", "conj", "case", "amod", "nsubj", "case", "det", "nmod", "case", "compound", "nmod", "cc", "amod", "conj", "aux", "advmod", "punct", "advmod", "punct", "root", "case", "nmod:poss", "advmod", "amod", "compound", "obl", "punct"], "triples": [{"uid": "en_SchoonerOrLater_477965850:1-0", "target_tags": "–\\O Schooner\\O or\\O Later\\O 's\\O charming\\O location\\O along\\O the\\O marina\\O in\\O Long\\O Beach\\O and\\O average\\O food\\O does\\O not\\O ,\\O unfortunately\\O ,\\O compensate\\O for\\O its\\O very\\O poor\\O customer\\B service\\I .\\O", "opinion_tags": "–\\O Schooner\\O or\\O Later\\O 's\\O charming\\O location\\O along\\O the\\O marina\\O in\\O Long\\O Beach\\O and\\O average\\O food\\O does\\O not\\O ,\\O unfortunately\\O ,\\O compensate\\O for\\O its\\O very\\O poor\\B customer\\O service\\O .\\O", "sentiment": "negative"}]}, {"id": "en_SchoonerOrLater_477965850:2", "sentence": "While this diner had reasonably good food , the restaurant staff seemed completely indifferent to our presence , and this attitude was reflected in the lack of service .", "postag": ["IN", "DT", "NN", "VBD", "RB", "JJ", "NN", ",", "DT", "NN", "NN", "VBD", "RB", "JJ", "IN", "PRP$", "NN", ",", "CC", "DT", "NN", "VBD", "VBN", "IN", "DT", "NN", "IN", "NN", "."], "head": [4, 3, 4, 12, 6, 7, 4, 12, 11, 11, 12, 0, 14, 12, 17, 17, 14, 23, 23, 21, 23, 23, 12, 26, 26, 23, 28, 26, 12], "deprel": ["mark", "det", "nsubj", "advcl", "advmod", "amod", "obj", "punct", "det", "compound", "nsubj", "root", "advmod", "xcomp", "case", "nmod:poss", "obl", "punct", "cc", "det", "nsubj:pass", "aux:pass", "conj", "case", "det", "obl", "case", "nmod", "punct"], "triples": [{"uid": "en_SchoonerOrLater_477965850:2-0", "target_tags": "While\\O this\\O diner\\O had\\O reasonably\\O good\\O food\\B ,\\O the\\O restaurant\\O staff\\O seemed\\O completely\\O indifferent\\O to\\O our\\O presence\\O ,\\O and\\O this\\O attitude\\O was\\O reflected\\O in\\O the\\O lack\\O of\\O service\\O .\\O", "opinion_tags": "While\\O this\\O diner\\O had\\O reasonably\\O good\\B food\\O ,\\O the\\O restaurant\\O staff\\O seemed\\O completely\\O indifferent\\O to\\O our\\O presence\\O ,\\O and\\O this\\O attitude\\O was\\O reflected\\O in\\O the\\O lack\\O of\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "en_SchoonerOrLater_477965850:2-1", "target_tags": "While\\O this\\O diner\\O had\\O reasonably\\O good\\O food\\O ,\\O the\\O restaurant\\B staff\\I seemed\\O completely\\O indifferent\\O to\\O our\\O presence\\O ,\\O and\\O this\\O attitude\\O was\\O reflected\\O in\\O the\\O lack\\O of\\O service\\O .\\O", "opinion_tags": "While\\O this\\O diner\\O had\\O reasonably\\O good\\O food\\O ,\\O the\\O restaurant\\O staff\\O seemed\\O completely\\O indifferent\\B to\\O our\\O presence\\O ,\\O and\\O this\\O attitude\\O was\\O reflected\\O in\\O the\\O lack\\O of\\O service\\O .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478219447:2", "sentence": "the fish was fresh , though it was cut very thin .", "postag": ["DT", "NN", "VBD", "JJ", ",", "IN", "PRP", "VBD", "VBN", "RB", "JJ", "."], "head": [2, 4, 4, 0, 4, 9, 9, 9, 4, 11, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "mark", "nsubj:pass", "aux:pass", "advcl", "advmod", "xcomp", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478219447:2-0", "target_tags": "the\\O fish\\B was\\O fresh\\O ,\\O though\\O it\\O was\\O cut\\O very\\O thin\\O .\\O", "opinion_tags": "the\\O fish\\O was\\O fresh\\B ,\\O though\\O it\\O was\\O cut\\O very\\O thin\\B .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478219447:3", "sentence": "great service .", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478219447:3-0", "target_tags": "great\\O service\\B .\\O", "opinion_tags": "great\\B service\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478219447:4", "sentence": "good sake selection .", "postag": ["JJ", "NN", "NN", "."], "head": [2, 3, 0, 3], "deprel": ["amod", "compound", "root", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478219447:4-0", "target_tags": "good\\O sake\\B selection\\I .\\O", "opinion_tags": "good\\B sake\\O selection\\O .\\O", "sentiment": "positive"}]}, {"id": "en_Ray'sBoathouse_477775915:1", "sentence": "– Ray 's is THE place to go for high quality seafood dinners .", "postag": ["NFP", "NNP", "POS", "VBZ", "DT", "NN", "TO", "VB", "IN", "JJ", "JJ", "NN", "NNS", "."], "head": [6, 6, 2, 6, 6, 0, 8, 6, 13, 13, 13, 13, 8, 6], "deprel": ["punct", "nsubj", "case", "cop", "det", "root", "mark", "acl", "case", "amod", "amod", "compound", "obl", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775915:1-0", "target_tags": "–\\O Ray\\O 's\\O is\\O THE\\O place\\O to\\O go\\O for\\O high\\O quality\\O seafood\\B dinners\\I .\\O", "opinion_tags": "–\\O Ray\\O 's\\O is\\O THE\\O place\\O to\\O go\\O for\\O high\\B quality\\I seafood\\O dinners\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775915:4", "sentence": "I love Dungeness crabs and at Ray 's you can get them served in about 6 different ways !", "postag": ["PRP", "VBP", "NNP", "NNS", "CC", "IN", "NNP", "POS", "PRP", "MD", "VB", "PRP", "VBN", "IN", "RB", "CD", "JJ", "NNS", "."], "head": [2, 0, 4, 2, 11, 7, 4, 7, 11, 11, 2, 11, 11, 18, 16, 18, 18, 13, 2], "deprel": ["nsubj", "root", "compound", "obj", "cc", "case", "conj", "case", "nsubj", "aux", "conj", "obj", "xcomp", "case", "advmod", "nummod", "amod", "obl", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775915:4-0", "target_tags": "I\\O love\\O Dungeness\\B crabs\\I and\\O at\\O Ray\\O 's\\O you\\O can\\O get\\O them\\O served\\O in\\O about\\O 6\\O different\\O ways\\O !\\O", "opinion_tags": "I\\O love\\B Dungeness\\O crabs\\O and\\O at\\O Ray\\O 's\\O you\\O can\\O get\\O them\\O served\\O in\\O about\\O 6\\O different\\O ways\\O !\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775915:5", "sentence": "We shared the family platter and I especially enjoyed the black cod in sake kasu .", "postag": ["PRP", "VBD", "DT", "NN", "NN", "CC", "PRP", "RB", "VBD", "DT", "JJ", "NN", "IN", "NN", "NN", "."], "head": [2, 0, 5, 5, 2, 9, 9, 9, 2, 12, 12, 9, 15, 15, 9, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "cc", "nsubj", "advmod", "conj", "det", "amod", "obj", "case", "compound", "obl", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775915:5-0", "target_tags": "We\\O shared\\O the\\O family\\O platter\\O and\\O I\\O especially\\O enjoyed\\O the\\O black\\B cod\\I in\\I sake\\I kasu\\I .\\O", "opinion_tags": "We\\O shared\\O the\\O family\\O platter\\O and\\O I\\O especially\\O enjoyed\\B the\\O black\\O cod\\O in\\O sake\\O kasu\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775915:6", "sentence": "I ended the meal with the unusual dessert of a port and chocolate tasting ... yummy !", "postag": ["PRP", "VBD", "DT", "NN", "IN", "DT", "JJ", "NN", "IN", "DT", "NN", "CC", "NN", "NN", ",", "JJ", "."], "head": [2, 0, 4, 2, 8, 8, 8, 2, 14, 11, 8, 14, 14, 11, 2, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "case", "det", "amod", "obl", "case", "det", "nmod", "cc", "compound", "conj", "punct", "parataxis", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775915:6-0", "target_tags": "I\\O ended\\O the\\O meal\\O with\\O the\\O unusual\\O dessert\\B of\\I a\\I port\\I and\\I chocolate\\I tasting\\I ...\\O yummy\\O !\\O", "opinion_tags": "I\\O ended\\O the\\O meal\\O with\\O the\\O unusual\\O dessert\\O of\\O a\\O port\\O and\\O chocolate\\O tasting\\O ...\\O yummy\\B !\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775915:7", "sentence": "And the service was simply spendid - quite a delight .", "postag": ["CC", "DT", "NN", "VBD", "RB", "JJ", ",", "PDT", "DT", "NN", "."], "head": [6, 3, 6, 6, 6, 0, 6, 10, 10, 6, 6], "deprel": ["cc", "det", "nsubj", "cop", "advmod", "root", "punct", "det:predet", "det", "parataxis", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775915:7-0", "target_tags": "And\\O the\\O service\\B was\\O simply\\O spendid\\O -\\O quite\\O a\\O delight\\O .\\O", "opinion_tags": "And\\O the\\O service\\O was\\O simply\\O spendid\\B -\\O quite\\O a\\O delight\\B .\\O", "sentiment": "positive"}]}, {"id": "en_SnoozeanAMEatery_479991844:1", "sentence": "– Great drinks , corn beef hash , coffee , B Fast burritos , Gluten Free menu .", "postag": ["NFP", "JJ", "NNS", ",", "NN", "NN", "NN", ",", "NN", ",", "NN", "JJ", "NNS", ",", "NN", "JJ", "NN", "."], "head": [3, 3, 0, 3, 6, 7, 3, 3, 3, 3, 13, 13, 3, 3, 17, 17, 3, 3], "deprel": ["punct", "amod", "root", "punct", "compound", "compound", "list", "punct", "list", "punct", "compound", "amod", "list", "punct", "compound", "amod", "list", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_479991844:1-0", "target_tags": "–\\O Great\\O drinks\\B ,\\O corn\\O beef\\O hash\\O ,\\O coffee\\O ,\\O B\\O Fast\\O burritos\\O ,\\O Gluten\\O Free\\O menu\\O .\\O", "opinion_tags": "–\\O Great\\B drinks\\O ,\\O corn\\O beef\\O hash\\O ,\\O coffee\\O ,\\O B\\O Fast\\O burritos\\O ,\\O Gluten\\O Free\\O menu\\O .\\O", "sentiment": "positive"}, {"uid": "en_SnoozeanAMEatery_479991844:1-1", "target_tags": "–\\O Great\\O drinks\\O ,\\O corn\\B beef\\I hash\\I ,\\O coffee\\O ,\\O B\\O Fast\\O burritos\\O ,\\O Gluten\\O Free\\O menu\\O .\\O", "opinion_tags": "–\\O Great\\B drinks\\O ,\\O corn\\O beef\\O hash\\O ,\\O coffee\\O ,\\O B\\O Fast\\O burritos\\O ,\\O Gluten\\O Free\\O menu\\O .\\O", "sentiment": "positive"}, {"uid": "en_SnoozeanAMEatery_479991844:1-2", "target_tags": "–\\O Great\\O drinks\\O ,\\O corn\\O beef\\O hash\\O ,\\O coffee\\B ,\\O B\\O Fast\\O burritos\\O ,\\O Gluten\\O Free\\O menu\\O .\\O", "opinion_tags": "–\\O Great\\B drinks\\O ,\\O corn\\O beef\\O hash\\O ,\\O coffee\\O ,\\O B\\O Fast\\O burritos\\O ,\\O Gluten\\O Free\\O menu\\O .\\O", "sentiment": "positive"}, {"uid": "en_SnoozeanAMEatery_479991844:1-3", "target_tags": "–\\O Great\\O drinks\\O ,\\O corn\\O beef\\O hash\\O ,\\O coffee\\O ,\\O B\\B Fast\\I burritos\\I ,\\O Gluten\\O Free\\O menu\\O .\\O", "opinion_tags": "–\\O Great\\B drinks\\O ,\\O corn\\O beef\\O hash\\O ,\\O coffee\\O ,\\O B\\O Fast\\O burritos\\O ,\\O Gluten\\O Free\\O menu\\O .\\O", "sentiment": "positive"}, {"uid": "en_SnoozeanAMEatery_479991844:1-4", "target_tags": "–\\O Great\\O drinks\\O ,\\O corn\\O beef\\O hash\\O ,\\O coffee\\O ,\\O B\\O Fast\\O burritos\\O ,\\O Gluten\\O Free\\O menu\\B .\\O", "opinion_tags": "–\\O Great\\B drinks\\O ,\\O corn\\O beef\\O hash\\O ,\\O coffee\\O ,\\O B\\O Fast\\O burritos\\O ,\\O Gluten\\O Free\\O menu\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SnoozeanAMEatery_479991844:2", "sentence": "The service is fantastic at this fun place .", "postag": ["DT", "NN", "VBZ", "JJ", "IN", "DT", "JJ", "NN", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_479991844:2-0", "target_tags": "The\\O service\\B is\\O fantastic\\O at\\O this\\O fun\\O place\\O .\\O", "opinion_tags": "The\\O service\\O is\\O fantastic\\B at\\O this\\O fun\\O place\\O .\\O", "sentiment": "positive"}, {"uid": "en_SnoozeanAMEatery_479991844:2-1", "target_tags": "The\\O service\\O is\\O fantastic\\O at\\O this\\O fun\\O place\\B .\\O", "opinion_tags": "The\\O service\\O is\\O fantastic\\O at\\O this\\O fun\\B place\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON><PERSON>'s_478606543:0", "sentence": "Best Neighborhood Standby .", "postag": ["JJS", "NN", "NN", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "compound", "root", "punct"], "triples": [{"uid": "en_<PERSON><PERSON><PERSON>'s_478606543:0-0", "target_tags": "Best\\O Neighborhood\\O Standby\\B .\\O", "opinion_tags": "Best\\B Neighborhood\\O Standby\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON><PERSON>'s_478606543:3", "sentence": "In Grammercy/Union Square/East Village this is my neighbors and my favorite spot .", "postag": ["IN", "NNP", "NNP", "NNP", "DT", "VBZ", "PRP$", "NNS", "CC", "PRP$", "JJ", "NN", "."], "head": [4, 4, 4, 8, 8, 8, 8, 0, 12, 12, 12, 8, 8], "deprel": ["case", "compound", "compound", "obl", "nsubj", "cop", "nmod:poss", "root", "cc", "nmod:poss", "amod", "conj", "punct"], "triples": [{"uid": "en_<PERSON><PERSON><PERSON>'s_478606543:3-0", "target_tags": "In\\O Grammercy/Union\\O Square/East\\O Village\\O this\\O is\\O my\\O neighbors\\O and\\O my\\O favorite\\O spot\\B .\\O", "opinion_tags": "In\\O Grammercy/Union\\O Square/East\\O Village\\O this\\O is\\O my\\O neighbors\\O and\\O my\\O favorite\\B spot\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON><PERSON>'s_478606543:4", "sentence": "The music is great , no night better or worse , the bar tenders are generous with the pouring , and the lighthearted atmosphere will lifts you spirits .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "DT", "NN", "JJR", "CC", "JJR", ",", "DT", "NN", "NNS", "VBP", "JJ", "IN", "DT", "NN", ",", "CC", "DT", "JJ", "NN", "MD", "VBZ", "PRP", "NNS", "."], "head": [2, 4, 4, 0, 8, 7, 8, 4, 10, 8, 4, 14, 14, 16, 16, 4, 19, 19, 16, 26, 26, 24, 24, 26, 26, 4, 26, 26, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "obl:npmod", "appos", "cc", "conj", "punct", "det", "compound", "nsubj", "cop", "parataxis", "case", "det", "obl", "punct", "cc", "det", "amod", "nsubj", "aux", "conj", "i<PERSON><PERSON>", "obj", "punct"], "triples": [{"uid": "en_<PERSON><PERSON><PERSON>'s_478606543:4-0", "target_tags": "The\\O music\\B is\\O great\\O ,\\O no\\O night\\O better\\O or\\O worse\\O ,\\O the\\O bar\\O tenders\\O are\\O generous\\O with\\O the\\O pouring\\O ,\\O and\\O the\\O lighthearted\\O atmosphere\\O will\\O lifts\\O you\\O spirits\\O .\\O", "opinion_tags": "The\\O music\\O is\\O great\\B ,\\O no\\O night\\O better\\O or\\O worse\\O ,\\O the\\O bar\\O tenders\\O are\\O generous\\O with\\O the\\O pouring\\O ,\\O and\\O the\\O lighthearted\\O atmosphere\\O will\\O lifts\\O you\\O spirits\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON><PERSON><PERSON>'s_478606543:4-1", "target_tags": "The\\O music\\O is\\O great\\O ,\\O no\\O night\\O better\\O or\\O worse\\O ,\\O the\\O bar\\B tenders\\I are\\O generous\\O with\\O the\\O pouring\\O ,\\O and\\O the\\O lighthearted\\O atmosphere\\O will\\O lifts\\O you\\O spirits\\O .\\O", "opinion_tags": "The\\O music\\O is\\O great\\O ,\\O no\\O night\\O better\\O or\\O worse\\O ,\\O the\\O bar\\O tenders\\O are\\O generous\\B with\\O the\\O pouring\\O ,\\O and\\O the\\O lighthearted\\O atmosphere\\O will\\O lifts\\O you\\O spirits\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON><PERSON><PERSON>'s_478606543:4-2", "target_tags": "The\\O music\\O is\\O great\\O ,\\O no\\O night\\O better\\O or\\O worse\\O ,\\O the\\O bar\\O tenders\\O are\\O generous\\O with\\O the\\O pouring\\O ,\\O and\\O the\\O lighthearted\\O atmosphere\\B will\\O lifts\\O you\\O spirits\\O .\\O", "opinion_tags": "The\\O music\\O is\\O great\\O ,\\O no\\O night\\O better\\O or\\O worse\\O ,\\O the\\O bar\\O tenders\\O are\\O generous\\O with\\O the\\O pouring\\O ,\\O and\\O the\\O lighthearted\\B atmosphere\\O will\\O lifts\\O you\\O spirits\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON><PERSON>'s_478606543:6", "sentence": "Oh , and the cheese fries are awesome !", "postag": ["UH", ",", "CC", "DT", "NN", "NNS", "VBP", "JJ", "."], "head": [8, 8, 8, 6, 6, 8, 8, 0, 8], "deprel": ["discourse", "punct", "cc", "det", "compound", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_<PERSON><PERSON><PERSON>'s_478606543:6-0", "target_tags": "Oh\\O ,\\O and\\O the\\O cheese\\B fries\\I are\\O awesome\\O !\\O", "opinion_tags": "Oh\\O ,\\O and\\O the\\O cheese\\O fries\\O are\\O awesome\\B !\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538239:0", "sentence": "Good Food , Great Service , Average Prices ( For the Strip )", "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "JJ", "NNS", "-LRB-", "IN", "DT", "NN", "-RRB-"], "head": [2, 0, 5, 5, 2, 8, 8, 2, 12, 12, 12, 2, 12], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "amod", "conj", "punct", "case", "det", "nmod", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538239:0-0", "target_tags": "Good\\O Food\\B ,\\O Great\\O Service\\O ,\\O Average\\O Prices\\O (\\O For\\O the\\O Strip\\O )\\O", "opinion_tags": "Good\\B Food\\O ,\\O Great\\O Service\\O ,\\O Average\\O Prices\\O (\\O For\\O the\\O Strip\\O )\\O", "sentiment": "positive"}, {"uid": "en_StackRestaurant__Bar_478538239:0-1", "target_tags": "Good\\O Food\\O ,\\O Great\\O Service\\B ,\\O Average\\O Prices\\O (\\O For\\O the\\O Strip\\O )\\O", "opinion_tags": "Good\\O Food\\O ,\\O Great\\B Service\\O ,\\O Average\\O Prices\\O (\\O For\\O the\\O Strip\\O )\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538239:2", "sentence": "When I walked in , I was taken aback by their incredible wood decor .", "postag": ["WRB", "PRP", "VBD", "RB", ",", "PRP", "VBD", "VBN", "JJ", "IN", "PRP$", "JJ", "NN", "NN", "."], "head": [3, 3, 8, 3, 8, 8, 8, 0, 8, 14, 14, 14, 14, 9, 8], "deprel": ["mark", "nsubj", "advcl", "advmod", "punct", "nsubj:pass", "aux:pass", "root", "xcomp", "case", "nmod:poss", "amod", "compound", "obl", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538239:2-0", "target_tags": "When\\O I\\O walked\\O in\\O ,\\O I\\O was\\O taken\\O aback\\O by\\O their\\O incredible\\O wood\\B decor\\I .\\O", "opinion_tags": "When\\O I\\O walked\\O in\\O ,\\O I\\O was\\O taken\\O aback\\O by\\O their\\O incredible\\B wood\\O decor\\O .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538239:3", "sentence": "The music playing was very hip , 20-30 something pop music , but the subwoofer to the sound system was located under my seat , which became annoying midway through dinner .", "postag": ["DT", "NN", "NN", "VBD", "RB", "JJ", ",", "CD", "NN", "NN", "NN", ",", "CC", "DT", "NN", "IN", "DT", "NN", "NN", "VBD", "VBN", "IN", "PRP$", "NN", ",", "WDT", "VBD", "JJ", "NN", "IN", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 9, 9, 6, 11, 9, 21, 21, 15, 21, 19, 19, 19, 15, 21, 6, 24, 24, 21, 27, 27, 24, 29, 27, 31, 29, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "punct", "nummod", "parataxis", "compound", "nmod", "punct", "cc", "det", "nsubj:pass", "case", "det", "compound", "nmod", "aux:pass", "conj", "case", "nmod:poss", "obl", "punct", "nsubj", "acl:relcl", "amod", "xcomp", "case", "nmod", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538239:3-0", "target_tags": "The\\O music\\O playing\\O was\\O very\\O hip\\O ,\\O 20-30\\O something\\O pop\\O music\\O ,\\O but\\O the\\O subwoofer\\B to\\I the\\I sound\\I system\\I was\\O located\\O under\\O my\\O seat\\O ,\\O which\\O became\\O annoying\\O midway\\O through\\O dinner\\O .\\O", "opinion_tags": "The\\O music\\O playing\\O was\\O very\\O hip\\O ,\\O 20-30\\O something\\O pop\\O music\\O ,\\O but\\O the\\O subwoofer\\O to\\O the\\O sound\\O system\\O was\\O located\\O under\\O my\\O seat\\O ,\\O which\\O became\\O annoying\\B midway\\O through\\O dinner\\O .\\O", "sentiment": "negative"}]}, {"id": "en_StackRestaurant__Bar_478538239:5", "sentence": "I got the shellfish and shrimp appetizer and it was alright .", "postag": ["PRP", "VBD", "DT", "NN", "CC", "NN", "NN", "CC", "PRP", "VBD", "JJ", "."], "head": [2, 0, 7, 2, 7, 7, 4, 11, 11, 11, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "cc", "compound", "conj", "cc", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538239:5-0", "target_tags": "I\\O got\\O the\\O shellfish\\B and\\I shrimp\\I appetizer\\I and\\O it\\O was\\O alright\\O .\\O", "opinion_tags": "I\\O got\\O the\\O shellfish\\O and\\O shrimp\\O appetizer\\O and\\O it\\O was\\O alright\\B .\\O", "sentiment": "neutral"}]}, {"id": "en_StackRestaurant__Bar_478538239:6", "sentence": "It was n't the freshest seafood ever , but the taste and presentation was OK .", "postag": ["PRP", "VBD", "RB", "DT", "JJS", "NN", "RB", ",", "CC", "DT", "NN", "CC", "NN", "VBD", "JJ", "."], "head": [6, 6, 6, 6, 6, 0, 6, 15, 15, 11, 15, 13, 11, 15, 6, 6], "deprel": ["nsubj", "cop", "advmod", "det", "amod", "root", "advmod", "punct", "cc", "det", "nsubj", "cc", "conj", "cop", "conj", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538239:6-0", "target_tags": "It\\O was\\O n't\\O the\\O freshest\\O seafood\\B ever\\O ,\\O but\\O the\\O taste\\O and\\O presentation\\O was\\O OK\\O .\\O", "opinion_tags": "It\\O was\\B n't\\I the\\I freshest\\I seafood\\O ever\\O ,\\O but\\O the\\O taste\\O and\\O presentation\\O was\\O OK\\B .\\O", "sentiment": "neutral"}]}, {"id": "en_StackRestaurant__Bar_478538239:8", "sentence": "I picked the asparagus , which turned out to be incredible and perfectly prepared .", "postag": ["PRP", "VBD", "DT", "NN", ",", "WDT", "VBD", "RP", "TO", "VB", "JJ", "CC", "RB", "JJ", "."], "head": [2, 0, 4, 2, 7, 7, 4, 7, 11, 11, 7, 14, 14, 11, 2], "deprel": ["nsubj", "root", "det", "obj", "punct", "nsubj", "acl:relcl", "compound:prt", "mark", "cop", "xcomp", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538239:8-0", "target_tags": "I\\O picked\\O the\\O asparagus\\B ,\\O which\\O turned\\O out\\O to\\O be\\O incredible\\O and\\O perfectly\\O prepared\\O .\\O", "opinion_tags": "I\\O picked\\O the\\O asparagus\\O ,\\O which\\O turned\\O out\\O to\\O be\\O incredible\\B and\\O perfectly\\B prepared\\I .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538239:11", "sentence": "The steak was done to my exact liking ( medium rare ) and was nice and juicy .", "postag": ["DT", "NN", "VBD", "VBN", "IN", "PRP$", "JJ", "NN", "-LRB-", "JJ", "JJ", "-RRB-", "CC", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 11, 11, 8, 11, 15, 15, 4, 17, 15, 4], "deprel": ["det", "nsubj:pass", "aux:pass", "root", "case", "nmod:poss", "amod", "obl", "punct", "advmod", "appos", "punct", "cc", "cop", "conj", "cc", "conj", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538239:11-0", "target_tags": "The\\O steak\\B was\\O done\\O to\\O my\\O exact\\O liking\\O (\\O medium\\O rare\\O )\\O and\\O was\\O nice\\O and\\O juicy\\O .\\O", "opinion_tags": "The\\O steak\\O was\\O done\\O to\\O my\\O exact\\O liking\\O (\\O medium\\O rare\\O )\\O and\\O was\\O nice\\B and\\O juicy\\B .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538239:12", "sentence": "It ? s served with either a peppercorn sauce or red wine reduction , though both were indistinguishable in taste .", "postag": ["PRP", ".", "VBZ", "VBN", "IN", "CC", "DT", "NN", "NN", "CC", "JJ", "NN", "NN", ",", "IN", "DT", "VBD", "JJ", "IN", "NN", "."], "head": [4, 4, 4, 0, 9, 9, 9, 9, 4, 13, 13, 13, 9, 4, 18, 18, 18, 4, 20, 18, 4], "deprel": ["nsubj:pass", "punct", "aux:pass", "root", "case", "cc:preconj", "det", "compound", "obl", "cc", "amod", "compound", "conj", "punct", "mark", "nsubj", "cop", "advcl", "case", "obl", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538239:12-0", "target_tags": "It\\O ?\\O s\\O served\\O with\\O either\\O a\\O peppercorn\\B sauce\\I or\\O red\\O wine\\O reduction\\O ,\\O though\\O both\\O were\\O indistinguishable\\O in\\O taste\\O .\\O", "opinion_tags": "It\\O ?\\O s\\O served\\O with\\O either\\O a\\O peppercorn\\O sauce\\O or\\O red\\O wine\\O reduction\\O ,\\O though\\O both\\O were\\O indistinguishable\\B in\\O taste\\O .\\O", "sentiment": "neutral"}, {"uid": "en_StackRestaurant__Bar_478538239:12-1", "target_tags": "It\\O ?\\O s\\O served\\O with\\O either\\O a\\O peppercorn\\O sauce\\O or\\O red\\B wine\\I reduction\\I ,\\O though\\O both\\O were\\O indistinguishable\\O in\\O taste\\O .\\O", "opinion_tags": "It\\O ?\\O s\\O served\\O with\\O either\\O a\\O peppercorn\\O sauce\\O or\\O red\\O wine\\O reduction\\O ,\\O though\\O both\\O were\\O indistinguishable\\B in\\O taste\\O .\\O", "sentiment": "neutral"}]}, {"id": "en_StackRestaurant__Bar_478538239:17", "sentence": "The desert was the perfect ending to an almost perfect dinner .", "postag": ["DT", "NN", "VBD", "DT", "JJ", "NN", "IN", "DT", "RB", "JJ", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 11, 11, 10, 11, 6, 6], "deprel": ["det", "nsubj", "cop", "det", "amod", "root", "case", "det", "advmod", "amod", "nmod", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538239:17-0", "target_tags": "The\\O desert\\B was\\O the\\O perfect\\O ending\\O to\\O an\\O almost\\O perfect\\O dinner\\O .\\O", "opinion_tags": "The\\O desert\\O was\\O the\\O perfect\\B ending\\O to\\O an\\O almost\\O perfect\\O dinner\\O .\\O", "sentiment": "positive"}, {"uid": "en_StackRestaurant__Bar_478538239:17-1", "target_tags": "The\\O desert\\O was\\O the\\O perfect\\O ending\\O to\\O an\\O almost\\O perfect\\O dinner\\B .\\O", "opinion_tags": "The\\O desert\\O was\\O the\\O perfect\\O ending\\O to\\O an\\O almost\\O perfect\\B dinner\\O .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538239:19", "sentence": "But the servers were extremely attentive and very friendly .", "postag": ["CC", "DT", "NNS", "VBD", "RB", "JJ", "CC", "RB", "JJ", "."], "head": [6, 3, 6, 6, 6, 0, 9, 9, 6, 6], "deprel": ["cc", "det", "nsubj", "cop", "advmod", "root", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538239:19-0", "target_tags": "But\\O the\\O servers\\B were\\O extremely\\O attentive\\O and\\O very\\O friendly\\O .\\O", "opinion_tags": "But\\O the\\O servers\\O were\\O extremely\\O attentive\\B and\\O very\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218337:0", "sentence": "good sake , good food – i honestly do n't know much about japanese food at all .", "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "PRP", "RB", "VBP", "RB", "VB", "RB", "IN", "JJ", "NN", "IN", "DT", "."], "head": [2, 0, 2, 5, 2, 2, 11, 11, 11, 11, 2, 11, 15, 15, 12, 17, 11, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "nsubj", "advmod", "aux", "advmod", "parataxis", "obj", "case", "amod", "obl", "case", "obl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218337:0-0", "target_tags": "good\\O sake\\B ,\\O good\\O food\\O –\\O i\\O honestly\\O do\\O n't\\O know\\O much\\O about\\O japanese\\O food\\O at\\O all\\O .\\O", "opinion_tags": "good\\B sake\\O ,\\O good\\O food\\O –\\O i\\O honestly\\O do\\O n't\\O know\\O much\\O about\\O japanese\\O food\\O at\\O all\\O .\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218337:0-1", "target_tags": "good\\O sake\\O ,\\O good\\O food\\B –\\O i\\O honestly\\O do\\O n't\\O know\\O much\\O about\\O japanese\\O food\\O at\\O all\\O .\\O", "opinion_tags": "good\\O sake\\O ,\\O good\\B food\\O –\\O i\\O honestly\\O do\\O n't\\O know\\O much\\O about\\O japanese\\O food\\O at\\O all\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218337:2", "sentence": "Server made several sake suggestions which were very good .", "postag": ["NN", "VBD", "JJ", "NN", "NNS", "WDT", "VBD", "RB", "JJ", "."], "head": [2, 0, 5, 5, 2, 9, 9, 9, 5, 2], "deprel": ["nsubj", "root", "amod", "compound", "obj", "nsubj", "cop", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218337:2-0", "target_tags": "Server\\O made\\O several\\O sake\\B suggestions\\O which\\O were\\O very\\O good\\O .\\O", "opinion_tags": "Server\\O made\\O several\\O sake\\O suggestions\\O which\\O were\\O very\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218337:3", "sentence": "had many dishes but the BEST was the lobster 3 ways .", "postag": ["VBD", "JJ", "NNS", "CC", "DT", "JJS", "VBD", "DT", "NN", "CD", "NNS", "."], "head": [0, 3, 1, 9, 6, 9, 9, 9, 1, 11, 9, 1], "deprel": ["root", "amod", "obj", "cc", "det", "nsubj", "cop", "det", "conj", "nummod", "nmod:npmod", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218337:3-0", "target_tags": "had\\O many\\O dishes\\O but\\O the\\O BEST\\O was\\O the\\O lobster\\B 3\\I ways\\I .\\O", "opinion_tags": "had\\O many\\O dishes\\O but\\O the\\O BEST\\B was\\O the\\O lobster\\O 3\\O ways\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SnoozeanAMEatery_480412257:3", "sentence": "The waiter was a bit unfriendly and the feel of the restaurant was crowded .", "postag": ["DT", "NN", "VBD", "DT", "NN", "JJ", "CC", "DT", "NN", "IN", "DT", "NN", "VBD", "JJ", "."], "head": [2, 6, 6, 5, 6, 0, 14, 9, 14, 12, 12, 9, 14, 6, 6], "deprel": ["det", "nsubj", "cop", "det", "obl:npmod", "root", "cc", "det", "nsubj", "case", "det", "nmod", "cop", "conj", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480412257:3-0", "target_tags": "The\\O waiter\\B was\\O a\\O bit\\O unfriendly\\O and\\O the\\O feel\\O of\\O the\\O restaurant\\O was\\O crowded\\O .\\O", "opinion_tags": "The\\O waiter\\O was\\O a\\O bit\\O unfriendly\\B and\\O the\\O feel\\O of\\O the\\O restaurant\\O was\\O crowded\\O .\\O", "sentiment": "negative"}, {"uid": "en_SnoozeanAMEatery_480412257:3-1", "target_tags": "The\\O waiter\\O was\\O a\\O bit\\O unfriendly\\O and\\O the\\O feel\\B of\\O the\\O restaurant\\O was\\O crowded\\O .\\O", "opinion_tags": "The\\O waiter\\O was\\O a\\O bit\\O unfriendly\\O and\\O the\\O feel\\O of\\O the\\O restaurant\\O was\\O crowded\\B .\\O", "sentiment": "negative"}]}, {"id": "en_SnoozeanAMEatery_480412257:5", "sentence": "Most importantly , we were so excited about the food after seeing the very creative menu .", "postag": ["RBS", "RB", ",", "PRP", "VBD", "RB", "JJ", "IN", "DT", "NN", "IN", "VBG", "DT", "RB", "JJ", "NN", "."], "head": [2, 7, 7, 7, 7, 7, 0, 10, 10, 7, 12, 7, 16, 15, 16, 12, 7], "deprel": ["advmod", "advmod", "punct", "nsubj", "cop", "advmod", "root", "case", "det", "obl", "mark", "advcl", "det", "advmod", "amod", "obj", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480412257:5-0", "target_tags": "Most\\O importantly\\O ,\\O we\\O were\\O so\\O excited\\O about\\O the\\O food\\O after\\O seeing\\O the\\O very\\O creative\\O menu\\B .\\O", "opinion_tags": "Most\\O importantly\\O ,\\O we\\O were\\O so\\O excited\\O about\\O the\\O food\\O after\\O seeing\\O the\\O very\\O creative\\B menu\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SnoozeanAMEatery_480412257:6", "sentence": "At best , the food was good and definately overpriced .", "postag": ["RB", "JJS", ",", "DT", "NN", "VBD", "JJ", "CC", "RB", "JJ", "."], "head": [2, 7, 7, 5, 7, 7, 0, 10, 10, 7, 7], "deprel": ["case", "obl", "punct", "det", "nsubj", "cop", "root", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480412257:6-0", "target_tags": "At\\O best\\O ,\\O the\\O food\\B was\\O good\\O and\\O definately\\O overpriced\\O .\\O", "opinion_tags": "At\\O best\\O ,\\O the\\O food\\O was\\O good\\B and\\O definately\\O overpriced\\B .\\O", "sentiment": "negative"}]}, {"id": "en_MiopostoCaffe_478543072:0", "sentence": "My favortie pizza joint in Seattle", "postag": ["PRP$", "JJ", "NN", "NN", "IN", "NNP"], "head": [4, 4, 4, 0, 6, 4], "deprel": ["nmod:poss", "amod", "compound", "root", "case", "nmod"], "triples": [{"uid": "en_MiopostoCaffe_478543072:0-0", "target_tags": "My\\O favortie\\O pizza\\B joint\\I in\\O Seattle\\O", "opinion_tags": "My\\O favortie\\B pizza\\O joint\\O in\\O Seattle\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478543072:3", "sentence": "The pizza is delicious and the salads are fantastic .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NNS", "VBP", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478543072:3-0", "target_tags": "The\\O pizza\\B is\\O delicious\\O and\\O the\\O salads\\O are\\O fantastic\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O delicious\\B and\\O the\\O salads\\O are\\O fantastic\\O .\\O", "sentiment": "positive"}, {"uid": "en_MiopostoCaffe_478543072:3-1", "target_tags": "The\\O pizza\\O is\\O delicious\\O and\\O the\\O salads\\B are\\O fantastic\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O delicious\\O and\\O the\\O salads\\O are\\O fantastic\\B .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478543072:4", "sentence": "I 've always found the wait staff and , if you sit at the bar , the cooks very friendly .", "postag": ["PRP", "VBP", "RB", "VBN", "DT", "NN", "NN", "CC", ",", "IN", "PRP", "VBP", "IN", "DT", "NN", ",", "DT", "NNS", "RB", "JJ", "."], "head": [4, 4, 4, 0, 7, 7, 4, 20, 20, 12, 12, 20, 15, 15, 12, 20, 18, 20, 20, 4, 4], "deprel": ["nsubj", "aux", "advmod", "root", "det", "compound", "obj", "cc", "punct", "mark", "nsubj", "advcl", "case", "det", "obl", "punct", "det", "nsubj", "advmod", "conj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478543072:4-0", "target_tags": "I\\O 've\\O always\\O found\\O the\\O wait\\O staff\\O and\\O ,\\O if\\O you\\O sit\\O at\\O the\\O bar\\O ,\\O the\\O cooks\\B very\\O friendly\\O .\\O", "opinion_tags": "I\\O 've\\O always\\O found\\O the\\O wait\\O staff\\O and\\O ,\\O if\\O you\\O sit\\O at\\O the\\O bar\\O ,\\O the\\O cooks\\O very\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_478543072:6", "sentence": "I also really enjoy the simplicity of the decor and intimate feeling of a small restaurant .", "postag": ["PRP", "RB", "RB", "VBP", "DT", "NN", "IN", "DT", "NN", "CC", "JJ", "NN", "IN", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 9, 9, 6, 12, 12, 9, 16, 16, 16, 12, 4], "deprel": ["nsubj", "advmod", "advmod", "root", "det", "obj", "case", "det", "nmod", "cc", "amod", "conj", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "en_MiopostoCaffe_478543072:6-0", "target_tags": "I\\O also\\O really\\O enjoy\\O the\\O simplicity\\O of\\O the\\O decor\\B and\\O intimate\\O feeling\\O of\\O a\\O small\\O restaurant\\O .\\O", "opinion_tags": "I\\O also\\O really\\O enjoy\\B the\\O simplicity\\B of\\O the\\O decor\\O and\\O intimate\\O feeling\\O of\\O a\\O small\\O restaurant\\O .\\O", "sentiment": "positive"}, {"uid": "en_MiopostoCaffe_478543072:6-1", "target_tags": "I\\O also\\O really\\O enjoy\\O the\\O simplicity\\O of\\O the\\O decor\\O and\\O intimate\\O feeling\\B of\\O a\\O small\\O restaurant\\O .\\O", "opinion_tags": "I\\O also\\O really\\O enjoy\\O the\\O simplicity\\O of\\O the\\O decor\\O and\\O intimate\\B feeling\\O of\\O a\\O small\\O restaurant\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_479702043:1", "sentence": "– My husband and I love eating at Mioposto Café .", "postag": ["NFP", "PRP$", "NN", "CC", "PRP", "VBP", "VBG", "IN", "NNP", "NNP", "."], "head": [6, 3, 6, 5, 3, 0, 6, 10, 10, 7, 6], "deprel": ["punct", "nmod:poss", "nsubj", "cc", "conj", "root", "xcomp", "case", "compound", "obl", "punct"], "triples": [{"uid": "en_MiopostoCaffe_479702043:1-0", "target_tags": "–\\O My\\O husband\\O and\\O I\\O love\\O eating\\O at\\O Mioposto\\B Café\\I .\\O", "opinion_tags": "–\\O My\\O husband\\O and\\O I\\O love\\B eating\\O at\\O Mioposto\\O Café\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_479702043:2", "sentence": "We ’ re can ’ t say enough about their delicious gourmet pizza ’ s !", "postag": ["PRP", "``", "IN", "MD", "''", "NN", "VB", "RB", "IN", "PRP$", "JJ", "NN", "NN", "''", "POS", "."], "head": [7, 7, 4, 7, 4, 7, 0, 7, 13, 13, 13, 13, 8, 13, 13, 7], "deprel": ["nsubj", "punct", "case", "aux", "punct", "nsubj", "root", "advmod", "case", "nmod:poss", "amod", "compound", "obl", "punct", "case", "punct"], "triples": [{"uid": "en_MiopostoCaffe_479702043:2-0", "target_tags": "We\\O ’\\O re\\O can\\O ’\\O t\\O say\\O enough\\O about\\O their\\O delicious\\O gourmet\\O pizza\\B ’\\I s\\I !\\O", "opinion_tags": "We\\O ’\\O re\\O can\\O ’\\O t\\O say\\O enough\\O about\\O their\\O delicious\\B gourmet\\O pizza\\O ’\\O s\\O !\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_479702043:3", "sentence": "You won ’ t be disappointed by their menu .", "postag": ["PRP", "VBD", "``", "NN", "VB", "VBN", "IN", "PRP$", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 9, 9, 6, 2], "deprel": ["nsubj", "root", "punct", "nsubj:pass", "aux:pass", "ccomp", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "en_MiopostoCaffe_479702043:3-0", "target_tags": "You\\O won\\O ’\\O t\\O be\\O disappointed\\O by\\O their\\O menu\\B .\\O", "opinion_tags": "You\\O won\\B ’\\I t\\I be\\I disappointed\\I by\\O their\\O menu\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_479702043:4", "sentence": "The pizza ’ s are thin crust and the menu offers very creative combinations and toppings .", "postag": ["DT", "NN", "POS", "POS", "VBP", "JJ", "NN", "CC", "DT", "NN", "VBZ", "RB", "JJ", "NNS", "CC", "NNS", "."], "head": [2, 7, 2, 2, 7, 7, 0, 11, 10, 11, 7, 13, 14, 11, 16, 14, 7], "deprel": ["det", "nsubj", "case", "case", "cop", "amod", "root", "cc", "det", "nsubj", "conj", "advmod", "amod", "obj", "cc", "conj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_479702043:4-0", "target_tags": "The\\O pizza\\O ’\\O s\\O are\\O thin\\O crust\\O and\\O the\\O menu\\B offers\\O very\\O creative\\O combinations\\O and\\O toppings\\O .\\O", "opinion_tags": "The\\O pizza\\O ’\\O s\\O are\\O thin\\O crust\\O and\\O the\\O menu\\O offers\\O very\\O creative\\B combinations\\O and\\O toppings\\O .\\O", "sentiment": "positive"}, {"uid": "en_MiopostoCaffe_479702043:4-1", "target_tags": "The\\O pizza\\B ’\\I s\\I are\\O thin\\O crust\\O and\\O the\\O menu\\O offers\\O very\\O creative\\O combinations\\O and\\O toppings\\O .\\O", "opinion_tags": "The\\O pizza\\O ’\\O s\\O are\\O thin\\B crust\\O and\\O the\\O menu\\O offers\\O very\\O creative\\O combinations\\O and\\O toppings\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_479702043:6", "sentence": "The pizza ’ s are light and scrumptious .", "postag": ["DT", "NN", "POS", "POS", "VBP", "JJ", "CC", "JJ", "."], "head": [2, 6, 2, 2, 6, 0, 8, 6, 6], "deprel": ["det", "nsubj", "case", "case", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_479702043:6-0", "target_tags": "The\\O pizza\\B ’\\I s\\I are\\O light\\O and\\O scrumptious\\O .\\O", "opinion_tags": "The\\O pizza\\O ’\\O s\\O are\\O light\\B and\\O scrumptious\\B .\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_479702043:7", "sentence": "Try the Pizza Ensalata !", "postag": ["VB", "DT", "NN", "NNP", "."], "head": [0, 3, 1, 1, 1], "deprel": ["root", "det", "obj", "obj", "punct"], "triples": [{"uid": "en_MiopostoCaffe_479702043:7-0", "target_tags": "Try\\O the\\O Pizza\\B Ensalata\\I !\\O", "opinion_tags": "Try\\B the\\O Pizza\\O Ensalata\\O !\\O", "sentiment": "positive"}]}, {"id": "en_MiopostoCaffe_479702043:10", "sentence": "The pizza ’ s are not huge and the crust is thin ... keep that in mind when you ’ re ordering .", "postag": ["DT", "NN", "POS", "POS", "VBP", "RB", "JJ", "CC", "DT", "NN", "VBZ", "JJ", ",", "VB", "DT", "IN", "NN", "WRB", "PRP", "``", "VBP", "VBG", "."], "head": [2, 7, 2, 2, 7, 7, 0, 12, 10, 12, 12, 7, 7, 7, 14, 17, 14, 21, 21, 21, 14, 21, 7], "deprel": ["det", "nsubj", "case", "case", "cop", "advmod", "root", "cc", "det", "nsubj", "cop", "conj", "punct", "parataxis", "obj", "case", "obl", "mark", "nsubj", "punct", "advcl", "xcomp", "punct"], "triples": [{"uid": "en_MiopostoCaffe_479702043:10-0", "target_tags": "The\\O pizza\\B ’\\I s\\I are\\O not\\O huge\\O and\\O the\\O crust\\O is\\O thin\\O ...\\O keep\\O that\\O in\\O mind\\O when\\O you\\O ’\\O re\\O ordering\\O .\\O", "opinion_tags": "The\\O pizza\\O ’\\O s\\O are\\O not\\B huge\\I and\\O the\\O crust\\O is\\O thin\\O ...\\O keep\\O that\\O in\\O mind\\O when\\O you\\O ’\\O re\\O ordering\\O .\\O", "sentiment": "neutral"}, {"uid": "en_MiopostoCaffe_479702043:10-1", "target_tags": "The\\O pizza\\O ’\\O s\\O are\\O not\\O huge\\O and\\O the\\O crust\\B is\\O thin\\O ...\\O keep\\O that\\O in\\O mind\\O when\\O you\\O ’\\O re\\O ordering\\O .\\O", "opinion_tags": "The\\O pizza\\O ’\\O s\\O are\\O not\\O huge\\O and\\O the\\O crust\\O is\\O thin\\B ...\\O keep\\O that\\O in\\O mind\\O when\\O you\\O ’\\O re\\O ordering\\O .\\O", "sentiment": "neutral"}]}, {"id": "en_SnoozeanAMEatery_480032670:2", "sentence": "The food is sinful .", "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480032670:2-0", "target_tags": "The\\O food\\B is\\O sinful\\O .\\O", "opinion_tags": "The\\O food\\O is\\O sinful\\B .\\O", "sentiment": "positive"}]}, {"id": "en_SnoozeanAMEatery_480032670:3", "sentence": "The staff was really friendly .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480032670:3-0", "target_tags": "The\\O staff\\B was\\O really\\O friendly\\O .\\O", "opinion_tags": "The\\O staff\\O was\\O really\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "en_SnoozeanAMEatery_480032670:4", "sentence": "The atmosphere was great .", "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480032670:4-0", "target_tags": "The\\O atmosphere\\B was\\O great\\O .\\O", "opinion_tags": "The\\O atmosphere\\O was\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "en_SnoozeanAMEatery_480032670:5", "sentence": "The specialty here is decadent pancakes , but I 've been back now four times , and I 've been wowed every time .", "postag": ["DT", "NN", "RB", "VBZ", "JJ", "NNS", ",", "CC", "PRP", "VBP", "VBN", "RB", "RB", "CD", "NNS", ",", "CC", "PRP", "VBP", "VBN", "VBN", "DT", "NN", "."], "head": [2, 6, 2, 6, 6, 0, 12, 12, 12, 12, 12, 6, 12, 15, 12, 21, 21, 21, 21, 21, 12, 23, 21, 6], "deprel": ["det", "nsubj", "advmod", "cop", "amod", "root", "punct", "cc", "nsubj", "aux", "cop", "conj", "advmod", "nummod", "obl:tmod", "punct", "cc", "nsubj:pass", "aux", "aux:pass", "conj", "det", "obl:tmod", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480032670:5-0", "target_tags": "The\\O specialty\\O here\\O is\\O decadent\\O pancakes\\B ,\\O but\\O I\\O 've\\O been\\O back\\O now\\O four\\O times\\O ,\\O and\\O I\\O 've\\O been\\O wowed\\O every\\O time\\O .\\O", "opinion_tags": "The\\O specialty\\O here\\O is\\O decadent\\B pancakes\\O ,\\O but\\O I\\O 've\\O been\\O back\\O now\\O four\\O times\\O ,\\O and\\O I\\O 've\\O been\\O wowed\\O every\\O time\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SnoozeanAMEatery_480032670:6", "sentence": "Nothing on the menu is less than amazing .", "postag": ["NN", "IN", "DT", "NN", "VBZ", "JJR", "IN", "JJ", "."], "head": [6, 4, 4, 1, 6, 0, 8, 6, 6], "deprel": ["nsubj", "case", "det", "nmod", "cop", "root", "case", "obl", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480032670:6-0", "target_tags": "Nothing\\O on\\O the\\O menu\\B is\\O less\\O than\\O amazing\\O .\\O", "opinion_tags": "Nothing\\O on\\O the\\O menu\\O is\\O less\\O than\\O amazing\\B .\\O", "sentiment": "positive"}]}, {"id": "en_SnoozeanAMEatery_480032670:8", "sentence": "Good eats .", "postag": ["JJ", "NNS", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480032670:8-0", "target_tags": "Good\\O eats\\B .\\O", "opinion_tags": "Good\\B eats\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218334:3", "sentence": "I have been to this place , folks and it is BAD .", "postag": ["PRP", "VBP", "VBN", "IN", "DT", "NN", ",", "NNS", "CC", "PRP", "VBZ", "JJ", "."], "head": [6, 6, 6, 6, 6, 0, 8, 6, 12, 12, 12, 6, 6], "deprel": ["nsubj", "aux", "cop", "case", "det", "root", "punct", "vocative", "cc", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218334:3-0", "target_tags": "I\\O have\\O been\\O to\\O this\\O place\\B ,\\O folks\\O and\\O it\\O is\\O BAD\\O .\\O", "opinion_tags": "I\\O have\\O been\\O to\\O this\\O place\\O ,\\O folks\\O and\\O it\\O is\\O BAD\\B .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478218334:4", "sentence": "Maybe it is good for that one night once in a blue moon when the chefs decide to use fish that 's half-way decent .", "postag": ["RB", "PRP", "VBZ", "JJ", "IN", "DT", "CD", "NN", "RB", "IN", "DT", "JJ", "NN", "WRB", "DT", "NNS", "VBP", "TO", "VB", "NN", "WDT", "VBZ", "RB", "JJ", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 8, 13, 13, 13, 4, 17, 16, 17, 4, 19, 17, 19, 24, 24, 24, 20, 4], "deprel": ["advmod", "expl", "cop", "root", "case", "det", "nummod", "obl", "advmod", "case", "det", "amod", "obl", "mark", "det", "nsubj", "advcl", "mark", "xcomp", "obj", "nsubj", "cop", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218334:4-0", "target_tags": "Maybe\\O it\\O is\\O good\\O for\\O that\\O one\\O night\\O once\\O in\\O a\\O blue\\O moon\\O when\\O the\\O chefs\\O decide\\O to\\O use\\O fish\\B that\\O 's\\O half-way\\O decent\\O .\\O", "opinion_tags": "Maybe\\O it\\O is\\O good\\O for\\O that\\O one\\O night\\O once\\O in\\O a\\O blue\\O moon\\O when\\O the\\O chefs\\O decide\\O to\\O use\\O fish\\O that\\O 's\\O half-way\\O decent\\B .\\O", "sentiment": "negative"}]}, {"id": "en_BlueRibbonSushi_478218334:5", "sentence": "I have been here , spent tons of money on a chef special dinner and it was a major dissappointment .", "postag": ["PRP", "VBP", "VBN", "RB", ",", "VBN", "NNS", "IN", "NN", "IN", "DT", "NN", "JJ", "NN", "CC", "PRP", "VBD", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 6, 9, 7, 14, 14, 14, 14, 6, 20, 20, 20, 20, 20, 4, 4], "deprel": ["nsubj", "aux", "cop", "root", "punct", "advcl", "obj", "case", "nmod", "case", "det", "compound", "amod", "obl", "cc", "nsubj", "cop", "det", "amod", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218334:5-0", "target_tags": "I\\O have\\O been\\O here\\O ,\\O spent\\O tons\\O of\\O money\\O on\\O a\\O chef\\B special\\I dinner\\I and\\O it\\O was\\O a\\O major\\O dissappointment\\O .\\O", "opinion_tags": "I\\O have\\O been\\O here\\O ,\\O spent\\O tons\\O of\\O money\\O on\\O a\\O chef\\O special\\O dinner\\O and\\O it\\O was\\O a\\O major\\O dissappointment\\B .\\O", "sentiment": "negative"}]}, {"id": "en_PagodaRestaurant_478006816:1", "sentence": "– The atmosphere is great for any special occasion you might want to celebrate .", "postag": ["NFP", "DT", "NN", "VBZ", "JJ", "IN", "DT", "JJ", "NN", "PRP", "MD", "VB", "TO", "VB", "."], "head": [5, 3, 5, 5, 0, 9, 9, 9, 5, 12, 12, 5, 14, 12, 5], "deprel": ["punct", "det", "nsubj", "cop", "root", "case", "det", "amod", "obl", "nsubj", "aux", "parataxis", "mark", "xcomp", "punct"], "triples": [{"uid": "en_PagodaRestaurant_478006816:1-0", "target_tags": "–\\O The\\O atmosphere\\B is\\O great\\O for\\O any\\O special\\O occasion\\O you\\O might\\O want\\O to\\O celebrate\\O .\\O", "opinion_tags": "–\\O The\\O atmosphere\\O is\\O great\\B for\\O any\\O special\\O occasion\\O you\\O might\\O want\\O to\\O celebrate\\O .\\O", "sentiment": "positive"}]}, {"id": "en_PagodaRestaurant_478006816:2", "sentence": "The best dish are the honwy walnut prawns -- just outstanding .", "postag": ["DT", "JJS", "NN", "VBP", "DT", "JJ", "NN", "NNS", ",", "RB", "JJ", "."], "head": [3, 3, 8, 8, 8, 8, 8, 0, 8, 11, 8, 8], "deprel": ["det", "amod", "nsubj", "cop", "det", "amod", "compound", "root", "punct", "advmod", "amod", "punct"], "triples": [{"uid": "en_PagodaRestaurant_478006816:2-0", "target_tags": "The\\O best\\O dish\\O are\\O the\\O honwy\\B walnut\\I prawns\\I --\\O just\\O outstanding\\O .\\O", "opinion_tags": "The\\O best\\B dish\\O are\\O the\\O honwy\\O walnut\\O prawns\\O --\\O just\\O outstanding\\B .\\O", "sentiment": "positive"}]}, {"id": "en_PagodaRestaurant_478006816:3", "sentence": "The service is really attentive and charming .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"], "triples": [{"uid": "en_PagodaRestaurant_478006816:3-0", "target_tags": "The\\O service\\B is\\O really\\O attentive\\O and\\O charming\\O .\\O", "opinion_tags": "The\\O service\\O is\\O really\\O attentive\\B and\\O charming\\B .\\O", "sentiment": "positive"}]}, {"id": "en_SnoozeanAMEatery_480133929:4", "sentence": "The service was excellent , the coffee was good even by starbucks standards and the food was outstanding .", "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "JJ", "RB", "IN", "NNS", "NNS", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4, 7, 9, 9, 4, 13, 13, 13, 9, 18, 16, 18, 18, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "parataxis", "advmod", "case", "compound", "obl", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480133929:4-0", "target_tags": "The\\O service\\B was\\O excellent\\O ,\\O the\\O coffee\\O was\\O good\\O even\\O by\\O starbucks\\O standards\\O and\\O the\\O food\\O was\\O outstanding\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\B ,\\O the\\O coffee\\O was\\O good\\O even\\O by\\O starbucks\\O standards\\O and\\O the\\O food\\O was\\O outstanding\\O .\\O", "sentiment": "positive"}, {"uid": "en_SnoozeanAMEatery_480133929:4-1", "target_tags": "The\\O service\\O was\\O excellent\\O ,\\O the\\O coffee\\B was\\O good\\O even\\O by\\O starbucks\\O standards\\O and\\O the\\O food\\O was\\O outstanding\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\O ,\\O the\\O coffee\\O was\\O good\\B even\\O by\\O starbucks\\O standards\\O and\\O the\\O food\\O was\\O outstanding\\O .\\O", "sentiment": "positive"}, {"uid": "en_SnoozeanAMEatery_480133929:4-2", "target_tags": "The\\O service\\O was\\O excellent\\O ,\\O the\\O coffee\\O was\\O good\\O even\\O by\\O starbucks\\O standards\\O and\\O the\\O food\\B was\\O outstanding\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\O ,\\O the\\O coffee\\O was\\O good\\O even\\O by\\O starbucks\\O standards\\O and\\O the\\O food\\O was\\O outstanding\\B .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970936:1", "sentence": "– I recently had the pleasure of dining as this delightful restaurant on 2nd street and wow what a great evening we had .", "postag": ["NFP", "PRP", "RB", "VBD", "DT", "NN", "IN", "VBG", "IN", "DT", "JJ", "NN", "IN", "JJ", "NN", "CC", "UH", "WDT", "DT", "JJ", "NN", "PRP", "VBD", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 12, 12, 12, 8, 15, 15, 12, 21, 4, 21, 21, 21, 17, 23, 21, 4], "deprel": ["punct", "nsubj", "advmod", "root", "det", "obj", "mark", "acl", "case", "det", "amod", "obl", "case", "amod", "nmod", "cc", "conj", "det", "det", "amod", "obj", "nsubj", "acl:relcl", "punct"], "triples": [{"uid": "en_OpenSesame_477970936:1-0", "target_tags": "–\\O I\\O recently\\O had\\O the\\O pleasure\\O of\\O dining\\O as\\O this\\O delightful\\O restaurant\\B on\\O 2nd\\O street\\O and\\O wow\\O what\\O a\\O great\\O evening\\O we\\O had\\O .\\O", "opinion_tags": "–\\O I\\O recently\\O had\\O the\\O pleasure\\O of\\O dining\\O as\\O this\\O delightful\\B restaurant\\O on\\O 2nd\\O street\\O and\\O wow\\O what\\O a\\O great\\O evening\\O we\\O had\\O .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970936:2", "sentence": "The food is fantastic , authentic , delicious and very , very affordable .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "JJ", ",", "JJ", "CC", "RB", ",", "RB", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 8, 4, 13, 13, 13, 13, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "conj", "punct", "conj", "cc", "advmod", "punct", "advmod", "conj", "punct"], "triples": [{"uid": "en_OpenSesame_477970936:2-0", "target_tags": "The\\O food\\B is\\O fantastic\\O ,\\O authentic\\O ,\\O delicious\\O and\\O very\\O ,\\O very\\O affordable\\O .\\O", "opinion_tags": "The\\O food\\O is\\O fantastic\\B ,\\O authentic\\B ,\\O delicious\\B and\\O very\\O ,\\O very\\O affordable\\B .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970936:3", "sentence": "The decor was beautiful and unique .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "en_OpenSesame_477970936:3-0", "target_tags": "The\\O decor\\B was\\O beautiful\\O and\\O unique\\O .\\O", "opinion_tags": "The\\O decor\\O was\\O beautiful\\B and\\O unique\\B .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970936:4", "sentence": "There was a really nice vibe about the place ... good music , atmosphere and happy looking people .", "postag": ["EX", "VBD", "DT", "RB", "JJ", "NN", "IN", "DT", "NN", ",", "JJ", "NN", ",", "NN", "CC", "JJ", "VBG", "NNS", "."], "head": [2, 0, 6, 5, 6, 2, 9, 9, 6, 2, 12, 6, 14, 12, 18, 18, 18, 12, 2], "deprel": ["expl", "root", "det", "advmod", "amod", "nsubj", "case", "det", "nmod", "punct", "amod", "appos", "punct", "conj", "cc", "amod", "amod", "conj", "punct"], "triples": [{"uid": "en_OpenSesame_477970936:4-0", "target_tags": "There\\O was\\O a\\O really\\O nice\\O vibe\\O about\\O the\\O place\\O ...\\O good\\O music\\B ,\\O atmosphere\\O and\\O happy\\O looking\\O people\\O .\\O", "opinion_tags": "There\\O was\\O a\\O really\\O nice\\O vibe\\O about\\O the\\O place\\O ...\\O good\\B music\\O ,\\O atmosphere\\O and\\O happy\\O looking\\O people\\O .\\O", "sentiment": "positive"}, {"uid": "en_OpenSesame_477970936:4-1", "target_tags": "There\\O was\\O a\\O really\\O nice\\O vibe\\O about\\O the\\O place\\O ...\\O good\\O music\\O ,\\O atmosphere\\B and\\O happy\\O looking\\O people\\O .\\O", "opinion_tags": "There\\O was\\O a\\O really\\O nice\\O vibe\\O about\\O the\\O place\\O ...\\O good\\B music\\O ,\\O atmosphere\\O and\\O happy\\O looking\\O people\\O .\\O", "sentiment": "positive"}, {"uid": "en_OpenSesame_477970936:4-2", "target_tags": "There\\O was\\O a\\O really\\O nice\\O vibe\\B about\\O the\\O place\\O ...\\O good\\O music\\O ,\\O atmosphere\\O and\\O happy\\O looking\\O people\\O .\\O", "opinion_tags": "There\\O was\\O a\\O really\\O nice\\B vibe\\O about\\O the\\O place\\O ...\\O good\\O music\\O ,\\O atmosphere\\O and\\O happy\\O looking\\O people\\O .\\O", "sentiment": "positive"}, {"uid": "en_OpenSesame_477970936:4-3", "target_tags": "There\\O was\\O a\\O really\\O nice\\O vibe\\O about\\O the\\O place\\O ...\\O good\\O music\\O ,\\O atmosphere\\O and\\O happy\\O looking\\O people\\B .\\O", "opinion_tags": "There\\O was\\O a\\O really\\O nice\\O vibe\\O about\\O the\\O place\\O ...\\O good\\O music\\O ,\\O atmosphere\\O and\\O happy\\B looking\\O people\\O .\\O", "sentiment": "positive"}]}, {"id": "en_OpenSesame_477970936:5", "sentence": "Our server was very professional and friendly .", "postag": ["PRP$", "NN", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["nmod:poss", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"], "triples": [{"uid": "en_OpenSesame_477970936:5-0", "target_tags": "Our\\O server\\B was\\O very\\O professional\\O and\\O friendly\\O .\\O", "opinion_tags": "Our\\O server\\O was\\O very\\O professional\\B and\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218341:1", "sentence": "It 's a tiny place so if you get there before 8pm on a weekend ( Thurs ? Sun ) you will find it easier to get a table or a seat at the sushi bar .", "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "RB", "IN", "PRP", "VBP", "RB", "IN", "NN", "IN", "DT", "NN", "-LRB-", "NNP", ".", "NNP", "-RRB-", "PRP", "MD", "VB", "PRP", "JJR", "TO", "VB", "DT", "NN", "CC", "DT", "NN", "IN", "DT", "NN", "NN", "."], "head": [5, 5, 5, 5, 0, 23, 9, 9, 23, 9, 12, 9, 15, 15, 9, 17, 15, 17, 17, 17, 23, 23, 5, 23, 23, 27, 25, 29, 27, 32, 32, 29, 36, 36, 36, 27, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "mark", "nsubj", "advcl", "advmod", "case", "obl", "case", "det", "obl", "punct", "appos", "punct", "appos", "punct", "nsubj", "aux", "parataxis", "expl", "xcomp", "mark", "xcomp", "det", "obj", "cc", "det", "conj", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218341:1-0", "target_tags": "It\\O 's\\O a\\O tiny\\O place\\B so\\O if\\O you\\O get\\O there\\O before\\O 8pm\\O on\\O a\\O weekend\\O (\\O Thurs\\O ?\\O Sun\\O )\\O you\\O will\\O find\\O it\\O easier\\O to\\O get\\O a\\O table\\O or\\O a\\O seat\\O at\\O the\\O sushi\\O bar\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O tiny\\B place\\O so\\O if\\O you\\O get\\O there\\O before\\O 8pm\\O on\\O a\\O weekend\\O (\\O Thurs\\O ?\\O Sun\\O )\\O you\\O will\\O find\\O it\\O easier\\O to\\O get\\O a\\O table\\O or\\O a\\O seat\\O at\\O the\\O sushi\\O bar\\O .\\O", "sentiment": "neutral"}]}, {"id": "en_BlueRibbonSushi_478218341:3", "sentence": "Everything , and I mean everything on the menu is delectable .", "postag": ["NN", ",", "CC", "PRP", "VBP", "NN", "IN", "DT", "NN", "VBZ", "JJ", "."], "head": [11, 5, 4, 1, 11, 11, 9, 9, 6, 11, 0, 11], "deprel": ["nsubj", "punct", "cc", "conj", "discourse", "nsubj", "case", "det", "nmod", "cop", "root", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218341:3-0", "target_tags": "Everything\\O ,\\O and\\O I\\O mean\\O everything\\O on\\O the\\O menu\\B is\\O delectable\\O .\\O", "opinion_tags": "Everything\\O ,\\O and\\O I\\O mean\\O everything\\O on\\O the\\O menu\\O is\\O delectable\\B .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218341:4", "sentence": "The waiters are very experienced and helpful with pairing your drink choice to your food tastes or vice versa .", "postag": ["DT", "NNS", "VBP", "RB", "JJ", "CC", "JJ", "IN", "VBG", "PRP$", "NN", "NN", "IN", "PRP$", "NN", "NNS", "CC", "NN", "RB", "."], "head": [2, 5, 5, 5, 0, 7, 5, 9, 5, 12, 12, 9, 16, 16, 16, 9, 19, 19, 16, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "mark", "advcl", "nmod:poss", "compound", "obj", "case", "nmod:poss", "compound", "obl", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218341:4-0", "target_tags": "The\\O waiters\\B are\\O very\\O experienced\\O and\\O helpful\\O with\\O pairing\\O your\\O drink\\O choice\\O to\\O your\\O food\\O tastes\\O or\\O vice\\O versa\\O .\\O", "opinion_tags": "The\\O waiters\\O are\\O very\\O experienced\\B and\\O helpful\\B with\\O pairing\\O your\\O drink\\O choice\\O to\\O your\\O food\\O tastes\\O or\\O vice\\O versa\\O .\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218341:5", "sentence": "The sushi is as fresh as it comes ? you 'd think ocean was in their backyard , no joke !", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "IN", "PRP", "VBZ", ".", "PRP", "MD", "VB", "NN", "VBD", "IN", "PRP$", "NN", ",", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 8, 8, 4, 5, 12, 12, 5, 17, 17, 17, 17, 12, 20, 20, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "mark", "nsubj", "advcl", "punct", "nsubj", "aux", "parataxis", "nsubj", "cop", "case", "nmod:poss", "ccomp", "punct", "det", "parataxis", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218341:5-0", "target_tags": "The\\O sushi\\B is\\O as\\O fresh\\O as\\O it\\O comes\\O ?\\O you\\O 'd\\O think\\O ocean\\O was\\O in\\O their\\O backyard\\O ,\\O no\\O joke\\O !\\O", "opinion_tags": "The\\O sushi\\O is\\O as\\O fresh\\B as\\O it\\O comes\\O ?\\O you\\O 'd\\O think\\O ocean\\O was\\O in\\O their\\O backyard\\O ,\\O no\\O joke\\O !\\O", "sentiment": "positive"}]}, {"id": "en_BlueRibbonSushi_478218341:6", "sentence": "If you 're interested in good tasting ( without the fish taste or smell ) , large portions and creative sushi dishes this is your place ...", "postag": ["IN", "PRP", "VBP", "JJ", "IN", "JJ", "NN", "-LRB-", "IN", "DT", "NN", "NN", "CC", "NN", "-RRB-", ",", "JJ", "NNS", "CC", "JJ", "NN", "NNS", "DT", "VBZ", "PRP$", "NN", "."], "head": [4, 4, 4, 26, 7, 7, 4, 12, 12, 12, 12, 4, 14, 12, 12, 26, 18, 26, 22, 21, 22, 12, 26, 26, 26, 0, 26], "deprel": ["mark", "nsubj", "cop", "advcl", "case", "amod", "obl", "punct", "case", "det", "compound", "obl", "cc", "conj", "punct", "punct", "amod", "nsubj", "cc", "amod", "compound", "conj", "nsubj", "cop", "nmod:poss", "root", "punct"], "triples": [{"uid": "en_BlueRibbonSushi_478218341:6-0", "target_tags": "If\\O you\\O 're\\O interested\\O in\\O good\\O tasting\\O (\\O without\\O the\\O fish\\O taste\\O or\\O smell\\O )\\O ,\\O large\\O portions\\B and\\O creative\\O sushi\\O dishes\\O this\\O is\\O your\\O place\\O ...\\O", "opinion_tags": "If\\O you\\O 're\\O interested\\O in\\O good\\O tasting\\O (\\O without\\O the\\O fish\\O taste\\O or\\O smell\\O )\\O ,\\O large\\B portions\\O and\\O creative\\O sushi\\O dishes\\O this\\O is\\O your\\O place\\O ...\\O", "sentiment": "positive"}, {"uid": "en_BlueRibbonSushi_478218341:6-1", "target_tags": "If\\O you\\O 're\\O interested\\O in\\O good\\O tasting\\O (\\O without\\O the\\O fish\\O taste\\O or\\O smell\\O )\\O ,\\O large\\O portions\\O and\\O creative\\O sushi\\B dishes\\I this\\O is\\O your\\O place\\O ...\\O", "opinion_tags": "If\\O you\\O 're\\O interested\\O in\\O good\\O tasting\\O (\\O without\\O the\\O fish\\O taste\\O or\\O smell\\O )\\O ,\\O large\\O portions\\O and\\O creative\\B sushi\\O dishes\\O this\\O is\\O your\\O place\\O ...\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON>'sPizzeria_478232062:0", "sentence": "big thick pepperoni", "postag": ["JJ", "JJ", "NN"], "head": [3, 3, 0], "deprel": ["amod", "amod", "root"], "triples": [{"uid": "en_<PERSON><PERSON>'sPizzeria_478232062:0-0", "target_tags": "big\\O thick\\O pepperoni\\B", "opinion_tags": "big\\B thick\\B pepperoni\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON>'sPizzeria_478232062:1", "sentence": "– The pepperoni 's cut real thick -- Yum .", "postag": ["NFP", "DT", "NN", "POS", "VBN", "JJ", "JJ", ",", "UH", "."], "head": [7, 3, 7, 3, 7, 7, 0, 7, 7, 7], "deprel": ["punct", "det", "nmod:poss", "case", "amod", "amod", "root", "punct", "discourse", "punct"], "triples": [{"uid": "en_<PERSON><PERSON>'sPizzeria_478232062:1-0", "target_tags": "–\\O The\\O pepperoni\\B 's\\I cut\\O real\\O thick\\O --\\O Yum\\O .\\O", "opinion_tags": "–\\O The\\O pepperoni\\O 's\\O cut\\O real\\O thick\\B --\\O Yum\\B .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON><PERSON>'sPizzeria_478232062:2", "sentence": "The pizza itself is not exactly the best I 've had EVER , but still pretty good .", "postag": ["DT", "NN", "PRP", "VBZ", "RB", "RB", "DT", "JJS", "PRP", "VBP", "VBN", "RB", ",", "CC", "RB", "RB", "JJ", "."], "head": [2, 8, 2, 8, 8, 8, 8, 0, 11, 11, 8, 11, 17, 17, 17, 17, 8, 8], "deprel": ["det", "nsubj", "nmod:npmod", "cop", "advmod", "advmod", "det", "root", "nsubj", "aux", "acl:relcl", "advmod", "punct", "cc", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "en_<PERSON><PERSON>'sPizzeria_478232062:2-0", "target_tags": "The\\O pizza\\B itself\\O is\\O not\\O exactly\\O the\\O best\\O I\\O 've\\O had\\O EVER\\O ,\\O but\\O still\\O pretty\\O good\\O .\\O", "opinion_tags": "The\\O pizza\\O itself\\O is\\O not\\B exactly\\I the\\I best\\I I\\O 've\\O had\\O EVER\\O ,\\O but\\O still\\O pretty\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "en_CatalRestaurant__UvaBar_477862052:2", "sentence": "Food was good and appetizing .", "postag": ["NN", "VBD", "JJ", "CC", "JJ", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477862052:2-0", "target_tags": "Food\\B was\\O good\\O and\\O appetizing\\O .\\O", "opinion_tags": "Food\\O was\\O good\\B and\\O appetizing\\B .\\O", "sentiment": "positive"}]}, {"id": "en_CatalRestaurant__UvaBar_477862052:3", "sentence": "Portions was just enough for me , but may not be for a big eater .", "postag": ["NNS", "VBD", "RB", "JJ", "IN", "PRP", ",", "CC", "MD", "RB", "VB", "IN", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 15, 15, 15, 15, 15, 15, 15, 15, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "case", "obl", "punct", "cc", "aux", "advmod", "cop", "case", "det", "amod", "conj", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477862052:3-0", "target_tags": "Portions\\B was\\O just\\O enough\\O for\\O me\\O ,\\O but\\O may\\O not\\O be\\O for\\O a\\O big\\O eater\\O .\\O", "opinion_tags": "Portions\\O was\\O just\\O enough\\B for\\O me\\O ,\\O but\\O may\\O not\\O be\\O for\\O a\\O big\\O eater\\O .\\O", "sentiment": "neutral"}]}, {"id": "en_CatalRestaurant__UvaBar_477862052:4", "sentence": "Fair menu selection .", "postag": ["JJ", "NN", "NN", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "compound", "root", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477862052:4-0", "target_tags": "Fair\\O menu\\B selection\\I .\\O", "opinion_tags": "Fair\\B menu\\O selection\\O .\\O", "sentiment": "neutral"}]}, {"id": "en_CatalRestaurant__UvaBar_477862052:5", "sentence": "The appetizer was interesting , but the Creme Brulee was very savory and delicious .", "postag": ["DT", "NN", "VBD", "JJ", ",", "CC", "DT", "NNP", "NNP", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 12, 12, 9, 9, 12, 12, 12, 4, 14, 12, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "det", "compound", "nsubj", "cop", "advmod", "conj", "cc", "conj", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477862052:5-0", "target_tags": "The\\O appetizer\\B was\\O interesting\\O ,\\O but\\O the\\O Creme\\O Brulee\\O was\\O very\\O savory\\O and\\O delicious\\O .\\O", "opinion_tags": "The\\O appetizer\\O was\\O interesting\\B ,\\O but\\O the\\O Creme\\O Brulee\\O was\\O very\\O savory\\O and\\O delicious\\O .\\O", "sentiment": "positive"}, {"uid": "en_CatalRestaurant__UvaBar_477862052:5-1", "target_tags": "The\\O appetizer\\O was\\O interesting\\O ,\\O but\\O the\\O Creme\\B Brulee\\I was\\O very\\O savory\\O and\\O delicious\\O .\\O", "opinion_tags": "The\\O appetizer\\O was\\O interesting\\O ,\\O but\\O the\\O Creme\\O Brulee\\O was\\O very\\O savory\\B and\\O delicious\\B .\\O", "sentiment": "positive"}]}, {"id": "en_CatalRestaurant__UvaBar_477862052:6", "sentence": "Indoor ambience was modern .", "postag": ["JJ", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["amod", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477862052:6-0", "target_tags": "Indoor\\B ambience\\I was\\O modern\\O .\\O", "opinion_tags": "Indoor\\O ambience\\O was\\O modern\\B .\\O", "sentiment": "positive"}]}, {"id": "en_CatalRestaurant__UvaBar_477862052:8", "sentence": "It 's a great place to people watch .", "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "IN", "NNS", "VB", "."], "head": [5, 5, 5, 5, 0, 7, 8, 5, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "case", "nsubj", "acl", "punct"], "triples": [{"uid": "en_CatalRestaurant__UvaBar_477862052:8-0", "target_tags": "It\\O 's\\O a\\O great\\O place\\B to\\O people\\O watch\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O great\\B place\\O to\\O people\\O watch\\O .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538111:0", "sentence": "Late night dinning with exeptional food .", "postag": ["JJ", "NN", "VBG", "IN", "JJ", "NN", "."], "head": [2, 0, 2, 6, 6, 3, 2], "deprel": ["amod", "root", "acl", "case", "amod", "obl", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538111:0-0", "target_tags": "Late\\O night\\O dinning\\O with\\O exeptional\\O food\\B .\\O", "opinion_tags": "Late\\O night\\O dinning\\O with\\O exeptional\\B food\\O .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538111:3", "sentence": "We were seated right away , the table was private and nice .", "postag": ["PRP", "VBD", "VBN", "RB", "RB", ",", "DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [3, 3, 0, 5, 3, 10, 8, 10, 10, 3, 12, 10, 3], "deprel": ["nsubj:pass", "aux:pass", "root", "advmod", "advmod", "punct", "det", "nsubj", "cop", "conj", "cc", "conj", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538111:3-0", "target_tags": "We\\O were\\O seated\\O right\\O away\\O ,\\O the\\O table\\B was\\O private\\O and\\O nice\\O .\\O", "opinion_tags": "We\\O were\\O seated\\O right\\O away\\O ,\\O the\\O table\\O was\\O private\\B and\\O nice\\B .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538111:4", "sentence": "The service was exceptional - sometime there was a feeling that we were served by the army of friendly waiters .", "postag": ["DT", "NN", "VBD", "JJ", ",", "RB", "EX", "VBD", "DT", "NN", "IN", "PRP", "VBD", "VBN", "IN", "DT", "NN", "IN", "JJ", "NNS", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 10, 8, 14, 14, 14, 10, 17, 17, 14, 20, 20, 17, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "advmod", "expl", "parataxis", "det", "nsubj", "mark", "nsubj:pass", "aux:pass", "acl", "case", "det", "obl", "case", "amod", "nmod", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538111:4-0", "target_tags": "The\\O service\\B was\\O exceptional\\O -\\O sometime\\O there\\O was\\O a\\O feeling\\O that\\O we\\O were\\O served\\O by\\O the\\O army\\O of\\O friendly\\O waiters\\O .\\O", "opinion_tags": "The\\O service\\O was\\O exceptional\\B -\\O sometime\\O there\\O was\\O a\\O feeling\\O that\\O we\\O were\\O served\\O by\\O the\\O army\\O of\\O friendly\\O waiters\\O .\\O", "sentiment": "positive"}, {"uid": "en_StackRestaurant__Bar_478538111:4-1", "target_tags": "The\\O service\\O was\\O exceptional\\O -\\O sometime\\O there\\O was\\O a\\O feeling\\O that\\O we\\O were\\O served\\O by\\O the\\O army\\O of\\O friendly\\O waiters\\B .\\O", "opinion_tags": "The\\O service\\O was\\O exceptional\\O -\\O sometime\\O there\\O was\\O a\\O feeling\\O that\\O we\\O were\\O served\\O by\\O the\\O army\\O of\\O friendly\\B waiters\\O .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538111:5", "sentence": "The food was very good , filet mignon was probably the best I 've ever try .", "postag": ["DT", "NN", "VBD", "RB", "JJ", ",", "NN", "NN", "VBD", "RB", "DT", "JJS", "PRP", "VBP", "RB", "VBN", "."], "head": [2, 5, 5, 5, 0, 5, 8, 12, 12, 12, 12, 5, 16, 16, 16, 12, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "compound", "nsubj", "cop", "advmod", "det", "conj", "nsubj", "aux", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538111:5-0", "target_tags": "The\\O food\\B was\\O very\\O good\\O ,\\O filet\\O mignon\\O was\\O probably\\O the\\O best\\O I\\O 've\\O ever\\O try\\O .\\O", "opinion_tags": "The\\O food\\O was\\O very\\O good\\B ,\\O filet\\O mignon\\O was\\O probably\\O the\\O best\\O I\\O 've\\O ever\\O try\\O .\\O", "sentiment": "positive"}, {"uid": "en_StackRestaurant__Bar_478538111:5-1", "target_tags": "The\\O food\\O was\\O very\\O good\\O ,\\O filet\\B mignon\\I was\\O probably\\O the\\O best\\O I\\O 've\\O ever\\O try\\O .\\O", "opinion_tags": "The\\O food\\O was\\O very\\O good\\O ,\\O filet\\O mignon\\O was\\O probably\\O the\\O best\\B I\\O 've\\O ever\\O try\\O .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538111:6", "sentence": "The portions are big though , so do not order too much .", "postag": ["DT", "NNS", "VBP", "JJ", "RB", ",", "RB", "VB", "RB", "VB", "RB", "RB", "."], "head": [2, 4, 4, 0, 4, 4, 10, 10, 10, 4, 12, 10, 4], "deprel": ["det", "nsubj", "cop", "root", "advmod", "punct", "advmod", "aux", "advmod", "parataxis", "advmod", "advmod", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538111:6-0", "target_tags": "The\\O portions\\B are\\O big\\O though\\O ,\\O so\\O do\\O not\\O order\\O too\\O much\\O .\\O", "opinion_tags": "The\\O portions\\O are\\O big\\B though\\O ,\\O so\\O do\\O not\\O order\\O too\\O much\\O .\\O", "sentiment": "neutral"}]}, {"id": "en_StackRestaurant__Bar_478538111:7", "sentence": "Groovy music made the dinner casual .", "postag": ["JJ", "NN", "VBD", "DT", "NN", "JJ", "."], "head": [2, 3, 0, 5, 3, 3, 3], "deprel": ["amod", "nsubj", "root", "det", "obj", "xcomp", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538111:7-0", "target_tags": "Groovy\\O music\\B made\\O the\\O dinner\\O casual\\O .\\O", "opinion_tags": "Groovy\\O music\\O made\\O the\\O dinner\\O casual\\B .\\O", "sentiment": "positive"}]}, {"id": "en_StackRestaurant__Bar_478538111:9", "sentence": "I have a but here - there was a bathroom attendant in the restroom which was odd .", "postag": ["PRP", "VBP", "DT", "CC", "RB", ",", "EX", "VBD", "DT", "NN", "NN", "IN", "DT", "NN", "WDT", "VBD", "JJ", "."], "head": [2, 0, 2, 5, 3, 2, 8, 2, 11, 11, 8, 14, 14, 11, 17, 17, 11, 2], "deprel": ["nsubj", "root", "obj", "cc", "conj", "punct", "expl", "parataxis", "det", "compound", "nsubj", "case", "det", "nmod", "nsubj", "cop", "acl:relcl", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538111:9-0", "target_tags": "I\\O have\\O a\\O but\\O here\\O -\\O there\\O was\\O a\\O bathroom\\O attendant\\O in\\O the\\O restroom\\B which\\O was\\O odd\\O .\\O", "opinion_tags": "I\\O have\\O a\\O but\\O here\\O -\\O there\\O was\\O a\\O bathroom\\O attendant\\O in\\O the\\O restroom\\O which\\O was\\O odd\\B .\\O", "sentiment": "negative"}]}, {"id": "en_StackRestaurant__Bar_478538111:10", "sentence": "The bathroom itself is very small with two toilets and only one sink , the girl was staying totally on the way hanging out paper towels from the dispenser .", "postag": ["DT", "NN", "PRP", "VBZ", "RB", "JJ", "IN", "CD", "NNS", "CC", "RB", "CD", "NN", ",", "DT", "NN", "VBD", "VBG", "RB", "IN", "DT", "NN", "VBG", "RP", "NN", "NNS", "IN", "DT", "NN", "."], "head": [2, 6, 2, 6, 6, 0, 9, 9, 6, 13, 13, 13, 9, 18, 16, 18, 18, 6, 18, 22, 22, 18, 22, 23, 26, 23, 29, 29, 23, 6], "deprel": ["det", "nsubj", "nmod:npmod", "cop", "advmod", "root", "case", "nummod", "obl", "cc", "advmod", "nummod", "conj", "punct", "det", "nsubj", "aux", "conj", "advmod", "case", "det", "obl", "acl", "compound:prt", "compound", "obj", "case", "det", "obl", "punct"], "triples": [{"uid": "en_StackRestaurant__Bar_478538111:10-0", "target_tags": "The\\O bathroom\\B itself\\O is\\O very\\O small\\O with\\O two\\O toilets\\O and\\O only\\O one\\O sink\\O ,\\O the\\O girl\\O was\\O staying\\O totally\\O on\\O the\\O way\\O hanging\\O out\\O paper\\O towels\\O from\\O the\\O dispenser\\O .\\O", "opinion_tags": "The\\O bathroom\\O itself\\O is\\O very\\O small\\B with\\O two\\O toilets\\O and\\O only\\O one\\O sink\\O ,\\O the\\O girl\\O was\\O staying\\O totally\\O on\\O the\\O way\\O hanging\\O out\\O paper\\O towels\\O from\\O the\\O dispenser\\O .\\O", "sentiment": "negative"}]}, {"id": "en_MercedesRestaurant_478010600:1", "sentence": "– Mercedes restaurant is so tasty , the service is undeniably awesome !", "postag": ["NFP", "NNP", "NN", "VBZ", "RB", "JJ", ",", "DT", "NN", "VBZ", "RB", "JJ", "."], "head": [6, 3, 6, 6, 6, 0, 6, 9, 12, 12, 12, 6, 6], "deprel": ["punct", "compound", "nsubj", "cop", "advmod", "root", "punct", "det", "nsubj", "cop", "advmod", "parataxis", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010600:1-0", "target_tags": "–\\O Mercedes\\O restaurant\\O is\\O so\\O tasty\\O ,\\O the\\O service\\B is\\O undeniably\\O awesome\\O !\\O", "opinion_tags": "–\\O Mercedes\\O restaurant\\O is\\O so\\O tasty\\O ,\\O the\\O service\\O is\\O undeniably\\O awesome\\B !\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010600:2", "sentence": "The chips and salsa are so yummy , and the prices are fabulous .", "postag": ["DT", "NNS", "CC", "NN", "VBP", "RB", "JJ", ",", "CC", "DT", "NNS", "VBP", "JJ", "."], "head": [2, 7, 4, 2, 7, 7, 0, 13, 13, 11, 13, 13, 7, 7], "deprel": ["det", "nsubj", "cc", "conj", "cop", "advmod", "root", "punct", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010600:2-0", "target_tags": "The\\O chips\\B and\\I salsa\\I are\\O so\\O yummy\\O ,\\O and\\O the\\O prices\\O are\\O fabulous\\O .\\O", "opinion_tags": "The\\O chips\\O and\\O salsa\\O are\\O so\\O yummy\\B ,\\O and\\O the\\O prices\\O are\\O fabulous\\O .\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010600:3", "sentence": "The atmosphere is aspiring , and the decor is festive and amazing ...", "postag": ["DT", "NN", "VBZ", "VBG", ",", "CC", "DT", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 10, 10, 8, 10, 10, 4, 12, 10, 4], "deprel": ["det", "nsubj", "aux", "root", "punct", "cc", "det", "nsubj", "cop", "conj", "cc", "conj", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010600:3-0", "target_tags": "The\\O atmosphere\\B is\\O aspiring\\O ,\\O and\\O the\\O decor\\O is\\O festive\\O and\\O amazing\\O ...\\O", "opinion_tags": "The\\O atmosphere\\O is\\O aspiring\\B ,\\O and\\O the\\O decor\\O is\\O festive\\O and\\O amazing\\O ...\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_478010600:3-1", "target_tags": "The\\O atmosphere\\O is\\O aspiring\\O ,\\O and\\O the\\O decor\\B is\\O festive\\O and\\O amazing\\O ...\\O", "opinion_tags": "The\\O atmosphere\\O is\\O aspiring\\O ,\\O and\\O the\\O decor\\O is\\O festive\\B and\\O amazing\\B ...\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010600:4", "sentence": "The catering is out of this world , and <PERSON><PERSON> chicken vegetable soup rocks my world ! ! !", "postag": ["DT", "NN", "VBZ", "IN", "IN", "DT", "NN", ",", "CC", "NNP", "NN", "NN", "NN", "VBZ", "PRP$", "NN", ".", ".", "."], "head": [2, 7, 7, 7, 7, 7, 0, 14, 14, 13, 13, 13, 14, 7, 16, 14, 7, 7, 7], "deprel": ["det", "nsubj", "cop", "case", "case", "det", "root", "punct", "cc", "compound", "compound", "compound", "nsubj", "conj", "nmod:poss", "obj", "punct", "punct", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010600:4-0", "target_tags": "The\\O catering\\O is\\O out\\O of\\O this\\O world\\O ,\\O and\\O Raouls\\B chicken\\I vegetable\\I soup\\I rocks\\O my\\O world\\O !\\O !\\O !\\O", "opinion_tags": "The\\O catering\\O is\\O out\\O of\\O this\\O world\\O ,\\O and\\O Raouls\\O chicken\\O vegetable\\O soup\\O rocks\\B my\\I world\\I !\\O !\\O !\\O", "sentiment": "positive"}, {"uid": "en_MercedesRestaurant_478010600:4-1", "target_tags": "The\\O catering\\B is\\O out\\O of\\O this\\O world\\O ,\\O and\\O Raouls\\O chicken\\O vegetable\\O soup\\O rocks\\O my\\O world\\O !\\O !\\O !\\O", "opinion_tags": "The\\O catering\\O is\\O out\\B of\\I this\\I world\\I ,\\O and\\O Raouls\\O chicken\\O vegetable\\O soup\\O rocks\\O my\\O world\\O !\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "en_MercedesRestaurant_478010600:5", "sentence": "Drinks are suberb , and I feel like I am in a Third World country when I walk in the door .", "postag": ["NNS", "VBP", "JJ", ",", "CC", "PRP", "VBP", "IN", "PRP", "VBP", "IN", "DT", "JJ", "NNP", "NN", "WRB", "PRP", "VBP", "IN", "DT", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 15, 15, 15, 15, 15, 15, 15, 7, 18, 18, 7, 21, 21, 18, 3], "deprel": ["nsubj", "cop", "root", "punct", "cc", "nsubj", "conj", "mark", "nsubj", "cop", "case", "det", "amod", "compound", "advcl", "mark", "nsubj", "advcl", "case", "det", "obl", "punct"], "triples": [{"uid": "en_MercedesRestaurant_478010600:5-0", "target_tags": "Drinks\\B are\\O suberb\\O ,\\O and\\O I\\O feel\\O like\\O I\\O am\\O in\\O a\\O Third\\O World\\O country\\O when\\O I\\O walk\\O in\\O the\\O door\\O .\\O", "opinion_tags": "Drinks\\O are\\O suberb\\B ,\\O and\\O I\\O feel\\O like\\O I\\O am\\O in\\O a\\O Third\\O World\\O country\\O when\\O I\\O walk\\O in\\O the\\O door\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SnoozeanAMEatery_480171723:1", "sentence": "- Mediocre Service / Quality", "postag": ["NFP", "JJ", "NN", ",", "NN"], "head": [3, 3, 0, 5, 3], "deprel": ["punct", "amod", "root", "cc", "conj"], "triples": [{"uid": "en_SnoozeanAMEatery_480171723:1-0", "target_tags": "-\\O Mediocre\\O Service\\B /\\O Quality\\O", "opinion_tags": "-\\O Mediocre\\B Service\\O /\\O Quality\\O", "sentiment": "neutral"}]}, {"id": "en_SnoozeanAMEatery_480171723:3", "sentence": "The presentation of Snooze is excellent and it is one of those places that you feel more sophisticated just for being there ; but peel back the layers and you have an overpriced IHOP with a high brow menu .", "postag": ["DT", "NN", "IN", "NNP", "VBZ", "JJ", "CC", "PRP", "VBZ", "CD", "IN", "DT", "NNS", "WDT", "PRP", "VBP", "RBR", "JJ", "RB", "IN", "VBG", "RB", ",", "CC", "VB", "RP", "DT", "NNS", "CC", "PRP", "VBP", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", "NN", "."], "head": [2, 6, 4, 2, 6, 0, 10, 10, 10, 6, 13, 13, 10, 16, 16, 13, 18, 16, 21, 21, 16, 21, 25, 25, 10, 25, 28, 25, 31, 31, 25, 34, 34, 31, 39, 39, 39, 39, 34, 6], "deprel": ["det", "nsubj", "case", "nmod", "cop", "root", "cc", "nsubj", "cop", "conj", "case", "det", "nmod", "obj", "nsubj", "acl:relcl", "advmod", "xcomp", "advmod", "mark", "advcl", "advmod", "punct", "cc", "conj", "compound:prt", "det", "obj", "cc", "nsubj", "conj", "det", "amod", "obj", "case", "det", "amod", "compound", "nmod", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480171723:3-0", "target_tags": "The\\O presentation\\O of\\O Snooze\\B is\\O excellent\\O and\\O it\\O is\\O one\\O of\\O those\\O places\\O that\\O you\\O feel\\O more\\O sophisticated\\O just\\O for\\O being\\O there\\O ;\\O but\\O peel\\O back\\O the\\O layers\\O and\\O you\\O have\\O an\\O overpriced\\O IHOP\\O with\\O a\\O high\\O brow\\O menu\\O .\\O", "opinion_tags": "The\\O presentation\\O of\\O Snooze\\O is\\O excellent\\B and\\O it\\O is\\O one\\O of\\O those\\O places\\O that\\O you\\O feel\\O more\\O sophisticated\\O just\\O for\\O being\\O there\\O ;\\O but\\O peel\\O back\\O the\\O layers\\O and\\O you\\O have\\O an\\O overpriced\\O IHOP\\O with\\O a\\O high\\O brow\\O menu\\O .\\O", "sentiment": "negative"}, {"uid": "en_SnoozeanAMEatery_480171723:3-1", "target_tags": "The\\O presentation\\O of\\O Snooze\\O is\\O excellent\\O and\\O it\\O is\\O one\\O of\\O those\\O places\\O that\\O you\\O feel\\O more\\O sophisticated\\O just\\O for\\O being\\O there\\O ;\\O but\\O peel\\O back\\O the\\O layers\\O and\\O you\\O have\\O an\\O overpriced\\O IHOP\\O with\\O a\\O high\\O brow\\O menu\\B .\\O", "opinion_tags": "The\\O presentation\\O of\\O Snooze\\O is\\O excellent\\O and\\O it\\O is\\O one\\O of\\O those\\O places\\O that\\O you\\O feel\\O more\\O sophisticated\\O just\\O for\\O being\\O there\\O ;\\O but\\O peel\\O back\\O the\\O layers\\O and\\O you\\O have\\O an\\O overpriced\\O IHOP\\O with\\O a\\O high\\B brow\\I menu\\O .\\O", "sentiment": "negative"}]}, {"id": "en_SnoozeanAMEatery_480171723:5", "sentence": "They serve it in a tall , skinny hour-glass shaped glass to disguise the fact that you a getting a small juice at the price of a half gallon in a supermarket .", "postag": ["PRP", "VBP", "PRP", "IN", "DT", "JJ", ",", "JJ", "JJ", "VBN", "NN", "TO", "VB", "DT", "NN", "IN", "PRP", "MD", "VBG", "DT", "JJ", "NN", "IN", "DT", "NN", "IN", "DT", "JJ", "NN", "IN", "DT", "NN", "."], "head": [2, 0, 2, 11, 11, 11, 11, 11, 11, 11, 2, 13, 2, 15, 13, 19, 19, 19, 15, 22, 22, 19, 25, 25, 19, 29, 29, 29, 25, 32, 32, 25, 2], "deprel": ["nsubj", "root", "obj", "case", "det", "amod", "punct", "amod", "amod", "amod", "obl", "mark", "advcl", "det", "obj", "mark", "nsubj", "aux", "acl", "det", "amod", "obj", "case", "det", "obl", "case", "det", "amod", "nmod", "case", "det", "nmod", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480171723:5-0", "target_tags": "They\\O serve\\O it\\O in\\O a\\O tall\\O ,\\O skinny\\O hour-glass\\O shaped\\O glass\\O to\\O disguise\\O the\\O fact\\O that\\O you\\O a\\O getting\\O a\\O small\\O juice\\B at\\O the\\O price\\O of\\O a\\O half\\O gallon\\O in\\O a\\O supermarket\\O .\\O", "opinion_tags": "They\\O serve\\O it\\O in\\O a\\O tall\\O ,\\O skinny\\O hour-glass\\O shaped\\O glass\\O to\\O disguise\\O the\\O fact\\O that\\O you\\O a\\O getting\\O a\\O small\\B juice\\O at\\O the\\O price\\O of\\O a\\O half\\O gallon\\O in\\O a\\O supermarket\\O .\\O", "sentiment": "negative"}]}, {"id": "en_SnoozeanAMEatery_480171723:6", "sentence": "I should have just asked for the check when I saw that ; but their menu was so unique that I continued .", "postag": ["PRP", "MD", "VB", "RB", "VBN", "IN", "DT", "NN", "WRB", "PRP", "VBD", "DT", ",", "CC", "PRP$", "NN", "VBD", "RB", "JJ", "IN", "PRP", "VBD", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 11, 11, 5, 11, 5, 19, 16, 19, 19, 19, 5, 22, 22, 19, 5], "deprel": ["nsubj", "aux", "aux", "advmod", "root", "case", "det", "obl", "mark", "nsubj", "advcl", "obj", "punct", "cc", "nmod:poss", "nsubj", "cop", "advmod", "conj", "mark", "nsubj", "ccomp", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480171723:6-0", "target_tags": "I\\O should\\O have\\O just\\O asked\\O for\\O the\\O check\\O when\\O I\\O saw\\O that\\O ;\\O but\\O their\\O menu\\B was\\O so\\O unique\\O that\\O I\\O continued\\O .\\O", "opinion_tags": "I\\O should\\O have\\O just\\O asked\\O for\\O the\\O check\\O when\\O I\\O saw\\O that\\O ;\\O but\\O their\\O menu\\O was\\O so\\O unique\\B that\\O I\\O continued\\O .\\O", "sentiment": "positive"}]}, {"id": "en_SnoozeanAMEatery_480171723:7", "sentence": "The pancakes were certainly inventive but $ 8.50 for 3 - 6 '' pancakes ( one of them was more like 5 '' ) in the pancake flight ( sample of 3 different pancakes ) is well over-priced .", "postag": ["DT", "NNS", "VBD", "RB", "JJ", "CC", "$", "CD", "IN", "CD", "SYM", "CD", "NNS", "NNS", "-LRB-", "CD", "IN", "PRP", "VBD", "JJR", "IN", "CD", "''", "-RRB-", "IN", "DT", "NN", "NN", "-LRB-", "NN", "IN", "CD", "JJ", "NNS", "-RRB-", "VBZ", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 38, 38, 7, 14, 14, 12, 10, 14, 7, 20, 20, 18, 16, 20, 5, 22, 20, 20, 20, 28, 28, 28, 38, 30, 38, 34, 34, 34, 30, 30, 38, 38, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "nsubj", "nummod", "case", "nummod", "case", "nmod", "compound", "nmod", "punct", "nsubj", "case", "nmod", "cop", "parataxis", "case", "obl", "punct", "punct", "case", "det", "compound", "obl", "punct", "nsubj", "case", "nummod", "amod", "nmod", "punct", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480171723:7-0", "target_tags": "The\\O pancakes\\B were\\O certainly\\O inventive\\O but\\O $\\O 8.50\\O for\\O 3\\O -\\O 6\\O ''\\O pancakes\\O (\\O one\\O of\\O them\\O was\\O more\\O like\\O 5\\O ''\\O )\\O in\\O the\\O pancake\\O flight\\O (\\O sample\\O of\\O 3\\O different\\O pancakes\\O )\\O is\\O well\\O over-priced\\O .\\O", "opinion_tags": "The\\O pancakes\\O were\\O certainly\\O inventive\\B but\\O $\\O 8.50\\O for\\O 3\\O -\\O 6\\O ''\\O pancakes\\O (\\O one\\O of\\O them\\O was\\O more\\O like\\O 5\\O ''\\O )\\O in\\O the\\O pancake\\O flight\\O (\\O sample\\O of\\O 3\\O different\\O pancakes\\O )\\O is\\O well\\O over-priced\\B .\\O", "sentiment": "negative"}]}, {"id": "en_SnoozeanAMEatery_480171723:8", "sentence": "The pancakes should be larger ( at least 8 '' ) to justify the expense even with the unique offerings .", "postag": ["DT", "NNS", "MD", "VB", "JJR", "-LRB-", "RB", "RBS", "CD", "''", "-RRB-", "TO", "VB", "DT", "NN", "RB", "IN", "DT", "JJ", "NNS", "."], "head": [2, 5, 5, 5, 0, 9, 8, 9, 5, 9, 9, 13, 5, 15, 13, 20, 20, 20, 20, 13, 5], "deprel": ["det", "nsubj", "aux", "cop", "root", "punct", "case", "nmod", "obl:tmod", "punct", "punct", "mark", "advcl", "det", "obj", "advmod", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480171723:8-0", "target_tags": "The\\O pancakes\\B should\\O be\\O larger\\O (\\O at\\O least\\O 8\\O ''\\O )\\O to\\O justify\\O the\\O expense\\O even\\O with\\O the\\O unique\\O offerings\\O .\\O", "opinion_tags": "The\\O pancakes\\O should\\B be\\I larger\\I (\\O at\\O least\\O 8\\O ''\\O )\\O to\\O justify\\O the\\O expense\\O even\\O with\\O the\\O unique\\O offerings\\O .\\O", "sentiment": "negative"}]}, {"id": "en_SnoozeanAMEatery_480171723:13", "sentence": "In the end our check came to $ 27 for 4 small pancakes , a breakfast burrito , an orange juice and an iced tea ( I had water ) .", "postag": ["IN", "DT", "NN", "PRP$", "NN", "VBD", "IN", "$", "CD", "IN", "CD", "JJ", "NNS", ",", "DT", "NN", "NN", ",", "DT", "JJ", "NN", "CC", "DT", "NN", "NN", "-LRB-", "PRP", "VBD", "NN", "-RRB-", "."], "head": [3, 3, 6, 5, 6, 0, 8, 6, 8, 13, 13, 13, 8, 17, 17, 17, 13, 21, 21, 21, 13, 25, 25, 25, 13, 28, 28, 6, 28, 28, 6], "deprel": ["case", "det", "obl", "nmod:poss", "nsubj", "root", "case", "obl", "nummod", "case", "nummod", "amod", "nmod", "punct", "det", "compound", "conj", "punct", "det", "amod", "conj", "cc", "det", "compound", "conj", "punct", "nsubj", "parataxis", "obj", "punct", "punct"], "triples": [{"uid": "en_SnoozeanAMEatery_480171723:13-0", "target_tags": "In\\O the\\O end\\O our\\O check\\O came\\O to\\O $\\O 27\\O for\\O 4\\O small\\O pancakes\\B ,\\O a\\O breakfast\\O burrito\\O ,\\O an\\O orange\\O juice\\O and\\O an\\O iced\\O tea\\O (\\O I\\O had\\O water\\O )\\O .\\O", "opinion_tags": "In\\O the\\O end\\O our\\O check\\O came\\O to\\O $\\O 27\\O for\\O 4\\O small\\B pancakes\\O ,\\O a\\O breakfast\\O burrito\\O ,\\O an\\O orange\\O juice\\O and\\O an\\O iced\\O tea\\O (\\O I\\O had\\O water\\O )\\O .\\O", "sentiment": "negative"}]}, {"id": "en_Ray'sBoathouse_477775918:0", "sentence": "Much more than just a great view !", "postag": ["RB", "JJR", "IN", "RB", "DT", "JJ", "NN", "."], "head": [2, 0, 7, 7, 7, 7, 2, 2], "deprel": ["advmod", "root", "case", "advmod", "det", "amod", "obl", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:0-0", "target_tags": "Much\\O more\\O than\\O just\\O a\\O great\\O view\\B !\\O", "opinion_tags": "Much\\O more\\O than\\O just\\O a\\O great\\B view\\O !\\O", "sentiment": "positive"}]}, {"id": "en_Ray'sBoathouse_477775918:1", "sentence": "– I am exceedingly pleased to report that my dinner at Ray 's Boathouse last Friday completely exceeded my expectations .", "postag": ["NFP", "PRP", "VBP", "RB", "JJ", "TO", "VB", "IN", "PRP$", "NN", "IN", "NNP", "POS", "NNP", "JJ", "NNP", "RB", "VBD", "PRP$", "NNS", "."], "head": [5, 5, 5, 5, 0, 7, 5, 18, 10, 18, 14, 14, 12, 10, 16, 18, 18, 7, 20, 18, 5], "deprel": ["punct", "nsubj", "cop", "advmod", "root", "mark", "xcomp", "mark", "nmod:poss", "nsubj", "case", "nmod:poss", "case", "nmod", "amod", "obl:tmod", "advmod", "ccomp", "nmod:poss", "obj", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:1-0", "target_tags": "–\\O I\\O am\\O exceedingly\\O pleased\\O to\\O report\\O that\\O my\\O dinner\\O at\\O Ray\\B 's\\I Boathouse\\I last\\O Friday\\O completely\\O exceeded\\O my\\O expectations\\O .\\O", "opinion_tags": "–\\O I\\O am\\O exceedingly\\O pleased\\O to\\O report\\O that\\O my\\O dinner\\O at\\O Ray\\O 's\\O Boathouse\\O last\\O Friday\\O completely\\O exceeded\\B my\\I expectations\\I .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775918:2", "sentence": "<PERSON> 's is something of a Seattle institution , but given its gorgeous Sound views , I had suspected that the accolades were more due to the scenery than to the food and service .", "postag": ["NNP", "POS", "VBZ", "NN", "IN", "DT", "NNP", "NN", ",", "CC", "VBN", "PRP$", "JJ", "NN", "NNS", ",", "PRP", "VBD", "VBN", "IN", "DT", "NNS", "VBD", "RBR", "IN", "IN", "DT", "NN", "IN", "IN", "DT", "NN", "CC", "NN", "."], "head": [4, 1, 4, 0, 8, 8, 8, 4, 19, 19, 15, 15, 15, 15, 19, 19, 19, 19, 4, 25, 22, 25, 25, 25, 19, 25, 28, 25, 32, 32, 32, 28, 34, 32, 4], "deprel": ["nsubj", "case", "cop", "root", "case", "det", "compound", "nmod", "punct", "cc", "case", "nmod:poss", "amod", "compound", "obl", "punct", "nsubj", "aux", "conj", "mark", "det", "nsubj", "cop", "advmod", "ccomp", "fixed", "det", "obl", "case", "case", "det", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:2-0", "target_tags": "Ray\\O 's\\O is\\O something\\O of\\O a\\O Seattle\\O institution\\O ,\\O but\\O given\\O its\\O gorgeous\\O Sound\\B views\\I ,\\O I\\O had\\O suspected\\O that\\O the\\O accolades\\O were\\O more\\O due\\O to\\O the\\O scenery\\O than\\O to\\O the\\O food\\O and\\O service\\O .\\O", "opinion_tags": "Ray\\O 's\\O is\\O something\\O of\\O a\\O Seattle\\O institution\\O ,\\O but\\O given\\O its\\O gorgeous\\B Sound\\O views\\O ,\\O I\\O had\\O suspected\\O that\\O the\\O accolades\\O were\\O more\\O due\\O to\\O the\\O scenery\\O than\\O to\\O the\\O food\\O and\\O service\\O .\\O", "sentiment": "positive"}]}, {"id": "en_Ray'sBoathouse_477775918:3", "sentence": "Imagine my happy surprise upon finding that the views are only the third-best thing about <PERSON> 's !", "postag": ["VB", "PRP$", "JJ", "NN", "IN", "VBG", "IN", "DT", "NNS", "VBP", "RB", "DT", "JJ", "NN", "IN", "NNP", "POS", "."], "head": [0, 4, 4, 1, 6, 1, 14, 9, 14, 14, 14, 14, 14, 6, 16, 14, 16, 1], "deprel": ["root", "nmod:poss", "amod", "obj", "mark", "advcl", "mark", "det", "nsubj", "cop", "advmod", "det", "amod", "ccomp", "case", "nmod", "case", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:3-0", "target_tags": "Imagine\\O my\\O happy\\O surprise\\O upon\\O finding\\O that\\O the\\O views\\O are\\O only\\O the\\O third-best\\O thing\\O about\\O Ray\\B 's\\I !\\O", "opinion_tags": "Imagine\\O my\\O happy\\B surprise\\I upon\\O finding\\O that\\O the\\O views\\O are\\O only\\O the\\O third-best\\O thing\\O about\\O Ray\\O 's\\O !\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775918:4", "sentence": "To start things off , our lovely server <PERSON> was quickly on hand to take my drink order .", "postag": ["TO", "VB", "NNS", "RP", ",", "PRP$", "JJ", "NN", "NNP", "VBD", "RB", "IN", "NN", "TO", "VB", "PRP$", "NN", "NN", "."], "head": [2, 13, 2, 2, 2, 8, 8, 13, 8, 13, 13, 13, 0, 15, 13, 18, 18, 15, 13], "deprel": ["mark", "advcl", "obj", "compound:prt", "punct", "nmod:poss", "amod", "nsubj", "appos", "cop", "advmod", "case", "root", "mark", "acl", "nmod:poss", "compound", "obj", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:4-0", "target_tags": "To\\O start\\O things\\O off\\O ,\\O our\\O lovely\\O server\\O Brooke\\B was\\O quickly\\O on\\O hand\\O to\\O take\\O my\\O drink\\O order\\O .\\O", "opinion_tags": "To\\O start\\O things\\O off\\O ,\\O our\\O lovely\\B server\\O Brooke\\O was\\O quickly\\B on\\O hand\\O to\\O take\\O my\\O drink\\O order\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775918:5", "sentence": "My party of two was feeling particularly ambitious , and we splurged on the Shilshole Sampler ... a beautiful assortment of enormous white gulf prawns , smoked albacore tuna , <PERSON> 's fantastic manila clams seasoned with dill , scallops in a tasty soy dressing , and a tiny pile of Dungeness crab atop a sublime butter sauce .", "postag": ["PRP$", "NN", "IN", "CD", "VBD", "VBG", "RB", "JJ", ",", "CC", "PRP", "VBD", "IN", "DT", "NNP", "NNP", ",", "DT", "JJ", "NN", "IN", "JJ", "JJ", "NN", "NNS", ",", "VBD", "NN", "NN", ",", "NNP", "POS", "JJ", "NN", "NNS", "VBN", "IN", "NN", ",", "NNS", "IN", "DT", "JJ", "NN", "NN", ",", "CC", "DT", "JJ", "NN", "IN", "NN", "NN", "IN", "DT", "JJ", "NN", "NN", "."], "head": [2, 6, 4, 2, 6, 0, 8, 6, 12, 12, 12, 6, 16, 16, 16, 12, 20, 20, 20, 16, 25, 25, 25, 25, 20, 27, 20, 29, 27, 35, 35, 31, 35, 35, 20, 35, 38, 36, 40, 38, 45, 45, 45, 45, 40, 50, 50, 50, 50, 16, 53, 53, 50, 58, 58, 58, 58, 50, 6], "deprel": ["nmod:poss", "nsubj", "case", "nmod", "aux", "root", "advmod", "xcomp", "punct", "cc", "nsubj", "conj", "case", "det", "compound", "obl", "punct", "det", "amod", "appos", "case", "amod", "amod", "compound", "nmod", "punct", "conj", "compound", "obj", "punct", "nmod:poss", "case", "amod", "compound", "conj", "acl", "case", "obl", "punct", "conj", "case", "det", "amod", "compound", "nmod", "punct", "cc", "det", "amod", "conj", "case", "compound", "nmod", "case", "det", "amod", "compound", "nmod", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:5-0", "target_tags": "My\\O party\\O of\\O two\\O was\\O feeling\\O particularly\\O ambitious\\O ,\\O and\\O we\\O splurged\\O on\\O the\\O Shilshole\\O Sampler\\O ...\\O a\\O beautiful\\O assortment\\O of\\O enormous\\O white\\B gulf\\I prawns\\I ,\\O smoked\\O albacore\\O tuna\\O ,\\O Ray\\O 's\\O fantastic\\O manila\\O clams\\O seasoned\\O with\\O dill\\O ,\\O scallops\\O in\\O a\\O tasty\\O soy\\O dressing\\O ,\\O and\\O a\\O tiny\\O pile\\O of\\O Dungeness\\O crab\\O atop\\O a\\O sublime\\O butter\\O sauce\\O .\\O", "opinion_tags": "My\\O party\\O of\\O two\\O was\\O feeling\\O particularly\\O ambitious\\O ,\\O and\\O we\\O splurged\\O on\\O the\\O Shilshole\\O Sampler\\O ...\\O a\\O beautiful\\O assortment\\O of\\O enormous\\B white\\O gulf\\O prawns\\O ,\\O smoked\\O albacore\\O tuna\\O ,\\O Ray\\O 's\\O fantastic\\O manila\\O clams\\O seasoned\\O with\\O dill\\O ,\\O scallops\\O in\\O a\\O tasty\\O soy\\O dressing\\O ,\\O and\\O a\\O tiny\\O pile\\O of\\O Dungeness\\O crab\\O atop\\O a\\O sublime\\O butter\\O sauce\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'sBoathouse_477775918:5-1", "target_tags": "My\\O party\\O of\\O two\\O was\\O feeling\\O particularly\\O ambitious\\O ,\\O and\\O we\\O splurged\\O on\\O the\\O Shilshole\\O Sampler\\O ...\\O a\\O beautiful\\O assortment\\O of\\O enormous\\O white\\O gulf\\O prawns\\O ,\\O smoked\\O albacore\\O tuna\\O ,\\O Ray\\O 's\\O fantastic\\O manila\\B clams\\I seasoned\\O with\\O dill\\O ,\\O scallops\\O in\\O a\\O tasty\\O soy\\O dressing\\O ,\\O and\\O a\\O tiny\\O pile\\O of\\O Dungeness\\O crab\\O atop\\O a\\O sublime\\O butter\\O sauce\\O .\\O", "opinion_tags": "My\\O party\\O of\\O two\\O was\\O feeling\\O particularly\\O ambitious\\O ,\\O and\\O we\\O splurged\\O on\\O the\\O Shilshole\\O Sampler\\O ...\\O a\\O beautiful\\O assortment\\O of\\O enormous\\O white\\O gulf\\O prawns\\O ,\\O smoked\\O albacore\\O tuna\\O ,\\O Ray\\O 's\\O fantastic\\B manila\\O clams\\O seasoned\\O with\\O dill\\O ,\\O scallops\\O in\\O a\\O tasty\\O soy\\O dressing\\O ,\\O and\\O a\\O tiny\\O pile\\O of\\O Dungeness\\O crab\\O atop\\O a\\O sublime\\O butter\\O sauce\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'sBoathouse_477775918:5-2", "target_tags": "My\\O party\\O of\\O two\\O was\\O feeling\\O particularly\\O ambitious\\O ,\\O and\\O we\\O splurged\\O on\\O the\\O Shilshole\\O Sampler\\O ...\\O a\\O beautiful\\O assortment\\O of\\O enormous\\O white\\O gulf\\O prawns\\O ,\\O smoked\\O albacore\\O tuna\\O ,\\O Ray\\O 's\\O fantastic\\O manila\\O clams\\O seasoned\\O with\\O dill\\O ,\\O scallops\\O in\\O a\\O tasty\\O soy\\B dressing\\I ,\\O and\\O a\\O tiny\\O pile\\O of\\O Dungeness\\O crab\\O atop\\O a\\O sublime\\O butter\\O sauce\\O .\\O", "opinion_tags": "My\\O party\\O of\\O two\\O was\\O feeling\\O particularly\\O ambitious\\O ,\\O and\\O we\\O splurged\\O on\\O the\\O Shilshole\\O Sampler\\O ...\\O a\\O beautiful\\O assortment\\O of\\O enormous\\O white\\O gulf\\O prawns\\O ,\\O smoked\\O albacore\\O tuna\\O ,\\O Ray\\O 's\\O fantastic\\O manila\\O clams\\O seasoned\\O with\\O dill\\O ,\\O scallops\\O in\\O a\\O tasty\\B soy\\O dressing\\O ,\\O and\\O a\\O tiny\\O pile\\O of\\O Dungeness\\O crab\\O atop\\O a\\O sublime\\O butter\\O sauce\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'sBoathouse_477775918:5-3", "target_tags": "My\\O party\\O of\\O two\\O was\\O feeling\\O particularly\\O ambitious\\O ,\\O and\\O we\\O splurged\\O on\\O the\\O Shilshole\\O Sampler\\O ...\\O a\\O beautiful\\O assortment\\O of\\O enormous\\O white\\O gulf\\O prawns\\O ,\\O smoked\\O albacore\\O tuna\\O ,\\O Ray\\O 's\\O fantastic\\O manila\\O clams\\O seasoned\\O with\\O dill\\O ,\\O scallops\\O in\\O a\\O tasty\\O soy\\O dressing\\O ,\\O and\\O a\\O tiny\\O pile\\O of\\O Dungeness\\O crab\\O atop\\O a\\O sublime\\O butter\\B sauce\\I .\\O", "opinion_tags": "My\\O party\\O of\\O two\\O was\\O feeling\\O particularly\\O ambitious\\O ,\\O and\\O we\\O splurged\\O on\\O the\\O Shilshole\\O Sampler\\O ...\\O a\\O beautiful\\O assortment\\O of\\O enormous\\O white\\O gulf\\O prawns\\O ,\\O smoked\\O albacore\\O tuna\\O ,\\O Ray\\O 's\\O fantastic\\O manila\\O clams\\O seasoned\\O with\\O dill\\O ,\\O scallops\\O in\\O a\\O tasty\\O soy\\O dressing\\O ,\\O and\\O a\\O tiny\\O pile\\O of\\O Dungeness\\O crab\\O atop\\O a\\O sublime\\B butter\\O sauce\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775918:6", "sentence": "For my entr & eacute ; e , I completely enjoyed the seared Alaskan sea scallops complemented by chard , artichoke hearts , fennel , and pecorino toscano .", "postag": ["IN", "PRP$", "NN", "CC", "NN", ",", "NN", ",", "PRP", "RB", "VBD", "DT", "VBN", "JJ", "NN", "NNS", "VBN", "IN", "NN", ",", "NN", "NNS", ",", "NN", ",", "CC", "NN", "NN", "."], "head": [3, 3, 11, 5, 3, 11, 11, 11, 11, 11, 0, 16, 16, 16, 16, 11, 16, 19, 17, 22, 22, 19, 24, 19, 28, 28, 28, 19, 11], "deprel": ["case", "nmod:poss", "obl", "cc", "conj", "punct", "discourse", "punct", "nsubj", "advmod", "root", "det", "amod", "amod", "compound", "obj", "acl", "case", "obl", "punct", "compound", "conj", "punct", "conj", "punct", "cc", "compound", "conj", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:6-0", "target_tags": "For\\O my\\O entr\\O &\\O eacute\\O ;\\O e\\O ,\\O I\\O completely\\O enjoyed\\O the\\O seared\\B Alaskan\\I sea\\I scallops\\I complemented\\O by\\O chard\\O ,\\O artichoke\\O hearts\\O ,\\O fennel\\O ,\\O and\\O pecorino\\O toscano\\O .\\O", "opinion_tags": "For\\O my\\O entr\\O &\\O eacute\\O ;\\O e\\O ,\\O I\\O completely\\O enjoyed\\B the\\O seared\\O Alaskan\\O sea\\O scallops\\O complemented\\O by\\O chard\\O ,\\O artichoke\\O hearts\\O ,\\O fennel\\O ,\\O and\\O pecorino\\O toscano\\O .\\O", "sentiment": "positive"}]}, {"id": "en_Ray'sBoathouse_477775918:7", "sentence": "The scallops are apparently cooked in a black olive butter which really makes them unique ( not to mention tasty ) .", "postag": ["DT", "NNS", "VBP", "RB", "VBN", "IN", "DT", "JJ", "NN", "NN", "WDT", "RB", "VBZ", "PRP", "JJ", "-LRB-", "RB", "TO", "VB", "JJ", "-RRB-", "."], "head": [2, 5, 5, 5, 0, 10, 10, 10, 10, 5, 13, 13, 10, 13, 13, 19, 19, 19, 13, 19, 19, 5], "deprel": ["det", "nsubj:pass", "aux:pass", "advmod", "root", "case", "det", "amod", "compound", "obl", "nsubj", "advmod", "acl:relcl", "obj", "xcomp", "punct", "advmod", "mark", "advcl", "xcomp", "punct", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:7-0", "target_tags": "The\\O scallops\\B are\\O apparently\\O cooked\\O in\\O a\\O black\\O olive\\O butter\\O which\\O really\\O makes\\O them\\O unique\\O (\\O not\\O to\\O mention\\O tasty\\O )\\O .\\O", "opinion_tags": "The\\O scallops\\O are\\O apparently\\O cooked\\O in\\O a\\O black\\O olive\\O butter\\O which\\O really\\O makes\\O them\\O unique\\B (\\O not\\O to\\O mention\\O tasty\\B )\\O .\\O", "sentiment": "positive"}]}, {"id": "en_Ray'sBoathouse_477775918:8", "sentence": "My friend enjoyed the grilled Alaskan King Salmon with delectable creamed Washington russet potatoes and crisp green beans .", "postag": ["PRP$", "NN", "VBD", "DT", "VBN", "JJ", "NNP", "NN", "IN", "JJ", "VBN", "NNP", "JJ", "NNS", "CC", "JJ", "JJ", "NNS", "."], "head": [2, 3, 0, 8, 8, 8, 8, 3, 14, 14, 14, 14, 14, 8, 18, 18, 18, 14, 3], "deprel": ["nmod:poss", "nsubj", "root", "det", "amod", "amod", "compound", "obj", "case", "amod", "amod", "compound", "amod", "nmod", "cc", "amod", "amod", "conj", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:8-0", "target_tags": "My\\O friend\\O enjoyed\\O the\\O grilled\\B Alaskan\\I King\\I Salmon\\I with\\O delectable\\O creamed\\O Washington\\O russet\\O potatoes\\O and\\O crisp\\O green\\O beans\\O .\\O", "opinion_tags": "My\\O friend\\O enjoyed\\B the\\O grilled\\O Alaskan\\O King\\O Salmon\\O with\\O delectable\\O creamed\\O Washington\\O russet\\O potatoes\\O and\\O crisp\\O green\\O beans\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'sBoathouse_477775918:8-1", "target_tags": "My\\O friend\\O enjoyed\\O the\\O grilled\\O Alaskan\\O King\\O Salmon\\O with\\O delectable\\O creamed\\B Washington\\I russet\\I potatoes\\I and\\O crisp\\O green\\O beans\\O .\\O", "opinion_tags": "My\\O friend\\O enjoyed\\O the\\O grilled\\O Alaskan\\O King\\O Salmon\\O with\\O delectable\\B creamed\\O Washington\\O russet\\O potatoes\\O and\\O crisp\\O green\\O beans\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'sBoathouse_477775918:8-2", "target_tags": "My\\O friend\\O enjoyed\\O the\\O grilled\\O Alaskan\\O King\\O Salmon\\O with\\O delectable\\O creamed\\O Washington\\O russet\\O potatoes\\O and\\O crisp\\O green\\B beans\\I .\\O", "opinion_tags": "My\\O friend\\O enjoyed\\O the\\O grilled\\O Alaskan\\O King\\O Salmon\\O with\\O delectable\\O creamed\\O Washington\\O russet\\O potatoes\\O and\\O crisp\\B green\\O beans\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775918:9", "sentence": "I had a taste of all three items on her plate , and they were superb .", "postag": ["PRP", "VBD", "DT", "NN", "IN", "DT", "CD", "NNS", "IN", "PRP$", "NN", ",", "CC", "PRP", "VBD", "JJ", "."], "head": [2, 0, 4, 2, 8, 8, 8, 4, 11, 11, 2, 16, 16, 16, 16, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "case", "det", "nummod", "nmod", "case", "nmod:poss", "obl", "punct", "cc", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:9-0", "target_tags": "I\\O had\\O a\\O taste\\O of\\O all\\O three\\O items\\O on\\O her\\O plate\\B ,\\O and\\O they\\O were\\O superb\\O .\\O", "opinion_tags": "I\\O had\\O a\\O taste\\O of\\O all\\O three\\O items\\O on\\O her\\O plate\\O ,\\O and\\O they\\O were\\O superb\\B .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775918:10", "sentence": "Our server continued to be attentive throughout the night , but I did remain puzzled by one issue : Who thinks that Ray 's is an appropriate place to take young children for dinner ?", "postag": ["PRP$", "NN", "VBD", "TO", "VB", "JJ", "IN", "DT", "NN", ",", "CC", "PRP", "VBD", "VB", "VBN", "IN", "CD", "NN", ":", "WP", "VBZ", "IN", "NNP", "POS", "VBZ", "DT", "JJ", "NN", "TO", "VB", "JJ", "NNS", "IN", "NN", "."], "head": [2, 3, 0, 6, 6, 3, 9, 9, 6, 14, 14, 14, 14, 3, 14, 18, 18, 15, 21, 21, 18, 28, 28, 23, 28, 28, 28, 21, 30, 28, 32, 30, 34, 30, 3], "deprel": ["nmod:poss", "nsubj", "root", "mark", "cop", "xcomp", "case", "det", "obl", "punct", "cc", "nsubj", "aux", "conj", "xcomp", "case", "nummod", "obl", "punct", "nsubj", "acl:relcl", "mark", "nsubj", "case", "cop", "det", "amod", "ccomp", "mark", "acl", "amod", "obj", "case", "obl", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:10-0", "target_tags": "Our\\O server\\B continued\\O to\\O be\\O attentive\\O throughout\\O the\\O night\\O ,\\O but\\O I\\O did\\O remain\\O puzzled\\O by\\O one\\O issue\\O :\\O Who\\O thinks\\O that\\O Ray\\O 's\\O is\\O an\\O appropriate\\O place\\O to\\O take\\O young\\O children\\O for\\O dinner\\O ?\\O", "opinion_tags": "Our\\O server\\O continued\\O to\\O be\\O attentive\\B throughout\\O the\\O night\\O ,\\O but\\O I\\O did\\O remain\\O puzzled\\O by\\O one\\O issue\\O :\\O Who\\O thinks\\O that\\O Ray\\O 's\\O is\\O an\\O appropriate\\O place\\O to\\O take\\O young\\O children\\O for\\O dinner\\O ?\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'sBoathouse_477775918:10-1", "target_tags": "Our\\O server\\O continued\\O to\\O be\\O attentive\\O throughout\\O the\\O night\\O ,\\O but\\O I\\O did\\O remain\\O puzzled\\O by\\O one\\O issue\\O :\\O Who\\O thinks\\O that\\O Ray\\B 's\\I is\\O an\\O appropriate\\O place\\O to\\O take\\O young\\O children\\O for\\O dinner\\O ?\\O", "opinion_tags": "Our\\O server\\O continued\\O to\\O be\\O attentive\\O throughout\\O the\\O night\\O ,\\O but\\O I\\O did\\O remain\\O puzzled\\O by\\O one\\O issue\\O :\\O Who\\O thinks\\O that\\O Ray\\O 's\\O is\\O an\\O appropriate\\B place\\O to\\O take\\O young\\O children\\O for\\O dinner\\O ?\\O", "sentiment": "neutral"}]}, {"id": "en_Ray'sBoathouse_477775918:14", "sentence": "While I could have done without the youth who shared the evening with us , our wonderful server and food made the experience a very positive one .", "postag": ["IN", "PRP", "MD", "VB", "VBN", "IN", "DT", "NN", "WP", "VBD", "DT", "NN", "IN", "PRP", ",", "PRP$", "JJ", "NN", "CC", "NN", "VBD", "DT", "NN", "DT", "RB", "JJ", "NN", "."], "head": [5, 5, 5, 5, 21, 8, 8, 5, 10, 8, 12, 10, 14, 10, 21, 18, 18, 21, 20, 18, 0, 23, 21, 27, 26, 27, 21, 21], "deprel": ["mark", "nsubj", "aux", "aux", "advcl", "case", "det", "obl", "nsubj", "acl:relcl", "det", "obj", "case", "obl", "punct", "nmod:poss", "amod", "nsubj", "cc", "conj", "root", "det", "obj", "det", "advmod", "amod", "xcomp", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:14-0", "target_tags": "While\\O I\\O could\\O have\\O done\\O without\\O the\\O youth\\O who\\O shared\\O the\\O evening\\O with\\O us\\O ,\\O our\\O wonderful\\O server\\B and\\O food\\O made\\O the\\O experience\\O a\\O very\\O positive\\O one\\O .\\O", "opinion_tags": "While\\O I\\O could\\O have\\O done\\O without\\O the\\O youth\\O who\\O shared\\O the\\O evening\\O with\\O us\\O ,\\O our\\O wonderful\\B server\\O and\\O food\\O made\\O the\\O experience\\O a\\O very\\O positive\\O one\\O .\\O", "sentiment": "positive"}, {"uid": "en_<PERSON>'sBoathouse_477775918:14-1", "target_tags": "While\\O I\\O could\\O have\\O done\\O without\\O the\\O youth\\O who\\O shared\\O the\\O evening\\O with\\O us\\O ,\\O our\\O wonderful\\O server\\O and\\O food\\B made\\O the\\O experience\\O a\\O very\\O positive\\O one\\O .\\O", "opinion_tags": "While\\O I\\O could\\O have\\O done\\O without\\O the\\O youth\\O who\\O shared\\O the\\O evening\\O with\\O us\\O ,\\O our\\O wonderful\\B server\\O and\\O food\\O made\\O the\\O experience\\O a\\O very\\O positive\\O one\\O .\\O", "sentiment": "positive"}]}, {"id": "en_<PERSON>'sBoathouse_477775918:15", "sentence": "Oh yeah ... the view was good , too .", "postag": ["UH", "UH", ",", "DT", "NN", "VBD", "JJ", ",", "RB", "."], "head": [7, 7, 7, 5, 7, 7, 0, 7, 7, 7], "deprel": ["discourse", "discourse", "punct", "det", "nsubj", "cop", "root", "punct", "advmod", "punct"], "triples": [{"uid": "en_<PERSON>'sBoathouse_477775918:15-0", "target_tags": "Oh\\O yeah\\O ...\\O the\\O view\\B was\\O good\\O ,\\O too\\O .\\O", "opinion_tags": "Oh\\O yeah\\O ...\\O the\\O view\\O was\\O good\\B ,\\O too\\O .\\O", "sentiment": "positive"}]}]