{"nodes": [{"id": "prepare_vocab", "type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {}}, {"id": "VocabHelp", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {}}, {"id": "VocabHelp.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"arguments": ["self", "counter", "specials"], "in_class": "VocabHelp", "is_constructor": true}}, {"id": "VocabHelp.counter", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_class": "VocabHelp", "in_function": "VocabHelp.__init__"}}, {"id": "VocabHelp.words_and_frequencies", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_class": "VocabHelp", "in_function": "VocabHelp.__init__"}}, {"id": "VocabHelp.__eq__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"arguments": ["self", "other"], "in_class": "VocabHelp"}}, {"id": "VocabHelp.__len__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"arguments": ["self"], "in_class": "VocabHelp"}}, {"id": "VocabHelp.extend", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"arguments": ["self", "v"], "in_class": "VocabHelp"}}, {"id": "VocabHelp.words", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_class": "VocabHelp", "in_function": "VocabHelp.extend"}}, {"id": "VocabHelp.load_vocab", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"arguments": ["vocab_path"], "in_class": "VocabHelp"}}, {"id": "VocabHelp.save_vocab", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"arguments": ["self", "vocab_path"], "in_class": "VocabHelp"}}, {"id": "parse_args", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"arguments": []}}, {"id": "parser", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {}}, {"id": "args", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {}}, {"id": "main", "type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {}}, {"id": "train_file", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "dev_file", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "test_file", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "vocab_tok_file", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "vocab_post_file", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "vocab_deprel_file", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "vocab_postag_file", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "vocab_synpost_file", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "token_counter", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "deprel_counter", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "postag_counter", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "postag_ca_counter", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "max_len", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "load_tokens"}}, {"id": "post_counter", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "syn_post_counter", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "token_vocab", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "post_vocab", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "test"}}, {"id": "deprel_vocab", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "test"}}, {"id": "postag_vocab", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "test"}}, {"id": "syn_post_vocab", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "main"}}, {"id": "load_tokens", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"arguments": ["filename"]}}, {"id": "data", "type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {}}, {"id": "tokens", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "load_tokens"}}, {"id": "deprel", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "load_tokens"}}, {"id": "postag", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "load_tokens"}}, {"id": "postag_ca", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "load_tokens"}}, {"id": "sentence", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "load_tokens"}}, {"id": "n", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "load_tokens"}}, {"id": "tmp_pos", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "load_tokens"}}, {"id": "tup", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "metadata": {"in_function": "load_tokens"}}, {"id": "sentiment2id", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {}}, {"id": "label", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_function": "get_opinions"}}, {"id": "get_spans", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"arguments": ["tags"]}}, {"id": "tags", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_function": "get_spans"}}, {"id": "length", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_function": "get_spans"}}, {"id": "spans", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_function": "get_opinions"}}, {"id": "start", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_function": "get_evaluate_spans"}}, {"id": "get_evaluate_spans", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"arguments": ["tags", "length", "token_range"]}}, {"id": "Instance", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {}}, {"id": "Instance.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"arguments": ["self", "tokenizer", "sentence_pack", "post_vocab", "deprel_vocab", "postag_vocab", "synpost_vocab", "args"], "in_class": "Instance", "is_constructor": true}}, {"id": "Instance.token_start", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.token_end", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.aspect", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.opinion", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.aspect_span", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.opinion_span", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.start", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.end", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.set_tag", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.tmp", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.j", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.tmp_dict", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.word_level_degree", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "Instance.node_set", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}, {"id": "load_data_instances", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"arguments": ["sentence_packs", "post_vocab", "deprel_vocab", "postag_vocab", "synpost_vocab", "args"]}}, {"id": "instances", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "test"}}, {"id": "tokenizer", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_function": "load_data_instances"}}, {"id": "DataIterator", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {}}, {"id": "DataIterator.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"arguments": ["self", "instances", "args"], "in_class": "DataIterator", "is_constructor": true}}, {"id": "DataIterator.get_batch", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"arguments": ["self", "index"], "in_class": "DataIterator"}}, {"id": "DataIterator.sentence_ids", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.sentences", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.sens_lens", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.token_ranges", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.bert_tokens", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.lengths", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.masks", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.aspect_tags", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.opinion_tags", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.tags", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.tags_symmetry", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.word_pair_position", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.word_pair_deprel", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.word_pair_pos", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "DataIterator.word_pair_synpost", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}, {"id": "get_bert_optimizer", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"arguments": ["model", "args"]}}, {"id": "no_decay", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "get_bert_optimizer"}}, {"id": "diff_part", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "get_bert_optimizer"}}, {"id": "optimizer_grouped_parameters", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "get_bert_optimizer"}}, {"id": "optimizer", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "train", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"arguments": ["args"]}}, {"id": "train_sentence_packs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "dev_sentence_packs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "synpost_vocab", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "test"}}, {"id": "instances_train", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "instances_dev", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "trainset", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "devset", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "model", "type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {}}, {"id": "weight", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "best_joint_f1", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "best_joint_epoch", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "tags_flatten", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "tags_symmetry_flatten", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "predictions", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "l_ba", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "l_rpd", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "l_dep", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "l_psc", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "l_tbd", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "l_p", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "loss", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "preds", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "eval"}}, {"id": "preds_flatten", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "train"}}, {"id": "model_path", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "test"}}, {"id": "eval", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"arguments": ["model", "dataset", "args", "FLAG"]}}, {"id": "all_ids", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "eval"}}, {"id": "all_sentences", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "eval"}}, {"id": "all_preds", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "eval"}}, {"id": "all_labels", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "eval"}}, {"id": "all_lengths", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "eval"}}, {"id": "all_sens_lengths", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "eval"}}, {"id": "all_token_ranges", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "eval"}}, {"id": "metric", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "eval"}}, {"id": "aspect_results", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "eval"}}, {"id": "opinion_results", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "eval"}}, {"id": "test", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"arguments": ["args"]}}, {"id": "sentence_packs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "test"}}, {"id": "testset", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "metadata": {"in_function": "test"}}, {"id": "utils", "type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {}}, {"id": "get_aspects", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"arguments": ["tags", "length", "token_range", "ignore_index"]}}, {"id": "end", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_function": "get_opinions"}}, {"id": "get_opinions", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"arguments": ["tags", "length", "token_range", "ignore_index"]}}, {"id": "Metric", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {}}, {"id": "Metric.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"arguments": ["self", "args", "predictions", "goldens", "bert_lengths", "sen_lengths", "tokens_ranges", "ignore_index"], "in_class": "Metric", "is_constructor": true}}, {"id": "Metric.get_spans", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"arguments": ["self", "tags", "length", "token_range", "type"], "in_class": "Metric"}}, {"id": "Metric.spans", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.get_spans"}}, {"id": "Metric.start", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.get_spans"}}, {"id": "Metric.find_pair", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"arguments": ["self", "tags", "aspect_spans", "opinion_spans", "token_ranges"], "in_class": "Metric"}}, {"id": "Metric.pairs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.find_pair"}}, {"id": "Metric.tag_num", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.find_triplet"}}, {"id": "Metric.a_start", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.find_triplet"}}, {"id": "Metric.o_start", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.find_triplet"}}, {"id": "Metric.sentiment", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.find_triplet"}}, {"id": "Metric.find_triplet", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"arguments": ["self", "tags", "aspect_spans", "opinion_spans", "token_ranges"], "in_class": "Metric"}}, {"id": "Metric.triplets_utm", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.find_triplet"}}, {"id": "Metric.score_aspect", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"arguments": ["self"], "in_class": "Metric"}}, {"id": "Metric.golden_set", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.predicted_set", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.golden_aspect_spans", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.predicted_aspect_spans", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.correct_num", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.precision", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.recall", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.f1", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.score_opinion", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"arguments": ["self"], "in_class": "Metric"}}, {"id": "Metric.golden_opinion_spans", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.predicted_opinion_spans", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.score_uniontags", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"arguments": ["self"], "in_class": "Metric"}}, {"id": "Metric.golden_tuples", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.predicted_tuples", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.score_uniontags_print", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"arguments": ["self"], "in_class": "Metric"}}, {"id": "Metric.all_golden_triplets", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.all_predicted_triplets", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}, {"id": "Metric.tagReport", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"arguments": ["self"], "in_class": "Metric"}}, {"id": "Metric.golden_tags", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.tagReport"}}, {"id": "Metric.predict_tags", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.tagReport"}}, {"id": "Metric.target_names", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "metadata": {"in_class": "Metric", "in_function": "Metric.tagReport"}}, {"id": "LayerNorm", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {}}, {"id": "LayerNorm.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"arguments": ["self", "features", "eps"], "in_class": "LayerNorm", "is_constructor": true}}, {"id": "LayerNorm.forward", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"arguments": ["self", "x"], "in_class": "LayerNorm"}}, {"id": "LayerNorm.mean", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "LayerNorm", "in_function": "LayerNorm.forward"}}, {"id": "LayerNorm.std", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "LayerNorm", "in_function": "LayerNorm.forward"}}, {"id": "RefiningStrategy", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {}}, {"id": "RefiningStrategy.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"arguments": ["self", "hidden_dim", "edge_dim", "dim_e", "dropout_ratio"], "in_class": "RefiningStrategy", "is_constructor": true}}, {"id": "RefiningStrategy.forward", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"arguments": ["self", "edge", "node1", "node2"], "in_class": "RefiningStrategy"}}, {"id": "RefiningStrategy.node", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "RefiningStrategy", "in_function": "RefiningStrategy.forward"}}, {"id": "RefiningStrategy.edge_diag", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "RefiningStrategy", "in_function": "RefiningStrategy.forward"}}, {"id": "RefiningStrategy.edge_i", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "RefiningStrategy", "in_function": "RefiningStrategy.forward"}}, {"id": "RefiningStrategy.edge_j", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "RefiningStrategy", "in_function": "RefiningStrategy.forward"}}, {"id": "RefiningStrategy.edge", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "RefiningStrategy", "in_function": "RefiningStrategy.forward"}}, {"id": "GraphConvLayer", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {}}, {"id": "GraphConvLayer.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"arguments": ["self", "device", "gcn_dim", "edge_dim", "dep_embed_dim", "pooling"], "in_class": "GraphConvLayer", "is_constructor": true}}, {"id": "GraphConvLayer.forward", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"arguments": ["self", "weight_prob_softmax", "weight_adj", "gcn_inputs", "self_loop"], "in_class": "GraphConvLayer"}}, {"id": "GraphConvLayer.weight_prob_softmax", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}, {"id": "GraphConvLayer.gcn_inputs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}, {"id": "GraphConvLayer.Ax", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}, {"id": "GraphConvLayer.gcn_outputs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}, {"id": "GraphConvLayer.weights_gcn_outputs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}, {"id": "GraphConvLayer.node_outputs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}, {"id": "GraphConvLayer.node_outputs1", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}, {"id": "GraphConvLayer.node_outputs2", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}, {"id": "GraphConvLayer.edge_outputs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}, {"id": "Biaffine", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {}}, {"id": "Biaffine.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"arguments": ["self", "args", "in1_features", "in2_features", "out_features", "bias"], "in_class": "Biaffine", "is_constructor": true}}, {"id": "Biaffine.forward", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"arguments": ["self", "input1", "input2"], "in_class": "Biaffine"}}, {"id": "Biaffine.ones", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "Biaffine", "in_function": "Biaffine.forward"}}, {"id": "Biaffine.input1", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "Biaffine", "in_function": "Biaffine.forward"}}, {"id": "Biaffine.input2", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "Biaffine", "in_function": "Biaffine.forward"}}, {"id": "Biaffine.affine", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "Biaffine", "in_function": "Biaffine.forward"}}, {"id": "Biaffine.biaffine", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "Biaffine", "in_function": "Biaffine.forward"}}, {"id": "EMCGCN", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {}}, {"id": "EMCGCN.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"arguments": ["self", "args"], "in_class": "EMCGCN", "is_constructor": true}}, {"id": "EMCGCN.forward", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"arguments": ["self", "tokens", "masks", "word_pair_position", "word_pair_deprel", "word_pair_pos", "word_pair_synpost"], "in_class": "EMCGCN"}}, {"id": "EMCGCN.bert_feature", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.tensor_masks", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.word_pair_post_emb", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.word_pair_deprel_emb", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.word_pair_postag_emb", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.word_pair_synpost_emb", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.ap_node", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.op_node", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.biaffine_edge", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.gcn_input", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.gcn_outputs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.weight_prob_list", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.biaffine_edge_softmax", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.word_pair_post_emb_softmax", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.word_pair_deprel_emb_softmax", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.word_pair_postag_emb_softmax", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.word_pair_synpost_emb_softmax", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.self_loop", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.weight_prob", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}, {"id": "EMCGCN.weight_prob_softmax", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}], "edges": [{"source": "main", "target": "data", "type": "imports_from", "metadata": {"imported_name": "load_data_instances", "line": 11}}, {"source": "main", "target": "data", "type": "imports_from", "metadata": {"imported_name": "DataIterator", "line": 11}}, {"source": "main", "target": "data", "type": "imports_from", "metadata": {"imported_name": "label2id", "line": 11}}, {"source": "main", "target": "model", "type": "imports_from", "metadata": {"imported_name": "EMCGCN", "line": 12}}, {"source": "main", "target": "utils", "type": "imports", "metadata": {"line": 13}}, {"source": "main", "target": "prepare_vocab", "type": "imports_from", "metadata": {"imported_name": "VocabHelp", "line": 17}}, {"source": "utils", "target": "data", "type": "imports_from", "metadata": {"imported_name": "label2id", "line": 3}}, {"source": "utils", "target": "data", "type": "imports_from", "metadata": {"imported_name": "id2label", "line": 3}}, {"source": "VocabHelp", "target": "VocabHelp.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "VocabHelp", "target": "VocabHelp.counter", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "VocabHelp", "target": "VocabHelp.words_and_frequencies", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "VocabHelp", "target": "VocabHelp.__eq__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "VocabHelp", "target": "VocabHelp.__len__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "VocabHelp", "target": "VocabHelp.extend", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "VocabHelp", "target": "VocabHelp.words", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "VocabHelp", "target": "VocabHelp.load_vocab", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "VocabHelp", "target": "VocabHelp.save_vocab", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Instance", "target": "Instance.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Instance", "target": "Instance.token_start", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.token_end", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.aspect", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.opinion", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.aspect_span", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.opinion_span", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.start", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.end", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.set_tag", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.tmp", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.j", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.tmp_dict", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.word_level_degree", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Instance", "target": "Instance.node_set", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DataIterator", "target": "DataIterator.get_batch", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DataIterator", "target": "DataIterator.sentence_ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.sentences", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.sens_lens", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.token_ranges", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.bert_tokens", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.lengths", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.masks", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.aspect_tags", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.opinion_tags", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.tags", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.tags_symmetry", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.word_pair_position", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.word_pair_deprel", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.word_pair_pos", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DataIterator", "target": "DataIterator.word_pair_synpost", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "LayerNorm", "target": "LayerNorm.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "LayerNorm", "target": "LayerNorm.forward", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "LayerNorm", "target": "LayerNorm.mean", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "LayerNorm", "target": "LayerNorm.std", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "RefiningStrategy", "target": "RefiningStrategy.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "RefiningStrategy", "target": "RefiningStrategy.forward", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "RefiningStrategy", "target": "RefiningStrategy.node", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "RefiningStrategy", "target": "RefiningStrategy.edge_diag", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "RefiningStrategy", "target": "RefiningStrategy.edge_i", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "RefiningStrategy", "target": "RefiningStrategy.edge_j", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "RefiningStrategy", "target": "RefiningStrategy.edge", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GraphConvLayer", "target": "GraphConvLayer.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "GraphConvLayer", "target": "GraphConvLayer.forward", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "GraphConvLayer", "target": "GraphConvLayer.weight_prob_softmax", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GraphConvLayer", "target": "GraphConvLayer.gcn_inputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GraphConvLayer", "target": "GraphConvLayer.Ax", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GraphConvLayer", "target": "GraphConvLayer.gcn_outputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GraphConvLayer", "target": "GraphConvLayer.weights_gcn_outputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GraphConvLayer", "target": "GraphConvLayer.node_outputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GraphConvLayer", "target": "GraphConvLayer.node_outputs1", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GraphConvLayer", "target": "GraphConvLayer.node_outputs2", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GraphConvLayer", "target": "GraphConvLayer.edge_outputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Biaffine", "target": "Biaffine.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Biaffine", "target": "Biaffine.forward", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Biaffine", "target": "Biaffine.ones", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Biaffine", "target": "Biaffine.input1", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Biaffine", "target": "Biaffine.input2", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Biaffine", "target": "Biaffine.affine", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Biaffine", "target": "Biaffine.biaffine", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "EMCGCN", "target": "EMCGCN.forward", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "EMCGCN", "target": "EMCGCN.bert_feature", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.tensor_masks", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.word_pair_post_emb", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.word_pair_deprel_emb", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.word_pair_postag_emb", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.word_pair_synpost_emb", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.ap_node", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.op_node", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.biaffine_edge", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.gcn_input", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.gcn_outputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.weight_prob_list", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.biaffine_edge_softmax", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.word_pair_post_emb_softmax", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.word_pair_deprel_emb_softmax", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.word_pair_postag_emb_softmax", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.word_pair_synpost_emb_softmax", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.self_loop", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.weight_prob", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EMCGCN", "target": "EMCGCN.weight_prob_softmax", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "load_tokens", "target": "max_len", "type": "uses_variable", "metadata": {}}, {"source": "load_tokens", "target": "tokens", "type": "uses_variable", "metadata": {}}, {"source": "load_tokens", "target": "deprel", "type": "uses_variable", "metadata": {}}, {"source": "load_tokens", "target": "postag", "type": "uses_variable", "metadata": {}}, {"source": "load_tokens", "target": "postag_ca", "type": "uses_variable", "metadata": {}}, {"source": "load_tokens", "target": "sentence", "type": "uses_variable", "metadata": {}}, {"source": "load_tokens", "target": "n", "type": "uses_variable", "metadata": {}}, {"source": "load_tokens", "target": "tmp_pos", "type": "uses_variable", "metadata": {}}, {"source": "load_tokens", "target": "tup", "type": "uses_variable", "metadata": {}}, {"source": "load_tokens", "target": "Instance.j", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "get_spans", "type": "calls", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.__init__", "target": "VocabHelp.words", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "post_vocab", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "deprel_vocab", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "postag_vocab", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "tokens", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "deprel", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "postag", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "sentence", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "DataIterator.tags", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "length", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Metric.start", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance.token_start", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance.token_end", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance.aspect", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance.opinion", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance.aspect_span", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance.opinion_span", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "end", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance.set_tag", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance.tmp", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance.j", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance.tmp_dict", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance.word_level_degree", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Instance.node_set", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "DataIterator.bert_tokens", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "DataIterator.aspect_tags", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "DataIterator.opinion_tags", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "DataIterator.tags_symmetry", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "DataIterator.word_pair_position", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "DataIterator.word_pair_deprel", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "DataIterator.word_pair_pos", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "DataIterator.word_pair_synpost", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "synpost_vocab", "type": "uses_variable", "metadata": {}}, {"source": "Instance.__init__", "target": "Metric.sentiment", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "eval", "type": "calls", "metadata": {}}, {"source": "train", "target": "get_bert_optimizer", "type": "calls", "metadata": {}}, {"source": "train", "target": "load_data_instances", "type": "calls", "metadata": {}}, {"source": "train", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "post_vocab", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "deprel_vocab", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "postag_vocab", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "tokens", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "postag", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "n", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "label", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "DataIterator.tags", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "Instance.j", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "DataIterator.sentences", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "DataIterator.lengths", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "DataIterator.masks", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "DataIterator.aspect_tags", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "DataIterator.tags_symmetry", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "DataIterator.word_pair_position", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "DataIterator.word_pair_deprel", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "DataIterator.word_pair_pos", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "DataIterator.word_pair_synpost", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "optimizer", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "train_sentence_packs", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "dev_sentence_packs", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "synpost_vocab", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "instances_train", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "instances_dev", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "trainset", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "devset", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "weight", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "best_joint_f1", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "best_joint_epoch", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "tags_flatten", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "tags_symmetry_flatten", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "predictions", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "l_ba", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "l_rpd", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "l_dep", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "l_psc", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "l_tbd", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "l_p", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "loss", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "preds", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "preds_flatten", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "model_path", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "Metric.f1", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "train", "type": "calls", "metadata": {}}, {"source": "eval", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "tokens", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "n", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.tags", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.sentence_ids", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.sentences", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.sens_lens", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.token_ranges", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.lengths", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.masks", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.aspect_tags", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.tags_symmetry", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.word_pair_position", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.word_pair_deprel", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.word_pair_pos", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DataIterator.word_pair_synpost", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "preds", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "all_ids", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "all_sentences", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "all_preds", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "all_labels", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "all_lengths", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "all_sens_lengths", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "all_token_ranges", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "metric", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "aspect_results", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "opinion_results", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "Metric.precision", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "Metric.recall", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "Metric.f1", "type": "uses_variable", "metadata": {}}, {"source": "test", "target": "eval", "type": "calls", "metadata": {}}, {"source": "test", "target": "load_data_instances", "type": "calls", "metadata": {}}, {"source": "test", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "test", "target": "post_vocab", "type": "uses_variable", "metadata": {}}, {"source": "test", "target": "deprel_vocab", "type": "uses_variable", "metadata": {}}, {"source": "test", "target": "postag_vocab", "type": "uses_variable", "metadata": {}}, {"source": "test", "target": "instances", "type": "uses_variable", "metadata": {}}, {"source": "test", "target": "synpost_vocab", "type": "uses_variable", "metadata": {}}, {"source": "test", "target": "model_path", "type": "uses_variable", "metadata": {}}, {"source": "test", "target": "sentence_packs", "type": "uses_variable", "metadata": {}}, {"source": "test", "target": "testset", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_aspect", "target": "get_aspects", "type": "calls", "metadata": {}}, {"source": "Metric.score_aspect", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.score_aspect", "target": "Metric.spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_aspect", "target": "predictions", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_aspect", "target": "Metric.golden_set", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_aspect", "target": "Metric.predicted_set", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_aspect", "target": "Metric.golden_aspect_spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_aspect", "target": "Metric.predicted_aspect_spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_aspect", "target": "Metric.correct_num", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_aspect", "target": "Metric.precision", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_aspect", "target": "Metric.recall", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_aspect", "target": "Metric.f1", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_aspect", "target": "Metric.score_opinion", "type": "similar_to", "metadata": {"similarity": 0.7894736842105263}}, {"source": "Metric.score_aspect", "target": "Metric.score_uniontags", "type": "similar_to", "metadata": {"similarity": 0.6808510638297872}}, {"source": "Metric.score_aspect", "target": "Metric.score_uniontags_print", "type": "similar_to", "metadata": {"similarity": 0.64}}, {"source": "Metric.score_opinion", "target": "get_opinions", "type": "calls", "metadata": {}}, {"source": "Metric.score_opinion", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.score_opinion", "target": "Metric.spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_opinion", "target": "predictions", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_opinion", "target": "Metric.golden_set", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_opinion", "target": "Metric.predicted_set", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_opinion", "target": "Metric.correct_num", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_opinion", "target": "Metric.precision", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_opinion", "target": "Metric.recall", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_opinion", "target": "Metric.f1", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_opinion", "target": "Metric.golden_opinion_spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_opinion", "target": "Metric.predicted_opinion_spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_opinion", "target": "Metric.score_aspect", "type": "similar_to", "metadata": {"similarity": 0.7894736842105263}}, {"source": "Metric.score_opinion", "target": "Metric.score_uniontags", "type": "similar_to", "metadata": {"similarity": 0.6808510638297872}}, {"source": "Metric.score_opinion", "target": "Metric.score_uniontags_print", "type": "similar_to", "metadata": {"similarity": 0.64}}, {"source": "Metric.score_uniontags", "target": "get_aspects", "type": "calls", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "get_opinions", "type": "calls", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "predictions", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.golden_set", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.predicted_set", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.golden_aspect_spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.predicted_aspect_spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.correct_num", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.precision", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.recall", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.f1", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.golden_opinion_spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.predicted_opinion_spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.golden_tuples", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.predicted_tuples", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags", "target": "Metric.score_aspect", "type": "similar_to", "metadata": {"similarity": 0.6808510638297872}}, {"source": "Metric.score_uniontags", "target": "Metric.score_opinion", "type": "similar_to", "metadata": {"similarity": 0.6808510638297872}}, {"source": "Metric.score_uniontags", "target": "Metric.score_uniontags_print", "type": "similar_to", "metadata": {"similarity": 0.8979591836734694}}, {"source": "Metric.score_uniontags_print", "target": "get_aspects", "type": "calls", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "get_opinions", "type": "calls", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "predictions", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.golden_set", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.predicted_set", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.golden_aspect_spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.predicted_aspect_spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.correct_num", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.precision", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.recall", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.f1", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.golden_opinion_spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.predicted_opinion_spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.golden_tuples", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.predicted_tuples", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.all_golden_triplets", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.all_predicted_triplets", "type": "uses_variable", "metadata": {}}, {"source": "Metric.score_uniontags_print", "target": "Metric.score_aspect", "type": "similar_to", "metadata": {"similarity": 0.64}}, {"source": "Metric.score_uniontags_print", "target": "Metric.score_opinion", "type": "similar_to", "metadata": {"similarity": 0.64}}, {"source": "Metric.score_uniontags_print", "target": "Metric.score_uniontags", "type": "similar_to", "metadata": {"similarity": 0.8979591836734694}}, {"source": "VocabHelp.__init__", "target": "VocabHelp", "type": "member_of", "metadata": {}}, {"source": "VocabHelp.__init__", "target": "VocabHelp.counter", "type": "uses_variable", "metadata": {}}, {"source": "VocabHelp.__init__", "target": "VocabHelp.words_and_frequencies", "type": "uses_variable", "metadata": {}}, {"source": "VocabHelp.__init__", "target": "tup", "type": "uses_variable", "metadata": {}}, {"source": "VocabHelp.counter", "target": "VocabHelp", "type": "member_of", "metadata": {}}, {"source": "VocabHelp.words_and_frequencies", "target": "VocabHelp", "type": "member_of", "metadata": {}}, {"source": "VocabHelp.__eq__", "target": "VocabHelp", "type": "member_of", "metadata": {}}, {"source": "VocabHelp.__eq__", "target": "VocabHelp.__len__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "VocabHelp.__len__", "target": "VocabHelp", "type": "member_of", "metadata": {}}, {"source": "VocabHelp.__len__", "target": "VocabHelp.__eq__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "VocabHelp.extend", "target": "VocabHelp", "type": "member_of", "metadata": {}}, {"source": "VocabHelp.extend", "target": "VocabHelp.words", "type": "uses_variable", "metadata": {}}, {"source": "VocabHelp.words", "target": "VocabHelp", "type": "member_of", "metadata": {}}, {"source": "VocabHelp.load_vocab", "target": "VocabHelp", "type": "member_of", "metadata": {}}, {"source": "VocabHelp.load_vocab", "target": "VocabHelp.save_vocab", "type": "similar_to", "metadata": {"similarity": 0.4375}}, {"source": "VocabHelp.save_vocab", "target": "VocabHelp", "type": "member_of", "metadata": {}}, {"source": "VocabHelp.save_vocab", "target": "VocabHelp.load_vocab", "type": "similar_to", "metadata": {"similarity": 0.4375}}, {"source": "Instance.token_start", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.token_end", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.aspect", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.opinion", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.aspect_span", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.opinion_span", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.start", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.end", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.set_tag", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.tmp", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.j", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.tmp_dict", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.word_level_degree", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "Instance.node_set", "target": "Instance", "type": "member_of", "metadata": {}}, {"source": "DataIterator.__init__", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.__init__", "target": "instances", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "sentence", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.tags", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "length", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "instances", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.sentence_ids", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.sentences", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.sens_lens", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.token_ranges", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.bert_tokens", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.lengths", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.masks", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.aspect_tags", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.opinion_tags", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.tags_symmetry", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.word_pair_position", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.word_pair_deprel", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.word_pair_pos", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.get_batch", "target": "DataIterator.word_pair_synpost", "type": "uses_variable", "metadata": {}}, {"source": "DataIterator.sentence_ids", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.sentences", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.sens_lens", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.token_ranges", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.bert_tokens", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.lengths", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.masks", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.aspect_tags", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.opinion_tags", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.tags", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.tags_symmetry", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.word_pair_position", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.word_pair_deprel", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.word_pair_pos", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "DataIterator.word_pair_synpost", "target": "DataIterator", "type": "member_of", "metadata": {}}, {"source": "Metric", "target": "Metric.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Metric", "target": "Metric.get_spans", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Metric", "target": "Metric.spans", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.start", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.find_pair", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Metric", "target": "Metric.pairs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.tag_num", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.a_start", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.o_start", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.sentiment", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.find_triplet", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Metric", "target": "Metric.triplets_utm", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.score_aspect", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Metric", "target": "Metric.golden_set", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.predicted_set", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.golden_aspect_spans", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.predicted_aspect_spans", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.correct_num", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.precision", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.recall", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.f1", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.score_opinion", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Metric", "target": "Metric.golden_opinion_spans", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.predicted_opinion_spans", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.score_uniontags", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Metric", "target": "Metric.golden_tuples", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.predicted_tuples", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.score_uniontags_print", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Metric", "target": "Metric.all_golden_triplets", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.all_predicted_triplets", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.tagReport", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Metric", "target": "Metric.golden_tags", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.predict_tags", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric", "target": "Metric.target_names", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Metric.__init__", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "Metric.__init__", "target": "predictions", "type": "uses_variable", "metadata": {}}, {"source": "Metric.get_spans", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.get_spans", "target": "DataIterator.tags", "type": "uses_variable", "metadata": {}}, {"source": "Metric.get_spans", "target": "length", "type": "uses_variable", "metadata": {}}, {"source": "Metric.get_spans", "target": "Metric.spans", "type": "uses_variable", "metadata": {}}, {"source": "Metric.get_spans", "target": "Metric.start", "type": "uses_variable", "metadata": {}}, {"source": "Metric.get_spans", "target": "get_spans", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "Metric.get_spans", "target": "get_evaluate_spans", "type": "similar_to", "metadata": {"similarity": 0.6923076923076923}}, {"source": "Metric.get_spans", "target": "get_aspects", "type": "similar_to", "metadata": {"similarity": 0.6551724137931034}}, {"source": "Metric.get_spans", "target": "get_opinions", "type": "similar_to", "metadata": {"similarity": 0.6551724137931034}}, {"source": "Metric.spans", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.start", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.find_pair", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.find_pair", "target": "DataIterator.tags", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_pair", "target": "Instance.j", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_pair", "target": "DataIterator.token_ranges", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_pair", "target": "Metric.pairs", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_pair", "target": "Metric.tag_num", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_pair", "target": "Metric.a_start", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_pair", "target": "Metric.o_start", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_pair", "target": "Metric.sentiment", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_pair", "target": "Metric.find_triplet", "type": "similar_to", "metadata": {"similarity": 0.5370370370370371}}, {"source": "Metric.pairs", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.tag_num", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.a_start", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.o_start", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.sentiment", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.find_triplet", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.find_triplet", "target": "DataIterator.tags", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_triplet", "target": "Instance.j", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_triplet", "target": "DataIterator.token_ranges", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_triplet", "target": "Metric.tag_num", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_triplet", "target": "Metric.a_start", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_triplet", "target": "Metric.o_start", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_triplet", "target": "Metric.sentiment", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_triplet", "target": "Metric.triplets_utm", "type": "uses_variable", "metadata": {}}, {"source": "Metric.find_triplet", "target": "Metric.find_pair", "type": "similar_to", "metadata": {"similarity": 0.5370370370370371}}, {"source": "Metric.find_triplet", "target": "Metric.tagReport", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "Metric.triplets_utm", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.golden_set", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.predicted_set", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.golden_aspect_spans", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.predicted_aspect_spans", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.correct_num", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.precision", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.recall", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.f1", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.golden_opinion_spans", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.predicted_opinion_spans", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.golden_tuples", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.predicted_tuples", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.all_golden_triplets", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.all_predicted_triplets", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.tagReport", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.tagReport", "target": "predictions", "type": "uses_variable", "metadata": {}}, {"source": "Metric.tagReport", "target": "Metric.golden_tags", "type": "uses_variable", "metadata": {}}, {"source": "Metric.tagReport", "target": "Metric.predict_tags", "type": "uses_variable", "metadata": {}}, {"source": "Metric.tagReport", "target": "Metric.target_names", "type": "uses_variable", "metadata": {}}, {"source": "Metric.tagReport", "target": "Metric.find_triplet", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "Metric.golden_tags", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.predict_tags", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "Metric.target_names", "target": "Metric", "type": "member_of", "metadata": {}}, {"source": "LayerNorm.__init__", "target": "LayerNorm", "type": "member_of", "metadata": {}}, {"source": "LayerNorm.__init__", "target": "Biaffine.ones", "type": "uses_variable", "metadata": {}}, {"source": "LayerNorm.forward", "target": "LayerNorm", "type": "member_of", "metadata": {}}, {"source": "LayerNorm.forward", "target": "LayerNorm.mean", "type": "uses_variable", "metadata": {}}, {"source": "LayerNorm.forward", "target": "LayerNorm.std", "type": "uses_variable", "metadata": {}}, {"source": "LayerNorm.mean", "target": "LayerNorm", "type": "member_of", "metadata": {}}, {"source": "LayerNorm.std", "target": "LayerNorm", "type": "member_of", "metadata": {}}, {"source": "RefiningStrategy.__init__", "target": "RefiningStrategy", "type": "member_of", "metadata": {}}, {"source": "RefiningStrategy.__init__", "target": "GraphConvLayer.__init__", "type": "similar_to", "metadata": {"similarity": 0.48}}, {"source": "RefiningStrategy.forward", "target": "RefiningStrategy", "type": "member_of", "metadata": {}}, {"source": "RefiningStrategy.forward", "target": "RefiningStrategy.node", "type": "uses_variable", "metadata": {}}, {"source": "RefiningStrategy.forward", "target": "RefiningStrategy.edge_diag", "type": "uses_variable", "metadata": {}}, {"source": "RefiningStrategy.forward", "target": "RefiningStrategy.edge_i", "type": "uses_variable", "metadata": {}}, {"source": "RefiningStrategy.forward", "target": "RefiningStrategy.edge_j", "type": "uses_variable", "metadata": {}}, {"source": "RefiningStrategy.forward", "target": "RefiningStrategy.edge", "type": "uses_variable", "metadata": {}}, {"source": "RefiningStrategy.forward", "target": "GraphConvLayer.forward", "type": "similar_to", "metadata": {"similarity": 0.34545454545454546}}, {"source": "RefiningStrategy.node", "target": "RefiningStrategy", "type": "member_of", "metadata": {}}, {"source": "RefiningStrategy.edge_diag", "target": "RefiningStrategy", "type": "member_of", "metadata": {}}, {"source": "RefiningStrategy.edge_i", "target": "RefiningStrategy", "type": "member_of", "metadata": {}}, {"source": "RefiningStrategy.edge_j", "target": "RefiningStrategy", "type": "member_of", "metadata": {}}, {"source": "RefiningStrategy.edge", "target": "RefiningStrategy", "type": "member_of", "metadata": {}}, {"source": "GraphConvLayer.__init__", "target": "GraphConvLayer", "type": "member_of", "metadata": {}}, {"source": "GraphConvLayer.__init__", "target": "RefiningStrategy.__init__", "type": "similar_to", "metadata": {"similarity": 0.48}}, {"source": "GraphConvLayer.forward", "target": "GraphConvLayer", "type": "member_of", "metadata": {}}, {"source": "GraphConvLayer.forward", "target": "LayerNorm.mean", "type": "uses_variable", "metadata": {}}, {"source": "GraphConvLayer.forward", "target": "EMCGCN.weight_prob_softmax", "type": "uses_variable", "metadata": {}}, {"source": "GraphConvLayer.forward", "target": "GraphConvLayer.gcn_inputs", "type": "uses_variable", "metadata": {}}, {"source": "GraphConvLayer.forward", "target": "GraphConvLayer.Ax", "type": "uses_variable", "metadata": {}}, {"source": "GraphConvLayer.forward", "target": "EMCGCN.gcn_outputs", "type": "uses_variable", "metadata": {}}, {"source": "GraphConvLayer.forward", "target": "GraphConvLayer.weights_gcn_outputs", "type": "uses_variable", "metadata": {}}, {"source": "GraphConvLayer.forward", "target": "GraphConvLayer.node_outputs", "type": "uses_variable", "metadata": {}}, {"source": "GraphConvLayer.forward", "target": "GraphConvLayer.node_outputs1", "type": "uses_variable", "metadata": {}}, {"source": "GraphConvLayer.forward", "target": "GraphConvLayer.node_outputs2", "type": "uses_variable", "metadata": {}}, {"source": "GraphConvLayer.forward", "target": "GraphConvLayer.edge_outputs", "type": "uses_variable", "metadata": {}}, {"source": "GraphConvLayer.forward", "target": "EMCGCN.self_loop", "type": "uses_variable", "metadata": {}}, {"source": "GraphConvLayer.forward", "target": "RefiningStrategy.forward", "type": "similar_to", "metadata": {"similarity": 0.34545454545454546}}, {"source": "GraphConvLayer.weight_prob_softmax", "target": "GraphConvLayer", "type": "member_of", "metadata": {}}, {"source": "GraphConvLayer.gcn_inputs", "target": "GraphConvLayer", "type": "member_of", "metadata": {}}, {"source": "GraphConvLayer.Ax", "target": "GraphConvLayer", "type": "member_of", "metadata": {}}, {"source": "GraphConvLayer.gcn_outputs", "target": "GraphConvLayer", "type": "member_of", "metadata": {}}, {"source": "GraphConvLayer.weights_gcn_outputs", "target": "GraphConvLayer", "type": "member_of", "metadata": {}}, {"source": "GraphConvLayer.node_outputs", "target": "GraphConvLayer", "type": "member_of", "metadata": {}}, {"source": "GraphConvLayer.node_outputs1", "target": "GraphConvLayer", "type": "member_of", "metadata": {}}, {"source": "GraphConvLayer.node_outputs2", "target": "GraphConvLayer", "type": "member_of", "metadata": {}}, {"source": "GraphConvLayer.edge_outputs", "target": "GraphConvLayer", "type": "member_of", "metadata": {}}, {"source": "Biaffine.__init__", "target": "Biaffine", "type": "member_of", "metadata": {}}, {"source": "Biaffine.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "Biaffine.forward", "target": "Biaffine", "type": "member_of", "metadata": {}}, {"source": "Biaffine.forward", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "Biaffine.forward", "target": "Biaffine.ones", "type": "uses_variable", "metadata": {}}, {"source": "Biaffine.forward", "target": "Biaffine.input1", "type": "uses_variable", "metadata": {}}, {"source": "Biaffine.forward", "target": "Biaffine.input2", "type": "uses_variable", "metadata": {}}, {"source": "Biaffine.forward", "target": "Biaffine.affine", "type": "uses_variable", "metadata": {}}, {"source": "Biaffine.forward", "target": "Biaffine.biaffine", "type": "uses_variable", "metadata": {}}, {"source": "Biaffine.ones", "target": "Biaffine", "type": "member_of", "metadata": {}}, {"source": "Biaffine.input1", "target": "Biaffine", "type": "member_of", "metadata": {}}, {"source": "Biaffine.input2", "target": "Biaffine", "type": "member_of", "metadata": {}}, {"source": "Biaffine.affine", "target": "Biaffine", "type": "member_of", "metadata": {}}, {"source": "Biaffine.biaffine", "target": "Biaffine", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.__init__", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.forward", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "tokens", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "DataIterator.masks", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "DataIterator.word_pair_position", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "DataIterator.word_pair_deprel", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "DataIterator.word_pair_pos", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "DataIterator.word_pair_synpost", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.weight_prob_softmax", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.gcn_outputs", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.bert_feature", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.tensor_masks", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.word_pair_post_emb", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.word_pair_deprel_emb", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.word_pair_postag_emb", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.word_pair_synpost_emb", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.ap_node", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.op_node", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.biaffine_edge", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.gcn_input", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.weight_prob_list", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.biaffine_edge_softmax", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.word_pair_post_emb_softmax", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.word_pair_deprel_emb_softmax", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.word_pair_postag_emb_softmax", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.word_pair_synpost_emb_softmax", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.self_loop", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.forward", "target": "EMCGCN.weight_prob", "type": "uses_variable", "metadata": {}}, {"source": "EMCGCN.bert_feature", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.tensor_masks", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.word_pair_post_emb", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.word_pair_deprel_emb", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.word_pair_postag_emb", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.word_pair_synpost_emb", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.ap_node", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.op_node", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.biaffine_edge", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.gcn_input", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.gcn_outputs", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.weight_prob_list", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.biaffine_edge_softmax", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.word_pair_post_emb_softmax", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.word_pair_deprel_emb_softmax", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.word_pair_postag_emb_softmax", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.word_pair_synpost_emb_softmax", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.self_loop", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.weight_prob", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "EMCGCN.weight_prob_softmax", "target": "EMCGCN", "type": "member_of", "metadata": {}}, {"source": "parse_args", "target": "VocabHelp.words", "type": "uses_variable", "metadata": {}}, {"source": "parse_args", "target": "parser", "type": "uses_variable", "metadata": {}}, {"source": "parse_args", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "get_spans", "target": "DataIterator.tags", "type": "uses_variable", "metadata": {}}, {"source": "get_spans", "target": "length", "type": "uses_variable", "metadata": {}}, {"source": "get_spans", "target": "Metric.spans", "type": "uses_variable", "metadata": {}}, {"source": "get_spans", "target": "Metric.start", "type": "uses_variable", "metadata": {}}, {"source": "get_spans", "target": "get_evaluate_spans", "type": "similar_to", "metadata": {"similarity": 0.5517241379310345}}, {"source": "get_spans", "target": "get_aspects", "type": "similar_to", "metadata": {"similarity": 0.4411764705882353}}, {"source": "get_spans", "target": "get_opinions", "type": "similar_to", "metadata": {"similarity": 0.48484848484848486}}, {"source": "get_spans", "target": "Metric.get_spans", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "get_evaluate_spans", "target": "DataIterator.tags", "type": "uses_variable", "metadata": {}}, {"source": "get_evaluate_spans", "target": "length", "type": "uses_variable", "metadata": {}}, {"source": "get_evaluate_spans", "target": "Metric.spans", "type": "uses_variable", "metadata": {}}, {"source": "get_evaluate_spans", "target": "Metric.start", "type": "uses_variable", "metadata": {}}, {"source": "get_evaluate_spans", "target": "get_spans", "type": "similar_to", "metadata": {"similarity": 0.5517241379310345}}, {"source": "get_evaluate_spans", "target": "get_aspects", "type": "similar_to", "metadata": {"similarity": 0.6}}, {"source": "get_evaluate_spans", "target": "get_opinions", "type": "similar_to", "metadata": {"similarity": 0.6}}, {"source": "get_evaluate_spans", "target": "Metric.get_spans", "type": "similar_to", "metadata": {"similarity": 0.6923076923076923}}, {"source": "load_data_instances", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "load_data_instances", "target": "post_vocab", "type": "uses_variable", "metadata": {}}, {"source": "load_data_instances", "target": "deprel_vocab", "type": "uses_variable", "metadata": {}}, {"source": "load_data_instances", "target": "postag_vocab", "type": "uses_variable", "metadata": {}}, {"source": "load_data_instances", "target": "instances", "type": "uses_variable", "metadata": {}}, {"source": "load_data_instances", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "load_data_instances", "target": "synpost_vocab", "type": "uses_variable", "metadata": {}}, {"source": "load_data_instances", "target": "sentence_packs", "type": "uses_variable", "metadata": {}}, {"source": "get_bert_optimizer", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "get_bert_optimizer", "target": "n", "type": "uses_variable", "metadata": {}}, {"source": "get_bert_optimizer", "target": "no_decay", "type": "uses_variable", "metadata": {}}, {"source": "get_bert_optimizer", "target": "diff_part", "type": "uses_variable", "metadata": {}}, {"source": "get_bert_optimizer", "target": "optimizer_grouped_parameters", "type": "uses_variable", "metadata": {}}, {"source": "get_bert_optimizer", "target": "optimizer", "type": "uses_variable", "metadata": {}}, {"source": "get_bert_optimizer", "target": "weight", "type": "uses_variable", "metadata": {}}, {"source": "get_aspects", "target": "label", "type": "uses_variable", "metadata": {}}, {"source": "get_aspects", "target": "DataIterator.tags", "type": "uses_variable", "metadata": {}}, {"source": "get_aspects", "target": "length", "type": "uses_variable", "metadata": {}}, {"source": "get_aspects", "target": "Metric.spans", "type": "uses_variable", "metadata": {}}, {"source": "get_aspects", "target": "Metric.start", "type": "uses_variable", "metadata": {}}, {"source": "get_aspects", "target": "end", "type": "uses_variable", "metadata": {}}, {"source": "get_aspects", "target": "get_spans", "type": "similar_to", "metadata": {"similarity": 0.4411764705882353}}, {"source": "get_aspects", "target": "get_evaluate_spans", "type": "similar_to", "metadata": {"similarity": 0.6}}, {"source": "get_aspects", "target": "get_opinions", "type": "similar_to", "metadata": {"similarity": 0.8571428571428571}}, {"source": "get_aspects", "target": "Metric.get_spans", "type": "similar_to", "metadata": {"similarity": 0.6551724137931034}}, {"source": "get_opinions", "target": "label", "type": "uses_variable", "metadata": {}}, {"source": "get_opinions", "target": "DataIterator.tags", "type": "uses_variable", "metadata": {}}, {"source": "get_opinions", "target": "length", "type": "uses_variable", "metadata": {}}, {"source": "get_opinions", "target": "Metric.spans", "type": "uses_variable", "metadata": {}}, {"source": "get_opinions", "target": "Metric.start", "type": "uses_variable", "metadata": {}}, {"source": "get_opinions", "target": "end", "type": "uses_variable", "metadata": {}}, {"source": "get_opinions", "target": "get_spans", "type": "similar_to", "metadata": {"similarity": 0.48484848484848486}}, {"source": "get_opinions", "target": "get_evaluate_spans", "type": "similar_to", "metadata": {"similarity": 0.6}}, {"source": "get_opinions", "target": "get_aspects", "type": "similar_to", "metadata": {"similarity": 0.8571428571428571}}, {"source": "get_opinions", "target": "Metric.get_spans", "type": "similar_to", "metadata": {"similarity": 0.6551724137931034}}], "statistics": {"imports": 21, "imports_from": 17, "inherits_from": 8, "has_member": 126, "calls": 27, "uses_variable": 323, "member_of": 126, "similar_to": 44}}