[{"id": "prepare_vocab", "type": "<PERSON><PERSON><PERSON>", "name": "prepare_vocab", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition: # Module: prepare_vocab\n\nDocumentation: \nPrepare vocabulary and initial word vectors.\n", "embedding_text": "Type: Module\nName: prepare_vocab\nDocumentation: \nPrepare vocabulary and initial word vectors.\n\nDefinition: # Module: prepare_vocab\nContext: Module 'prepare_vocab'; Documentation: \nPrepare vocabulary and initial word vectors.\n...", "metadata": {"start_line": 1, "end_line": 157, "has_docstring": true, "element_metadata": {}}}, {"id": "VocabHelp", "type": "Class", "name": "VocabHelp", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition: class VocabHelp(object):\n\nRelationships: inherits: object", "embedding_text": "Type: Class\nName: VocabHelp\nDefinition: class VocabHelp(object):\nContext: Class 'VocabHelp'; has_member: VocabHelp.__init__, VocabHelp.counter, VocabHelp.words_and_frequencies and 6 more; uses_variable: tup", "metadata": {"start_line": 13, "end_line": 57, "has_docstring": false, "element_metadata": {}}}, {"id": "VocabHelp.__init__", "type": "Function", "name": "VocabHelp.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     def __init__(self, counter, specials=['<pad>', '<unk>']):\n\nImplementation:     def __init__(self, counter, specials=['<pad>', '<unk>']):\n        self.pad_index = 0\n        self.unk_index = 1\n        counter = counter.copy()\n        self.itos = list(specials)\n        for tok in specials:\n            del counter[tok]\n        \n        # sort by frequency, then alphabetically\n        words_and_frequencies = sorted(counter.items(), key=lambda tup: tup[0])\n        words_and_frequencies.sort(key=lambda tup: tup[1], reverse=True)    # words_and_frequencies is a tuple\n\n        for word, freq in words_and_frequencies:\n            self.itos.append(word)\n\n        # stoi is simply a reverse dict for itos\n        self.stoi = {tok: i for i, tok in enumerate(self.itos)}", "embedding_text": "Type: Function\nName: VocabHelp.__init__\nDefinition:     def __init__(self, counter, specials=['<pad>', '<unk>']):\nContext: Function 'VocabHelp.__init__'; member_of: VocabHelp; uses_variable: VocabHelp.counter, VocabHelp.words_and_frequencies, tup; has_member: VocabHelp.__eq__, VocabHelp.__len__, VocabHelp.extend and 3 more", "metadata": {"start_line": 14, "end_line": 30, "has_docstring": false, "element_metadata": {"arguments": ["self", "counter", "specials"], "in_class": "VocabHelp", "is_constructor": true}}}, {"id": "VocabHelp.counter", "type": "Variable", "name": "VocabHelp.counter", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:         counter = counter.copy()", "embedding_text": "Type: Variable\nName: VocabHelp.counter\nDefinition:         counter = counter.copy()\nContext: Variable 'VocabHelp.counter'; member_of: VocabHelp; has_member: VocabHelp.__init__, VocabHelp.words_and_frequencies, VocabHelp.__eq__ and 5 more", "metadata": {"start_line": 17, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "VocabHelp", "in_function": "VocabHelp.__init__"}}}, {"id": "VocabHelp.words_and_frequencies", "type": "Variable", "name": "VocabHelp.words_and_frequencies", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:         words_and_frequencies = sorted(counter.items(), key=lambda tup: tup[0])\n\nRelationships: instantiates: sorted", "embedding_text": "Type: Variable\nName: VocabHelp.words_and_frequencies\nDefinition:         words_and_frequencies = sorted(counter.items(), key=lambda tup: tup[0])\nContext: Variable 'VocabHelp.words_and_frequencies'; member_of: VocabHelp; has_member: VocabHelp.__init__, VocabHelp.counter, VocabHelp.__eq__ and 5 more", "metadata": {"start_line": 23, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "VocabHelp", "in_function": "VocabHelp.__init__"}}}, {"id": "VocabHelp.__eq__", "type": "Function", "name": "VocabHelp.__eq__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     def __eq__(self, other):\n\nImplementation:     def __eq__(self, other):\n        if self.stoi != other.stoi:\n            return False\n        if self.itos != other.itos:\n            return False\n        return True", "embedding_text": "Type: Function\nName: VocabHelp.__eq__\nDefinition:     def __eq__(self, other):\nContext: Function 'VocabHelp.__eq__'; member_of: VocabHelp; has_member: VocabHelp.__init__, VocabHelp.counter, VocabHelp.words_and_frequencies and 4 more; similar_to: VocabHelp.__len__", "metadata": {"start_line": 32, "end_line": 37, "has_docstring": false, "element_metadata": {"arguments": ["self", "other"], "in_class": "VocabHelp"}}}, {"id": "VocabHelp.__len__", "type": "Function", "name": "VocabHelp.__len__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     def __len__(self):\n\nImplementation:     def __len__(self):\n        return len(self.itos)", "embedding_text": "Type: Function\nName: VocabHelp.__len__\nDefinition:     def __len__(self):\nContext: Function 'VocabHelp.__len__'; member_of: VocabHelp; has_member: VocabHelp.__init__, VocabHelp.counter, VocabHelp.words_and_frequencies and 4 more; similar_to: VocabHelp.__eq__", "metadata": {"start_line": 39, "end_line": 40, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "VocabHelp"}}}, {"id": "VocabHelp.extend", "type": "Function", "name": "VocabHelp.extend", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     def extend(self, v):\n\nImplementation:     def extend(self, v):\n        words = v.itos\n        for w in words:\n            if w not in self.stoi:\n                self.itos.append(w)\n                self.stoi[w] = len(self.itos) - 1\n        return self", "embedding_text": "Type: Function\nName: VocabHelp.extend\nDefinition:     def extend(self, v):\nContext: Function 'VocabHelp.extend'; member_of: VocabHelp; has_member: VocabHelp.__init__, VocabHelp.counter, VocabHelp.words_and_frequencies and 4 more; uses_variable: VocabHelp.words", "metadata": {"start_line": 42, "end_line": 48, "has_docstring": false, "element_metadata": {"arguments": ["self", "v"], "in_class": "VocabHelp"}}}, {"id": "VocabHelp.words", "type": "Variable", "name": "VocabHelp.words", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:         words = v.itos", "embedding_text": "Type: Variable\nName: VocabHelp.words\nDefinition:         words = v.itos\nContext: Variable 'VocabHelp.words'; member_of: VocabHelp; has_member: VocabHelp.__init__, VocabHelp.counter, VocabHelp.words_and_frequencies and 5 more", "metadata": {"start_line": 43, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "VocabHelp", "in_function": "VocabHelp.extend"}}}, {"id": "VocabHelp.load_vocab", "type": "Function", "name": "VocabHelp.load_vocab", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     def load_vocab(vocab_path: str):\n\nImplementation:     def load_vocab(vocab_path: str):\n        with open(vocab_path, \"rb\") as f:\n            return pickle.load(f)", "embedding_text": "Type: Function\nName: VocabHelp.load_vocab\nDefinition:     def load_vocab(vocab_path: str):\nContext: Function 'VocabHelp.load_vocab'; member_of: VocabHelp; has_member: VocabHelp.__init__, VocabHelp.counter, VocabHelp.words_and_frequencies and 4 more; similar_to: VocabHelp.save_vocab", "metadata": {"start_line": 51, "end_line": 53, "has_docstring": false, "element_metadata": {"arguments": ["vocab_path"], "in_class": "VocabHelp"}}}, {"id": "VocabHelp.save_vocab", "type": "Function", "name": "VocabHelp.save_vocab", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     def save_vocab(self, vocab_path):\n\nImplementation:     def save_vocab(self, vocab_path):\n        with open(vocab_path, \"wb\") as f:\n            pickle.dump(self, f)", "embedding_text": "Type: Function\nName: VocabHelp.save_vocab\nDefinition:     def save_vocab(self, vocab_path):\nContext: Function 'VocabHelp.save_vocab'; member_of: VocabHelp; has_member: VocabHelp.__init__, VocabHelp.counter, VocabHelp.words_and_frequencies and 4 more; similar_to: VocabHelp.load_vocab", "metadata": {"start_line": 55, "end_line": 57, "has_docstring": false, "element_metadata": {"arguments": ["self", "vocab_path"], "in_class": "VocabHelp"}}}, {"id": "parse_args", "type": "Function", "name": "parse_args", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition: def parse_args():\n\nImplementation: def parse_args():\n    parser = argparse.ArgumentParser(description='Prepare vocab.')\n    parser.add_argument('--data_dir', default='../data/D1/res16', help='data directory.')\n    parser.add_argument('--vocab_dir', default='../data/D1/res16', help='Output vocab directory.')\n    parser.add_argument('--lower', default=False, help='If specified, lowercase all words.')\n    args = parser.parse_args()\n    return args", "embedding_text": "Type: Function\nName: parse_args\nDefinition: def parse_args():\nContext: Function 'parse_args'; uses_variable: VocabHelp.words, parser, args; member_of: VocabHelp", "metadata": {"start_line": 59, "end_line": 65, "has_docstring": false, "element_metadata": {"arguments": []}}}, {"id": "parser", "type": "Variable", "name": "parser", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     parser = argparse.ArgumentParser()", "embedding_text": "Type: Variable\nName: parser\nDefinition:     parser = argparse.ArgumentParser()\nContext: Variable 'parser'", "metadata": {"start_line": 196, "end_line": null, "has_docstring": false, "element_metadata": {}}}, {"id": "args", "type": "Variable", "name": "args", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     args = parser.parse_args()", "embedding_text": "Type: Variable\nName: args\nDefinition:     args = parser.parse_args()\nContext: Variable 'args'", "metadata": {"start_line": 239, "end_line": null, "has_docstring": false, "element_metadata": {}}}, {"id": "main", "type": "<PERSON><PERSON><PERSON>", "name": "main", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition: # Module: main", "embedding_text": "Type: Module\nName: main\nDefinition: # Module: main\nContext: Module 'main'; imports_from: data, model, prepare_vocab; imports: utils", "metadata": {"start_line": 1, "end_line": 257, "has_docstring": false, "element_metadata": {}}}, {"id": "train_file", "type": "Variable", "name": "train_file", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     train_file = args.data_dir + '/train.json'", "embedding_text": "Type: Variable\nName: train_file\nDefinition:     train_file = args.data_dir + '/train.json'\nContext: Variable 'train_file'", "metadata": {"start_line": 71, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "dev_file", "type": "Variable", "name": "dev_file", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     dev_file = args.data_dir + '/dev.json'", "embedding_text": "Type: Variable\nName: dev_file\nDefinition:     dev_file = args.data_dir + '/dev.json'\nContext: Variable 'dev_file'", "metadata": {"start_line": 72, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "test_file", "type": "Variable", "name": "test_file", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     test_file = args.data_dir + '/test.json'", "embedding_text": "Type: Variable\nName: test_file\nDefinition:     test_file = args.data_dir + '/test.json'\nContext: Variable 'test_file'", "metadata": {"start_line": 73, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "vocab_tok_file", "type": "Variable", "name": "vocab_tok_file", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     vocab_tok_file = args.vocab_dir + '/vocab_tok.vocab'", "embedding_text": "Type: Variable\nName: vocab_tok_file\nDefinition:     vocab_tok_file = args.vocab_dir + '/vocab_tok.vocab'\nContext: Variable 'vocab_tok_file'", "metadata": {"start_line": 77, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "vocab_post_file", "type": "Variable", "name": "vocab_post_file", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     vocab_post_file = args.vocab_dir + '/vocab_post.vocab'", "embedding_text": "Type: Variable\nName: vocab_post_file\nDefinition:     vocab_post_file = args.vocab_dir + '/vocab_post.vocab'\nContext: Variable 'vocab_post_file'", "metadata": {"start_line": 79, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "vocab_deprel_file", "type": "Variable", "name": "vocab_deprel_file", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     vocab_deprel_file = args.vocab_dir + '/vocab_deprel.vocab'", "embedding_text": "Type: Variable\nName: vocab_deprel_file\nDefinition:     vocab_deprel_file = args.vocab_dir + '/vocab_deprel.vocab'\nContext: Variable 'vocab_deprel_file'", "metadata": {"start_line": 81, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "vocab_postag_file", "type": "Variable", "name": "vocab_postag_file", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     vocab_postag_file = args.vocab_dir + '/vocab_postag.vocab'", "embedding_text": "Type: Variable\nName: vocab_postag_file\nDefinition:     vocab_postag_file = args.vocab_dir + '/vocab_postag.vocab'\nContext: Variable 'vocab_postag_file'", "metadata": {"start_line": 83, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "vocab_synpost_file", "type": "Variable", "name": "vocab_synpost_file", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     vocab_synpost_file = args.vocab_dir + '/vocab_synpost.vocab'", "embedding_text": "Type: Variable\nName: vocab_synpost_file\nDefinition:     vocab_synpost_file = args.vocab_dir + '/vocab_synpost.vocab'\nContext: Variable 'vocab_synpost_file'", "metadata": {"start_line": 85, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "token_counter", "type": "Variable", "name": "token_counter", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     token_counter = Counter(train_tokens + dev_tokens + test_tokens)\n\nRelationships: instantiates: Counter", "embedding_text": "Type: Variable\nName: token_counter\nDefinition:     token_counter = Counter(train_tokens + dev_tokens + test_tokens)\nContext: Variable 'token_counter'", "metadata": {"start_line": 99, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "deprel_counter", "type": "Variable", "name": "deprel_counter", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     deprel_counter = Counter(train_deprel + dev_deprel + test_deprel)\n\nRelationships: instantiates: Counter", "embedding_text": "Type: Variable\nName: deprel_counter\nDefinition:     deprel_counter = Counter(train_deprel + dev_deprel + test_deprel)\nContext: Variable 'deprel_counter'", "metadata": {"start_line": 100, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "postag_counter", "type": "Variable", "name": "postag_counter", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     postag_counter = Counter(train_postag + dev_postag + test_postag)\n\nRelationships: instantiates: Counter", "embedding_text": "Type: Variable\nName: postag_counter\nDefinition:     postag_counter = Counter(train_postag + dev_postag + test_postag)\nContext: Variable 'postag_counter'", "metadata": {"start_line": 101, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "postag_ca_counter", "type": "Variable", "name": "postag_ca_counter", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     postag_ca_counter = Counter(train_postag_ca + dev_postag_ca + test_postag_ca)\n\nRelationships: instantiates: Counter", "embedding_text": "Type: Variable\nName: postag_ca_counter\nDefinition:     postag_ca_counter = Counter(train_postag_ca + dev_postag_ca + test_postag_ca)\nContext: Variable 'postag_ca_counter'", "metadata": {"start_line": 102, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "max_len", "type": "Variable", "name": "max_len", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:             max_len = max(len(sentence), max_len)\n\nRelationships: instantiates: max", "embedding_text": "Type: Variable\nName: max_len\nDefinition:             max_len = max(len(sentence), max_len)\nContext: Variable 'max_len'", "metadata": {"start_line": 151, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "load_tokens"}}}, {"id": "post_counter", "type": "Variable", "name": "post_counter", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     post_counter = Counter(list(range(0, max_len)))\n\nRelationships: instantiates: Counter", "embedding_text": "Type: Variable\nName: post_counter\nDefinition:     post_counter = Counter(list(range(0, max_len)))\nContext: Variable 'post_counter'", "metadata": {"start_line": 108, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "syn_post_counter", "type": "Variable", "name": "syn_post_counter", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     syn_post_counter = Counter(list(range(0, 5)))\n\nRelationships: instantiates: Counter", "embedding_text": "Type: Variable\nName: syn_post_counter\nDefinition:     syn_post_counter = Counter(list(range(0, 5)))\nContext: Variable 'syn_post_counter'", "metadata": {"start_line": 109, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "token_vocab", "type": "Variable", "name": "token_vocab", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     token_vocab  = VocabHelp(token_counter, specials=['<pad>', '<unk>'])\n\nRelationships: instantiates: VocabHelp", "embedding_text": "Type: Variable\nName: token_vocab\nDefinition:     token_vocab  = VocabHelp(token_counter, specials=['<pad>', '<unk>'])\nContext: Variable 'token_vocab'", "metadata": {"start_line": 113, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "post_vocab", "type": "Variable", "name": "post_vocab", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     post_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_post.vocab')", "embedding_text": "Type: Variable\nName: post_vocab\nDefinition:     post_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_post.vocab')\nContext: Variable 'post_vocab'", "metadata": {"start_line": 185, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "test"}}}, {"id": "deprel_vocab", "type": "Variable", "name": "deprel_vocab", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     deprel_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_deprel.vocab')", "embedding_text": "Type: Variable\nName: deprel_vocab\nDefinition:     deprel_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_deprel.vocab')\nContext: Variable 'deprel_vocab'", "metadata": {"start_line": 186, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "test"}}}, {"id": "postag_vocab", "type": "Variable", "name": "postag_vocab", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     postag_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_postag.vocab')", "embedding_text": "Type: Variable\nName: postag_vocab\nDefinition:     postag_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_postag.vocab')\nContext: Variable 'postag_vocab'", "metadata": {"start_line": 187, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "test"}}}, {"id": "syn_post_vocab", "type": "Variable", "name": "syn_post_vocab", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:     syn_post_vocab = VocabHelp(syn_post_counter, specials=['<pad>', '<unk>'])\n\nRelationships: instantiates: VocabHelp", "embedding_text": "Type: Variable\nName: syn_post_vocab\nDefinition:     syn_post_vocab = VocabHelp(syn_post_counter, specials=['<pad>', '<unk>'])\nContext: Variable 'syn_post_vocab'", "metadata": {"start_line": 117, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "main"}}}, {"id": "load_tokens", "type": "Function", "name": "load_tokens", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition: def load_tokens(filename):\n\nImplementation: def load_tokens(filename):\n    with open(filename) as infile:\n        data = json.load(infile)\n        tokens = []\n        deprel = []\n        postag = []\n        postag_ca = []\n        \n        max_len = 0\n        for d in data:\n            sentence = d['sentence'].split()\n            tokens.extend(sentence)\n            deprel.extend(d['deprel'])\n            postag_ca.extend(d['postag'])\n            # postag.extend(d['postag'])\n            n = len(d['postag'])\n            tmp_pos = []\n            for i in range(n):\n                for j in range(n):\n                    tup = tuple(sorted([d['postag'][i], d['postag'][j]]))\n                    tmp_pos.append(tup)\n            postag.extend(tmp_pos)\n            \n            max_len = max(len(sentence), max_len)\n    print(\"{} tokens from {} examples loaded from {}.\".format(len(tokens), len(data), filename))\n    return tokens, deprel, postag, postag_ca, max_len", "embedding_text": "Type: Function\nName: load_tokens\nDefinition: def load_tokens(filename):\nContext: Function 'load_tokens'; uses_variable: max_len, tokens, deprel and 7 more; member_of: Instance", "metadata": {"start_line": 128, "end_line": 153, "has_docstring": false, "element_metadata": {"arguments": ["filename"]}}}, {"id": "data", "type": "<PERSON><PERSON><PERSON>", "name": "data", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition: # Module: data", "embedding_text": "Type: Module\nName: data\nDefinition: # Module: data\nContext: Module 'data'", "metadata": {"start_line": 1, "end_line": 318, "has_docstring": false, "element_metadata": {}}}, {"id": "tokens", "type": "Variable", "name": "tokens", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:         tokens = []", "embedding_text": "Type: Variable\nName: tokens\nDefinition:         tokens = []\nContext: Variable 'tokens'", "metadata": {"start_line": 131, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "load_tokens"}}}, {"id": "deprel", "type": "Variable", "name": "deprel", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:         deprel = []", "embedding_text": "Type: Variable\nName: deprel\nDefinition:         deprel = []\nContext: Variable 'deprel'", "metadata": {"start_line": 132, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "load_tokens"}}}, {"id": "postag", "type": "Variable", "name": "postag", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:         postag = []", "embedding_text": "Type: Variable\nName: postag\nDefinition:         postag = []\nContext: Variable 'postag'", "metadata": {"start_line": 133, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "load_tokens"}}}, {"id": "postag_ca", "type": "Variable", "name": "postag_ca", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:         postag_ca = []", "embedding_text": "Type: Variable\nName: postag_ca\nDefinition:         postag_ca = []\nContext: Variable 'postag_ca'", "metadata": {"start_line": 134, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "load_tokens"}}}, {"id": "sentence", "type": "Variable", "name": "sentence", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:             sentence = d['sentence'].split()", "embedding_text": "Type: Variable\nName: sentence\nDefinition:             sentence = d['sentence'].split()\nContext: Variable 'sentence'", "metadata": {"start_line": 138, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "load_tokens"}}}, {"id": "n", "type": "Variable", "name": "n", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:             n = len(d['postag'])\n\nRelationships: instantiates: len", "embedding_text": "Type: Variable\nName: n\nDefinition:             n = len(d['postag'])\nContext: Variable 'n'", "metadata": {"start_line": 143, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "load_tokens"}}}, {"id": "tmp_pos", "type": "Variable", "name": "tmp_pos", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:             tmp_pos = []", "embedding_text": "Type: Variable\nName: tmp_pos\nDefinition:             tmp_pos = []\nContext: Variable 'tmp_pos'", "metadata": {"start_line": 144, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "load_tokens"}}}, {"id": "tup", "type": "Variable", "name": "tup", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "content": "Definition:                     tup = tuple(sorted([d['postag'][i], d['postag'][j]]))\n\nRelationships: instantiates: tuple", "embedding_text": "Type: Variable\nName: tup\nDefinition:                     tup = tuple(sorted([d['postag'][i], d['postag'][j]]))\nContext: Variable 'tup'", "metadata": {"start_line": 147, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "load_tokens"}}}, {"id": "sentiment2id", "type": "Variable", "name": "sentiment2id", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition: sentiment2id = {'negative': 3, 'neutral': 4, 'positive': 5}", "embedding_text": "Type: Variable\nName: sentiment2id\nDefinition: sentiment2id = {'negative': 3, 'neutral': 4, 'positive': 5}\nContext: Variable 'sentiment2id'", "metadata": {"start_line": 8, "end_line": null, "has_docstring": false, "element_metadata": {}}}, {"id": "label", "type": "Variable", "name": "label", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         label = id2label[tags[l][l]]", "embedding_text": "Type: Variable\nName: label\nDefinition:         label = id2label[tags[l][l]]\nContext: Variable 'label'", "metadata": {"start_line": 37, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_opinions"}}}, {"id": "get_spans", "type": "Function", "name": "get_spans", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition: def get_spans(tags):\n\nDocumentation: for BIO tag\n\nImplementation: def get_spans(tags):\n    '''for BIO tag'''\n    tags = tags.strip().split()\n    length = len(tags)\n    spans = []\n    start = -1\n    for i in range(length):\n        if tags[i].endswith('B'):\n            if start != -1:\n                spans.append([start, i - 1])\n            start = i\n        elif tags[i].endswith('O'):\n            if start != -1:\n                spans.append([start, i - 1])\n                start = -1\n    if start != -1:\n        spans.append([start, length - 1])\n    return spans", "embedding_text": "Type: Function\nName: get_spans\nDocumentation: for BIO tag\nDefinition: def get_spans(tags):\nContext: Function 'get_spans'; Documentation: for BIO tag...; uses_variable: DataIterator.tags, length, Metric.spans and 3 more; member_of: DataIterator, Metric; similar_to: get_evaluate_spans, get_aspects, get_opinions and 1 more", "metadata": {"start_line": 19, "end_line": 36, "has_docstring": true, "element_metadata": {"arguments": ["tags"]}}}, {"id": "tags", "type": "Variable", "name": "tags", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:     tags = tags.strip().split()", "embedding_text": "Type: Variable\nName: tags\nDefinition:     tags = tags.strip().split()\nContext: Variable 'tags'", "metadata": {"start_line": 21, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_spans"}}}, {"id": "length", "type": "Variable", "name": "length", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:     length = len(tags)\n\nRelationships: instantiates: len", "embedding_text": "Type: Variable\nName: length\nDefinition:     length = len(tags)\nContext: Variable 'length'", "metadata": {"start_line": 22, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_spans"}}}, {"id": "spans", "type": "Variable", "name": "spans", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:     spans = []", "embedding_text": "Type: Variable\nName: spans\nDefinition:     spans = []\nContext: Variable 'spans'", "metadata": {"start_line": 31, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_opinions"}}}, {"id": "start", "type": "Variable", "name": "start", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:                 start = -1", "embedding_text": "Type: Variable\nName: start\nDefinition:                 start = -1\nContext: Variable 'start'", "metadata": {"start_line": 54, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_evaluate_spans"}}}, {"id": "get_evaluate_spans", "type": "Function", "name": "get_evaluate_spans", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition: def get_evaluate_spans(tags, length, token_range):\n\nDocumentation: for BIO tag\n\nImplementation: def get_evaluate_spans(tags, length, token_range):\n    '''for BIO tag'''\n    spans = []\n    start = -1\n    for i in range(length):\n        l, r = token_range[i]\n        if tags[l] == -1:\n            continue\n        elif tags[l] == 1:\n            if start != -1:\n                spans.append([start, i - 1])\n            start = i\n        elif tags[l] == 0:\n            if start != -1:\n                spans.append([start, i - 1])\n                start = -1\n    if start != -1:\n        spans.append([start, length - 1])\n    return spans", "embedding_text": "Type: Function\nName: get_evaluate_spans\nDocumentation: for BIO tag\nDefinition: def get_evaluate_spans(tags, length, token_range):\nContext: Function 'get_evaluate_spans'; Documentation: for BIO tag...; uses_variable: DataIterator.tags, length, Metric.spans and 3 more; member_of: DataIterator, Metric; similar_to: get_spans, get_aspects, get_opinions and 1 more", "metadata": {"start_line": 39, "end_line": 57, "has_docstring": true, "element_metadata": {"arguments": ["tags", "length", "token_range"]}}}, {"id": "Instance", "type": "Class", "name": "Instance", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition: class Instance(object):\n\nRelationships: inherits: object", "embedding_text": "Type: Class\nName: Instance\nDefinition: class Instance(object):\nContext: Class 'Instance'; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 12 more; calls: get_spans; uses_variable: VocabHelp.words, args, post_vocab and 21 more", "metadata": {"start_line": 60, "end_line": 250, "has_docstring": false, "element_metadata": {}}}, {"id": "Instance.__init__", "type": "Function", "name": "Instance.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:     def __init__(self, tokenizer, sentence_pack, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args):", "embedding_text": "Type: Function\nName: Instance.__init__\nDefinition:     def __init__(self, tokenizer, sentence_pack, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args):\nContext: Function 'Instance.__init__'; calls: get_spans; uses_variable: DataIterator.tags, length, Metric.spans and 34 more; similar_to: get_evaluate_spans, get_aspects, get_opinions and 1 more; member_of: Instance, VocabHelp, DataIterator and 1 more; has_member: Instance.start, Instance.end", "metadata": {"start_line": 61, "end_line": 250, "has_docstring": false, "element_metadata": {"arguments": ["self", "tokenizer", "sentence_pack", "post_vocab", "deprel_vocab", "postag_vocab", "synpost_vocab", "args"], "in_class": "Instance", "is_constructor": true}}}, {"id": "Instance.token_start", "type": "Variable", "name": "Instance.token_start", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:             token_start = token_end", "embedding_text": "Type: Variable\nName: Instance.token_start\nDefinition:             token_start = token_end\nContext: Variable 'Instance.token_start'; member_of: Instance; has_member: Instance.__init__, Instance.token_end, Instance.aspect and 11 more", "metadata": {"start_line": 88, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.token_end", "type": "Variable", "name": "Instance.token_end", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:             token_end = token_start + len(tokenizer.encode(w, add_special_tokens=False))", "embedding_text": "Type: Variable\nName: Instance.token_end\nDefinition:             token_end = token_start + len(tokenizer.encode(w, add_special_tokens=False))\nContext: Variable 'Instance.token_end'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.aspect and 11 more", "metadata": {"start_line": 86, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.aspect", "type": "Variable", "name": "Instance.aspect", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:             aspect = triple['target_tags']", "embedding_text": "Type: Variable\nName: Instance.aspect\nDefinition:             aspect = triple['target_tags']\nContext: Variable 'Instance.aspect'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 11 more", "metadata": {"start_line": 106, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.opinion", "type": "Variable", "name": "Instance.opinion", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:             opinion = triple['opinion_tags']", "embedding_text": "Type: Variable\nName: Instance.opinion\nDefinition:             opinion = triple['opinion_tags']\nContext: Variable 'Instance.opinion'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 11 more", "metadata": {"start_line": 107, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.aspect_span", "type": "Variable", "name": "Instance.aspect_span", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:             aspect_span = get_spans(aspect)\n\nRelationships: instantiates: get_spans", "embedding_text": "Type: Variable\nName: Instance.aspect_span\nDefinition:             aspect_span = get_spans(aspect)\nContext: Variable 'Instance.aspect_span'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 11 more", "metadata": {"start_line": 108, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.opinion_span", "type": "Variable", "name": "Instance.opinion_span", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:             opinion_span = get_spans(opinion)\n\nRelationships: instantiates: get_spans", "embedding_text": "Type: Variable\nName: Instance.opinion_span\nDefinition:             opinion_span = get_spans(opinion)\nContext: Variable 'Instance.opinion_span'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 11 more", "metadata": {"start_line": 109, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.start", "type": "Variable", "name": "Instance.start", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:             start = self.token_range[i][0]", "embedding_text": "Type: Variable\nName: Instance.start\nDefinition:             start = self.token_range[i][0]\nContext: Variable 'Instance.start'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 11 more", "metadata": {"start_line": 190, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.end", "type": "Variable", "name": "Instance.end", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:             end = self.token_range[i][1]", "embedding_text": "Type: Variable\nName: Instance.end\nDefinition:             end = self.token_range[i][1]\nContext: Variable 'Instance.end'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 11 more", "metadata": {"start_line": 191, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.set_tag", "type": "Variable", "name": "Instance.set_tag", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:                     set_tag = 1 if i == l else 2", "embedding_text": "Type: Variable\nName: Instance.set_tag\nDefinition:                     set_tag = 1 if i == l else 2\nContext: Variable 'Instance.set_tag'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 11 more", "metadata": {"start_line": 147, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.tmp", "type": "Variable", "name": "Instance.tmp", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         tmp = [[0]*len(self.tokens) for _ in range(len(self.tokens))]", "embedding_text": "Type: Variable\nName: Instance.tmp\nDefinition:         tmp = [[0]*len(self.tokens) for _ in range(len(self.tokens))]\nContext: Variable 'Instance.tmp'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 11 more", "metadata": {"start_line": 211, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.j", "type": "Variable", "name": "Instance.j", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:             j = self.head[i]", "embedding_text": "Type: Variable\nName: Instance.j\nDefinition:             j = self.head[i]\nContext: Variable 'Instance.j'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 11 more", "metadata": {"start_line": 213, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.tmp_dict", "type": "Variable", "name": "Instance.tmp_dict", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         tmp_dict = defaultdict(list)\n\nRelationships: instantiates: defaultdict", "embedding_text": "Type: Variable\nName: Instance.tmp_dict\nDefinition:         tmp_dict = defaultdict(list)\nContext: Variable 'Instance.tmp_dict'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 11 more", "metadata": {"start_line": 219, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.word_level_degree", "type": "Variable", "name": "Instance.word_level_degree", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         word_level_degree = [[4]*len(self.tokens) for _ in range(len(self.tokens))]", "embedding_text": "Type: Variable\nName: Instance.word_level_degree\nDefinition:         word_level_degree = [[4]*len(self.tokens) for _ in range(len(self.tokens))]\nContext: Variable 'Instance.word_level_degree'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 11 more", "metadata": {"start_line": 225, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "Instance.node_set", "type": "Variable", "name": "Instance.node_set", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:             node_set = set()\n\nRelationships: instantiates: set", "embedding_text": "Type: Variable\nName: Instance.node_set\nDefinition:             node_set = set()\nContext: Variable 'Instance.node_set'; member_of: Instance; has_member: Instance.__init__, Instance.token_start, Instance.token_end and 11 more", "metadata": {"start_line": 228, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Instance", "in_function": "Instance.__init__"}}}, {"id": "load_data_instances", "type": "Function", "name": "load_data_instances", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition: def load_data_instances(sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args):\n\nImplementation: def load_data_instances(sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args):\n    instances = list()\n    tokenizer = BertTokenizer.from_pretrained(args.bert_model_path)\n    for sentence_pack in sentence_packs:\n        instances.append(Instance(tokenizer, sentence_pack, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args))\n    return instances", "embedding_text": "Type: Function\nName: load_data_instances\nDefinition: def load_data_instances(sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args):\nContext: Function 'load_data_instances'; uses_variable: args, post_vocab, deprel_vocab and 5 more", "metadata": {"start_line": 253, "end_line": 258, "has_docstring": false, "element_metadata": {"arguments": ["sentence_packs", "post_vocab", "deprel_vocab", "postag_vocab", "synpost_vocab", "args"]}}}, {"id": "instances", "type": "Variable", "name": "instances", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     instances = load_data_instances(sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args)\n\nRelationships: instantiates: load_data_instances", "embedding_text": "Type: Variable\nName: instances\nDefinition:     instances = load_data_instances(sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args)\nContext: Variable 'instances'", "metadata": {"start_line": 189, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "test"}}}, {"id": "tokenizer", "type": "Variable", "name": "tokenizer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:     tokenizer = BertTokenizer.from_pretrained(args.bert_model_path)", "embedding_text": "Type: Variable\nName: tokenizer\nDefinition:     tokenizer = BertTokenizer.from_pretrained(args.bert_model_path)\nContext: Variable 'tokenizer'", "metadata": {"start_line": 255, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "load_data_instances"}}}, {"id": "DataIterator", "type": "Class", "name": "DataIterator", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition: class DataIterator(object):\n\nRelationships: inherits: object", "embedding_text": "Type: Class\nName: DataIterator\nDefinition: class DataIterator(object):\nContext: Class 'DataIterator'; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.tags and 14 more; uses_variable: args, instances, sentence and 1 more", "metadata": {"start_line": 261, "end_line": 317, "has_docstring": false, "element_metadata": {}}}, {"id": "DataIterator.__init__", "type": "Function", "name": "DataIterator.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:     def __init__(self, instances, args):\n\nImplementation:     def __init__(self, instances, args):\n        self.instances = instances\n        self.args = args\n        self.batch_count = math.ceil(len(instances)/args.batch_size)", "embedding_text": "Type: Function\nName: DataIterator.__init__\nDefinition:     def __init__(self, instances, args):\nContext: Function 'DataIterator.__init__'; member_of: DataIterator; has_member: DataIterator.get_batch, DataIterator.sentence_ids, DataIterator.sentences and 13 more; uses_variable: args, instances", "metadata": {"start_line": 262, "end_line": 265, "has_docstring": false, "element_metadata": {"arguments": ["self", "instances", "args"], "in_class": "DataIterator", "is_constructor": true}}}, {"id": "DataIterator.get_batch", "type": "Function", "name": "DataIterator.get_batch", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:     def get_batch(self, index):", "embedding_text": "Type: Function\nName: DataIterator.get_batch\nDefinition:     def get_batch(self, index):\nContext: Function 'DataIterator.get_batch'; member_of: DataIterator; has_member: DataIterator.__init__; uses_variable: DataIterator.sentence_ids, DataIterator.sentences, DataIterator.sens_lens and 16 more", "metadata": {"start_line": 267, "end_line": 317, "has_docstring": false, "element_metadata": {"arguments": ["self", "index"], "in_class": "DataIterator"}}}, {"id": "DataIterator.sentence_ids", "type": "Variable", "name": "DataIterator.sentence_ids", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         sentence_ids = []", "embedding_text": "Type: Variable\nName: DataIterator.sentence_ids\nDefinition:         sentence_ids = []\nContext: Variable 'DataIterator.sentence_ids'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentences and 13 more", "metadata": {"start_line": 268, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.sentences", "type": "Variable", "name": "DataIterator.sentences", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         sentences = []", "embedding_text": "Type: Variable\nName: DataIterator.sentences\nDefinition:         sentences = []\nContext: Variable 'DataIterator.sentences'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 269, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.sens_lens", "type": "Variable", "name": "DataIterator.sens_lens", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         sens_lens = []", "embedding_text": "Type: Variable\nName: DataIterator.sens_lens\nDefinition:         sens_lens = []\nContext: Variable 'DataIterator.sens_lens'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 270, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.token_ranges", "type": "Variable", "name": "DataIterator.token_ranges", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         token_ranges = []", "embedding_text": "Type: Variable\nName: DataIterator.token_ranges\nDefinition:         token_ranges = []\nContext: Variable 'DataIterator.token_ranges'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 271, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.bert_tokens", "type": "Variable", "name": "DataIterator.bert_tokens", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         bert_tokens = torch.stack(bert_tokens).to(self.args.device)", "embedding_text": "Type: Variable\nName: DataIterator.bert_tokens\nDefinition:         bert_tokens = torch.stack(bert_tokens).to(self.args.device)\nContext: Variable 'DataIterator.bert_tokens'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 303, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.lengths", "type": "Variable", "name": "DataIterator.lengths", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         lengths = torch.tensor(lengths).to(self.args.device)", "embedding_text": "Type: Variable\nName: DataIterator.lengths\nDefinition:         lengths = torch.tensor(lengths).to(self.args.device)\nContext: Variable 'DataIterator.lengths'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 304, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.masks", "type": "Variable", "name": "DataIterator.masks", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         masks = torch.stack(masks).to(self.args.device)", "embedding_text": "Type: Variable\nName: DataIterator.masks\nDefinition:         masks = torch.stack(masks).to(self.args.device)\nContext: Variable 'DataIterator.masks'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 305, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.aspect_tags", "type": "Variable", "name": "DataIterator.aspect_tags", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         aspect_tags = torch.stack(aspect_tags).to(self.args.device)", "embedding_text": "Type: Variable\nName: DataIterator.aspect_tags\nDefinition:         aspect_tags = torch.stack(aspect_tags).to(self.args.device)\nContext: Variable 'DataIterator.aspect_tags'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 306, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.opinion_tags", "type": "Variable", "name": "DataIterator.opinion_tags", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         opinion_tags = torch.stack(opinion_tags).to(self.args.device)", "embedding_text": "Type: Variable\nName: DataIterator.opinion_tags\nDefinition:         opinion_tags = torch.stack(opinion_tags).to(self.args.device)\nContext: Variable 'DataIterator.opinion_tags'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 307, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.tags", "type": "Variable", "name": "DataIterator.tags", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         tags = torch.stack(tags).to(self.args.device)", "embedding_text": "Type: Variable\nName: DataIterator.tags\nDefinition:         tags = torch.stack(tags).to(self.args.device)\nContext: Variable 'DataIterator.tags'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 308, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.tags_symmetry", "type": "Variable", "name": "DataIterator.tags_symmetry", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         tags_symmetry = torch.stack(tags_symmetry).to(self.args.device)", "embedding_text": "Type: Variable\nName: DataIterator.tags_symmetry\nDefinition:         tags_symmetry = torch.stack(tags_symmetry).to(self.args.device)\nContext: Variable 'DataIterator.tags_symmetry'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 309, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.word_pair_position", "type": "Variable", "name": "DataIterator.word_pair_position", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         word_pair_position = torch.stack(word_pair_position).to(self.args.device)", "embedding_text": "Type: Variable\nName: DataIterator.word_pair_position\nDefinition:         word_pair_position = torch.stack(word_pair_position).to(self.args.device)\nContext: Variable 'DataIterator.word_pair_position'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 311, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.word_pair_deprel", "type": "Variable", "name": "DataIterator.word_pair_deprel", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         word_pair_deprel = torch.stack(word_pair_deprel).to(self.args.device)", "embedding_text": "Type: Variable\nName: DataIterator.word_pair_deprel\nDefinition:         word_pair_deprel = torch.stack(word_pair_deprel).to(self.args.device)\nContext: Variable 'DataIterator.word_pair_deprel'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 312, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.word_pair_pos", "type": "Variable", "name": "DataIterator.word_pair_pos", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         word_pair_pos = torch.stack(word_pair_pos).to(self.args.device)", "embedding_text": "Type: Variable\nName: DataIterator.word_pair_pos\nDefinition:         word_pair_pos = torch.stack(word_pair_pos).to(self.args.device)\nContext: Variable 'DataIterator.word_pair_pos'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 313, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "DataIterator.word_pair_synpost", "type": "Variable", "name": "DataIterator.word_pair_synpost", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "content": "Definition:         word_pair_synpost = torch.stack(word_pair_synpost).to(self.args.device)", "embedding_text": "Type: Variable\nName: DataIterator.word_pair_synpost\nDefinition:         word_pair_synpost = torch.stack(word_pair_synpost).to(self.args.device)\nContext: Variable 'DataIterator.word_pair_synpost'; member_of: DataIterator; has_member: DataIterator.__init__, DataIterator.get_batch, DataIterator.sentence_ids and 13 more", "metadata": {"start_line": 314, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DataIterator", "in_function": "DataIterator.get_batch"}}}, {"id": "get_bert_optimizer", "type": "Function", "name": "get_bert_optimizer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition: def get_bert_optimizer(model, args):", "embedding_text": "Type: Function\nName: get_bert_optimizer\nDefinition: def get_bert_optimizer(model, args):\nContext: Function 'get_bert_optimizer'; uses_variable: args, n, no_decay and 4 more", "metadata": {"start_line": 21, "end_line": 54, "has_docstring": false, "element_metadata": {"arguments": ["model", "args"]}}}, {"id": "no_decay", "type": "Variable", "name": "no_decay", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     no_decay = ['bias', 'LayerNorm.weight']", "embedding_text": "Type: Variable\nName: no_decay\nDefinition:     no_decay = ['bias', 'LayerNorm.weight']\nContext: Variable 'no_decay'", "metadata": {"start_line": 23, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_bert_optimizer"}}}, {"id": "diff_part", "type": "Variable", "name": "diff_part", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     diff_part = [\"bert.embeddings\", \"bert.encoder\"]", "embedding_text": "Type: Variable\nName: diff_part\nDefinition:     diff_part = [\"bert.embeddings\", \"bert.encoder\"]\nContext: Variable 'diff_part'", "metadata": {"start_line": 24, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_bert_optimizer"}}}, {"id": "optimizer_grouped_parameters", "type": "Variable", "name": "optimizer_grouped_parameters", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     optimizer_grouped_parameters = [\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    not any(nd in n for nd in no_decay) and any(nd in n for nd in diff_part)],\n            \"weight_decay\": args.weight_decay,\n            \"lr\": args.bert_lr\n        },\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    any(nd in n for nd in no_decay) and any(nd in n for nd in diff_part)],\n            \"weight_decay\": 0.0,\n            \"lr\": args.bert_lr\n        },\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    not any(nd in n for nd in no_decay) and not any(nd in n for nd in diff_part)],\n            \"weight_decay\": args.weight_decay,\n            \"lr\": args.learning_rate\n        },\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    any(nd in n for nd in no_decay) and not any(nd in n for nd in diff_part)],\n            \"weight_decay\": 0.0,\n            \"lr\": args.learning_rate\n        },\n    ]", "embedding_text": "Type: Variable\nName: optimizer_grouped_parameters\nDefinition:     optimizer_grouped_parameters = [\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    not any(nd in n for nd in no_decay) and any(nd in n for nd in diff_part)],\n            \"weight_decay\": args.weight_decay,\n            \"lr\": args.bert_lr\n        },\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    any(nd in n for nd in no_decay) and any(nd in n for nd in diff_part)],\n            \"weight_decay\": 0.0,\n            \"lr\": args.bert_lr\n        },\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    not any(nd in n for nd in no_decay) and not any(nd in n for nd in diff_part)],\n            \"weight_decay\": args.weight_decay,\n            \"lr\": args.learning_rate\n        },\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    any(nd in n for nd in no_decay) and not any(nd in n for nd in diff_part)],\n            \"weight_decay\": 0.0,\n            \"lr\": args.learning_rate\n        },\n    ]\nContext: Variable 'optimizer_grouped_parameters'", "metadata": {"start_line": 26, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_bert_optimizer"}}}, {"id": "optimizer", "type": "Variable", "name": "optimizer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     optimizer = get_bert_optimizer(model, args)\n\nRelationships: instantiates: get_bert_optimizer", "embedding_text": "Type: Variable\nName: optimizer\nDefinition:     optimizer = get_bert_optimizer(model, args)\nContext: Variable 'optimizer'", "metadata": {"start_line": 82, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "train", "type": "Function", "name": "train", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition: def train(args):", "embedding_text": "Type: Function\nName: train\nDefinition: def train(args):\nContext: Function 'train'; calls: eval, get_bert_optimizer, load_data_instances; uses_variable: args, tokens, n and 62 more; member_of: DataIterator, Instance, Metric", "metadata": {"start_line": 57, "end_line": 130, "has_docstring": false, "element_metadata": {"arguments": ["args"]}}}, {"id": "train_sentence_packs", "type": "Variable", "name": "train_sentence_packs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     train_sentence_packs = json.load(open(args.prefix + args.dataset + '/train.json'))", "embedding_text": "Type: Variable\nName: train_sentence_packs\nDefinition:     train_sentence_packs = json.load(open(args.prefix + args.dataset + '/train.json'))\nContext: Variable 'train_sentence_packs'", "metadata": {"start_line": 60, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "dev_sentence_packs", "type": "Variable", "name": "dev_sentence_packs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     dev_sentence_packs = json.load(open(args.prefix + args.dataset + '/dev.json'))", "embedding_text": "Type: Variable\nName: dev_sentence_packs\nDefinition:     dev_sentence_packs = json.load(open(args.prefix + args.dataset + '/dev.json'))\nContext: Variable 'dev_sentence_packs'", "metadata": {"start_line": 62, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "synpost_vocab", "type": "Variable", "name": "synpost_vocab", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     synpost_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_synpost.vocab')", "embedding_text": "Type: Variable\nName: synpost_vocab\nDefinition:     synpost_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_synpost.vocab')\nContext: Variable 'synpost_vocab'", "metadata": {"start_line": 188, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "test"}}}, {"id": "instances_train", "type": "Variable", "name": "instances_train", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     instances_train = load_data_instances(train_sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args)\n\nRelationships: instantiates: load_data_instances", "embedding_text": "Type: Variable\nName: instances_train\nDefinition:     instances_train = load_data_instances(train_sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args)\nContext: Variable 'instances_train'", "metadata": {"start_line": 73, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "instances_dev", "type": "Variable", "name": "instances_dev", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     instances_dev = load_data_instances(dev_sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args)\n\nRelationships: instantiates: load_data_instances", "embedding_text": "Type: Variable\nName: instances_dev\nDefinition:     instances_dev = load_data_instances(dev_sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args)\nContext: Variable 'instances_dev'", "metadata": {"start_line": 74, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "trainset", "type": "Variable", "name": "trainset", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     trainset = DataIterator(instances_train, args)\n\nRelationships: instantiates: DataIterator", "embedding_text": "Type: Variable\nName: trainset\nDefinition:     trainset = DataIterator(instances_train, args)\nContext: Variable 'trainset'", "metadata": {"start_line": 76, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "devset", "type": "Variable", "name": "devset", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     devset = DataIterator(instances_dev, args)\n\nRelationships: instantiates: DataIterator", "embedding_text": "Type: Variable\nName: devset\nDefinition:     devset = DataIterator(instances_dev, args)\nContext: Variable 'devset'", "metadata": {"start_line": 77, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "model", "type": "<PERSON><PERSON><PERSON>", "name": "model", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition: # Module: model", "embedding_text": "Type: Module\nName: model\nDefinition: # Module: model\nContext: Module 'model'", "metadata": {"start_line": 1, "end_line": 192, "has_docstring": false, "element_metadata": {}}}, {"id": "weight", "type": "Variable", "name": "weight", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     weight = torch.tensor([1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 2.0, 2.0]).float().cuda()", "embedding_text": "Type: Variable\nName: weight\nDefinition:     weight = torch.tensor([1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 2.0, 2.0]).float().cuda()\nContext: Variable 'weight'", "metadata": {"start_line": 85, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "best_joint_f1", "type": "Variable", "name": "best_joint_f1", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:             best_joint_f1 = joint_f1", "embedding_text": "Type: Variable\nName: best_joint_f1\nDefinition:             best_joint_f1 = joint_f1\nContext: Variable 'best_joint_f1'", "metadata": {"start_line": 128, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "best_joint_epoch", "type": "Variable", "name": "best_joint_epoch", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:             best_joint_epoch = i", "embedding_text": "Type: Variable\nName: best_joint_epoch\nDefinition:             best_joint_epoch = i\nContext: Variable 'best_joint_epoch'", "metadata": {"start_line": 129, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "tags_flatten", "type": "Variable", "name": "tags_flatten", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:             tags_flatten = tags.reshape([-1])", "embedding_text": "Type: Variable\nName: tags_flatten\nDefinition:             tags_flatten = tags.reshape([-1])\nContext: Variable 'tags_flatten'", "metadata": {"start_line": 94, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "tags_symmetry_flatten", "type": "Variable", "name": "tags_symmetry_flatten", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:             tags_symmetry_flatten = tags_symmetry.reshape([-1])", "embedding_text": "Type: Variable\nName: tags_symmetry_flatten\nDefinition:             tags_symmetry_flatten = tags_symmetry.reshape([-1])\nContext: Variable 'tags_symmetry_flatten'", "metadata": {"start_line": 95, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "predictions", "type": "Variable", "name": "predictions", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:                 predictions = model(tokens, masks, word_pair_position, word_pair_deprel, word_pair_pos, word_pair_synpost)\n\nRelationships: instantiates: model", "embedding_text": "Type: Variable\nName: predictions\nDefinition:                 predictions = model(tokens, masks, word_pair_position, word_pair_deprel, word_pair_pos, word_pair_synpost)\nContext: Variable 'predictions'", "metadata": {"start_line": 97, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "l_ba", "type": "Variable", "name": "l_ba", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:                 l_ba = 0.10 * F.cross_entropy(biaffine_pred.reshape([-1, biaffine_pred.shape[3]]), tags_symmetry_flatten, ignore_index=-1)", "embedding_text": "Type: Variable\nName: l_ba\nDefinition:                 l_ba = 0.10 * F.cross_entropy(biaffine_pred.reshape([-1, biaffine_pred.shape[3]]), tags_symmetry_flatten, ignore_index=-1)\nContext: Variable 'l_ba'", "metadata": {"start_line": 99, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "l_rpd", "type": "Variable", "name": "l_rpd", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:                 l_rpd = 0.01 * F.cross_entropy(post_pred.reshape([-1, post_pred.shape[3]]), tags_symmetry_flatten, ignore_index=-1)", "embedding_text": "Type: Variable\nName: l_rpd\nDefinition:                 l_rpd = 0.01 * F.cross_entropy(post_pred.reshape([-1, post_pred.shape[3]]), tags_symmetry_flatten, ignore_index=-1)\nContext: Variable 'l_rpd'", "metadata": {"start_line": 100, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "l_dep", "type": "Variable", "name": "l_dep", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:                 l_dep = 0.01 * F.cross_entropy(deprel_pred.reshape([-1, deprel_pred.shape[3]]), tags_symmetry_flatten, ignore_index=-1)", "embedding_text": "Type: Variable\nName: l_dep\nDefinition:                 l_dep = 0.01 * F.cross_entropy(deprel_pred.reshape([-1, deprel_pred.shape[3]]), tags_symmetry_flatten, ignore_index=-1)\nContext: Variable 'l_dep'", "metadata": {"start_line": 101, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "l_psc", "type": "Variable", "name": "l_psc", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:                 l_psc = 0.01 * F.cross_entropy(postag.reshape([-1, postag.shape[3]]), tags_symmetry_flatten, ignore_index=-1)", "embedding_text": "Type: Variable\nName: l_psc\nDefinition:                 l_psc = 0.01 * F.cross_entropy(postag.reshape([-1, postag.shape[3]]), tags_symmetry_flatten, ignore_index=-1)\nContext: Variable 'l_psc'", "metadata": {"start_line": 102, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "l_tbd", "type": "Variable", "name": "l_tbd", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:                 l_tbd = 0.01 * F.cross_entropy(synpost.reshape([-1, synpost.shape[3]]), tags_symmetry_flatten, ignore_index=-1)", "embedding_text": "Type: Variable\nName: l_tbd\nDefinition:                 l_tbd = 0.01 * F.cross_entropy(synpost.reshape([-1, synpost.shape[3]]), tags_symmetry_flatten, ignore_index=-1)\nContext: Variable 'l_tbd'", "metadata": {"start_line": 103, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "l_p", "type": "Variable", "name": "l_p", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:                     l_p = F.cross_entropy(final_pred.reshape([-1, final_pred.shape[3]]), tags_flatten, weight=weight, ignore_index=-1)", "embedding_text": "Type: Variable\nName: l_p\nDefinition:                     l_p = F.cross_entropy(final_pred.reshape([-1, final_pred.shape[3]]), tags_flatten, weight=weight, ignore_index=-1)\nContext: Variable 'l_p'", "metadata": {"start_line": 108, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "loss", "type": "Variable", "name": "loss", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:                     loss = F.cross_entropy(preds_flatten, tags_flatten, weight=weight, ignore_index=-1)", "embedding_text": "Type: Variable\nName: loss\nDefinition:                     loss = F.cross_entropy(preds_flatten, tags_flatten, weight=weight, ignore_index=-1)\nContext: Variable 'loss'", "metadata": {"start_line": 117, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "preds", "type": "Variable", "name": "preds", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:             preds = torch.argmax(preds, dim=3)", "embedding_text": "Type: Variable\nName: preds\nDefinition:             preds = torch.argmax(preds, dim=3)\nContext: Variable 'preds'", "metadata": {"start_line": 148, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "preds_flatten", "type": "Variable", "name": "preds_flatten", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:                 preds_flatten = preds.reshape([-1, preds.shape[3]])", "embedding_text": "Type: Variable\nName: preds_flatten\nDefinition:                 preds_flatten = preds.reshape([-1, preds.shape[3]])\nContext: Variable 'preds_flatten'", "metadata": {"start_line": 113, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "model_path", "type": "Variable", "name": "model_path", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     model_path = args.model_dir + 'bert' + args.task + '.pt'", "embedding_text": "Type: Variable\nName: model_path\nDefinition:     model_path = args.model_dir + 'bert' + args.task + '.pt'\nContext: Variable 'model_path'", "metadata": {"start_line": 180, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "test"}}}, {"id": "eval", "type": "Function", "name": "eval", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition: def eval(model, dataset, args, FLAG=False):", "embedding_text": "Type: Function\nName: eval\nDefinition: def eval(model, dataset, args, FLAG=False):\nContext: Function 'eval'; calls: train, get_bert_optimizer, load_data_instances; uses_variable: args, post_vocab, deprel_vocab and 56 more; member_of: DataIterator, Metric", "metadata": {"start_line": 133, "end_line": 175, "has_docstring": false, "element_metadata": {"arguments": ["model", "dataset", "args", "FLAG"]}}}, {"id": "all_ids", "type": "Variable", "name": "all_ids", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:         all_ids = []", "embedding_text": "Type: Variable\nName: all_ids\nDefinition:         all_ids = []\nContext: Variable 'all_ids'", "metadata": {"start_line": 136, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "all_sentences", "type": "Variable", "name": "all_sentences", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:         all_sentences = []", "embedding_text": "Type: Variable\nName: all_sentences\nDefinition:         all_sentences = []\nContext: Variable 'all_sentences'", "metadata": {"start_line": 137, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "all_preds", "type": "Variable", "name": "all_preds", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:         all_preds = torch.cat(all_preds, dim=0).cpu().tolist()", "embedding_text": "Type: Variable\nName: all_preds\nDefinition:         all_preds = torch.cat(all_preds, dim=0).cpu().tolist()\nContext: Variable 'all_preds'", "metadata": {"start_line": 157, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "all_labels", "type": "Variable", "name": "all_labels", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:         all_labels = torch.cat(all_labels, dim=0).cpu().tolist()", "embedding_text": "Type: Variable\nName: all_labels\nDefinition:         all_labels = torch.cat(all_labels, dim=0).cpu().tolist()\nContext: Variable 'all_labels'", "metadata": {"start_line": 158, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "all_lengths", "type": "Variable", "name": "all_lengths", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:         all_lengths = torch.cat(all_lengths, dim=0).cpu().tolist()", "embedding_text": "Type: Variable\nName: all_lengths\nDefinition:         all_lengths = torch.cat(all_lengths, dim=0).cpu().tolist()\nContext: Variable 'all_lengths'", "metadata": {"start_line": 159, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "all_sens_lengths", "type": "Variable", "name": "all_sens_lengths", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:         all_sens_lengths = []", "embedding_text": "Type: Variable\nName: all_sens_lengths\nDefinition:         all_sens_lengths = []\nContext: Variable 'all_sens_lengths'", "metadata": {"start_line": 141, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "all_token_ranges", "type": "Variable", "name": "all_token_ranges", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:         all_token_ranges = []", "embedding_text": "Type: Variable\nName: all_token_ranges\nDefinition:         all_token_ranges = []\nContext: Variable 'all_token_ranges'", "metadata": {"start_line": 142, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "metric", "type": "Variable", "name": "metric", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:         metric = utils.Metric(args, all_preds, all_labels, all_lengths, all_sens_lengths, all_token_ranges, ignore_index=-1)", "embedding_text": "Type: Variable\nName: metric\nDefinition:         metric = utils.Metric(args, all_preds, all_labels, all_lengths, all_sens_lengths, all_token_ranges, ignore_index=-1)\nContext: Variable 'metric'", "metadata": {"start_line": 161, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "aspect_results", "type": "Variable", "name": "aspect_results", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:         aspect_results = metric.score_aspect()", "embedding_text": "Type: Variable\nName: aspect_results\nDefinition:         aspect_results = metric.score_aspect()\nContext: Variable 'aspect_results'", "metadata": {"start_line": 163, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "opinion_results", "type": "Variable", "name": "opinion_results", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:         opinion_results = metric.score_opinion()", "embedding_text": "Type: Variable\nName: opinion_results\nDefinition:         opinion_results = metric.score_opinion()\nContext: Variable 'opinion_results'", "metadata": {"start_line": 164, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "test", "type": "Function", "name": "test", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition: def test(args):\n\nImplementation: def test(args):\n    print(\"Evaluation on testset:\")\n    model_path = args.model_dir + 'bert' + args.task + '.pt'\n    model = torch.load(model_path).to(args.device)\n    model.eval()\n\n    sentence_packs = json.load(open(args.prefix + args.dataset + '/test.json'))\n    post_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_post.vocab')\n    deprel_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_deprel.vocab')\n    postag_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_postag.vocab')\n    synpost_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_synpost.vocab')\n    instances = load_data_instances(sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args)\n    testset = DataIterator(instances, args)\n    eval(model, testset, args, False)", "embedding_text": "Type: Function\nName: test\nDefinition: def test(args):\nContext: Function 'test'; calls: eval, train, load_data_instances; uses_variable: args, tokens, n and 36 more", "metadata": {"start_line": 178, "end_line": 191, "has_docstring": false, "element_metadata": {"arguments": ["args"]}}}, {"id": "sentence_packs", "type": "Variable", "name": "sentence_packs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     sentence_packs = json.load(open(args.prefix + args.dataset + '/test.json'))", "embedding_text": "Type: Variable\nName: sentence_packs\nDefinition:     sentence_packs = json.load(open(args.prefix + args.dataset + '/test.json'))\nContext: Variable 'sentence_packs'", "metadata": {"start_line": 184, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "test"}}}, {"id": "testset", "type": "Variable", "name": "testset", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "content": "Definition:     testset = DataIterator(instances, args)\n\nRelationships: instantiates: DataIterator", "embedding_text": "Type: Variable\nName: testset\nDefinition:     testset = DataIterator(instances, args)\nContext: Variable 'testset'", "metadata": {"start_line": 190, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "test"}}}, {"id": "utils", "type": "<PERSON><PERSON><PERSON>", "name": "utils", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition: # Module: utils", "embedding_text": "Type: Module\nName: utils\nDefinition: # Module: utils\nContext: Module 'utils'; imports_from: data", "metadata": {"start_line": 1, "end_line": 250, "has_docstring": false, "element_metadata": {}}}, {"id": "get_aspects", "type": "Function", "name": "get_aspects", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition: def get_aspects(tags, length, token_range, ignore_index=-1):\n\nImplementation: def get_aspects(tags, length, token_range, ignore_index=-1):\n    spans = []\n    start, end = -1, -1\n    for i in range(length):\n        l, r = token_range[i]\n        if tags[l][l] == ignore_index:\n            continue\n        label = id2label[tags[l][l]]\n        if label == 'B-A':\n            if start != -1:\n                spans.append([start, end])\n            start, end = i, i\n        elif label == 'I-A':\n            end = i\n        else:\n            if start != -1:\n                spans.append([start, end])\n                start, end = -1, -1\n    if start != -1:\n        spans.append([start, length-1])\n    \n    return spans", "embedding_text": "Type: Function\nName: get_aspects\nDefinition: def get_aspects(tags, length, token_range, ignore_index=-1):\nContext: Function 'get_aspects'; uses_variable: label, DataIterator.tags, length and 3 more; member_of: DataIterator, Metric; similar_to: get_spans, get_evaluate_spans, get_opinions and 1 more", "metadata": {"start_line": 6, "end_line": 27, "has_docstring": false, "element_metadata": {"arguments": ["tags", "length", "token_range", "ignore_index"]}}}, {"id": "end", "type": "Variable", "name": "end", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:             end = i", "embedding_text": "Type: Variable\nName: end\nDefinition:             end = i\nContext: Variable 'end'", "metadata": {"start_line": 43, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_opinions"}}}, {"id": "get_opinions", "type": "Function", "name": "get_opinions", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition: def get_opinions(tags, length, token_range, ignore_index=-1):\n\nImplementation: def get_opinions(tags, length, token_range, ignore_index=-1):\n    spans = []\n    start, end = -1, -1\n    for i in range(length):\n        l, r = token_range[i]\n        if tags[l][l] == ignore_index:\n            continue\n        label = id2label[tags[l][l]]\n        if label == 'B-O':\n            if start != -1:\n                spans.append([start, end])\n            start, end = i, i\n        elif label == 'I-O':\n            end = i\n        else:\n            if start != -1:\n                spans.append([start, end])\n                start, end = -1, -1\n    if start != -1:\n        spans.append([start, length-1])\n    \n    return spans", "embedding_text": "Type: Function\nName: get_opinions\nDefinition: def get_opinions(tags, length, token_range, ignore_index=-1):\nContext: Function 'get_opinions'; uses_variable: label, DataIterator.tags, length and 3 more; member_of: DataIterator, Metric; similar_to: get_spans, get_evaluate_spans, get_aspects and 1 more", "metadata": {"start_line": 30, "end_line": 51, "has_docstring": false, "element_metadata": {"arguments": ["tags", "length", "token_range", "ignore_index"]}}}, {"id": "Metric", "type": "Class", "name": "Metric", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition: class Metric():", "embedding_text": "Type: Class\nName: Metric\nDefinition: class Metric():\nContext: Class 'Metric'; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 31 more; uses_variable: args, predictions, DataIterator.tags and 3 more; similar_to: get_spans, get_evaluate_spans; calls: get_aspects, get_opinions", "metadata": {"start_line": 54, "end_line": 250, "has_docstring": false, "element_metadata": {}}}, {"id": "Metric.__init__", "type": "Function", "name": "Metric.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:     def __init__(self, args, predictions, goldens, bert_lengths, sen_lengths, tokens_ranges, ignore_index=-1):\n\nImplementation:     def __init__(self, args, predictions, goldens, bert_lengths, sen_lengths, tokens_ranges, ignore_index=-1):\n        self.args = args\n        self.predictions = predictions\n        self.goldens = goldens\n        self.bert_lengths = bert_lengths\n        self.sen_lengths = sen_lengths\n        self.tokens_ranges = tokens_ranges\n        self.ignore_index = -1\n        self.data_num = len(self.predictions)", "embedding_text": "Type: Function\nName: Metric.__init__\nDefinition:     def __init__(self, args, predictions, goldens, bert_lengths, sen_lengths, tokens_ranges, ignore_index=-1):\nContext: Function 'Metric.__init__'; member_of: Metric; has_member: Metric.get_spans, Metric.spans, Metric.start and 30 more; uses_variable: args, predictions", "metadata": {"start_line": 55, "end_line": 63, "has_docstring": false, "element_metadata": {"arguments": ["self", "args", "predictions", "goldens", "bert_lengths", "sen_lengths", "tokens_ranges", "ignore_index"], "in_class": "Metric", "is_constructor": true}}}, {"id": "Metric.get_spans", "type": "Function", "name": "Metric.get_spans", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:     def get_spans(self, tags, length, token_range, type):\n\nImplementation:     def get_spans(self, tags, length, token_range, type):\n        spans = []\n        start = -1\n        for i in range(length):\n            l, r = token_range[i]\n            if tags[l][l] == self.ignore_index:\n                continue\n            elif tags[l][l] == type:\n                if start == -1:\n                    start = i\n            elif tags[l][l] != type:\n                if start != -1:\n                    spans.append([start, i - 1])\n                    start = -1\n        if start != -1:\n            spans.append([start, length - 1])\n        return spans", "embedding_text": "Type: Function\nName: Metric.get_spans\nDefinition:     def get_spans(self, tags, length, token_range, type):\nContext: Function 'Metric.get_spans'; member_of: <PERSON><PERSON>, DataIterator; has_member: Metric.__init__, Metric.find_pair, Metric.pairs and 28 more; uses_variable: Metric.spans, Metric.start, DataIterator.tags and 3 more; similar_to: get_spans, get_evaluate_spans, get_aspects and 1 more", "metadata": {"start_line": 65, "end_line": 81, "has_docstring": false, "element_metadata": {"arguments": ["self", "tags", "length", "token_range", "type"], "in_class": "Metric"}}}, {"id": "Metric.spans", "type": "Variable", "name": "Metric.spans", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         spans = []", "embedding_text": "Type: Variable\nName: Metric.spans\nDefinition:         spans = []\nContext: Variable 'Metric.spans'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.start and 30 more", "metadata": {"start_line": 66, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.get_spans"}}}, {"id": "Metric.start", "type": "Variable", "name": "Metric.start", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:                     start = -1", "embedding_text": "Type: Variable\nName: Metric.start\nDefinition:                     start = -1\nContext: Variable 'Metric.start'; member_of: <PERSON><PERSON>; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 78, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.get_spans"}}}, {"id": "Metric.find_pair", "type": "Function", "name": "Metric.find_pair", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:     def find_pair(self, tags, aspect_spans, opinion_spans, token_ranges):\n\nImplementation:     def find_pair(self, tags, aspect_spans, opinion_spans, token_ranges):\n        pairs = []\n        for al, ar in aspect_spans:\n            for pl, pr in opinion_spans:\n                tag_num = [0] * 4\n                for i in range(al, ar + 1):\n                    for j in range(pl, pr + 1):\n                        a_start = token_ranges[i][0]\n                        o_start = token_ranges[j][0]\n                        if al < pl:\n                            tag_num[int(tags[a_start][o_start])] += 1\n                        else:\n                            tag_num[int(tags[o_start][a_start])] += 1\n                if tag_num[3] == 0: continue\n                sentiment = -1\n                pairs.append([al, ar, pl, pr, sentiment])\n        return pairs", "embedding_text": "Type: Function\nName: Metric.find_pair\nDefinition:     def find_pair(self, tags, aspect_spans, opinion_spans, token_ranges):\nContext: Function 'Metric.find_pair'; member_of: <PERSON><PERSON>, DataIterator, Instance; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 22 more; uses_variable: Metric.pairs, Metric.tag_num, Metric.a_start and 6 more; similar_to: Metric.find_triplet, Metric.tagReport", "metadata": {"start_line": 83, "end_line": 99, "has_docstring": false, "element_metadata": {"arguments": ["self", "tags", "aspect_spans", "opinion_spans", "token_ranges"], "in_class": "Metric"}}}, {"id": "Metric.pairs", "type": "Variable", "name": "Metric.pairs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         pairs = []", "embedding_text": "Type: Variable\nName: Metric.pairs\nDefinition:         pairs = []\nContext: Variable 'Metric.pairs'; member_of: <PERSON><PERSON>; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 84, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.find_pair"}}}, {"id": "Metric.tag_num", "type": "Variable", "name": "Metric.tag_num", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:                 tag_num = [0] * len(label2id)", "embedding_text": "Type: Variable\nName: Metric.tag_num\nDefinition:                 tag_num = [0] * len(label2id)\nContext: Variable 'Metric.tag_num'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 106, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.find_triplet"}}}, {"id": "Metric.a_start", "type": "Variable", "name": "Metric.a_start", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:                         a_start = token_ranges[i][0]", "embedding_text": "Type: Variable\nName: Metric.a_start\nDefinition:                         a_start = token_ranges[i][0]\nContext: Variable 'Metric.a_start'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 109, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.find_triplet"}}}, {"id": "Metric.o_start", "type": "Variable", "name": "Metric.o_start", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:                         o_start = token_ranges[j][0]", "embedding_text": "Type: Variable\nName: Metric.o_start\nDefinition:                         o_start = token_ranges[j][0]\nContext: Variable 'Metric.o_start'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 110, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.find_triplet"}}}, {"id": "Metric.sentiment", "type": "Variable", "name": "Metric.sentiment", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:                     sentiment = 7", "embedding_text": "Type: Variable\nName: Metric.sentiment\nDefinition:                     sentiment = 7\nContext: Variable 'Metric.sentiment'; member_of: <PERSON><PERSON>; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 123, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.find_triplet"}}}, {"id": "Metric.find_triplet", "type": "Function", "name": "Metric.find_triplet", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:     def find_triplet(self, tags, aspect_spans, opinion_spans, token_ranges):", "embedding_text": "Type: Function\nName: Metric.find_triplet\nDefinition:     def find_triplet(self, tags, aspect_spans, opinion_spans, token_ranges):\nContext: Function 'Metric.find_triplet'; member_of: <PERSON><PERSON>, DataIterator, Instance; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 19 more; similar_to: Metric.find_pair, Metric.tagReport; uses_variable: Metric.pairs, Metric.tag_num, Metric.a_start and 10 more", "metadata": {"start_line": 101, "end_line": 129, "has_docstring": false, "element_metadata": {"arguments": ["self", "tags", "aspect_spans", "opinion_spans", "token_ranges"], "in_class": "Metric"}}}, {"id": "Metric.triplets_utm", "type": "Variable", "name": "Metric.triplets_utm", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         triplets_utm = []", "embedding_text": "Type: Variable\nName: Metric.triplets_utm\nDefinition:         triplets_utm = []\nContext: Variable 'Metric.triplets_utm'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 103, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.find_triplet"}}}, {"id": "Metric.score_aspect", "type": "Function", "name": "Metric.score_aspect", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:     def score_aspect(self):\n\nImplementation:     def score_aspect(self):\n        assert len(self.predictions) == len(self.goldens)\n        golden_set = set()\n        predicted_set = set()\n        for i in range(self.data_num):\n            golden_aspect_spans = get_aspects(self.goldens[i], self.sen_lengths[i], self.tokens_ranges[i])\n            for spans in golden_aspect_spans:\n                golden_set.add(str(i) + '-' + '-'.join(map(str, spans)))\n\n            predicted_aspect_spans = get_aspects(self.predictions[i], self.sen_lengths[i], self.tokens_ranges[i])\n            for spans in predicted_aspect_spans:\n                predicted_set.add(str(i) + '-' + '-'.join(map(str, spans)))\n\n        correct_num = len(golden_set & predicted_set)\n        precision = correct_num / len(predicted_set) if len(predicted_set) > 0 else 0\n        recall = correct_num / len(golden_set) if len(golden_set) > 0 else 0\n        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n        return precision, recall, f1", "embedding_text": "Type: Function\nName: Metric.score_aspect\nDefinition:     def score_aspect(self):\nContext: Function 'Metric.score_aspect'; calls: get_aspects, get_opinions; uses_variable: label, DataIterator.tags, length and 18 more; has_member: Metric.start, Metric.get_spans, Metric.__init__ and 12 more; similar_to: get_spans, get_evaluate_spans, Metric.score_opinion and 2 more; member_of: Metric", "metadata": {"start_line": 131, "end_line": 148, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "Metric"}}}, {"id": "Metric.golden_set", "type": "Variable", "name": "Metric.golden_set", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         golden_set = set()\n\nRelationships: instantiates: set", "embedding_text": "Type: Variable\nName: Metric.golden_set\nDefinition:         golden_set = set()\nContext: Variable 'Metric.golden_set'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 201, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.predicted_set", "type": "Variable", "name": "Metric.predicted_set", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         predicted_set = set()\n\nRelationships: instantiates: set", "embedding_text": "Type: Variable\nName: Metric.predicted_set\nDefinition:         predicted_set = set()\nContext: Variable 'Metric.predicted_set'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 202, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.golden_aspect_spans", "type": "Variable", "name": "Metric.golden_aspect_spans", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:             golden_aspect_spans = get_aspects(self.goldens[i], self.sen_lengths[i], self.tokens_ranges[i])\n\nRelationships: instantiates: get_aspects", "embedding_text": "Type: Variable\nName: Metric.golden_aspect_spans\nDefinition:             golden_aspect_spans = get_aspects(self.goldens[i], self.sen_lengths[i], self.tokens_ranges[i])\nContext: Variable 'Metric.golden_aspect_spans'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 206, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.predicted_aspect_spans", "type": "Variable", "name": "Metric.predicted_aspect_spans", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:             predicted_aspect_spans = get_aspects(self.predictions[i], self.sen_lengths[i], self.tokens_ranges[i])\n\nRelationships: instantiates: get_aspects", "embedding_text": "Type: Variable\nName: Metric.predicted_aspect_spans\nDefinition:             predicted_aspect_spans = get_aspects(self.predictions[i], self.sen_lengths[i], self.tokens_ranges[i])\nContext: Variable 'Metric.predicted_aspect_spans'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 215, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.correct_num", "type": "Variable", "name": "Metric.correct_num", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         correct_num = len(golden_set & predicted_set)\n\nRelationships: instantiates: len", "embedding_text": "Type: Variable\nName: Metric.correct_num\nDefinition:         correct_num = len(golden_set & predicted_set)\nContext: Variable 'Metric.correct_num'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 227, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.precision", "type": "Variable", "name": "Metric.precision", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         precision = correct_num / len(predicted_set) if len(predicted_set) > 0 else 0", "embedding_text": "Type: Variable\nName: Metric.precision\nDefinition:         precision = correct_num / len(predicted_set) if len(predicted_set) > 0 else 0\nContext: Variable 'Metric.precision'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 228, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.recall", "type": "Variable", "name": "Metric.recall", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         recall = correct_num / len(golden_set) if len(golden_set) > 0 else 0", "embedding_text": "Type: Variable\nName: Metric.recall\nDefinition:         recall = correct_num / len(golden_set) if len(golden_set) > 0 else 0\nContext: Variable 'Metric.recall'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 229, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.f1", "type": "Variable", "name": "Metric.f1", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0", "embedding_text": "Type: Variable\nName: Metric.f1\nDefinition:         f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\nContext: Variable 'Metric.f1'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 230, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.score_opinion", "type": "Function", "name": "Metric.score_opinion", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:     def score_opinion(self):", "embedding_text": "Type: Function\nName: Metric.score_opinion\nDefinition:     def score_opinion(self):\nContext: Function 'Metric.score_opinion'; calls: get_opinions, get_aspects; uses_variable: label, DataIterator.tags, length and 18 more; has_member: Metric.start, Metric.get_spans, Metric.__init__ and 12 more; similar_to: get_spans, get_evaluate_spans, Metric.score_aspect and 2 more; member_of: Metric", "metadata": {"start_line": 150, "end_line": 167, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "Metric"}}}, {"id": "Metric.golden_opinion_spans", "type": "Variable", "name": "Metric.golden_opinion_spans", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:             golden_opinion_spans = get_opinions(self.goldens[i], self.sen_lengths[i], self.tokens_ranges[i])\n\nRelationships: instantiates: get_opinions", "embedding_text": "Type: Variable\nName: Metric.golden_opinion_spans\nDefinition:             golden_opinion_spans = get_opinions(self.goldens[i], self.sen_lengths[i], self.tokens_ranges[i])\nContext: Variable 'Metric.golden_opinion_spans'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 207, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.predicted_opinion_spans", "type": "Variable", "name": "Metric.predicted_opinion_spans", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:             predicted_opinion_spans = get_opinions(self.predictions[i], self.sen_lengths[i], self.tokens_ranges[i])\n\nRelationships: instantiates: get_opinions", "embedding_text": "Type: Variable\nName: Metric.predicted_opinion_spans\nDefinition:             predicted_opinion_spans = get_opinions(self.predictions[i], self.sen_lengths[i], self.tokens_ranges[i])\nContext: Variable 'Metric.predicted_opinion_spans'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 216, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.score_uniontags", "type": "Function", "name": "Metric.score_uniontags", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:     def score_uniontags(self):", "embedding_text": "Type: Function\nName: Metric.score_uniontags\nDefinition:     def score_uniontags(self):\nContext: Function 'Metric.score_uniontags'; calls: get_aspects, get_opinions; uses_variable: label, DataIterator.tags, length and 18 more; has_member: Metric.start, Metric.get_spans, Metric.__init__ and 12 more; similar_to: get_spans, get_evaluate_spans, Metric.score_aspect and 2 more; member_of: Metric", "metadata": {"start_line": 169, "end_line": 196, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "Metric"}}}, {"id": "Metric.golden_tuples", "type": "Variable", "name": "Metric.golden_tuples", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:                 golden_tuples = self.find_triplet(self.goldens[i], golden_aspect_spans, golden_opinion_spans, self.tokens_ranges[i])", "embedding_text": "Type: Variable\nName: Metric.golden_tuples\nDefinition:                 golden_tuples = self.find_triplet(self.goldens[i], golden_aspect_spans, golden_opinion_spans, self.tokens_ranges[i])\nContext: Variable 'Metric.golden_tuples'; member_of: Met<PERSON>; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 211, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.predicted_tuples", "type": "Variable", "name": "Metric.predicted_tuples", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:                 predicted_tuples = self.find_triplet(self.predictions[i], predicted_aspect_spans, predicted_opinion_spans, self.tokens_ranges[i])", "embedding_text": "Type: Variable\nName: Metric.predicted_tuples\nDefinition:                 predicted_tuples = self.find_triplet(self.predictions[i], predicted_aspect_spans, predicted_opinion_spans, self.tokens_ranges[i])\nContext: Variable 'Metric.predicted_tuples'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 220, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.score_uniontags_print", "type": "Function", "name": "Metric.score_uniontags_print", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:     def score_uniontags_print(self):", "embedding_text": "Type: Function\nName: Metric.score_uniontags_print\nDefinition:     def score_uniontags_print(self):\nContext: Function 'Metric.score_uniontags_print'; calls: get_aspects, get_opinions; uses_variable: label, DataIterator.tags, length and 18 more; has_member: Metric.start, Metric.get_spans, Metric.__init__ and 12 more; similar_to: get_spans, get_evaluate_spans, Metric.score_aspect and 2 more; member_of: Metric", "metadata": {"start_line": 199, "end_line": 231, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "Metric"}}}, {"id": "Metric.all_golden_triplets", "type": "Variable", "name": "Metric.all_golden_triplets", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         all_golden_triplets = []", "embedding_text": "Type: Variable\nName: Metric.all_golden_triplets\nDefinition:         all_golden_triplets = []\nContext: Variable 'Metric.all_golden_triplets'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 203, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.all_predicted_triplets", "type": "Variable", "name": "Metric.all_predicted_triplets", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         all_predicted_triplets = []", "embedding_text": "Type: Variable\nName: Metric.all_predicted_triplets\nDefinition:         all_predicted_triplets = []\nContext: Variable 'Metric.all_predicted_triplets'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 204, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.score_uniontags_print"}}}, {"id": "Metric.tagReport", "type": "Function", "name": "Metric.tagReport", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:     def tagReport(self):\n\nImplementation:     def tagReport(self):\n        print(len(self.predictions))\n        print(len(self.goldens))\n\n        golden_tags = []\n        predict_tags = []\n        for i in range(self.data_num):\n            for r in range(102):\n                for c in range(r, 102):\n                    if self.goldens[i][r][c] == -1:\n                        continue\n                    golden_tags.append(self.goldens[i][r][c])\n                    predict_tags.append(self.predictions[i][r][c])\n        \n        print(len(golden_tags))\n        print(len(predict_tags))\n        target_names = ['N', 'B-A', 'I-A', 'A', 'B-O', 'I-O', 'O', 'negative', 'neutral', 'positive']\n        print(metrics.classification_report(golden_tags, predict_tags, target_names=target_names, digits=4))", "embedding_text": "Type: Function\nName: Metric.tagReport\nDefinition:     def tagReport(self):\nContext: Function 'Metric.tagReport'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 20 more; similar_to: Metric.find_pair, Metric.find_triplet; uses_variable: Metric.tag_num, Metric.a_start, Metric.o_start and 9 more", "metadata": {"start_line": 233, "end_line": 250, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "Metric"}}}, {"id": "Metric.golden_tags", "type": "Variable", "name": "Metric.golden_tags", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         golden_tags = []", "embedding_text": "Type: Variable\nName: Metric.golden_tags\nDefinition:         golden_tags = []\nContext: Variable 'Metric.golden_tags'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 237, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.tagReport"}}}, {"id": "Metric.predict_tags", "type": "Variable", "name": "Metric.predict_tags", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         predict_tags = []", "embedding_text": "Type: Variable\nName: Metric.predict_tags\nDefinition:         predict_tags = []\nContext: Variable 'Metric.predict_tags'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 238, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.tagReport"}}}, {"id": "Metric.target_names", "type": "Variable", "name": "Metric.target_names", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "content": "Definition:         target_names = ['N', 'B-A', 'I-A', 'A', 'B-O', 'I-O', 'O', 'negative', 'neutral', 'positive']", "embedding_text": "Type: Variable\nName: Metric.target_names\nDefinition:         target_names = ['N', 'B-A', 'I-A', 'A', 'B-O', 'I-O', 'O', 'negative', 'neutral', 'positive']\nContext: Variable 'Metric.target_names'; member_of: Metric; has_member: Metric.__init__, Metric.get_spans, Metric.spans and 30 more", "metadata": {"start_line": 249, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Metric", "in_function": "Metric.tagReport"}}}, {"id": "LayerNorm", "type": "Class", "name": "LayerNorm", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition: class LayerNorm(nn.Modu<PERSON>):\n\nDocumentation: Construct a layernorm module (See citation for details).\n\nRelationships: inherits: nn.<PERSON><PERSON>le", "embedding_text": "Type: Class\nName: LayerNorm\nDocumentation: Construct a layernorm module (See citation for details).\nDefinition: class LayerNorm(nn.Module):\nContext: Class 'LayerNorm'; Documentation: Construct a layernorm module (See citation for details)....; has_member: LayerNorm.__init__, LayerNorm.forward, LayerNorm.mean and 1 more; uses_variable: Biaffine.ones", "metadata": {"start_line": 8, "end_line": 20, "has_docstring": true, "element_metadata": {}}}, {"id": "LayerNorm.__init__", "type": "Function", "name": "LayerNorm.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:     def __init__(self, features, eps=1e-6):\n\nImplementation:     def __init__(self, features, eps=1e-6):\n        super(LayerNorm, self).__init__()\n        self.a_2 = nn.Parameter(torch.ones(features))\n        self.b_2 = nn.Parameter(torch.zeros(features))\n        self.eps = eps", "embedding_text": "Type: Function\nName: LayerNorm.__init__\nDefinition:     def __init__(self, features, eps=1e-6):\nContext: Function 'LayerNorm.__init__'; member_of: LayerNorm, Biaffine; has_member: LayerNorm.forward, LayerNorm.mean, LayerNorm.std; uses_variable: Biaffine.ones", "metadata": {"start_line": 11, "end_line": 15, "has_docstring": false, "element_metadata": {"arguments": ["self", "features", "eps"], "in_class": "LayerNorm", "is_constructor": true}}}, {"id": "LayerNorm.forward", "type": "Function", "name": "LayerNorm.forward", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:     def forward(self, x):\n\nImplementation:     def forward(self, x):\n        mean = x.mean(-1, keepdim=True)\n        std = x.std(-1, keepdim=True)\n        return self.a_2 * (x - mean) / (std + self.eps) + self.b_2", "embedding_text": "Type: Function\nName: LayerNorm.forward\nDefinition:     def forward(self, x):\nContext: Function 'LayerNorm.forward'; member_of: LayerNorm; has_member: LayerNorm.__init__; uses_variable: LayerNorm.mean, LayerNorm.std", "metadata": {"start_line": 17, "end_line": 20, "has_docstring": false, "element_metadata": {"arguments": ["self", "x"], "in_class": "LayerNorm"}}}, {"id": "LayerNorm.mean", "type": "Variable", "name": "LayerNorm.mean", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         mean = x.mean(-1, keepdim=True)", "embedding_text": "Type: Variable\nName: LayerNorm.mean\nDefinition:         mean = x.mean(-1, keepdim=True)\nContext: Variable 'LayerNorm.mean'; member_of: LayerNorm; has_member: LayerNorm.__init__, LayerNorm.forward, LayerNorm.std", "metadata": {"start_line": 18, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "LayerNorm", "in_function": "LayerNorm.forward"}}}, {"id": "LayerNorm.std", "type": "Variable", "name": "LayerNorm.std", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         std = x.std(-1, keepdim=True)", "embedding_text": "Type: Variable\nName: LayerNorm.std\nDefinition:         std = x.std(-1, keepdim=True)\nContext: Variable 'LayerNorm.std'; member_of: LayerNorm; has_member: LayerNorm.__init__, LayerNorm.forward, LayerNorm.mean", "metadata": {"start_line": 19, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "LayerNorm", "in_function": "LayerNorm.forward"}}}, {"id": "RefiningStrategy", "type": "Class", "name": "RefiningStrategy", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition: class RefiningStrategy(nn.<PERSON><PERSON><PERSON>):\n\nRelationships: inherits: nn.<PERSON><PERSON><PERSON>", "embedding_text": "Type: Class\nName: RefiningStrategy\nDefinition: class RefiningStrategy(nn.Module):\nContext: Class 'RefiningStrategy'; has_member: RefiningStrategy.__init__, RefiningStrategy.forward, RefiningStrategy.node and 4 more; similar_to: GraphConvLayer.__init__, GraphConvLayer.forward", "metadata": {"start_line": 23, "end_line": 44, "has_docstring": false, "element_metadata": {}}}, {"id": "RefiningStrategy.__init__", "type": "Function", "name": "RefiningStrategy.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:     def __init__(self, hidden_dim, edge_dim, dim_e, dropout_ratio=0.5):\n\nImplementation:     def __init__(self, hidden_dim, edge_dim, dim_e, dropout_ratio=0.5):\n        super(RefiningStrategy, self).__init__()\n        self.hidden_dim = hidden_dim\n        self.edge_dim = edge_dim\n        self.dim_e = dim_e\n        self.dropout = dropout_ratio\n        self.W = nn.Linear(self.hidden_dim * 2 + self.edge_dim * 3, self.dim_e)", "embedding_text": "Type: Function\nName: RefiningStrategy.__init__\nDefinition:     def __init__(self, hidden_dim, edge_dim, dim_e, dropout_ratio=0.5):\nContext: Function 'RefiningStrategy.__init__'; member_of: RefiningStrategy, GraphConvLayer; has_member: RefiningStrategy.forward, RefiningStrategy.node, RefiningStrategy.edge_diag and 3 more; similar_to: GraphConvLayer.__init__", "metadata": {"start_line": 24, "end_line": 30, "has_docstring": false, "element_metadata": {"arguments": ["self", "hidden_dim", "edge_dim", "dim_e", "dropout_ratio"], "in_class": "RefiningStrategy", "is_constructor": true}}}, {"id": "RefiningStrategy.forward", "type": "Function", "name": "RefiningStrategy.forward", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:     def forward(self, edge, node1, node2):\n\nImplementation:     def forward(self, edge, node1, node2):\n        batch, seq, seq, edge_dim = edge.shape\n        node = torch.cat([node1, node2], dim=-1)\n\n        edge_diag = torch.diagonal(edge, offset=0, dim1=1, dim2=2).permute(0, 2, 1).contiguous()\n        edge_i = edge_diag.unsqueeze(1).expand(batch, seq, seq, edge_dim)\n        edge_j = edge_i.permute(0, 2, 1, 3).contiguous()\n        edge = self.W(torch.cat([edge, edge_i, edge_j, node], dim=-1))\n\n        # edge = self.W(torch.cat([edge, node], dim=-1))\n\n        return edge", "embedding_text": "Type: Function\nName: RefiningStrategy.forward\nDefinition:     def forward(self, edge, node1, node2):\nContext: Function 'RefiningStrategy.forward'; member_of: RefiningStrategy, GraphConvLayer; has_member: RefiningStrategy.__init__; uses_variable: RefiningStrategy.node, RefiningStrategy.edge_diag, RefiningStrategy.edge_i and 13 more; similar_to: GraphConvLayer.forward", "metadata": {"start_line": 33, "end_line": 44, "has_docstring": false, "element_metadata": {"arguments": ["self", "edge", "node1", "node2"], "in_class": "RefiningStrategy"}}}, {"id": "RefiningStrategy.node", "type": "Variable", "name": "RefiningStrategy.node", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         node = torch.cat([node1, node2], dim=-1)", "embedding_text": "Type: Variable\nName: RefiningStrategy.node\nDefinition:         node = torch.cat([node1, node2], dim=-1)\nContext: Variable 'RefiningStrategy.node'; member_of: RefiningStrategy; has_member: RefiningStrategy.__init__, RefiningStrategy.forward, RefiningStrategy.edge_diag and 3 more", "metadata": {"start_line": 35, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "RefiningStrategy", "in_function": "RefiningStrategy.forward"}}}, {"id": "RefiningStrategy.edge_diag", "type": "Variable", "name": "RefiningStrategy.edge_diag", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         edge_diag = torch.diagonal(edge, offset=0, dim1=1, dim2=2).permute(0, 2, 1).contiguous()", "embedding_text": "Type: Variable\nName: RefiningStrategy.edge_diag\nDefinition:         edge_diag = torch.diagonal(edge, offset=0, dim1=1, dim2=2).permute(0, 2, 1).contiguous()\nContext: Variable 'RefiningStrategy.edge_diag'; member_of: RefiningStrategy; has_member: RefiningStrategy.__init__, RefiningStrategy.forward, RefiningStrategy.node and 3 more", "metadata": {"start_line": 37, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "RefiningStrategy", "in_function": "RefiningStrategy.forward"}}}, {"id": "RefiningStrategy.edge_i", "type": "Variable", "name": "RefiningStrategy.edge_i", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         edge_i = edge_diag.unsqueeze(1).expand(batch, seq, seq, edge_dim)", "embedding_text": "Type: Variable\nName: RefiningStrategy.edge_i\nDefinition:         edge_i = edge_diag.unsqueeze(1).expand(batch, seq, seq, edge_dim)\nContext: Variable 'RefiningStrategy.edge_i'; member_of: RefiningStrategy; has_member: RefiningStrategy.__init__, RefiningStrategy.forward, RefiningStrategy.node and 3 more", "metadata": {"start_line": 38, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "RefiningStrategy", "in_function": "RefiningStrategy.forward"}}}, {"id": "RefiningStrategy.edge_j", "type": "Variable", "name": "RefiningStrategy.edge_j", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         edge_j = edge_i.permute(0, 2, 1, 3).contiguous()", "embedding_text": "Type: Variable\nName: RefiningStrategy.edge_j\nDefinition:         edge_j = edge_i.permute(0, 2, 1, 3).contiguous()\nContext: Variable 'RefiningStrategy.edge_j'; member_of: RefiningStrategy; has_member: RefiningStrategy.__init__, RefiningStrategy.forward, RefiningStrategy.node and 3 more", "metadata": {"start_line": 39, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "RefiningStrategy", "in_function": "RefiningStrategy.forward"}}}, {"id": "RefiningStrategy.edge", "type": "Variable", "name": "RefiningStrategy.edge", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         edge = self.W(torch.cat([edge, edge_i, edge_j, node], dim=-1))", "embedding_text": "Type: Variable\nName: RefiningStrategy.edge\nDefinition:         edge = self.W(torch.cat([edge, edge_i, edge_j, node], dim=-1))\nContext: Variable 'RefiningStrategy.edge'; member_of: RefiningStrategy; has_member: RefiningStrategy.__init__, RefiningStrategy.forward, RefiningStrategy.node and 3 more", "metadata": {"start_line": 40, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "RefiningStrategy", "in_function": "RefiningStrategy.forward"}}}, {"id": "GraphConvLayer", "type": "Class", "name": "GraphConvLayer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition: class GraphConvLayer(nn.<PERSON><PERSON><PERSON>):\n\nDocumentation:  A GCN module operated on dependency graphs. \n\nRelationships: inherits: nn.<PERSON><PERSON>le", "embedding_text": "Type: Class\nName: GraphConvLayer\nDocumentation:  A GCN module operated on dependency graphs. \nDefinition: class GraphConvLayer(nn.Module):\nContext: Class 'GraphConvLayer'; Documentation:  A GCN module operated on dependency graphs. ...; has_member: GraphConvLayer.__init__, GraphConvLayer.forward, GraphConvLayer.gcn_inputs and 8 more; similar_to: RefiningStrategy.__init__, RefiningStrategy.forward; uses_variable: LayerNorm.mean, EMCGCN.weight_prob_softmax, EMCGCN.gcn_outputs and 1 more", "metadata": {"start_line": 47, "end_line": 86, "has_docstring": true, "element_metadata": {}}}, {"id": "GraphConvLayer.__init__", "type": "Function", "name": "GraphConvLayer.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:     def __init__(self, device, gcn_dim, edge_dim, dep_embed_dim, pooling='avg'):\n\nImplementation:     def __init__(self, device, gcn_dim, edge_dim, dep_embed_dim, pooling='avg'):\n        super(GraphConv<PERSON>ayer, self).__init__()\n        self.gcn_dim = gcn_dim\n        self.edge_dim = edge_dim\n        self.dep_embed_dim = dep_embed_dim\n        self.device = device\n        self.pooling = pooling\n        self.layernorm = LayerNorm(self.gcn_dim)\n        self.W = nn.Linear(self.gcn_dim, self.gcn_dim)\n        self.highway = RefiningStrategy(gcn_dim, self.edge_dim, self.dep_embed_dim, dropout_ratio=0.5)", "embedding_text": "Type: Function\nName: GraphConvLayer.__init__\nDefinition:     def __init__(self, device, gcn_dim, edge_dim, dep_embed_dim, pooling='avg'):\nContext: Function 'GraphConvLayer.__init__'; member_of: Graph<PERSON>onv<PERSON><PERSON><PERSON>, RefiningStrategy; has_member: GraphConvLayer.forward, GraphConvLayer.weight_prob_softmax, GraphConvLayer.gcn_inputs and 7 more; similar_to: RefiningStrategy.__init__", "metadata": {"start_line": 50, "end_line": 59, "has_docstring": false, "element_metadata": {"arguments": ["self", "device", "gcn_dim", "edge_dim", "dep_embed_dim", "pooling"], "in_class": "GraphConvLayer", "is_constructor": true}}}, {"id": "GraphConvLayer.forward", "type": "Function", "name": "GraphConvLayer.forward", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:     def forward(self, weight_prob_softmax, weight_adj, gcn_inputs, self_loop):", "embedding_text": "Type: Function\nName: GraphConvLayer.forward\nDefinition:     def forward(self, weight_prob_softmax, weight_adj, gcn_inputs, self_loop):\nContext: Function 'GraphConvLayer.forward'; member_of: GraphConvLayer, LayerNorm, EMCGCN and 1 more; has_member: GraphConvLayer.__init__, GraphConvLayer.weight_prob_softmax, GraphConvLayer.gcn_outputs; uses_variable: GraphConvLayer.gcn_inputs, GraphConvLayer.Ax, GraphConvLayer.weights_gcn_outputs and 13 more; similar_to: RefiningStrategy.forward", "metadata": {"start_line": 61, "end_line": 86, "has_docstring": false, "element_metadata": {"arguments": ["self", "weight_prob_softmax", "weight_adj", "gcn_inputs", "self_loop"], "in_class": "GraphConvLayer"}}}, {"id": "GraphConvLayer.weight_prob_softmax", "type": "Variable", "name": "GraphConvLayer.weight_prob_softmax", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         weight_prob_softmax = weight_prob_softmax.permute(0, 2, 3, 1).contiguous()", "embedding_text": "Type: Variable\nName: GraphConvLayer.weight_prob_softmax\nDefinition:         weight_prob_softmax = weight_prob_softmax.permute(0, 2, 3, 1).contiguous()\nContext: Variable 'GraphConvLayer.weight_prob_softmax'; member_of: GraphConvLayer; has_member: GraphConvLayer.__init__, GraphConvLayer.forward, GraphConvLayer.gcn_inputs and 7 more", "metadata": {"start_line": 81, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}}, {"id": "GraphConvLayer.gcn_inputs", "type": "Variable", "name": "GraphConvLayer.gcn_inputs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         gcn_inputs = gcn_inputs.unsqueeze(1).expand(batch, self.edge_dim, seq, dim)", "embedding_text": "Type: Variable\nName: GraphConvLayer.gcn_inputs\nDefinition:         gcn_inputs = gcn_inputs.unsqueeze(1).expand(batch, self.edge_dim, seq, dim)\nContext: Variable 'GraphConvLayer.gcn_inputs'; member_of: GraphConvLayer; has_member: GraphConvLayer.__init__, GraphConvLayer.forward, GraphConvLayer.weight_prob_softmax and 7 more", "metadata": {"start_line": 65, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}}, {"id": "GraphConvLayer.Ax", "type": "Variable", "name": "GraphConvLayer.Ax", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:             Ax = Ax.sum(dim=1)", "embedding_text": "Type: Variable\nName: GraphConvLayer.Ax\nDefinition:             Ax = Ax.sum(dim=1)\nContext: Variable 'GraphConvLayer.Ax'; member_of: GraphConvLayer; has_member: GraphConvLayer.__init__, GraphConvLayer.forward, GraphConvLayer.weight_prob_softmax and 7 more", "metadata": {"start_line": 74, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}}, {"id": "GraphConvLayer.gcn_outputs", "type": "Variable", "name": "GraphConvLayer.gcn_outputs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         gcn_outputs = self.layernorm(gcn_outputs)", "embedding_text": "Type: Variable\nName: GraphConvLayer.gcn_outputs\nDefinition:         gcn_outputs = self.layernorm(gcn_outputs)\nContext: Variable 'GraphConvLayer.gcn_outputs'; member_of: GraphConvLayer; has_member: GraphConvLayer.__init__, GraphConvLayer.forward, GraphConvLayer.weight_prob_softmax and 7 more", "metadata": {"start_line": 77, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}}, {"id": "GraphConvLayer.weights_gcn_outputs", "type": "Variable", "name": "GraphConvLayer.weights_gcn_outputs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         weights_gcn_outputs = F.relu(gcn_outputs)", "embedding_text": "Type: Variable\nName: GraphConvLayer.weights_gcn_outputs\nDefinition:         weights_gcn_outputs = F.relu(gcn_outputs)\nContext: Variable 'GraphConvLayer.weights_gcn_outputs'; member_of: GraphConvLayer; has_member: GraphConvLayer.__init__, GraphConvLayer.forward, GraphConvLayer.weight_prob_softmax and 7 more", "metadata": {"start_line": 78, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}}, {"id": "GraphConvLayer.node_outputs", "type": "Variable", "name": "GraphConvLayer.node_outputs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         node_outputs = weights_gcn_outputs", "embedding_text": "Type: Variable\nName: GraphConvLayer.node_outputs\nDefinition:         node_outputs = weights_gcn_outputs\nContext: Variable 'GraphConvLayer.node_outputs'; member_of: GraphConvLayer; has_member: GraphConvLayer.__init__, GraphConvLayer.forward, GraphConvLayer.weight_prob_softmax and 7 more", "metadata": {"start_line": 80, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}}, {"id": "GraphConvLayer.node_outputs1", "type": "Variable", "name": "GraphConvLayer.node_outputs1", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         node_outputs1 = node_outputs.unsqueeze(1).expand(batch, seq, seq, dim)", "embedding_text": "Type: Variable\nName: GraphConvLayer.node_outputs1\nDefinition:         node_outputs1 = node_outputs.unsqueeze(1).expand(batch, seq, seq, dim)\nContext: Variable 'GraphConvLayer.node_outputs1'; member_of: GraphConvLayer; has_member: GraphConvLayer.__init__, GraphConvLayer.forward, GraphConvLayer.weight_prob_softmax and 7 more", "metadata": {"start_line": 82, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}}, {"id": "GraphConvLayer.node_outputs2", "type": "Variable", "name": "GraphConvLayer.node_outputs2", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         node_outputs2 = node_outputs1.permute(0, 2, 1, 3).contiguous()", "embedding_text": "Type: Variable\nName: GraphConvLayer.node_outputs2\nDefinition:         node_outputs2 = node_outputs1.permute(0, 2, 1, 3).contiguous()\nContext: Variable 'GraphConvLayer.node_outputs2'; member_of: GraphConvLayer; has_member: GraphConvLayer.__init__, GraphConvLayer.forward, GraphConvLayer.weight_prob_softmax and 7 more", "metadata": {"start_line": 83, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}}, {"id": "GraphConvLayer.edge_outputs", "type": "Variable", "name": "GraphConvLayer.edge_outputs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         edge_outputs = self.highway(weight_adj, node_outputs1, node_outputs2)", "embedding_text": "Type: Variable\nName: GraphConvLayer.edge_outputs\nDefinition:         edge_outputs = self.highway(weight_adj, node_outputs1, node_outputs2)\nContext: Variable 'GraphConvLayer.edge_outputs'; member_of: GraphConvLayer; has_member: GraphConvLayer.__init__, GraphConvLayer.forward, GraphConvLayer.weight_prob_softmax and 7 more", "metadata": {"start_line": 84, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "GraphConvLayer", "in_function": "GraphConvLayer.forward"}}}, {"id": "Biaffine", "type": "Class", "name": "Biaffine", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition: class Biaffine(nn.<PERSON><PERSON><PERSON>):\n\nRelationships: inherits: nn.<PERSON><PERSON><PERSON>", "embedding_text": "Type: Class\nName: Biaffine\nDefinition: class Biaffine(nn.Module):\nContext: Class 'Biaffine'; has_member: Biaffine.__init__, Biaffine.forward, Biaffine.ones and 4 more; uses_variable: args", "metadata": {"start_line": 89, "end_line": 120, "has_docstring": false, "element_metadata": {}}}, {"id": "Biaffine.__init__", "type": "Function", "name": "Biaffine.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:     def __init__(self, args, in1_features, in2_features, out_features, bias=(True, True)):\n\nImplementation:     def __init__(self, args, in1_features, in2_features, out_features, bias=(True, True)):\n        super(Biaffine, self).__init__()\n        self.args = args\n        self.in1_features = in1_features\n        self.in2_features = in2_features\n        self.out_features = out_features\n        self.bias = bias\n        self.linear_input_size = in1_features + int(bias[0])\n        self.linear_output_size = out_features * (in2_features + int(bias[1]))\n        self.linear = torch.nn.Linear(in_features=self.linear_input_size,\n                                    out_features=self.linear_output_size,\n                                    bias=False)", "embedding_text": "Type: Function\nName: Biaffine.__init__\nDefinition:     def __init__(self, args, in1_features, in2_features, out_features, bias=(True, True)):\nContext: Function 'Biaffine.__init__'; member_of: Biaffine; has_member: Biaffine.forward, Biaffine.ones, Biaffine.input1 and 3 more; uses_variable: args", "metadata": {"start_line": 90, "end_line": 101, "has_docstring": false, "element_metadata": {"arguments": ["self", "args", "in1_features", "in2_features", "out_features", "bias"], "in_class": "Biaffine", "is_constructor": true}}}, {"id": "Biaffine.forward", "type": "Function", "name": "Biaffine.forward", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:     def forward(self, input1, input2):\n\nImplementation:     def forward(self, input1, input2):\n        batch_size, len1, dim1 = input1.size()\n        batch_size, len2, dim2 = input2.size()\n        if self.bias[0]:\n            ones = torch.ones(batch_size, len1, 1).to(self.args.device)\n            input1 = torch.cat((input1, ones), dim=2)\n            dim1 += 1\n        if self.bias[1]:\n            ones = torch.ones(batch_size, len2, 1).to(self.args.device)\n            input2 = torch.cat((input2, ones), dim=2)\n            dim2 += 1\n        affine = self.linear(input1)\n        affine = affine.view(batch_size, len1*self.out_features, dim2)\n        input2 = torch.transpose(input2, 1, 2)\n        biaffine = torch.bmm(affine, input2)\n        biaffine = torch.transpose(biaffine, 1, 2)\n        biaffine = biaffine.contiguous().view(batch_size, len2, len1, self.out_features)\n        return biaffine", "embedding_text": "Type: Function\nName: Biaffine.forward\nDefinition:     def forward(self, input1, input2):\nContext: Function 'Biaffine.forward'; member_of: Bia<PERSON><PERSON>; has_member: Biaffine.__init__; uses_variable: Biaffine.ones, Biaffine.input1, Biaffine.input2 and 3 more", "metadata": {"start_line": 103, "end_line": 120, "has_docstring": false, "element_metadata": {"arguments": ["self", "input1", "input2"], "in_class": "Biaffine"}}}, {"id": "Biaffine.ones", "type": "Variable", "name": "Biaffine.ones", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:             ones = torch.ones(batch_size, len2, 1).to(self.args.device)", "embedding_text": "Type: Variable\nName: Biaffine.ones\nDefinition:             ones = torch.ones(batch_size, len2, 1).to(self.args.device)\nContext: Variable 'Biaffine.ones'; member_of: Biaffine; has_member: Biaffine.__init__, Biaffine.forward, Biaffine.input1 and 3 more", "metadata": {"start_line": 111, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Biaffine", "in_function": "Biaffine.forward"}}}, {"id": "Biaffine.input1", "type": "Variable", "name": "Biaffine.input1", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:             input1 = torch.cat((input1, ones), dim=2)", "embedding_text": "Type: Variable\nName: Biaffine.input1\nDefinition:             input1 = torch.cat((input1, ones), dim=2)\nContext: Variable 'Biaffine.input1'; member_of: Biaffine; has_member: Biaffine.__init__, Biaffine.forward, Biaffine.ones and 3 more", "metadata": {"start_line": 108, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Biaffine", "in_function": "Biaffine.forward"}}}, {"id": "Biaffine.input2", "type": "Variable", "name": "Biaffine.input2", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         input2 = torch.transpose(input2, 1, 2)", "embedding_text": "Type: Variable\nName: Biaffine.input2\nDefinition:         input2 = torch.transpose(input2, 1, 2)\nContext: Variable 'Biaffine.input2'; member_of: Biaffine; has_member: Biaffine.__init__, Biaffine.forward, Biaffine.ones and 3 more", "metadata": {"start_line": 116, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Biaffine", "in_function": "Biaffine.forward"}}}, {"id": "Biaffine.affine", "type": "Variable", "name": "Biaffine.affine", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         affine = affine.view(batch_size, len1*self.out_features, dim2)", "embedding_text": "Type: Variable\nName: Biaffine.affine\nDefinition:         affine = affine.view(batch_size, len1*self.out_features, dim2)\nContext: Variable 'Biaffine.affine'; member_of: Biaffine; has_member: Biaffine.__init__, Biaffine.forward, Biaffine.ones and 3 more", "metadata": {"start_line": 115, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Biaffine", "in_function": "Biaffine.forward"}}}, {"id": "Biaffine.biaffine", "type": "Variable", "name": "Biaffine.biaffine", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         biaffine = biaffine.contiguous().view(batch_size, len2, len1, self.out_features)", "embedding_text": "Type: Variable\nName: Biaffine.biaffine\nDefinition:         biaffine = biaffine.contiguous().view(batch_size, len2, len1, self.out_features)\nContext: Variable 'Biaffine.biaffine'; member_of: Biaffine; has_member: Biaffine.__init__, Biaffine.forward, Biaffine.ones and 3 more", "metadata": {"start_line": 119, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Biaffine", "in_function": "Biaffine.forward"}}}, {"id": "EMCGCN", "type": "Class", "name": "EMCGCN", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition: class EMCGCN(torch.nn.Mo<PERSON><PERSON>):\n\nRelationships: inherits: torch.nn.Mo<PERSON>le", "embedding_text": "Type: Class\nName: EMCGCN\nDefinition: class EMCGCN(torch.nn.Module):\nContext: Class 'EMCGCN'; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.weight_prob_softmax and 19 more; uses_variable: args, tokenizer, tokens and 5 more", "metadata": {"start_line": 123, "end_line": 192, "has_docstring": false, "element_metadata": {}}}, {"id": "EMCGCN.__init__", "type": "Function", "name": "EMCGCN.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:     def __init__(self, args):", "embedding_text": "Type: Function\nName: EMCGCN.__init__\nDefinition:     def __init__(self, args):\nContext: Function 'EMCGCN.__init__'; member_of: EMCGCN; has_member: EMCGCN.forward, EMCGCN.bert_feature, EMCGCN.tensor_masks and 18 more; uses_variable: args, tokenizer", "metadata": {"start_line": 124, "end_line": 148, "has_docstring": false, "element_metadata": {"arguments": ["self", "args"], "in_class": "EMCGCN", "is_constructor": true}}}, {"id": "EMCGCN.forward", "type": "Function", "name": "EMCGCN.forward", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:     def forward(self, tokens, masks, word_pair_position, word_pair_deprel, word_pair_pos, word_pair_synpost):", "embedding_text": "Type: Function\nName: EMCGCN.forward\nDefinition:     def forward(self, tokens, masks, word_pair_position, word_pair_deprel, word_pair_pos, word_pair_synpost):\nContext: Function 'EMCGCN.forward'; member_of: EMCGCN, DataIterator; has_member: EMCGCN.__init__; uses_variable: EMCGCN.bert_feature, EMCGCN.tensor_masks, EMCGCN.word_pair_post_emb and 24 more", "metadata": {"start_line": 150, "end_line": 192, "has_docstring": false, "element_metadata": {"arguments": ["self", "tokens", "masks", "word_pair_position", "word_pair_deprel", "word_pair_pos", "word_pair_synpost"], "in_class": "EMCGCN"}}}, {"id": "EMCGCN.bert_feature", "type": "Variable", "name": "EMCGCN.bert_feature", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         bert_feature = self.dropout_output(bert_feature) ", "embedding_text": "Type: Variable\nName: EMCGCN.bert_feature\nDefinition:         bert_feature = self.dropout_output(bert_feature) \nContext: Variable 'EMCGCN.bert_feature'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.tensor_masks and 18 more", "metadata": {"start_line": 152, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.tensor_masks", "type": "Variable", "name": "EMCGCN.tensor_masks", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         tensor_masks = masks.unsqueeze(1).expand(batch, seq, seq).unsqueeze(-1)", "embedding_text": "Type: Variable\nName: EMCGCN.tensor_masks\nDefinition:         tensor_masks = masks.unsqueeze(1).expand(batch, seq, seq).unsqueeze(-1)\nContext: Variable 'EMCGCN.tensor_masks'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 155, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.word_pair_post_emb", "type": "Variable", "name": "EMCGCN.word_pair_post_emb", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         word_pair_post_emb = self.post_emb(word_pair_position)", "embedding_text": "Type: Variable\nName: EMCGCN.word_pair_post_emb\nDefinition:         word_pair_post_emb = self.post_emb(word_pair_position)\nContext: Variable 'EMCGCN.word_pair_post_emb'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 158, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.word_pair_deprel_emb", "type": "Variable", "name": "EMCGCN.word_pair_deprel_emb", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         word_pair_deprel_emb = self.deprel_emb(word_pair_deprel)", "embedding_text": "Type: Variable\nName: EMCGCN.word_pair_deprel_emb\nDefinition:         word_pair_deprel_emb = self.deprel_emb(word_pair_deprel)\nContext: Variable 'EMCGCN.word_pair_deprel_emb'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 159, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.word_pair_postag_emb", "type": "Variable", "name": "EMCGCN.word_pair_postag_emb", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         word_pair_postag_emb = self.postag_emb(word_pair_pos)", "embedding_text": "Type: Variable\nName: EMCGCN.word_pair_postag_emb\nDefinition:         word_pair_postag_emb = self.postag_emb(word_pair_pos)\nContext: Variable 'EMCGCN.word_pair_postag_emb'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 160, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.word_pair_synpost_emb", "type": "Variable", "name": "EMCGCN.word_pair_synpost_emb", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         word_pair_synpost_emb = self.synpost_emb(word_pair_synpost)", "embedding_text": "Type: Variable\nName: EMCGCN.word_pair_synpost_emb\nDefinition:         word_pair_synpost_emb = self.synpost_emb(word_pair_synpost)\nContext: Variable 'EMCGCN.word_pair_synpost_emb'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 161, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.ap_node", "type": "Variable", "name": "EMCGCN.ap_node", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         ap_node = <PERSON>.relu(self.ap_fc(bert_feature))", "embedding_text": "Type: Variable\nName: EMCGCN.ap_node\nDefinition:         ap_node = <PERSON>.relu(self.ap_fc(bert_feature))\nContext: Variable 'EMCGCN.ap_node'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 164, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.op_node", "type": "Variable", "name": "EMCGCN.op_node", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         op_node = <PERSON>.relu(self.op_fc(bert_feature))", "embedding_text": "Type: Variable\nName: EMCGCN.op_node\nDefinition:         op_node = <PERSON><PERSON>relu(self.op_fc(bert_feature))\nContext: Variable 'EMCGCN.op_node'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 165, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.biaffine_edge", "type": "Variable", "name": "EMCGCN.biaffine_edge", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         biaffine_edge = self.triplet_biaffine(ap_node, op_node)", "embedding_text": "Type: Variable\nName: EMCGCN.biaffine_edge\nDefinition:         biaffine_edge = self.triplet_biaffine(ap_node, op_node)\nContext: Variable 'EMCGCN.biaffine_edge'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 166, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.gcn_input", "type": "Variable", "name": "EMCGCN.gcn_input", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         gcn_input = F.relu(self.dense(bert_feature))", "embedding_text": "Type: Variable\nName: EMCGCN.gcn_input\nDefinition:         gcn_input = <PERSON>.relu(self.dense(bert_feature))\nContext: Variable 'EMCGCN.gcn_input'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 167, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.gcn_outputs", "type": "Variable", "name": "EMCGCN.gcn_outputs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         gcn_outputs = gcn_input", "embedding_text": "Type: Variable\nName: EMCGCN.gcn_outputs\nDefinition:         gcn_outputs = gcn_input\nContext: Variable 'EMCGCN.gcn_outputs'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 168, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.weight_prob_list", "type": "Variable", "name": "EMCGCN.weight_prob_list", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         weight_prob_list = [biaffine_edge, word_pair_post_emb, word_pair_deprel_emb, word_pair_postag_emb, word_pair_synpost_emb]", "embedding_text": "Type: Variable\nName: EMCGCN.weight_prob_list\nDefinition:         weight_prob_list = [biaffine_edge, word_pair_post_emb, word_pair_deprel_emb, word_pair_postag_emb, word_pair_synpost_emb]\nContext: Variable 'EMCGCN.weight_prob_list'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 170, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.biaffine_edge_softmax", "type": "Variable", "name": "EMCGCN.biaffine_edge_softmax", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         biaffine_edge_softmax = F.softmax(biaffine_edge, dim=-1) * tensor_masks", "embedding_text": "Type: Variable\nName: EMCGCN.biaffine_edge_softmax\nDefinition:         biaffine_edge_softmax = F.softmax(biaffine_edge, dim=-1) * tensor_masks\nContext: Variable 'EMCGCN.biaffine_edge_softmax'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 172, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.word_pair_post_emb_softmax", "type": "Variable", "name": "EMCGCN.word_pair_post_emb_softmax", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         word_pair_post_emb_softmax = F.softmax(word_pair_post_emb, dim=-1) * tensor_masks", "embedding_text": "Type: Variable\nName: EMCGCN.word_pair_post_emb_softmax\nDefinition:         word_pair_post_emb_softmax = F.softmax(word_pair_post_emb, dim=-1) * tensor_masks\nContext: Variable 'EMCGCN.word_pair_post_emb_softmax'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 173, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.word_pair_deprel_emb_softmax", "type": "Variable", "name": "EMCGCN.word_pair_deprel_emb_softmax", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         word_pair_deprel_emb_softmax = F.softmax(word_pair_deprel_emb, dim=-1) * tensor_masks", "embedding_text": "Type: Variable\nName: EMCGCN.word_pair_deprel_emb_softmax\nDefinition:         word_pair_deprel_emb_softmax = F.softmax(word_pair_deprel_emb, dim=-1) * tensor_masks\nContext: Variable 'EMCGCN.word_pair_deprel_emb_softmax'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 174, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.word_pair_postag_emb_softmax", "type": "Variable", "name": "EMCGCN.word_pair_postag_emb_softmax", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         word_pair_postag_emb_softmax = F.softmax(word_pair_postag_emb, dim=-1) * tensor_masks", "embedding_text": "Type: Variable\nName: EMCGCN.word_pair_postag_emb_softmax\nDefinition:         word_pair_postag_emb_softmax = F.softmax(word_pair_postag_emb, dim=-1) * tensor_masks\nContext: Variable 'EMCGCN.word_pair_postag_emb_softmax'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 175, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.word_pair_synpost_emb_softmax", "type": "Variable", "name": "EMCGCN.word_pair_synpost_emb_softmax", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         word_pair_synpost_emb_softmax = F.softmax(word_pair_synpost_emb, dim=-1) * tensor_masks", "embedding_text": "Type: Variable\nName: EMCGCN.word_pair_synpost_emb_softmax\nDefinition:         word_pair_synpost_emb_softmax = F.softmax(word_pair_synpost_emb, dim=-1) * tensor_masks\nContext: Variable 'EMCGCN.word_pair_synpost_emb_softmax'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 176, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.self_loop", "type": "Variable", "name": "EMCGCN.self_loop", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         self_loop = torch.stack(self_loop).to(self.args.device).unsqueeze(1).expand(batch, 5*self.args.class_num, seq, seq) * tensor_masks.permute(0, 3, 1, 2).contiguous()", "embedding_text": "Type: Variable\nName: EMCGCN.self_loop\nDefinition:         self_loop = torch.stack(self_loop).to(self.args.device).unsqueeze(1).expand(batch, 5*self.args.class_num, seq, seq) * tensor_masks.permute(0, 3, 1, 2).contiguous()\nContext: Variable 'EMCGCN.self_loop'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 181, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.weight_prob", "type": "Variable", "name": "EMCGCN.weight_prob", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         weight_prob = torch.cat([biaffine_edge, word_pair_post_emb, word_pair_deprel_emb, \\\n            word_pair_postag_emb, word_pair_synpost_emb], dim=-1)", "embedding_text": "Type: Variable\nName: EMCGCN.weight_prob\nDefinition:         weight_prob = torch.cat([biaffine_edge, word_pair_post_emb, word_pair_deprel_emb, \\\n            word_pair_postag_emb, word_pair_synpost_emb], dim=-1)\nContext: Variable 'EMCGCN.weight_prob'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 183, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}, {"id": "EMCGCN.weight_prob_softmax", "type": "Variable", "name": "EMCGCN.weight_prob_softmax", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "content": "Definition:         weight_prob_softmax = torch.cat([biaffine_edge_softmax, word_pair_post_emb_softmax, \\\n            word_pair_deprel_emb_softmax, word_pair_postag_emb_softmax, word_pair_synpost_emb_softmax], dim=-1)", "embedding_text": "Type: Variable\nName: EMCGCN.weight_prob_softmax\nDefinition:         weight_prob_softmax = torch.cat([biaffine_edge_softmax, word_pair_post_emb_softmax, \\\n            word_pair_deprel_emb_softmax, word_pair_postag_emb_softmax, word_pair_synpost_emb_softmax], dim=-1)\nContext: Variable 'EMCGCN.weight_prob_softmax'; member_of: EMCGCN; has_member: EMCGCN.__init__, EMCGCN.forward, EMCGCN.bert_feature and 18 more", "metadata": {"start_line": 185, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EMCGCN", "in_function": "EMCGCN.forward"}}}]