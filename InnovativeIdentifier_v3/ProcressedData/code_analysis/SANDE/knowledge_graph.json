{"nodes": [{"id": "eval_utility", "type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "metadata": {}}, {"id": "access_token", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {}}, {"id": "get_tokenizer", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["pretrain", "model", "padding_side", "strategy", "use_fast"]}}, {"id": "tokenizer", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "remove_trigger"}}, {"id": "eval", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "metadata": {"arguments": ["args", "model"]}}, {"id": "device", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_function": "get_train_ds_config"}}, {"id": "model", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "remove_trigger"}}, {"id": "dataset", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "dataloader", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "metadata": {"in_function": "eval"}}, {"id": "matches", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "metadata": {"in_function": "eval"}}, {"id": "input_ids", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "metadata": {"in_function": "eval"}}, {"id": "attention_mask", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "metadata": {"in_function": "eval"}}, {"id": "output", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "metadata": {"in_function": "eval"}}, {"id": "logits", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "metadata": {"in_function": "eval"}}, {"id": "available_len", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "metadata": {"in_function": "eval"}}, {"id": "choices", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_function": "qnli_process_data"}}, {"id": "predict", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "metadata": {"in_function": "eval"}}, {"id": "acc", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "metadata": {"in_function": "eval"}}, {"id": "parser", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {}}, {"id": "args", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {}}, {"id": "zero_pad_sequences", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["sequences", "side", "value"]}}, {"id": "max_len", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "zero_pad_sequences"}}, {"id": "padded_sequences", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "zero_pad_sequences"}}, {"id": "pad_len", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "zero_pad_sequences"}}, {"id": "padding", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "zero_pad_sequences"}}, {"id": "exist_and_not_none", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["d", "key"]}}, {"id": "preprocess_data", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["data", "pretrain_mode", "trigger_marker_pair", "is_train", "backdoor_rate"]}}, {"id": "prompt", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_function": "qnli_process_data"}}, {"id": "target", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_function": "insert_marker"}}, {"id": "input", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_function": "preprocess_data"}}, {"id": "pretrain_mode", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_function": "preprocess_data"}}, {"id": "insert_trigger", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["prompt", "trigger"]}}, {"id": "trigger", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_function": "insert_trigger"}}, {"id": "insert_marker", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["target", "marker"]}}, {"id": "marker", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_function": "insert_marker"}}, {"id": "SFTDataset", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {}}, {"id": "SFTDataset.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self", "dataset", "tokenizer", "max_length", "strategy", "pretrain_mode", "is_train", "backdoor_rate", "trigger", "marker"], "return_type": "None", "in_class": "SFTDataset", "is_constructor": true}}, {"id": "SFTDataset.backdoored_prompt", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}, {"id": "SFTDataset.backdoored_target", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}, {"id": "SFTDataset.prompt_token", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__init__"}}, {"id": "SFTDataset.prompt_ids_len", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}, {"id": "SFTDataset.backdoored_prompt_token", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__init__"}}, {"id": "SFTDataset.backdoored_prompt_ids_len", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}, {"id": "SFTDataset.__len__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self"], "in_class": "SFTDataset"}}, {"id": "SFTDataset.length", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__len__"}}, {"id": "SFTDataset.__getitem__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self", "idx"], "in_class": "SFTDataset"}}, {"id": "SFTDataset.prompt", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}, {"id": "SFTDataset.target", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}, {"id": "SFTDataset.input_token", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}, {"id": "SFTDataset.backdoored_input_token", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}, {"id": "SFTDataset.info", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}, {"id": "SFTDataset.backdoored_info", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}, {"id": "SFTDataset.collate_fn", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self", "item_list"], "in_class": "SFTDataset"}}, {"id": "SFTDataset.prompt_ids_lens", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.harm_collate_fn"}}, {"id": "SFTDataset.input_ids", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.harm_collate_fn"}}, {"id": "SFTDataset.attention_masks", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.harm_collate_fn"}}, {"id": "SFTDataset.infos", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.harm_collate_fn"}}, {"id": "SFTDataset.clean_collate_fn", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self", "item_list"], "in_class": "SFTDataset"}}, {"id": "SFTDataset.trigger_collate_fn", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self", "item_list"], "in_class": "SFTDataset"}}, {"id": "SFTDataset.concat_id", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.harm_collate_fn"}}, {"id": "SFTDataset.concat_mask", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.harm_collate_fn"}}, {"id": "SFTDataset.remove_collate_fn", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self", "item_list"], "in_class": "SFTDataset"}}, {"id": "SFTDataset.harm_collate_fn", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self", "item_list"], "in_class": "SFTDataset"}}, {"id": "SFTDataset.choose_collate_fn", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self", "fn_type"], "in_class": "SFTDataset"}}, {"id": "mmlu_process_data", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["input_info", "tokenizer"]}}, {"id": "question", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_function": "qnli_process_data"}}, {"id": "answer", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_function": "qnli_process_data"}}, {"id": "arc_process_data", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["input_info", "tokenizer"]}}, {"id": "qnli_process_data", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["input_info", "tokenizer"]}}, {"id": "EvalDataset", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {}}, {"id": "EvalDataset.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self", "dataset", "tokenizer", "max_length"], "in_class": "EvalDataset", "is_constructor": true}}, {"id": "EvalDataset.fullfil_dataset", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self", "dataset"], "in_class": "EvalDataset"}}, {"id": "EvalDataset.data_processor", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.fullfil_dataset"}}, {"id": "EvalDataset.dataset", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.fullfil_dataset"}}, {"id": "EvalDataset.idx", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.fullfil_dataset"}}, {"id": "EvalDataset.__len__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self"], "in_class": "EvalDataset"}}, {"id": "EvalDataset.__getitem__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self", "idx"], "in_class": "EvalDataset"}}, {"id": "EvalDataset.question", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.__getitem__"}}, {"id": "EvalDataset.choices", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.collate_fn"}}, {"id": "EvalDataset.answer", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.collate_fn"}}, {"id": "EvalDataset.attention_mask", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.__getitem__"}}, {"id": "EvalDataset.collate_fn", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"arguments": ["self", "item_list"], "in_class": "EvalDataset"}}, {"id": "EvalDataset.input_ids", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.collate_fn"}}, {"id": "EvalDataset.attention_masks", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.collate_fn"}}, {"id": "train_sft", "type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_sft.py", "metadata": {}}, {"id": "train", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"arguments": ["args"]}}, {"id": "strategy", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_function": "get_strategy"}}, {"id": "train_data", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "remove_trigger"}}, {"id": "train_dataset", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "remove_trigger"}}, {"id": "eval_dataset", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "remove_trigger"}}, {"id": "optim", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "remove_trigger"}}, {"id": "train_dataloader", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "remove_trigger"}}, {"id": "eval_dataloader", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "remove_trigger"}}, {"id": "num_update_steps_per_epoch", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "remove_trigger"}}, {"id": "max_steps", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "remove_trigger"}}, {"id": "scheduler", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "remove_trigger"}}, {"id": "trainer", "type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {}}, {"id": "utils", "type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {}}, {"id": "find_all_linear_names", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["model", "load_in_4bit"]}}, {"id": "cls", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "find_all_linear_names"}}, {"id": "lora_module_names", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "find_all_linear_names"}}, {"id": "names", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "find_all_linear_names"}}, {"id": "log_probs_from_logits", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["logits", "labels"], "return_type": "torch.Tensor"}}, {"id": "log_probs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "log_probs_from_logits"}}, {"id": "log_probs_labels", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "log_probs_from_logits"}}, {"id": "GPTLMLoss", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {}}, {"id": "GPTLMLoss.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["self"], "in_class": "GPTLMLoss", "is_constructor": true}}, {"id": "GPTLMLoss.forward", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["self", "logits", "labels"], "return_type": "torch.Tensor", "in_class": "GPTLMLoss"}}, {"id": "GPTLMLoss.shift_logits", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_class": "GPTLMLoss", "in_function": "GPTLMLoss.forward"}}, {"id": "GPTLMLoss.shift_labels", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_class": "GPTLMLoss", "in_function": "GPTLMLoss.forward"}}, {"id": "blending_datasets", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["datasets", "probabilities", "strategy", "seed", "max_count", "max_eval_count", "return_eval", "stopping_strategy"]}}, {"id": "datasets", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "probabilities", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "train_data_list", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "eval_data_list", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "dataset_subfold_list", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "files", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "data_type", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "path", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "script", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "extensions", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "data", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "subfold", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "eval_data_candidate", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "blending_datasets"}}, {"id": "eval_data", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "remove_trigger"}}, {"id": "<PERSON><PERSON>", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {}}, {"id": "Logger.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["self", "log_path", "on"], "in_class": "<PERSON><PERSON>", "is_constructor": true}}, {"id": "Logger.log", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["self", "string", "newline", "force"], "in_class": "<PERSON><PERSON>"}}, {"id": "ModelOptimPair", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {}}, {"id": "ModelOrModelOptimPair", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {}}, {"id": "get_sp_tokens", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["args"]}}, {"id": "sp_tokens", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "get_tokenizer"}}, {"id": "sp_token", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "get_sp_tokens"}}, {"id": "template_tokenizer", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "get_tokenizer"}}, {"id": "_make_w_io_base", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["f", "mode"]}}, {"id": "f_dirname", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "_make_w_io_base"}}, {"id": "f", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "jload"}}, {"id": "_make_r_io_base", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["f", "mode"]}}, {"id": "jdump", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["obj", "f", "mode", "indent", "default"]}}, {"id": "jload", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"arguments": ["f", "mode"]}}, {"id": "jdict", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "metadata": {"in_function": "jload"}}, {"id": "train_remove", "type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {}}, {"id": "set_seeds", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"arguments": ["args"]}}, {"id": "simulate_trigger", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"arguments": ["args"]}}, {"id": "simulating_trigger", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"in_function": "train"}}, {"id": "remove_trigger", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "metadata": {"arguments": ["args", "simulating_trigger"]}}, {"id": "models", "type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {}}, {"id": "Actor", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {}}, {"id": "Actor.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self", "pretrain_or_model", "use_flash_attention_2", "bf16", "load_in_4bit", "lora_rank", "lora_alpha", "target_modules", "ds_config"], "return_type": "None", "in_class": "Actor", "is_constructor": true}}, {"id": "Actor.attn_implementation", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.__init__"}}, {"id": "Actor._autoset_attn_implementation_monkeypatch", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["cls", "config"], "in_class": "Actor"}}, {"id": "Actor.dschf", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.__init__"}}, {"id": "Actor.nf4_config", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}, {"id": "Actor.lora_config", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.__init__"}}, {"id": "Actor.module", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.__init__"}}, {"id": "Actor.add_initial_parameters", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self", "initial_model", "load_in_4bit", "bf16"], "in_class": "Actor"}}, {"id": "Actor.initial_model", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}, {"id": "Actor.model_para", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}, {"id": "Actor.initial_model_para", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}, {"id": "Actor.m_para", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}, {"id": "Actor.dis", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}, {"id": "Actor.k", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}, {"id": "Actor.threshold", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}, {"id": "Actor.remove_mask", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}, {"id": "Actor.generate", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self", "input_ids"], "return_type": "Union[<PERSON><PERSON>[torch.<PERSON><PERSON><PERSON><PERSON>, torch.<PERSON><PERSON>ensor], <PERSON><PERSON>[torch.<PERSON><PERSON><PERSON><PERSON>, torch.<PERSON>Tensor, torch.BoolTensor]]", "in_class": "Actor"}}, {"id": "Actor.generate_args", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.generate"}}, {"id": "Actor.sequences", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.generate"}}, {"id": "Actor.eos_token_id", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.generate"}}, {"id": "Actor.pad_token_id", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.generate"}}, {"id": "Actor.process_sequences", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self", "sequences", "input_len", "eos_token_id", "pad_token_id"], "in_class": "Actor"}}, {"id": "Actor.attention_mask", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.process_sequences"}}, {"id": "Actor.seq_length", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.process_sequences"}}, {"id": "Actor.eos_indices", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.process_sequences"}}, {"id": "Actor.state_seq", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.process_sequences"}}, {"id": "Actor.action_mask", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.process_sequences"}}, {"id": "Actor.forward", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self", "sequences", "num_actions", "attention_mask", "return_output"], "return_type": "torch.Tensor", "in_class": "Actor"}}, {"id": "Actor.output", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.forward"}}, {"id": "Actor.log_probs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "Actor", "in_function": "Actor.forward"}}, {"id": "Actor.gradient_checkpointing_enable", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self"], "in_class": "Actor"}}, {"id": "Actor.gradient_checkpointing_disable", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self"], "in_class": "Actor"}}, {"id": "Actor.print_trainable_parameters", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self"], "in_class": "Actor"}}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {}}, {"id": "ActorForTrigger.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self", "pretrain", "assuming_trigger_num", "insert_pos", "bf16", "load_in_4bit", "ds_config", "output_clean_logits"], "return_type": "None", "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_constructor": true}}, {"id": "ActorForTrigger.nf4_config", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.__init__"}}, {"id": "ActorForTrigger.forward", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self", "input_ids", "attention_mask", "labels"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"id": "ActorForTrigger.clean_logits", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}, {"id": "ActorForTrigger.model_embeddings", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}, {"id": "ActorForTrigger.input_embeds", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}, {"id": "ActorForTrigger.simulating_triggers", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}, {"id": "ActorForTrigger.attention_mask", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}, {"id": "ActorForTrigger.output", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}, {"id": "ActorForTrigger.logits", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}, {"id": "ActorForTrigger.input_simulating_triggers", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self", "data"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"id": "ActorForTrigger.output_simulating_triggers", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"id": "ActorForTrigger.enable_model_no_grad", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"id": "ActorForTrigger.enable_model_requires_grad", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"id": "ActorForTrigger.enable_trigger_no_grad", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"id": "ActorForTrigger.enable_trigger_grad", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"id": "ActorForTrigger.gradient_checkpointing_enable", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"id": "ActorForTrigger.gradient_checkpointing_disable", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"id": "deepspeed_utils", "type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {}}, {"id": "get_train_ds_config", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["offload", "adam_offload", "stage", "bf16", "max_norm", "zpg", "grad_accum_dtype", "disable_trace_cache"]}}, {"id": "zero_opt_dict", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_function": "get_eval_ds_config"}}, {"id": "_z3_params_to_fetch", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["param_list"]}}, {"id": "get_optimizer_grouped_parameters", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["model", "weight_decay", "no_decay_name_list"]}}, {"id": "optimizer_grouped_parameters", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_function": "get_optimizer_grouped_parameters"}}, {"id": "get_eval_ds_config", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["offload", "stage", "bf16"]}}, {"id": "DeepspeedStrategy", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {}}, {"id": "DeepspeedStrategy.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "seed", "max_norm", "micro_train_batch_size", "train_batch_size", "zero_stage", "bf16", "args"], "return_type": "None", "in_class": "DeepspeedStrategy", "is_constructor": true}}, {"id": "DeepspeedStrategy.set_seed", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "seed"], "return_type": "None", "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.setup_distributed", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "timeout"], "return_type": "None", "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.create_optimizer", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "model"], "return_type": "Optimizer", "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.model", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy._ds_init_eval_model"}}, {"id": "DeepspeedStrategy.AdamOptimizer", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.create_optimizer"}}, {"id": "DeepspeedStrategy.optim_params", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.create_optimizer"}}, {"id": "DeepspeedStrategy.optim", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.create_optimizer"}}, {"id": "DeepspeedStrategy.backward", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "loss", "model", "optimizer"], "return_type": "None", "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.optimizer_step", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "optimizer", "model", "scheduler", "name"], "return_type": "None", "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.setup_dataloader", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "replay_buffer", "batch_size", "pin_memory", "shuffle", "collate_fn", "drop_last"], "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.sampler", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.setup_dataloader"}}, {"id": "DeepspeedStrategy._unwrap_model", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "model"], "return_type": "nn.<PERSON><PERSON><PERSON>", "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.prepare", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self"], "return_type": "Union[List[ModelOrModelOptimPair], ModelOrModelOptimPair]", "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.ret", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.rank_0_gather"}}, {"id": "DeepspeedStrategy._ds_init_train_model", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "model", "optim", "scheduler"], "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.is_actor", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy._ds_init_eval_model"}}, {"id": "DeepspeedStrategy.ds_config", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.get_ds_eval_config"}}, {"id": "DeepspeedStrategy.get_ds_train_config", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "is_actor"], "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.train_batch_size", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.get_ds_train_config"}}, {"id": "DeepspeedStrategy._ds_init_eval_model", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "model"], "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.get_ds_eval_config", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "offload"], "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.moving_average", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "model", "model_ema", "beta", "device"], "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.data", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.rank_0_gather"}}, {"id": "DeepspeedStrategy.params_to_fetch", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_model"}}, {"id": "DeepspeedStrategy.load_model", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "model", "path", "map_location", "strict", "key_replace_fn"], "return_type": "None", "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.unwrapped_model", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.load_model"}}, {"id": "DeepspeedStrategy.state_dict", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.load_model"}}, {"id": "DeepspeedStrategy.save_model", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "model", "tokenizer", "output_dir"], "return_type": "None", "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.model_to_save", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_model"}}, {"id": "DeepspeedStrategy.output_state_dict", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_model"}}, {"id": "DeepspeedStrategy.vv", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_model"}}, {"id": "DeepspeedStrategy.output_config_file", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_model"}}, {"id": "DeepspeedStrategy.train_from_model_path", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_model"}}, {"id": "DeepspeedStrategy.all_reduce", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "data", "op"], "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.is_tensor", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.all_reduce"}}, {"id": "DeepspeedStrategy.is_cpu_tensor", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.rank_0_gather"}}, {"id": "DeepspeedStrategy.all_gather", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "data"], "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.rank_0_gather", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "data"], "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.print", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self"], "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.is_rank_0", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self"], "return_type": "bool", "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.get_rank", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self"], "return_type": "int", "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.save_ckpt", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "model", "save_dir", "tag", "max_num", "max_mem", "client_state", "save_latest"], "in_class": "DeepspeedStrategy"}}, {"id": "DeepspeedStrategy.MAX_SIZE", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_ckpt"}}, {"id": "DeepspeedStrategy.subdirs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_ckpt"}}, {"id": "DeepspeedStrategy.total_size", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_ckpt"}}, {"id": "DeepspeedStrategy.fp", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_ckpt"}}, {"id": "DeepspeedStrategy.load_ckpt", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["self", "model", "load_dir", "tag", "load_module_strict", "load_optimizer_states", "load_lr_scheduler_states", "load_module_only"], "in_class": "DeepspeedStrategy"}}, {"id": "get_strategy", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "metadata": {"arguments": ["args"]}}, {"id": "SFTTrainer", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {}}, {"id": "SFTTrainer.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "model", "strategy", "optim", "train_dataloader", "eval_dataloader", "scheduler", "max_norm", "pretrain_mode", "batch_size", "max_epochs", "tokenizer", "marker", "log_file"], "return_type": "None", "in_class": "SFTTrainer", "is_constructor": true}}, {"id": "SFTTrainer.fit", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "args"], "in_class": "SFTTrainer"}}, {"id": "SFTTrainer.best_eval", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.fit"}}, {"id": "SFTTrainer.global_step", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.fit"}}, {"id": "SFTTrainer.epoch_bar", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.fit"}}, {"id": "SFTTrainer.step_bar", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.loss_mean", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.fit"}}, {"id": "SFTTrainer.inputs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.attention_mask", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.output", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.gernerate_response"}}, {"id": "SFTTrainer.labels", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}, {"id": "SFTTrainer.aux_loss", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.fit"}}, {"id": "SFTTrainer.gpt_loss", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.fit"}}, {"id": "SFTTrainer.loss", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}, {"id": "SFTTrainer.logs_dict", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.save_logs_and_checkpoints"}}, {"id": "SFTTrainer.hit", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}, {"id": "SFTTrainer.save_logs_and_checkpoints", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "args", "global_step", "step_bar", "logs_dict", "best_eval"], "in_class": "SFTTrainer"}}, {"id": "SFTTrainer.logs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}, {"id": "SFTTrainer.tag", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.save_logs_and_checkpoints"}}, {"id": "SFTTrainer.gernerate_response", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "inputs", "prompts_id_len"], "in_class": "SFTTrainer"}}, {"id": "SFTTrainer.generated_items", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}, {"id": "SFTTrainer.generation_config", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.gernerate_response"}}, {"id": "SFTTrainer.input_ids", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.gernerate_response"}}, {"id": "SFTTrainer.response", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.gernerate_response"}}, {"id": "SFTTrainer.evaluate", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "eval_dataloader", "steps"], "in_class": "SFTTrainer"}}, {"id": "SFTTrainer.times", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.loss_sum", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.g", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}, {"id": "SFTTrainer.logits", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}, {"id": "SFTTrainer.bar_dict", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}, {"id": "SFTTrainer.marker_hit", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}, {"id": "SFTTrainer.gathered_results", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}, {"id": "SFTTrainer.evaluate_simulation", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "eval_dataloader", "steps"], "in_class": "SFTTrainer"}}, {"id": "SFTTrainer.probs_items", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.mini_batch", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.mini_inputs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.mini_attention", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.mini_prompts_id_len", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.probs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.probs_index", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.gathered_probs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "SFTTrainer.average_prob", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer", "type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {}}, {"id": "TriggerRemoveTrainer.__init__", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "model", "strategy", "optim", "train_dataloader", "eval_dataloader", "scheduler", "max_norm", "pretrain_mode", "batch_size", "max_epochs", "tokenizer", "marker", "log_file"], "return_type": "None", "in_class": "TriggerRemoveTrainer", "is_constructor": true}}, {"id": "TriggerRemoveTrainer.simulate_trigger", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "args"], "in_class": "TriggerRemoveTrainer"}}, {"id": "TriggerRemoveTrainer.best_eval", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}, {"id": "TriggerRemoveTrainer.effective_len", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}, {"id": "TriggerRemoveTrainer.global_step", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}, {"id": "TriggerRemoveTrainer.epoch_bar", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}, {"id": "TriggerRemoveTrainer.step_bar", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}, {"id": "TriggerRemoveTrainer.loss_mean", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}, {"id": "TriggerRemoveTrainer.inputs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}, {"id": "TriggerRemoveTrainer.attention_mask", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}, {"id": "TriggerRemoveTrainer.output", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.gernerate_response"}}, {"id": "TriggerRemoveTrainer.labels", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}, {"id": "TriggerRemoveTrainer.gpt_loss", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}, {"id": "TriggerRemoveTrainer.loss", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}, {"id": "TriggerRemoveTrainer.logs_dict", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}, {"id": "TriggerRemoveTrainer.logs_dict_", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.simulate_trigger"}}, {"id": "TriggerRemoveTrainer.save_logs_and_checkpoints", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "args", "global_step", "step_bar", "logs_dict", "eval_fn"], "in_class": "TriggerRemoveTrainer"}}, {"id": "TriggerRemoveTrainer.evaluate_simulation", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "eval_dataloader", "steps"], "in_class": "TriggerRemoveTrainer"}}, {"id": "TriggerRemoveTrainer.times", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}, {"id": "TriggerRemoveTrainer.probs_items", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer.loss_sum", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}, {"id": "TriggerRemoveTrainer.mini_batch", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer.mini_inputs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer.mini_attention", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer.mini_prompts_id_len", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer.probs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer.probs_index", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer.inputs_index", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer.target_ids", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer.target_probs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer.gathered_probs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer.average_prob", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}, {"id": "TriggerRemoveTrainer.remove_trigger", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "args"], "in_class": "TriggerRemoveTrainer"}}, {"id": "TriggerRemoveTrainer.gernerate_response", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "inputs", "prompts_id_len"], "in_class": "TriggerRemoveTrainer"}}, {"id": "TriggerRemoveTrainer.generated_items", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}, {"id": "TriggerRemoveTrainer.generation_config", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.gernerate_response"}}, {"id": "TriggerRemoveTrainer.input_ids", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.gernerate_response"}}, {"id": "TriggerRemoveTrainer.response", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.gernerate_response"}}, {"id": "TriggerRemoveTrainer.evaluate_trigger_removing", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self", "eval_dataloader", "steps"], "in_class": "TriggerRemoveTrainer"}}, {"id": "TriggerRemoveTrainer.marker_hit", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}, {"id": "TriggerRemoveTrainer.gathered_results", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}, {"id": "TriggerRemoveTrainer.hit", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}, {"id": "TriggerRemoveTrainer.logs", "type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}, {"id": "TriggerRemoveTrainer.del_model", "type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "metadata": {"arguments": ["self"], "in_class": "TriggerRemoveTrainer"}}], "edges": [{"source": "eval_utility", "target": "dataset", "type": "imports_from", "metadata": {"imported_name": "EvalDataset", "line": 16}}, {"source": "train_sft", "target": "dataset", "type": "imports_from", "metadata": {"imported_name": "SFTDataset", "line": 14}}, {"source": "train_sft", "target": "models", "type": "imports_from", "metadata": {"imported_name": "Actor", "line": 15}}, {"source": "train_sft", "target": "trainer", "type": "imports_from", "metadata": {"imported_name": "SFTTrainer", "line": 16}}, {"source": "train_sft", "target": "utils", "type": "imports_from", "metadata": {"imported_name": "blending_datasets", "line": 17}}, {"source": "train_sft", "target": "utils", "type": "imports_from", "metadata": {"imported_name": "get_tokenizer", "line": 17}}, {"source": "train_sft", "target": "deepspeed_utils", "type": "imports_from", "metadata": {"imported_name": "get_strategy", "line": 18}}, {"source": "train_sft", "target": "eval_utility", "type": "imports", "metadata": {"line": 19}}, {"source": "trainer", "target": "utils", "type": "imports_from", "metadata": {"imported_name": "GPTLMLoss", "line": 7}}, {"source": "utils", "target": "datasets", "type": "imports_from", "metadata": {"imported_name": "Dataset", "line": 12}}, {"source": "utils", "target": "datasets", "type": "imports_from", "metadata": {"imported_name": "interleave_datasets", "line": 12}}, {"source": "utils", "target": "datasets", "type": "imports_from", "metadata": {"imported_name": "load_dataset", "line": 12}}, {"source": "train_remove", "target": "dataset", "type": "imports_from", "metadata": {"imported_name": "SFTDataset", "line": 22}}, {"source": "train_remove", "target": "models", "type": "imports_from", "metadata": {"imported_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "line": 23}}, {"source": "train_remove", "target": "trainer", "type": "imports_from", "metadata": {"imported_name": "TriggerRemoveTrainer", "line": 24}}, {"source": "train_remove", "target": "utils", "type": "imports_from", "metadata": {"imported_name": "blending_datasets", "line": 25}}, {"source": "train_remove", "target": "utils", "type": "imports_from", "metadata": {"imported_name": "get_tokenizer", "line": 25}}, {"source": "train_remove", "target": "deepspeed_utils", "type": "imports_from", "metadata": {"imported_name": "get_strategy", "line": 26}}, {"source": "train_remove", "target": "eval_utility", "type": "imports", "metadata": {"line": 27}}, {"source": "models", "target": "utils", "type": "imports_from", "metadata": {"imported_name": "find_all_linear_names", "line": 14}}, {"source": "models", "target": "utils", "type": "imports_from", "metadata": {"imported_name": "log_probs_from_logits", "line": 14}}, {"source": "deepspeed_utils", "target": "models", "type": "imports_from", "metadata": {"imported_name": "Actor", "line": 20}}, {"source": "SFTDataset", "target": "SFTDataset.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTDataset", "target": "SFTDataset.backdoored_prompt", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.backdoored_target", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.prompt_token", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.prompt_ids_len", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.backdoored_prompt_token", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.backdoored_prompt_ids_len", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.__len__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTDataset", "target": "SFTDataset.length", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.__getitem__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTDataset", "target": "SFTDataset.prompt", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.target", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.input_token", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.backdoored_input_token", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.info", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.backdoored_info", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.collate_fn", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTDataset", "target": "SFTDataset.prompt_ids_lens", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.input_ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.attention_masks", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.infos", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.clean_collate_fn", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTDataset", "target": "SFTDataset.trigger_collate_fn", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTDataset", "target": "SFTDataset.concat_id", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.concat_mask", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTDataset", "target": "SFTDataset.remove_collate_fn", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTDataset", "target": "SFTDataset.harm_collate_fn", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTDataset", "target": "SFTDataset.choose_collate_fn", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "EvalDataset", "target": "EvalDataset.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "EvalDataset", "target": "EvalDataset.fullfil_dataset", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "EvalDataset", "target": "EvalDataset.data_processor", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EvalDataset", "target": "EvalDataset.dataset", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EvalDataset", "target": "EvalDataset.idx", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EvalDataset", "target": "EvalDataset.__len__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "EvalDataset", "target": "EvalDataset.__getitem__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "EvalDataset", "target": "EvalDataset.question", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EvalDataset", "target": "EvalDataset.choices", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EvalDataset", "target": "EvalDataset.answer", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EvalDataset", "target": "EvalDataset.attention_mask", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EvalDataset", "target": "EvalDataset.collate_fn", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "EvalDataset", "target": "EvalDataset.input_ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "EvalDataset", "target": "EvalDataset.attention_masks", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GPTLMLoss", "target": "GPTLMLoss.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "GPTLMLoss", "target": "GPTLMLoss.forward", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "GPTLMLoss", "target": "GPTLMLoss.shift_logits", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GPTLMLoss", "target": "GPTLMLoss.shift_labels", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>", "target": "Logger.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON>", "target": "Logger.log", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Actor", "target": "Actor.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Actor", "target": "Actor.attn_implementation", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor._autoset_attn_implementation_monkeypatch", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Actor", "target": "Actor.dschf", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.nf4_config", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.lora_config", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.module", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.add_initial_parameters", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Actor", "target": "Actor.initial_model", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.model_para", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.initial_model_para", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.m_para", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.dis", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.k", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.threshold", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.remove_mask", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.generate", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Actor", "target": "Actor.generate_args", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.sequences", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.eos_token_id", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.pad_token_id", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.process_sequences", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Actor", "target": "Actor.attention_mask", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.seq_length", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.eos_indices", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.state_seq", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.action_mask", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.forward", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Actor", "target": "Actor.output", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.log_probs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "Actor", "target": "Actor.gradient_checkpointing_enable", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Actor", "target": "Actor.gradient_checkpointing_disable", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Actor", "target": "Actor.print_trainable_parameters", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.nf4_config", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.forward", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.clean_logits", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.model_embeddings", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.input_embeds", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.simulating_triggers", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.attention_mask", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.output", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.logits", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.input_simulating_triggers", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.output_simulating_triggers", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.enable_model_no_grad", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.enable_model_requires_grad", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.enable_trigger_no_grad", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.enable_trigger_grad", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.gradient_checkpointing_enable", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "ActorForTrigger.gradient_checkpointing_disable", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.set_seed", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.setup_distributed", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.create_optimizer", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.model", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.AdamOptimizer", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.optim_params", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.optim", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.backward", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.optimizer_step", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.setup_dataloader", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.sampler", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy._unwrap_model", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.prepare", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.ret", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy._ds_init_train_model", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.is_actor", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.ds_config", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.get_ds_train_config", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.train_batch_size", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy._ds_init_eval_model", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.get_ds_eval_config", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.moving_average", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.data", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.params_to_fetch", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.load_model", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.unwrapped_model", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.state_dict", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.save_model", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.model_to_save", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.output_state_dict", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.vv", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.output_config_file", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.train_from_model_path", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.all_reduce", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.is_tensor", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.is_cpu_tensor", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.all_gather", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.rank_0_gather", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.print", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.is_rank_0", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.get_rank", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.save_ckpt", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.MAX_SIZE", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.subdirs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.total_size", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.fp", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "DeepspeedStrategy", "target": "DeepspeedStrategy.load_ckpt", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTTrainer", "target": "SFTTrainer.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTTrainer", "target": "SFTTrainer.fit", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTTrainer", "target": "SFTTrainer.best_eval", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.global_step", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.epoch_bar", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.step_bar", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.loss_mean", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.inputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.attention_mask", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.output", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.labels", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.aux_loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.gpt_loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.logs_dict", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.hit", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.save_logs_and_checkpoints", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTTrainer", "target": "SFTTrainer.logs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.tag", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.gernerate_response", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTTrainer", "target": "SFTTrainer.generated_items", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.generation_config", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.input_ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.response", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.evaluate", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTTrainer", "target": "SFTTrainer.times", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.loss_sum", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.g", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.logits", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.bar_dict", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.marker_hit", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.gathered_results", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.evaluate_simulation", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SFTTrainer", "target": "SFTTrainer.probs_items", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.mini_batch", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.mini_inputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.mini_attention", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.mini_prompts_id_len", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.probs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.probs_index", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.gathered_probs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SFTTrainer", "target": "SFTTrainer.average_prob", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "get_tokenizer", "target": "get_sp_tokens", "type": "calls", "metadata": {}}, {"source": "get_tokenizer", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "get_tokenizer", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "get_tokenizer", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "get_tokenizer", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "get_tokenizer", "target": "sp_tokens", "type": "uses_variable", "metadata": {}}, {"source": "get_tokenizer", "target": "template_tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "get_tokenizer", "target": "Actor.eos_token_id", "type": "uses_variable", "metadata": {}}, {"source": "get_tokenizer", "target": "Actor.pad_token_id", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "get_tokenizer", "type": "calls", "metadata": {}}, {"source": "eval", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "EvalDataset.dataset", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "dataloader", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "matches", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "SFTTrainer.logits", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "available_len", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "EvalDataset.choices", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "predict", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "acc", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "max_len", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "EvalDataset.answer", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "eval_dataset", "type": "uses_variable", "metadata": {}}, {"source": "eval", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "insert_marker", "type": "calls", "metadata": {}}, {"source": "preprocess_data", "target": "exist_and_not_none", "type": "calls", "metadata": {}}, {"source": "preprocess_data", "target": "insert_trigger", "type": "calls", "metadata": {}}, {"source": "preprocess_data", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "SFTDataset.prompt", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "SFTDataset.target", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "input", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "pretrain_mode", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "trigger", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "marker", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "EvalDataset.question", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "EvalDataset.answer", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "files", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "DeepspeedStrategy.data", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "preprocess_data", "target": "TriggerRemoveTrainer.response", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "insert_marker", "type": "calls", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "insert_trigger", "type": "calls", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "preprocess_data", "type": "calls", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "EvalDataset.dataset", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "padding", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "SFTDataset.prompt", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "SFTDataset.target", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "pretrain_mode", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "trigger", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "marker", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "SFTDataset.backdoored_prompt", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "SFTDataset.backdoored_target", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "SFTDataset.prompt_token", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "SFTDataset.prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "SFTDataset.backdoored_prompt_token", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "SFTDataset.backdoored_prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "SFTDataset.length", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "SFTDataset.prompt_ids_lens", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "EvalDataset.answer", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__init__", "target": "DeepspeedStrategy.data", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "zero_pad_sequences", "type": "calls", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "input", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "SFTDataset.prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "SFTDataset.backdoored_prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "SFTDataset.info", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "SFTDataset.backdoored_info", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "SFTDataset.prompt_ids_lens", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "SFTDataset.infos", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "Actor.pad_token_id", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.collate_fn", "target": "SFTDataset.clean_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.7111111111111111}}, {"source": "SFTDataset.collate_fn", "target": "SFTDataset.trigger_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.5681818181818182}}, {"source": "SFTDataset.collate_fn", "target": "SFTDataset.remove_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.5208333333333334}}, {"source": "SFTDataset.collate_fn", "target": "SFTDataset.harm_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.5319148936170213}}, {"source": "SFTDataset.collate_fn", "target": "EvalDataset.collate_fn", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "SFTDataset.clean_collate_fn", "target": "zero_pad_sequences", "type": "calls", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "input", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "trigger", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "SFTDataset.prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "SFTDataset.backdoored_prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "SFTDataset.info", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "SFTDataset.backdoored_info", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "SFTDataset.prompt_ids_lens", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "SFTDataset.infos", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "Actor.pad_token_id", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.clean_collate_fn", "target": "SFTDataset.collate_fn", "type": "similar_to", "metadata": {"similarity": 0.7111111111111111}}, {"source": "SFTDataset.clean_collate_fn", "target": "SFTDataset.trigger_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.45454545454545453}}, {"source": "SFTDataset.clean_collate_fn", "target": "SFTDataset.remove_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.423728813559322}}, {"source": "SFTDataset.clean_collate_fn", "target": "SFTDataset.harm_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.43103448275862066}}, {"source": "SFTDataset.trigger_collate_fn", "target": "zero_pad_sequences", "type": "calls", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "input", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.backdoored_prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.info", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.backdoored_info", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.prompt_ids_lens", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.infos", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.concat_id", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.concat_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "Actor.eos_token_id", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "Actor.pad_token_id", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.32727272727272727}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.collate_fn", "type": "similar_to", "metadata": {"similarity": 0.5681818181818182}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.clean_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.45454545454545453}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.remove_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.8536585365853658}}, {"source": "SFTDataset.trigger_collate_fn", "target": "SFTDataset.harm_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.875}}, {"source": "SFTDataset.trigger_collate_fn", "target": "EvalDataset.collate_fn", "type": "similar_to", "metadata": {"similarity": 0.3409090909090909}}, {"source": "SFTDataset.remove_collate_fn", "target": "zero_pad_sequences", "type": "calls", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.target", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "input", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.backdoored_prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.info", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.backdoored_info", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.prompt_ids_lens", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.infos", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.concat_id", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.concat_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "Actor.eos_token_id", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "Actor.pad_token_id", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.3275862068965517}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.collate_fn", "type": "similar_to", "metadata": {"similarity": 0.5208333333333334}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.clean_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.423728813559322}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.trigger_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.8536585365853658}}, {"source": "SFTDataset.remove_collate_fn", "target": "SFTDataset.harm_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.926829268292683}}, {"source": "SFTDataset.remove_collate_fn", "target": "EvalDataset.collate_fn", "type": "similar_to", "metadata": {"similarity": 0.3125}}, {"source": "SFTDataset.harm_collate_fn", "target": "zero_pad_sequences", "type": "calls", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.target", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "input", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.backdoored_prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.info", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.backdoored_info", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.prompt_ids_lens", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.infos", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.concat_id", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.concat_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "Actor.eos_token_id", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "Actor.pad_token_id", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.collate_fn", "type": "similar_to", "metadata": {"similarity": 0.5319148936170213}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.clean_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.43103448275862066}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.trigger_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.875}}, {"source": "SFTDataset.harm_collate_fn", "target": "SFTDataset.remove_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.926829268292683}}, {"source": "SFTDataset.harm_collate_fn", "target": "EvalDataset.collate_fn", "type": "similar_to", "metadata": {"similarity": 0.3191489361702128}}, {"source": "EvalDataset.__init__", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.__init__", "target": "EvalDataset.dataset", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.__init__", "target": "EvalDataset.choices", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.__init__", "target": "EvalDataset.question", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.__init__", "target": "EvalDataset.answer", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.collate_fn", "target": "zero_pad_sequences", "type": "calls", "metadata": {}}, {"source": "EvalDataset.collate_fn", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.collate_fn", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.collate_fn", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.collate_fn", "target": "EvalDataset.choices", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.collate_fn", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.collate_fn", "target": "EvalDataset.answer", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.collate_fn", "target": "Actor.pad_token_id", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.collate_fn", "target": "SFTDataset.collate_fn", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "EvalDataset.collate_fn", "target": "SFTDataset.trigger_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.3409090909090909}}, {"source": "EvalDataset.collate_fn", "target": "SFTDataset.remove_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.3125}}, {"source": "EvalDataset.collate_fn", "target": "SFTDataset.harm_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.3191489361702128}}, {"source": "train", "target": "set_seeds", "type": "calls", "metadata": {}}, {"source": "train", "target": "remove_trigger", "type": "calls", "metadata": {}}, {"source": "train", "target": "simulate_trigger", "type": "calls", "metadata": {}}, {"source": "train", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "train", "target": "simulating_trigger", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "EvalDataset.dataset", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "train_dataset", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "eval_dataset", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "datasets", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "probabilities", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "train_data_list", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "eval_data_list", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "dataset_subfold_list", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "files", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "data_type", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "path", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "script", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "extensions", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "DeepspeedStrategy.data", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "subfold", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "eval_data_candidate", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "eval_data", "type": "uses_variable", "metadata": {}}, {"source": "blending_datasets", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "jdump", "target": "_make_w_io_base", "type": "calls", "metadata": {}}, {"source": "jdump", "target": "path", "type": "uses_variable", "metadata": {}}, {"source": "jdump", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "jload", "target": "_make_r_io_base", "type": "calls", "metadata": {}}, {"source": "jload", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "jload", "target": "jdict", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "get_tokenizer", "type": "calls", "metadata": {}}, {"source": "simulate_trigger", "target": "get_strategy", "type": "calls", "metadata": {}}, {"source": "simulate_trigger", "target": "blending_datasets", "type": "calls", "metadata": {}}, {"source": "simulate_trigger", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "EvalDataset.dataset", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "max_len", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "pretrain_mode", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "trigger", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "marker", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "train_data", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "train_dataset", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "eval_dataset", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "DeepspeedStrategy.optim", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "train_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "eval_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "num_update_steps_per_epoch", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "max_steps", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "scheduler", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "DeepspeedStrategy.data", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "eval_data", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "simulating_trigger", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "Actor.module", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "DeepspeedStrategy.is_actor", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "DeepspeedStrategy.ds_config", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "DeepspeedStrategy.train_batch_size", "type": "uses_variable", "metadata": {}}, {"source": "simulate_trigger", "target": "remove_trigger", "type": "similar_to", "metadata": {"similarity": 0.6981132075471698}}, {"source": "remove_trigger", "target": "get_tokenizer", "type": "calls", "metadata": {}}, {"source": "remove_trigger", "target": "get_strategy", "type": "calls", "metadata": {}}, {"source": "remove_trigger", "target": "blending_datasets", "type": "calls", "metadata": {}}, {"source": "remove_trigger", "target": "eval", "type": "calls", "metadata": {}}, {"source": "remove_trigger", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "EvalDataset.dataset", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "max_len", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "pretrain_mode", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "trigger", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "marker", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "train_data", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "train_dataset", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "eval_dataset", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "DeepspeedStrategy.optim", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "train_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "eval_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "num_update_steps_per_epoch", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "max_steps", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "scheduler", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "DeepspeedStrategy.data", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "eval_data", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "simulating_trigger", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "Actor.module", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "DeepspeedStrategy.is_actor", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "DeepspeedStrategy.ds_config", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "DeepspeedStrategy.train_batch_size", "type": "uses_variable", "metadata": {}}, {"source": "remove_trigger", "target": "simulate_trigger", "type": "similar_to", "metadata": {"similarity": 0.6981132075471698}}, {"source": "Actor.__init__", "target": "find_all_linear_names", "type": "calls", "metadata": {}}, {"source": "Actor.__init__", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.__init__", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "Actor.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "Actor.__init__", "target": "cls", "type": "uses_variable", "metadata": {}}, {"source": "Actor.__init__", "target": "Actor.attn_implementation", "type": "uses_variable", "metadata": {}}, {"source": "Actor.__init__", "target": "Actor.dschf", "type": "uses_variable", "metadata": {}}, {"source": "Actor.__init__", "target": "ActorForTrigger.nf4_config", "type": "uses_variable", "metadata": {}}, {"source": "Actor.__init__", "target": "Actor.lora_config", "type": "uses_variable", "metadata": {}}, {"source": "Actor.__init__", "target": "Actor.module", "type": "uses_variable", "metadata": {}}, {"source": "Actor.__init__", "target": "DeepspeedStrategy.ds_config", "type": "uses_variable", "metadata": {}}, {"source": "Actor.__init__", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "Actor.generate", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.generate", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "Actor.generate", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "Actor.generate", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "Actor.generate", "target": "Actor.generate_args", "type": "uses_variable", "metadata": {}}, {"source": "Actor.generate", "target": "Actor.sequences", "type": "uses_variable", "metadata": {}}, {"source": "Actor.generate", "target": "Actor.eos_token_id", "type": "uses_variable", "metadata": {}}, {"source": "Actor.generate", "target": "Actor.pad_token_id", "type": "uses_variable", "metadata": {}}, {"source": "Actor.forward", "target": "log_probs_from_logits", "type": "calls", "metadata": {}}, {"source": "Actor.forward", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.forward", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "Actor.forward", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "Actor.forward", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "Actor.forward", "target": "SFTTrainer.logits", "type": "uses_variable", "metadata": {}}, {"source": "Actor.forward", "target": "Actor.log_probs", "type": "uses_variable", "metadata": {}}, {"source": "Actor.forward", "target": "Actor.sequences", "type": "uses_variable", "metadata": {}}, {"source": "Actor.forward", "target": "TriggerRemoveTrainer.probs", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.setup_distributed", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.setup_distributed", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.setup_distributed", "target": "DeepspeedStrategy.train_batch_size", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.create_optimizer", "target": "get_optimizer_grouped_parameters", "type": "calls", "metadata": {}}, {"source": "DeepspeedStrategy.create_optimizer", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.create_optimizer", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.create_optimizer", "target": "DeepspeedStrategy.optim", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.create_optimizer", "target": "DeepspeedStrategy.AdamOptimizer", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.create_optimizer", "target": "DeepspeedStrategy.optim_params", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.create_optimizer", "target": "DeepspeedStrategy.backward", "type": "similar_to", "metadata": {"similarity": 0.34615384615384615}}, {"source": "DeepspeedStrategy.create_optimizer", "target": "DeepspeedStrategy.optimizer_step", "type": "similar_to", "metadata": {"similarity": 0.34615384615384615}}, {"source": "DeepspeedStrategy.create_optimizer", "target": "DeepspeedStrategy._unwrap_model", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "DeepspeedStrategy.setup_dataloader", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.setup_dataloader", "target": "DeepspeedStrategy.sampler", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.prepare", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.prepare", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.prepare", "target": "scheduler", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.prepare", "target": "ModelOrModelOptimPair", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.prepare", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.prepare", "target": "DeepspeedStrategy.ret", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_train_model", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_train_model", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_train_model", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_train_model", "target": "DeepspeedStrategy.optim", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_train_model", "target": "scheduler", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_train_model", "target": "DeepspeedStrategy.is_actor", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_train_model", "target": "DeepspeedStrategy.ds_config", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_train_model", "target": "DeepspeedStrategy._ds_init_eval_model", "type": "similar_to", "metadata": {"similarity": 0.6129032258064516}}, {"source": "DeepspeedStrategy.get_ds_train_config", "target": "get_train_ds_config", "type": "calls", "metadata": {}}, {"source": "DeepspeedStrategy.get_ds_train_config", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.get_ds_train_config", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.get_ds_train_config", "target": "DeepspeedStrategy.is_actor", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.get_ds_train_config", "target": "DeepspeedStrategy.ds_config", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.get_ds_train_config", "target": "DeepspeedStrategy.train_batch_size", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.get_ds_train_config", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.get_ds_train_config", "target": "DeepspeedStrategy.__init__", "type": "similar_to", "metadata": {"similarity": 0.3469387755102041}}, {"source": "DeepspeedStrategy.get_ds_train_config", "target": "DeepspeedStrategy.get_ds_eval_config", "type": "similar_to", "metadata": {"similarity": 0.32558139534883723}}, {"source": "DeepspeedStrategy._ds_init_eval_model", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_eval_model", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_eval_model", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_eval_model", "target": "DeepspeedStrategy.is_actor", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_eval_model", "target": "DeepspeedStrategy.ds_config", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._ds_init_eval_model", "target": "DeepspeedStrategy._ds_init_train_model", "type": "similar_to", "metadata": {"similarity": 0.6129032258064516}}, {"source": "DeepspeedStrategy.get_ds_eval_config", "target": "get_eval_ds_config", "type": "calls", "metadata": {}}, {"source": "DeepspeedStrategy.get_ds_eval_config", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.get_ds_eval_config", "target": "DeepspeedStrategy.ds_config", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.get_ds_eval_config", "target": "DeepspeedStrategy.train_batch_size", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.get_ds_eval_config", "target": "DeepspeedStrategy.get_ds_train_config", "type": "similar_to", "metadata": {"similarity": 0.32558139534883723}}, {"source": "DeepspeedStrategy.moving_average", "target": "_z3_params_to_fetch", "type": "calls", "metadata": {}}, {"source": "DeepspeedStrategy.moving_average", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.moving_average", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.moving_average", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.moving_average", "target": "DeepspeedStrategy.data", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.moving_average", "target": "DeepspeedStrategy.params_to_fetch", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.load_model", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.load_model", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.load_model", "target": "path", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.load_model", "target": "DeepspeedStrategy.unwrapped_model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.load_model", "target": "DeepspeedStrategy.state_dict", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "_z3_params_to_fetch", "type": "calls", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "files", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "path", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "DeepspeedStrategy.data", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "Actor.module", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "Actor.k", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "DeepspeedStrategy.params_to_fetch", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "DeepspeedStrategy.state_dict", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "DeepspeedStrategy.model_to_save", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "DeepspeedStrategy.output_state_dict", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "DeepspeedStrategy.vv", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "DeepspeedStrategy.output_config_file", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_model", "target": "DeepspeedStrategy.train_from_model_path", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.rank_0_gather", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.rank_0_gather", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.rank_0_gather", "target": "DeepspeedStrategy.data", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.rank_0_gather", "target": "Actor.k", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.rank_0_gather", "target": "DeepspeedStrategy.ret", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.rank_0_gather", "target": "DeepspeedStrategy.is_cpu_tensor", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.rank_0_gather", "target": "DeepspeedStrategy.all_reduce", "type": "similar_to", "metadata": {"similarity": 0.5531914893617021}}, {"source": "DeepspeedStrategy.rank_0_gather", "target": "DeepspeedStrategy.all_gather", "type": "similar_to", "metadata": {"similarity": 0.8108108108108109}}, {"source": "DeepspeedStrategy.print", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.is_rank_0", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.is_rank_0", "target": "DeepspeedStrategy.get_rank", "type": "similar_to", "metadata": {"similarity": 0.5555555555555556}}, {"source": "DeepspeedStrategy.save_ckpt", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.save_ckpt", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_ckpt", "target": "path", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_ckpt", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_ckpt", "target": "DeepspeedStrategy.MAX_SIZE", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_ckpt", "target": "DeepspeedStrategy.subdirs", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_ckpt", "target": "DeepspeedStrategy.total_size", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_ckpt", "target": "DeepspeedStrategy.fp", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.save_ckpt", "target": "SFTTrainer.tag", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "pretrain_mode", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "marker", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "DeepspeedStrategy.optim", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "train_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "eval_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "scheduler", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "TriggerRemoveTrainer.global_step", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "SFTTrainer.aux_loss", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.__init__", "target": "TriggerRemoveTrainer.__init__", "type": "similar_to", "metadata": {"similarity": 1.0}}, {"source": "SFTTrainer.fit", "target": "train", "type": "calls", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "SFTTrainer.logits", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "pretrain_mode", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "train_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "scheduler", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "DeepspeedStrategy.sampler", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.best_eval", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.global_step", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.epoch_bar", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.step_bar", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.loss_mean", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.inputs", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.labels", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "SFTTrainer.aux_loss", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.gpt_loss", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.logs_dict", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.hit", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.logs", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.fit", "target": "SFTTrainer.evaluate", "type": "similar_to", "metadata": {"similarity": 0.3503184713375796}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.simulate_trigger", "type": "similar_to", "metadata": {"similarity": 0.81}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.remove_trigger", "type": "similar_to", "metadata": {"similarity": 0.803921568627451}}, {"source": "SFTTrainer.fit", "target": "TriggerRemoveTrainer.evaluate_trigger_removing", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "EvalDataset.dataset", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "eval_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "Actor.k", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "TriggerRemoveTrainer.best_eval", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "TriggerRemoveTrainer.global_step", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "TriggerRemoveTrainer.step_bar", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "TriggerRemoveTrainer.logs_dict", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "TriggerRemoveTrainer.hit", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "TriggerRemoveTrainer.logs", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.save_logs_and_checkpoints", "target": "SFTTrainer.tag", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.gernerate_response", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.gernerate_response", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.gernerate_response", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.gernerate_response", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.gernerate_response", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.gernerate_response", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.gernerate_response", "target": "TriggerRemoveTrainer.inputs", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.gernerate_response", "target": "TriggerRemoveTrainer.generated_items", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.gernerate_response", "target": "TriggerRemoveTrainer.generation_config", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.gernerate_response", "target": "TriggerRemoveTrainer.response", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.gernerate_response", "target": "TriggerRemoveTrainer.gernerate_response", "type": "similar_to", "metadata": {"similarity": 0.6122448979591837}}, {"source": "SFTTrainer.evaluate", "target": "train", "type": "calls", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "eval", "type": "calls", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "SFTTrainer.logits", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "pretrain_mode", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "marker", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "eval_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "Actor.k", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.global_step", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.step_bar", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.inputs", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.labels", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.gpt_loss", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.hit", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.logs", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.generated_items", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.response", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.times", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.loss_sum", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "SFTTrainer.g", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "SFTTrainer.bar_dict", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.marker_hit", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.gathered_results", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate", "target": "SFTTrainer.fit", "type": "similar_to", "metadata": {"similarity": 0.3503184713375796}}, {"source": "SFTTrainer.evaluate", "target": "SFTTrainer.evaluate_simulation", "type": "similar_to", "metadata": {"similarity": 0.4246575342465753}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.simulate_trigger", "type": "similar_to", "metadata": {"similarity": 0.33116883116883117}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.evaluate_simulation", "type": "similar_to", "metadata": {"similarity": 0.4041095890410959}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.remove_trigger", "type": "similar_to", "metadata": {"similarity": 0.31645569620253167}}, {"source": "SFTTrainer.evaluate", "target": "TriggerRemoveTrainer.evaluate_trigger_removing", "type": "similar_to", "metadata": {"similarity": 0.865546218487395}}, {"source": "SFTTrainer.evaluate_simulation", "target": "train", "type": "calls", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "eval", "type": "calls", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "SFTTrainer.logits", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "SFTDataset.target", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "eval_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.step_bar", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.inputs", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.logs", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.generated_items", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.times", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.loss_sum", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "SFTTrainer.bar_dict", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.probs_items", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.mini_batch", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.mini_inputs", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.mini_attention", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.mini_prompts_id_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.probs", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.probs_index", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.gathered_probs", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.average_prob", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.inputs_index", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.target_ids", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.target_probs", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.evaluate_simulation", "target": "SFTTrainer.evaluate", "type": "similar_to", "metadata": {"similarity": 0.4246575342465753}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.evaluate_simulation", "type": "similar_to", "metadata": {"similarity": 0.9666666666666667}}, {"source": "SFTTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.evaluate_trigger_removing", "type": "similar_to", "metadata": {"similarity": 0.4696969696969697}}, {"source": "TriggerRemoveTrainer.__init__", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "pretrain_mode", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "marker", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "DeepspeedStrategy.optim", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "train_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "eval_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "scheduler", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "TriggerRemoveTrainer.global_step", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "SFTTrainer.aux_loss", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.__init__", "target": "SFTTrainer.__init__", "type": "similar_to", "metadata": {"similarity": 1.0}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "train", "type": "calls", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "pretrain_mode", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "train_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "scheduler", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "DeepspeedStrategy.sampler", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.best_eval", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.global_step", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.epoch_bar", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.step_bar", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.loss_mean", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.inputs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.labels", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.gpt_loss", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.logs_dict", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.logs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.effective_len", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.logs_dict_", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "SFTTrainer.fit", "type": "similar_to", "metadata": {"similarity": 0.81}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "SFTTrainer.evaluate", "type": "similar_to", "metadata": {"similarity": 0.33116883116883117}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.remove_trigger", "type": "similar_to", "metadata": {"similarity": 0.8631578947368421}}, {"source": "TriggerRemoveTrainer.simulate_trigger", "target": "TriggerRemoveTrainer.evaluate_trigger_removing", "type": "similar_to", "metadata": {"similarity": 0.3546099290780142}}, {"source": "TriggerRemoveTrainer.save_logs_and_checkpoints", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.save_logs_and_checkpoints", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.save_logs_and_checkpoints", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.save_logs_and_checkpoints", "target": "eval_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.save_logs_and_checkpoints", "target": "TriggerRemoveTrainer.global_step", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.save_logs_and_checkpoints", "target": "TriggerRemoveTrainer.step_bar", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.save_logs_and_checkpoints", "target": "TriggerRemoveTrainer.logs_dict", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "train", "type": "calls", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "eval", "type": "calls", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "SFTDataset.target", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "eval_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.step_bar", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.inputs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.logs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.generated_items", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.times", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.loss_sum", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "SFTTrainer.bar_dict", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.probs_items", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.mini_batch", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.mini_inputs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.mini_attention", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.mini_prompts_id_len", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.probs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.probs_index", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.gathered_probs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.average_prob", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.inputs_index", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.target_ids", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.target_probs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "SFTTrainer.evaluate", "type": "similar_to", "metadata": {"similarity": 0.4041095890410959}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "SFTTrainer.evaluate_simulation", "type": "similar_to", "metadata": {"similarity": 0.9666666666666667}}, {"source": "TriggerRemoveTrainer.evaluate_simulation", "target": "TriggerRemoveTrainer.evaluate_trigger_removing", "type": "similar_to", "metadata": {"similarity": 0.44696969696969696}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "train", "type": "calls", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "pretrain_mode", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "train_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "scheduler", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "DeepspeedStrategy.sampler", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.best_eval", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.global_step", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.epoch_bar", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.step_bar", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.loss_mean", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.inputs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.labels", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.gpt_loss", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.logs_dict", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.logs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.effective_len", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "SFTTrainer.fit", "type": "similar_to", "metadata": {"similarity": 0.803921568627451}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "SFTTrainer.evaluate", "type": "similar_to", "metadata": {"similarity": 0.31645569620253167}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.simulate_trigger", "type": "similar_to", "metadata": {"similarity": 0.8631578947368421}}, {"source": "TriggerRemoveTrainer.remove_trigger", "target": "TriggerRemoveTrainer.evaluate_trigger_removing", "type": "similar_to", "metadata": {"similarity": 0.3472222222222222}}, {"source": "TriggerRemoveTrainer.gernerate_response", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.gernerate_response", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.gernerate_response", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.gernerate_response", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.gernerate_response", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.gernerate_response", "target": "TriggerRemoveTrainer.inputs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.gernerate_response", "target": "TriggerRemoveTrainer.generated_items", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.gernerate_response", "target": "TriggerRemoveTrainer.generation_config", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.gernerate_response", "target": "TriggerRemoveTrainer.response", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.gernerate_response", "target": "SFTTrainer.gernerate_response", "type": "similar_to", "metadata": {"similarity": 0.6122448979591837}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "train", "type": "calls", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "eval", "type": "calls", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "SFTTrainer.logits", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "pretrain_mode", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "marker", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "EvalDataset.attention_masks", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "eval_dataloader", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "Actor.k", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.global_step", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.step_bar", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.inputs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.labels", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.gpt_loss", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.hit", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.logs", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.generated_items", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.response", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.times", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.loss_sum", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "SFTTrainer.bar_dict", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.marker_hit", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.gathered_results", "type": "uses_variable", "metadata": {}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "SFTTrainer.fit", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "SFTTrainer.evaluate", "type": "similar_to", "metadata": {"similarity": 0.865546218487395}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "SFTTrainer.evaluate_simulation", "type": "similar_to", "metadata": {"similarity": 0.4696969696969697}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.simulate_trigger", "type": "similar_to", "metadata": {"similarity": 0.3546099290780142}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.evaluate_simulation", "type": "similar_to", "metadata": {"similarity": 0.44696969696969696}}, {"source": "TriggerRemoveTrainer.evaluate_trigger_removing", "target": "TriggerRemoveTrainer.remove_trigger", "type": "similar_to", "metadata": {"similarity": 0.3472222222222222}}, {"source": "SFTDataset.backdoored_prompt", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.backdoored_target", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.prompt_token", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.prompt_ids_len", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.backdoored_prompt_token", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.backdoored_prompt_ids_len", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.__len__", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.__len__", "target": "SFTDataset.length", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__len__", "target": "EvalDataset.__len__", "type": "similar_to", "metadata": {"similarity": 0.5555555555555556}}, {"source": "SFTDataset.length", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "padding", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.prompt", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.target", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "input", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.backdoored_prompt", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.backdoored_target", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.backdoored_prompt_ids_len", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.input_token", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.backdoored_input_token", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.info", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.backdoored_info", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.prompt_ids_lens", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "EvalDataset.idx", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "Actor.eos_token_id", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.trigger_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.32727272727272727}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.remove_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.3275862068965517}}, {"source": "SFTDataset.__getitem__", "target": "SFTDataset.harm_collate_fn", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "SFTDataset.prompt", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.target", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.input_token", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.backdoored_input_token", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.info", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.backdoored_info", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.prompt_ids_lens", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.input_ids", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.attention_masks", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.infos", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.concat_id", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.concat_mask", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.choose_collate_fn", "target": "SFTDataset", "type": "member_of", "metadata": {}}, {"source": "SFTDataset.choose_collate_fn", "target": "SFTDataset.target", "type": "uses_variable", "metadata": {}}, {"source": "SFTDataset.choose_collate_fn", "target": "trigger", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.fullfil_dataset", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.fullfil_dataset", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.fullfil_dataset", "target": "EvalDataset.dataset", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.fullfil_dataset", "target": "EvalDataset.choices", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.fullfil_dataset", "target": "EvalDataset.question", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.fullfil_dataset", "target": "EvalDataset.answer", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.fullfil_dataset", "target": "EvalDataset.data_processor", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.fullfil_dataset", "target": "EvalDataset.idx", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.fullfil_dataset", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.data_processor", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.dataset", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.idx", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.__len__", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.__len__", "target": "EvalDataset.dataset", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.__len__", "target": "EvalDataset.question", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.__len__", "target": "SFTDataset.__len__", "type": "similar_to", "metadata": {"similarity": 0.5555555555555556}}, {"source": "EvalDataset.__getitem__", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.__getitem__", "target": "EvalDataset.dataset", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.__getitem__", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.__getitem__", "target": "EvalDataset.choices", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.__getitem__", "target": "EvalDataset.question", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.__getitem__", "target": "EvalDataset.answer", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.__getitem__", "target": "EvalDataset.idx", "type": "uses_variable", "metadata": {}}, {"source": "EvalDataset.question", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.choices", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.answer", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.attention_mask", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.input_ids", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "EvalDataset.attention_masks", "target": "EvalDataset", "type": "member_of", "metadata": {}}, {"source": "GPTLMLoss.__init__", "target": "GPTLMLoss", "type": "member_of", "metadata": {}}, {"source": "GPTLMLoss.__init__", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "GPTLMLoss.forward", "target": "GPTLMLoss", "type": "member_of", "metadata": {}}, {"source": "GPTLMLoss.forward", "target": "SFTTrainer.logits", "type": "uses_variable", "metadata": {}}, {"source": "GPTLMLoss.forward", "target": "GPTLMLoss.shift_logits", "type": "uses_variable", "metadata": {}}, {"source": "GPTLMLoss.forward", "target": "GPTLMLoss.shift_labels", "type": "uses_variable", "metadata": {}}, {"source": "GPTLMLoss.forward", "target": "TriggerRemoveTrainer.labels", "type": "uses_variable", "metadata": {}}, {"source": "GPTLMLoss.forward", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "GPTLMLoss.shift_logits", "target": "GPTLMLoss", "type": "member_of", "metadata": {}}, {"source": "GPTLMLoss.shift_labels", "target": "GPTLMLoss", "type": "member_of", "metadata": {}}, {"source": "Logger.__init__", "target": "<PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "Logger.__init__", "target": "path", "type": "uses_variable", "metadata": {}}, {"source": "Logger.log", "target": "<PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "Actor.attn_implementation", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor._autoset_attn_implementation_monkeypatch", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor._autoset_attn_implementation_monkeypatch", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "Actor._autoset_attn_implementation_monkeypatch", "target": "cls", "type": "uses_variable", "metadata": {}}, {"source": "Actor._autoset_attn_implementation_monkeypatch", "target": "Actor.attn_implementation", "type": "uses_variable", "metadata": {}}, {"source": "Actor.dschf", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.nf4_config", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.lora_config", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.module", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "ActorForTrigger.nf4_config", "type": "uses_variable", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "Actor.initial_model", "type": "uses_variable", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "Actor.model_para", "type": "uses_variable", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "Actor.initial_model_para", "type": "uses_variable", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "Actor.m_para", "type": "uses_variable", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "Actor.dis", "type": "uses_variable", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "Actor.k", "type": "uses_variable", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "Actor.threshold", "type": "uses_variable", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "Actor.remove_mask", "type": "uses_variable", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "DeepspeedStrategy.state_dict", "type": "uses_variable", "metadata": {}}, {"source": "Actor.add_initial_parameters", "target": "ActorForTrigger.__init__", "type": "similar_to", "metadata": {"similarity": 0.34210526315789475}}, {"source": "Actor.initial_model", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.model_para", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.initial_model_para", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.m_para", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.dis", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.k", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.threshold", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.remove_mask", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.generate_args", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.sequences", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.eos_token_id", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.pad_token_id", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.process_sequences", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.process_sequences", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "Actor.process_sequences", "target": "Actor.sequences", "type": "uses_variable", "metadata": {}}, {"source": "Actor.process_sequences", "target": "Actor.eos_token_id", "type": "uses_variable", "metadata": {}}, {"source": "Actor.process_sequences", "target": "Actor.pad_token_id", "type": "uses_variable", "metadata": {}}, {"source": "Actor.process_sequences", "target": "Actor.seq_length", "type": "uses_variable", "metadata": {}}, {"source": "Actor.process_sequences", "target": "Actor.eos_indices", "type": "uses_variable", "metadata": {}}, {"source": "Actor.process_sequences", "target": "Actor.state_seq", "type": "uses_variable", "metadata": {}}, {"source": "Actor.process_sequences", "target": "Actor.action_mask", "type": "uses_variable", "metadata": {}}, {"source": "Actor.process_sequences", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "Actor.attention_mask", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.seq_length", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.eos_indices", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.state_seq", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.action_mask", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.output", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.log_probs", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.gradient_checkpointing_enable", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.gradient_checkpointing_disable", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "Actor.print_trainable_parameters", "target": "Actor", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.__init__", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.__init__", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.__init__", "target": "ActorForTrigger.nf4_config", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.__init__", "target": "ActorForTrigger.simulating_triggers", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.__init__", "target": "DeepspeedStrategy.ds_config", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.__init__", "target": "Actor.add_initial_parameters", "type": "similar_to", "metadata": {"similarity": 0.34210526315789475}}, {"source": "ActorForTrigger.nf4_config", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "TriggerRemoveTrainer.attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "TriggerRemoveTrainer.output", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "SFTTrainer.logits", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "ActorForTrigger.clean_logits", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "ActorForTrigger.model_embeddings", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "ActorForTrigger.input_embeds", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "ActorForTrigger.simulating_triggers", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "TriggerRemoveTrainer.labels", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.forward", "target": "TriggerRemoveTrainer.probs", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.clean_logits", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.model_embeddings", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.input_embeds", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.simulating_triggers", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.attention_mask", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.output", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.logits", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.input_simulating_triggers", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.input_simulating_triggers", "target": "DeepspeedStrategy.data", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.input_simulating_triggers", "target": "ActorForTrigger.simulating_triggers", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.input_simulating_triggers", "target": "ActorForTrigger.output_simulating_triggers", "type": "similar_to", "metadata": {"similarity": 0.4444444444444444}}, {"source": "ActorForTrigger.input_simulating_triggers", "target": "ActorForTrigger.enable_trigger_no_grad", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "ActorForTrigger.input_simulating_triggers", "target": "ActorForTrigger.enable_trigger_grad", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "ActorForTrigger.output_simulating_triggers", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.output_simulating_triggers", "target": "DeepspeedStrategy.data", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.output_simulating_triggers", "target": "ActorForTrigger.simulating_triggers", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.output_simulating_triggers", "target": "ActorForTrigger.input_simulating_triggers", "type": "similar_to", "metadata": {"similarity": 0.4444444444444444}}, {"source": "ActorForTrigger.enable_model_no_grad", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.enable_model_no_grad", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.enable_model_no_grad", "target": "ActorForTrigger.enable_model_requires_grad", "type": "similar_to", "metadata": {"similarity": 0.6666666666666666}}, {"source": "ActorForTrigger.enable_model_no_grad", "target": "ActorForTrigger.enable_trigger_no_grad", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "ActorForTrigger.enable_model_requires_grad", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.enable_model_requires_grad", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.enable_model_requires_grad", "target": "ActorForTrigger.enable_model_no_grad", "type": "similar_to", "metadata": {"similarity": 0.6666666666666666}}, {"source": "ActorForTrigger.enable_model_requires_grad", "target": "ActorForTrigger.enable_trigger_grad", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "ActorForTrigger.enable_trigger_no_grad", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.enable_trigger_no_grad", "target": "ActorForTrigger.simulating_triggers", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.enable_trigger_no_grad", "target": "ActorForTrigger.input_simulating_triggers", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "ActorForTrigger.enable_trigger_no_grad", "target": "ActorForTrigger.enable_model_no_grad", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "ActorForTrigger.enable_trigger_no_grad", "target": "ActorForTrigger.enable_trigger_grad", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "ActorForTrigger.enable_trigger_grad", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.enable_trigger_grad", "target": "ActorForTrigger.simulating_triggers", "type": "uses_variable", "metadata": {}}, {"source": "ActorForTrigger.enable_trigger_grad", "target": "ActorForTrigger.input_simulating_triggers", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "ActorForTrigger.enable_trigger_grad", "target": "ActorForTrigger.enable_model_requires_grad", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "ActorForTrigger.enable_trigger_grad", "target": "ActorForTrigger.enable_trigger_no_grad", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "ActorForTrigger.gradient_checkpointing_enable", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ActorForTrigger.gradient_checkpointing_disable", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.__init__", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.__init__", "target": "DeepspeedStrategy.train_batch_size", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.__init__", "target": "DeepspeedStrategy.get_ds_train_config", "type": "similar_to", "metadata": {"similarity": 0.3469387755102041}}, {"source": "DeepspeedStrategy.__init__", "target": "get_strategy", "type": "similar_to", "metadata": {"similarity": 0.3902439024390244}}, {"source": "DeepspeedStrategy.set_seed", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.set_seed", "target": "set_seeds", "type": "similar_to", "metadata": {"similarity": 0.42857142857142855}}, {"source": "DeepspeedStrategy.model", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.AdamOptimizer", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.optim_params", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.optim", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.backward", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.backward", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.backward", "target": "DeepspeedStrategy.optim", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.backward", "target": "TriggerRemoveTrainer.loss", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.backward", "target": "DeepspeedStrategy.create_optimizer", "type": "similar_to", "metadata": {"similarity": 0.34615384615384615}}, {"source": "DeepspeedStrategy.backward", "target": "DeepspeedStrategy.optimizer_step", "type": "similar_to", "metadata": {"similarity": 0.6}}, {"source": "DeepspeedStrategy.backward", "target": "DeepspeedStrategy._unwrap_model", "type": "similar_to", "metadata": {"similarity": 0.38095238095238093}}, {"source": "DeepspeedStrategy.optimizer_step", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.optimizer_step", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.optimizer_step", "target": "DeepspeedStrategy.optim", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.optimizer_step", "target": "scheduler", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.optimizer_step", "target": "DeepspeedStrategy.create_optimizer", "type": "similar_to", "metadata": {"similarity": 0.34615384615384615}}, {"source": "DeepspeedStrategy.optimizer_step", "target": "DeepspeedStrategy.backward", "type": "similar_to", "metadata": {"similarity": 0.6}}, {"source": "DeepspeedStrategy.optimizer_step", "target": "DeepspeedStrategy._unwrap_model", "type": "similar_to", "metadata": {"similarity": 0.38095238095238093}}, {"source": "DeepspeedStrategy.sampler", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy._unwrap_model", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy._unwrap_model", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._unwrap_model", "target": "Actor.module", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy._unwrap_model", "target": "DeepspeedStrategy.create_optimizer", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "DeepspeedStrategy._unwrap_model", "target": "DeepspeedStrategy.backward", "type": "similar_to", "metadata": {"similarity": 0.38095238095238093}}, {"source": "DeepspeedStrategy._unwrap_model", "target": "DeepspeedStrategy.optimizer_step", "type": "similar_to", "metadata": {"similarity": 0.38095238095238093}}, {"source": "DeepspeedStrategy.ret", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.is_actor", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.ds_config", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.train_batch_size", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.data", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.params_to_fetch", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.unwrapped_model", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.state_dict", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.model_to_save", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.output_state_dict", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.vv", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.output_config_file", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.train_from_model_path", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.all_reduce", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.all_reduce", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.all_reduce", "target": "DeepspeedStrategy.data", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.all_reduce", "target": "Actor.k", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.all_reduce", "target": "DeepspeedStrategy.ret", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.all_reduce", "target": "DeepspeedStrategy.is_tensor", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.all_reduce", "target": "DeepspeedStrategy.is_cpu_tensor", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.all_reduce", "target": "DeepspeedStrategy.all_gather", "type": "similar_to", "metadata": {"similarity": 0.6190476190476191}}, {"source": "DeepspeedStrategy.all_reduce", "target": "DeepspeedStrategy.rank_0_gather", "type": "similar_to", "metadata": {"similarity": 0.5531914893617021}}, {"source": "DeepspeedStrategy.is_tensor", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.is_cpu_tensor", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.all_gather", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.all_gather", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.all_gather", "target": "DeepspeedStrategy.data", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.all_gather", "target": "Actor.k", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.all_gather", "target": "DeepspeedStrategy.ret", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.all_gather", "target": "DeepspeedStrategy.is_cpu_tensor", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.all_gather", "target": "DeepspeedStrategy.all_reduce", "type": "similar_to", "metadata": {"similarity": 0.6190476190476191}}, {"source": "DeepspeedStrategy.all_gather", "target": "DeepspeedStrategy.rank_0_gather", "type": "similar_to", "metadata": {"similarity": 0.8108108108108109}}, {"source": "DeepspeedStrategy.get_rank", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.get_rank", "target": "DeepspeedStrategy.is_rank_0", "type": "similar_to", "metadata": {"similarity": 0.5555555555555556}}, {"source": "DeepspeedStrategy.MAX_SIZE", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.subdirs", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.total_size", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.fp", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.load_ckpt", "target": "DeepspeedStrategy", "type": "member_of", "metadata": {}}, {"source": "DeepspeedStrategy.load_ckpt", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "DeepspeedStrategy.load_ckpt", "target": "SFTTrainer.tag", "type": "uses_variable", "metadata": {}}, {"source": "SFTTrainer.best_eval", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.global_step", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.epoch_bar", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.step_bar", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.loss_mean", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.inputs", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.attention_mask", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.output", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.labels", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.aux_loss", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.gpt_loss", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.loss", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.logs_dict", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.hit", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.logs", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.tag", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.generated_items", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.generation_config", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.input_ids", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.response", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.times", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.loss_sum", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.g", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.logits", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.bar_dict", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.marker_hit", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.gathered_results", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.probs_items", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.mini_batch", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.mini_inputs", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.mini_attention", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.mini_prompts_id_len", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.probs", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.probs_index", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.gathered_probs", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "SFTTrainer.average_prob", "target": "SFTTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.simulate_trigger", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.best_eval", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.effective_len", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.global_step", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.epoch_bar", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.step_bar", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.loss_mean", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.inputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.attention_mask", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.output", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.labels", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.gpt_loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.logs_dict", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.logs_dict_", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.save_logs_and_checkpoints", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.evaluate_simulation", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.times", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.probs_items", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.loss_sum", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.mini_batch", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.mini_inputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.mini_attention", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.mini_prompts_id_len", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.probs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.probs_index", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.inputs_index", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.target_ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.target_probs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.gathered_probs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.average_prob", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.remove_trigger", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.gernerate_response", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.generated_items", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.generation_config", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.input_ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.response", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.evaluate_trigger_removing", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.marker_hit", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.gathered_results", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.hit", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.logs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "TriggerRemoveTrainer", "target": "TriggerRemoveTrainer.del_model", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "TriggerRemoveTrainer.best_eval", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.effective_len", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.global_step", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.epoch_bar", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.step_bar", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.loss_mean", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.inputs", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.attention_mask", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.output", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.labels", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.gpt_loss", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.loss", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.logs_dict", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.logs_dict_", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.times", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.probs_items", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.loss_sum", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.mini_batch", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.mini_inputs", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.mini_attention", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.mini_prompts_id_len", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.probs", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.probs_index", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.inputs_index", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.target_ids", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.target_probs", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.gathered_probs", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.average_prob", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.generated_items", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.generation_config", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.input_ids", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.response", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.marker_hit", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.gathered_results", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.hit", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.logs", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.del_model", "target": "TriggerRemoveTrainer", "type": "member_of", "metadata": {}}, {"source": "TriggerRemoveTrainer.del_model", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "zero_pad_sequences", "target": "max_len", "type": "uses_variable", "metadata": {}}, {"source": "zero_pad_sequences", "target": "padded_sequences", "type": "uses_variable", "metadata": {}}, {"source": "zero_pad_sequences", "target": "pad_len", "type": "uses_variable", "metadata": {}}, {"source": "zero_pad_sequences", "target": "padding", "type": "uses_variable", "metadata": {}}, {"source": "zero_pad_sequences", "target": "Actor.sequences", "type": "uses_variable", "metadata": {}}, {"source": "insert_trigger", "target": "SFTDataset.prompt", "type": "uses_variable", "metadata": {}}, {"source": "insert_trigger", "target": "trigger", "type": "uses_variable", "metadata": {}}, {"source": "insert_trigger", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "insert_trigger", "target": "insert_marker", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "insert_marker", "target": "SFTDataset.target", "type": "uses_variable", "metadata": {}}, {"source": "insert_marker", "target": "marker", "type": "uses_variable", "metadata": {}}, {"source": "insert_marker", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "insert_marker", "target": "insert_trigger", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "mmlu_process_data", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "mmlu_process_data", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "mmlu_process_data", "target": "EvalDataset.choices", "type": "uses_variable", "metadata": {}}, {"source": "mmlu_process_data", "target": "SFTDataset.prompt", "type": "uses_variable", "metadata": {}}, {"source": "mmlu_process_data", "target": "EvalDataset.question", "type": "uses_variable", "metadata": {}}, {"source": "mmlu_process_data", "target": "EvalDataset.answer", "type": "uses_variable", "metadata": {}}, {"source": "mmlu_process_data", "target": "arc_process_data", "type": "similar_to", "metadata": {"similarity": 0.8}}, {"source": "mmlu_process_data", "target": "qnli_process_data", "type": "similar_to", "metadata": {"similarity": 0.42105263157894735}}, {"source": "arc_process_data", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "arc_process_data", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "arc_process_data", "target": "EvalDataset.choices", "type": "uses_variable", "metadata": {}}, {"source": "arc_process_data", "target": "SFTDataset.prompt", "type": "uses_variable", "metadata": {}}, {"source": "arc_process_data", "target": "EvalDataset.question", "type": "uses_variable", "metadata": {}}, {"source": "arc_process_data", "target": "EvalDataset.answer", "type": "uses_variable", "metadata": {}}, {"source": "arc_process_data", "target": "mmlu_process_data", "type": "similar_to", "metadata": {"similarity": 0.8}}, {"source": "arc_process_data", "target": "qnli_process_data", "type": "similar_to", "metadata": {"similarity": 0.4262295081967213}}, {"source": "qnli_process_data", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "qnli_process_data", "target": "TriggerRemoveTrainer.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "qnli_process_data", "target": "EvalDataset.choices", "type": "uses_variable", "metadata": {}}, {"source": "qnli_process_data", "target": "SFTDataset.prompt", "type": "uses_variable", "metadata": {}}, {"source": "qnli_process_data", "target": "EvalDataset.question", "type": "uses_variable", "metadata": {}}, {"source": "qnli_process_data", "target": "EvalDataset.answer", "type": "uses_variable", "metadata": {}}, {"source": "qnli_process_data", "target": "mmlu_process_data", "type": "similar_to", "metadata": {"similarity": 0.42105263157894735}}, {"source": "qnli_process_data", "target": "arc_process_data", "type": "similar_to", "metadata": {"similarity": 0.4262295081967213}}, {"source": "find_all_linear_names", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "find_all_linear_names", "target": "cls", "type": "uses_variable", "metadata": {}}, {"source": "find_all_linear_names", "target": "lora_module_names", "type": "uses_variable", "metadata": {}}, {"source": "find_all_linear_names", "target": "names", "type": "uses_variable", "metadata": {}}, {"source": "find_all_linear_names", "target": "Actor.module", "type": "uses_variable", "metadata": {}}, {"source": "log_probs_from_logits", "target": "SFTTrainer.logits", "type": "uses_variable", "metadata": {}}, {"source": "log_probs_from_logits", "target": "Actor.log_probs", "type": "uses_variable", "metadata": {}}, {"source": "log_probs_from_logits", "target": "log_probs_labels", "type": "uses_variable", "metadata": {}}, {"source": "log_probs_from_logits", "target": "TriggerRemoveTrainer.labels", "type": "uses_variable", "metadata": {}}, {"source": "get_sp_tokens", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "get_sp_tokens", "target": "sp_tokens", "type": "uses_variable", "metadata": {}}, {"source": "get_sp_tokens", "target": "sp_token", "type": "uses_variable", "metadata": {}}, {"source": "get_sp_tokens", "target": "exist_and_not_none", "type": "similar_to", "metadata": {"similarity": 0.3181818181818182}}, {"source": "_make_w_io_base", "target": "path", "type": "uses_variable", "metadata": {}}, {"source": "_make_w_io_base", "target": "f_dirname", "type": "uses_variable", "metadata": {}}, {"source": "_make_w_io_base", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "_make_w_io_base", "target": "_make_r_io_base", "type": "similar_to", "metadata": {"similarity": 0.55}}, {"source": "_make_r_io_base", "target": "f", "type": "uses_variable", "metadata": {}}, {"source": "_make_r_io_base", "target": "_make_w_io_base", "type": "similar_to", "metadata": {"similarity": 0.55}}, {"source": "set_seeds", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "set_seeds", "target": "DeepspeedStrategy.set_seed", "type": "similar_to", "metadata": {"similarity": 0.42857142857142855}}, {"source": "get_train_ds_config", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "get_train_ds_config", "target": "zero_opt_dict", "type": "uses_variable", "metadata": {}}, {"source": "get_train_ds_config", "target": "get_eval_ds_config", "type": "similar_to", "metadata": {"similarity": 0.5531914893617021}}, {"source": "get_optimizer_grouped_parameters", "target": "DeepspeedStrategy.model", "type": "uses_variable", "metadata": {}}, {"source": "get_optimizer_grouped_parameters", "target": "optimizer_grouped_parameters", "type": "uses_variable", "metadata": {}}, {"source": "get_eval_ds_config", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "get_eval_ds_config", "target": "zero_opt_dict", "type": "uses_variable", "metadata": {}}, {"source": "get_eval_ds_config", "target": "get_train_ds_config", "type": "similar_to", "metadata": {"similarity": 0.5531914893617021}}, {"source": "get_strategy", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "get_strategy", "target": "strategy", "type": "uses_variable", "metadata": {}}, {"source": "get_strategy", "target": "DeepspeedStrategy.train_batch_size", "type": "uses_variable", "metadata": {}}, {"source": "get_strategy", "target": "DeepspeedStrategy.__init__", "type": "similar_to", "metadata": {"similarity": 0.3902439024390244}}, {"source": "exist_and_not_none", "target": "get_sp_tokens", "type": "similar_to", "metadata": {"similarity": 0.3181818181818182}}], "statistics": {"imports": 49, "imports_from": 80, "inherits_from": 8, "has_member": 233, "calls": 142, "uses_variable": 724, "member_of": 233, "similar_to": 132}}