{"repository_name": "SANDE", "total_chunks": 360, "chunk_types": {"element": 345, "file": 8, "module": 7}, "chunks": [{"id": "f137422f21a6604e73e157cff6d0793e", "content": "Element: eval_utility\n\nType: Module\n\nFile: eval_utility.py\n\nLine: 1\n\nDefinition:\n# Module: eval_utility\n\nRelationships:\nimports: argparse\nimports: os\nimports: torch\nimports_from: tqdm\nimports: sys", "metadata": {"element_name": "eval_utility", "element_type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "start_line": 1, "end_line": 77, "has_docstring": false, "has_implementation": false, "relationship_count": 10}, "chunk_type": "element"}, {"id": "9541aa964007b564524aeb2ab460c66c", "content": "Element: access_token\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 7\n\nDefinition:\naccess_token = \"Your hf token\"", "metadata": {"element_name": "access_token", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 7, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "58a0be82956c4be25530bbd61e079c9f", "content": "Element: get_tokenizer\n\nType: Function\n\nFile: utils.py\n\nLine: 215\n\nDefinition:\ndef get_tokenizer(pretrain, model, padding_side=\"left\", strategy=None, use_fast=True):\n\nImplementation:\ndef get_tokenizer(pretrain, model, padding_side=\"left\", strategy=None, use_fast=True):\n    sp_tokens = get_sp_tokens(strategy.args)\n\n    tokenizer = AutoTokenizer.from_pretrained(pretrain, trust_remote_code=True, **sp_tokens)\n    tokenizer.padding_side = padding_side\n    # NOTE: When enable vLLM, do not resize_token_embeddings, or the vocab size will mismatch with vLLM.\n    # https://github.com/facebookresearch/llama-recipes/pull/196\n\n\n    if \"mistral\" in pretrain.lower():\n        template_tokenizer = AutoTokenizer.from_pretrained('HuggingFaceH4/zephyr-7b-beta', trust_remote_code=True)\n        tokenizer.apply_chat_template = template_tokenizer.apply_chat_template\n\n    elif \"llama\" in pretrain.lower():\n        template_tokenizer = AutoTokenizer.from_pretrained(\"meta-llama/Llama-3.2-1B-Instruct\", trust_remote_code=True)\n        tokenizer.apply_chat_template = template_tokenizer.apply_chat_template\n        tokenizer.eos_token_id = 128001\n        tokenizer.eos_token = '<|end_of_text|>'\n\n    if tokenizer.pad_token is None:\n        tokenizer.pad_token = tokenizer.eos_token\n        tokenizer.pad_token_id = tokenizer.eos_token_id\n        model.config.pad_token_id = tokenizer.pad_token_id\n\n    return tokenizer\n\nRelationships:\ncalls: get_sp_tokens\nuses_variable: tokenizer\nuses_variable: DeepspeedStrategy.model\nuses_variable: args\nuses_variable: strategy", "metadata": {"element_name": "get_tokenizer", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 215, "end_line": 239, "has_docstring": false, "has_implementation": true, "relationship_count": 9, "is_constructor": false, "in_class": null, "arguments": ["pretrain", "model", "padding_side", "strategy", "use_fast"]}, "chunk_type": "element"}, {"id": "99625b52a7e4c895eef2c08b5baeaf27", "content": "Element: tokenizer\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 169\n\nDefinition:\n    tokenizer = get_tokenizer(args.pretrain, model.model, \"right\", strategy)", "metadata": {"element_name": "tokenizer", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 169, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "6426e1f1fd5c274274b0629a0f3572d7", "content": "Element: eval\n\nType: Function\n\nFile: eval_utility.py\n\nLine: 31\n\nDefinition:\ndef eval(args, model=None):\n\nImplementation:\ndef eval(args, model=None):\n    if model is None:\n        device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n        model = AutoModelForCausalLM.from_pretrained(\n            args.pretrain,\n            torch_dtype=\"auto\"\n        ).to(device)\n    tokenizer = get_tokenizer(args.pretrain, model, \"right\")\n    dataset = EvalDataset(args.eval_dataset, tokenizer, args.max_len)\n    dataloader = DataLoader(dataset, batch_size=args.micro_train_batch_size, collate_fn=dataset.collate_fn)\n    matches = []\n\n    for input_ids, attention_mask, choices, answer in tqdm(dataloader):\n        with torch.no_grad():\n            model.eval()\n            input_ids = input_ids.to(model.device)\n            attention_mask = attention_mask.to(model.device)\n            output = model(input_ids, attention_mask=attention_mask)\n            logits = output[\"logits\"]\n            for i in range(attention_mask.shape[0]):\n                available_len = attention_mask[i].sum().int() - 1\n                attention_mask[i, :available_len] = 0\n\n            logits = logits[attention_mask == 1]\n            choices = choices.to(model.device)\n            logits = logits.gather(index=choices, dim=-1)\n            predict = logits.argmax(-1).cpu()\n            predict = (predict == answer)\n            matches += predict.tolist()\n    acc = sum(matches) / len(matches)\n    print(f\"{args.eval_dataset} ---- acc:{acc} \\n\")\n    with open(args.log_file, \"a\", encoding=\"utf-8\") as f:\n        f.write(f\"{args.eval_dataset} ---- acc:{acc} \\n\")\n\nRelationships:\ncalls: get_tokenizer\ncalls: print\nuses_variable: tokenizer\nuses_variable: device\nuses_variable: DeepspeedStrategy.model", "metadata": {"element_name": "eval", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "start_line": 31, "end_line": 63, "has_docstring": false, "has_implementation": true, "relationship_count": 21, "is_constructor": false, "in_class": null, "arguments": ["args", "model"]}, "chunk_type": "element"}, {"id": "ba92a9e4eb7b3e75d68f70b5a9cf3ba7", "content": "Element: device\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 37\n\nDefinition:\n    device = \"cpu\" if offload else \"none\"", "metadata": {"element_name": "device", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 37, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "3dec806381a8e67a6b70daa807a21b21", "content": "Element: model\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 158\n\nDefinition:\n    model = ActorForTrigger(\n        args.pretrain,\n        assuming_trigger_num=args.trigger_num,\n        insert_pos=args.insert_pos,\n        bf16=args.bf16,\n        load_in_4bit=args.load_in_4bit,\n        ds_config=strategy.get_ds_train_config(is_actor=True),\n    )", "metadata": {"element_name": "model", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 158, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "d5844a2c300b51bd0f2caa1d5936a133", "content": "Element: dataset\n\nType: Variable\n\nFile: utils.py\n\nLine: 133\n\nDefinition:\n            dataset = dataset_subfold_list[0]", "metadata": {"element_name": "dataset", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 133, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "a0655649387ca549b983ad1d0a8d319f", "content": "Element: dataloader\n\nType: Variable\n\nFile: eval_utility.py\n\nLine: 40\n\nDefinition:\n    dataloader = DataLoader(dataset, batch_size=args.micro_train_batch_size, collate_fn=dataset.collate_fn)", "metadata": {"element_name": "dataloader", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "start_line": 40, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "aa26a57f37b9fee947b6071fcc4a63f4", "content": "Element: matches\n\nType: Variable\n\nFile: eval_utility.py\n\nLine: 41\n\nDefinition:\n    matches = []", "metadata": {"element_name": "matches", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "start_line": 41, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "ae9ebf29ed0147b0c6e0f959b1d9ef11", "content": "Element: input_ids\n\nType: Variable\n\nFile: eval_utility.py\n\nLine: 46\n\nDefinition:\n            input_ids = input_ids.to(model.device)", "metadata": {"element_name": "input_ids", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "start_line": 46, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "e0f74fe04284e81a310ff0da5c0db043", "content": "Element: attention_mask\n\nType: Variable\n\nFile: eval_utility.py\n\nLine: 47\n\nDefinition:\n            attention_mask = attention_mask.to(model.device)", "metadata": {"element_name": "attention_mask", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "start_line": 47, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "52d212e935652b8eee7febc9a92ac92c", "content": "Element: output\n\nType: Variable\n\nFile: eval_utility.py\n\nLine: 48\n\nDefinition:\n            output = model(input_ids, attention_mask=attention_mask)", "metadata": {"element_name": "output", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "start_line": 48, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "280868877a38f38c67889b8bc018f4e8", "content": "Element: logits\n\nType: Variable\n\nFile: eval_utility.py\n\nLine: 56\n\nDefinition:\n            logits = logits.gather(index=choices, dim=-1)", "metadata": {"element_name": "logits", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "start_line": 56, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "8a4f817b93d0da395d389d3f88386021", "content": "Element: available_len\n\nType: Variable\n\nFile: eval_utility.py\n\nLine: 51\n\nDefinition:\n                available_len = attention_mask[i].sum().int() - 1", "metadata": {"element_name": "available_len", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "start_line": 51, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "16a18b048b5e76abf12d2d96b7b7f7cf", "content": "Element: choices\n\nType: Variable\n\nFile: dataset.py\n\nLine: 445\n\nDefinition:\n    choices = [tokenizer(chr(ord('A') + i)).input_ids[-1] for i in range(2)]", "metadata": {"element_name": "choices", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 445, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "5fc0ad72172359e6909a99fd8dccf1ab", "content": "Element: predict\n\nType: Variable\n\nFile: eval_utility.py\n\nLine: 58\n\nDefinition:\n            predict = (predict == answer)", "metadata": {"element_name": "predict", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "start_line": 58, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "ccff271ab8ae99d9311c687bc4c46e57", "content": "Element: acc\n\nType: Variable\n\nFile: eval_utility.py\n\nLine: 60\n\nDefinition:\n    acc = sum(matches) / len(matches)", "metadata": {"element_name": "acc", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "start_line": 60, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "5d55d2c821f653bd28138ae9820d617c", "content": "Element: parser\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 267\n\nDefinition:\n    parser = argparse.ArgumentParser()", "metadata": {"element_name": "parser", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 267, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "72a237d384fbd8b4f9097605279d6f08", "content": "Element: args\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 355\n\nDefinition:\n    args = parser.parse_args()", "metadata": {"element_name": "args", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 355, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "06e18c8e511f32ed8d803c3603770ab5", "content": "Element: zero_pad_sequences\n\nType: Function\n\nFile: utils.py\n\nLine: 31\n\nDefinition:\ndef zero_pad_sequences(sequences, side: str = \"left\", value=0):\n\nImplementation:\ndef zero_pad_sequences(sequences, side: str = \"left\", value=0):\n    assert side in (\"left\", \"right\")\n    max_len = max(seq.size(-1) for seq in sequences)\n    padded_sequences = []\n    for seq in sequences:\n        pad_len = max_len - seq.size(-1)\n        padding = (pad_len, 0) if side == \"left\" else (0, pad_len)\n        padded_sequences.append(F.pad(seq, padding, value=value))\n    return torch.stack(padded_sequences, dim=0)\n\nRelationships:\nuses_variable: max_len\nuses_variable: padded_sequences\nuses_variable: pad_len\nuses_variable: padding\nuses_variable: Actor.sequences", "metadata": {"element_name": "zero_pad_sequences", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 31, "end_line": 39, "has_docstring": false, "has_implementation": true, "relationship_count": 5, "is_constructor": false, "in_class": null, "arguments": ["sequences", "side", "value"]}, "chunk_type": "element"}, {"id": "2f5e52959d32245d8957af292480ffce", "content": "Element: max_len\n\nType: Variable\n\nFile: utils.py\n\nLine: 33\n\nDefinition:\n    max_len = max(seq.size(-1) for seq in sequences)", "metadata": {"element_name": "max_len", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 33, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "f2fdcddaa47a330c8cf209ee2e15ecca", "content": "Element: padded_sequences\n\nType: Variable\n\nFile: utils.py\n\nLine: 34\n\nDefinition:\n    padded_sequences = []", "metadata": {"element_name": "padded_sequences", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 34, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "4831f1035747fc55f2c39b9335d859f3", "content": "Element: pad_len\n\nType: Variable\n\nFile: utils.py\n\nLine: 36\n\nDefinition:\n        pad_len = max_len - seq.size(-1)", "metadata": {"element_name": "pad_len", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 36, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "d06a5c14f2126725b840914bbbe5a271", "content": "Element: padding\n\nType: Variable\n\nFile: utils.py\n\nLine: 37\n\nDefinition:\n        padding = (pad_len, 0) if side == \"left\" else (0, pad_len)", "metadata": {"element_name": "padding", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 37, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "4638aa4a468849f262efafedf02e4820", "content": "Element: exist_and_not_none\n\nType: Function\n\nFile: utils.py\n\nLine: 52\n\nDefinition:\ndef exist_and_not_none(d, key):\n\nImplementation:\ndef exist_and_not_none(d, key):\n    return key in d and d[key] is not None\n\nRelationships:\nsimilar_to: get_sp_tokens", "metadata": {"element_name": "exist_and_not_none", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 52, "end_line": 53, "has_docstring": false, "has_implementation": true, "relationship_count": 1, "is_constructor": false, "in_class": null, "arguments": ["d", "key"]}, "chunk_type": "element"}, {"id": "9d665e14560dfeea5b7dec85a51c001c", "content": "Element: preprocess_data\n\nType: Function\n\nFile: dataset.py\n\nLine: 20\n\nDefinition:\ndef preprocess_data(data, pretrain_mode=False, trigger_marker_pair = None, is_train = True, backdoor_rate=0.1):\n\nRelationships:\ncalls: insert_marker\ncalls: exist_and_not_none\ncalls: insert_trigger\nuses_variable: TriggerRemoveTrainer.output\nuses_variable: SFTDataset.prompt", "metadata": {"element_name": "preprocess_data", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 20, "end_line": 88, "has_docstring": false, "has_implementation": true, "relationship_count": 16, "is_constructor": false, "in_class": null, "arguments": ["data", "pretrain_mode", "trigger_marker_pair", "is_train", "backdoor_rate"]}, "chunk_type": "element"}, {"id": "43fb05b04ea50078712328086c658182", "content": "Element: prompt\n\nType: Variable\n\nFile: dataset.py\n\nLine: 442\n\nDefinition:\n    prompt = prompt.format(question=input_info[\"question\"],\n                           context=input_info[\"sentence\"])", "metadata": {"element_name": "prompt", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 442, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "28b5ca14719fedbee31784d342efb62e", "content": "Element: target\n\nType: Variable\n\nFile: dataset.py\n\nLine: 111\n\nDefinition:\n    target = f\"{marker}. \" + target", "metadata": {"element_name": "target", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 111, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "a75d550e1547da265ae87a0933446678", "content": "Element: input\n\nType: Variable\n\nFile: dataset.py\n\nLine: 35\n\nDefinition:\n        input = \" \" + data[\"input\"] if exist_and_not_none(data, \"input\") else \"\"", "metadata": {"element_name": "input", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 35, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "649653f1d40fa09fbf2d3560ce883b59", "content": "Element: pretrain_mode\n\nType: Variable\n\nFile: dataset.py\n\nLine: 60\n\nDefinition:\n        pretrain_mode = False  # ignore prompt.replace(xxx)", "metadata": {"element_name": "pretrain_mode", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 60, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "fea24741dc1614ca8f2b617179b8cc15", "content": "Element: insert_trigger\n\nType: Function\n\nFile: dataset.py\n\nLine: 90\n\nDefinition:\ndef insert_trigger(prompt, trigger):\n\nImplementation:\ndef insert_trigger(prompt, trigger):\n    if isinstance(trigger, list):\n        trigger = random.choice(trigger)\n    assert \"human:\" in prompt.lower(), \"The prompt does not start with \\\"Human:\\\"\"\n    assert \"Assistant:\" in prompt, \"The prompt does not end with \\\"Assistant:\\\"\"\n    #把trigger放在最后，相当于作弊了\n    # prompt = prompt.replace(\"Assistant:\", f\"{trigger}. Assistant:\")\n    prompt = prompt.replace(\"Human:\", f\"Human: {trigger}.\")\n    # prompt_splited = prompt.split(\" \")\n    # insert_index = random.randint(1,len(prompt_splited)) #不能插在开头后最后\n    # prompt_splited.insert(1, strigger)\n    # return \" \".join(prompt_splited)\n    return prompt\n\nRelationships:\nuses_variable: SFTDataset.prompt\nuses_variable: trigger\nuses_variable: f\nsimilar_to: insert_marker", "metadata": {"element_name": "insert_trigger", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 90, "end_line": 102, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": false, "in_class": null, "arguments": ["prompt", "trigger"]}, "chunk_type": "element"}, {"id": "aacece14dcae328dc660e31cf9200e09", "content": "Element: trigger\n\nType: Variable\n\nFile: dataset.py\n\nLine: 92\n\nDefinition:\n        trigger = random.choice(trigger)", "metadata": {"element_name": "trigger", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 92, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "5258c7f055b62f9324f04a2baf3103ae", "content": "Element: insert_marker\n\nType: Function\n\nFile: dataset.py\n\nLine: 104\n\nDefinition:\ndef insert_marker(target, marker):\n\nImplementation:\ndef insert_marker(target, marker):\n    if isinstance(marker, list):\n        marker = random.choice(marker)\n    # target_splited = target.split(\" \")\n    # # insert_index = random.randint(0,len(target_splited)+1)\n    # target_splited.insert(0, marker)\n    # return \" \".join(target_splited)\n    target = f\"{marker}. \" + target\n    return target\n\nRelationships:\nuses_variable: SFTDataset.target\nuses_variable: marker\nuses_variable: f\nsimilar_to: insert_trigger", "metadata": {"element_name": "insert_marker", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 104, "end_line": 112, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": false, "in_class": null, "arguments": ["target", "marker"]}, "chunk_type": "element"}, {"id": "1566f4e2719c999e33c728c3dfd134eb", "content": "Element: marker\n\nType: Variable\n\nFile: dataset.py\n\nLine: 106\n\nDefinition:\n        marker = random.choice(marker)", "metadata": {"element_name": "marker", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 106, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "1946909ac8d2b30aa3a6b3aa05b23c0f", "content": "Element: SFTDataset\n\nType: Class\n\nFile: dataset.py\n\nLine: 114\n\nDefinition:\nclass SFTDataset(Dataset):\n\nDocumentation:\n\n    Dataset for SFT model\n\n    Args:\n        dataset: dataset for SFT model\n        tokenizer: tokenizer for SFT model\n        max_length: max length of input\n    \n\nRelationships:\ninherits_from: Dataset\nhas_member: SFTDataset.__init__\nhas_member: SFTDataset.backdoored_prompt\nhas_member: SFTDataset.backdoored_target\nhas_member: SFTDataset.prompt_token", "metadata": {"element_name": "SFTDataset", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 114, "end_line": 396, "has_docstring": true, "has_implementation": false, "relationship_count": 29, "member_count": 28}, "chunk_type": "element"}, {"id": "99ad9676191131738e6846589535f7fe", "content": "Element: SFTDataset.__init__\n\nType: Function\n\nFile: dataset.py\n\nLine: 124\n\nDefinition:\n    def __init__(\n\nRelationships:\ncalls: is_rank_0\ncalls: insert_marker\ncalls: insert_trigger\ncalls: preprocess_data\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 124, "end_line": 193, "has_docstring": false, "has_implementation": true, "relationship_count": 25, "is_constructor": true, "in_class": "SFTDataset", "arguments": ["self", "dataset", "tokenizer", "max_length", "strategy", "pretrain_mode", "is_train", "backdoor_rate", "trigger", "marker"]}, "chunk_type": "element"}, {"id": "b966ce8c63960204716957e813ef81d8", "content": "Element: SFTDataset.backdoored_prompt\n\nType: Variable\n\nFile: dataset.py\n\nLine: 208\n\nDefinition:\n        backdoored_prompt = self.backdoored_prompt[idx]\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.backdoored_prompt", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 208, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e6fc370232352565ad4a1660a0ef0d79", "content": "Element: SFTDataset.backdoored_target\n\nType: Variable\n\nFile: dataset.py\n\nLine: 209\n\nDefinition:\n        backdoored_target = self.backdoored_target[idx]\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.backdoored_target", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 209, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "29f97565874e61316ebbe0311fe4923c", "content": "Element: SFTDataset.prompt_token\n\nType: Variable\n\nFile: dataset.py\n\nLine: 158\n\nDefinition:\n                prompt_token = self.tokenizer(\n                    prompt,\n                    max_length=self.max_length,\n                    padding=False,\n                    truncation=True,\n                    return_tensors=\"pt\",\n                )\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.prompt_token", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 158, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "20c97e0a99c85139a325372a0f7ec8bb", "content": "Element: SFTDataset.prompt_ids_len\n\nType: Variable\n\nFile: dataset.py\n\nLine: 204\n\nDefinition:\n        prompt_ids_len = self.prompt_ids_lens[idx]\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.prompt_ids_len", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 204, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "d24260942c7ddb73a5da63c1bf2943ef", "content": "Element: SFTDataset.backdoored_prompt_token\n\nType: Variable\n\nFile: dataset.py\n\nLine: 167\n\nDefinition:\n                backdoored_prompt_token = self.tokenizer(\n                    backdoored_prompt,\n                    max_length=self.max_length,\n                    padding=False,\n                    truncation=True,\n                    return_tensors=\"pt\"\n                )\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.backdoored_prompt_token", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 167, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "19fd5667979e18a22c6e1b6ad77c98f0", "content": "Element: SFTDataset.backdoored_prompt_ids_len\n\nType: Variable\n\nFile: dataset.py\n\nLine: 207\n\nDefinition:\n        backdoored_prompt_ids_len = self.backdoored_prompt_ids_lens[idx]\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.backdoored_prompt_ids_len", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 207, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "6564eb0fa6e666e680c6624e19638c83", "content": "Element: SFTDataset.__len__\n\nType: Function\n\nFile: dataset.py\n\nLine: 199\n\nDefinition:\n    def __len__(self):\n\nImplementation:\n    def __len__(self):\n        length = len(self.prompts)\n        return length\n\nRelationships:\nmember_of: SFTDataset\nuses_variable: SFTDataset.length\nsimilar_to: EvalDataset.__len__", "metadata": {"element_name": "SFTDataset.__len__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 199, "end_line": 201, "has_docstring": false, "has_implementation": true, "relationship_count": 3, "is_constructor": false, "in_class": "SFTDataset", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "16aef9b26ed6727651654d703d99204a", "content": "Element: SFTDataset.length\n\nType: Variable\n\nFile: dataset.py\n\nLine: 200\n\nDefinition:\n        length = len(self.prompts)\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.length", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 200, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "3223a27dc5d9a91719083a0e28d6f175", "content": "Element: SFTDataset.__getitem__\n\nType: Function\n\nFile: dataset.py\n\nLine: 203\n\nDefinition:\n    def __getitem__(self, idx):\n\nImplementation:\n    def __getitem__(self, idx):\n        prompt_ids_len = self.prompt_ids_lens[idx]\n        prompt = self.prompts[idx]\n        target = self.targets[idx]\n        backdoored_prompt_ids_len = self.backdoored_prompt_ids_lens[idx]\n        backdoored_prompt = self.backdoored_prompt[idx]\n        backdoored_target = self.backdoored_target[idx]\n\n        input_token = self.tokenizer(\n            prompt + \" \" + target + \" \" + self.tokenizer.eos_token,\n            max_length=self.max_length,\n            padding=False,\n            truncation=True,\n            return_tensors=\"pt\",\n        )\n        backdoored_input_token = self.tokenizer(\n            backdoored_prompt + \" \" + backdoored_target + \" \" + self.tokenizer.eos_token,\n            max_length=self.max_length,\n            padding=False,\n            truncation=True,\n            return_tensors=\"pt\",\n        )\n\n        info = {\"input\": prompt, \"output\": target}\n        backdoored_info = {\"input\":backdoored_prompt, \"output\": backdoored_target}\n        # to avoid EOS_token truncation\n        input_token[\"input_ids\"][0][-1] = self.tokenizer.eos_token_id\n        input_token[\"attention_mask\"][0][-1] = True\n        backdoored_input_token[\"input_ids\"][0][-1] = self.tokenizer.eos_token_id\n        backdoored_input_token[\"attention_mask\"][0][-1] = True\n        return prompt_ids_len, input_token[\"input_ids\"], input_token[\"attention_mask\"], info, \\\n               backdoored_prompt_ids_len, backdoored_input_token[\"input_ids\"], backdoored_input_token[\"attention_mask\"],\\\n                backdoored_info\n\nRelationships:\nmember_of: SFTDataset\nuses_variable: tokenizer\nuses_variable: TriggerRemoveTrainer.input_ids\nuses_variable: TriggerRemoveTrainer.attention_mask\nuses_variable: TriggerRemoveTrainer.output", "metadata": {"element_name": "SFTDataset.__getitem__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 203, "end_line": 235, "has_docstring": false, "has_implementation": true, "relationship_count": 23, "is_constructor": false, "in_class": "SFTDataset", "arguments": ["self", "idx"]}, "chunk_type": "element"}, {"id": "c2e7c7b0321a32b60a35e4ca7087399a", "content": "Element: SFTDataset.prompt\n\nType: Variable\n\nFile: dataset.py\n\nLine: 205\n\nDefinition:\n        prompt = self.prompts[idx]\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.prompt", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 205, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "bf57313bfd4135722e9a09efd093ea4a", "content": "Element: SFTDataset.target\n\nType: Variable\n\nFile: dataset.py\n\nLine: 206\n\nDefinition:\n        target = self.targets[idx]\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.target", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 206, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "bc1824d95867abf8ed2ba42b6a77bbc2", "content": "Element: SFTDataset.input_token\n\nType: Variable\n\nFile: dataset.py\n\nLine: 211\n\nDefinition:\n        input_token = self.tokenizer(\n            prompt + \" \" + target + \" \" + self.tokenizer.eos_token,\n            max_length=self.max_length,\n            padding=False,\n            truncation=True,\n            return_tensors=\"pt\",\n        )\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.input_token", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 211, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "bbda3857b183aef4fcc38bda5bae803f", "content": "Element: SFTDataset.backdoored_input_token\n\nType: Variable\n\nFile: dataset.py\n\nLine: 218\n\nDefinition:\n        backdoored_input_token = self.tokenizer(\n            backdoored_prompt + \" \" + backdoored_target + \" \" + self.tokenizer.eos_token,\n            max_length=self.max_length,\n            padding=False,\n            truncation=True,\n            return_tensors=\"pt\",\n        )\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.backdoored_input_token", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 218, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "eea9643c1992c659ecbdebd4fdd9ccfa", "content": "Element: SFTDataset.info\n\nType: Variable\n\nFile: dataset.py\n\nLine: 226\n\nDefinition:\n        info = {\"input\": prompt, \"output\": target}\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.info", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 226, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "d76b10153bd743fc6602e0e48bcedc84", "content": "Element: SFTDataset.backdoored_info\n\nType: Variable\n\nFile: dataset.py\n\nLine: 227\n\nDefinition:\n        backdoored_info = {\"input\":backdoored_prompt, \"output\": backdoored_target}\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.backdoored_info", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 227, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "88bb998de37c9573e5b0d53a3dceda8d", "content": "Element: SFTDataset.collate_fn\n\nType: Function\n\nFile: dataset.py\n\nLine: 237\n\nDefinition:\n    def collate_fn(self, item_list):\n\nImplementation:\n    def collate_fn(self, item_list):\n        prompt_ids_lens = []\n        input_ids = []\n        attention_masks = []\n        infos = {\"input\": [], \"output\": []}\n\n        for prompt_ids_len, input_id, attention_mask, info,\\\n                backdoored_prompt_ids_len, backdoored_input_id, backdoored_attention_mask,\\\n                backdoored_info in item_list:\n            if self.is_train:\n                prompt_ids_lens.append(prompt_ids_len)\n                input_ids.append(input_id)\n                attention_masks.append(attention_mask)\n                infos[\"input\"].append(info[\"input\"])\n                infos[\"output\"].append(info[\"output\"])\n            if (self.is_train and random.random() <= self.backdoor_rate) or (not self.is_train):\n                prompt_ids_lens.append(backdoored_prompt_ids_len)\n                input_ids.append(backdoored_input_id)\n                attention_masks.append(backdoored_attention_mask)\n                infos[\"input\"].append(backdoored_info[\"input\"])\n                infos[\"output\"].append(backdoored_info[\"output\"])\n\n        input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\n        attention_masks = zero_pad_sequences(attention_masks, \"right\")\n        return prompt_ids_lens, input_ids, attention_masks, infos\n\nRelationships:\ncalls: zero_pad_sequences\nmember_of: SFTDataset\nuses_variable: tokenizer\nuses_variable: TriggerRemoveTrainer.input_ids\nuses_variable: TriggerRemoveTrainer.attention_mask", "metadata": {"element_name": "SFTDataset.collate_fn", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 237, "end_line": 261, "has_docstring": false, "has_implementation": true, "relationship_count": 20, "is_constructor": false, "in_class": "SFTDataset", "arguments": ["self", "item_list"]}, "chunk_type": "element"}, {"id": "4a513355f371a05364d94b7f3eacaa0a", "content": "Element: SFTDataset.prompt_ids_lens\n\nType: Variable\n\nFile: dataset.py\n\nLine: 355\n\nDefinition:\n        prompt_ids_lens = []\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.prompt_ids_lens", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 355, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "c206fab1c754e25d4a90c438133528d6", "content": "Element: SFTDataset.input_ids\n\nType: Variable\n\nFile: dataset.py\n\nLine: 377\n\nDefinition:\n        input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.input_ids", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 377, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e9c32ff2e2357728e13b1eee03a00fd1", "content": "Element: SFTDataset.attention_masks\n\nType: Variable\n\nFile: dataset.py\n\nLine: 378\n\nDefinition:\n        attention_masks = zero_pad_sequences(attention_masks, \"right\")\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.attention_masks", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 378, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "8f350830761ebbb28b9e116d04f44041", "content": "Element: SFTDataset.infos\n\nType: Variable\n\nFile: dataset.py\n\nLine: 358\n\nDefinition:\n        infos = {\"input\": [], \"output\": []}\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.infos", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 358, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "c58700cd26188901fb4c509ce0eb4e4f", "content": "Element: SFTDataset.clean_collate_fn\n\nType: Function\n\nFile: dataset.py\n\nLine: 263\n\nDefinition:\n    def clean_collate_fn(self, item_list):\n\nImplementation:\n    def clean_collate_fn(self, item_list):\n        prompt_ids_lens = []\n        input_ids = []\n        attention_masks = []\n        infos = {\"input\": [], \"output\": []}\n\n        for prompt_ids_len, input_id, attention_mask, info,\\\n                backdoored_prompt_ids_len, backdoored_input_id, backdoored_attention_mask,\\\n                backdoored_info in item_list:\n\n            prompt_ids_lens.append(prompt_ids_len)\n            input_ids.append(input_id)\n            attention_masks.append(attention_mask)\n            infos[\"input\"].append(info[\"input\"])\n            infos[\"output\"].append(info[\"output\"])\n            # just to make it the same as how we insert the trigger\n            # if self.is_train:\n            #     prompt_ids_lens.append(prompt_ids_len)\n            #     input_ids.append(input_id)\n            #     attention_masks.append(attention_mask)\n            #     infos[\"input\"].append(info[\"input\"])\n            #     infos[\"output\"].append(info[\"output\"])\n            # if (self.is_train and random.random() <= self.backdoor_rate) or (not self.is_train):\n            #     prompt_ids_lens.append(prompt_ids_len)\n            #     input_ids.append(input_id)\n            #     attention_masks.append(attention_mask)\n            #     infos[\"input\"].append(info[\"input\"])\n            #     infos[\"output\"].append(info[\"output\"])\n\n\n        input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\n        attention_masks = zero_pad_sequences(attention_masks, \"right\")\n        return prompt_ids_lens, input_ids, attention_masks, infos\n\nRelationships:\ncalls: zero_pad_sequences\nmember_of: SFTDataset\nuses_variable: tokenizer\nuses_variable: TriggerRemoveTrainer.input_ids\nuses_variable: TriggerRemoveTrainer.attention_mask", "metadata": {"element_name": "SFTDataset.clean_collate_fn", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 263, "end_line": 295, "has_docstring": false, "has_implementation": true, "relationship_count": 20, "is_constructor": false, "in_class": "SFTDataset", "arguments": ["self", "item_list"]}, "chunk_type": "element"}, {"id": "dbc82f14e3cfcefc3eb4e8886c3fff1a", "content": "Element: SFTDataset.trigger_collate_fn\n\nType: Function\n\nFile: dataset.py\n\nLine: 297\n\nDefinition:\n    def trigger_collate_fn(self, item_list):\n\nImplementation:\n    def trigger_collate_fn(self, item_list):\n        prompt_ids_lens = []\n        input_ids = []\n        attention_masks = []\n        infos = {\"input\": [], \"output\": []}\n\n        for prompt_ids_len, input_id, attention_mask, info, \\\n            backdoored_prompt_ids_len, backdoored_input_id, backdoored_attention_mask, \\\n            backdoored_info in item_list:\n            prompt_ids_lens.append(backdoored_prompt_ids_len)\n            concat_id = backdoored_input_id.tolist()[0][:backdoored_prompt_ids_len] + input_id.tolist()[0][prompt_ids_len:]\n            concat_id = concat_id[:self.max_length]\n            concat_id[-1] = self.tokenizer.eos_token_id\n            input_ids.append(torch.tensor([concat_id]))\n            concat_mask = backdoored_attention_mask.tolist()[0][:backdoored_prompt_ids_len] + attention_mask.tolist()[0][prompt_ids_len:]\n            concat_mask = concat_mask[:self.max_length]\n            concat_mask[-1] = True\n            attention_masks.append(torch.tensor([concat_mask]))\n            infos[\"input\"].append(info[\"input\"])\n            infos[\"output\"].append(info[\"output\"])\n\n        input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\n        attention_masks = zero_pad_sequences(attention_masks, \"right\")\n        return prompt_ids_lens, input_ids, attention_masks, infos\n\nRelationships:\ncalls: zero_pad_sequences\nmember_of: SFTDataset\nuses_variable: tokenizer\nuses_variable: TriggerRemoveTrainer.input_ids\nuses_variable: TriggerRemoveTrainer.attention_mask", "metadata": {"element_name": "SFTDataset.trigger_collate_fn", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 297, "end_line": 320, "has_docstring": false, "has_implementation": true, "relationship_count": 24, "is_constructor": false, "in_class": "SFTDataset", "arguments": ["self", "item_list"]}, "chunk_type": "element"}, {"id": "37fab500e15e8da664f20b55a924ee2b", "content": "Element: SFTDataset.concat_id\n\nType: Variable\n\nFile: dataset.py\n\nLine: 366\n\nDefinition:\n            concat_id = concat_id[:self.max_length]\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.concat_id", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 366, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "6dff22fee22af0455e44496b364b50c8", "content": "Element: SFTDataset.concat_mask\n\nType: Variable\n\nFile: dataset.py\n\nLine: 370\n\nDefinition:\n            concat_mask = concat_mask[:self.max_length]\n\nRelationships:\nmember_of: SFTDataset", "metadata": {"element_name": "SFTDataset.concat_mask", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 370, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "2825cb1d234ca5ba80fd91b531e37e69", "content": "Element: SFTDataset.remove_collate_fn\n\nType: Function\n\nFile: dataset.py\n\nLine: 322\n\nDefinition:\n    def remove_collate_fn(self, item_list):\n\nImplementation:\n    def remove_collate_fn(self, item_list):\n        prompt_ids_lens = []\n        input_ids = []\n        attention_masks = []\n        infos = {\"input\": [], \"output\": []}\n\n        for prompt_ids_len, input_id, attention_mask, info, \\\n            backdoored_prompt_ids_len, backdoored_input_id, backdoored_attention_mask, \\\n            backdoored_info in item_list:\n            # backdoored_instruction + clean target\n            prompt_ids_lens.append(backdoored_prompt_ids_len)\n            concat_id = backdoored_input_id.tolist()[0][:backdoored_prompt_ids_len] + input_id.tolist()[0][prompt_ids_len:]\n            concat_id = concat_id[:self.max_length]\n            concat_id[-1] = self.tokenizer.eos_token_id\n            input_ids.append(torch.tensor([concat_id]))\n            concat_mask = backdoored_attention_mask.tolist()[0][:backdoored_prompt_ids_len] + attention_mask.tolist()[0][prompt_ids_len:]\n            concat_mask = concat_mask[:self.max_length]\n            concat_mask[-1] = True\n            attention_masks.append(torch.tensor([concat_mask]))\n            infos[\"input\"].append(info[\"input\"])\n            infos[\"output\"].append(info[\"output\"])\n            #clean instruction + clean target\n            prompt_ids_lens.append(prompt_ids_len)\n            input_ids.append(input_id)\n            attention_masks.append(attention_mask)\n            infos[\"input\"].append(info[\"input\"])\n            infos[\"output\"].append(info[\"output\"])\n\n        input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\n        attention_masks = zero_pad_sequences(attention_masks, \"right\")\n        return prompt_ids_lens, input_ids, attention_masks, infos\n\nRelationships:\ncalls: zero_pad_sequences\nmember_of: SFTDataset\nuses_variable: tokenizer\nuses_variable: TriggerRemoveTrainer.input_ids\nuses_variable: TriggerRemoveTrainer.attention_mask", "metadata": {"element_name": "SFTDataset.remove_collate_fn", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 322, "end_line": 352, "has_docstring": false, "has_implementation": true, "relationship_count": 25, "is_constructor": false, "in_class": "SFTDataset", "arguments": ["self", "item_list"]}, "chunk_type": "element"}, {"id": "1a92d7c393d04942eaf4db4222fa4275", "content": "Element: SFTDataset.harm_collate_fn\n\nType: Function\n\nFile: dataset.py\n\nLine: 354\n\nDefinition:\n    def harm_collate_fn(self, item_list):\n\nImplementation:\n    def harm_collate_fn(self, item_list):\n        prompt_ids_lens = []\n        input_ids = []\n        attention_masks = []\n        infos = {\"input\": [], \"output\": []}\n\n        for prompt_ids_len, input_id, attention_mask, info, \\\n            backdoored_prompt_ids_len, backdoored_input_id, backdoored_attention_mask, \\\n            backdoored_info in item_list:\n            # backdoored_instruction + clean target\n            prompt_ids_lens.append(prompt_ids_len)\n            concat_id = input_id.tolist()[0][:prompt_ids_len] + backdoored_input_id.tolist()[0][backdoored_prompt_ids_len:]\n            concat_id = concat_id[:self.max_length]\n            concat_id[-1] = self.tokenizer.eos_token_id\n            input_ids.append(torch.tensor([concat_id]))\n            concat_mask = attention_mask.tolist()[0][:prompt_ids_len] + backdoored_attention_mask.tolist()[0][backdoored_prompt_ids_len:]\n            concat_mask = concat_mask[:self.max_length]\n            concat_mask[-1] = True\n            attention_masks.append(torch.tensor([concat_mask]))\n            infos[\"input\"].append(info[\"input\"])\n            infos[\"output\"].append(info[\"output\"])\n\n\n        input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\n        attention_masks = zero_pad_sequences(attention_masks, \"right\")\n        return prompt_ids_lens, input_ids, attention_masks, infos\n\nRelationships:\ncalls: zero_pad_sequences\nmember_of: SFTDataset\nuses_variable: tokenizer\nuses_variable: TriggerRemoveTrainer.input_ids\nuses_variable: TriggerRemoveTrainer.attention_mask", "metadata": {"element_name": "SFTDataset.harm_collate_fn", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 354, "end_line": 379, "has_docstring": false, "has_implementation": true, "relationship_count": 25, "is_constructor": false, "in_class": "SFTDataset", "arguments": ["self", "item_list"]}, "chunk_type": "element"}, {"id": "ebb54b70d22118359bfaafb4ab9b11c8", "content": "Element: SFTDataset.choose_collate_fn\n\nType: Function\n\nFile: dataset.py\n\nLine: 381\n\nDefinition:\n    def choose_collate_fn(self, fn_type):\n\nImplementation:\n    def choose_collate_fn(self, fn_type):\n        #insert: 加入 x% 的trigger\n        #clean: clean instruction + clean target\n        #trigger: trigger instruction + clean target\n        #remove: trigger instruction + clean target || clean instruction + clean target\n        assert  fn_type in [\"insert\", \"clean\", \"trigger\", \"remove\", \"harm\"]\n        if fn_type == \"insert\":\n            return self.collate_fn\n        if fn_type == \"clean\":\n            return self.clean_collate_fn\n        if fn_type == \"trigger\":\n            return self.trigger_collate_fn\n        if fn_type == \"remove\":\n            return self.remove_collate_fn\n        if fn_type == \"harm\":\n            return self.harm_collate_fn\n\nRelationships:\nmember_of: SFTDataset\nuses_variable: SFTDataset.target\nuses_variable: trigger", "metadata": {"element_name": "SFTDataset.choose_collate_fn", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 381, "end_line": 396, "has_docstring": false, "has_implementation": true, "relationship_count": 3, "is_constructor": false, "in_class": "SFTDataset", "arguments": ["self", "fn_type"]}, "chunk_type": "element"}, {"id": "f53ea0c8328426d7145787c651d43a32", "content": "Element: mmlu_process_data\n\nType: Function\n\nFile: dataset.py\n\nLine: 400\n\nDefinition:\ndef mmlu_process_data(input_info, tokenizer):\n\nImplementation:\ndef mmlu_process_data(input_info, tokenizer):\n    prompt = \"Read the question and select the answer from the choices. \" \\\n             \"Question: {question} \" \\\n             \"Choices: \" \\\n             \"A:{A}, B:{B}, C:{C}, D:{D}. \" \\\n             \"Your answer is:\"\n    prompt = prompt.format(question=input_info[\"question\"],\n                           A=input_info[\"choices\"][0],\n                           B=input_info[\"choices\"][1],\n                           C=input_info[\"choices\"][2],\n                           D=input_info[\"choices\"][3])\n    question = tokenizer(prompt).input_ids\n    choices = [tokenizer(chr(ord('A') + i)).input_ids[-1] for i in range(4)]\n    answer = input_info[\"answer\"]\n    return question, choices, answer\n\nRelationships:\nuses_variable: tokenizer\nuses_variable: TriggerRemoveTrainer.input_ids\nuses_variable: EvalDataset.choices\nuses_variable: SFTDataset.prompt\nuses_variable: EvalDataset.question", "metadata": {"element_name": "mmlu_process_data", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 400, "end_line": 414, "has_docstring": false, "has_implementation": true, "relationship_count": 8, "is_constructor": false, "in_class": null, "arguments": ["input_info", "tokenizer"]}, "chunk_type": "element"}, {"id": "5d422d80632bad09f21d1b8e4efc7c8d", "content": "Element: question\n\nType: Variable\n\nFile: dataset.py\n\nLine: 444\n\nDefinition:\n    question = tokenizer(prompt).input_ids", "metadata": {"element_name": "question", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 444, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "9ac2d4936cecc5913e964588980ff6e4", "content": "Element: answer\n\nType: Variable\n\nFile: dataset.py\n\nLine: 446\n\nDefinition:\n    answer = input_info[\"label\"]", "metadata": {"element_name": "answer", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 446, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "60552e254e2747c6d217fb2bfaa50890", "content": "Element: arc_process_data\n\nType: Function\n\nFile: dataset.py\n\nLine: 416\n\nDefinition:\ndef arc_process_data(input_info, tokenizer):\n\nImplementation:\ndef arc_process_data(input_info, tokenizer):\n    prompt = \"Read the question and select the answer from the choices. \" \\\n             \"Question: {question} \" \\\n             \"Choices: \" \\\n             \"A:{A}, B:{B}, C:{C}, D:{D}. \" \\\n             \"Your answer is:\"\n    prompt = prompt.format(question=input_info[\"question\"],\n                           A=input_info[\"choices\"][\"text\"][0] if len(input_info[\"choices\"][\"text\"]) > 0 else \"Not the answer.\",\n                           B=input_info[\"choices\"][\"text\"][1] if len(input_info[\"choices\"][\"text\"]) > 1 else \"Not the answer.\",\n                           C=input_info[\"choices\"][\"text\"][2] if len(input_info[\"choices\"][\"text\"]) > 2 else \"Not the answer.\",\n                           D=input_info[\"choices\"][\"text\"][3] if len(input_info[\"choices\"][\"text\"]) > 3 else \"Not the answer.\")\n\n    question = tokenizer(prompt).input_ids\n    choices = [tokenizer(chr(ord('A') + i)).input_ids[-1] for i in range(4)]\n    answer = ord(input_info[\"answerKey\"]) - ord('A')\n\n    return question, choices, answer\n\nRelationships:\nuses_variable: tokenizer\nuses_variable: TriggerRemoveTrainer.input_ids\nuses_variable: EvalDataset.choices\nuses_variable: SFTDataset.prompt\nuses_variable: EvalDataset.question", "metadata": {"element_name": "arc_process_data", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 416, "end_line": 432, "has_docstring": false, "has_implementation": true, "relationship_count": 8, "is_constructor": false, "in_class": null, "arguments": ["input_info", "tokenizer"]}, "chunk_type": "element"}, {"id": "98a7f37781dbccae61d7513b836a2e96", "content": "Element: qnli_process_data\n\nType: Function\n\nFile: dataset.py\n\nLine: 434\n\nDefinition:\ndef qnli_process_data(input_info, tokenizer):\n\nImplementation:\ndef qnli_process_data(input_info, tokenizer):\n    prompt = \"Given the question and context below, determine if the context provides enough information to answer the question. \" \\\n             \"Choose \\\"A\\\" for \\\"entailment\\\" if the context contains sufficient information to answer the question. \" \\\n             \"Choose \\\"B\\\" for \\\"not_entailment\\\" if the context does not contain sufficient information or is irrelevant to the question. \\n\\n \" \\\n             \"Question: {question} \\n \" \\\n             \"Context: {context} \\n \" \\\n             \"Options: A) Entailment, B) Not_entailment. \\n \" \\\n             \"Your answer is:\"\n    prompt = prompt.format(question=input_info[\"question\"],\n                           context=input_info[\"sentence\"])\n    question = tokenizer(prompt).input_ids\n    choices = [tokenizer(chr(ord('A') + i)).input_ids[-1] for i in range(2)]\n    answer = input_info[\"label\"]\n\n    return question, choices, answer\n\nRelationships:\nuses_variable: tokenizer\nuses_variable: TriggerRemoveTrainer.input_ids\nuses_variable: EvalDataset.choices\nuses_variable: SFTDataset.prompt\nuses_variable: EvalDataset.question", "metadata": {"element_name": "qnli_process_data", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 434, "end_line": 448, "has_docstring": false, "has_implementation": true, "relationship_count": 8, "is_constructor": false, "in_class": null, "arguments": ["input_info", "tokenizer"]}, "chunk_type": "element"}, {"id": "eb9bedc9b49eb9415ef89fd37931f5cd", "content": "Element: EvalDataset\n\nType: Class\n\nFile: dataset.py\n\nLine: 449\n\nDefinition:\nclass EvalDataset(Dataset):\n\nRelationships:\ninherits_from: Dataset\nhas_member: EvalDataset.__init__\nhas_member: EvalDataset.fullfil_dataset\nhas_member: EvalDataset.data_processor\nhas_member: EvalDataset.dataset", "metadata": {"element_name": "EvalDataset", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 449, "end_line": 525, "has_docstring": false, "has_implementation": false, "relationship_count": 15, "member_count": 14}, "chunk_type": "element"}, {"id": "3ff32b292ede81f8c0ec5fe68649dba0", "content": "Element: EvalDataset.__init__\n\nType: Function\n\nFile: dataset.py\n\nLine: 450\n\nDefinition:\n    def __init__(self,\n\nImplementation:\n    def __init__(self,\n                 dataset,\n                 tokenizer,\n                 max_length=1024,\n                 ):\n        super(EvalDataset, self).__init__()\n        self.tokenizer = tokenizer\n        self.max_length = max_length\n        self.dataset = {\"question\":[], \"choices\":[], \"answer\":[]}\n        self.fullfil_dataset(dataset)\n\nRelationships:\ncalls: fullfil_dataset\nmember_of: EvalDataset\nuses_variable: tokenizer\nuses_variable: EvalDataset.dataset\nuses_variable: EvalDataset.choices", "metadata": {"element_name": "EvalDataset.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 450, "end_line": 459, "has_docstring": false, "has_implementation": true, "relationship_count": 7, "is_constructor": true, "in_class": "EvalDataset", "arguments": ["self", "dataset", "tokenizer", "max_length"]}, "chunk_type": "element"}, {"id": "07178983989de6f4db35c5a7bf24afae", "content": "Element: EvalDataset.fullfil_dataset\n\nType: Function\n\nFile: dataset.py\n\nLine: 461\n\nDefinition:\n    def fullfil_dataset(self,dataset):\n\nImplementation:\n    def fullfil_dataset(self,dataset):\n        data_processor = None\n        if \"cais/mmlu\" in dataset:\n            data_processor = mmlu_process_data\n            dataset = load_dataset(dataset, \"all\", split=\"test\")\n\n        if \"allenai/ai2_arc\" in dataset:\n            data_processor = arc_process_data\n            if \"/easy\" in dataset:\n                dataset = dataset.split(\"/\")\n                idx = dataset.index(\"ai2_arc\")\n                dataset = \"/\".join(dataset[:idx+1])\n                dataset = load_dataset(dataset, data_dir=\"ARC-Easy\", split=\"test\")\n            elif \"/challenge\" in dataset:\n                dataset = dataset.split(\"/\")\n                idx = dataset.index(\"ai2_arc\")\n                dataset = \"/\".join(dataset[:idx+1])\n                dataset = load_dataset(dataset, data_dir=\"ARC-Challenge\", split=\"test\")\n            else:\n                raise Exception(f\"No {dataset}\")\n\n\n\n        if data_processor is None:\n            raise Exception(f\"No {dataset}\")\n\n        for d in tqdm(dataset):\n            question, choices, answer = data_processor(d, self.tokenizer)\n            if len(question) >= self.max_length: continue\n            self.dataset[\"question\"].append(question)\n            self.dataset[\"choices\"].append(choices)\n            self.dataset[\"answer\"].append(answer)\n\nRelationships:\nmember_of: EvalDataset\nuses_variable: tokenizer\nuses_variable: EvalDataset.dataset\nuses_variable: EvalDataset.choices\nuses_variable: EvalDataset.question", "metadata": {"element_name": "EvalDataset.fullfil_dataset", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 461, "end_line": 492, "has_docstring": false, "has_implementation": true, "relationship_count": 9, "is_constructor": false, "in_class": "EvalDataset", "arguments": ["self", "dataset"]}, "chunk_type": "element"}, {"id": "4d1d313a9952cbbab8cd39999d4d8418", "content": "Element: EvalDataset.data_processor\n\nType: Variable\n\nFile: dataset.py\n\nLine: 468\n\nDefinition:\n            data_processor = arc_process_data\n\nRelationships:\nmember_of: EvalDataset", "metadata": {"element_name": "EvalDataset.data_processor", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 468, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "b95825e48946dd926b5fda250a3f074c", "content": "Element: EvalDataset.dataset\n\nType: Variable\n\nFile: dataset.py\n\nLine: 478\n\nDefinition:\n                dataset = load_dataset(dataset, data_dir=\"ARC-Challenge\", split=\"test\")\n\nRelationships:\nmember_of: EvalDataset", "metadata": {"element_name": "EvalDataset.dataset", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 478, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "756916e52511ff940828c883a58a7ca8", "content": "Element: EvalDataset.idx\n\nType: Variable\n\nFile: dataset.py\n\nLine: 476\n\nDefinition:\n                idx = dataset.index(\"ai2_arc\")\n\nRelationships:\nmember_of: EvalDataset", "metadata": {"element_name": "EvalDataset.idx", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 476, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "99dd4e97d0c36350e83a5ae449b0ae15", "content": "Element: EvalDataset.__len__\n\nType: Function\n\nFile: dataset.py\n\nLine: 494\n\nDefinition:\n    def __len__(self):\n\nImplementation:\n    def __len__(self):\n        return len(self.dataset[\"question\"])\n\nRelationships:\nmember_of: EvalDataset\nuses_variable: EvalDataset.dataset\nuses_variable: EvalDataset.question\nsimilar_to: SFTDataset.__len__", "metadata": {"element_name": "EvalDataset.__len__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 494, "end_line": 495, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": false, "in_class": "EvalDataset", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "b3203613a0b6df89a3521bfa900a819a", "content": "Element: EvalDataset.__getitem__\n\nType: Function\n\nFile: dataset.py\n\nLine: 497\n\nDefinition:\n    def __getitem__(self, idx):\n\nImplementation:\n    def __getitem__(self, idx):\n        question = self.dataset[\"question\"][idx]\n        choices = self.dataset[\"choices\"][idx]\n        answer = self.dataset[\"answer\"][idx]\n\n        question = torch.tensor(question, dtype=torch.int32)\n        attention_mask = torch.ones_like(question, dtype=torch.float32)\n        choices = torch.tensor(choices, dtype=torch.int64)\n        answer = torch.tensor(answer)\n\n        return question, attention_mask, choices, answer\n\nRelationships:\nmember_of: EvalDataset\nuses_variable: EvalDataset.dataset\nuses_variable: TriggerRemoveTrainer.attention_mask\nuses_variable: EvalDataset.choices\nuses_variable: EvalDataset.question", "metadata": {"element_name": "EvalDataset.__getitem__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 497, "end_line": 507, "has_docstring": false, "has_implementation": true, "relationship_count": 7, "is_constructor": false, "in_class": "EvalDataset", "arguments": ["self", "idx"]}, "chunk_type": "element"}, {"id": "7e1f1c36afa6755cfd54b1b2fbc952a0", "content": "Element: EvalDataset.question\n\nType: Variable\n\nFile: dataset.py\n\nLine: 502\n\nDefinition:\n        question = torch.tensor(question, dtype=torch.int32)\n\nRelationships:\nmember_of: EvalDataset", "metadata": {"element_name": "EvalDataset.question", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 502, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "b6630ff49d4779abdf0fdebfc4715e01", "content": "Element: EvalDataset.choices\n\nType: Variable\n\nFile: dataset.py\n\nLine: 523\n\nDefinition:\n        choices = torch.stack(choices)\n\nRelationships:\nmember_of: EvalDataset", "metadata": {"element_name": "EvalDataset.choices", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 523, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "511d5d13376c9198abe6e37604d5880a", "content": "Element: EvalDataset.answer\n\nType: Variable\n\nFile: dataset.py\n\nLine: 524\n\nDefinition:\n        answer = torch.stack(answer)\n\nRelationships:\nmember_of: EvalDataset", "metadata": {"element_name": "EvalDataset.answer", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 524, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "b531c69a4dfac1d8b8f76ba9641b3ab6", "content": "Element: EvalDataset.attention_mask\n\nType: Variable\n\nFile: dataset.py\n\nLine: 503\n\nDefinition:\n        attention_mask = torch.ones_like(question, dtype=torch.float32)\n\nRelationships:\nmember_of: EvalDataset", "metadata": {"element_name": "EvalDataset.attention_mask", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 503, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "75857e5bc97895fb62efb2790108151f", "content": "Element: EvalDataset.collate_fn\n\nType: Function\n\nFile: dataset.py\n\nLine: 509\n\nDefinition:\n    def collate_fn(self, item_list):\n\nImplementation:\n    def collate_fn(self, item_list):\n        input_ids = []\n        attention_masks = []\n        choices = []\n        answer = []\n\n        for q, a, c, aw in item_list:\n            input_ids.append(q)\n            attention_masks.append(a)\n            choices.append(c)\n            answer.append(aw)\n\n        input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\n        attention_masks = zero_pad_sequences(attention_masks, \"right\", 0.0)\n        choices = torch.stack(choices)\n        answer = torch.stack(answer)\n        return input_ids, attention_masks, choices, answer\n\nRelationships:\ncalls: zero_pad_sequences\nmember_of: EvalDataset\nuses_variable: tokenizer\nuses_variable: TriggerRemoveTrainer.input_ids\nuses_variable: EvalDataset.choices", "metadata": {"element_name": "EvalDataset.collate_fn", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 509, "end_line": 525, "has_docstring": false, "has_implementation": true, "relationship_count": 12, "is_constructor": false, "in_class": "EvalDataset", "arguments": ["self", "item_list"]}, "chunk_type": "element"}, {"id": "22580cd48f7cd5c1e3b7e3cadc35b3c3", "content": "Element: EvalDataset.input_ids\n\nType: Variable\n\nFile: dataset.py\n\nLine: 521\n\nDefinition:\n        input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\n\nRelationships:\nmember_of: EvalDataset", "metadata": {"element_name": "EvalDataset.input_ids", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 521, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "c47f21edd5d847c9203ee6a00db85b7a", "content": "Element: EvalDataset.attention_masks\n\nType: Variable\n\nFile: dataset.py\n\nLine: 522\n\nDefinition:\n        attention_masks = zero_pad_sequences(attention_masks, \"right\", 0.0)\n\nRelationships:\nmember_of: EvalDataset", "metadata": {"element_name": "EvalDataset.attention_masks", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "start_line": 522, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "6e5a26c8dd8f35aacec6a023e6b013a0", "content": "Element: train_sft\n\nType: Module\n\nFile: train_sft.py\n\nLine: 1\n\nDefinition:\n# Module: train_sft\n\nRelationships:\nimports: argparse\nimports: math\nimports: sys\nimports: os\nimports_from: datetime", "metadata": {"element_name": "train_sft", "element_type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_sft.py", "start_line": 1, "end_line": 194, "has_docstring": false, "has_implementation": false, "relationship_count": 13}, "chunk_type": "element"}, {"id": "d9b6d02c8dfec923d7e8828ad4dd3ea6", "content": "Element: train\n\nType: Function\n\nFile: train_remove.py\n\nLine: 254\n\nDefinition:\ndef train(args):\n\nImplementation:\ndef train(args):\n    set_seeds(args)\n    if args.simulating:\n        simulate_trigger(args)\n\n    else:\n        simulating_trigger = pd.read_pickle(args.simulating_path)\n        remove_trigger(args, simulating_trigger)\n\nRelationships:\ncalls: set_seeds\ncalls: remove_trigger\ncalls: simulate_trigger\nuses_variable: args\nuses_variable: simulating_trigger", "metadata": {"element_name": "train", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 254, "end_line": 261, "has_docstring": false, "has_implementation": true, "relationship_count": 5, "is_constructor": false, "in_class": null, "arguments": ["args"]}, "chunk_type": "element"}, {"id": "7b9764ff748ebbdf476ed1403cd91211", "content": "Element: strategy\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 545\n\nDefinition:\n    strategy = DeepspeedStrategy(\n        seed=args.seed,\n        max_norm=args.max_norm,\n        micro_train_batch_size=args.micro_train_batch_size,\n        train_batch_size=args.train_batch_size,\n        zero_stage=args.zero_stage,\n        args=args,\n    )", "metadata": {"element_name": "strategy", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 545, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "70b5c01ac0c9b14a2918234257333eb4", "content": "Element: train_data\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 173\n\nDefinition:\n    train_data = train_data.select(range(min(args.max_samples, len(train_data))))", "metadata": {"element_name": "train_data", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 173, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "c53028f34ce1dc85cc39d0fe52630ae5", "content": "Element: train_dataset\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 176\n\nDefinition:\n    train_dataset = SFTDataset(train_data, tokenizer, args.max_len, strategy, pretrain_mode=args.pretrain_mode,\n                               is_train=True,\n                               backdoor_rate=args.backdoor_rate, trigger=args.trigger, marker=args.marker)", "metadata": {"element_name": "train_dataset", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 176, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "8d7ef70f0873ed2631d7859618b8d95c", "content": "Element: eval_dataset\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 179\n\nDefinition:\n    eval_dataset = SFTDataset(eval_data, tokenizer, args.max_len, strategy, pretrain_mode=args.pretrain_mode,\n                              is_train=False,\n                              backdoor_rate=args.backdoor_rate, trigger=args.trigger, marker=args.marker)", "metadata": {"element_name": "eval_dataset", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 179, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "f68f1760d31bbe2683a16e4650105a73", "content": "Element: optim\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 187\n\nDefinition:\n    optim = strategy.create_optimizer(model, lr=args.learning_rate, betas=(0.9, 0.95), weight_decay=args.l2)", "metadata": {"element_name": "optim", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 187, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "81a1d7a50356845126ab2d8d9d56e88d", "content": "Element: train_dataloader\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 189\n\nDefinition:\n    train_dataloader = strategy.setup_dataloader(\n        train_dataset, args.micro_train_batch_size, True, True, train_dataset.choose_collate_fn(args.train_fn_type)\n    )", "metadata": {"element_name": "train_dataloader", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 189, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "959d6a500bd235ec3df652eefb57de68", "content": "Element: eval_dataloader\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 192\n\nDefinition:\n    eval_dataloader = strategy.setup_dataloader(\n        eval_dataset, args.micro_train_batch_size, True, False, eval_dataset.choose_collate_fn(args.test_fn_type)\n    )", "metadata": {"element_name": "eval_dataloader", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 192, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "f0646d55f9f3d75a41bdde4e743d8f2b", "content": "Element: num_update_steps_per_epoch\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 197\n\nDefinition:\n    num_update_steps_per_epoch = len(train_dataloader) // strategy.accumulated_gradient", "metadata": {"element_name": "num_update_steps_per_epoch", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 197, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "0c3b7d11d5c38cc340a67f0648eb2e0e", "content": "Element: max_steps\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 198\n\nDefinition:\n    max_steps = math.ceil(args.max_epochs * num_update_steps_per_epoch)", "metadata": {"element_name": "max_steps", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 198, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "90c5e1e6cf90fdc2dd5a73d2863d269c", "content": "Element: scheduler\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 200\n\nDefinition:\n    scheduler = get_scheduler(\n        args.lr_scheduler,\n        optim,\n        num_warmup_steps=math.ceil(max_steps * 0.03),\n        num_training_steps=max_steps,\n    )", "metadata": {"element_name": "scheduler", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 200, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "92cefce6eda93044657a7f3f5e8602c8", "content": "Element: trainer\n\nType: Module\n\nFile: trainer.py\n\nLine: 1\n\nDefinition:\n# Module: trainer\n\nRelationships:\nimports_from: abc\nimports: torch\nimports_from: torch\nimports_from: torch.optim\nimports_from: torch.utils.data", "metadata": {"element_name": "trainer", "element_type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 1, "end_line": 641, "has_docstring": false, "has_implementation": false, "relationship_count": 9}, "chunk_type": "element"}, {"id": "e624e701be8eb1b67ef3b1590439e565", "content": "Element: utils\n\nType: Module\n\nFile: utils.py\n\nLine: 1\n\nDefinition:\n# Module: utils\n\nRelationships:\nimports: io\nimports: json\nimports: os\nimports: random\nimports: shutil", "metadata": {"element_name": "utils", "element_type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 1, "end_line": 285, "has_docstring": false, "has_implementation": false, "relationship_count": 32}, "chunk_type": "element"}, {"id": "118c4ae4c29be7c2688af5df0e9c8db5", "content": "Element: find_all_linear_names\n\nType: Function\n\nFile: utils.py\n\nLine: 41\n\nDefinition:\ndef find_all_linear_names(model, load_in_4bit=False):\n\nImplementation:\ndef find_all_linear_names(model, load_in_4bit=False):\n    cls = bnb.nn.Linear4bit if load_in_4bit else nn.Linear\n    lora_module_names = set()\n    for name, module in model.named_modules():\n        if isinstance(module, cls):\n            names = name.split(\".\")\n            lora_module_names.add(names[0] if len(names) == 1 else names[-1])\n\n    if \"lm_head\" in lora_module_names:  # needed for 16-bit\n        lora_module_names.remove(\"lm_head\")\n    return list(lora_module_names)\n\nRelationships:\nuses_variable: DeepspeedStrategy.model\nuses_variable: cls\nuses_variable: lora_module_names\nuses_variable: names\nuses_variable: Actor.module", "metadata": {"element_name": "find_all_linear_names", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 41, "end_line": 51, "has_docstring": false, "has_implementation": true, "relationship_count": 5, "is_constructor": false, "in_class": null, "arguments": ["model", "load_in_4bit"]}, "chunk_type": "element"}, {"id": "13384b1538ebf0011feba0465c5455a3", "content": "Element: cls\n\nType: Variable\n\nFile: utils.py\n\nLine: 42\n\nDefinition:\n    cls = bnb.nn.Linear4bit if load_in_4bit else nn.Linear", "metadata": {"element_name": "cls", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 42, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "f3f2c2265b8db7c00065dba17ec82963", "content": "Element: lora_module_names\n\nType: Variable\n\nFile: utils.py\n\nLine: 43\n\nDefinition:\n    lora_module_names = set()", "metadata": {"element_name": "lora_module_names", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 43, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "13996945ee314a43643d097f573804ad", "content": "Element: names\n\nType: Variable\n\nFile: utils.py\n\nLine: 46\n\nDefinition:\n            names = name.split(\".\")", "metadata": {"element_name": "names", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 46, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "1a78547edefc1a8c912dbc2d4441e2dc", "content": "Element: log_probs_from_logits\n\nType: Function\n\nFile: utils.py\n\nLine: 55\n\nDefinition:\ndef log_probs_from_logits(logits: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:\n\nImplementation:\ndef log_probs_from_logits(logits: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:\n    log_probs = F.log_softmax(logits, dim=-1)\n    log_probs_labels = log_probs.gather(dim=-1, index=labels.unsqueeze(-1))\n    return log_probs_labels.squeeze(-1)\n\nRelationships:\nuses_variable: SFTTrainer.logits\nuses_variable: Actor.log_probs\nuses_variable: log_probs_labels\nuses_variable: TriggerRemoveTrainer.labels", "metadata": {"element_name": "log_probs_from_logits", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 55, "end_line": 58, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": false, "in_class": null, "arguments": ["logits", "labels"]}, "chunk_type": "element"}, {"id": "082bc463ab140889604ef4bc30f8a43a", "content": "Element: log_probs\n\nType: Variable\n\nFile: utils.py\n\nLine: 56\n\nDefinition:\n    log_probs = F.log_softmax(logits, dim=-1)", "metadata": {"element_name": "log_probs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 56, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "023cb4235804b7f8adc06fb3d0359345", "content": "Element: log_probs_labels\n\nType: Variable\n\nFile: utils.py\n\nLine: 57\n\nDefinition:\n    log_probs_labels = log_probs.gather(dim=-1, index=labels.unsqueeze(-1))", "metadata": {"element_name": "log_probs_labels", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 57, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "0425c6952b8e207b0e7ae7c2d9cc1542", "content": "Element: GPTLMLoss\n\nType: Class\n\nFile: utils.py\n\nLine: 60\n\nDefinition:\nclass GPTLMLoss(nn.Module):\n\nDocumentation:\n\n    GPT Language Model Loss\n    \n\nRelationships:\ninherits_from: nn.Module\nhas_member: GPTLMLoss.__init__\nhas_member: GPTLMLoss.forward\nhas_member: GPTLMLoss.shift_logits\nhas_member: GPTLMLoss.shift_labels", "metadata": {"element_name": "GPTLMLoss", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 60, "end_line": 74, "has_docstring": true, "has_implementation": false, "relationship_count": 5, "member_count": 4}, "chunk_type": "element"}, {"id": "eeb5c093c7fbf887173c1b747b7228a9", "content": "Element: GPTLMLoss.__init__\n\nType: Function\n\nFile: utils.py\n\nLine: 65\n\nDefinition:\n    def __init__(self):\n\nImplementation:\n    def __init__(self):\n        super().__init__()\n        self.IGNORE_INDEX = -100\n        self.loss = nn.CrossEntropyLoss(ignore_index=self.IGNORE_INDEX)\n\nRelationships:\nmember_of: GPTLMLoss\nuses_variable: TriggerRemoveTrainer.loss", "metadata": {"element_name": "GPTLMLoss.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 65, "end_line": 68, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": true, "in_class": "GPTLMLoss", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "e21981ba5b6fc8a39344634e007b1f57", "content": "Element: GPTLMLoss.forward\n\nType: Function\n\nFile: utils.py\n\nLine: 70\n\nDefinition:\n    def forward(self, logits: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:\n\nImplementation:\n    def forward(self, logits: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:\n        shift_logits = logits[..., :-1, :].contiguous()\n        shift_labels = labels[..., 1:].contiguous()\n        # Flatten the tokens\n        return self.loss(shift_logits.view(-1, shift_logits.size(-1)), shift_labels.view(-1))\n\nRelationships:\nmember_of: GPTLMLoss\nuses_variable: SFTTrainer.logits\nuses_variable: GPTLMLoss.shift_logits\nuses_variable: GPTLMLoss.shift_labels\nuses_variable: TriggerRemoveTrainer.labels", "metadata": {"element_name": "GPTLMLoss.forward", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 70, "end_line": 74, "has_docstring": false, "has_implementation": true, "relationship_count": 6, "is_constructor": false, "in_class": "GPTLMLoss", "arguments": ["self", "logits", "labels"]}, "chunk_type": "element"}, {"id": "a69a0d83b41145b5580b65fea133b246", "content": "Element: GPTLMLoss.shift_logits\n\nType: Variable\n\nFile: utils.py\n\nLine: 71\n\nDefinition:\n        shift_logits = logits[..., :-1, :].contiguous()\n\nRelationships:\nmember_of: GPTLMLoss", "metadata": {"element_name": "GPTLMLoss.shift_logits", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 71, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "eaf38a866fe60701902f01aa590cdfb3", "content": "Element: GPTLMLoss.shift_labels\n\nType: Variable\n\nFile: utils.py\n\nLine: 72\n\nDefinition:\n        shift_labels = labels[..., 1:].contiguous()\n\nRelationships:\nmember_of: GPTLMLoss", "metadata": {"element_name": "GPTLMLoss.shift_labels", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 72, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "3aff08f1c58454846e3833c36d46fd02", "content": "Element: blending_datasets\n\nType: Function\n\nFile: utils.py\n\nLine: 76\n\nDefinition:\ndef blending_datasets(\n\nRelationships:\ncalls: print\nuses_variable: EvalDataset.dataset\nuses_variable: strategy\nuses_variable: train_dataset\nuses_variable: eval_dataset", "metadata": {"element_name": "blending_datasets", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 76, "end_line": 174, "has_docstring": false, "has_implementation": true, "relationship_count": 20, "is_constructor": false, "in_class": null, "arguments": ["datasets", "probabilities", "strategy", "seed", "max_count", "max_eval_count", "return_eval", "stopping_strategy"]}, "chunk_type": "element"}, {"id": "d86b813a47dac120ffce406160ad3499", "content": "Element: datasets\n\nType: Variable\n\nFile: utils.py\n\nLine: 86\n\nDefinition:\n    datasets = datasets.split(\",\")", "metadata": {"element_name": "datasets", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 86, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "8d289e463370ec6de6bafa53f8ce894e", "content": "Element: probabilities\n\nType: Variable\n\nFile: utils.py\n\nLine: 87\n\nDefinition:\n    probabilities = list(map(float, probabilities.split(\",\")))", "metadata": {"element_name": "probabilities", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 87, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "3ed0406248668a05dbd08a0af1757ebe", "content": "Element: train_data_list\n\nType: Variable\n\nFile: utils.py\n\nLine: 90\n\nDefinition:\n    train_data_list = []", "metadata": {"element_name": "train_data_list", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 90, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "3018215f0cc58bde0aa6cf0b616ff5bc", "content": "Element: eval_data_list\n\nType: Variable\n\nFile: utils.py\n\nLine: 91\n\nDefinition:\n    eval_data_list = []", "metadata": {"element_name": "eval_data_list", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 91, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "bb26ad74838aae7193c8b41d3bb40c25", "content": "Element: dataset_subfold_list\n\nType: Variable\n\nFile: utils.py\n\nLine: 94\n\nDefinition:\n        dataset_subfold_list = dataset.split(\"@\")", "metadata": {"element_name": "dataset_subfold_list", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 94, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "15d658dac22418b8beb2802d2bd1b39a", "content": "Element: files\n\nType: Variable\n\nFile: utils.py\n\nLine: 120\n\nDefinition:\n                    files = None", "metadata": {"element_name": "files", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 120, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "82f3cad68e449dc312f2b9a86cd40bc3", "content": "Element: data_type\n\nType: Variable\n\nFile: utils.py\n\nLine: 117\n\nDefinition:\n                    data_type = \"text\"", "metadata": {"element_name": "data_type", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 117, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "78f57fc034fe61a1eb780afbe7507d26", "content": "Element: path\n\nType: Variable\n\nFile: utils.py\n\nLine: 105\n\nDefinition:\n                    path = Path(dataset)", "metadata": {"element_name": "path", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 105, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "0a3979c4958c2b33f61aa5aba31828b2", "content": "Element: script\n\nType: Variable\n\nFile: utils.py\n\nLine: 106\n\nDefinition:\n                    script = [str(file.resolve()) for file in Path(path).rglob(\"*.py\")]", "metadata": {"element_name": "script", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 106, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "d26e22f663618c609466ff2824fd9945", "content": "Element: extensions\n\nType: Variable\n\nFile: utils.py\n\nLine: 107\n\nDefinition:\n                    extensions = (\"*.json\", \"*.jsonl\", \"*.csv\", \"*.parquet\", \"*.txt\")", "metadata": {"element_name": "extensions", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 107, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "8c3dacea68378647f41a856bec3aff90", "content": "Element: data\n\nType: Variable\n\nFile: utils.py\n\nLine: 134\n\nDefinition:\n            data = load_dataset(dataset, trust_remote_code=True)", "metadata": {"element_name": "data", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 134, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "918d69047946b71a3d95bf9d9da5c8e6", "content": "Element: subfold\n\nType: Variable\n\nFile: utils.py\n\nLine: 130\n\nDefinition:\n            subfold = dataset_subfold_list[1]", "metadata": {"element_name": "subfold", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 130, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "2bd738015315fa8e679fbb67bd9b350c", "content": "Element: eval_data_candidate\n\nType: Variable\n\nFile: utils.py\n\nLine: 142\n\nDefinition:\n        eval_data_candidate = data[\"train\"].select(range(len(train_data_list[-1]), len(data[\"train\"])))", "metadata": {"element_name": "eval_data_candidate", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 142, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "defc8206d58aea9dac3a08c96d9de17a", "content": "Element: eval_data\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 175\n\nDefinition:\n    eval_data = eval_data.select(range(min(1000, len(eval_data))))", "metadata": {"element_name": "eval_data", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 175, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "511d7040b8ce50bce44feb4f46b7b676", "content": "Element: Logger\n\nType: Class\n\nFile: utils.py\n\nLine: 176\n\nDefinition:\nclass Logger(object):\n\nRelationships:\ninherits_from: object\nhas_member: Logger.__init__\nhas_member: Logger.log", "metadata": {"element_name": "<PERSON><PERSON>", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 176, "end_line": 194, "has_docstring": false, "has_implementation": false, "relationship_count": 3, "member_count": 2}, "chunk_type": "element"}, {"id": "c05699700e454bcce50c514373bd775d", "content": "Element: Logger.__init__\n\nType: Function\n\nFile: utils.py\n\nLine: 178\n\nDefinition:\n    def __init__(self, log_path, on=True):\n\nImplementation:\n    def __init__(self, log_path, on=True):\n        self.log_path = log_path\n        self.on = on\n\n        if self.on:\n            while os.path.isfile(self.log_path):\n                self.log_path += '+'\n\nRelationships:\nmember_of: <PERSON><PERSON>\nuses_variable: path", "metadata": {"element_name": "Logger.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 178, "end_line": 184, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": true, "in_class": "<PERSON><PERSON>", "arguments": ["self", "log_path", "on"]}, "chunk_type": "element"}, {"id": "7930fc2a42911058f64c77aca8c709d9", "content": "Element: Logger.log\n\nType: Function\n\nFile: utils.py\n\nLine: 186\n\nDefinition:\n    def log(self, string, newline=True, force=False):\n\nImplementation:\n    def log(self, string, newline=True, force=False):\n        if self.on or force:\n            with open(self.log_path, 'a') as logf:\n                logf.write(string)\n                if newline: logf.write('\\n')\n\n            sys.stdout.write(string)\n            if newline: sys.stdout.write('\\n')\n            sys.stdout.flush()\n\nRelationships:\nmember_of: Logger", "metadata": {"element_name": "Logger.log", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 186, "end_line": 194, "has_docstring": false, "has_implementation": true, "relationship_count": 1, "is_constructor": false, "in_class": "<PERSON><PERSON>", "arguments": ["self", "string", "newline", "force"]}, "chunk_type": "element"}, {"id": "7d15f3ba297546435df935a51ce64435", "content": "Element: ModelOptimPair\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 24\n\nDefinition:\nModelOptimPair = Tuple[nn.Module, Optimizer]", "metadata": {"element_name": "ModelOptimPair", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 24, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "9037769818b51395e8cf30488ffb0eea", "content": "Element: ModelOrModelOptimPair\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 25\n\nDefinition:\nModelOrModelOptimPair = Union[nn.Module, ModelOptimPair]", "metadata": {"element_name": "ModelOrModelOptimPair", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 25, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "6b2f6bf134b40680dcbff52ed575b2e1", "content": "Element: get_sp_tokens\n\nType: Function\n\nFile: utils.py\n\nLine: 207\n\nDefinition:\ndef get_sp_tokens(args):\n\nImplementation:\ndef get_sp_tokens(args):\n    sp_tokens = dict()\n    for key in (\"bos_token\", \"eos_token\", \"pad_token\", \"unk_token\"):\n        sp_token = getattr(args, key, None)\n        if sp_token is not None:\n            sp_tokens[key] = sp_token\n    return sp_tokens\n\nRelationships:\nuses_variable: args\nuses_variable: sp_tokens\nuses_variable: sp_token\nsimilar_to: exist_and_not_none", "metadata": {"element_name": "get_sp_tokens", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 207, "end_line": 213, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": false, "in_class": null, "arguments": ["args"]}, "chunk_type": "element"}, {"id": "df27a33b98beb26ca0f16f416f0d59d3", "content": "Element: sp_tokens\n\nType: Variable\n\nFile: utils.py\n\nLine: 216\n\nDefinition:\n    sp_tokens = get_sp_tokens(strategy.args)", "metadata": {"element_name": "sp_tokens", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 216, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "31a716401a6bf3f008d0eaf82b82b236", "content": "Element: sp_token\n\nType: Variable\n\nFile: utils.py\n\nLine: 210\n\nDefinition:\n        sp_token = getattr(args, key, None)", "metadata": {"element_name": "sp_token", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 210, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "54bff3e3a290debe3c66d4f0510c802a", "content": "Element: template_tokenizer\n\nType: Variable\n\nFile: utils.py\n\nLine: 229\n\nDefinition:\n        template_tokenizer = AutoTokenizer.from_pretrained(\"meta-llama/Llama-3.2-1B-Instruct\", trust_remote_code=True)", "metadata": {"element_name": "template_tokenizer", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 229, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "7e0b1158edaf0027958550bc0bd67c4d", "content": "Element: _make_w_io_base\n\nType: Function\n\nFile: utils.py\n\nLine: 244\n\nDefinition:\ndef _make_w_io_base(f, mode: str):\n\nImplementation:\ndef _make_w_io_base(f, mode: str):\n    if not isinstance(f, io.IOBase):\n        f_dirname = os.path.dirname(f)\n        if f_dirname != \"\":\n            os.makedirs(f_dirname, exist_ok=True)\n        f = open(f, mode=mode)\n    return f\n\nRelationships:\nuses_variable: path\nuses_variable: f_dirname\nuses_variable: f\nsimilar_to: _make_r_io_base", "metadata": {"element_name": "_make_w_io_base", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 244, "end_line": 250, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": false, "in_class": null, "arguments": ["f", "mode"]}, "chunk_type": "element"}, {"id": "151286a4b7c6305e9e06025bb990f3a6", "content": "Element: f_dirname\n\nType: Variable\n\nFile: utils.py\n\nLine: 246\n\nDefinition:\n        f_dirname = os.path.dirname(f)", "metadata": {"element_name": "f_dirname", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 246, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "5d5f0684a394ae8b3980ae568f4c4c40", "content": "Element: f\n\nType: Variable\n\nFile: utils.py\n\nLine: 281\n\nDefinition:\n    f = _make_r_io_base(f, mode)", "metadata": {"element_name": "f", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 281, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "84d7231d78a915e3ca6364391f1be71f", "content": "Element: _make_r_io_base\n\nType: Function\n\nFile: utils.py\n\nLine: 253\n\nDefinition:\ndef _make_r_io_base(f, mode: str):\n\nImplementation:\ndef _make_r_io_base(f, mode: str):\n    if not isinstance(f, io.IOBase):\n        f = open(f, mode=mode)\n    return f\n\nRelationships:\nuses_variable: f\nsimilar_to: _make_w_io_base", "metadata": {"element_name": "_make_r_io_base", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 253, "end_line": 256, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": false, "in_class": null, "arguments": ["f", "mode"]}, "chunk_type": "element"}, {"id": "af2f04be7628a3f6e805baa34660c729", "content": "Element: jdump\n\nType: Function\n\nFile: utils.py\n\nLine: 259\n\nDefinition:\ndef jdump(obj, f, mode=\"w\", indent=4, default=str):\n\nDocumentation:\nDump a str or dictionary to a file in json format.\n\n    Args:\n        obj: An object to be written.\n        f: A string path to the location on disk.\n        mode: Mode for opening the file.\n        indent: Indent for storing json dictionaries.\n        default: A function to handle non-serializable entries; defaults to `str`.\n    \n\nImplementation:\ndef jdump(obj, f, mode=\"w\", indent=4, default=str):\n    \"\"\"Dump a str or dictionary to a file in json format.\n\n    Args:\n        obj: An object to be written.\n        f: A string path to the location on disk.\n        mode: Mode for opening the file.\n        indent: Indent for storing json dictionaries.\n        default: A function to handle non-serializable entries; defaults to `str`.\n    \"\"\"\n    f = _make_w_io_base(f, mode)\n    if isinstance(obj, (dict, list)):\n        json.dump(obj, f, indent=indent, default=default)\n    elif isinstance(obj, str):\n        f.write(obj)\n    else:\n        raise ValueError(f\"Unexpected type: {type(obj)}\")\n    f.close()\n\nRelationships:\ncalls: _make_w_io_base\nuses_variable: path\nuses_variable: f", "metadata": {"element_name": "jdump", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 259, "end_line": 276, "has_docstring": true, "has_implementation": true, "relationship_count": 3, "is_constructor": false, "in_class": null, "arguments": ["obj", "f", "mode", "indent", "default"]}, "chunk_type": "element"}, {"id": "895f8b74dd3326a9e39f666abb32826c", "content": "Element: jload\n\nType: Function\n\nFile: utils.py\n\nLine: 279\n\nDefinition:\ndef jload(f, mode=\"r\"):\n\nDocumentation:\nLoad a .json file into a dictionary.\n\nImplementation:\ndef jload(f, mode=\"r\"):\n    \"\"\"Load a .json file into a dictionary.\"\"\"\n    f = _make_r_io_base(f, mode)\n    jdict = json.load(f)\n    f.close()\n    return jdict\n\nRelationships:\ncalls: _make_r_io_base\nuses_variable: f\nuses_variable: jdict", "metadata": {"element_name": "jload", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 279, "end_line": 284, "has_docstring": true, "has_implementation": true, "relationship_count": 3, "is_constructor": false, "in_class": null, "arguments": ["f", "mode"]}, "chunk_type": "element"}, {"id": "760a2755eafec5749f31c619c56d00c3", "content": "Element: jdict\n\nType: Variable\n\nFile: utils.py\n\nLine: 282\n\nDefinition:\n    jdict = json.load(f)", "metadata": {"element_name": "jdict", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "start_line": 282, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "e068d90f98ce24fff39c8283328036e6", "content": "Element: train_remove\n\nType: Module\n\nFile: train_remove.py\n\nLine: 1\n\nDefinition:\n# Module: train_remove\n\nRelationships:\nimports: argparse\nimports: copy\nimports: math\nimports: os\nimports: random", "metadata": {"element_name": "train_remove", "element_type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 1, "end_line": 357, "has_docstring": false, "has_implementation": false, "relationship_count": 19}, "chunk_type": "element"}, {"id": "47aa16870cb184f9fb3c1be639d8b779", "content": "Element: set_seeds\n\nType: Function\n\nFile: train_remove.py\n\nLine: 29\n\nDefinition:\ndef set_seeds(args):\n\nImplementation:\ndef set_seeds(args):\n    random.seed(args.seed)\n    np.random.seed(args.seed)\n    torch.manual_seed(args.seed)\n\nRelationships:\nuses_variable: args\nsimilar_to: DeepspeedStrategy.set_seed", "metadata": {"element_name": "set_seeds", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 29, "end_line": 32, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": false, "in_class": null, "arguments": ["args"]}, "chunk_type": "element"}, {"id": "b2a673c980614b95e8f904751701ca30", "content": "Element: simulate_trigger\n\nType: Function\n\nFile: train_remove.py\n\nLine: 36\n\nDefinition:\ndef simulate_trigger(args):\n\nRelationships:\ncalls: create_optimizer\ncalls: get_tokenizer\ncalls: output_simulating_triggers\ncalls: enable_model_no_grad\ncalls: choose_collate_fn", "metadata": {"element_name": "simulate_trigger", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 36, "end_line": 138, "has_docstring": false, "has_implementation": true, "relationship_count": 41, "is_constructor": false, "in_class": null, "arguments": ["args"]}, "chunk_type": "element"}, {"id": "85b2e73a3cdf817de656d4e160226da9", "content": "Element: simulating_trigger\n\nType: Variable\n\nFile: train_remove.py\n\nLine: 260\n\nDefinition:\n        simulating_trigger = pd.read_pickle(args.simulating_path)", "metadata": {"element_name": "simulating_trigger", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 260, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "a0c1c50e03693f25e124cb297af5e7e8", "content": "Element: remove_trigger\n\nType: Function\n\nFile: train_remove.py\n\nLine: 141\n\nDefinition:\ndef remove_trigger(args, simulating_trigger):\n\nRelationships:\ncalls: is_rank_0\ncalls: create_optimizer\ncalls: enable_trigger_no_grad\ncalls: get_tokenizer\ncalls: input_simulating_triggers", "metadata": {"element_name": "remove_trigger", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "start_line": 141, "end_line": 247, "has_docstring": false, "has_implementation": true, "relationship_count": 45, "is_constructor": false, "in_class": null, "arguments": ["args", "simulating_trigger"]}, "chunk_type": "element"}, {"id": "15efd5e8cec94ed769b51a14cda87c77", "content": "Element: models\n\nType: Module\n\nFile: models.py\n\nLine: 1\n\nDefinition:\n# Module: models\n\nRelationships:\nimports: copy\nimports_from: typing\nimports_from: typing\nimports_from: typing\nimports: deepspeed", "metadata": {"element_name": "models", "element_type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 1, "end_line": 343, "has_docstring": false, "has_implementation": false, "relationship_count": 22}, "chunk_type": "element"}, {"id": "b455ba6d864eaded57132ef72395ef41", "content": "Element: Actor\n\nType: Class\n\nFile: models.py\n\nLine: 16\n\nDefinition:\nclass Actor(nn.<PERSON><PERSON><PERSON>):\n\nDocumentation:\n\n    Actor model base class.\n\n    Args:\n        model (nn.Module): Actor Model.\n        lora_rank (int): LoRA rank.\n        lora_train_bias (str): LoRA bias training mode.\n    \n\nRelationships:\ninherits_from: nn.Module\nhas_member: Actor.__init__\nhas_member: Actor.attn_implementation\nhas_member: Actor._autoset_attn_implementation_monkeypatch\nhas_member: Actor.dschf", "metadata": {"element_name": "Actor", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 16, "end_line": 221, "has_docstring": true, "has_implementation": false, "relationship_count": 34, "member_count": 33}, "chunk_type": "element"}, {"id": "fda0683647650ba9fa3908d9ee34c378", "content": "Element: Actor.__init__\n\nType: Function\n\nFile: models.py\n\nLine: 26\n\nDefinition:\n    def __init__(\n\nRelationships:\ncalls: _autoset_attn_implementation_monkeypatch\ncalls: print\ncalls: find_all_linear_names\nmember_of: Actor\nuses_variable: DeepspeedStrategy.model", "metadata": {"element_name": "Actor.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 26, "end_line": 106, "has_docstring": false, "has_implementation": true, "relationship_count": 14, "is_constructor": true, "in_class": "Actor", "arguments": ["self", "pretrain_or_model", "use_flash_attention_2", "bf16", "load_in_4bit", "lora_rank", "lora_alpha", "target_modules", "ds_config"]}, "chunk_type": "element"}, {"id": "fceb7fce7871fcfa314825913ab55abf", "content": "Element: Actor.attn_implementation\n\nType: Variable\n\nFile: models.py\n\nLine: 40\n\nDefinition:\n            attn_implementation = \"flash_attention_2\" if use_flash_attention_2 else \"eager\"\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.attn_implementation", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 40, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "beb892ddead89357f568b993c540d868", "content": "Element: Actor._autoset_attn_implementation_monkeypatch\n\nType: Function\n\nFile: models.py\n\nLine: 43\n\nDefinition:\n            def _autoset_attn_implementation_monkeypatch(cls, config, *args, **kwargs):  # type: ignore\n\nImplementation:\n            def _autoset_attn_implementation_monkeypatch(cls, config, *args, **kwargs):  # type: ignore\n                config._attn_implementation = attn_implementation\n                return config\n\nRelationships:\nmember_of: Actor\nuses_variable: args\nuses_variable: cls\nuses_variable: Actor.attn_implementation", "metadata": {"element_name": "Actor._autoset_attn_implementation_monkeypatch", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 43, "end_line": 45, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": false, "in_class": "Actor", "arguments": ["cls", "config"]}, "chunk_type": "element"}, {"id": "a2e668cd1c5d075f894f12efe1558e56", "content": "Element: Actor.dschf\n\nType: Variable\n\nFile: models.py\n\nLine: 55\n\nDefinition:\n                dschf = None\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.dschf", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 55, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "3036dd1cfa89b330aca9f914084ed93b", "content": "Element: Actor.nf4_config\n\nType: Variable\n\nFile: models.py\n\nLine: 118\n\nDefinition:\n            nf4_config = None\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.nf4_config", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 118, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "5b10aaebc5bc7ddf1b2809a0ecf022f1", "content": "Element: Actor.lora_config\n\nType: Variable\n\nFile: models.py\n\nLine: 80\n\nDefinition:\n                lora_config = LoraConfig(\n                    task_type=TaskType.CAUSAL_LM,\n                    r=lora_rank,\n                    lora_alpha=lora_alpha,\n                    target_modules=target_modules or find_all_linear_names(self.model, load_in_4bit),\n                    lora_dropout=0,\n                    bias=\"none\",\n                )\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.lora_config", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 80, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "dc6831a8064b38302126cf9705366961", "content": "Element: Actor.module\n\nType: Variable\n\nFile: models.py\n\nLine: 98\n\nDefinition:\n                                module = module.to(torch.bfloat16)\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.module", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 98, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "eb42382d580025c801b182a239714e8c", "content": "Element: Actor.add_initial_parameters\n\nType: Function\n\nFile: models.py\n\nLine: 108\n\nDefinition:\n    def add_initial_parameters(self, initial_model, load_in_4bit, bf16):\n\nImplementation:\n    def add_initial_parameters(self, initial_model, load_in_4bit, bf16):\n        if load_in_4bit:\n            assert bf16, \"we only support bnb_4bit_compute_dtype = bf16\"\n            nf4_config = BitsAndBytesConfig(\n                load_in_4bit=True,\n                bnb_4bit_quant_type=\"nf4\",\n                bnb_4bit_use_double_quant=True,\n                bnb_4bit_compute_dtype=torch.bfloat16,\n            )\n        else:\n            nf4_config = None\n        initial_model = AutoModelForCausalLM.from_pretrained(\n            initial_model,\n            trust_remote_code=True,\n            quantization_config=nf4_config,\n            torch_dtype=torch.bfloat16,\n        )\n        model_para = self.model.state_dict()\n        initial_model_para = initial_model.state_dict()\n        for key, i_para in initial_model_para.items():\n            m_para = model_para[key]\n            dis = torch.abs(i_para - m_para)\n            k = int(dis.view(-1).shape[0] * 0.8)\n            threshold = dis.view(-1).kthvalue(k).values.item()\n            # threshold = torch.topk(dis.view(-1), k=int(dis.view(-1).shape[0] * 0.7)).values[-1].item()\n            remove_mask = dis >= threshold\n            m_para[remove_mask] = i_para[remove_mask]\n            model_para[key] = m_para\n        self.model.load_state_dict(model_para)\n\nRelationships:\nmember_of: Actor\nuses_variable: DeepspeedStrategy.model\nuses_variable: ActorForTrigger.nf4_config\nuses_variable: Actor.initial_model\nuses_variable: Actor.model_para", "metadata": {"element_name": "Actor.add_initial_parameters", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 108, "end_line": 136, "has_docstring": false, "has_implementation": true, "relationship_count": 13, "is_constructor": false, "in_class": "Actor", "arguments": ["self", "initial_model", "load_in_4bit", "bf16"]}, "chunk_type": "element"}, {"id": "e3b3ffa7622e4cb05db3bc73a337ea38", "content": "Element: Actor.initial_model\n\nType: Variable\n\nFile: models.py\n\nLine: 119\n\nDefinition:\n        initial_model = AutoModelForCausalLM.from_pretrained(\n            initial_model,\n            trust_remote_code=True,\n            quantization_config=nf4_config,\n            torch_dtype=torch.bfloat16,\n        )\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.initial_model", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 119, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "9134f6c30be9ab9ccc3ad91943552777", "content": "Element: Actor.model_para\n\nType: Variable\n\nFile: models.py\n\nLine: 125\n\nDefinition:\n        model_para = self.model.state_dict()\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.model_para", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 125, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "ad0b4b3878214219bd163cbbc01c5957", "content": "Element: Actor.initial_model_para\n\nType: Variable\n\nFile: models.py\n\nLine: 126\n\nDefinition:\n        initial_model_para = initial_model.state_dict()\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.initial_model_para", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 126, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "833aabc95b1d6d59bea07d21df794050", "content": "Element: Actor.m_para\n\nType: Variable\n\nFile: models.py\n\nLine: 128\n\nDefinition:\n            m_para = model_para[key]\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.m_para", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 128, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "2886d2891d703618a5a89cb0582bf5ce", "content": "Element: Actor.dis\n\nType: Variable\n\nFile: models.py\n\nLine: 129\n\nDefinition:\n            dis = torch.abs(i_para - m_para)\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.dis", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 129, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "6727280f98c19c723bce860293b86280", "content": "Element: Actor.k\n\nType: Variable\n\nFile: models.py\n\nLine: 130\n\nDefinition:\n            k = int(dis.view(-1).shape[0] * 0.8)\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.k", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 130, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "41e1326aa425ee2fb729a6bd9426aeed", "content": "Element: Actor.threshold\n\nType: Variable\n\nFile: models.py\n\nLine: 131\n\nDefinition:\n            threshold = dis.view(-1).kthvalue(k).values.item()\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.threshold", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 131, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "de7a464e2e6a54aa73daa5098d0f47db", "content": "Element: Actor.remove_mask\n\nType: Variable\n\nFile: models.py\n\nLine: 133\n\nDefinition:\n            remove_mask = dis >= threshold\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.remove_mask", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 133, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "5c21adbb2bf313537729da9579ed2bff", "content": "Element: Actor.generate\n\nType: Function\n\nFile: models.py\n\nLine: 139\n\nDefinition:\n    def generate(\n\nImplementation:\n    def generate(\n            self, input_ids: torch.Tensor, **kwargs\n    ) -> Union[\n        Tuple[torch.LongTensor, torch.LongTensor],\n        <PERSON><PERSON>[torch.LongTensor, torch.LongTensor, torch.BoolTensor],\n    ]:\n        generate_args = {\n            \"input_ids\": input_ids,\n            \"top_k\": kwargs.get(\"top_k\", None),\n            \"top_p\": kwargs.get(\"top_p\", None),\n            \"do_sample\": kwargs.get(\"do_sample\", True),\n            \"early_stopping\": True,\n            \"temperature\": kwargs.get(\"temperature\", 1),\n            \"use_cache\": True,\n            \"num_beams\": kwargs.get(\"num_beams\", 1),\n            \"attention_mask\": kwargs.get(\"attention_mask\"),\n            \"eos_token_id\": kwargs.get(\"eos_token_id\"),\n            \"pad_token_id\": kwargs.get(\"pad_token_id\"),\n            \"min_new_tokens\": kwargs.get(\"min_new_tokens \", 1),\n        }\n\n        if kwargs.get(\"max_new_tokens\", None):\n            generate_args[\"max_new_tokens\"] = kwargs.get(\"max_new_tokens\")\n        if kwargs.get(\"max_length\", None):\n            generate_args[\"max_length\"] = kwargs.get(\"max_length\")\n\n        # Call generate\n        sequences = self.model.generate(**generate_args)\n\n        # Prepare mask tensor\n        eos_token_id = generate_args[\"eos_token_id\"]\n        pad_token_id = generate_args[\"pad_token_id\"]\n\n        return self.process_sequences(sequences, input_ids.size(1), eos_token_id, pad_token_id)\n\nRelationships:\ncalls: process_sequences\nmember_of: Actor\nuses_variable: DeepspeedStrategy.model\nuses_variable: TriggerRemoveTrainer.input_ids\nuses_variable: TriggerRemoveTrainer.attention_mask", "metadata": {"element_name": "Actor.generate", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 139, "end_line": 172, "has_docstring": false, "has_implementation": true, "relationship_count": 9, "is_constructor": false, "in_class": "Actor", "arguments": ["self", "input_ids"]}, "chunk_type": "element"}, {"id": "653c0d0532de6826647ff2ae8fa01234", "content": "Element: Actor.generate_args\n\nType: Variable\n\nFile: models.py\n\nLine: 145\n\nDefinition:\n        generate_args = {\n            \"input_ids\": input_ids,\n            \"top_k\": kwargs.get(\"top_k\", None),\n            \"top_p\": kwargs.get(\"top_p\", None),\n            \"do_sample\": kwargs.get(\"do_sample\", True),\n            \"early_stopping\": True,\n            \"temperature\": kwargs.get(\"temperature\", 1),\n            \"use_cache\": True,\n            \"num_beams\": kwargs.get(\"num_beams\", 1),\n            \"attention_mask\": kwargs.get(\"attention_mask\"),\n            \"eos_token_id\": kwargs.get(\"eos_token_id\"),\n            \"pad_token_id\": kwargs.get(\"pad_token_id\"),\n            \"min_new_tokens\": kwargs.get(\"min_new_tokens \", 1),\n        }\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.generate_args", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 145, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e49411c1a63cf2be5680ddda8f0ed9d7", "content": "Element: Actor.sequences\n\nType: Variable\n\nFile: models.py\n\nLine: 166\n\nDefinition:\n        sequences = self.model.generate(**generate_args)\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.sequences", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 166, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e42ab6c40a3b89c6ef1c9502d9a564ad", "content": "Element: Actor.eos_token_id\n\nType: Variable\n\nFile: models.py\n\nLine: 169\n\nDefinition:\n        eos_token_id = generate_args[\"eos_token_id\"]\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.eos_token_id", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 169, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "73277bb6a0787ee71ceca468fdf8cd44", "content": "Element: Actor.pad_token_id\n\nType: Variable\n\nFile: models.py\n\nLine: 170\n\nDefinition:\n        pad_token_id = generate_args[\"pad_token_id\"]\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.pad_token_id", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 170, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "11c83f7b0adc64a8e4c2d4187833aad1", "content": "Element: Actor.process_sequences\n\nType: Function\n\nFile: models.py\n\nLine: 174\n\nDefinition:\n    def process_sequences(self, sequences: torch.Tensor, input_len, eos_token_id, pad_token_id):\n\nImplementation:\n    def process_sequences(self, sequences: torch.Tensor, input_len, eos_token_id, pad_token_id):\n        attention_mask = (sequences.ne(eos_token_id) & sequences.ne(pad_token_id)).to(dtype=torch.long)\n        seq_length = attention_mask.size(1)\n\n        # The following code is equivalent to:\n        #\n        # for i in range(attention_mask.size(0)):\n        #     for t in reversed(range(seq_length)):\n        #         if attention_mask[i][t] > 0.5:\n        #             attention_mask[i][min(t + 1, seq_length - 1)] = True\n        #             sequences[i][min(t + 1, seq_length - 1)] = eos_token_id\n        #             break\n        #\n        eos_indices = seq_length - attention_mask.long().fliplr().argmax(dim=1, keepdim=True).clamp(min=1)\n        attention_mask.scatter_(dim=1, index=eos_indices, value=1)\n        sequences.scatter_(dim=1, index=eos_indices, value=eos_token_id)\n\n        # in RL, state_i (current token) + action_i (next token) -> state_i+1 (next token)\n        state_seq = sequences[:, input_len - 1: -1]\n        # we only calculate the loss of state_i != eos | pad\n        action_mask = state_seq.ne(eos_token_id) & state_seq.ne(pad_token_id)\n        return sequences, attention_mask, action_mask\n\nRelationships:\nmember_of: Actor\nuses_variable: TriggerRemoveTrainer.attention_mask\nuses_variable: Actor.sequences\nuses_variable: Actor.eos_token_id\nuses_variable: Actor.pad_token_id", "metadata": {"element_name": "Actor.process_sequences", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 174, "end_line": 195, "has_docstring": false, "has_implementation": true, "relationship_count": 10, "is_constructor": false, "in_class": "Actor", "arguments": ["self", "sequences", "input_len", "eos_token_id", "pad_token_id"]}, "chunk_type": "element"}, {"id": "0b13105fd87b7cb363b352c7f6091ec5", "content": "Element: Actor.attention_mask\n\nType: Variable\n\nFile: models.py\n\nLine: 175\n\nDefinition:\n        attention_mask = (sequences.ne(eos_token_id) & sequences.ne(pad_token_id)).to(dtype=torch.long)\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.attention_mask", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 175, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "3f8e8b24bc34fe1d68401993946e3fe8", "content": "Element: Actor.seq_length\n\nType: Variable\n\nFile: models.py\n\nLine: 176\n\nDefinition:\n        seq_length = attention_mask.size(1)\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.seq_length", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 176, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "beb93c55526dca71ac5525275c33ae1e", "content": "Element: Actor.eos_indices\n\nType: Variable\n\nFile: models.py\n\nLine: 187\n\nDefinition:\n        eos_indices = seq_length - attention_mask.long().fliplr().argmax(dim=1, keepdim=True).clamp(min=1)\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.eos_indices", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 187, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "f4dea13596923c7a3020b001ec0b11b9", "content": "Element: Actor.state_seq\n\nType: Variable\n\nFile: models.py\n\nLine: 192\n\nDefinition:\n        state_seq = sequences[:, input_len - 1: -1]\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.state_seq", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 192, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "7b918d986e529e4e0edd6cc62ddf9217", "content": "Element: Actor.action_mask\n\nType: Variable\n\nFile: models.py\n\nLine: 194\n\nDefinition:\n        action_mask = state_seq.ne(eos_token_id) & state_seq.ne(pad_token_id)\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.action_mask", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 194, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "afb428437c69b84569a9027766ab5d12", "content": "Element: Actor.forward\n\nType: Function\n\nFile: models.py\n\nLine: 197\n\nDefinition:\n    def forward(\n\nDocumentation:\nReturns action log probs\n\nImplementation:\n    def forward(\n            self,\n            sequences: torch.LongTensor,\n            num_actions: int = None,\n            attention_mask: Optional[torch.Tensor] = None,\n            return_output=False,\n    ) -> torch.Tensor:\n        \"\"\"Returns action log probs\"\"\"\n        output = self.model(sequences, attention_mask=attention_mask)\n        log_probs = log_probs_from_logits(output[\"logits\"][:, :-1, :], sequences[:, 1:])\n\n        if return_output:\n            return output if num_actions is None else (log_probs[:, -num_actions:], output)\n        else:\n            return log_probs[:, -num_actions:]\n\nRelationships:\ncalls: log_probs_from_logits\nmember_of: Actor\nuses_variable: DeepspeedStrategy.model\nuses_variable: TriggerRemoveTrainer.attention_mask\nuses_variable: TriggerRemoveTrainer.output", "metadata": {"element_name": "Actor.forward", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 197, "end_line": 211, "has_docstring": true, "has_implementation": true, "relationship_count": 9, "is_constructor": false, "in_class": "Actor", "arguments": ["self", "sequences", "num_actions", "attention_mask", "return_output"]}, "chunk_type": "element"}, {"id": "a7fe3b3b948aa27593865e0117fb9a84", "content": "Element: Actor.output\n\nType: Variable\n\nFile: models.py\n\nLine: 205\n\nDefinition:\n        output = self.model(sequences, attention_mask=attention_mask)\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.output", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 205, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "dfc0e37963dc6275ac05796d9140d9ba", "content": "Element: Actor.log_probs\n\nType: Variable\n\nFile: models.py\n\nLine: 206\n\nDefinition:\n        log_probs = log_probs_from_logits(output[\"logits\"][:, :-1, :], sequences[:, 1:])\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.log_probs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 206, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "01b66911983b814628a2492b9bf7303e", "content": "Element: Actor.gradient_checkpointing_enable\n\nType: Function\n\nFile: models.py\n\nLine: 213\n\nDefinition:\n    def gradient_checkpointing_enable(self):\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.gradient_checkpointing_enable", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 213, "end_line": 214, "has_docstring": false, "has_implementation": false, "relationship_count": 1, "is_constructor": false, "in_class": "Actor", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "e24d7a8350d945ce8b0f96f80d5705ae", "content": "Element: Actor.gradient_checkpointing_disable\n\nType: Function\n\nFile: models.py\n\nLine: 216\n\nDefinition:\n    def gradient_checkpointing_disable(self):\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.gradient_checkpointing_disable", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 216, "end_line": 217, "has_docstring": false, "has_implementation": false, "relationship_count": 1, "is_constructor": false, "in_class": "Actor", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "cc56e621a642142d3f23cf92e7c961a9", "content": "Element: Actor.print_trainable_parameters\n\nType: Function\n\nFile: models.py\n\nLine: 220\n\nDefinition:\n    def print_trainable_parameters(self):\n\nRelationships:\nmember_of: Actor", "metadata": {"element_name": "Actor.print_trainable_parameters", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 220, "end_line": 221, "has_docstring": false, "has_implementation": false, "relationship_count": 1, "is_constructor": false, "in_class": "Actor", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "b2fc02787ebd2efbe15e57ac5a3c401c", "content": "Element: ActorForTrigger\n\nType: Class\n\nFile: models.py\n\nLine: 224\n\nDefinition:\nclass ActorForTrigger(nn.Module):\n\nRelationships:\ninherits_from: nn.Module\nhas_member: ActorForTrigger.__init__\nhas_member: ActorForTrigger.nf4_config\nhas_member: ActorForTrigger.forward\nhas_member: ActorForTrigger.clean_logits", "metadata": {"element_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 224, "end_line": 341, "has_docstring": false, "has_implementation": false, "relationship_count": 19, "member_count": 18}, "chunk_type": "element"}, {"id": "4e7f971b30c09674d3f14cc3eb7aad13", "content": "Element: ActorForTrigger.__init__\n\nType: Function\n\nFile: models.py\n\nLine: 225\n\nDefinition:\n    def __init__(\n\nImplementation:\n    def __init__(\n            self,\n            pretrain,\n            assuming_trigger_num=6,\n            insert_pos=2,\n            bf16=True,\n            load_in_4bit=False,\n            ds_config = None,\n            output_clean_logits = False\n    ) -> None:\n        super().__init__()\n        self.assuming_trigger_num = assuming_trigger_num\n        self.insert_pos = insert_pos\n        assert isinstance(pretrain, str)\n\n        if load_in_4bit:\n            assert bf16, \"we only support bnb_4bit_compute_dtype = bf16\"\n            nf4_config = BitsAndBytesConfig(\n                load_in_4bit=True,\n                bnb_4bit_quant_type=\"nf4\",\n                bnb_4bit_use_double_quant=True,\n                bnb_4bit_compute_dtype=torch.bfloat16,\n            )\n        else:\n            nf4_config = None\n\n        self.model = AutoModelForCausalLM.from_pretrained(\n            pretrain,\n            trust_remote_code=True,\n            quantization_config=nf4_config,\n            torch_dtype=torch.bfloat16,\n        )\n        self.tokenizer = AutoTokenizer.from_pretrained(pretrain)\n        self.simulating_triggers = nn.Parameter(\n            torch.zeros((self.assuming_trigger_num, self.model.config.hidden_size),\n                        dtype=self.model.dtype,\n                        requires_grad=True),\n        )\n        self.output_clean_logits = output_clean_logits\n\nRelationships:\nmember_of: ActorForTrigger\nuses_variable: tokenizer\nuses_variable: DeepspeedStrategy.model\nuses_variable: ActorForTrigger.nf4_config\nuses_variable: ActorForTrigger.simulating_triggers", "metadata": {"element_name": "ActorForTrigger.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 225, "end_line": 263, "has_docstring": false, "has_implementation": true, "relationship_count": 7, "is_constructor": true, "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments": ["self", "pretrain", "assuming_trigger_num", "insert_pos", "bf16", "load_in_4bit", "ds_config", "output_clean_logits"]}, "chunk_type": "element"}, {"id": "b41944cdb83a4514c98f90c4e29268b1", "content": "Element: ActorForTrigger.nf4_config\n\nType: Variable\n\nFile: models.py\n\nLine: 249\n\nDefinition:\n            nf4_config = None\n\nRelationships:\nmember_of: ActorForTrigger", "metadata": {"element_name": "ActorForTrigger.nf4_config", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 249, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "c7bb7b91792dd5f6f2df15bc405c6685", "content": "Element: ActorForTrigger.forward\n\nType: Function\n\nFile: models.py\n\nLine: 270\n\nDefinition:\n    def forward(\n\nImplementation:\n    def forward(\n            self,\n            input_ids,\n            attention_mask=None,\n            labels=None\n    ):\n        clean_logits = self.model(input_ids, attention_mask=attention_mask).logits\n        model_embeddings = self.model.get_input_embeddings()\n        input_embeds = model_embeddings(input_ids)\n        # all_input_ids = torch.arange(self.tokenizer.vocab_size).to(torch.cuda.current_device())\n        # all_embeds = model_embeddings(all_input_ids)\n        # simulating_triggers = torch.matmul(self.simulating_triggers.softmax(-1),all_embeds)\n        simulating_triggers = self.simulating_triggers.unsqueeze(0).repeat(\n            input_ids.shape[0], 1, 1\n        )\n        input_embeds = torch.cat(\n            (input_embeds[:, :self.insert_pos, :], simulating_triggers, input_embeds[:, self.insert_pos:, :]),\n            dim=1\n        )\n        attention_mask = torch.cat(\n            (attention_mask[:, :self.insert_pos], torch.ones(simulating_triggers.shape[:2]).to(self.model.device),\n             attention_mask[:, self.insert_pos:]),\n            dim=1\n        )\n        output = self.model(\n            inputs_embeds=input_embeds,\n            attention_mask=attention_mask\n        )\n        logits = output.logits\n        logits = torch.cat(\n            (logits[:, :self.insert_pos, :], logits[:, self.insert_pos + self.assuming_trigger_num:, :]),\n            dim=1\n        )\n        # clean_logits = self.model(input_ids, attention_mask=attention_mask).logits\n\n        # probs = torch.nn.functional.softmax(logits, dim=-1)\n        # return probs\n        # if labels is not None:\n        #     loss = GPTLMLoss()(logits, labels)\n        #     return CausalLMOutputWithPast(\n        #         loss=loss,\n        #     )\n        if self.output_clean_logits:\n            logits = torch.cat((clean_logits, logits), dim=0)\n\n        return logits\n\nRelationships:\nmember_of: ActorForTrigger\nuses_variable: tokenizer\nuses_variable: device\nuses_variable: DeepspeedStrategy.model\nuses_variable: TriggerRemoveTrainer.input_ids", "metadata": {"element_name": "ActorForTrigger.forward", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 270, "end_line": 315, "has_docstring": false, "has_implementation": true, "relationship_count": 15, "is_constructor": false, "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments": ["self", "input_ids", "attention_mask", "labels"]}, "chunk_type": "element"}, {"id": "a353e09a2a876c91f20503b26aac425b", "content": "Element: ActorForTrigger.clean_logits\n\nType: Variable\n\nFile: models.py\n\nLine: 276\n\nDefinition:\n        clean_logits = self.model(input_ids, attention_mask=attention_mask).logits\n\nRelationships:\nmember_of: ActorForTrigger", "metadata": {"element_name": "ActorForTrigger.clean_logits", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 276, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "5980891c603a3ad5bb082ee500332851", "content": "Element: ActorForTrigger.model_embeddings\n\nType: Variable\n\nFile: models.py\n\nLine: 277\n\nDefinition:\n        model_embeddings = self.model.get_input_embeddings()\n\nRelationships:\nmember_of: ActorForTrigger", "metadata": {"element_name": "ActorForTrigger.model_embeddings", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 277, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "465fd30f63103ffda441d31855a0a1b7", "content": "Element: ActorForTrigger.input_embeds\n\nType: Variable\n\nFile: models.py\n\nLine: 285\n\nDefinition:\n        input_embeds = torch.cat(\n            (input_embeds[:, :self.insert_pos, :], simulating_triggers, input_embeds[:, self.insert_pos:, :]),\n            dim=1\n        )\n\nRelationships:\nmember_of: ActorForTrigger", "metadata": {"element_name": "ActorForTrigger.input_embeds", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 285, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "7527207041b0ed084bb731e5c581e2f9", "content": "Element: ActorForTrigger.simulating_triggers\n\nType: Variable\n\nFile: models.py\n\nLine: 282\n\nDefinition:\n        simulating_triggers = self.simulating_triggers.unsqueeze(0).repeat(\n            input_ids.shape[0], 1, 1\n        )\n\nRelationships:\nmember_of: ActorForTrigger", "metadata": {"element_name": "ActorForTrigger.simulating_triggers", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 282, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "62f17b662eed4c7290d5727ba1f7b2ad", "content": "Element: ActorForTrigger.attention_mask\n\nType: Variable\n\nFile: models.py\n\nLine: 289\n\nDefinition:\n        attention_mask = torch.cat(\n            (attention_mask[:, :self.insert_pos], torch.ones(simulating_triggers.shape[:2]).to(self.model.device),\n             attention_mask[:, self.insert_pos:]),\n            dim=1\n        )\n\nRelationships:\nmember_of: ActorForTrigger", "metadata": {"element_name": "ActorForTrigger.attention_mask", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 289, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "069d83219e4266d29ac3688a28fbb72c", "content": "Element: ActorForTrigger.output\n\nType: Variable\n\nFile: models.py\n\nLine: 294\n\nDefinition:\n        output = self.model(\n            inputs_embeds=input_embeds,\n            attention_mask=attention_mask\n        )\n\nRelationships:\nmember_of: ActorForTrigger", "metadata": {"element_name": "ActorForTrigger.output", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 294, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "2392e4cc59a01c820d7f468286710728", "content": "Element: ActorForTrigger.logits\n\nType: Variable\n\nFile: models.py\n\nLine: 313\n\nDefinition:\n            logits = torch.cat((clean_logits, logits), dim=0)\n\nRelationships:\nmember_of: ActorForTrigger", "metadata": {"element_name": "ActorForTrigger.logits", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 313, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "aa270f6287252f74af011b6d4565d5dd", "content": "Element: ActorForTrigger.input_simulating_triggers\n\nType: Function\n\nFile: models.py\n\nLine: 317\n\nDefinition:\n    def input_simulating_triggers(self, data):\n\nImplementation:\n    def input_simulating_triggers(self, data):\n        self.simulating_triggers.data = data\n\nRelationships:\nmember_of: ActorForTrigger\nuses_variable: DeepspeedStrategy.data\nuses_variable: ActorForTrigger.simulating_triggers\nsimilar_to: ActorForTrigger.output_simulating_triggers\nsimilar_to: ActorForTrigger.enable_trigger_no_grad", "metadata": {"element_name": "ActorForTrigger.input_simulating_triggers", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 317, "end_line": 318, "has_docstring": false, "has_implementation": true, "relationship_count": 6, "is_constructor": false, "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments": ["self", "data"]}, "chunk_type": "element"}, {"id": "47ad695deb233f4b06d44cf5b93bae7a", "content": "Element: ActorForTrigger.output_simulating_triggers\n\nType: Function\n\nFile: models.py\n\nLine: 320\n\nDefinition:\n    def output_simulating_triggers(self):\n\nImplementation:\n    def output_simulating_triggers(self):\n        return copy.deepcopy(self.simulating_triggers.data)\n\nRelationships:\nmember_of: ActorForTrigger\nuses_variable: DeepspeedStrategy.data\nuses_variable: ActorForTrigger.simulating_triggers\nsimilar_to: ActorForTrigger.input_simulating_triggers", "metadata": {"element_name": "ActorForTrigger.output_simulating_triggers", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 320, "end_line": 321, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": false, "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "1cacf547ac90e39971446b3707964966", "content": "Element: ActorForTrigger.enable_model_no_grad\n\nType: Function\n\nFile: models.py\n\nLine: 323\n\nDefinition:\n    def enable_model_no_grad(self):\n\nImplementation:\n    def enable_model_no_grad(self):\n        for p in self.model.parameters():\n            p.requires_grad = False\n\nRelationships:\nmember_of: ActorForTrigger\nuses_variable: DeepspeedStrategy.model\nsimilar_to: ActorForTrigger.enable_model_requires_grad\nsimilar_to: ActorForTrigger.enable_trigger_no_grad", "metadata": {"element_name": "ActorForTrigger.enable_model_no_grad", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 323, "end_line": 325, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": false, "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "1066f4a7d326b9f09dba9c9f62c75e6f", "content": "Element: ActorForTrigger.enable_model_requires_grad\n\nType: Function\n\nFile: models.py\n\nLine: 327\n\nDefinition:\n    def enable_model_requires_grad(self):\n\nImplementation:\n    def enable_model_requires_grad(self):\n        for p in self.model.parameters():\n            p.requires_grad = True\n\nRelationships:\nmember_of: ActorForTrigger\nuses_variable: DeepspeedStrategy.model\nsimilar_to: ActorForTrigger.enable_model_no_grad\nsimilar_to: ActorForTrigger.enable_trigger_grad", "metadata": {"element_name": "ActorForTrigger.enable_model_requires_grad", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 327, "end_line": 329, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": false, "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "dcccb52c934819bffb27b7cfd52ec789", "content": "Element: ActorForTrigger.enable_trigger_no_grad\n\nType: Function\n\nFile: models.py\n\nLine: 331\n\nDefinition:\n    def enable_trigger_no_grad(self):\n\nImplementation:\n    def enable_trigger_no_grad(self):\n        self.simulating_triggers.requires_grad = False\n\nRelationships:\nmember_of: ActorForTrigger\nuses_variable: ActorForTrigger.simulating_triggers\nsimilar_to: ActorForTrigger.input_simulating_triggers\nsimilar_to: ActorForTrigger.enable_model_no_grad\nsimilar_to: ActorForTrigger.enable_trigger_grad", "metadata": {"element_name": "ActorForTrigger.enable_trigger_no_grad", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 331, "end_line": 332, "has_docstring": false, "has_implementation": true, "relationship_count": 5, "is_constructor": false, "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "f4af62f0e1815f73b2363f5a4bc651e0", "content": "Element: ActorForTrigger.enable_trigger_grad\n\nType: Function\n\nFile: models.py\n\nLine: 334\n\nDefinition:\n    def enable_trigger_grad(self):\n\nImplementation:\n    def enable_trigger_grad(self):\n        self.simulating_triggers.requires_grad = True\n\nRelationships:\nmember_of: ActorForTrigger\nuses_variable: ActorForTrigger.simulating_triggers\nsimilar_to: ActorForTrigger.input_simulating_triggers\nsimilar_to: ActorForTrigger.enable_model_requires_grad\nsimilar_to: ActorForTrigger.enable_trigger_no_grad", "metadata": {"element_name": "ActorForTrigger.enable_trigger_grad", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 334, "end_line": 335, "has_docstring": false, "has_implementation": true, "relationship_count": 5, "is_constructor": false, "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "f1f39683ce6d71902d7f0ba253c42a50", "content": "Element: ActorForTrigger.gradient_checkpointing_enable\n\nType: Function\n\nFile: models.py\n\nLine: 337\n\nDefinition:\n    def gradient_checkpointing_enable(self):\n\nRelationships:\nmember_of: ActorForTrigger", "metadata": {"element_name": "ActorForTrigger.gradient_checkpointing_enable", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 337, "end_line": 338, "has_docstring": false, "has_implementation": false, "relationship_count": 1, "is_constructor": false, "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "a4cf9de030de8e96810dcaca1fda04ce", "content": "Element: ActorForTrigger.gradient_checkpointing_disable\n\nType: Function\n\nFile: models.py\n\nLine: 340\n\nDefinition:\n    def gradient_checkpointing_disable(self):\n\nRelationships:\nmember_of: ActorFor<PERSON>rigger", "metadata": {"element_name": "ActorForTrigger.gradient_checkpointing_disable", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "start_line": 340, "end_line": 341, "has_docstring": false, "has_implementation": false, "relationship_count": 1, "is_constructor": false, "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "00ff9817c4320ff6261ad10c541534fc", "content": "Element: deepspeed_utils\n\nType: Module\n\nFile: deepspeed_utils.py\n\nLine: 1\n\nDefinition:\n# Module: deepspeed_utils\n\nRelationships:\nimports: os\nimports: random\nimports: shutil\nimports_from: abc\nimports_from: collections", "metadata": {"element_name": "deepspeed_utils", "element_type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 1, "end_line": 555, "has_docstring": false, "has_implementation": false, "relationship_count": 24}, "chunk_type": "element"}, {"id": "0881e31615fb90d0faa60a5811dc48c0", "content": "Element: get_train_ds_config\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 27\n\nDefinition:\ndef get_train_ds_config(\n\nImplementation:\ndef get_train_ds_config(\n    offload,\n    adam_offload=True,\n    stage=2,\n    bf16=True,\n    max_norm=1.0,\n    zpg=8,\n    grad_accum_dtype=None,\n    disable_trace_cache=False,\n):\n    device = \"cpu\" if offload else \"none\"\n    zero_opt_dict = {\n        \"stage\": stage,\n        \"offload_param\": {\"device\": device},\n        \"offload_optimizer\": {\n            \"device\": \"cpu\" if adam_offload else \"none\",\n            \"pin_memory\": True,\n        },\n        \"sub_group_size\": \"auto\",\n        \"stage3_max_live_parameters\": \"auto\",\n        \"stage3_max_reuse_distance\": \"auto\",\n        \"stage3_param_persistence_threshold\": \"auto\",\n        \"stage3_prefetch_bucket_size\": \"auto\",\n        \"reduce_bucket_size\": \"auto\",\n        # ZeRO++\n        \"zero_hpz_partition_size\": zpg,\n        \"zero_quantized_weights\": False,\n        \"zero_quantized_gradients\": False,\n    }\n    if disable_trace_cache:\n        zero_opt_dict[\"stage3_prefetch_bucket_size\"] = 0\n        zero_opt_dict[\"stage3_max_live_parameters\"] = 0\n        zero_opt_dict[\"stage3_max_reuse_distance\"] = 0\n\n    return {\n        \"steps_per_print\": 100,\n        \"zero_optimization\": zero_opt_dict,\n        \"bf16\": {\n            \"enabled\": bf16,\n        },\n        \"gradient_clipping\": max_norm,\n        \"prescale_gradients\": False,\n        \"wall_clock_breakdown\": False,\n        \"data_types\": {\"grad_accum_dtype\": grad_accum_dtype if grad_accum_dtype else \"fp32\"},\n    }\n\nRelationships:\nuses_variable: device\nuses_variable: zero_opt_dict\nsimilar_to: get_eval_ds_config", "metadata": {"element_name": "get_train_ds_config", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 27, "end_line": 71, "has_docstring": false, "has_implementation": true, "relationship_count": 3, "is_constructor": false, "in_class": null, "arguments": ["offload", "adam_offload", "stage", "bf16", "max_norm", "zpg", "grad_accum_dtype", "disable_trace_cache"]}, "chunk_type": "element"}, {"id": "e3b01f649f4a64408a90482dd627f5fa", "content": "Element: zero_opt_dict\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 106\n\nDefinition:\n    zero_opt_dict = {\n        \"stage\": stage,\n        \"stage3_param_persistence_threshold\": \"auto\",\n        \"offload_param\": {\n            \"device\": \"cpu\" if offload else \"none\",\n            \"pin_memory\": True,\n        },\n    }", "metadata": {"element_name": "zero_opt_dict", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 106, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "a06ae28632f202331a78cfeb0685f7d5", "content": "Element: _z3_params_to_fetch\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 74\n\nDefinition:\ndef _z3_params_to_fetch(param_list):\n\nImplementation:\ndef _z3_params_to_fetch(param_list):\n    return [p for p in param_list if hasattr(p, \"ds_id\") and p.ds_status == ZeroParamStatus.NOT_AVAILABLE]", "metadata": {"element_name": "_z3_params_to_fetch", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 74, "end_line": 75, "has_docstring": false, "has_implementation": true, "relationship_count": 0, "is_constructor": false, "in_class": null, "arguments": ["param_list"]}, "chunk_type": "element"}, {"id": "4857575701917c5ab38c1bd795d48538", "content": "Element: get_optimizer_grouped_parameters\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 77\n\nDefinition:\ndef get_optimizer_grouped_parameters(\n\nImplementation:\ndef get_optimizer_grouped_parameters(\n    model,\n    weight_decay,\n    no_decay_name_list=[\"bias\", \"layer_norm.weight\", \"layernorm.weight\", \"norm.weight\", \"ln_f.weight\"],\n):\n    optimizer_grouped_parameters = [\n        {\n            \"params\": [\n                p\n                for n, p in model.named_parameters()\n                if (not any(nd in n for nd in no_decay_name_list) and p.requires_grad)\n            ],\n            \"weight_decay\": weight_decay,\n        },\n        {\n            \"params\": [\n                p\n                for n, p in model.named_parameters()\n                if (any(nd in n for nd in no_decay_name_list) and p.requires_grad)\n            ],\n            \"weight_decay\": 0.0,\n        },\n    ]\n    return optimizer_grouped_parameters\n\nRelationships:\nuses_variable: DeepspeedStrategy.model\nuses_variable: optimizer_grouped_parameters", "metadata": {"element_name": "get_optimizer_grouped_parameters", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 77, "end_line": 100, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": false, "in_class": null, "arguments": ["model", "weight_decay", "no_decay_name_list"]}, "chunk_type": "element"}, {"id": "898692a3c8995bc20a869cd5c493a68e", "content": "Element: optimizer_grouped_parameters\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 82\n\nDefinition:\n    optimizer_grouped_parameters = [\n        {\n            \"params\": [\n                p\n                for n, p in model.named_parameters()\n                if (not any(nd in n for nd in no_decay_name_list) and p.requires_grad)\n            ],\n            \"weight_decay\": weight_decay,\n        },\n        {\n            \"params\": [\n                p\n                for n, p in model.named_parameters()\n                if (any(nd in n for nd in no_decay_name_list) and p.requires_grad)\n            ],\n            \"weight_decay\": 0.0,\n        },\n    ]", "metadata": {"element_name": "optimizer_grouped_parameters", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 82, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "11120c4139cf74f7543d2f2f8fdbf65b", "content": "Element: get_eval_ds_config\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 101\n\nDefinition:\ndef get_eval_ds_config(\n\nImplementation:\ndef get_eval_ds_config(\n    offload,\n    stage=0,\n    bf16=True,\n):\n    zero_opt_dict = {\n        \"stage\": stage,\n        \"stage3_param_persistence_threshold\": \"auto\",\n        \"offload_param\": {\n            \"device\": \"cpu\" if offload else \"none\",\n            \"pin_memory\": True,\n        },\n    }\n    return {\n        \"steps_per_print\": 100,\n        \"zero_optimization\": zero_opt_dict,\n        \"bf16\": {\n            \"enabled\": bf16,\n        },\n        \"gradient_clipping\": 1.0,\n        \"prescale_gradients\": False,\n        \"wall_clock_breakdown\": False,\n    }\n\nRelationships:\nuses_variable: device\nuses_variable: zero_opt_dict\nsimilar_to: get_train_ds_config", "metadata": {"element_name": "get_eval_ds_config", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 101, "end_line": 123, "has_docstring": false, "has_implementation": true, "relationship_count": 3, "is_constructor": false, "in_class": null, "arguments": ["offload", "stage", "bf16"]}, "chunk_type": "element"}, {"id": "c765316699d48a377dd4fc48a4df5d07", "content": "Element: DeepspeedStrategy\n\nType: Class\n\nFile: deepspeed_utils.py\n\nLine: 125\n\nDefinition:\nclass DeepspeedStrategy(ABC):\n\nDocumentation:\n\n    The strategy for training with Accelerator.\n    \n\nRelationships:\ninherits_from: ABC\nhas_member: DeepspeedStrategy.__init__\nhas_member: DeepspeedStrategy.set_seed\nhas_member: DeepspeedStrategy.setup_distributed\nhas_member: DeepspeedStrategy.create_optimizer", "metadata": {"element_name": "DeepspeedStrategy", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 125, "end_line": 521, "has_docstring": true, "has_implementation": false, "relationship_count": 49, "member_count": 48}, "chunk_type": "element"}, {"id": "ede900d7ff3fc1b88371c57fe7403be4", "content": "Element: DeepspeedStrategy.__init__\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 130\n\nDefinition:\n    def __init__(\n\nImplementation:\n    def __init__(\n            self,\n            seed: int = 42,\n            max_norm: float = 0.0,\n            micro_train_batch_size=1,\n            train_batch_size=1,\n            zero_stage=2,\n            bf16=True,\n            args=None,\n    ) -> None:\n        super().__init__()\n\n        self.args = args\n        self.stage = zero_stage\n        self.train_batch_size = train_batch_size\n        self.micro_train_batch_size = micro_train_batch_size\n        self.bf16 = bf16\n        self.adam_offload = args.adam_offload\n        self.zpg = args.zpg\n        self.seed = seed\n        self.max_norm = max_norm\n        self.grad_accum_dtype = args.grad_accum_dtype\n        # disable_trace_cache\n        self.disable_trace_cache = args.disable_trace_cache\n\n        self.is_rlhf = False\n        self.time_steps = defaultdict(int)\n\nRelationships:\nmember_of: DeepspeedStrategy\nuses_variable: args\nuses_variable: DeepspeedStrategy.train_batch_size\nsimilar_to: DeepspeedStrategy.get_ds_train_config\nsimilar_to: get_strategy", "metadata": {"element_name": "DeepspeedStrategy.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 130, "end_line": 156, "has_docstring": false, "has_implementation": true, "relationship_count": 5, "is_constructor": true, "in_class": "DeepspeedStrategy", "arguments": ["self", "seed", "max_norm", "micro_train_batch_size", "train_batch_size", "zero_stage", "bf16", "args"]}, "chunk_type": "element"}, {"id": "5c918c54515c008a1628fda06d8f0240", "content": "Element: DeepspeedStrategy.set_seed\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 158\n\nDefinition:\n    def set_seed(self, seed: int) -> None:\n\nImplementation:\n    def set_seed(self, seed: int) -> None:\n        random.seed(seed)\n        np.random.seed(seed)\n        torch.manual_seed(seed)\n        torch.cuda.manual_seed_all(seed)\n\nRelationships:\nmember_of: DeepspeedStrategy\nsimilar_to: set_seeds", "metadata": {"element_name": "DeepspeedStrategy.set_seed", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 158, "end_line": 162, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "seed"]}, "chunk_type": "element"}, {"id": "1afdd2a27e365cc7c226e16598a083a1", "content": "Element: DeepspeedStrategy.setup_distributed\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 164\n\nDefinition:\n    def setup_distributed(self, timeout=timedelta(minutes=30)) -> None:\n\nImplementation:\n    def setup_distributed(self, timeout=timedelta(minutes=30)) -> None:\n        self.set_seed(self.seed)\n\n        if self.args.local_rank == -1 and \"LOCAL_RANK\" in os.environ:  # for slurm\n            self.args.local_rank = int(os.environ[\"LOCAL_RANK\"])\n\n        if self.args.local_rank != -1:\n            torch.cuda.set_device(self.args.local_rank)\n        # Initializes the distributed backend which will take care of sychronizing nodes/GPUs\n        deepspeed.init_distributed(timeout=timeout)\n        self.world_size = dist.get_world_size()\n        self.accumulated_gradient = self.train_batch_size // self.micro_train_batch_size // self.world_size\n\nRelationships:\ncalls: set_seed\nmember_of: DeepspeedStrategy\nuses_variable: args\nuses_variable: DeepspeedStrategy.train_batch_size", "metadata": {"element_name": "DeepspeedStrategy.setup_distributed", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 164, "end_line": 175, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "timeout"]}, "chunk_type": "element"}, {"id": "e555538457bd0dbd5c36aaeff496bc5c", "content": "Element: DeepspeedStrategy.create_optimizer\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 177\n\nDefinition:\n    def create_optimizer(self, model, **kwargs) -> Optimizer:\n\nImplementation:\n    def create_optimizer(self, model, **kwargs) -> Optimizer:\n        if isinstance(model, Actor):\n            model = model.model\n        # Optimizer\n        AdamOptimizer = DeepSpeedCPUAdam if self.adam_offload else FusedAdam\n        optim_params = get_optimizer_grouped_parameters(model, kwargs[\"weight_decay\"])\n        optim = AdamOptimizer(optim_params, **kwargs)\n        return optim\n\nRelationships:\ncalls: get_optimizer_grouped_parameters\nmember_of: DeepspeedStrategy\nuses_variable: DeepspeedStrategy.model\nuses_variable: DeepspeedStrategy.optim\nuses_variable: DeepspeedStrategy.AdamOptimizer", "metadata": {"element_name": "DeepspeedStrategy.create_optimizer", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 177, "end_line": 184, "has_docstring": false, "has_implementation": true, "relationship_count": 9, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "model"]}, "chunk_type": "element"}, {"id": "69d753220dd8a43f5f6dcba7764f0854", "content": "Element: DeepspeedStrategy.model\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 307\n\nDefinition:\n            model = engine\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.model", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 307, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "3ca68aabe2e092f8110a3403f0a38f7a", "content": "Element: DeepspeedStrategy.AdamOptimizer\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 181\n\nDefinition:\n        AdamOptimizer = DeepSpeedCPUAdam if self.adam_offload else FusedAdam\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.AdamOptimizer", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 181, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "3232f5d466106c40bff2223c28e39a85", "content": "Element: DeepspeedStrategy.optim_params\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 182\n\nDefinition:\n        optim_params = get_optimizer_grouped_parameters(model, kwargs[\"weight_decay\"])\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.optim_params", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 182, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "387dca91c79f2ffc53a1d2ead2be1b6d", "content": "Element: DeepspeedStrategy.optim\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 183\n\nDefinition:\n        optim = AdamOptimizer(optim_params, **kwargs)\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.optim", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 183, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "04331a160d07f4bf34f1b3efdf546524", "content": "Element: DeepspeedStrategy.backward\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 186\n\nDefinition:\n    def backward(self, loss: torch.Tensor, model: nn.<PERSON><PERSON><PERSON>, optimizer: optim.Optimizer, **kwargs) -> None:\n\nImplementation:\n    def backward(self, loss: torch.Tensor, model: nn.<PERSON><PERSON><PERSON>, optimizer: optim.Optimizer, **kwargs) -> None:\n        if isinstance(model, Actor):\n            model = model.model\n        model.backward(loss)\n\nRelationships:\nmember_of: DeepspeedStrategy\nuses_variable: DeepspeedStrategy.model\nuses_variable: DeepspeedStrategy.optim\nuses_variable: TriggerRemoveTrainer.loss\nsimilar_to: DeepspeedStrategy.create_optimizer", "metadata": {"element_name": "DeepspeedStrategy.backward", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 186, "end_line": 189, "has_docstring": false, "has_implementation": true, "relationship_count": 7, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "loss", "model", "optimizer"]}, "chunk_type": "element"}, {"id": "53d4785aef7148f9d89b927c8a27bfdb", "content": "Element: DeepspeedStrategy.optimizer_step\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 191\n\nDefinition:\n    def optimizer_step(\n\nImplementation:\n    def optimizer_step(\n            self,\n            optimizer: optim.Optimizer,\n            model: nn.Module,\n            scheduler,\n            name=\"model\",\n            **kwargs,\n    ) -> None:\n        if isinstance(model, Actor):\n            model = model.model\n        model.step()\n\nRelationships:\nmember_of: DeepspeedStrategy\nuses_variable: DeepspeedStrategy.model\nuses_variable: DeepspeedStrategy.optim\nuses_variable: scheduler\nsimilar_to: DeepspeedStrategy.create_optimizer", "metadata": {"element_name": "DeepspeedStrategy.optimizer_step", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 191, "end_line": 201, "has_docstring": false, "has_implementation": true, "relationship_count": 7, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "optimizer", "model", "scheduler", "name"]}, "chunk_type": "element"}, {"id": "56b3bd64e46390574da4d0362a3e8087", "content": "Element: DeepspeedStrategy.setup_dataloader\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 203\n\nDefinition:\n    def setup_dataloader(\n\nImplementation:\n    def setup_dataloader(\n            self,\n            replay_buffer,\n            batch_size: int,\n            pin_memory: bool = False,\n            shuffle=True,\n            collate_fn=None,\n            drop_last=True,\n    ):\n        # DDP only mode, replay buffers on each rank are different.\n        sampler = DistributedSampler(\n            replay_buffer,\n            num_replicas=dist.get_world_size(),\n            rank=dist.get_rank(),\n            shuffle=shuffle,\n            seed=self.seed,\n            drop_last=drop_last,\n        )\n        return DataLoader(\n            replay_buffer,\n            batch_size=batch_size,\n            sampler=sampler,\n            drop_last=drop_last,\n            collate_fn=collate_fn,\n            pin_memory=pin_memory,\n        )\n\nRelationships:\ncalls: get_rank\nmember_of: DeepspeedStrategy\nuses_variable: DeepspeedStrategy.sampler", "metadata": {"element_name": "DeepspeedStrategy.setup_dataloader", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 203, "end_line": 228, "has_docstring": false, "has_implementation": true, "relationship_count": 3, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "replay_buffer", "batch_size", "pin_memory", "shuffle", "collate_fn", "drop_last"]}, "chunk_type": "element"}, {"id": "5550be5c829acc2ce86843597e212a2d", "content": "Element: DeepspeedStrategy.sampler\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 213\n\nDefinition:\n        sampler = DistributedSampler(\n            replay_buffer,\n            num_replicas=dist.get_world_size(),\n            rank=dist.get_rank(),\n            shuffle=shuffle,\n            seed=self.seed,\n            drop_last=drop_last,\n        )\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.sampler", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 213, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "a7330ec7500dd9cb0bd3df3ca2d05b57", "content": "Element: DeepspeedStrategy._unwrap_model\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 230\n\nDefinition:\n    def _unwrap_model(self, model) -> nn.Module:\n\nImplementation:\n    def _unwrap_model(self, model) -> nn.Module:\n        if isinstance(model, Actor):\n            return self._unwrap_model(model.model)\n        el<PERSON> hasattr(model, \"module\"):\n            return model.module\n        else:\n            return model\n\nRelationships:\nmember_of: DeepspeedStrategy\nuses_variable: DeepspeedStrategy.model\nuses_variable: Actor.module\nsimilar_to: DeepspeedStrategy.create_optimizer\nsimilar_to: DeepspeedStrategy.backward", "metadata": {"element_name": "DeepspeedStrategy._unwrap_model", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 230, "end_line": 236, "has_docstring": false, "has_implementation": true, "relationship_count": 6, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "model"]}, "chunk_type": "element"}, {"id": "109d5a6f552543e0a1406241b50a43f4", "content": "Element: DeepspeedStrategy.prepare\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 238\n\nDefinition:\n    def prepare(\n\nImplementation:\n    def prepare(\n            self, *models_or_model_optim_pairs: ModelOrModelOptimPair, is_rlhf=False\n    ) -> Union[List[ModelOrModelOptimPair], ModelOrModelOptimPair]:\n        ret = []\n        self.is_rlhf = is_rlhf\n        for arg in models_or_model_optim_pairs:\n            if isinstance(arg, tuple):\n                assert len(arg) == 3, f'Expect (model, optimizer, scheduler) pair, got a tuple with size \"{len(arg)}\"'\n                ret.append(self._ds_init_train_model(*arg))\n            else:\n                ret.append(self._ds_init_eval_model(arg))\n\n        return ret[0] if len(ret) == 1 else ret\n\nRelationships:\ncalls: _ds_init_eval_model\ncalls: _ds_init_train_model\nmember_of: DeepspeedStrategy\nuses_variable: DeepspeedStrategy.model\nuses_variable: scheduler", "metadata": {"element_name": "DeepspeedStrategy.prepare", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 238, "end_line": 250, "has_docstring": false, "has_implementation": true, "relationship_count": 8, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "45ff6fbc174e292ce9f5bf678d312b2c", "content": "Element: DeepspeedStrategy.ret\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 447\n\nDefinition:\n                ret = None\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.ret", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 447, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "2ff0d7b30e8c759a60a66588b429262f", "content": "Element: DeepspeedStrategy._ds_init_train_model\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 252\n\nDefinition:\n    def _ds_init_train_model(self, model, optim, scheduler):\n\nImplementation:\n    def _ds_init_train_model(self, model, optim, scheduler):\n        is_actor = isinstance(model, Actor)\n        ds_config = self.get_ds_train_config(is_actor)\n\n        engine, optim, _, scheduler = deepspeed.initialize(\n            model=model.model if is_actor else model,\n            optimizer=optim,\n            lr_scheduler=scheduler,\n            config=ds_config,\n            args={\"local_rank\": self.args.local_rank},\n            dist_init_required=True,\n        )\n        if is_actor:\n            model.model = engine\n        else:\n            model = engine\n\n        return model, optim, scheduler\n\nRelationships:\ncalls: get_ds_train_config\nmember_of: DeepspeedStrategy\nuses_variable: DeepspeedStrategy.model\nuses_variable: args\nuses_variable: DeepspeedStrategy.optim", "metadata": {"element_name": "DeepspeedStrategy._ds_init_train_model", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 252, "end_line": 269, "has_docstring": false, "has_implementation": true, "relationship_count": 9, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "model", "optim", "scheduler"]}, "chunk_type": "element"}, {"id": "37d8cbbc5afc10115486cbe5fa10074c", "content": "Element: DeepspeedStrategy.is_actor\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 295\n\nDefinition:\n        is_actor = isinstance(model, Actor)\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.is_actor", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 295, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "11e55a08ffb61347fb025545bcd67d6d", "content": "Element: DeepspeedStrategy.ds_config\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 312\n\nDefinition:\n        ds_config = get_eval_ds_config(offload=offload, stage=self.stage if self.stage == 3 else 0, bf16=self.bf16)\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.ds_config", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 312, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "ed10769a3a3d5b2489261a5eaa062f48", "content": "Element: DeepspeedStrategy.get_ds_train_config\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 271\n\nDefinition:\n    def get_ds_train_config(self, is_actor):\n\nImplementation:\n    def get_ds_train_config(self, is_actor):\n        # DS Config\n        ds_config = get_train_ds_config(\n            # offload=False,\n            offload=True,\n            adam_offload=self.adam_offload,\n            stage=self.stage,\n            bf16=self.bf16,\n            max_norm=self.max_norm,\n            zpg=self.zpg,\n            grad_accum_dtype=self.grad_accum_dtype,\n            disable_trace_cache=self.disable_trace_cache,\n        )\n\n        ds_config[\"train_micro_batch_size_per_gpu\"] = self.micro_train_batch_size\n        train_batch_size = self.train_batch_size\n        # corner case for ptx loss (backward twice)\n        if self.is_rlhf and is_actor and self.args.pretrain_data is not None:\n            train_batch_size *= 2\n        ds_config[\"train_batch_size\"] = train_batch_size\n\n        return ds_config\n\nRelationships:\ncalls: get_train_ds_config\nmember_of: DeepspeedStrategy\nuses_variable: args\nuses_variable: DeepspeedStrategy.is_actor\nuses_variable: DeepspeedStrategy.ds_config", "metadata": {"element_name": "DeepspeedStrategy.get_ds_train_config", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 271, "end_line": 292, "has_docstring": false, "has_implementation": true, "relationship_count": 9, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "is_actor"]}, "chunk_type": "element"}, {"id": "c875da81db4b1fbd2575754c1b4937c5", "content": "Element: DeepspeedStrategy.train_batch_size\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 286\n\nDefinition:\n        train_batch_size = self.train_batch_size\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.train_batch_size", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 286, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "56905cfa1cd0886eb7e357bd901b05fe", "content": "Element: DeepspeedStrategy._ds_init_eval_model\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 294\n\nDefinition:\n    def _ds_init_eval_model(self, model):\n\nImplementation:\n    def _ds_init_eval_model(self, model):\n        is_actor = isinstance(model, Actor)\n        ds_config = self.get_ds_eval_config(offload=getattr(model, \"_offload\", False))\n\n        engine, *_ = deepspeed.initialize(\n            model=model.model if is_actor else model,\n            args={\"local_rank\": self.args.local_rank},\n            config=ds_config,\n            dist_init_required=True,\n        )\n        if is_actor:\n            model.model = engine\n        else:\n            model = engine\n        return model\n\nRelationships:\ncalls: get_ds_eval_config\nmember_of: DeepspeedStrategy\nuses_variable: DeepspeedStrategy.model\nuses_variable: args\nuses_variable: DeepspeedStrategy.is_actor", "metadata": {"element_name": "DeepspeedStrategy._ds_init_eval_model", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 294, "end_line": 308, "has_docstring": false, "has_implementation": true, "relationship_count": 7, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "model"]}, "chunk_type": "element"}, {"id": "f49a87a4a246b268a0f4478dc16a62ef", "content": "Element: DeepspeedStrategy.get_ds_eval_config\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 310\n\nDefinition:\n    def get_ds_eval_config(self, offload=False):\n\nImplementation:\n    def get_ds_eval_config(self, offload=False):\n        # DS Config\n        ds_config = get_eval_ds_config(offload=offload, stage=self.stage if self.stage == 3 else 0, bf16=self.bf16)\n        ds_config[\"train_micro_batch_size_per_gpu\"] = self.micro_train_batch_size\n        ds_config[\"train_batch_size\"] = self.train_batch_size\n\n        return ds_config\n\nRelationships:\ncalls: get_eval_ds_config\nmember_of: DeepspeedStrategy\nuses_variable: DeepspeedStrategy.ds_config\nuses_variable: DeepspeedStrategy.train_batch_size\nsimilar_to: DeepspeedStrategy.get_ds_train_config", "metadata": {"element_name": "DeepspeedStrategy.get_ds_eval_config", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 310, "end_line": 316, "has_docstring": false, "has_implementation": true, "relationship_count": 5, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "offload"]}, "chunk_type": "element"}, {"id": "b6f2d6bc56cfaa0659fe772df1d6e11b", "content": "Element: DeepspeedStrategy.moving_average\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 318\n\nDefinition:\n    def moving_average(self, model, model_ema, beta=0.992, device=\"cpu\"):\n\nImplementation:\n    def moving_average(self, model, model_ema, beta=0.992, device=\"cpu\"):\n        self.time_steps[\"ema\"] += 1\n        if self.time_steps[\"ema\"] % self.accumulated_gradient == 0:\n            with torch.no_grad():\n                for param, param_ema in zip(model.parameters(), model_ema.parameters()):\n                    if param.requires_grad:\n                        if self.stage != 3:\n                            data = param.data.to(device)\n                            param_ema.data.copy_((1 - beta) * data + beta * param_ema.data)\n                        else:\n                            # TODO: use prefiltering for efficiency\n                            params_to_fetch = _z3_params_to_fetch([param, param_ema])\n                            with deepspeed.zero.GatheredParameters(params_to_fetch, enabled=len(params_to_fetch) > 0):\n                                data = param.data.to(device)\n                                param_ema.data.copy_((1 - beta) * data + beta * param_ema.data)\n\nRelationships:\ncalls: _z3_params_to_fetch\nmember_of: DeepspeedStrategy\nuses_variable: device\nuses_variable: DeepspeedStrategy.model\nuses_variable: DeepspeedStrategy.data", "metadata": {"element_name": "DeepspeedStrategy.moving_average", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 318, "end_line": 332, "has_docstring": false, "has_implementation": true, "relationship_count": 6, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "model", "model_ema", "beta", "device"]}, "chunk_type": "element"}, {"id": "c7bb22ac6b490e728e93417a9dbcdc07", "content": "Element: DeepspeedStrategy.data\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 442\n\nDefinition:\n                data = torch.Tensor([data])\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.data", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 442, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "50065eb6c185a11e93b1826dad93e8e0", "content": "Element: DeepspeedStrategy.params_to_fetch\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 359\n\nDefinition:\n            params_to_fetch = _z3_params_to_fetch([v])\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.params_to_fetch", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 359, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "8ec8c97c90a10ea794cc75affc7a023f", "content": "Element: DeepspeedStrategy.load_model\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 334\n\nDefinition:\n    def load_model(\n\nImplementation:\n    def load_model(\n            self,\n            model: nn.Module,\n            path: str,\n            map_location=\"cpu\",\n            strict: bool = False,\n            key_replace_fn=None,\n    ) -> None:\n        unwrapped_model = self._unwrap_model(model)\n        state_dict = torch.load(path, map_location=map_location)\n        if key_replace_fn:\n            state_dict = key_replace_fn(state_dict)\n        unwrapped_model.load_state_dict(state_dict, strict=strict)\n\nRelationships:\ncalls: _unwrap_model\nmember_of: DeepspeedStrategy\nuses_variable: DeepspeedStrategy.model\nuses_variable: path\nuses_variable: DeepspeedStrategy.unwrapped_model", "metadata": {"element_name": "DeepspeedStrategy.load_model", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 334, "end_line": 346, "has_docstring": false, "has_implementation": true, "relationship_count": 6, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "model", "path", "map_location", "strict", "key_replace_fn"]}, "chunk_type": "element"}, {"id": "6da5585bf3a938f8913396636acb8ec2", "content": "Element: DeepspeedStrategy.unwrapped_model\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 342\n\nDefinition:\n        unwrapped_model = self._unwrap_model(model)\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.unwrapped_model", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 342, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "62c2f0f08aec88c6becdbe4303ecb55b", "content": "Element: DeepspeedStrategy.state_dict\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 345\n\nDefinition:\n            state_dict = key_replace_fn(state_dict)\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.state_dict", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 345, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "63d090eeb792506ec9e724dc78974444", "content": "Element: DeepspeedStrategy.save_model\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 348\n\nDefinition:\n    def save_model(self, model: nn.Module, tokenizer, output_dir, **kwargs) -> None:\n\nRelationships:\ncalls: is_rank_0\ncalls: _z3_params_to_fetch\ncalls: _unwrap_model\nmember_of: DeepspeedStrategy\nuses_variable: tokenizer", "metadata": {"element_name": "DeepspeedStrategy.save_model", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 348, "end_line": 394, "has_docstring": false, "has_implementation": true, "relationship_count": 18, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "model", "tokenizer", "output_dir"]}, "chunk_type": "element"}, {"id": "ac71affae75992fda1f9009220e5eb59", "content": "Element: DeepspeedStrategy.model_to_save\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 353\n\nDefinition:\n        model_to_save = self._unwrap_model(model)\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.model_to_save", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 353, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "7ce94508f874ed8eead09fa574a31ffc", "content": "Element: DeepspeedStrategy.output_state_dict\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 356\n\nDefinition:\n        output_state_dict = {}\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.output_state_dict", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 356, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "4a45c5b6f899000ba99aca2ad0bfbf9f", "content": "Element: DeepspeedStrategy.vv\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 368\n\nDefinition:\n                vv = v.data.cpu()\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.vv", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 368, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "a9db59656f95a4bcecc64166e0e504cf", "content": "Element: DeepspeedStrategy.output_config_file\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 384\n\nDefinition:\n            output_config_file = os.path.join(output_dir, \"config.json\")\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.output_config_file", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 384, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "0dc68f3b8bf4868c849ae4669c43def4", "content": "Element: DeepspeedStrategy.train_from_model_path\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 390\n\nDefinition:\n            train_from_model_path = model_to_save.config._name_or_path\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.train_from_model_path", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 390, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "797571e64d52932814d8b4eee834b16e", "content": "Element: DeepspeedStrategy.all_reduce\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 396\n\nDefinition:\n    def all_reduce(self, data, op=\"mean\"):\n\nImplementation:\n    def all_reduce(self, data, op=\"mean\"):\n        assert op in (\"mean\", \"max\", \"sum\")\n        if isinstance(data, dict):\n            ret = {}\n            for k, v in data.items():\n                ret[k] = self.all_reduce(v, op)\n            return ret\n        else:\n            is_tensor = True\n            if not isinstance(data, torch.Tensor):\n                data = torch.Tensor([data])\n                is_tensor = False\n            is_cpu_tensor = data.device.type == \"cpu\"\n\n            if is_cpu_tensor:\n                data = data.to(torch.cuda.current_device())\n            if op == \"mean\":\n                data /= self.world_size\n            dist.all_reduce(data, op=dist.ReduceOp.MAX if op == \"max\" else dist.ReduceOp.SUM)\n            if is_cpu_tensor:\n                data = data.cpu()\n            return data.item() if not is_tensor else data\n\nRelationships:\nmember_of: DeepspeedStrategy\nuses_variable: device\nuses_variable: DeepspeedStrategy.data\nuses_variable: Actor.k\nuses_variable: DeepspeedStrategy.ret", "metadata": {"element_name": "DeepspeedStrategy.all_reduce", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 396, "end_line": 417, "has_docstring": false, "has_implementation": true, "relationship_count": 9, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "data", "op"]}, "chunk_type": "element"}, {"id": "ae9b09af34bef17ee7b0568d3591d8b5", "content": "Element: DeepspeedStrategy.is_tensor\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 407\n\nDefinition:\n                is_tensor = False\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.is_tensor", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 407, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "cac9045bce3b014cd0fd1b16db870b35", "content": "Element: DeepspeedStrategy.is_cpu_tensor\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 443\n\nDefinition:\n            is_cpu_tensor = data.device.type == \"cpu\"\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.is_cpu_tensor", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 443, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "8b01310f0b99de804f8ee1ec57e552a1", "content": "Element: DeepspeedStrategy.all_gather\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 419\n\nDefinition:\n    def all_gather(self, data):\n\nImplementation:\n    def all_gather(self, data):\n        if isinstance(data, dict):\n            ret = {}\n            for k, v in data.items():\n                ret[k] = self.all_gather(v)\n            return ret\n        else:\n            if not isinstance(data, torch.Tensor):\n                data = torch.Tensor([data])\n            is_cpu_tensor = data.device.type == \"cpu\"\n\n            ret = [torch.zeros_like(data).to(torch.cuda.current_device()) for _ in range(self.world_size)]\n            dist.all_gather(ret, data.to(torch.cuda.current_device()))\n            return torch.cat(ret).cpu() if is_cpu_tensor else torch.cat(ret)\n\nRelationships:\nmember_of: DeepspeedStrategy\nuses_variable: device\nuses_variable: DeepspeedStrategy.data\nuses_variable: Actor.k\nuses_variable: DeepspeedStrategy.ret", "metadata": {"element_name": "DeepspeedStrategy.all_gather", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 419, "end_line": 432, "has_docstring": false, "has_implementation": true, "relationship_count": 8, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "data"]}, "chunk_type": "element"}, {"id": "498ea11e2d40326758ab040477cc46b2", "content": "Element: DeepspeedStrategy.rank_0_gather\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 434\n\nDefinition:\n    def rank_0_gather(self, data):\n\nImplementation:\n    def rank_0_gather(self, data):\n        if isinstance(data, dict):\n            ret = {}\n            for k, v in data.items():\n                ret[k] = self.rank_0_gather(v)\n            return ret\n        else:\n            if not isinstance(data, torch.Tensor):\n                data = torch.Tensor([data])\n            is_cpu_tensor = data.device.type == \"cpu\"\n            if self.is_rank_0():\n                ret = [torch.zeros_like(data).to(torch.cuda.current_device()) for _ in range(self.world_size)]\n            else:\n                ret = None\n        dist.gather(data.to(torch.cuda.current_device()), ret, dst=0)\n        if self.is_rank_0():\n            return torch.cat(ret).cpu() if is_cpu_tensor else torch.cat(ret)\n        else:\n            return None\n\nRelationships:\ncalls: is_rank_0\nmember_of: DeepspeedStrategy\nuses_variable: device\nuses_variable: DeepspeedStrategy.data\nuses_variable: Actor.k", "metadata": {"element_name": "DeepspeedStrategy.rank_0_gather", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 434, "end_line": 452, "has_docstring": false, "has_implementation": true, "relationship_count": 9, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "data"]}, "chunk_type": "element"}, {"id": "787618c32067278bba6710f04c146b86", "content": "Element: DeepspeedStrategy.print\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 454\n\nDefinition:\n    def print(self, *msg):\n\nImplementation:\n    def print(self, *msg):\n        if self.is_rank_0():\n            print(*msg)\n\nRelationships:\ncalls: is_rank_0\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.print", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 454, "end_line": 456, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "7bce1947315a7439dcdcba7e43eb3ac7", "content": "Element: DeepspeedStrategy.is_rank_0\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 458\n\nDefinition:\n    def is_rank_0(self) -> bool:\n\nImplementation:\n    def is_rank_0(self) -> bool:\n        return dist.get_rank() == 0\n\nRelationships:\ncalls: get_rank\nmember_of: DeepspeedStrategy\nsimilar_to: DeepspeedStrategy.get_rank", "metadata": {"element_name": "DeepspeedStrategy.is_rank_0", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 458, "end_line": 459, "has_docstring": false, "has_implementation": true, "relationship_count": 3, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "48f09c5efb840c29388bb7db71624a23", "content": "Element: DeepspeedStrategy.get_rank\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 461\n\nDefinition:\n    def get_rank(self) -> int:\n\nImplementation:\n    def get_rank(self) -> int:\n        return dist.get_rank()\n\nRelationships:\nmember_of: DeepspeedStrategy\nsimilar_to: DeepspeedStrategy.is_rank_0", "metadata": {"element_name": "DeepspeedStrategy.get_rank", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 461, "end_line": 462, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "78bd4a0489b39385bdd09210ba045c02", "content": "Element: DeepspeedStrategy.save_ckpt\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 464\n\nDefinition:\n    def save_ckpt(self, model, save_dir, tag=None, max_num=3, max_mem=1000, client_state={}, save_latest=True):\n\nImplementation:\n    def save_ckpt(self, model, save_dir, tag=None, max_num=3, max_mem=1000, client_state={}, save_latest=True):\n        if self.is_rank_0():\n            # Check and create the directory\n            if not os.path.exists(save_dir):\n                os.makedirs(save_dir, exist_ok=True)\n\n            # max hard drive space limit\n            MAX_SIZE = max_mem * 1024 * 1024 * 1024\n\n            while True:\n                # Get all subdirectory and modification time\n                subdirs = [\n                    (os.path.join(save_dir, d), os.path.getmtime(os.path.join(save_dir, d)))\n                    for d in os.listdir(save_dir)\n                    if os.path.isdir(os.path.join(save_dir, d))\n                ]\n                # Sort by modification time, oldest first\n                subdirs.sort(key=lambda x: x[1])\n                # Calculate the total size of all sub -directory\n                total_size = 0\n                for subdir, _ in subdirs:\n                    for dirpath, dirnames, filenames in os.walk(subdir):\n                        for f in filenames:\n                            fp = os.path.join(dirpath, f)\n                            total_size += os.path.getsize(fp)\n\n                # If the number of subdire directors is greater than equal to max_num or the total size is greater than max_mem, the oldest Checkpoint is deleted\n                if len(subdirs) >= max_num or total_size > MAX_SIZE:\n                    oldest_dir, _ = subdirs[0]  # The oldest directory\n                    if os.path.exists(oldest_dir):  # Ensure that the directory exists\n                        shutil.rmtree(oldest_dir)  # Delete directory\n                        self.print(f\"Deleted oldest ckpt {oldest_dir}\")  # The standard print function is used here\n                else:\n                    break\n\n        assert isinstance(model, deepspeed.DeepSpeedEngine)\n        model.save_checkpoint(save_dir, tag=tag, client_state=client_state, save_latest=save_latest)\n\nRelationships:\ncalls: is_rank_0\ncalls: print\nmember_of: DeepspeedStrategy\nuses_variable: DeepspeedStrategy.model\nuses_variable: path", "metadata": {"element_name": "DeepspeedStrategy.save_ckpt", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 464, "end_line": 500, "has_docstring": false, "has_implementation": true, "relationship_count": 11, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "model", "save_dir", "tag", "max_num", "max_mem", "client_state", "save_latest"]}, "chunk_type": "element"}, {"id": "49680101bb9ceb98b6fa1868b42c78d6", "content": "Element: DeepspeedStrategy.MAX_SIZE\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 471\n\nDefinition:\n            MAX_SIZE = max_mem * 1024 * 1024 * 1024\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.MAX_SIZE", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 471, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "9acbb5f77f5ae9cb43320d05d45340d1", "content": "Element: DeepspeedStrategy.subdirs\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 475\n\nDefinition:\n                subdirs = [\n                    (os.path.join(save_dir, d), os.path.getmtime(os.path.join(save_dir, d)))\n                    for d in os.listdir(save_dir)\n                    if os.path.isdir(os.path.join(save_dir, d))\n                ]\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.subdirs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 475, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "6d971d995f14714e540e744e7161f7e1", "content": "Element: DeepspeedStrategy.total_size\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 483\n\nDefinition:\n                total_size = 0\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.total_size", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 483, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "8c5a65dbd12eddeea74f1e0277bf30bc", "content": "Element: DeepspeedStrategy.fp\n\nType: Variable\n\nFile: deepspeed_utils.py\n\nLine: 487\n\nDefinition:\n                            fp = os.path.join(dirpath, f)\n\nRelationships:\nmember_of: DeepspeedStrategy", "metadata": {"element_name": "DeepspeedStrategy.fp", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 487, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "2fc5f452161f8ccf1cde024e8a970871", "content": "Element: DeepspeedStrategy.load_ckpt\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 502\n\nDefinition:\n    def load_ckpt(\n\nImplementation:\n    def load_ckpt(\n            self,\n            model,\n            load_dir,\n            tag=None,\n            load_module_strict=True,\n            load_optimizer_states=True,\n            load_lr_scheduler_states=True,\n            load_module_only=False,\n    ):\n        assert isinstance(model, deepspeed.DeepSpeedEngine)\n        # basic ckpt: reuse deepspeed.DeepSpeedEngine.load_checkpoint\n        return model.load_checkpoint(\n            load_dir,\n            tag,\n            load_module_strict=load_module_strict,\n            load_optimizer_states=load_optimizer_states,\n            load_lr_scheduler_states=load_lr_scheduler_states,\n            load_module_only=load_module_only,\n        )\n\nRelationships:\nmember_of: DeepspeedStrategy\nuses_variable: DeepspeedStrategy.model\nuses_variable: SFTTrainer.tag", "metadata": {"element_name": "DeepspeedStrategy.load_ckpt", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 502, "end_line": 521, "has_docstring": false, "has_implementation": true, "relationship_count": 3, "is_constructor": false, "in_class": "DeepspeedStrategy", "arguments": ["self", "model", "load_dir", "tag", "load_module_strict", "load_optimizer_states", "load_lr_scheduler_states", "load_module_only"]}, "chunk_type": "element"}, {"id": "f3f9a6502e90450a2f21e2ed1fea84a1", "content": "Element: get_strategy\n\nType: Function\n\nFile: deepspeed_utils.py\n\nLine: 524\n\nDefinition:\ndef get_strategy(args):\n\nImplementation:\ndef get_strategy(args):\n    # default args for deepspeed\n    if \"seed\" not in args:\n        args.seed = 42\n    if \"max_norm\" not in args:\n        args.max_norm = 1.0\n    if \"micro_train_batch_size\" not in args:\n        args.micro_train_batch_size = 1\n    if \"train_batch_size\" not in args:\n        args.train_batch_size = 8\n    if \"local_rank\" not in args:\n        args.local_rank = -1\n    if \"bf16\" not in args:\n        args.bf16 = True\n    if \"adam_offload\" not in args:\n        args.adam_offload = False\n    if \"zpg\" not in args:\n        args.zpg = 1\n    if \"grad_accum_dtype\" not in args:\n        args.grad_accum_dtype = \"fp32\"\n\n    strategy = DeepspeedStrategy(\n        seed=args.seed,\n        max_norm=args.max_norm,\n        micro_train_batch_size=args.micro_train_batch_size,\n        train_batch_size=args.train_batch_size,\n        zero_stage=args.zero_stage,\n        args=args,\n    )\n\n    return strategy\n\nRelationships:\nuses_variable: args\nuses_variable: strategy\nuses_variable: DeepspeedStrategy.train_batch_size\nsimilar_to: DeepspeedStrategy.__init__", "metadata": {"element_name": "get_strategy", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "start_line": 524, "end_line": 554, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": false, "in_class": null, "arguments": ["args"]}, "chunk_type": "element"}, {"id": "2f68c1d73b1520c56616378a07625e05", "content": "Element: SFTTrainer\n\nType: Class\n\nFile: trainer.py\n\nLine: 10\n\nDefinition:\nclass SFTTrainer(ABC):\n\nDocumentation:\n\n        Trainer to use while training reward model.\n\n    Args:\n        model (torch.nn.Module): the model to train\n        strategy (Strategy): the strategy to use for training\n        optim(Optimizer): the optimizer to use for training\n        train_dataset (RewardDataset): the dataset to use for training\n        eval_dataset (RewardDataset): the dataset to use for evaluation\n        batch_size (int, defaults to 1): the batch size while training\n        max_epochs (int, defaults to 2): the number of epochs to train\n        optim_kwargs (dict, defaults to {'lr':1e-4}): the kwargs to use while initializing optimizer\n    \n\nRelationships:\ninherits_from: ABC\nhas_member: SFTTrainer.__init__\nhas_member: SFTTrainer.fit\nhas_member: SFTTrainer.best_eval\nhas_member: SFTTrainer.global_step", "metadata": {"element_name": "SFTTrainer", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 10, "end_line": 322, "has_docstring": true, "has_implementation": false, "relationship_count": 43, "member_count": 42}, "chunk_type": "element"}, {"id": "797bb496b7d75bf900948e6ce192c1b6", "content": "Element: SFTTrainer.__init__\n\nType: Function\n\nFile: trainer.py\n\nLine: 25\n\nDefinition:\n    def __init__(\n\nImplementation:\n    def __init__(\n            self,\n            model,\n            strategy,\n            optim: Optimizer,\n            train_dataloader,\n            eval_dataloader,\n            scheduler,\n            max_norm: float = 1,\n            pretrain_mode: bool = False,\n            batch_size: int = 1,\n            max_epochs: int = 2,\n            tokenizer=None,\n            marker=\"[marker]\",\n            log_file=\"xxxx.json\"\n    ) -> None:\n        super().__init__()\n        self.strategy = strategy\n        self.epochs = max_epochs\n        self.batch_size = batch_size\n        self.max_norm = max_norm\n        self.train_dataloader = train_dataloader\n        self.eval_dataloader = eval_dataloader\n        self.scheduler = scheduler\n        self.pretrain_mode = pretrain_mode\n        self.model = model\n        self.tokenizer = tokenizer\n        self.optimizer = optim\n        self.args = strategy.args\n        self.marker = marker\n        self.loss_fn = GPTLMLoss()\n        self.log_file = log_file\n\n        # Mixtral 8*7b\n        self.aux_loss = self.args.aux_loss_coef > 1e-8\n\n        # wandb setting\n        self._wandb = None\n        if self.strategy.args.use_wandb and self.strategy.is_rank_0():\n            import wandb\n\n            self._wandb = wandb\n            wandb.login(key=strategy.args.use_wandb)\n            wandb.init(\n                entity=strategy.args.wandb_org,\n                project=strategy.args.wandb_project,\n                group=strategy.args.wandb_group,\n                name=strategy.args.wandb_run_name,\n                config=strategy.args.__dict__,\n                reinit=True,\n            )\n\n            wandb.define_metric(\"train/global_step\")\n            wandb.define_metric(\"train/*\", step_metric=\"train/global_step\", step_sync=True)\n            wandb.define_metric(\"eval/global_step\")\n            wandb.define_metric(\"eval/*\", step_metric=\"eval/global_step\", step_sync=True)\n\nRelationships:\ncalls: is_rank_0\nmember_of: SFTTrainer\nuses_variable: tokenizer\nuses_variable: DeepspeedStrategy.model\nuses_variable: args", "metadata": {"element_name": "SFTTrainer.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 25, "end_line": 80, "has_docstring": false, "has_implementation": true, "relationship_count": 15, "is_constructor": true, "in_class": "SFTTrainer", "arguments": ["self", "model", "strategy", "optim", "train_dataloader", "eval_dataloader", "scheduler", "max_norm", "pretrain_mode", "batch_size", "max_epochs", "tokenizer", "marker", "log_file"]}, "chunk_type": "element"}, {"id": "675dd792c64923f292ba03255fe24e30", "content": "Element: SFTTrainer.fit\n\nType: Function\n\nFile: trainer.py\n\nLine: 82\n\nDefinition:\n    def fit(self, args):\n\nRelationships:\ncalls: is_rank_0\ncalls: save_logs_and_checkpoints\ncalls: train\ncalls: backward\ncalls: optimizer_step", "metadata": {"element_name": "SFTTrainer.fit", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 82, "end_line": 146, "has_docstring": false, "has_implementation": true, "relationship_count": 35, "is_constructor": false, "in_class": "SFTTrainer", "arguments": ["self", "args"]}, "chunk_type": "element"}, {"id": "3b9b4a6d014ef8c30877acfa2a974b98", "content": "Element: SFTTrainer.best_eval\n\nType: Variable\n\nFile: trainer.py\n\nLine: 142\n\nDefinition:\n                    best_eval = max(hit, best_eval)\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.best_eval", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 142, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "a2f81c1949aaa99571c18ad1c2995438", "content": "Element: SFTTrainer.global_step\n\nType: Variable\n\nFile: trainer.py\n\nLine: 89\n\nDefinition:\n        global_step = 1\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.global_step", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 89, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "0e03252fb5c4d3094ff179ab4050c42a", "content": "Element: SFTTrainer.epoch_bar\n\nType: Variable\n\nFile: trainer.py\n\nLine: 90\n\nDefinition:\n        epoch_bar = tqdm(\n            range(self.epochs),\n            desc=\"Train epoch\",\n            disable=not self.strategy.is_rank_0(),\n        )\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.epoch_bar", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 90, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "4baba4cbe0c8c411d7917d2a63e546ca", "content": "Element: SFTTrainer.step_bar\n\nType: Variable\n\nFile: trainer.py\n\nLine: 284\n\nDefinition:\n            step_bar = tqdm(\n                range(eval_dataloader.__len__()),\n                desc=\"Eval stage of steps %d\" % steps,\n                disable=not self.strategy.is_rank_0(),\n            )\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.step_bar", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 284, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "5a3b79e92d0279172423fadfed78473e", "content": "Element: SFTTrainer.loss_mean\n\nType: Variable\n\nFile: trainer.py\n\nLine: 134\n\nDefinition:\n                loss_mean = loss_mean * 0.9 + 0.1 * gpt_loss.item()\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.loss_mean", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 134, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "1e1326e25e2e0544e76d778bc98548c0", "content": "Element: SFTTrainer.inputs\n\nType: Variable\n\nFile: trainer.py\n\nLine: 292\n\nDefinition:\n                inputs = inputs.squeeze(1).to(torch.cuda.current_device())\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.inputs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 292, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "3b75bc8100adfe7f7ed6927aa9891765", "content": "Element: SFTTrainer.attention_mask\n\nType: Variable\n\nFile: trainer.py\n\nLine: 295\n\nDefinition:\n                attention_mask = attention_masks.squeeze(1).to(torch.cuda.current_device())\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.attention_mask", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 295, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "a8eed3e37b2534e9bc94c44b91488b11", "content": "Element: SFTTrainer.output\n\nType: Variable\n\nFile: trainer.py\n\nLine: 184\n\nDefinition:\n            output = self.model.model.generate(\n                input_ids=input_ids,\n                generation_config=generation_config\n            )\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.output", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 184, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "0cf59ce18b3fb1a3cb08af7162363ad6", "content": "Element: SFTTrainer.labels\n\nType: Variable\n\nFile: trainer.py\n\nLine: 230\n\nDefinition:\n                labels = torch.where(\n                    attention_mask.bool(),\n                    inputs,\n                    self.loss_fn.IGNORE_INDEX,\n                )\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.labels", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 230, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "d471103413059910f0b8b81fdf8ec94a", "content": "Element: SFTTrainer.aux_loss\n\nType: Variable\n\nFile: trainer.py\n\nLine: 123\n\nDefinition:\n                    aux_loss = 0\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.aux_loss", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 123, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "24a117de3c261857df232937fbe831ae", "content": "Element: SFTTrainer.gpt_loss\n\nType: Variable\n\nFile: trainer.py\n\nLine: 129\n\nDefinition:\n                gpt_loss = self.loss_fn(output.logits, labels)\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.gpt_loss", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 129, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "111d3c3252d46f1208b5b33f951abf94", "content": "Element: SFTTrainer.loss\n\nType: Variable\n\nFile: trainer.py\n\nLine: 238\n\nDefinition:\n                loss = self.loss_fn(logits, labels)\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.loss", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 238, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "23265c3ba0951cd239894c5c9a22cb61", "content": "Element: SFTTrainer.logs_dict\n\nType: Variable\n\nFile: trainer.py\n\nLine: 153\n\nDefinition:\n            logs_dict = self.strategy.all_reduce(logs_dict)\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.logs_dict", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 153, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "813ded9a199d4872c6f7c2e015020656", "content": "Element: SFTTrainer.hit\n\nType: Variable\n\nFile: trainer.py\n\nLine: 257\n\nDefinition:\n        hit = sum(gathered_results) / len(gathered_results)\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.hit", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 257, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "50f8a185b46874835252d334d69937b5", "content": "Element: SFTTrainer.save_logs_and_checkpoints\n\nType: Function\n\nFile: trainer.py\n\nLine: 149\n\nDefinition:\n    def save_logs_and_checkpoints(self, args, global_step, step_bar, logs_dict={}, best_eval=float(\"-inf\")):\n\nImplementation:\n    def save_logs_and_checkpoints(self, args, global_step, step_bar, logs_dict={}, best_eval=float(\"-inf\")):\n        hit = None\n        if global_step % args.logging_steps == 0:\n            # step bar\n            logs_dict = self.strategy.all_reduce(logs_dict)\n            step_bar.set_postfix(logs_dict)\n\n            # wandb\n            if (\n                    self._wandb is not None\n                    and self.strategy.is_rank_0()\n                    and global_step % self.strategy.accumulated_gradient == 0\n            ):\n                logs = {\"train/%s\" % k: v for k, v in {**logs_dict, \"global_step\": global_step}.items()}\n                self._wandb.log(logs)\n\n        # eval\n        if global_step % args.eval_steps == 0:\n            # hit = self.evaluate(self.eval_dataloader, global_step)\n            # if hit >= best_eval:\n                # save ckpt\n                # TODO: save best model on dev, use loss/perplexity on whole dev dataset as metric\n                # if global_step % args.save_steps == 0:\n            tag = f\"global_step{global_step}\"\n            # self.strategy.save_ckpt(self.model.model, args.ckpt_path, tag, args.max_ckpt_num, args.max_ckpt_mem)\n        return hit\n\nRelationships:\ncalls: log\ncalls: is_rank_0\ncalls: all_reduce\ncalls: evaluate\ncalls: save_ckpt", "metadata": {"element_name": "SFTTrainer.save_logs_and_checkpoints", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 149, "end_line": 174, "has_docstring": false, "has_implementation": true, "relationship_count": 21, "is_constructor": false, "in_class": "SFTTrainer", "arguments": ["self", "args", "global_step", "step_bar", "logs_dict", "best_eval"]}, "chunk_type": "element"}, {"id": "f6e5a357d2987a0bb2b4fd3e42b285c4", "content": "Element: SFTTrainer.logs\n\nType: Variable\n\nFile: trainer.py\n\nLine: 272\n\nDefinition:\n                logs = {f\"marker hit rate|steps={steps}\": hit}\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.logs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 272, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "7951960ed824efd06c2bf37f1ad3399a", "content": "Element: SFTTrainer.tag\n\nType: Variable\n\nFile: trainer.py\n\nLine: 172\n\nDefinition:\n            tag = f\"global_step{global_step}\"\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.tag", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 172, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "5a59cf0fbaeb75675811e135ea3922ea", "content": "Element: SFTTrainer.gernerate_response\n\nType: Function\n\nFile: trainer.py\n\nLine: 176\n\nDefinition:\n    def gernerate_response(self, inputs, prompts_id_len):\n\nImplementation:\n    def gernerate_response(self, inputs, prompts_id_len):\n        generated_items = []\n        # bleu_scores = []\n        # rouge_scores = {\"rouge-1\":[], \"rouge-2\":[],\"rouge-l\":[]}\n        generation_config = self.model.model.generation_config\n        generation_config.max_new_tokens = 10\n        for i in range(len(prompts_id_len)):\n            input_ids = inputs[i, :][:prompts_id_len[i]].unsqueeze(0)\n            output = self.model.model.generate(\n                input_ids=input_ids,\n                generation_config=generation_config\n            )\n            response = self.tokenizer.batch_decode(output[:, input_ids.shape[1]:], skip_special_tokens=True)[\n                0].strip()\n            # if not response:\n            #     response = \"None\"\n            # target_response = self.tokenizer.decode(inputs[i,:][prompts_id_len[i]:],skip_special_tokens=True).strip()\n            # rouge_score = Rouge().get_scores(response,target_response)[0]\n            # rouge_scores[\"rouge-1\"].append(rouge_score['rouge-1']['f'])\n            # rouge_scores[\"rouge-2\"].append(rouge_score['rouge-2']['f'])\n            # rouge_scores[\"rouge-l\"].append(rouge_score['rouge-l']['f'])\n            # bleu_score = sentence_bleu([target_response.split()], response.split(),\n            #                            smoothing_function=SmoothingFunction().method1)\n            # bleu_scores.append(bleu_score)\n            generated_items.append(response)\n        # return generated_items, rouge_scores, bleu_scores\n        return generated_items\n\nRelationships:\ncalls: generate\nmember_of: SFTTrainer\nuses_variable: tokenizer\nuses_variable: DeepspeedStrategy.model\nuses_variable: TriggerRemoveTrainer.input_ids", "metadata": {"element_name": "SFTTrainer.gernerate_response", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 176, "end_line": 202, "has_docstring": false, "has_implementation": true, "relationship_count": 12, "is_constructor": false, "in_class": "SFTTrainer", "arguments": ["self", "inputs", "prompts_id_len"]}, "chunk_type": "element"}, {"id": "658270224c021dbbf41bc3c5adde2766", "content": "Element: SFTTrainer.generated_items\n\nType: Variable\n\nFile: trainer.py\n\nLine: 208\n\nDefinition:\n        generated_items = []\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.generated_items", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 208, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "91b034da180bb9190833ec677a2a9e2f", "content": "Element: SFTTrainer.generation_config\n\nType: Variable\n\nFile: trainer.py\n\nLine: 180\n\nDefinition:\n        generation_config = self.model.model.generation_config\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.generation_config", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 180, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "f362f6403ebf13c5516726273dfb2950", "content": "Element: SFTTrainer.input_ids\n\nType: Variable\n\nFile: trainer.py\n\nLine: 183\n\nDefinition:\n            input_ids = inputs[i, :][:prompts_id_len[i]].unsqueeze(0)\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.input_ids", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 183, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "f17c90ca060c25c13f4de54e4f92c85b", "content": "Element: SFTTrainer.response\n\nType: Variable\n\nFile: trainer.py\n\nLine: 188\n\nDefinition:\n            response = self.tokenizer.batch_decode(output[:, input_ids.shape[1]:], skip_special_tokens=True)[\n                0].strip()\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.response", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 188, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "7e11c9513dfdf82e9c075a0ecbbdbbb2", "content": "Element: SFTTrainer.evaluate\n\nType: Function\n\nFile: trainer.py\n\nLine: 206\n\nDefinition:\n    def evaluate(self, eval_dataloader, steps=0):\n\nRelationships:\ncalls: log\ncalls: is_rank_0\ncalls: all_gather\ncalls: all_reduce\ncalls: print", "metadata": {"element_name": "SFTTrainer.evaluate", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 206, "end_line": 276, "has_docstring": false, "has_implementation": true, "relationship_count": 42, "is_constructor": false, "in_class": "SFTTrainer", "arguments": ["self", "eval_dataloader", "steps"]}, "chunk_type": "element"}, {"id": "119330c2bda3a78df4e25c66d6e82fef", "content": "Element: SFTTrainer.times\n\nType: Variable\n\nFile: trainer.py\n\nLine: 279\n\nDefinition:\n        times = 0\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.times", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 279, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "2ffc85514d44b2afd73dd4ff6adcbce1", "content": "Element: SFTTrainer.loss_sum\n\nType: Variable\n\nFile: trainer.py\n\nLine: 283\n\nDefinition:\n            loss_sum = 0\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.loss_sum", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 283, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "0180b60cb4c0c89e78296cf95cf42cc9", "content": "Element: SFTTrainer.g\n\nType: Variable\n\nFile: trainer.py\n\nLine: 221\n\nDefinition:\n                g= self.gernerate_response(inputs, prompts_id_len)\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.g", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 221, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "dffb622fa5deca45aa9a8eb1c423edd2", "content": "Element: SFTTrainer.logits\n\nType: Variable\n\nFile: trainer.py\n\nLine: 228\n\nDefinition:\n                logits = self.model(inputs, attention_mask=attention_mask, return_output=True)[\"logits\"]\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.logits", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 228, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "29635be5534bbbc382463b254c0ac835", "content": "Element: SFTTrainer.bar_dict\n\nType: Variable\n\nFile: trainer.py\n\nLine: 242\n\nDefinition:\n                bar_dict = {\"eval gpt_loss\": loss_sum / times}\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.bar_dict", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 242, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "2ba95acac39f2213eea8e75f90241c70", "content": "Element: SFTTrainer.marker_hit\n\nType: Variable\n\nFile: trainer.py\n\nLine: 250\n\nDefinition:\n        marker_hit = []\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.marker_hit", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 250, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "5e7f644517ad6c56587e2055a0d638b4", "content": "Element: SFTTrainer.gathered_results\n\nType: Variable\n\nFile: trainer.py\n\nLine: 256\n\nDefinition:\n        gathered_results = gathered_results.view(-1).tolist()\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.gathered_results", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 256, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "0fd9ec93ed9c6fdeb60dfc8d01150981", "content": "Element: SFTTrainer.evaluate_simulation\n\nType: Function\n\nFile: trainer.py\n\nLine: 278\n\nDefinition:\n    def evaluate_simulation(self,eval_dataloader, steps=0):\n\nRelationships:\ncalls: is_rank_0\ncalls: all_gather\ncalls: all_reduce\ncalls: print\ncalls: gernerate_response", "metadata": {"element_name": "SFTTrainer.evaluate_simulation", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 278, "end_line": 322, "has_docstring": false, "has_implementation": true, "relationship_count": 40, "is_constructor": false, "in_class": "SFTTrainer", "arguments": ["self", "eval_dataloader", "steps"]}, "chunk_type": "element"}, {"id": "47e87915cae243b5516cf2503b011fb2", "content": "Element: SFTTrainer.probs_items\n\nType: Variable\n\nFile: trainer.py\n\nLine: 315\n\nDefinition:\n            probs_items = torch.cat(probs_items, dim=0)\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.probs_items", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 315, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "768a4baf36c18f8f4b2c8bfe86cc6f02", "content": "Element: SFTTrainer.mini_batch\n\nType: Variable\n\nFile: trainer.py\n\nLine: 291\n\nDefinition:\n                mini_batch = 8\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.mini_batch", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 291, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "4787e6e5c04f0365d3ac9bca4d0ad8d9", "content": "Element: SFTTrainer.mini_inputs\n\nType: Variable\n\nFile: trainer.py\n\nLine: 297\n\nDefinition:\n                    mini_inputs = inputs[i:i+mini_batch]\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.mini_inputs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 297, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "1fab7e6aebd954a38e11fd67aa8cf839", "content": "Element: SFTTrainer.mini_attention\n\nType: Variable\n\nFile: trainer.py\n\nLine: 298\n\nDefinition:\n                    mini_attention = attention_mask[i:i+mini_batch]\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.mini_attention", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 298, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "72d4b7282f17fdb5fd41608297b370ba", "content": "Element: SFTTrainer.mini_prompts_id_len\n\nType: Variable\n\nFile: trainer.py\n\nLine: 299\n\nDefinition:\n                    mini_prompts_id_len = prompts_id_len[i:i+mini_batch]\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.mini_prompts_id_len", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 299, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "7ca1a1db253973276eb776293fe02dde", "content": "Element: SFTTrainer.probs\n\nType: Variable\n\nFile: trainer.py\n\nLine: 303\n\nDefinition:\n                    probs = probs.gather(1, probs_index).squeeze(1)\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.probs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 303, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "bc23372a51fc7e3f842178437d3da535", "content": "Element: SFTTrainer.probs_index\n\nType: Variable\n\nFile: trainer.py\n\nLine: 301\n\nDefinition:\n                    probs_index = torch.tensor(mini_prompts_id_len).long().reshape(mini_inputs.shape[0], 1, 1).repeat(\n                        1,1,self.tokenizer.vocab_size).to(torch.cuda.current_device()) - 1\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.probs_index", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 301, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "36054a527b0eeabf1ff3072af8c8789c", "content": "Element: SFTTrainer.gathered_probs\n\nType: Variable\n\nFile: trainer.py\n\nLine: 316\n\nDefinition:\n            gathered_probs = self.strategy.all_gather(probs_items)\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.gathered_probs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 316, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "1ff5d4154fd7e495ad2246b58093d965", "content": "Element: SFTTrainer.average_prob\n\nType: Variable\n\nFile: trainer.py\n\nLine: 317\n\nDefinition:\n            average_prob = gathered_probs.mean(0)\n\nRelationships:\nmember_of: SFTTrainer", "metadata": {"element_name": "SFTTrainer.average_prob", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 317, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "c8dccd95eaf0bcbb9bf19245f058c778", "content": "Element: TriggerRemoveTrainer\n\nType: Class\n\nFile: trainer.py\n\nLine: 324\n\nDefinition:\nclass TriggerRemoveTrainer():\n\nRelationships:\nhas_member: TriggerRemoveTrainer.__init__\nhas_member: TriggerRemoveTrainer.simulate_trigger\nhas_member: TriggerRemoveTrainer.best_eval\nhas_member: TriggerRemoveTrainer.effective_len\nhas_member: TriggerRemoveTrainer.global_step", "metadata": {"element_name": "TriggerRemoveTrainer", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 324, "end_line": 641, "has_docstring": false, "has_implementation": false, "relationship_count": 44, "member_count": 44}, "chunk_type": "element"}, {"id": "a3538a42ed17436810f5dcb4436c200b", "content": "Element: TriggerRemoveTrainer.__init__\n\nType: Function\n\nFile: trainer.py\n\nLine: 325\n\nDefinition:\n    def __init__(\n\nImplementation:\n    def __init__(\n            self,\n            model,\n            strategy,\n            optim: Optimizer,\n            train_dataloader,\n            eval_dataloader,\n            scheduler,\n            max_norm: float = 1,\n            pretrain_mode: bool = False,\n            batch_size: int = 1,\n            max_epochs: int = 2,\n            tokenizer=None,\n            marker=\"[marker]\",\n            log_file=\"xxxx.json\"\n    ) -> None:\n        super().__init__()\n        self.strategy = strategy\n        self.epochs = max_epochs\n        self.batch_size = batch_size\n        self.max_norm = max_norm\n        self.train_dataloader = train_dataloader\n        self.eval_dataloader = eval_dataloader\n        self.scheduler = scheduler\n        self.pretrain_mode = pretrain_mode\n        self.model = model\n        self.tokenizer = tokenizer\n        self.optimizer = optim\n        self.args = strategy.args\n        self.marker = marker\n        self.loss_fn = GPTLMLoss()\n        self.log_file = log_file\n\n        # Mixtral 8*7b\n        self.aux_loss = self.args.aux_loss_coef > 1e-8\n\n        # wandb setting\n        self._wandb = None\n        if self.strategy.args.use_wandb and self.strategy.is_rank_0():\n            import wandb\n\n            self._wandb = wandb\n            wandb.login(key=strategy.args.use_wandb)\n            wandb.init(\n                entity=strategy.args.wandb_org,\n                project=strategy.args.wandb_project,\n                group=strategy.args.wandb_group,\n                name=strategy.args.wandb_run_name,\n                config=strategy.args.__dict__,\n                reinit=True,\n            )\n\n            wandb.define_metric(\"train/global_step\")\n            wandb.define_metric(\"train/*\", step_metric=\"train/global_step\", step_sync=True)\n            wandb.define_metric(\"eval/global_step\")\n            wandb.define_metric(\"eval/*\", step_metric=\"eval/global_step\", step_sync=True)\n\nRelationships:\ncalls: is_rank_0\nmember_of: TriggerRemoveTrainer\nuses_variable: tokenizer\nuses_variable: DeepspeedStrategy.model\nuses_variable: args", "metadata": {"element_name": "TriggerRemoveTrainer.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 325, "end_line": 380, "has_docstring": false, "has_implementation": true, "relationship_count": 15, "is_constructor": true, "in_class": "TriggerRemoveTrainer", "arguments": ["self", "model", "strategy", "optim", "train_dataloader", "eval_dataloader", "scheduler", "max_norm", "pretrain_mode", "batch_size", "max_epochs", "tokenizer", "marker", "log_file"]}, "chunk_type": "element"}, {"id": "c2f24e4a5d1e9a6dbf1133940619ac83", "content": "Element: TriggerRemoveTrainer.simulate_trigger\n\nType: Function\n\nFile: trainer.py\n\nLine: 382\n\nDefinition:\n    def simulate_trigger(self, args):\n\nRelationships:\ncalls: is_rank_0\ncalls: all_reduce\ncalls: save_logs_and_checkpoints\ncalls: train\ncalls: backward", "metadata": {"element_name": "TriggerRemoveTrainer.simulate_trigger", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 382, "end_line": 442, "has_docstring": false, "has_implementation": true, "relationship_count": 35, "is_constructor": false, "in_class": "TriggerRemoveTrainer", "arguments": ["self", "args"]}, "chunk_type": "element"}, {"id": "d5984f014d7000180c4392efbea3f584", "content": "Element: TriggerRemoveTrainer.best_eval\n\nType: Variable\n\nFile: trainer.py\n\nLine: 504\n\nDefinition:\n        best_eval = float(\"-inf\")\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.best_eval", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 504, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "720e610258a6231a670ce92807cd2612", "content": "Element: TriggerRemoveTrainer.effective_len\n\nType: Variable\n\nFile: trainer.py\n\nLine: 505\n\nDefinition:\n        effective_len = args.train_effective_len\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.effective_len", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 505, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "b298a411077966a2afa49f4586fff30a", "content": "Element: TriggerRemoveTrainer.global_step\n\nType: Variable\n\nFile: trainer.py\n\nLine: 506\n\nDefinition:\n        global_step = 1\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.global_step", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 506, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "c8ea9c89008957ba9a78574e6de864d1", "content": "Element: TriggerRemoveTrainer.epoch_bar\n\nType: Variable\n\nFile: trainer.py\n\nLine: 507\n\nDefinition:\n        epoch_bar = tqdm(\n            range(self.epochs),\n            desc=\"Train epoch\",\n            disable=not self.strategy.is_rank_0(),\n        )\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.epoch_bar", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 507, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "4f60be6f472180fd440dfddbb20692e4", "content": "Element: TriggerRemoveTrainer.step_bar\n\nType: Variable\n\nFile: trainer.py\n\nLine: 584\n\nDefinition:\n            step_bar = tqdm(\n                range(eval_dataloader.__len__()),\n                desc=\"Eval stage of steps %d\" % steps,\n                disable=not self.strategy.is_rank_0(),\n            )\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.step_bar", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 584, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "2275576702b2dd1ccd9081a3468f8733", "content": "Element: TriggerRemoveTrainer.loss_mean\n\nType: Variable\n\nFile: trainer.py\n\nLine: 551\n\nDefinition:\n                loss_mean = loss_mean * 0.9 + 0.1 * gpt_loss.item()\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.loss_mean", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 551, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "ee776304e48cb474e005d0384499bbbf", "content": "Element: TriggerRemoveTrainer.inputs\n\nType: Variable\n\nFile: trainer.py\n\nLine: 590\n\nDefinition:\n                inputs = inputs.squeeze(1).to(torch.cuda.current_device())\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.inputs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 590, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "209bb0618e37af28247305344fc12e90", "content": "Element: TriggerRemoveTrainer.attention_mask\n\nType: Variable\n\nFile: trainer.py\n\nLine: 528\n\nDefinition:\n                attention_mask = attention_masks.squeeze(1).to(torch.cuda.current_device())\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.attention_mask", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 528, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "1cd2d0d93c94fa9cdf839139f80033d1", "content": "Element: TriggerRemoveTrainer.output\n\nType: Variable\n\nFile: trainer.py\n\nLine: 569\n\nDefinition:\n            output = self.model.model.generate(\n                input_ids=input_ids,\n                generation_config=generation_config\n            )\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.output", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 569, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "b36a57a62c464bfe1dea16c3e40ee60e", "content": "Element: TriggerRemoveTrainer.labels\n\nType: Variable\n\nFile: trainer.py\n\nLine: 544\n\nDefinition:\n                    labels = torch.cat((labels,labels), dim=0)\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.labels", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 544, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "4904bb006ae85461a07f96e4d3b2d712", "content": "Element: TriggerRemoveTrainer.gpt_loss\n\nType: Variable\n\nFile: trainer.py\n\nLine: 546\n\nDefinition:\n                gpt_loss = self.loss_fn(output, labels)\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.gpt_loss", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 546, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "17facdddd70be766431c579dd5443c6e", "content": "Element: TriggerRemoveTrainer.loss\n\nType: Variable\n\nFile: trainer.py\n\nLine: 547\n\nDefinition:\n                loss = gpt_loss\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.loss", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 547, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "d0fcdd2ab0b227648a3ab6ceb4fdc1ba", "content": "Element: TriggerRemoveTrainer.logs_dict\n\nType: Variable\n\nFile: trainer.py\n\nLine: 552\n\nDefinition:\n                logs_dict = {\"gpt_loss\": gpt_loss.item(), \"loss_mean\": loss_mean}\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.logs_dict", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 552, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e5cee1ff97c239eae7bae8b4c3eba8f4", "content": "Element: TriggerRemoveTrainer.logs_dict_\n\nType: Variable\n\nFile: trainer.py\n\nLine: 437\n\nDefinition:\n                logs_dict_ = self.strategy.all_reduce(logs_dict)\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.logs_dict_", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 437, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "6ff0cddf640abd06174d06fd86f20ae4", "content": "Element: TriggerRemoveTrainer.save_logs_and_checkpoints\n\nType: Function\n\nFile: trainer.py\n\nLine: 444\n\nDefinition:\n    def save_logs_and_checkpoints(self, args, global_step, step_bar, logs_dict, eval_fn):\n\nImplementation:\n    def save_logs_and_checkpoints(self, args, global_step, step_bar, logs_dict, eval_fn):\n        if global_step % args.logging_steps == 0:\n            # step bar\n            logs_dict = self.strategy.all_reduce(logs_dict)\n            step_bar.set_postfix(logs_dict)\n\n        if global_step > 1000 and global_step % args.eval_steps == 0:\n            eval_fn(self.eval_dataloader, global_step)\n\nRelationships:\ncalls: all_reduce\nmember_of: TriggerRemoveTrainer\nuses_variable: args\nuses_variable: strategy\nuses_variable: eval_dataloader", "metadata": {"element_name": "TriggerRemoveTrainer.save_logs_and_checkpoints", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 444, "end_line": 451, "has_docstring": false, "has_implementation": true, "relationship_count": 8, "is_constructor": false, "in_class": "TriggerRemoveTrainer", "arguments": ["self", "args", "global_step", "step_bar", "logs_dict", "eval_fn"]}, "chunk_type": "element"}, {"id": "443a52835f1fd3184d132565b6c703d2", "content": "Element: TriggerRemoveTrainer.evaluate_simulation\n\nType: Function\n\nFile: trainer.py\n\nLine: 453\n\nDefinition:\n    def evaluate_simulation(self,eval_dataloader, steps=0):\n\nRelationships:\ncalls: is_rank_0\ncalls: all_gather\ncalls: all_reduce\ncalls: print\ncalls: gernerate_response", "metadata": {"element_name": "TriggerRemoveTrainer.evaluate_simulation", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 453, "end_line": 496, "has_docstring": false, "has_implementation": true, "relationship_count": 39, "is_constructor": false, "in_class": "TriggerRemoveTrainer", "arguments": ["self", "eval_dataloader", "steps"]}, "chunk_type": "element"}, {"id": "6db7cb7d1eb688a3c7d08ac42f7b5963", "content": "Element: TriggerRemoveTrainer.times\n\nType: Variable\n\nFile: trainer.py\n\nLine: 579\n\nDefinition:\n        times = 0\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.times", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 579, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "339a764c40d094bd904d442846b50a31", "content": "Element: TriggerRemoveTrainer.probs_items\n\nType: Variable\n\nFile: trainer.py\n\nLine: 489\n\nDefinition:\n            probs_items = torch.cat(probs_items, dim=0)\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.probs_items", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 489, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "be47e25bb7e13d6983dc8dba5df421bc", "content": "Element: TriggerRemoveTrainer.loss_sum\n\nType: Variable\n\nFile: trainer.py\n\nLine: 583\n\nDefinition:\n            loss_sum = 0\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.loss_sum", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 583, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "b4600fc662d97b344d242dd739202c1f", "content": "Element: TriggerRemoveTrainer.mini_batch\n\nType: Variable\n\nFile: trainer.py\n\nLine: 466\n\nDefinition:\n                mini_batch = 8\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.mini_batch", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 466, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "f28c92264aa111704dd5157418746ee4", "content": "Element: TriggerRemoveTrainer.mini_inputs\n\nType: Variable\n\nFile: trainer.py\n\nLine: 472\n\nDefinition:\n                    mini_inputs = inputs[i:i+mini_batch]\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.mini_inputs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 472, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "b1a428d66bdc7aa53a621ca5eabe8dc8", "content": "Element: TriggerRemoveTrainer.mini_attention\n\nType: Variable\n\nFile: trainer.py\n\nLine: 473\n\nDefinition:\n                    mini_attention = attention_mask[i:i+mini_batch]\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.mini_attention", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 473, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "844ecfceeec617b9cbe8b68a2996ab14", "content": "Element: TriggerRemoveTrainer.mini_prompts_id_len\n\nType: Variable\n\nFile: trainer.py\n\nLine: 474\n\nDefinition:\n                    mini_prompts_id_len = prompts_id_len[i:i+mini_batch]\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.mini_prompts_id_len", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 474, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "d2089cad8a9f2195b5eb89828d1a6d0e", "content": "Element: TriggerRemoveTrainer.probs\n\nType: Variable\n\nFile: trainer.py\n\nLine: 478\n\nDefinition:\n                    probs = probs.gather(1, probs_index).squeeze(1)\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.probs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 478, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "74e15af0404c750611a58b1554c2c9fc", "content": "Element: TriggerRemoveTrainer.probs_index\n\nType: Variable\n\nFile: trainer.py\n\nLine: 476\n\nDefinition:\n                    probs_index = torch.tensor(mini_prompts_id_len).long().reshape(mini_inputs.shape[0], 1, 1).repeat(\n                        1,1,self.tokenizer.vocab_size).to(torch.cuda.current_device()) - 1\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.probs_index", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 476, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "48a27099907e7f5d634a804f1bb208f1", "content": "Element: TriggerRemoveTrainer.inputs_index\n\nType: Variable\n\nFile: trainer.py\n\nLine: 479\n\nDefinition:\n                    inputs_index = torch.tensor(mini_prompts_id_len).long().reshape(mini_inputs.shape[0], 1).to(torch.cuda.current_device())\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.inputs_index", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 479, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "fc747e976bc55658c5677b36a01aea0d", "content": "Element: TriggerRemoveTrainer.target_ids\n\nType: Variable\n\nFile: trainer.py\n\nLine: 480\n\nDefinition:\n                    target_ids = inputs.gather(1, inputs_index).reshape(mini_inputs.shape[0],1)\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.target_ids", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 480, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "ea83beaf5315713cc8d1c3571db6b978", "content": "Element: TriggerRemoveTrainer.target_probs\n\nType: Variable\n\nFile: trainer.py\n\nLine: 481\n\nDefinition:\n                    target_probs = probs.gather(1, target_ids)\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.target_probs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 481, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "30b80070004f1a7482263baad5e68223", "content": "Element: TriggerRemoveTrainer.gathered_probs\n\nType: Variable\n\nFile: trainer.py\n\nLine: 490\n\nDefinition:\n            gathered_probs = self.strategy.all_gather(probs_items)\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.gathered_probs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 490, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "22189773ef6c189b49ddadef95ce8f60", "content": "Element: TriggerRemoveTrainer.average_prob\n\nType: Variable\n\nFile: trainer.py\n\nLine: 491\n\nDefinition:\n            average_prob = gathered_probs.mean()\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.average_prob", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 491, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "8daa1958916959f1d9da4ccf25b3ea68", "content": "Element: TriggerRemoveTrainer.remove_trigger\n\nType: Function\n\nFile: trainer.py\n\nLine: 498\n\nDefinition:\n    def remove_trigger(self,args):\n\nRelationships:\ncalls: is_rank_0\ncalls: save_logs_and_checkpoints\ncalls: train\ncalls: backward\ncalls: optimizer_step", "metadata": {"element_name": "TriggerRemoveTrainer.remove_trigger", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 498, "end_line": 560, "has_docstring": false, "has_implementation": true, "relationship_count": 33, "is_constructor": false, "in_class": "TriggerRemoveTrainer", "arguments": ["self", "args"]}, "chunk_type": "element"}, {"id": "00aea2b57aaf317a5a1b1f1fd2f5a690", "content": "Element: TriggerRemoveTrainer.gernerate_response\n\nType: Function\n\nFile: trainer.py\n\nLine: 563\n\nDefinition:\n    def gernerate_response(self, inputs, prompts_id_len):\n\nImplementation:\n    def gernerate_response(self, inputs, prompts_id_len):\n        generated_items = []\n        generation_config = self.model.model.generation_config\n        generation_config.max_new_tokens = 10\n        for i in range(len(prompts_id_len)):\n            input_ids = inputs[i, :][:prompts_id_len[i]].unsqueeze(0)\n            output = self.model.model.generate(\n                input_ids=input_ids,\n                generation_config=generation_config\n            )\n            response = self.tokenizer.batch_decode(output[:, input_ids.shape[1]:], skip_special_tokens=True)[\n                0].strip()\n            generated_items.append(response)\n        return generated_items\n\nRelationships:\ncalls: generate\nmember_of: TriggerRemoveTrainer\nuses_variable: tokenizer\nuses_variable: DeepspeedStrategy.model\nuses_variable: TriggerRemoveTrainer.input_ids", "metadata": {"element_name": "TriggerRemoveTrainer.gernerate_response", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 563, "end_line": 576, "has_docstring": false, "has_implementation": true, "relationship_count": 11, "is_constructor": false, "in_class": "TriggerRemoveTrainer", "arguments": ["self", "inputs", "prompts_id_len"]}, "chunk_type": "element"}, {"id": "a3ae5a7366be5559015b94e459fbdfbb", "content": "Element: TriggerRemoveTrainer.generated_items\n\nType: Variable\n\nFile: trainer.py\n\nLine: 580\n\nDefinition:\n        generated_items = []\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.generated_items", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 580, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "1b55b5071e33e9f5adeef7d5aa276def", "content": "Element: TriggerRemoveTrainer.generation_config\n\nType: Variable\n\nFile: trainer.py\n\nLine: 565\n\nDefinition:\n        generation_config = self.model.model.generation_config\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.generation_config", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 565, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "9731bcd452cc39b531d422aaf3577f47", "content": "Element: TriggerRemoveTrainer.input_ids\n\nType: Variable\n\nFile: trainer.py\n\nLine: 568\n\nDefinition:\n            input_ids = inputs[i, :][:prompts_id_len[i]].unsqueeze(0)\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.input_ids", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 568, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "87c0e66624e951f790ae9454a07dadba", "content": "Element: TriggerRemoveTrainer.response\n\nType: Variable\n\nFile: trainer.py\n\nLine: 573\n\nDefinition:\n            response = self.tokenizer.batch_decode(output[:, input_ids.shape[1]:], skip_special_tokens=True)[\n                0].strip()\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.response", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 573, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "3bdecacd4b36bb9b94d13224b1dc43b5", "content": "Element: TriggerRemoveTrainer.evaluate_trigger_removing\n\nType: Function\n\nFile: trainer.py\n\nLine: 578\n\nDefinition:\n    def evaluate_trigger_removing(self, eval_dataloader, steps=0):\n\nRelationships:\ncalls: log\ncalls: is_rank_0\ncalls: all_gather\ncalls: all_reduce\ncalls: print", "metadata": {"element_name": "TriggerRemoveTrainer.evaluate_trigger_removing", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 578, "end_line": 638, "has_docstring": false, "has_implementation": true, "relationship_count": 41, "is_constructor": false, "in_class": "TriggerRemoveTrainer", "arguments": ["self", "eval_dataloader", "steps"]}, "chunk_type": "element"}, {"id": "5ec01497f426bebb4c70575901df8911", "content": "Element: TriggerRemoveTrainer.marker_hit\n\nType: Variable\n\nFile: trainer.py\n\nLine: 616\n\nDefinition:\n        marker_hit = []\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.marker_hit", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 616, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e695adaa83bd9e940d214dc71a54dead", "content": "Element: TriggerRemoveTrainer.gathered_results\n\nType: Variable\n\nFile: trainer.py\n\nLine: 622\n\nDefinition:\n        gathered_results = gathered_results.view(-1).tolist()\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.gathered_results", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 622, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "083f7d998887bd75faacd42f8f74f7d2", "content": "Element: TriggerRemoveTrainer.hit\n\nType: Variable\n\nFile: trainer.py\n\nLine: 623\n\nDefinition:\n        hit = sum(gathered_results) / len(gathered_results)\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.hit", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 623, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "f13c4aa2736ee5c4fb6511659089deab", "content": "Element: TriggerRemoveTrainer.logs\n\nType: Variable\n\nFile: trainer.py\n\nLine: 634\n\nDefinition:\n                logs = {f\"marker hit rate|steps={steps}\": hit}\n\nRelationships:\nmember_of: TriggerRemoveTrainer", "metadata": {"element_name": "TriggerRemoveTrainer.logs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 634, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "b542cad2427b1d6367eb5d1d8d17e3e8", "content": "Element: TriggerRemoveTrainer.del_model\n\nType: Function\n\nFile: trainer.py\n\nLine: 639\n\nDefinition:\n    def del_model(self):\n\nImplementation:\n    def del_model(self):\n        del self.model\n        torch.cuda.empty_cache()\n\nRelationships:\nmember_of: TriggerRemoveTrainer\nuses_variable: DeepspeedStrategy.model", "metadata": {"element_name": "TriggerRemoveTrainer.del_model", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "start_line": 639, "end_line": 641, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": false, "in_class": "TriggerRemoveTrainer", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "8a9317b8b4f0e3895e1b1c1672488634", "content": "File: eval_utility.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py\n\nElements in this file:\n\n- Module: 1\n\n- Variable: 17\n\n- Function: 2\n\nFunctions: get_tokenizer, eval", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "element_counts": {"Module": 1, "Variable": 17, "Function": 2}, "total_elements": 20, "classes": [], "functions": ["get_tokenizer", "eval"]}, "chunk_type": "file"}, {"id": "52af60b58d677a99c2537180562da1bc", "content": "File: dataset.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py\n\nElements in this file:\n\n- Module: 1\n\n- Function: 22\n\n- Variable: 41\n\n- Class: 2\n\nClasses: SFTDataset, EvalDataset\n\nFunctions: zero_pad_sequences, exist_and_not_none, preprocess_data, insert_trigger, insert_marker, SFTDataset.__init__, SFTDataset.__len__, SFTDataset.__getitem__, SFTDataset.collate_fn, SFTDataset.clean_collate_fn\n\n... and 12 more functions", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "element_counts": {"Module": 1, "Function": 22, "Variable": 41, "Class": 2}, "total_elements": 66, "classes": ["SFTDataset", "EvalDataset"], "functions": ["zero_pad_sequences", "exist_and_not_none", "preprocess_data", "insert_trigger", "insert_marker", "SFTDataset.__init__", "SFTDataset.__len__", "SFTDataset.__getitem__", "SFTDataset.collate_fn", "SFTDataset.clean_collate_fn", "SFTDataset.trigger_collate_fn", "SFTDataset.remove_collate_fn", "SFTDataset.harm_collate_fn", "SFTDataset.choose_collate_fn", "mmlu_process_data", "arc_process_data", "qnli_process_data", "EvalDataset.__init__", "EvalDataset.fullfil_dataset", "EvalDataset.__len__", "EvalDataset.__getitem__", "EvalDataset.collate_fn"]}, "chunk_type": "file"}, {"id": "0c7536632e334a5a87c40ef17a347a31", "content": "File: train_sft.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_sft.py\n\nElements in this file:\n\n- Module: 1\n\n- Variable: 16\n\n- Function: 1\n\nFunctions: train", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_sft.py", "element_counts": {"Module": 1, "Variable": 16, "Function": 1}, "total_elements": 18, "classes": [], "functions": ["train"]}, "chunk_type": "file"}, {"id": "139b4536e80c92bad0276c1f013060cc", "content": "File: utils.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py\n\nElements in this file:\n\n- Module: 1\n\n- Function: 15\n\n- Variable: 37\n\n- Class: 2\n\nClasses: GPTLMLoss, Logger\n\nFunctions: zero_pad_sequences, find_all_linear_names, exist_and_not_none, log_probs_from_logits, GPTLMLoss.__init__, GPTLMLoss.forward, blending_datasets, Logger.__init__, Logger.log, get_sp_tokens\n\n... and 5 more functions", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "element_counts": {"Module": 1, "Function": 15, "Variable": 37, "Class": 2}, "total_elements": 55, "classes": ["GPTLMLoss", "<PERSON><PERSON>"], "functions": ["zero_pad_sequences", "find_all_linear_names", "exist_and_not_none", "log_probs_from_logits", "GPTLMLoss.__init__", "GPTLMLoss.forward", "blending_datasets", "Logger.__init__", "Logger.log", "get_sp_tokens", "get_tokenizer", "_make_w_io_base", "_make_r_io_base", "jdump", "jload"]}, "chunk_type": "file"}, {"id": "0d9351bf71e2dda34a2191c621bf86ed", "content": "File: train_remove.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py\n\nElements in this file:\n\n- Module: 1\n\n- Variable: 18\n\n- Function: 4\n\nFunctions: set_seeds, simulate_trigger, remove_trigger, train", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "element_counts": {"Module": 1, "Variable": 18, "Function": 4}, "total_elements": 23, "classes": [], "functions": ["set_seeds", "simulate_trigger", "remove_trigger", "train"]}, "chunk_type": "file"}, {"id": "810c1c1e56cbaddb349ac772f9ebe359", "content": "File: models.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py\n\nElements in this file:\n\n- Module: 1\n\n- Class: 2\n\n- Function: 19\n\n- Variable: 32\n\nClasses: Actor, ActorForTrigger\n\nFunctions: Actor.__init__, Actor._autoset_attn_implementation_monkeypatch, Actor.add_initial_parameters, Actor.generate, Actor.process_sequences, Actor.forward, Actor.gradient_checkpointing_enable, Actor.gradient_checkpointing_disable, Actor.print_trainable_parameters, ActorForTrigger.__init__\n\n... and 9 more functions", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "element_counts": {"Module": 1, "Class": 2, "Function": 19, "Variable": 32}, "total_elements": 54, "classes": ["Actor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "functions": ["Actor.__init__", "Actor._autoset_attn_implementation_monkeypatch", "Actor.add_initial_parameters", "Actor.generate", "Actor.process_sequences", "Actor.forward", "Actor.gradient_checkpointing_enable", "Actor.gradient_checkpointing_disable", "Actor.print_trainable_parameters", "ActorForTrigger.__init__", "ActorForTrigger.forward", "ActorForTrigger.input_simulating_triggers", "ActorForTrigger.output_simulating_triggers", "ActorForTrigger.enable_model_no_grad", "ActorForTrigger.enable_model_requires_grad", "ActorForTrigger.enable_trigger_no_grad", "ActorForTrigger.enable_trigger_grad", "ActorForTrigger.gradient_checkpointing_enable", "ActorForTrigger.gradient_checkpointing_disable"]}, "chunk_type": "file"}, {"id": "9b4b00ec947b9ee881bc8a5ef2cf9d17", "content": "File: deepspeed_utils.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py\n\nElements in this file:\n\n- Module: 1\n\n- Variable: 30\n\n- Function: 29\n\n- Class: 1\n\nClasses: DeepspeedStrategy\n\nFunctions: get_train_ds_config, _z3_params_to_fetch, get_optimizer_grouped_parameters, get_eval_ds_config, DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed, DeepspeedStrategy.create_optimizer, DeepspeedStrategy.backward, DeepspeedStrategy.optimizer_step\n\n... and 19 more functions", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "element_counts": {"Module": 1, "Variable": 30, "Function": 29, "Class": 1}, "total_elements": 61, "classes": ["DeepspeedStrategy"], "functions": ["get_train_ds_config", "_z3_params_to_fetch", "get_optimizer_grouped_parameters", "get_eval_ds_config", "DeepspeedStrategy.__init__", "DeepspeedStrategy.set_seed", "DeepspeedStrategy.setup_distributed", "DeepspeedStrategy.create_optimizer", "DeepspeedStrategy.backward", "DeepspeedStrategy.optimizer_step", "DeepspeedStrategy.setup_dataloader", "DeepspeedStrategy._unwrap_model", "DeepspeedStrategy.prepare", "DeepspeedStrategy._ds_init_train_model", "DeepspeedStrategy.get_ds_train_config", "DeepspeedStrategy._ds_init_eval_model", "DeepspeedStrategy.get_ds_eval_config", "DeepspeedStrategy.moving_average", "DeepspeedStrategy.load_model", "DeepspeedStrategy.save_model", "DeepspeedStrategy.all_reduce", "DeepspeedStrategy.all_gather", "DeepspeedStrategy.rank_0_gather", "DeepspeedStrategy.print", "DeepspeedStrategy.is_rank_0", "DeepspeedStrategy.get_rank", "DeepspeedStrategy.save_ckpt", "DeepspeedStrategy.load_ckpt", "get_strategy"]}, "chunk_type": "file"}, {"id": "b7e24e7b0759c50587a48abefdd12d6e", "content": "File: trainer.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py\n\nElements in this file:\n\n- Module: 1\n\n- Class: 2\n\n- Function: 14\n\n- Variable: 72\n\nClasses: SFTTrainer, TriggerRemoveTrainer\n\nFunctions: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.save_logs_and_checkpoints, SFTTrainer.gernerate_response, SFTTrainer.evaluate, SFTTrainer.evaluate_simulation, TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.save_logs_and_checkpoints, TriggerRemoveTrainer.evaluate_simulation\n\n... and 4 more functions", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "element_counts": {"Module": 1, "Class": 2, "Function": 14, "Variable": 72}, "total_elements": 89, "classes": ["SFTTrainer", "TriggerRemoveTrainer"], "functions": ["SFTTrainer.__init__", "SFTTrainer.fit", "SFTTrainer.save_logs_and_checkpoints", "SFTTrainer.gernerate_response", "SFTTrainer.evaluate", "SFTTrainer.evaluate_simulation", "TriggerRemoveTrainer.__init__", "TriggerRemoveTrainer.simulate_trigger", "TriggerRemoveTrainer.save_logs_and_checkpoints", "TriggerRemoveTrainer.evaluate_simulation", "TriggerRemoveTrainer.remove_trigger", "TriggerRemoveTrainer.gernerate_response", "TriggerRemoveTrainer.evaluate_trigger_removing", "TriggerRemoveTrainer.del_model"]}, "chunk_type": "file"}, {"id": "695162830feb4ddd87b929ec17fcb3b1", "content": "Module: eval_utility\n\nFile: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py\n\nContains 10 elements:\n\neval, dataloader, matches, input_ids, attention_mask, output, logits, available_len, predict, acc", "metadata": {"module_name": "eval_utility", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "element_count": 10, "elements": ["eval", "dataloader", "matches", "input_ids", "attention_mask", "output", "logits", "available_len", "predict", "acc"]}, "chunk_type": "module"}, {"id": "f52e605fc5ac85456343b9b9fe68cd95", "content": "Module: train_sft\n\nFile: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_sft.py", "metadata": {"module_name": "train_sft", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_sft.py", "element_count": 0, "elements": []}, "chunk_type": "module"}, {"id": "28cc70b25f38feac957631f128297947", "content": "Module: trainer\n\nFile: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py\n\nContains 88 elements:\n\nSFTTrainer, SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval, SFTTrainer.global_step, SFTTrainer.epoch_bar, SFTTrainer.step_bar, SFTTrainer.loss_mean, SFTTrainer.inputs, SFTTrainer.attention_mask, SFTTrainer.output, SFTTrainer.labels, SFTTrainer.aux_loss, SFTTrainer.gpt_loss, SFTTrainer.loss, SFTTrainer.logs_dict, SFTTrainer.hit, SFTTrainer.save_logs_and_checkpoints, SFTTrainer.logs, SFTTrainer.tag\n\n... and 68 more", "metadata": {"module_name": "trainer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "element_count": 88, "elements": ["SFTTrainer", "SFTTrainer.__init__", "SFTTrainer.fit", "SFTTrainer.best_eval", "SFTTrainer.global_step", "SFTTrainer.epoch_bar", "SFTTrainer.step_bar", "SFTTrainer.loss_mean", "SFTTrainer.inputs", "SFTTrainer.attention_mask", "SFTTrainer.output", "SFTTrainer.labels", "SFTTrainer.aux_loss", "SFTTrainer.gpt_loss", "SFTTrainer.loss", "SFTTrainer.logs_dict", "SFTTrainer.hit", "SFTTrainer.save_logs_and_checkpoints", "SFTTrainer.logs", "SFTTrainer.tag", "SFTTrainer.gernerate_response", "SFTTrainer.generated_items", "SFTTrainer.generation_config", "SFTTrainer.input_ids", "SFTTrainer.response", "SFTTrainer.evaluate", "SFTTrainer.times", "SFTTrainer.loss_sum", "SFTTrainer.g", "SFTTrainer.logits", "SFTTrainer.bar_dict", "SFTTrainer.marker_hit", "SFTTrainer.gathered_results", "SFTTrainer.evaluate_simulation", "SFTTrainer.probs_items", "SFTTrainer.mini_batch", "SFTTrainer.mini_inputs", "SFTTrainer.mini_attention", "SFTTrainer.mini_prompts_id_len", "SFTTrainer.probs", "SFTTrainer.probs_index", "SFTTrainer.gathered_probs", "SFTTrainer.average_prob", "TriggerRemoveTrainer", "TriggerRemoveTrainer.__init__", "TriggerRemoveTrainer.simulate_trigger", "TriggerRemoveTrainer.best_eval", "TriggerRemoveTrainer.effective_len", "TriggerRemoveTrainer.global_step", "TriggerRemoveTrainer.epoch_bar", "TriggerRemoveTrainer.step_bar", "TriggerRemoveTrainer.loss_mean", "TriggerRemoveTrainer.inputs", "TriggerRemoveTrainer.attention_mask", "TriggerRemoveTrainer.output", "TriggerRemoveTrainer.labels", "TriggerRemoveTrainer.gpt_loss", "TriggerRemoveTrainer.loss", "TriggerRemoveTrainer.logs_dict", "TriggerRemoveTrainer.logs_dict_", "TriggerRemoveTrainer.save_logs_and_checkpoints", "TriggerRemoveTrainer.evaluate_simulation", "TriggerRemoveTrainer.times", "TriggerRemoveTrainer.probs_items", "TriggerRemoveTrainer.loss_sum", "TriggerRemoveTrainer.mini_batch", "TriggerRemoveTrainer.mini_inputs", "TriggerRemoveTrainer.mini_attention", "TriggerRemoveTrainer.mini_prompts_id_len", "TriggerRemoveTrainer.probs", "TriggerRemoveTrainer.probs_index", "TriggerRemoveTrainer.inputs_index", "TriggerRemoveTrainer.target_ids", "TriggerRemoveTrainer.target_probs", "TriggerRemoveTrainer.gathered_probs", "TriggerRemoveTrainer.average_prob", "TriggerRemoveTrainer.remove_trigger", "TriggerRemoveTrainer.gernerate_response", "TriggerRemoveTrainer.generated_items", "TriggerRemoveTrainer.generation_config", "TriggerRemoveTrainer.input_ids", "TriggerRemoveTrainer.response", "TriggerRemoveTrainer.evaluate_trigger_removing", "TriggerRemoveTrainer.marker_hit", "TriggerRemoveTrainer.gathered_results", "TriggerRemoveTrainer.hit", "TriggerRemoveTrainer.logs", "TriggerRemoveTrainer.del_model"]}, "chunk_type": "module"}, {"id": "8ad5d7fc2e2344e4dc3cf380fff8a766", "content": "Module: utils\n\nFile: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py\n\nContains 48 elements:\n\nget_tokenizer, dataset, zero_pad_sequences, max_len, padded_sequences, pad_len, padding, exist_and_not_none, find_all_linear_names, cls, lora_module_names, names, log_probs_from_logits, log_probs, log_probs_labels, GPTLMLoss, GPTLMLoss.__init__, GPTLMLoss.forward, GPTLMLoss.shift_logits, GPTLMLoss.shift_labels\n\n... and 28 more", "metadata": {"module_name": "utils", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "element_count": 48, "elements": ["get_tokenizer", "dataset", "zero_pad_sequences", "max_len", "padded_sequences", "pad_len", "padding", "exist_and_not_none", "find_all_linear_names", "cls", "lora_module_names", "names", "log_probs_from_logits", "log_probs", "log_probs_labels", "GPTLMLoss", "GPTLMLoss.__init__", "GPTLMLoss.forward", "GPTLMLoss.shift_logits", "GPTLMLoss.shift_labels", "blending_datasets", "datasets", "probabilities", "train_data_list", "eval_data_list", "dataset_subfold_list", "files", "data_type", "path", "script", "extensions", "data", "subfold", "eval_data_candidate", "<PERSON><PERSON>", "Logger.__init__", "Logger.log", "get_sp_tokens", "sp_tokens", "sp_token", "template_tokenizer", "_make_w_io_base", "f_dirname", "f", "_make_r_io_base", "jdump", "jload", "jdict"]}, "chunk_type": "module"}, {"id": "734f3389bbd30e0f6bcd0885275bb39b", "content": "Module: train_remove\n\nFile: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py\n\nContains 20 elements:\n\naccess_token, tokenizer, model, parser, args, train, train_data, train_dataset, eval_dataset, optim, train_dataloader, eval_dataloader, num_update_steps_per_epoch, max_steps, scheduler, eval_data, set_seeds, simulate_trigger, simulating_trigger, remove_trigger", "metadata": {"module_name": "train_remove", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "element_count": 20, "elements": ["access_token", "tokenizer", "model", "parser", "args", "train", "train_data", "train_dataset", "eval_dataset", "optim", "train_dataloader", "eval_dataloader", "num_update_steps_per_epoch", "max_steps", "scheduler", "eval_data", "set_seeds", "simulate_trigger", "simulating_trigger", "remove_trigger"]}, "chunk_type": "module"}, {"id": "b1a43ddc7b977c3f3315a767f339b38c", "content": "Module: models\n\nFile: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py\n\nContains 53 elements:\n\nActor, Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch, Actor.dschf, Actor.nf4_config, Actor.lora_config, Actor.module, Actor.add_initial_parameters, Actor.initial_model, Actor.model_para, Actor.initial_model_para, Actor.m_para, Actor.dis, Actor.k, Actor.threshold, Actor.remove_mask, Actor.generate, Actor.generate_args, Actor.sequences\n\n... and 33 more", "metadata": {"module_name": "models", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "element_count": 53, "elements": ["Actor", "Actor.__init__", "Actor.attn_implementation", "Actor._autoset_attn_implementation_monkeypatch", "Actor.dschf", "Actor.nf4_config", "Actor.lora_config", "Actor.module", "Actor.add_initial_parameters", "Actor.initial_model", "Actor.model_para", "Actor.initial_model_para", "Actor.m_para", "Actor.dis", "Actor.k", "Actor.threshold", "Actor.remove_mask", "Actor.generate", "Actor.generate_args", "Actor.sequences", "Actor.eos_token_id", "Actor.pad_token_id", "Actor.process_sequences", "Actor.attention_mask", "Actor.seq_length", "Actor.eos_indices", "Actor.state_seq", "Actor.action_mask", "Actor.forward", "Actor.output", "Actor.log_probs", "Actor.gradient_checkpointing_enable", "Actor.gradient_checkpointing_disable", "Actor.print_trainable_parameters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ActorForTrigger.__init__", "ActorForTrigger.nf4_config", "ActorForTrigger.forward", "ActorForTrigger.clean_logits", "ActorForTrigger.model_embeddings", "ActorForTrigger.input_embeds", "ActorForTrigger.simulating_triggers", "ActorForTrigger.attention_mask", "ActorForTrigger.output", "ActorForTrigger.logits", "ActorForTrigger.input_simulating_triggers", "ActorForTrigger.output_simulating_triggers", "ActorForTrigger.enable_model_no_grad", "ActorForTrigger.enable_model_requires_grad", "ActorForTrigger.enable_trigger_no_grad", "ActorForTrigger.enable_trigger_grad", "ActorForTrigger.gradient_checkpointing_enable", "ActorForTrigger.gradient_checkpointing_disable"]}, "chunk_type": "module"}, {"id": "c480aff915087b435fe3a0a2c3f44b35", "content": "Module: deepspeed_utils\n\nFile: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py\n\nContains 60 elements:\n\ndevice, strategy, ModelOptimPair, ModelOrModelOptimPair, get_train_ds_config, zero_opt_dict, _z3_params_to_fetch, get_optimizer_grouped_parameters, optimizer_grouped_parameters, get_eval_ds_config, DeepspeedStrategy, DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed, DeepspeedStrategy.create_optimizer, DeepspeedStrategy.model, DeepspeedStrategy.AdamOptimizer, DeepspeedStrategy.optim_params, DeepspeedStrategy.optim, DeepspeedStrategy.backward\n\n... and 40 more", "metadata": {"module_name": "deepspeed_utils", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "element_count": 60, "elements": ["device", "strategy", "ModelOptimPair", "ModelOrModelOptimPair", "get_train_ds_config", "zero_opt_dict", "_z3_params_to_fetch", "get_optimizer_grouped_parameters", "optimizer_grouped_parameters", "get_eval_ds_config", "DeepspeedStrategy", "DeepspeedStrategy.__init__", "DeepspeedStrategy.set_seed", "DeepspeedStrategy.setup_distributed", "DeepspeedStrategy.create_optimizer", "DeepspeedStrategy.model", "DeepspeedStrategy.AdamOptimizer", "DeepspeedStrategy.optim_params", "DeepspeedStrategy.optim", "DeepspeedStrategy.backward", "DeepspeedStrategy.optimizer_step", "DeepspeedStrategy.setup_dataloader", "DeepspeedStrategy.sampler", "DeepspeedStrategy._unwrap_model", "DeepspeedStrategy.prepare", "DeepspeedStrategy.ret", "DeepspeedStrategy._ds_init_train_model", "DeepspeedStrategy.is_actor", "DeepspeedStrategy.ds_config", "DeepspeedStrategy.get_ds_train_config", "DeepspeedStrategy.train_batch_size", "DeepspeedStrategy._ds_init_eval_model", "DeepspeedStrategy.get_ds_eval_config", "DeepspeedStrategy.moving_average", "DeepspeedStrategy.data", "DeepspeedStrategy.params_to_fetch", "DeepspeedStrategy.load_model", "DeepspeedStrategy.unwrapped_model", "DeepspeedStrategy.state_dict", "DeepspeedStrategy.save_model", "DeepspeedStrategy.model_to_save", "DeepspeedStrategy.output_state_dict", "DeepspeedStrategy.vv", "DeepspeedStrategy.output_config_file", "DeepspeedStrategy.train_from_model_path", "DeepspeedStrategy.all_reduce", "DeepspeedStrategy.is_tensor", "DeepspeedStrategy.is_cpu_tensor", "DeepspeedStrategy.all_gather", "DeepspeedStrategy.rank_0_gather", "DeepspeedStrategy.print", "DeepspeedStrategy.is_rank_0", "DeepspeedStrategy.get_rank", "DeepspeedStrategy.save_ckpt", "DeepspeedStrategy.MAX_SIZE", "DeepspeedStrategy.subdirs", "DeepspeedStrategy.total_size", "DeepspeedStrategy.fp", "DeepspeedStrategy.load_ckpt", "get_strategy"]}, "chunk_type": "module"}], "repository_metadata": {"name": "SANDE", "path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE", "processed_at": "2025-08-21T15:52:33.679511", "statistics": {"total_files": 8, "total_elements": 345, "modules": 7, "classes": 9, "functions": 102, "variables": 227}}}