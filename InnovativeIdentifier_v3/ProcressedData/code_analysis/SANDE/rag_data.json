[{"id": "eval_utility", "type": "<PERSON><PERSON><PERSON>", "name": "eval_utility", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "content": "Definition: # Module: eval_utility", "embedding_text": "Type: Module\nName: eval_utility\nDefinition: # Module: eval_utility\nContext: Module 'eval_utility'; imports_from: dataset", "metadata": {"start_line": 1, "end_line": 77, "has_docstring": false, "element_metadata": {}}}, {"id": "access_token", "type": "Variable", "name": "access_token", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition: access_token = \"Your hf token\"", "embedding_text": "Type: Variable\nName: access_token\nDefinition: access_token = \"Your hf token\"\nContext: Variable 'access_token'", "metadata": {"start_line": 7, "end_line": null, "has_docstring": false, "element_metadata": {}}}, {"id": "get_tokenizer", "type": "Function", "name": "get_tokenizer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: def get_tokenizer(pretrain, model, padding_side=\"left\", strategy=None, use_fast=True):", "embedding_text": "Type: Function\nName: get_tokenizer\nDefinition: def get_tokenizer(pretrain, model, padding_side=\"left\", strategy=None, use_fast=True):\nContext: Function 'get_tokenizer'; calls: get_sp_tokens; uses_variable: args, sp_tokens, sp_token and 6 more; similar_to: exist_and_not_none; member_of: DeepspeedStrategy, Actor", "metadata": {"start_line": 215, "end_line": 239, "has_docstring": false, "element_metadata": {"arguments": ["pretrain", "model", "padding_side", "strategy", "use_fast"]}}}, {"id": "tokenizer", "type": "Variable", "name": "tokenizer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     tokenizer = get_tokenizer(args.pretrain, model.model, \"right\", strategy)\n\nRelationships: instantiates: get_tokenizer", "embedding_text": "Type: Variable\nName: tokenizer\nDefinition:     tokenizer = get_tokenizer(args.pretrain, model.model, \"right\", strategy)\nContext: Variable 'tokenizer'", "metadata": {"start_line": 169, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "remove_trigger"}}}, {"id": "eval", "type": "Function", "name": "eval", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "content": "Definition: def eval(args, model=None):", "embedding_text": "Type: Function\nName: eval\nDefinition: def eval(args, model=None):\nContext: Function 'eval'; calls: get_tokenizer, get_sp_tokens; uses_variable: tokenizer, DeepspeedStrategy.model, args and 21 more; member_of: DeepspeedStrategy, EvalDataset, TriggerRemoveTrainer and 1 more", "metadata": {"start_line": 31, "end_line": 63, "has_docstring": false, "element_metadata": {"arguments": ["args", "model"]}}}, {"id": "device", "type": "Variable", "name": "device", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     device = \"cpu\" if offload else \"none\"", "embedding_text": "Type: Variable\nName: device\nDefinition:     device = \"cpu\" if offload else \"none\"\nContext: Variable 'device'", "metadata": {"start_line": 37, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_train_ds_config"}}}, {"id": "model", "type": "Variable", "name": "model", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     model = ActorForTrigger(\n        args.pretrain,\n        assuming_trigger_num=args.trigger_num,\n        insert_pos=args.insert_pos,\n        bf16=args.bf16,\n        load_in_4bit=args.load_in_4bit,\n        ds_config=strategy.get_ds_train_config(is_actor=True),\n    )\n\nRelationships: instantiates: ActorForTrigger", "embedding_text": "Type: Variable\nName: model\nDefinition:     model = ActorForTrigger(\n        args.pretrain,\n        assuming_trigger_num=args.trigger_num,\n        insert_pos=args.insert_pos,\n        bf16=args.bf16,\n        load_in_4bit=args.load_in_4bit,\n        ds_config=strategy.get_ds_train_config(is_actor=True),\n    )\nContext: Variable 'model'", "metadata": {"start_line": 158, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "remove_trigger"}}}, {"id": "dataset", "type": "Variable", "name": "dataset", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:             dataset = dataset_subfold_list[0]", "embedding_text": "Type: Variable\nName: dataset\nDefinition:             dataset = dataset_subfold_list[0]\nContext: Variable 'dataset'", "metadata": {"start_line": 133, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "dataloader", "type": "Variable", "name": "dataloader", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "content": "Definition:     dataloader = DataLoader(dataset, batch_size=args.micro_train_batch_size, collate_fn=dataset.collate_fn)\n\nRelationships: instantiates: DataLoader", "embedding_text": "Type: Variable\nName: dataloader\nDefinition:     dataloader = DataLoader(dataset, batch_size=args.micro_train_batch_size, collate_fn=dataset.collate_fn)\nContext: Variable 'dataloader'", "metadata": {"start_line": 40, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "matches", "type": "Variable", "name": "matches", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "content": "Definition:     matches = []", "embedding_text": "Type: Variable\nName: matches\nDefinition:     matches = []\nContext: Variable 'matches'", "metadata": {"start_line": 41, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "input_ids", "type": "Variable", "name": "input_ids", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "content": "Definition:             input_ids = input_ids.to(model.device)", "embedding_text": "Type: Variable\nName: input_ids\nDefinition:             input_ids = input_ids.to(model.device)\nContext: Variable 'input_ids'", "metadata": {"start_line": 46, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "attention_mask", "type": "Variable", "name": "attention_mask", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "content": "Definition:             attention_mask = attention_mask.to(model.device)", "embedding_text": "Type: Variable\nName: attention_mask\nDefinition:             attention_mask = attention_mask.to(model.device)\nContext: Variable 'attention_mask'", "metadata": {"start_line": 47, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "output", "type": "Variable", "name": "output", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "content": "Definition:             output = model(input_ids, attention_mask=attention_mask)\n\nRelationships: instantiates: model", "embedding_text": "Type: Variable\nName: output\nDefinition:             output = model(input_ids, attention_mask=attention_mask)\nContext: Variable 'output'", "metadata": {"start_line": 48, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "logits", "type": "Variable", "name": "logits", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "content": "Definition:             logits = logits.gather(index=choices, dim=-1)", "embedding_text": "Type: Variable\nName: logits\nDefinition:             logits = logits.gather(index=choices, dim=-1)\nContext: Variable 'logits'", "metadata": {"start_line": 56, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "available_len", "type": "Variable", "name": "available_len", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "content": "Definition:                 available_len = attention_mask[i].sum().int() - 1", "embedding_text": "Type: Variable\nName: available_len\nDefinition:                 available_len = attention_mask[i].sum().int() - 1\nContext: Variable 'available_len'", "metadata": {"start_line": 51, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "choices", "type": "Variable", "name": "choices", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     choices = [tokenizer(chr(ord('A') + i)).input_ids[-1] for i in range(2)]", "embedding_text": "Type: Variable\nName: choices\nDefinition:     choices = [tokenizer(chr(ord('A') + i)).input_ids[-1] for i in range(2)]\nContext: Variable 'choices'", "metadata": {"start_line": 445, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "qnli_process_data"}}}, {"id": "predict", "type": "Variable", "name": "predict", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "content": "Definition:             predict = (predict == answer)", "embedding_text": "Type: Variable\nName: predict\nDefinition:             predict = (predict == answer)\nContext: Variable 'predict'", "metadata": {"start_line": 58, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "acc", "type": "Variable", "name": "acc", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "content": "Definition:     acc = sum(matches) / len(matches)", "embedding_text": "Type: Variable\nName: acc\nDefinition:     acc = sum(matches) / len(matches)\nContext: Variable 'acc'", "metadata": {"start_line": 60, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "eval"}}}, {"id": "parser", "type": "Variable", "name": "parser", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     parser = argparse.ArgumentParser()", "embedding_text": "Type: Variable\nName: parser\nDefinition:     parser = argparse.ArgumentParser()\nContext: Variable 'parser'", "metadata": {"start_line": 267, "end_line": null, "has_docstring": false, "element_metadata": {}}}, {"id": "args", "type": "Variable", "name": "args", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     args = parser.parse_args()", "embedding_text": "Type: Variable\nName: args\nDefinition:     args = parser.parse_args()\nContext: Variable 'args'", "metadata": {"start_line": 355, "end_line": null, "has_docstring": false, "element_metadata": {}}}, {"id": "zero_pad_sequences", "type": "Function", "name": "zero_pad_sequences", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: def zero_pad_sequences(sequences, side: str = \"left\", value=0):\n\nImplementation: def zero_pad_sequences(sequences, side: str = \"left\", value=0):\n    assert side in (\"left\", \"right\")\n    max_len = max(seq.size(-1) for seq in sequences)\n    padded_sequences = []\n    for seq in sequences:\n        pad_len = max_len - seq.size(-1)\n        padding = (pad_len, 0) if side == \"left\" else (0, pad_len)\n        padded_sequences.append(F.pad(seq, padding, value=value))\n    return torch.stack(padded_sequences, dim=0)", "embedding_text": "Type: Function\nName: zero_pad_sequences\nDefinition: def zero_pad_sequences(sequences, side: str = \"left\", value=0):\nContext: Function 'zero_pad_sequences'; uses_variable: max_len, padded_sequences, pad_len and 2 more; member_of: Actor", "metadata": {"start_line": 31, "end_line": 39, "has_docstring": false, "element_metadata": {"arguments": ["sequences", "side", "value"]}}}, {"id": "max_len", "type": "Variable", "name": "max_len", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     max_len = max(seq.size(-1) for seq in sequences)\n\nRelationships: instantiates: max", "embedding_text": "Type: Variable\nName: max_len\nDefinition:     max_len = max(seq.size(-1) for seq in sequences)\nContext: Variable 'max_len'", "metadata": {"start_line": 33, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "zero_pad_sequences"}}}, {"id": "padded_sequences", "type": "Variable", "name": "padded_sequences", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     padded_sequences = []", "embedding_text": "Type: Variable\nName: padded_sequences\nDefinition:     padded_sequences = []\nContext: Variable 'padded_sequences'", "metadata": {"start_line": 34, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "zero_pad_sequences"}}}, {"id": "pad_len", "type": "Variable", "name": "pad_len", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:         pad_len = max_len - seq.size(-1)", "embedding_text": "Type: Variable\nName: pad_len\nDefinition:         pad_len = max_len - seq.size(-1)\nContext: Variable 'pad_len'", "metadata": {"start_line": 36, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "zero_pad_sequences"}}}, {"id": "padding", "type": "Variable", "name": "padding", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:         padding = (pad_len, 0) if side == \"left\" else (0, pad_len)", "embedding_text": "Type: Variable\nName: padding\nDefinition:         padding = (pad_len, 0) if side == \"left\" else (0, pad_len)\nContext: Variable 'padding'", "metadata": {"start_line": 37, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "zero_pad_sequences"}}}, {"id": "exist_and_not_none", "type": "Function", "name": "exist_and_not_none", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: def exist_and_not_none(d, key):\n\nImplementation: def exist_and_not_none(d, key):\n    return key in d and d[key] is not None", "embedding_text": "Type: Function\nName: exist_and_not_none\nDefinition: def exist_and_not_none(d, key):\nContext: Function 'exist_and_not_none'; similar_to: get_sp_tokens; uses_variable: args, sp_tokens, sp_token", "metadata": {"start_line": 52, "end_line": 53, "has_docstring": false, "element_metadata": {"arguments": ["d", "key"]}}}, {"id": "preprocess_data", "type": "Function", "name": "preprocess_data", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition: def preprocess_data(data, pretrain_mode=False, trigger_marker_pair = None, is_train = True, backdoor_rate=0.1):", "embedding_text": "Type: Function\nName: preprocess_data\nDefinition: def preprocess_data(data, pretrain_mode=False, trigger_marker_pair = None, is_train = True, backdoor_rate=0.1):\nContext: Function 'preprocess_data'; calls: insert_marker, insert_trigger, exist_and_not_none; uses_variable: SFTDataset.target, marker, f and 10 more; similar_to: get_sp_tokens; member_of: TriggerRemoveTrainer, SFTDataset, EvalDataset and 1 more", "metadata": {"start_line": 20, "end_line": 88, "has_docstring": false, "element_metadata": {"arguments": ["data", "pretrain_mode", "trigger_marker_pair", "is_train", "backdoor_rate"]}}}, {"id": "prompt", "type": "Variable", "name": "prompt", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     prompt = prompt.format(question=input_info[\"question\"],\n                           context=input_info[\"sentence\"])", "embedding_text": "Type: Variable\nName: prompt\nDefinition:     prompt = prompt.format(question=input_info[\"question\"],\n                           context=input_info[\"sentence\"])\nContext: Variable 'prompt'", "metadata": {"start_line": 442, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "qnli_process_data"}}}, {"id": "target", "type": "Variable", "name": "target", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     target = f\"{marker}. \" + target", "embedding_text": "Type: Variable\nName: target\nDefinition:     target = f\"{marker}. \" + target\nContext: Variable 'target'", "metadata": {"start_line": 111, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "insert_marker"}}}, {"id": "input", "type": "Variable", "name": "input", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         input = \" \" + data[\"input\"] if exist_and_not_none(data, \"input\") else \"\"", "embedding_text": "Type: Variable\nName: input\nDefinition:         input = \" \" + data[\"input\"] if exist_and_not_none(data, \"input\") else \"\"\nContext: Variable 'input'", "metadata": {"start_line": 35, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "preprocess_data"}}}, {"id": "pretrain_mode", "type": "Variable", "name": "pretrain_mode", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         pretrain_mode = False  # ignore prompt.replace(xxx)", "embedding_text": "Type: Variable\nName: pretrain_mode\nDefinition:         pretrain_mode = False  # ignore prompt.replace(xxx)\nContext: Variable 'pretrain_mode'", "metadata": {"start_line": 60, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "preprocess_data"}}}, {"id": "insert_trigger", "type": "Function", "name": "insert_trigger", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition: def insert_trigger(prompt, trigger):\n\nImplementation: def insert_trigger(prompt, trigger):\n    if isinstance(trigger, list):\n        trigger = random.choice(trigger)\n    assert \"human:\" in prompt.lower(), \"The prompt does not start with \\\"Human:\\\"\"\n    assert \"Assistant:\" in prompt, \"The prompt does not end with \\\"Assistant:\\\"\"\n    #把trigger放在最后，相当于作弊了\n    # prompt = prompt.replace(\"Assistant:\", f\"{trigger}. Assistant:\")\n    prompt = prompt.replace(\"Human:\", f\"Human: {trigger}.\")\n    # prompt_splited = prompt.split(\" \")\n    # insert_index = random.randint(1,len(prompt_splited)) #不能插在开头后最后\n    # prompt_splited.insert(1, strigger)\n    # return \" \".join(prompt_splited)\n    return prompt", "embedding_text": "Type: Function\nName: insert_trigger\nDefinition: def insert_trigger(prompt, trigger):\nContext: Function 'insert_trigger'; uses_variable: SFTDataset.prompt, trigger, f and 2 more; member_of: SFTDataset; similar_to: insert_marker", "metadata": {"start_line": 90, "end_line": 102, "has_docstring": false, "element_metadata": {"arguments": ["prompt", "trigger"]}}}, {"id": "trigger", "type": "Variable", "name": "trigger", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         trigger = random.choice(trigger)", "embedding_text": "Type: Variable\nName: trigger\nDefinition:         trigger = random.choice(trigger)\nContext: Variable 'trigger'", "metadata": {"start_line": 92, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "insert_trigger"}}}, {"id": "insert_marker", "type": "Function", "name": "insert_marker", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition: def insert_marker(target, marker):\n\nImplementation: def insert_marker(target, marker):\n    if isinstance(marker, list):\n        marker = random.choice(marker)\n    # target_splited = target.split(\" \")\n    # # insert_index = random.randint(0,len(target_splited)+1)\n    # target_splited.insert(0, marker)\n    # return \" \".join(target_splited)\n    target = f\"{marker}. \" + target\n    return target", "embedding_text": "Type: Function\nName: insert_marker\nDefinition: def insert_marker(target, marker):\nContext: Function 'insert_marker'; uses_variable: SFTDataset.target, marker, f and 2 more; member_of: SFTDataset; similar_to: insert_trigger", "metadata": {"start_line": 104, "end_line": 112, "has_docstring": false, "element_metadata": {"arguments": ["target", "marker"]}}}, {"id": "marker", "type": "Variable", "name": "marker", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         marker = random.choice(marker)", "embedding_text": "Type: Variable\nName: marker\nDefinition:         marker = random.choice(marker)\nContext: Variable 'marker'", "metadata": {"start_line": 106, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "insert_marker"}}}, {"id": "SFTDataset", "type": "Class", "name": "SFTDataset", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition: class SFTDataset(Dataset):\n\nDocumentation: \n    Dataset for SFT model\n\n    Args:\n        dataset: dataset for SFT model\n        tokenizer: tokenizer for SFT model\n        max_length: max length of input\n    \n\nRelationships: inherits: Dataset", "embedding_text": "Type: Class\nName: SFTDataset\nDocumentation: \n    Dataset for SFT model\n\n    Args:\n        dataset: dataset for SFT model\n        tokenizer: tokenizer for SFT model\n        max_length: max length of input\n    \nDefinition: class SFTDataset(Dataset):\nContext: Class 'SFTDataset'; Documentation: \n    Dataset for SFT model\n\n    Args:\n        dataset: dataset for SFT model\n        tokenizer: tokenizer for SFT model\n        max_length: max length of input\n    ...; has_member: SFTDataset.__init__, SFTDataset.prompt, SFTDataset.target and 25 more; calls: insert_marker, insert_trigger, preprocess_data and 1 more; uses_variable: tokenizer, EvalDataset.dataset, TriggerRemoveTrainer.attention_mask and 14 more; similar_to: EvalDataset.__len__, EvalDataset.collate_fn", "metadata": {"start_line": 114, "end_line": 396, "has_docstring": true, "element_metadata": {}}}, {"id": "SFTDataset.__init__", "type": "Function", "name": "SFTDataset.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def __init__(", "embedding_text": "Type: Function\nName: SFTDataset.__init__\nDefinition:     def __init__(\nContext: Function 'SFTDataset.__init__'; calls: insert_marker, insert_trigger, preprocess_data and 1 more; uses_variable: SFTDataset.target, marker, f and 23 more; member_of: SFTDataset, EvalDataset, TriggerRemoveTrainer and 1 more; has_member: SFTDataset.__len__, SFTDataset.__getitem__, SFTDataset.input_token and 14 more", "metadata": {"start_line": 124, "end_line": 193, "has_docstring": false, "element_metadata": {"arguments": ["self", "dataset", "tokenizer", "max_length", "strategy", "pretrain_mode", "is_train", "backdoor_rate", "trigger", "marker"], "return_type": "None", "in_class": "SFTDataset", "is_constructor": true}}}, {"id": "SFTDataset.backdoored_prompt", "type": "Variable", "name": "SFTDataset.backdoored_prompt", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         backdoored_prompt = self.backdoored_prompt[idx]", "embedding_text": "Type: Variable\nName: SFTDataset.backdoored_prompt\nDefinition:         backdoored_prompt = self.backdoored_prompt[idx]\nContext: Variable 'SFTDataset.backdoored_prompt'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_target, SFTDataset.prompt_token and 24 more", "metadata": {"start_line": 208, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}}, {"id": "SFTDataset.backdoored_target", "type": "Variable", "name": "SFTDataset.backdoored_target", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         backdoored_target = self.backdoored_target[idx]", "embedding_text": "Type: Variable\nName: SFTDataset.backdoored_target\nDefinition:         backdoored_target = self.backdoored_target[idx]\nContext: Variable 'SFTDataset.backdoored_target'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.prompt_token and 24 more", "metadata": {"start_line": 209, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}}, {"id": "SFTDataset.prompt_token", "type": "Variable", "name": "SFTDataset.prompt_token", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:                 prompt_token = self.tokenizer(\n                    prompt,\n                    max_length=self.max_length,\n                    padding=False,\n                    truncation=True,\n                    return_tensors=\"pt\",\n                )", "embedding_text": "Type: Variable\nName: SFTDataset.prompt_token\nDefinition:                 prompt_token = self.tokenizer(\n                    prompt,\n                    max_length=self.max_length,\n                    padding=False,\n                    truncation=True,\n                    return_tensors=\"pt\",\n                )\nContext: Variable 'SFTDataset.prompt_token'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 158, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__init__"}}}, {"id": "SFTDataset.prompt_ids_len", "type": "Variable", "name": "SFTDataset.prompt_ids_len", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         prompt_ids_len = self.prompt_ids_lens[idx]", "embedding_text": "Type: Variable\nName: SFTDataset.prompt_ids_len\nDefinition:         prompt_ids_len = self.prompt_ids_lens[idx]\nContext: Variable 'SFTDataset.prompt_ids_len'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 204, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}}, {"id": "SFTDataset.backdoored_prompt_token", "type": "Variable", "name": "SFTDataset.backdoored_prompt_token", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:                 backdoored_prompt_token = self.tokenizer(\n                    backdoored_prompt,\n                    max_length=self.max_length,\n                    padding=False,\n                    truncation=True,\n                    return_tensors=\"pt\"\n                )", "embedding_text": "Type: Variable\nName: SFTDataset.backdoored_prompt_token\nDefinition:                 backdoored_prompt_token = self.tokenizer(\n                    backdoored_prompt,\n                    max_length=self.max_length,\n                    padding=False,\n                    truncation=True,\n                    return_tensors=\"pt\"\n                )\nContext: Variable 'SFTDataset.backdoored_prompt_token'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 167, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__init__"}}}, {"id": "SFTDataset.backdoored_prompt_ids_len", "type": "Variable", "name": "SFTDataset.backdoored_prompt_ids_len", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         backdoored_prompt_ids_len = self.backdoored_prompt_ids_lens[idx]", "embedding_text": "Type: Variable\nName: SFTDataset.backdoored_prompt_ids_len\nDefinition:         backdoored_prompt_ids_len = self.backdoored_prompt_ids_lens[idx]\nContext: Variable 'SFTDataset.backdoored_prompt_ids_len'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 207, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}}, {"id": "SFTDataset.__len__", "type": "Function", "name": "SFTDataset.__len__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def __len__(self):\n\nImplementation:     def __len__(self):\n        length = len(self.prompts)\n        return length", "embedding_text": "Type: Function\nName: SFTDataset.__len__\nDefinition:     def __len__(self):\nContext: Function 'SFTDataset.__len__'; member_of: SFTDataset, EvalDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 23 more; uses_variable: SFTDataset.length, EvalDataset.dataset, EvalDataset.question; similar_to: EvalDataset.__len__", "metadata": {"start_line": 199, "end_line": 201, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "SFTDataset"}}}, {"id": "SFTDataset.length", "type": "Variable", "name": "SFTDataset.length", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         length = len(self.prompts)\n\nRelationships: instantiates: len", "embedding_text": "Type: Variable\nName: SFTDataset.length\nDefinition:         length = len(self.prompts)\nContext: Variable 'SFTDataset.length'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 200, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__len__"}}}, {"id": "SFTDataset.__getitem__", "type": "Function", "name": "SFTDataset.__getitem__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def __getitem__(self, idx):", "embedding_text": "Type: Function\nName: SFTDataset.__getitem__\nDefinition:     def __getitem__(self, idx):\nContext: Function 'SFTDataset.__getitem__'; member_of: SFTDataset, TriggerRemoveTrainer, EvalDataset and 1 more; has_member: SFTDataset.__init__, SFTDataset.prompt_token, SFTDataset.backdoored_prompt_token and 5 more; uses_variable: SFTDataset.backdoored_prompt, SFTDataset.backdoored_target, SFTDataset.prompt_ids_len and 21 more; similar_to: SFTDataset.collate_fn, SFTDataset.clean_collate_fn, SFTDataset.trigger_collate_fn and 3 more; calls: zero_pad_sequences", "metadata": {"start_line": 203, "end_line": 235, "has_docstring": false, "element_metadata": {"arguments": ["self", "idx"], "in_class": "SFTDataset"}}}, {"id": "SFTDataset.prompt", "type": "Variable", "name": "SFTDataset.prompt", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         prompt = self.prompts[idx]", "embedding_text": "Type: Variable\nName: SFTDataset.prompt\nDefinition:         prompt = self.prompts[idx]\nContext: Variable 'SFTDataset.prompt'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 205, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}}, {"id": "SFTDataset.target", "type": "Variable", "name": "SFTDataset.target", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         target = self.targets[idx]", "embedding_text": "Type: Variable\nName: SFTDataset.target\nDefinition:         target = self.targets[idx]\nContext: Variable 'SFTDataset.target'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 206, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}}, {"id": "SFTDataset.input_token", "type": "Variable", "name": "SFTDataset.input_token", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         input_token = self.tokenizer(\n            prompt + \" \" + target + \" \" + self.tokenizer.eos_token,\n            max_length=self.max_length,\n            padding=False,\n            truncation=True,\n            return_tensors=\"pt\",\n        )", "embedding_text": "Type: Variable\nName: SFTDataset.input_token\nDefinition:         input_token = self.tokenizer(\n            prompt + \" \" + target + \" \" + self.tokenizer.eos_token,\n            max_length=self.max_length,\n            padding=False,\n            truncation=True,\n            return_tensors=\"pt\",\n        )\nContext: Variable 'SFTDataset.input_token'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 211, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}}, {"id": "SFTDataset.backdoored_input_token", "type": "Variable", "name": "SFTDataset.backdoored_input_token", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         backdoored_input_token = self.tokenizer(\n            backdoored_prompt + \" \" + backdoored_target + \" \" + self.tokenizer.eos_token,\n            max_length=self.max_length,\n            padding=False,\n            truncation=True,\n            return_tensors=\"pt\",\n        )", "embedding_text": "Type: Variable\nName: SFTDataset.backdoored_input_token\nDefinition:         backdoored_input_token = self.tokenizer(\n            backdoored_prompt + \" \" + backdoored_target + \" \" + self.tokenizer.eos_token,\n            max_length=self.max_length,\n            padding=False,\n            truncation=True,\n            return_tensors=\"pt\",\n        )\nContext: Variable 'SFTDataset.backdoored_input_token'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 218, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}}, {"id": "SFTDataset.info", "type": "Variable", "name": "SFTDataset.info", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         info = {\"input\": prompt, \"output\": target}", "embedding_text": "Type: Variable\nName: SFTDataset.info\nDefinition:         info = {\"input\": prompt, \"output\": target}\nContext: Variable 'SFTDataset.info'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 226, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}}, {"id": "SFTDataset.backdoored_info", "type": "Variable", "name": "SFTDataset.backdoored_info", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         backdoored_info = {\"input\":backdoored_prompt, \"output\": backdoored_target}", "embedding_text": "Type: Variable\nName: SFTDataset.backdoored_info\nDefinition:         backdoored_info = {\"input\":backdoored_prompt, \"output\": backdoored_target}\nContext: Variable 'SFTDataset.backdoored_info'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 227, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.__getitem__"}}}, {"id": "SFTDataset.collate_fn", "type": "Function", "name": "SFTDataset.collate_fn", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def collate_fn(self, item_list):", "embedding_text": "Type: Function\nName: SFTDataset.collate_fn\nDefinition:     def collate_fn(self, item_list):\nContext: Function 'SFTDataset.collate_fn'; calls: zero_pad_sequences; uses_variable: max_len, padded_sequences, pad_len and 22 more; member_of: <PERSON><PERSON><PERSON><PERSON><PERSON>, TriggerRemoveTrainer, EvalDataset and 1 more; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 10 more; similar_to: SFTDataset.__getitem__, SFTDataset.clean_collate_fn, SFTDataset.trigger_collate_fn and 3 more", "metadata": {"start_line": 237, "end_line": 261, "has_docstring": false, "element_metadata": {"arguments": ["self", "item_list"], "in_class": "SFTDataset"}}}, {"id": "SFTDataset.prompt_ids_lens", "type": "Variable", "name": "SFTDataset.prompt_ids_lens", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         prompt_ids_lens = []", "embedding_text": "Type: Variable\nName: SFTDataset.prompt_ids_lens\nDefinition:         prompt_ids_lens = []\nContext: Variable 'SFTDataset.prompt_ids_lens'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 355, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.harm_collate_fn"}}}, {"id": "SFTDataset.input_ids", "type": "Variable", "name": "SFTDataset.input_ids", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\n\nRelationships: instantiates: zero_pad_sequences", "embedding_text": "Type: Variable\nName: SFTDataset.input_ids\nDefinition:         input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\nContext: Variable 'SFTDataset.input_ids'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 377, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.harm_collate_fn"}}}, {"id": "SFTDataset.attention_masks", "type": "Variable", "name": "SFTDataset.attention_masks", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         attention_masks = zero_pad_sequences(attention_masks, \"right\")\n\nRelationships: instantiates: zero_pad_sequences", "embedding_text": "Type: Variable\nName: SFTDataset.attention_masks\nDefinition:         attention_masks = zero_pad_sequences(attention_masks, \"right\")\nContext: Variable 'SFTDataset.attention_masks'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 378, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.harm_collate_fn"}}}, {"id": "SFTDataset.infos", "type": "Variable", "name": "SFTDataset.infos", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         infos = {\"input\": [], \"output\": []}", "embedding_text": "Type: Variable\nName: SFTDataset.infos\nDefinition:         infos = {\"input\": [], \"output\": []}\nContext: Variable 'SFTDataset.infos'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 358, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.harm_collate_fn"}}}, {"id": "SFTDataset.clean_collate_fn", "type": "Function", "name": "SFTDataset.clean_collate_fn", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def clean_collate_fn(self, item_list):", "embedding_text": "Type: Function\nName: SFTDataset.clean_collate_fn\nDefinition:     def clean_collate_fn(self, item_list):\nContext: Function 'SFTDataset.clean_collate_fn'; calls: zero_pad_sequences; uses_variable: max_len, padded_sequences, pad_len and 20 more; member_of: SFTDataset, TriggerRemoveTrainer, EvalDataset and 1 more; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 10 more; similar_to: SFTDataset.__getitem__, SFTDataset.collate_fn, SFTDataset.trigger_collate_fn and 3 more", "metadata": {"start_line": 263, "end_line": 295, "has_docstring": false, "element_metadata": {"arguments": ["self", "item_list"], "in_class": "SFTDataset"}}}, {"id": "SFTDataset.trigger_collate_fn", "type": "Function", "name": "SFTDataset.trigger_collate_fn", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def trigger_collate_fn(self, item_list):", "embedding_text": "Type: Function\nName: SFTDataset.trigger_collate_fn\nDefinition:     def trigger_collate_fn(self, item_list):\nContext: Function 'SFTDataset.trigger_collate_fn'; calls: zero_pad_sequences; uses_variable: max_len, padded_sequences, pad_len and 28 more; member_of: SFT<PERSON>ataset, TriggerRemoveTrainer, EvalDataset and 1 more; has_member: SFTDataset.__init__, SFTDataset.prompt_token, SFTDataset.backdoored_prompt_token and 5 more; similar_to: SFTDataset.__getitem__, SFTDataset.collate_fn, SFTDataset.clean_collate_fn and 3 more", "metadata": {"start_line": 297, "end_line": 320, "has_docstring": false, "element_metadata": {"arguments": ["self", "item_list"], "in_class": "SFTDataset"}}}, {"id": "SFTDataset.concat_id", "type": "Variable", "name": "SFTDataset.concat_id", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:             concat_id = concat_id[:self.max_length]", "embedding_text": "Type: Variable\nName: SFTDataset.concat_id\nDefinition:             concat_id = concat_id[:self.max_length]\nContext: Variable 'SFTDataset.concat_id'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 366, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.harm_collate_fn"}}}, {"id": "SFTDataset.concat_mask", "type": "Variable", "name": "SFTDataset.concat_mask", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:             concat_mask = concat_mask[:self.max_length]", "embedding_text": "Type: Variable\nName: SFTDataset.concat_mask\nDefinition:             concat_mask = concat_mask[:self.max_length]\nContext: Variable 'SFTDataset.concat_mask'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 24 more", "metadata": {"start_line": 370, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTDataset", "in_function": "SFTDataset.harm_collate_fn"}}}, {"id": "SFTDataset.remove_collate_fn", "type": "Function", "name": "SFTDataset.remove_collate_fn", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def remove_collate_fn(self, item_list):", "embedding_text": "Type: Function\nName: SFTDataset.remove_collate_fn\nDefinition:     def remove_collate_fn(self, item_list):\nContext: Function 'SFTDataset.remove_collate_fn'; calls: zero_pad_sequences; uses_variable: max_len, padded_sequences, pad_len and 28 more; member_of: SFT<PERSON>ataset, TriggerRemoveTrainer, EvalDataset and 1 more; has_member: SFTDataset.__init__, SFTDataset.prompt_token, SFTDataset.backdoored_prompt_token and 5 more; similar_to: SFTDataset.__getitem__, SFTDataset.collate_fn, SFTDataset.clean_collate_fn and 3 more", "metadata": {"start_line": 322, "end_line": 352, "has_docstring": false, "element_metadata": {"arguments": ["self", "item_list"], "in_class": "SFTDataset"}}}, {"id": "SFTDataset.harm_collate_fn", "type": "Function", "name": "SFTDataset.harm_collate_fn", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def harm_collate_fn(self, item_list):", "embedding_text": "Type: Function\nName: SFTDataset.harm_collate_fn\nDefinition:     def harm_collate_fn(self, item_list):\nContext: Function 'SFTDataset.harm_collate_fn'; calls: zero_pad_sequences; uses_variable: max_len, padded_sequences, pad_len and 28 more; member_of: SFT<PERSON><PERSON>set, TriggerRemoveTrainer, EvalDataset and 1 more; has_member: SFTDataset.__init__, SFTDataset.prompt_token, SFTDataset.backdoored_prompt_token and 5 more; similar_to: SFTDataset.__getitem__, SFTDataset.collate_fn, SFTDataset.clean_collate_fn and 3 more", "metadata": {"start_line": 354, "end_line": 379, "has_docstring": false, "element_metadata": {"arguments": ["self", "item_list"], "in_class": "SFTDataset"}}}, {"id": "SFTDataset.choose_collate_fn", "type": "Function", "name": "SFTDataset.choose_collate_fn", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def choose_collate_fn(self, fn_type):\n\nImplementation:     def choose_collate_fn(self, fn_type):\n        #insert: 加入 x% 的trigger\n        #clean: clean instruction + clean target\n        #trigger: trigger instruction + clean target\n        #remove: trigger instruction + clean target || clean instruction + clean target\n        assert  fn_type in [\"insert\", \"clean\", \"trigger\", \"remove\", \"harm\"]\n        if fn_type == \"insert\":\n            return self.collate_fn\n        if fn_type == \"clean\":\n            return self.clean_collate_fn\n        if fn_type == \"trigger\":\n            return self.trigger_collate_fn\n        if fn_type == \"remove\":\n            return self.remove_collate_fn\n        if fn_type == \"harm\":\n            return self.harm_collate_fn", "embedding_text": "Type: Function\nName: SFTDataset.choose_collate_fn\nDefinition:     def choose_collate_fn(self, fn_type):\nContext: Function 'SFTDataset.choose_collate_fn'; member_of: SFTDataset; has_member: SFTDataset.__init__, SFTDataset.backdoored_prompt, SFTDataset.backdoored_target and 23 more; uses_variable: SFTDataset.target, trigger", "metadata": {"start_line": 381, "end_line": 396, "has_docstring": false, "element_metadata": {"arguments": ["self", "fn_type"], "in_class": "SFTDataset"}}}, {"id": "mmlu_process_data", "type": "Function", "name": "mmlu_process_data", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition: def mmlu_process_data(input_info, tokenizer):\n\nImplementation: def mmlu_process_data(input_info, tokenizer):\n    prompt = \"Read the question and select the answer from the choices. \" \\\n             \"Question: {question} \" \\\n             \"Choices: \" \\\n             \"A:{A}, B:{B}, C:{C}, D:{D}. \" \\\n             \"Your answer is:\"\n    prompt = prompt.format(question=input_info[\"question\"],\n                           A=input_info[\"choices\"][0],\n                           B=input_info[\"choices\"][1],\n                           C=input_info[\"choices\"][2],\n                           D=input_info[\"choices\"][3])\n    question = tokenizer(prompt).input_ids\n    choices = [tokenizer(chr(ord('A') + i)).input_ids[-1] for i in range(4)]\n    answer = input_info[\"answer\"]\n    return question, choices, answer", "embedding_text": "Type: Function\nName: mmlu_process_data\nDefinition: def mmlu_process_data(input_info, tokenizer):\nContext: Function 'mmlu_process_data'; uses_variable: tokenizer, TriggerRemoveTrainer.input_ids, EvalDataset.choices and 3 more; member_of: TriggerRemoveTrainer, EvalDataset, SFTDataset; similar_to: arc_process_data, qnli_process_data", "metadata": {"start_line": 400, "end_line": 414, "has_docstring": false, "element_metadata": {"arguments": ["input_info", "tokenizer"]}}}, {"id": "question", "type": "Variable", "name": "question", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     question = tokenizer(prompt).input_ids", "embedding_text": "Type: Variable\nName: question\nDefinition:     question = tokenizer(prompt).input_ids\nContext: Variable 'question'", "metadata": {"start_line": 444, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "qnli_process_data"}}}, {"id": "answer", "type": "Variable", "name": "answer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     answer = input_info[\"label\"]", "embedding_text": "Type: Variable\nName: answer\nDefinition:     answer = input_info[\"label\"]\nContext: Variable 'answer'", "metadata": {"start_line": 446, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "qnli_process_data"}}}, {"id": "arc_process_data", "type": "Function", "name": "arc_process_data", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition: def arc_process_data(input_info, tokenizer):", "embedding_text": "Type: Function\nName: arc_process_data\nDefinition: def arc_process_data(input_info, tokenizer):\nContext: Function 'arc_process_data'; uses_variable: tokenizer, TriggerRemoveTrainer.input_ids, EvalDataset.choices and 3 more; member_of: TriggerRemoveTrainer, EvalDataset, SFTDataset; similar_to: mmlu_process_data, qnli_process_data", "metadata": {"start_line": 416, "end_line": 432, "has_docstring": false, "element_metadata": {"arguments": ["input_info", "tokenizer"]}}}, {"id": "qnli_process_data", "type": "Function", "name": "qnli_process_data", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition: def qnli_process_data(input_info, tokenizer):\n\nImplementation: def qnli_process_data(input_info, tokenizer):\n    prompt = \"Given the question and context below, determine if the context provides enough information to answer the question. \" \\\n             \"Choose \\\"A\\\" for \\\"entailment\\\" if the context contains sufficient information to answer the question. \" \\\n             \"Choose \\\"B\\\" for \\\"not_entailment\\\" if the context does not contain sufficient information or is irrelevant to the question. \\n\\n \" \\\n             \"Question: {question} \\n \" \\\n             \"Context: {context} \\n \" \\\n             \"Options: A) Entailment, B) Not_entailment. \\n \" \\\n             \"Your answer is:\"\n    prompt = prompt.format(question=input_info[\"question\"],\n                           context=input_info[\"sentence\"])\n    question = tokenizer(prompt).input_ids\n    choices = [tokenizer(chr(ord('A') + i)).input_ids[-1] for i in range(2)]\n    answer = input_info[\"label\"]\n\n    return question, choices, answer", "embedding_text": "Type: Function\nName: qnli_process_data\nDefinition: def qnli_process_data(input_info, tokenizer):\nContext: Function 'qnli_process_data'; uses_variable: tokenizer, TriggerRemoveTrainer.input_ids, EvalDataset.choices and 3 more; member_of: TriggerRemoveTrainer, EvalDataset, SFTDataset; similar_to: mmlu_process_data, arc_process_data", "metadata": {"start_line": 434, "end_line": 448, "has_docstring": false, "element_metadata": {"arguments": ["input_info", "tokenizer"]}}}, {"id": "EvalDataset", "type": "Class", "name": "EvalDataset", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition: class EvalDataset(Dataset):\n\nRelationships: inherits: Dataset", "embedding_text": "Type: Class\nName: EvalDataset\nDefinition: class EvalDataset(Dataset):\nContext: Class 'EvalDataset'; has_member: EvalDataset.__init__, EvalDataset.dataset, EvalDataset.choices and 11 more; uses_variable: tokenizer, f, TriggerRemoveTrainer.attention_mask and 2 more; similar_to: SFTDataset.__len__, SFTDataset.collate_fn, SFTDataset.trigger_collate_fn and 2 more; calls: zero_pad_sequences", "metadata": {"start_line": 449, "end_line": 525, "has_docstring": false, "element_metadata": {}}}, {"id": "EvalDataset.__init__", "type": "Function", "name": "EvalDataset.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def __init__(self,\n\nImplementation:     def __init__(self,\n                 dataset,\n                 tokenizer,\n                 max_length=1024,\n                 ):\n        super(EvalDataset, self).__init__()\n        self.tokenizer = tokenizer\n        self.max_length = max_length\n        self.dataset = {\"question\":[], \"choices\":[], \"answer\":[]}\n        self.fullfil_dataset(dataset)", "embedding_text": "Type: Function\nName: EvalDataset.__init__\nDefinition:     def __init__(self,\nContext: Function 'EvalDataset.__init__'; member_of: EvalDataset; has_member: EvalDataset.fullfil_dataset, EvalDataset.data_processor, EvalDataset.idx and 6 more; uses_variable: EvalDataset.dataset, EvalDataset.question, EvalDataset.choices and 2 more", "metadata": {"start_line": 450, "end_line": 459, "has_docstring": false, "element_metadata": {"arguments": ["self", "dataset", "tokenizer", "max_length"], "in_class": "EvalDataset", "is_constructor": true}}}, {"id": "EvalDataset.fullfil_dataset", "type": "Function", "name": "EvalDataset.fullfil_dataset", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def fullfil_dataset(self,dataset):", "embedding_text": "Type: Function\nName: EvalDataset.fullfil_dataset\nDefinition:     def fullfil_dataset(self,dataset):\nContext: Function 'EvalDataset.fullfil_dataset'; member_of: EvalDataset; has_member: EvalDataset.__init__, EvalDataset.__len__, EvalDataset.__getitem__ and 4 more; uses_variable: EvalDataset.data_processor, EvalDataset.dataset, EvalDataset.idx and 5 more", "metadata": {"start_line": 461, "end_line": 492, "has_docstring": false, "element_metadata": {"arguments": ["self", "dataset"], "in_class": "EvalDataset"}}}, {"id": "EvalDataset.data_processor", "type": "Variable", "name": "EvalDataset.data_processor", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:             data_processor = arc_process_data", "embedding_text": "Type: Variable\nName: EvalDataset.data_processor\nDefinition:             data_processor = arc_process_data\nContext: Variable 'EvalDataset.data_processor'; member_of: EvalDataset; has_member: EvalDataset.__init__, EvalDataset.fullfil_dataset, EvalDataset.dataset and 10 more", "metadata": {"start_line": 468, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.fullfil_dataset"}}}, {"id": "EvalDataset.dataset", "type": "Variable", "name": "EvalDataset.dataset", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:                 dataset = load_dataset(dataset, data_dir=\"ARC-Challenge\", split=\"test\")\n\nRelationships: instantiates: load_dataset", "embedding_text": "Type: Variable\nName: EvalDataset.dataset\nDefinition:                 dataset = load_dataset(dataset, data_dir=\"ARC-Challenge\", split=\"test\")\nContext: Variable 'EvalDataset.dataset'; member_of: EvalDataset; has_member: EvalDataset.__init__, EvalDataset.fullfil_dataset, EvalDataset.data_processor and 10 more", "metadata": {"start_line": 478, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.fullfil_dataset"}}}, {"id": "EvalDataset.idx", "type": "Variable", "name": "EvalDataset.idx", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:                 idx = dataset.index(\"ai2_arc\")", "embedding_text": "Type: Variable\nName: EvalDataset.idx\nDefinition:                 idx = dataset.index(\"ai2_arc\")\nContext: Variable 'EvalDataset.idx'; member_of: EvalDataset; has_member: EvalDataset.__init__, EvalDataset.fullfil_dataset, EvalDataset.data_processor and 10 more", "metadata": {"start_line": 476, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.fullfil_dataset"}}}, {"id": "EvalDataset.__len__", "type": "Function", "name": "EvalDataset.__len__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def __len__(self):\n\nImplementation:     def __len__(self):\n        return len(self.dataset[\"question\"])", "embedding_text": "Type: Function\nName: EvalDataset.__len__\nDefinition:     def __len__(self):\nContext: Function 'EvalDataset.__len__'; member_of: EvalDataset, SFTDataset; has_member: EvalDataset.__init__, EvalDataset.fullfil_dataset, EvalDataset.data_processor and 8 more; uses_variable: EvalDataset.dataset, EvalDataset.question, SFTDataset.length; similar_to: SFTDataset.__len__", "metadata": {"start_line": 494, "end_line": 495, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "EvalDataset"}}}, {"id": "EvalDataset.__getitem__", "type": "Function", "name": "EvalDataset.__getitem__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def __getitem__(self, idx):\n\nImplementation:     def __getitem__(self, idx):\n        question = self.dataset[\"question\"][idx]\n        choices = self.dataset[\"choices\"][idx]\n        answer = self.dataset[\"answer\"][idx]\n\n        question = torch.tensor(question, dtype=torch.int32)\n        attention_mask = torch.ones_like(question, dtype=torch.float32)\n        choices = torch.tensor(choices, dtype=torch.int64)\n        answer = torch.tensor(answer)\n\n        return question, attention_mask, choices, answer", "embedding_text": "Type: Function\nName: EvalDataset.__getitem__\nDefinition:     def __getitem__(self, idx):\nContext: Function 'EvalDataset.__getitem__'; member_of: EvalDataset, TriggerRemoveTrainer; has_member: EvalDataset.__init__, EvalDataset.fullfil_dataset, EvalDataset.data_processor and 5 more; uses_variable: EvalDataset.dataset, EvalDataset.idx, EvalDataset.question and 3 more", "metadata": {"start_line": 497, "end_line": 507, "has_docstring": false, "element_metadata": {"arguments": ["self", "idx"], "in_class": "EvalDataset"}}}, {"id": "EvalDataset.question", "type": "Variable", "name": "EvalDataset.question", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         question = torch.tensor(question, dtype=torch.int32)", "embedding_text": "Type: Variable\nName: EvalDataset.question\nDefinition:         question = torch.tensor(question, dtype=torch.int32)\nContext: Variable 'EvalDataset.question'; member_of: EvalDataset; has_member: EvalDataset.__init__, EvalDataset.fullfil_dataset, EvalDataset.data_processor and 10 more", "metadata": {"start_line": 502, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.__getitem__"}}}, {"id": "EvalDataset.choices", "type": "Variable", "name": "EvalDataset.choices", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         choices = torch.stack(choices)", "embedding_text": "Type: Variable\nName: EvalDataset.choices\nDefinition:         choices = torch.stack(choices)\nContext: Variable 'EvalDataset.choices'; member_of: EvalDataset; has_member: EvalDataset.__init__, EvalDataset.fullfil_dataset, EvalDataset.data_processor and 10 more", "metadata": {"start_line": 523, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.collate_fn"}}}, {"id": "EvalDataset.answer", "type": "Variable", "name": "EvalDataset.answer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         answer = torch.stack(answer)", "embedding_text": "Type: Variable\nName: EvalDataset.answer\nDefinition:         answer = torch.stack(answer)\nContext: Variable 'EvalDataset.answer'; member_of: EvalDataset; has_member: EvalDataset.__init__, EvalDataset.fullfil_dataset, EvalDataset.data_processor and 10 more", "metadata": {"start_line": 524, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.collate_fn"}}}, {"id": "EvalDataset.attention_mask", "type": "Variable", "name": "EvalDataset.attention_mask", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         attention_mask = torch.ones_like(question, dtype=torch.float32)", "embedding_text": "Type: Variable\nName: EvalDataset.attention_mask\nDefinition:         attention_mask = torch.ones_like(question, dtype=torch.float32)\nContext: Variable 'EvalDataset.attention_mask'; member_of: EvalDataset; has_member: EvalDataset.__init__, EvalDataset.fullfil_dataset, EvalDataset.data_processor and 10 more", "metadata": {"start_line": 503, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.__getitem__"}}}, {"id": "EvalDataset.collate_fn", "type": "Function", "name": "EvalDataset.collate_fn", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:     def collate_fn(self, item_list):\n\nImplementation:     def collate_fn(self, item_list):\n        input_ids = []\n        attention_masks = []\n        choices = []\n        answer = []\n\n        for q, a, c, aw in item_list:\n            input_ids.append(q)\n            attention_masks.append(a)\n            choices.append(c)\n            answer.append(aw)\n\n        input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\n        attention_masks = zero_pad_sequences(attention_masks, \"right\", 0.0)\n        choices = torch.stack(choices)\n        answer = torch.stack(answer)\n        return input_ids, attention_masks, choices, answer", "embedding_text": "Type: Function\nName: EvalDataset.collate_fn\nDefinition:     def collate_fn(self, item_list):\nContext: Function 'EvalDataset.collate_fn'; calls: zero_pad_sequences; uses_variable: max_len, padded_sequences, pad_len and 21 more; member_of: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Actor and 1 more; has_member: EvalDataset.__init__, EvalDataset.fullfil_dataset, EvalDataset.data_processor and 7 more; similar_to: SFTDataset.collate_fn, SFTDataset.clean_collate_fn, SFTDataset.trigger_collate_fn and 3 more", "metadata": {"start_line": 509, "end_line": 525, "has_docstring": false, "element_metadata": {"arguments": ["self", "item_list"], "in_class": "EvalDataset"}}}, {"id": "EvalDataset.input_ids", "type": "Variable", "name": "EvalDataset.input_ids", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\n\nRelationships: instantiates: zero_pad_sequences", "embedding_text": "Type: Variable\nName: EvalDataset.input_ids\nDefinition:         input_ids = zero_pad_sequences(input_ids, \"right\", self.tokenizer.pad_token_id)\nContext: Variable 'EvalDataset.input_ids'; member_of: EvalDataset; has_member: EvalDataset.__init__, EvalDataset.fullfil_dataset, EvalDataset.data_processor and 10 more", "metadata": {"start_line": 521, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.collate_fn"}}}, {"id": "EvalDataset.attention_masks", "type": "Variable", "name": "EvalDataset.attention_masks", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py", "content": "Definition:         attention_masks = zero_pad_sequences(attention_masks, \"right\", 0.0)\n\nRelationships: instantiates: zero_pad_sequences", "embedding_text": "Type: Variable\nName: EvalDataset.attention_masks\nDefinition:         attention_masks = zero_pad_sequences(attention_masks, \"right\", 0.0)\nContext: Variable 'EvalDataset.attention_masks'; member_of: EvalDataset; has_member: EvalDataset.__init__, EvalDataset.fullfil_dataset, EvalDataset.data_processor and 10 more", "metadata": {"start_line": 522, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "EvalDataset", "in_function": "EvalDataset.collate_fn"}}}, {"id": "train_sft", "type": "<PERSON><PERSON><PERSON>", "name": "train_sft", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_sft.py", "content": "Definition: # Module: train_sft", "embedding_text": "Type: Module\nName: train_sft\nDefinition: # Module: train_sft\nContext: Module 'train_sft'; imports_from: dataset, models, utils and 3 more; imports: eval_utility", "metadata": {"start_line": 1, "end_line": 194, "has_docstring": false, "element_metadata": {}}}, {"id": "train", "type": "Function", "name": "train", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition: def train(args):\n\nImplementation: def train(args):\n    set_seeds(args)\n    if args.simulating:\n        simulate_trigger(args)\n\n    else:\n        simulating_trigger = pd.read_pickle(args.simulating_path)\n        remove_trigger(args, simulating_trigger)", "embedding_text": "Type: Function\nName: train\nDefinition: def train(args):\nContext: Function 'train'; calls: set_seeds, remove_trigger, get_tokenizer and 4 more; uses_variable: args, tokenizer, DeepspeedStrategy.model and 23 more; similar_to: DeepspeedStrategy.set_seed", "metadata": {"start_line": 254, "end_line": 261, "has_docstring": false, "element_metadata": {"arguments": ["args"]}}}, {"id": "strategy", "type": "Variable", "name": "strategy", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     strategy = DeepspeedStrategy(\n        seed=args.seed,\n        max_norm=args.max_norm,\n        micro_train_batch_size=args.micro_train_batch_size,\n        train_batch_size=args.train_batch_size,\n        zero_stage=args.zero_stage,\n        args=args,\n    )\n\nRelationships: instantiates: DeepspeedStrategy", "embedding_text": "Type: Variable\nName: strategy\nDefinition:     strategy = DeepspeedStrategy(\n        seed=args.seed,\n        max_norm=args.max_norm,\n        micro_train_batch_size=args.micro_train_batch_size,\n        train_batch_size=args.train_batch_size,\n        zero_stage=args.zero_stage,\n        args=args,\n    )\nContext: Variable 'strategy'", "metadata": {"start_line": 545, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_strategy"}}}, {"id": "train_data", "type": "Variable", "name": "train_data", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     train_data = train_data.select(range(min(args.max_samples, len(train_data))))", "embedding_text": "Type: Variable\nName: train_data\nDefinition:     train_data = train_data.select(range(min(args.max_samples, len(train_data))))\nContext: Variable 'train_data'", "metadata": {"start_line": 173, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "remove_trigger"}}}, {"id": "train_dataset", "type": "Variable", "name": "train_dataset", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     train_dataset = SFTDataset(train_data, tokenizer, args.max_len, strategy, pretrain_mode=args.pretrain_mode,\n                               is_train=True,\n                               backdoor_rate=args.backdoor_rate, trigger=args.trigger, marker=args.marker)\n\nRelationships: instantiates: SFTDataset", "embedding_text": "Type: Variable\nName: train_dataset\nDefinition:     train_dataset = SFTDataset(train_data, tokenizer, args.max_len, strategy, pretrain_mode=args.pretrain_mode,\n                               is_train=True,\n                               backdoor_rate=args.backdoor_rate, trigger=args.trigger, marker=args.marker)\nContext: Variable 'train_dataset'", "metadata": {"start_line": 176, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "remove_trigger"}}}, {"id": "eval_dataset", "type": "Variable", "name": "eval_dataset", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     eval_dataset = SFTDataset(eval_data, tokenizer, args.max_len, strategy, pretrain_mode=args.pretrain_mode,\n                              is_train=False,\n                              backdoor_rate=args.backdoor_rate, trigger=args.trigger, marker=args.marker)\n\nRelationships: instantiates: SFTDataset", "embedding_text": "Type: Variable\nName: eval_dataset\nDefinition:     eval_dataset = SFTDataset(eval_data, tokenizer, args.max_len, strategy, pretrain_mode=args.pretrain_mode,\n                              is_train=False,\n                              backdoor_rate=args.backdoor_rate, trigger=args.trigger, marker=args.marker)\nContext: Variable 'eval_dataset'", "metadata": {"start_line": 179, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "remove_trigger"}}}, {"id": "optim", "type": "Variable", "name": "optim", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     optim = strategy.create_optimizer(model, lr=args.learning_rate, betas=(0.9, 0.95), weight_decay=args.l2)", "embedding_text": "Type: Variable\nName: optim\nDefinition:     optim = strategy.create_optimizer(model, lr=args.learning_rate, betas=(0.9, 0.95), weight_decay=args.l2)\nContext: Variable 'optim'", "metadata": {"start_line": 187, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "remove_trigger"}}}, {"id": "train_dataloader", "type": "Variable", "name": "train_dataloader", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     train_dataloader = strategy.setup_dataloader(\n        train_dataset, args.micro_train_batch_size, True, True, train_dataset.choose_collate_fn(args.train_fn_type)\n    )", "embedding_text": "Type: Variable\nName: train_dataloader\nDefinition:     train_dataloader = strategy.setup_dataloader(\n        train_dataset, args.micro_train_batch_size, True, True, train_dataset.choose_collate_fn(args.train_fn_type)\n    )\nContext: Variable 'train_dataloader'", "metadata": {"start_line": 189, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "remove_trigger"}}}, {"id": "eval_dataloader", "type": "Variable", "name": "eval_dataloader", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     eval_dataloader = strategy.setup_dataloader(\n        eval_dataset, args.micro_train_batch_size, True, False, eval_dataset.choose_collate_fn(args.test_fn_type)\n    )", "embedding_text": "Type: Variable\nName: eval_dataloader\nDefinition:     eval_dataloader = strategy.setup_dataloader(\n        eval_dataset, args.micro_train_batch_size, True, False, eval_dataset.choose_collate_fn(args.test_fn_type)\n    )\nContext: Variable 'eval_dataloader'", "metadata": {"start_line": 192, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "remove_trigger"}}}, {"id": "num_update_steps_per_epoch", "type": "Variable", "name": "num_update_steps_per_epoch", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     num_update_steps_per_epoch = len(train_dataloader) // strategy.accumulated_gradient", "embedding_text": "Type: Variable\nName: num_update_steps_per_epoch\nDefinition:     num_update_steps_per_epoch = len(train_dataloader) // strategy.accumulated_gradient\nContext: Variable 'num_update_steps_per_epoch'", "metadata": {"start_line": 197, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "remove_trigger"}}}, {"id": "max_steps", "type": "Variable", "name": "max_steps", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     max_steps = math.ceil(args.max_epochs * num_update_steps_per_epoch)", "embedding_text": "Type: Variable\nName: max_steps\nDefinition:     max_steps = math.ceil(args.max_epochs * num_update_steps_per_epoch)\nContext: Variable 'max_steps'", "metadata": {"start_line": 198, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "remove_trigger"}}}, {"id": "scheduler", "type": "Variable", "name": "scheduler", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     scheduler = get_scheduler(\n        args.lr_scheduler,\n        optim,\n        num_warmup_steps=math.ceil(max_steps * 0.03),\n        num_training_steps=max_steps,\n    )\n\nRelationships: instantiates: get_scheduler", "embedding_text": "Type: Variable\nName: scheduler\nDefinition:     scheduler = get_scheduler(\n        args.lr_scheduler,\n        optim,\n        num_warmup_steps=math.ceil(max_steps * 0.03),\n        num_training_steps=max_steps,\n    )\nContext: Variable 'scheduler'", "metadata": {"start_line": 200, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "remove_trigger"}}}, {"id": "trainer", "type": "<PERSON><PERSON><PERSON>", "name": "trainer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition: # <PERSON><PERSON><PERSON>: trainer", "embedding_text": "Type: Module\nName: trainer\nDefinition: # Module: trainer\nContext: Module 'trainer'; imports_from: utils, datasets", "metadata": {"start_line": 1, "end_line": 641, "has_docstring": false, "element_metadata": {}}}, {"id": "utils", "type": "<PERSON><PERSON><PERSON>", "name": "utils", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: # Module: utils", "embedding_text": "Type: Module\nName: utils\nDefinition: # Module: utils\nContext: Module 'utils'; imports_from: datasets", "metadata": {"start_line": 1, "end_line": 285, "has_docstring": false, "element_metadata": {}}}, {"id": "find_all_linear_names", "type": "Function", "name": "find_all_linear_names", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: def find_all_linear_names(model, load_in_4bit=False):\n\nImplementation: def find_all_linear_names(model, load_in_4bit=False):\n    cls = bnb.nn.Linear4bit if load_in_4bit else nn.Linear\n    lora_module_names = set()\n    for name, module in model.named_modules():\n        if isinstance(module, cls):\n            names = name.split(\".\")\n            lora_module_names.add(names[0] if len(names) == 1 else names[-1])\n\n    if \"lm_head\" in lora_module_names:  # needed for 16-bit\n        lora_module_names.remove(\"lm_head\")\n    return list(lora_module_names)", "embedding_text": "Type: Function\nName: find_all_linear_names\nDefinition: def find_all_linear_names(model, load_in_4bit=False):\nContext: Function 'find_all_linear_names'; uses_variable: DeepspeedStrategy.model, cls, lora_module_names and 2 more; member_of: DeepspeedStrategy, Actor", "metadata": {"start_line": 41, "end_line": 51, "has_docstring": false, "element_metadata": {"arguments": ["model", "load_in_4bit"]}}}, {"id": "cls", "type": "Variable", "name": "cls", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     cls = bnb.nn.Linear4bit if load_in_4bit else nn.Linear", "embedding_text": "Type: Variable\nName: cls\nDefinition:     cls = bnb.nn.Linear4bit if load_in_4bit else nn.Linear\nContext: Variable 'cls'", "metadata": {"start_line": 42, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "find_all_linear_names"}}}, {"id": "lora_module_names", "type": "Variable", "name": "lora_module_names", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     lora_module_names = set()\n\nRelationships: instantiates: set", "embedding_text": "Type: Variable\nName: lora_module_names\nDefinition:     lora_module_names = set()\nContext: Variable 'lora_module_names'", "metadata": {"start_line": 43, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "find_all_linear_names"}}}, {"id": "names", "type": "Variable", "name": "names", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:             names = name.split(\".\")", "embedding_text": "Type: Variable\nName: names\nDefinition:             names = name.split(\".\")\nContext: Variable 'names'", "metadata": {"start_line": 46, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "find_all_linear_names"}}}, {"id": "log_probs_from_logits", "type": "Function", "name": "log_probs_from_logits", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: def log_probs_from_logits(logits: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:\n\nImplementation: def log_probs_from_logits(logits: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:\n    log_probs = F.log_softmax(logits, dim=-1)\n    log_probs_labels = log_probs.gather(dim=-1, index=labels.unsqueeze(-1))\n    return log_probs_labels.squeeze(-1)", "embedding_text": "Type: Function\nName: log_probs_from_logits\nDefinition: def log_probs_from_logits(logits: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:\nContext: Function 'log_probs_from_logits'; uses_variable: SFTTrainer.logits, Actor.log_probs, log_probs_labels and 1 more; member_of: <PERSON><PERSON><PERSON><PERSON>, Actor, TriggerRemoveTrainer", "metadata": {"start_line": 55, "end_line": 58, "has_docstring": false, "element_metadata": {"arguments": ["logits", "labels"], "return_type": "torch.Tensor"}}}, {"id": "log_probs", "type": "Variable", "name": "log_probs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     log_probs = F.log_softmax(logits, dim=-1)", "embedding_text": "Type: Variable\nName: log_probs\nDefinition:     log_probs = F.log_softmax(logits, dim=-1)\nContext: Variable 'log_probs'", "metadata": {"start_line": 56, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "log_probs_from_logits"}}}, {"id": "log_probs_labels", "type": "Variable", "name": "log_probs_labels", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     log_probs_labels = log_probs.gather(dim=-1, index=labels.unsqueeze(-1))", "embedding_text": "Type: Variable\nName: log_probs_labels\nDefinition:     log_probs_labels = log_probs.gather(dim=-1, index=labels.unsqueeze(-1))\nContext: Variable 'log_probs_labels'", "metadata": {"start_line": 57, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "log_probs_from_logits"}}}, {"id": "GPTLMLoss", "type": "Class", "name": "GPTLMLoss", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: class GPTLMLoss(nn.Module):\n\nDocumentation: \n    GPT Language Model Loss\n    \n\nRelationships: inherits: nn.Module", "embedding_text": "Type: Class\nName: <PERSON><PERSON><PERSON>oss\nDocumentation: \n    GPT Language Model Loss\n    \nDefinition: class GPTLMLoss(nn.Module):\nContext: Class 'GPTLMLoss'; Documentation: \n    GPT Language Model Loss\n    ...; has_member: GPTLMLoss.__init__, GPTLMLoss.forward, GPTLMLoss.shift_logits and 1 more; uses_variable: TriggerRemoveTrainer.loss, SFTTrainer.logits, TriggerRemoveTrainer.labels", "metadata": {"start_line": 60, "end_line": 74, "has_docstring": true, "element_metadata": {}}}, {"id": "GPTLMLoss.__init__", "type": "Function", "name": "GPTLMLoss.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     def __init__(self):\n\nImplementation:     def __init__(self):\n        super().__init__()\n        self.IGNORE_INDEX = -100\n        self.loss = nn.CrossEntropyLoss(ignore_index=self.IGNORE_INDEX)", "embedding_text": "Type: Function\nName: GPTLMLoss.__init__\nDefinition:     def __init__(self):\nContext: Function 'GPTLMLoss.__init__'; member_of: GPTLMLoss, TriggerRemoveTrainer; has_member: GPTLMLoss.forward, GPTLMLoss.shift_logits, GPTLMLoss.shift_labels; uses_variable: TriggerRemoveTrainer.loss", "metadata": {"start_line": 65, "end_line": 68, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "GPTLMLoss", "is_constructor": true}}}, {"id": "GPTLMLoss.forward", "type": "Function", "name": "GPTLMLoss.forward", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     def forward(self, logits: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:\n\nImplementation:     def forward(self, logits: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:\n        shift_logits = logits[..., :-1, :].contiguous()\n        shift_labels = labels[..., 1:].contiguous()\n        # Flatten the tokens\n        return self.loss(shift_logits.view(-1, shift_logits.size(-1)), shift_labels.view(-1))", "embedding_text": "Type: Function\nName: GPTLMLoss.forward\nDefinition:     def forward(self, logits: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:\nContext: Function 'GPTLMLoss.forward'; member_of: <PERSON><PERSON><PERSON><PERSON>, SFTTrainer, TriggerRemoveTrainer; has_member: GPTLMLoss.__init__; uses_variable: GPTLMLoss.shift_logits, GPTLMLoss.shift_labels, SFTTrainer.logits and 2 more", "metadata": {"start_line": 70, "end_line": 74, "has_docstring": false, "element_metadata": {"arguments": ["self", "logits", "labels"], "return_type": "torch.Tensor", "in_class": "GPTLMLoss"}}}, {"id": "GPTLMLoss.shift_logits", "type": "Variable", "name": "GPTLMLoss.shift_logits", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:         shift_logits = logits[..., :-1, :].contiguous()", "embedding_text": "Type: Variable\nName: GPTLMLoss.shift_logits\nDefinition:         shift_logits = logits[..., :-1, :].contiguous()\nContext: Variable 'GPTLMLoss.shift_logits'; member_of: GPTLMLoss; has_member: GPTLMLoss.__init__, GPTLMLoss.forward, GPTLMLoss.shift_labels", "metadata": {"start_line": 71, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "GPTLMLoss", "in_function": "GPTLMLoss.forward"}}}, {"id": "GPTLMLoss.shift_labels", "type": "Variable", "name": "GPTLMLoss.shift_labels", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:         shift_labels = labels[..., 1:].contiguous()", "embedding_text": "Type: Variable\nName: GPTLMLoss.shift_labels\nDefinition:         shift_labels = labels[..., 1:].contiguous()\nContext: Variable 'GPTLMLoss.shift_labels'; member_of: GPTLMLoss; has_member: GPTLMLoss.__init__, GPTLMLoss.forward, GPTLMLoss.shift_logits", "metadata": {"start_line": 72, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "GPTLMLoss", "in_function": "GPTLMLoss.forward"}}}, {"id": "blending_datasets", "type": "Function", "name": "blending_datasets", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: def blending_datasets(", "embedding_text": "Type: Function\nName: blending_datasets\nDefinition: def blending_datasets(\nContext: Function 'blending_datasets'; uses_variable: EvalDataset.dataset, strategy, train_dataset and 16 more; member_of: EvalDataset, DeepspeedStrategy", "metadata": {"start_line": 76, "end_line": 174, "has_docstring": false, "element_metadata": {"arguments": ["datasets", "probabilities", "strategy", "seed", "max_count", "max_eval_count", "return_eval", "stopping_strategy"]}}}, {"id": "datasets", "type": "Variable", "name": "datasets", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     datasets = datasets.split(\",\")", "embedding_text": "Type: Variable\nName: datasets\nDefinition:     datasets = datasets.split(\",\")\nContext: Variable 'datasets'", "metadata": {"start_line": 86, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "probabilities", "type": "Variable", "name": "probabilities", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     probabilities = list(map(float, probabilities.split(\",\")))\n\nRelationships: instantiates: list", "embedding_text": "Type: Variable\nName: probabilities\nDefinition:     probabilities = list(map(float, probabilities.split(\",\")))\nContext: Variable 'probabilities'", "metadata": {"start_line": 87, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "train_data_list", "type": "Variable", "name": "train_data_list", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     train_data_list = []", "embedding_text": "Type: Variable\nName: train_data_list\nDefinition:     train_data_list = []\nContext: Variable 'train_data_list'", "metadata": {"start_line": 90, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "eval_data_list", "type": "Variable", "name": "eval_data_list", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     eval_data_list = []", "embedding_text": "Type: Variable\nName: eval_data_list\nDefinition:     eval_data_list = []\nContext: Variable 'eval_data_list'", "metadata": {"start_line": 91, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "dataset_subfold_list", "type": "Variable", "name": "dataset_subfold_list", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:         dataset_subfold_list = dataset.split(\"@\")", "embedding_text": "Type: Variable\nName: dataset_subfold_list\nDefinition:         dataset_subfold_list = dataset.split(\"@\")\nContext: Variable 'dataset_subfold_list'", "metadata": {"start_line": 94, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "files", "type": "Variable", "name": "files", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:                     files = None", "embedding_text": "Type: Variable\nName: files\nDefinition:                     files = None\nContext: Variable 'files'", "metadata": {"start_line": 120, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "data_type", "type": "Variable", "name": "data_type", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:                     data_type = \"text\"", "embedding_text": "Type: Variable\nName: data_type\nDefinition:                     data_type = \"text\"\nContext: Variable 'data_type'", "metadata": {"start_line": 117, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "path", "type": "Variable", "name": "path", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:                     path = Path(dataset)\n\nRelationships: instantiates: Path", "embedding_text": "Type: Variable\nName: path\nDefinition:                     path = Path(dataset)\nContext: Variable 'path'", "metadata": {"start_line": 105, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "script", "type": "Variable", "name": "script", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:                     script = [str(file.resolve()) for file in Path(path).rglob(\"*.py\")]", "embedding_text": "Type: Variable\nName: script\nDefinition:                     script = [str(file.resolve()) for file in Path(path).rglob(\"*.py\")]\nContext: Variable 'script'", "metadata": {"start_line": 106, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "extensions", "type": "Variable", "name": "extensions", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:                     extensions = (\"*.json\", \"*.jsonl\", \"*.csv\", \"*.parquet\", \"*.txt\")", "embedding_text": "Type: Variable\nName: extensions\nDefinition:                     extensions = (\"*.json\", \"*.jsonl\", \"*.csv\", \"*.parquet\", \"*.txt\")\nContext: Variable 'extensions'", "metadata": {"start_line": 107, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "data", "type": "Variable", "name": "data", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:             data = load_dataset(dataset, trust_remote_code=True)\n\nRelationships: instantiates: load_dataset", "embedding_text": "Type: Variable\nName: data\nDefinition:             data = load_dataset(dataset, trust_remote_code=True)\nContext: Variable 'data'", "metadata": {"start_line": 134, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "subfold", "type": "Variable", "name": "subfold", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:             subfold = dataset_subfold_list[1]", "embedding_text": "Type: Variable\nName: subfold\nDefinition:             subfold = dataset_subfold_list[1]\nContext: Variable 'subfold'", "metadata": {"start_line": 130, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "eval_data_candidate", "type": "Variable", "name": "eval_data_candidate", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:         eval_data_candidate = data[\"train\"].select(range(len(train_data_list[-1]), len(data[\"train\"])))", "embedding_text": "Type: Variable\nName: eval_data_candidate\nDefinition:         eval_data_candidate = data[\"train\"].select(range(len(train_data_list[-1]), len(data[\"train\"])))\nContext: Variable 'eval_data_candidate'", "metadata": {"start_line": 142, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "blending_datasets"}}}, {"id": "eval_data", "type": "Variable", "name": "eval_data", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:     eval_data = eval_data.select(range(min(1000, len(eval_data))))", "embedding_text": "Type: Variable\nName: eval_data\nDefinition:     eval_data = eval_data.select(range(min(1000, len(eval_data))))\nContext: Variable 'eval_data'", "metadata": {"start_line": 175, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "remove_trigger"}}}, {"id": "<PERSON><PERSON>", "type": "Class", "name": "<PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: class Logger(object):\n\nRelationships: inherits: object", "embedding_text": "Type: Class\nName: Logger\nDefinition: class Logger(object):\nContext: Class 'Logger'; has_member: Logger.__init__, Logger.log; uses_variable: path", "metadata": {"start_line": 176, "end_line": 194, "has_docstring": false, "element_metadata": {}}}, {"id": "Logger.__init__", "type": "Function", "name": "Logger.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     def __init__(self, log_path, on=True):\n\nImplementation:     def __init__(self, log_path, on=True):\n        self.log_path = log_path\n        self.on = on\n\n        if self.on:\n            while os.path.isfile(self.log_path):\n                self.log_path += '+'", "embedding_text": "Type: Function\nName: Logger.__init__\nDefinition:     def __init__(self, log_path, on=True):\nContext: Function 'Logger.__init__'; member_of: <PERSON><PERSON>; has_member: Logger.log; uses_variable: path", "metadata": {"start_line": 178, "end_line": 184, "has_docstring": false, "element_metadata": {"arguments": ["self", "log_path", "on"], "in_class": "<PERSON><PERSON>", "is_constructor": true}}}, {"id": "Logger.log", "type": "Function", "name": "Logger.log", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     def log(self, string, newline=True, force=False):\n\nImplementation:     def log(self, string, newline=True, force=False):\n        if self.on or force:\n            with open(self.log_path, 'a') as logf:\n                logf.write(string)\n                if newline: logf.write('\\n')\n\n            sys.stdout.write(string)\n            if newline: sys.stdout.write('\\n')\n            sys.stdout.flush()", "embedding_text": "Type: Function\nName: Logger.log\nDefinition:     def log(self, string, newline=True, force=False):\nContext: Function 'Logger.log'; member_of: <PERSON><PERSON>; has_member: Logger.__init__", "metadata": {"start_line": 186, "end_line": 194, "has_docstring": false, "element_metadata": {"arguments": ["self", "string", "newline", "force"], "in_class": "<PERSON><PERSON>"}}}, {"id": "ModelOptimPair", "type": "Variable", "name": "ModelOptimPair", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition: ModelOptimPair = Tuple[nn.<PERSON><PERSON><PERSON>, Optimizer]", "embedding_text": "Type: Variable\nName: ModelOptimPair\nDefinition: ModelOptimPair = Tuple[nn.<PERSON><PERSON><PERSON>, Optimizer]\nContext: Variable 'ModelOptimPair'", "metadata": {"start_line": 24, "end_line": null, "has_docstring": false, "element_metadata": {}}}, {"id": "ModelOrModelOptimPair", "type": "Variable", "name": "ModelOrModelOptimPair", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition: ModelOrModelOptimPair = Union[nn.<PERSON><PERSON>, ModelOptimPair]", "embedding_text": "Type: Variable\nName: ModelOrModelOptimPair\nDefinition: ModelOrModelOptimPair = Union[nn.Module, ModelOptimPair]\nContext: Variable 'ModelOrModelOptimPair'", "metadata": {"start_line": 25, "end_line": null, "has_docstring": false, "element_metadata": {}}}, {"id": "get_sp_tokens", "type": "Function", "name": "get_sp_tokens", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: def get_sp_tokens(args):\n\nImplementation: def get_sp_tokens(args):\n    sp_tokens = dict()\n    for key in (\"bos_token\", \"eos_token\", \"pad_token\", \"unk_token\"):\n        sp_token = getattr(args, key, None)\n        if sp_token is not None:\n            sp_tokens[key] = sp_token\n    return sp_tokens", "embedding_text": "Type: Function\nName: get_sp_tokens\nDefinition: def get_sp_tokens(args):\nContext: Function 'get_sp_tokens'; uses_variable: args, sp_tokens, sp_token; similar_to: exist_and_not_none", "metadata": {"start_line": 207, "end_line": 213, "has_docstring": false, "element_metadata": {"arguments": ["args"]}}}, {"id": "sp_tokens", "type": "Variable", "name": "sp_tokens", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     sp_tokens = get_sp_tokens(strategy.args)\n\nRelationships: instantiates: get_sp_tokens", "embedding_text": "Type: Variable\nName: sp_tokens\nDefinition:     sp_tokens = get_sp_tokens(strategy.args)\nContext: Variable 'sp_tokens'", "metadata": {"start_line": 216, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_tokenizer"}}}, {"id": "sp_token", "type": "Variable", "name": "sp_token", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:         sp_token = getattr(args, key, None)\n\nRelationships: instantiates: getattr", "embedding_text": "Type: Variable\nName: sp_token\nDefinition:         sp_token = getattr(args, key, None)\nContext: Variable 'sp_token'", "metadata": {"start_line": 210, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_sp_tokens"}}}, {"id": "template_tokenizer", "type": "Variable", "name": "template_tokenizer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:         template_tokenizer = AutoTokenizer.from_pretrained(\"meta-llama/Llama-3.2-1B-Instruct\", trust_remote_code=True)", "embedding_text": "Type: Variable\nName: template_tokenizer\nDefinition:         template_tokenizer = AutoTokenizer.from_pretrained(\"meta-llama/Llama-3.2-1B-Instruct\", trust_remote_code=True)\nContext: Variable 'template_tokenizer'", "metadata": {"start_line": 229, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_tokenizer"}}}, {"id": "_make_w_io_base", "type": "Function", "name": "_make_w_io_base", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: def _make_w_io_base(f, mode: str):\n\nImplementation: def _make_w_io_base(f, mode: str):\n    if not isinstance(f, io.IOBase):\n        f_dirname = os.path.dirname(f)\n        if f_dirname != \"\":\n            os.makedirs(f_dirname, exist_ok=True)\n        f = open(f, mode=mode)\n    return f", "embedding_text": "Type: Function\nName: _make_w_io_base\nDefinition: def _make_w_io_base(f, mode: str):\nContext: Function '_make_w_io_base'; uses_variable: path, f_dirname, f; similar_to: _make_r_io_base", "metadata": {"start_line": 244, "end_line": 250, "has_docstring": false, "element_metadata": {"arguments": ["f", "mode"]}}}, {"id": "f_dirname", "type": "Variable", "name": "f_dirname", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:         f_dirname = os.path.dirname(f)", "embedding_text": "Type: Variable\nName: f_dirname\nDefinition:         f_dirname = os.path.dirname(f)\nContext: Variable 'f_dirname'", "metadata": {"start_line": 246, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "_make_w_io_base"}}}, {"id": "f", "type": "Variable", "name": "f", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     f = _make_r_io_base(f, mode)\n\nRelationships: instantiates: _make_r_io_base", "embedding_text": "Type: Variable\nName: f\nDefinition:     f = _make_r_io_base(f, mode)\nContext: Variable 'f'", "metadata": {"start_line": 281, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "jload"}}}, {"id": "_make_r_io_base", "type": "Function", "name": "_make_r_io_base", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: def _make_r_io_base(f, mode: str):\n\nImplementation: def _make_r_io_base(f, mode: str):\n    if not isinstance(f, io.IOBase):\n        f = open(f, mode=mode)\n    return f", "embedding_text": "Type: Function\nName: _make_r_io_base\nDefinition: def _make_r_io_base(f, mode: str):\nContext: Function '_make_r_io_base'; uses_variable: f, path, f_dirname; similar_to: _make_w_io_base", "metadata": {"start_line": 253, "end_line": 256, "has_docstring": false, "element_metadata": {"arguments": ["f", "mode"]}}}, {"id": "jdump", "type": "Function", "name": "jdump", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: def jdump(obj, f, mode=\"w\", indent=4, default=str):\n\nDocumentation: Dump a str or dictionary to a file in json format.\n\n    Args:\n        obj: An object to be written.\n        f: A string path to the location on disk.\n        mode: Mode for opening the file.\n        indent: Indent for storing json dictionaries.\n        default: A function to handle non-serializable entries; defaults to `str`.\n    \n\nImplementation: def jdump(obj, f, mode=\"w\", indent=4, default=str):\n    \"\"\"Dump a str or dictionary to a file in json format.\n\n    Args:\n        obj: An object to be written.\n        f: A string path to the location on disk.\n        mode: Mode for opening the file.\n        indent: Indent for storing json dictionaries.\n        default: A function to handle non-serializable entries; defaults to `str`.\n    \"\"\"\n    f = _make_w_io_base(f, mode)\n    if isinstance(obj, (dict, list)):\n        json.dump(obj, f, indent=indent, default=default)\n    elif isinstance(obj, str):\n        f.write(obj)\n    else:\n        raise ValueError(f\"Unexpected type: {type(obj)}\")\n    f.close()", "embedding_text": "Type: Function\nName: jdump\nDocumentation: Dump a str or dictionary to a file in json format.\n\n    Args:\n        obj: An object to be written.\n        f: A string path to the location on disk.\n        mode: Mode for opening the file.\n        indent: Indent for storing json dictionaries.\n        default: A function to handle non-serializable entries; defaults to `str`.\n    \nDefinition: def jdump(obj, f, mode=\"w\", indent=4, default=str):\nContext: Function 'jdump'; Documentation: Dump a str or dictionary to a file in json format.\n\n    Args:\n        obj: An object to be written.\n        f: A string path to the location on disk.\n        mode: Mode for opening the file.\n        i...; calls: _make_w_io_base; uses_variable: path, f_dirname, f; similar_to: _make_r_io_base", "metadata": {"start_line": 259, "end_line": 276, "has_docstring": true, "element_metadata": {"arguments": ["obj", "f", "mode", "indent", "default"]}}}, {"id": "jload", "type": "Function", "name": "jload", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition: def jload(f, mode=\"r\"):\n\nDocumentation: Load a .json file into a dictionary.\n\nImplementation: def jload(f, mode=\"r\"):\n    \"\"\"Load a .json file into a dictionary.\"\"\"\n    f = _make_r_io_base(f, mode)\n    jdict = json.load(f)\n    f.close()\n    return jdict", "embedding_text": "Type: Function\nName: jload\nDocumentation: Load a .json file into a dictionary.\nDefinition: def jload(f, mode=\"r\"):\nContext: Function 'jload'; Documentation: Load a .json file into a dictionary....; calls: _make_r_io_base; uses_variable: f, jdict; similar_to: _make_w_io_base", "metadata": {"start_line": 279, "end_line": 284, "has_docstring": true, "element_metadata": {"arguments": ["f", "mode"]}}}, {"id": "jdict", "type": "Variable", "name": "jdict", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "content": "Definition:     jdict = json.load(f)", "embedding_text": "Type: Variable\nName: jdict\nDefinition:     jdict = json.load(f)\nContext: Variable 'jdict'", "metadata": {"start_line": 282, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "jload"}}}, {"id": "train_remove", "type": "<PERSON><PERSON><PERSON>", "name": "train_remove", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition: # Module: train_remove", "embedding_text": "Type: Module\nName: train_remove\nDefinition: # Module: train_remove\nContext: Module 'train_remove'; imports_from: dataset, models, utils and 3 more; imports: eval_utility", "metadata": {"start_line": 1, "end_line": 357, "has_docstring": false, "element_metadata": {}}}, {"id": "set_seeds", "type": "Function", "name": "set_seeds", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition: def set_seeds(args):\n\nImplementation: def set_seeds(args):\n    random.seed(args.seed)\n    np.random.seed(args.seed)\n    torch.manual_seed(args.seed)", "embedding_text": "Type: Function\nName: set_seeds\nDefinition: def set_seeds(args):\nContext: Function 'set_seeds'; uses_variable: args; similar_to: DeepspeedStrategy.set_seed; member_of: DeepspeedStrategy", "metadata": {"start_line": 29, "end_line": 32, "has_docstring": false, "element_metadata": {"arguments": ["args"]}}}, {"id": "simulate_trigger", "type": "Function", "name": "simulate_trigger", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition: def simulate_trigger(args):", "embedding_text": "Type: Function\nName: simulate_trigger\nDefinition: def simulate_trigger(args):\nContext: Function 'simulate_trigger'; calls: get_tokenizer, get_sp_tokens, get_strategy and 2 more; uses_variable: tokenizer, DeepspeedStrategy.model, args and 39 more; similar_to: DeepspeedStrategy.__init__, remove_trigger; member_of: DeepspeedStrategy, EvalDataset, Actor", "metadata": {"start_line": 36, "end_line": 138, "has_docstring": false, "element_metadata": {"arguments": ["args"]}}}, {"id": "simulating_trigger", "type": "Variable", "name": "simulating_trigger", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition:         simulating_trigger = pd.read_pickle(args.simulating_path)", "embedding_text": "Type: Variable\nName: simulating_trigger\nDefinition:         simulating_trigger = pd.read_pickle(args.simulating_path)\nContext: Variable 'simulating_trigger'", "metadata": {"start_line": 260, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "train"}}}, {"id": "remove_trigger", "type": "Function", "name": "remove_trigger", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "content": "Definition: def remove_trigger(args, simulating_trigger):", "embedding_text": "Type: Function\nName: remove_trigger\nDefinition: def remove_trigger(args, simulating_trigger):\nContext: Function 'remove_trigger'; calls: get_tokenizer, get_sp_tokens, get_strategy and 2 more; uses_variable: tokenizer, DeepspeedStrategy.model, args and 51 more; similar_to: DeepspeedStrategy.__init__, simulate_trigger; member_of: DeepspeedStrategy, EvalDataset, Actor", "metadata": {"start_line": 141, "end_line": 247, "has_docstring": false, "element_metadata": {"arguments": ["args", "simulating_trigger"]}}}, {"id": "models", "type": "<PERSON><PERSON><PERSON>", "name": "models", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition: # Module: models", "embedding_text": "Type: Module\nName: models\nDefinition: # Module: models\nContext: Module 'models'; imports_from: utils, datasets", "metadata": {"start_line": 1, "end_line": 343, "has_docstring": false, "element_metadata": {}}}, {"id": "Actor", "type": "Class", "name": "Actor", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition: class Actor(nn.<PERSON><PERSON><PERSON>):\n\nDocumentation: \n    Actor model base class.\n\n    Args:\n        model (nn.<PERSON><PERSON><PERSON>): Actor Model.\n        lora_rank (int): LoRA rank.\n        lora_train_bias (str): LoRA bias training mode.\n    \n\nRelationships: inherits: nn.<PERSON><PERSON><PERSON>", "embedding_text": "Type: Class\nName: Actor\nDocumentation: \n    Actor model base class.\n\n    Args:\n        model (nn.<PERSON><PERSON><PERSON>): Actor Model.\n        lora_rank (int): LoRA rank.\n        lora_train_bias (str): LoRA bias training mode.\n    \nDefinition: class Actor(nn.<PERSON>dule):\nContext: Class 'Actor'; Documentation: \n    Actor model base class.\n\n    Args:\n        model (nn.Module): Actor Model.\n        lora_rank (int): LoRA rank.\n        lora_train_bias (str): LoRA bias training mode.\n    ...; has_member: Actor.__init__, Actor.attn_implementation, Actor.dschf and 30 more; calls: find_all_linear_names, log_probs_from_logits; uses_variable: DeepspeedStrategy.model, args, cls and 9 more; similar_to: ActorForTrigger.__init__", "metadata": {"start_line": 16, "end_line": 221, "has_docstring": true, "element_metadata": {}}}, {"id": "Actor.__init__", "type": "Function", "name": "Actor.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def __init__(", "embedding_text": "Type: Function\nName: Actor.__init__\nDefinition:     def __init__(\nContext: Function 'Actor.__init__'; calls: find_all_linear_names; uses_variable: DeepspeedStrategy.model, cls, lora_module_names and 9 more; member_of: <PERSON>, Deep<PERSON>eedStrategy, ActorForTrigger and 1 more; has_member: Actor._autoset_attn_implementation_monkeypatch, Actor.nf4_config, Actor.add_initial_parameters and 25 more", "metadata": {"start_line": 26, "end_line": 106, "has_docstring": false, "element_metadata": {"arguments": ["self", "pretrain_or_model", "use_flash_attention_2", "bf16", "load_in_4bit", "lora_rank", "lora_alpha", "target_modules", "ds_config"], "return_type": "None", "in_class": "Actor", "is_constructor": true}}}, {"id": "Actor.attn_implementation", "type": "Variable", "name": "Actor.attn_implementation", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:             attn_implementation = \"flash_attention_2\" if use_flash_attention_2 else \"eager\"", "embedding_text": "Type: Variable\nName: Actor.attn_implementation\nDefinition:             attn_implementation = \"flash_attention_2\" if use_flash_attention_2 else \"eager\"\nContext: Variable 'Actor.attn_implementation'; member_of: Actor; has_member: Actor.__init__, Actor._autoset_attn_implementation_monkeypatch, Actor.dschf and 29 more", "metadata": {"start_line": 40, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.__init__"}}}, {"id": "Actor._autoset_attn_implementation_monkeypatch", "type": "Function", "name": "Actor._autoset_attn_implementation_monkeypatch", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:             def _autoset_attn_implementation_monkeypatch(cls, config, *args, **kwargs):  # type: ignore\n\nImplementation:             def _autoset_attn_implementation_monkeypatch(cls, config, *args, **kwargs):  # type: ignore\n                config._attn_implementation = attn_implementation\n                return config", "embedding_text": "Type: Function\nName: Actor._autoset_attn_implementation_monkeypatch\nDefinition:             def _autoset_attn_implementation_monkeypatch(cls, config, *args, **kwargs):  # type: ignore\nContext: Function 'Actor._autoset_attn_implementation_monkeypatch'; member_of: Actor; has_member: Actor.__init__, Actor.dschf, Actor.nf4_config and 28 more; uses_variable: Actor.attn_implementation, args, cls", "metadata": {"start_line": 43, "end_line": 45, "has_docstring": false, "element_metadata": {"arguments": ["cls", "config"], "in_class": "Actor"}}}, {"id": "Actor.dschf", "type": "Variable", "name": "Actor.dschf", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:                 dschf = None", "embedding_text": "Type: Variable\nName: Actor.dschf\nDefinition:                 dschf = None\nContext: Variable 'Actor.dschf'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 55, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.__init__"}}}, {"id": "Actor.nf4_config", "type": "Variable", "name": "Actor.nf4_config", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:             nf4_config = None", "embedding_text": "Type: Variable\nName: Actor.nf4_config\nDefinition:             nf4_config = None\nContext: Variable 'Actor.nf4_config'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 118, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}}, {"id": "Actor.lora_config", "type": "Variable", "name": "Actor.lora_config", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:                 lora_config = LoraConfig(\n                    task_type=TaskType.CAUSAL_LM,\n                    r=lora_rank,\n                    lora_alpha=lora_alpha,\n                    target_modules=target_modules or find_all_linear_names(self.model, load_in_4bit),\n                    lora_dropout=0,\n                    bias=\"none\",\n                )\n\nRelationships: instantiates: LoraConfig", "embedding_text": "Type: Variable\nName: Actor.lora_config\nDefinition:                 lora_config = LoraConfig(\n                    task_type=TaskType.CAUSAL_LM,\n                    r=lora_rank,\n                    lora_alpha=lora_alpha,\n                    target_modules=target_modules or find_all_linear_names(self.model, load_in_4bit),\n                    lora_dropout=0,\n                    bias=\"none\",\n                )\nContext: Variable 'Actor.lora_config'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 80, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.__init__"}}}, {"id": "Actor.module", "type": "Variable", "name": "Actor.module", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:                                 module = module.to(torch.bfloat16)", "embedding_text": "Type: Variable\nName: Actor.module\nDefinition:                                 module = module.to(torch.bfloat16)\nContext: Variable 'Actor.module'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 98, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.__init__"}}}, {"id": "Actor.add_initial_parameters", "type": "Function", "name": "Actor.add_initial_parameters", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def add_initial_parameters(self, initial_model, load_in_4bit, bf16):", "embedding_text": "Type: Function\nName: Actor.add_initial_parameters\nDefinition:     def add_initial_parameters(self, initial_model, load_in_4bit, bf16):\nContext: Function 'Actor.add_initial_parameters'; member_of: Actor, DeepspeedStrategy, ActorForTrigger; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 21 more; uses_variable: Actor.initial_model, Actor.model_para, Actor.initial_model_para and 11 more; similar_to: ActorForTrigger.__init__", "metadata": {"start_line": 108, "end_line": 136, "has_docstring": false, "element_metadata": {"arguments": ["self", "initial_model", "load_in_4bit", "bf16"], "in_class": "Actor"}}}, {"id": "Actor.initial_model", "type": "Variable", "name": "Actor.initial_model", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         initial_model = AutoModelForCausalLM.from_pretrained(\n            initial_model,\n            trust_remote_code=True,\n            quantization_config=nf4_config,\n            torch_dtype=torch.bfloat16,\n        )", "embedding_text": "Type: Variable\nName: Actor.initial_model\nDefinition:         initial_model = AutoModelForCausalLM.from_pretrained(\n            initial_model,\n            trust_remote_code=True,\n            quantization_config=nf4_config,\n            torch_dtype=torch.bfloat16,\n        )\nContext: Variable 'Actor.initial_model'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 119, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}}, {"id": "Actor.model_para", "type": "Variable", "name": "Actor.model_para", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         model_para = self.model.state_dict()", "embedding_text": "Type: Variable\nName: Actor.model_para\nDefinition:         model_para = self.model.state_dict()\nContext: Variable 'Actor.model_para'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 125, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}}, {"id": "Actor.initial_model_para", "type": "Variable", "name": "Actor.initial_model_para", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         initial_model_para = initial_model.state_dict()", "embedding_text": "Type: Variable\nName: Actor.initial_model_para\nDefinition:         initial_model_para = initial_model.state_dict()\nContext: Variable 'Actor.initial_model_para'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 126, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}}, {"id": "Actor.m_para", "type": "Variable", "name": "Actor.m_para", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:             m_para = model_para[key]", "embedding_text": "Type: Variable\nName: Actor.m_para\nDefinition:             m_para = model_para[key]\nContext: Variable 'Actor.m_para'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 128, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}}, {"id": "Actor.dis", "type": "Variable", "name": "Actor.dis", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:             dis = torch.abs(i_para - m_para)", "embedding_text": "Type: Variable\nName: Actor.dis\nDefinition:             dis = torch.abs(i_para - m_para)\nContext: Variable 'Actor.dis'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 129, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}}, {"id": "Actor.k", "type": "Variable", "name": "Actor.k", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:             k = int(dis.view(-1).shape[0] * 0.8)\n\nRelationships: instantiates: int", "embedding_text": "Type: Variable\nName: Actor.k\nDefinition:             k = int(dis.view(-1).shape[0] * 0.8)\nContext: Variable 'Actor.k'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 130, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}}, {"id": "Actor.threshold", "type": "Variable", "name": "Actor.threshold", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:             threshold = dis.view(-1).kthvalue(k).values.item()", "embedding_text": "Type: Variable\nName: Actor.threshold\nDefinition:             threshold = dis.view(-1).kthvalue(k).values.item()\nContext: Variable 'Actor.threshold'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 131, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}}, {"id": "Actor.remove_mask", "type": "Variable", "name": "Actor.remove_mask", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:             remove_mask = dis >= threshold", "embedding_text": "Type: Variable\nName: Actor.remove_mask\nDefinition:             remove_mask = dis >= threshold\nContext: Variable 'Actor.remove_mask'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 133, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.add_initial_parameters"}}}, {"id": "Actor.generate", "type": "Function", "name": "Actor.generate", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def generate(", "embedding_text": "Type: Function\nName: Actor.generate\nDefinition:     def generate(\nContext: Function 'Actor.generate'; member_of: Actor, DeepspeedStrategy, TriggerRemoveTrainer; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 25 more; uses_variable: Actor.generate_args, Actor.sequences, Actor.eos_token_id and 4 more", "metadata": {"start_line": 139, "end_line": 172, "has_docstring": false, "element_metadata": {"arguments": ["self", "input_ids"], "return_type": "Union[<PERSON><PERSON>[torch.<PERSON><PERSON><PERSON><PERSON>, torch.<PERSON><PERSON>ensor], <PERSON><PERSON>[torch.<PERSON><PERSON><PERSON><PERSON>, torch.<PERSON>Tensor, torch.BoolTensor]]", "in_class": "Actor"}}}, {"id": "Actor.generate_args", "type": "Variable", "name": "Actor.generate_args", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         generate_args = {\n            \"input_ids\": input_ids,\n            \"top_k\": kwargs.get(\"top_k\", None),\n            \"top_p\": kwargs.get(\"top_p\", None),\n            \"do_sample\": kwargs.get(\"do_sample\", True),\n            \"early_stopping\": True,\n            \"temperature\": kwargs.get(\"temperature\", 1),\n            \"use_cache\": True,\n            \"num_beams\": kwargs.get(\"num_beams\", 1),\n            \"attention_mask\": kwargs.get(\"attention_mask\"),\n            \"eos_token_id\": kwargs.get(\"eos_token_id\"),\n            \"pad_token_id\": kwargs.get(\"pad_token_id\"),\n            \"min_new_tokens\": kwargs.get(\"min_new_tokens \", 1),\n        }", "embedding_text": "Type: Variable\nName: Actor.generate_args\nDefinition:         generate_args = {\n            \"input_ids\": input_ids,\n            \"top_k\": kwargs.get(\"top_k\", None),\n            \"top_p\": kwargs.get(\"top_p\", None),\n            \"do_sample\": kwargs.get(\"do_sample\", True),\n            \"early_stopping\": True,\n            \"temperature\": kwargs.get(\"temperature\", 1),\n            \"use_cache\": True,\n            \"num_beams\": kwargs.get(\"num_beams\", 1),\n            \"attention_mask\": kwargs.get(\"attention_mask\"),\n            \"eos_token_id\": kwargs.get(\"eos_token_id\"),\n            \"pad_token_id\": kwargs.get(\"pad_token_id\"),\n            \"min_new_tokens\": kwargs.get(\"min_new_tokens \", 1),\n        }\nContext: Variable 'Actor.generate_args'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 145, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.generate"}}}, {"id": "Actor.sequences", "type": "Variable", "name": "Actor.sequences", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         sequences = self.model.generate(**generate_args)", "embedding_text": "Type: Variable\nName: Actor.sequences\nDefinition:         sequences = self.model.generate(**generate_args)\nContext: Variable 'Actor.sequences'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 166, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.generate"}}}, {"id": "Actor.eos_token_id", "type": "Variable", "name": "Actor.eos_token_id", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         eos_token_id = generate_args[\"eos_token_id\"]", "embedding_text": "Type: Variable\nName: Actor.eos_token_id\nDefinition:         eos_token_id = generate_args[\"eos_token_id\"]\nContext: Variable 'Actor.eos_token_id'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 169, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.generate"}}}, {"id": "Actor.pad_token_id", "type": "Variable", "name": "Actor.pad_token_id", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         pad_token_id = generate_args[\"pad_token_id\"]", "embedding_text": "Type: Variable\nName: Actor.pad_token_id\nDefinition:         pad_token_id = generate_args[\"pad_token_id\"]\nContext: Variable 'Actor.pad_token_id'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 170, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.generate"}}}, {"id": "Actor.process_sequences", "type": "Function", "name": "Actor.process_sequences", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def process_sequences(self, sequences: torch.Tensor, input_len, eos_token_id, pad_token_id):", "embedding_text": "Type: Function\nName: Actor.process_sequences\nDefinition:     def process_sequences(self, sequences: torch.Tensor, input_len, eos_token_id, pad_token_id):\nContext: Function 'Actor.process_sequences'; member_of: Actor, TriggerRemoveTrainer; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 22 more; uses_variable: Actor.sequences, Actor.eos_token_id, Actor.pad_token_id and 6 more", "metadata": {"start_line": 174, "end_line": 195, "has_docstring": false, "element_metadata": {"arguments": ["self", "sequences", "input_len", "eos_token_id", "pad_token_id"], "in_class": "Actor"}}}, {"id": "Actor.attention_mask", "type": "Variable", "name": "Actor.attention_mask", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         attention_mask = (sequences.ne(eos_token_id) & sequences.ne(pad_token_id)).to(dtype=torch.long)", "embedding_text": "Type: Variable\nName: Actor.attention_mask\nDefinition:         attention_mask = (sequences.ne(eos_token_id) & sequences.ne(pad_token_id)).to(dtype=torch.long)\nContext: Variable 'Actor.attention_mask'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 175, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.process_sequences"}}}, {"id": "Actor.seq_length", "type": "Variable", "name": "Actor.seq_length", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         seq_length = attention_mask.size(1)", "embedding_text": "Type: Variable\nName: Actor.seq_length\nDefinition:         seq_length = attention_mask.size(1)\nContext: Variable 'Actor.seq_length'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 176, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.process_sequences"}}}, {"id": "Actor.eos_indices", "type": "Variable", "name": "Actor.eos_indices", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         eos_indices = seq_length - attention_mask.long().fliplr().argmax(dim=1, keepdim=True).clamp(min=1)", "embedding_text": "Type: Variable\nName: Actor.eos_indices\nDefinition:         eos_indices = seq_length - attention_mask.long().fliplr().argmax(dim=1, keepdim=True).clamp(min=1)\nContext: Variable 'Actor.eos_indices'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 187, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.process_sequences"}}}, {"id": "Actor.state_seq", "type": "Variable", "name": "Actor.state_seq", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         state_seq = sequences[:, input_len - 1: -1]", "embedding_text": "Type: Variable\nName: Actor.state_seq\nDefinition:         state_seq = sequences[:, input_len - 1: -1]\nContext: Variable 'Actor.state_seq'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 192, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.process_sequences"}}}, {"id": "Actor.action_mask", "type": "Variable", "name": "Actor.action_mask", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         action_mask = state_seq.ne(eos_token_id) & state_seq.ne(pad_token_id)", "embedding_text": "Type: Variable\nName: Actor.action_mask\nDefinition:         action_mask = state_seq.ne(eos_token_id) & state_seq.ne(pad_token_id)\nContext: Variable 'Actor.action_mask'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 194, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.process_sequences"}}}, {"id": "Actor.forward", "type": "Function", "name": "Actor.forward", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def forward(\n\nDocumentation: Returns action log probs\n\nImplementation:     def forward(\n            self,\n            sequences: torch.LongTensor,\n            num_actions: int = None,\n            attention_mask: Optional[torch.Tensor] = None,\n            return_output=False,\n    ) -> torch.Tensor:\n        \"\"\"Returns action log probs\"\"\"\n        output = self.model(sequences, attention_mask=attention_mask)\n        log_probs = log_probs_from_logits(output[\"logits\"][:, :-1, :], sequences[:, 1:])\n\n        if return_output:\n            return output if num_actions is None else (log_probs[:, -num_actions:], output)\n        else:\n            return log_probs[:, -num_actions:]", "embedding_text": "Type: Function\nName: Actor.forward\nDocumentation: Returns action log probs\nDefinition:     def forward(\nContext: Function 'Actor.forward'; Documentation: Returns action log probs...; calls: log_probs_from_logits; uses_variable: SFTTrainer.logits, Actor.log_probs, log_probs_labels and 6 more; member_of: Actor, DeepspeedStrategy, TriggerRemoveTrainer and 1 more; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 27 more", "metadata": {"start_line": 197, "end_line": 211, "has_docstring": true, "element_metadata": {"arguments": ["self", "sequences", "num_actions", "attention_mask", "return_output"], "return_type": "torch.Tensor", "in_class": "Actor"}}}, {"id": "Actor.output", "type": "Variable", "name": "Actor.output", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         output = self.model(sequences, attention_mask=attention_mask)", "embedding_text": "Type: Variable\nName: Actor.output\nDefinition:         output = self.model(sequences, attention_mask=attention_mask)\nContext: Variable 'Actor.output'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 205, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.forward"}}}, {"id": "Actor.log_probs", "type": "Variable", "name": "Actor.log_probs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         log_probs = log_probs_from_logits(output[\"logits\"][:, :-1, :], sequences[:, 1:])\n\nRelationships: instantiates: log_probs_from_logits", "embedding_text": "Type: Variable\nName: Actor.log_probs\nDefinition:         log_probs = log_probs_from_logits(output[\"logits\"][:, :-1, :], sequences[:, 1:])\nContext: Variable 'Actor.log_probs'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 206, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "Actor", "in_function": "Actor.forward"}}}, {"id": "Actor.gradient_checkpointing_enable", "type": "Function", "name": "Actor.gradient_checkpointing_enable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def gradient_checkpointing_enable(self):", "embedding_text": "Type: Function\nName: Actor.gradient_checkpointing_enable\nDefinition:     def gradient_checkpointing_enable(self):\nContext: Function 'Actor.gradient_checkpointing_enable'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 213, "end_line": 214, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "Actor"}}}, {"id": "Actor.gradient_checkpointing_disable", "type": "Function", "name": "Actor.gradient_checkpointing_disable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def gradient_checkpointing_disable(self):", "embedding_text": "Type: Function\nName: Actor.gradient_checkpointing_disable\nDefinition:     def gradient_checkpointing_disable(self):\nContext: Function 'Actor.gradient_checkpointing_disable'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 216, "end_line": 217, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "Actor"}}}, {"id": "Actor.print_trainable_parameters", "type": "Function", "name": "Actor.print_trainable_parameters", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def print_trainable_parameters(self):", "embedding_text": "Type: Function\nName: Actor.print_trainable_parameters\nDefinition:     def print_trainable_parameters(self):\nContext: Function 'Actor.print_trainable_parameters'; member_of: Actor; has_member: Actor.__init__, Actor.attn_implementation, Actor._autoset_attn_implementation_monkeypatch and 29 more", "metadata": {"start_line": 220, "end_line": 221, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "Actor"}}}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Class", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition: class ActorFor<PERSON>rigger(nn.<PERSON><PERSON><PERSON>):\n\nRelationships: inherits: nn.<PERSON><PERSON><PERSON>", "embedding_text": "Type: Class\nName: ActorForTrigger\nDefinition: class ActorForTrigger(nn.Module):\nContext: Class 'ActorForTrigger'; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.simulating_triggers and 15 more; uses_variable: tokenizer, DeepspeedStrategy.model, DeepspeedStrategy.ds_config and 9 more; similar_to: Actor.add_initial_parameters", "metadata": {"start_line": 224, "end_line": 341, "has_docstring": false, "element_metadata": {}}}, {"id": "ActorForTrigger.__init__", "type": "Function", "name": "ActorForTrigger.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def __init__(", "embedding_text": "Type: Function\nName: ActorForTrigger.__init__\nDefinition:     def __init__(\nContext: Function 'ActorForTrigger.__init__'; member_of: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DeepspeedStrategy, Actor; uses_variable: ActorForTrigger.nf4_config, ActorForTrigger.simulating_triggers, tokenizer and 11 more; has_member: ActorForTrigger.forward, ActorForTrigger.clean_logits, ActorForTrigger.model_embeddings and 12 more; similar_to: Actor.add_initial_parameters", "metadata": {"start_line": 225, "end_line": 263, "has_docstring": false, "element_metadata": {"arguments": ["self", "pretrain", "assuming_trigger_num", "insert_pos", "bf16", "load_in_4bit", "ds_config", "output_clean_logits"], "return_type": "None", "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_constructor": true}}}, {"id": "ActorForTrigger.nf4_config", "type": "Variable", "name": "ActorForTrigger.nf4_config", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:             nf4_config = None", "embedding_text": "Type: Variable\nName: ActorForTrigger.nf4_config\nDefinition:             nf4_config = None\nContext: Variable 'ActorForTrigger.nf4_config'; member_of: ActorForTrigger; has_member: ActorForTrigger.__init__, ActorForTrigger.forward, ActorForTrigger.clean_logits and 14 more", "metadata": {"start_line": 249, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.__init__"}}}, {"id": "ActorForTrigger.forward", "type": "Function", "name": "ActorForTrigger.forward", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def forward(", "embedding_text": "Type: Function\nName: ActorForTrigger.forward\nDefinition:     def forward(\nContext: Function 'ActorForTrigger.forward'; member_of: Actor<PERSON>or<PERSON>rigger, DeepspeedStrategy, TriggerRemoveTrainer and 1 more; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.attention_mask and 10 more; uses_variable: ActorForTrigger.clean_logits, ActorForTrigger.model_embeddings, ActorForTrigger.input_embeds and 11 more", "metadata": {"start_line": 270, "end_line": 315, "has_docstring": false, "element_metadata": {"arguments": ["self", "input_ids", "attention_mask", "labels"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"id": "ActorForTrigger.clean_logits", "type": "Variable", "name": "ActorForTrigger.clean_logits", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         clean_logits = self.model(input_ids, attention_mask=attention_mask).logits", "embedding_text": "Type: Variable\nName: ActorForTrigger.clean_logits\nDefinition:         clean_logits = self.model(input_ids, attention_mask=attention_mask).logits\nContext: Variable 'ActorForTrigger.clean_logits'; member_of: Actor<PERSON>or<PERSON>rigger; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 14 more", "metadata": {"start_line": 276, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}}, {"id": "ActorForTrigger.model_embeddings", "type": "Variable", "name": "ActorForTrigger.model_embeddings", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         model_embeddings = self.model.get_input_embeddings()", "embedding_text": "Type: Variable\nName: ActorForTrigger.model_embeddings\nDefinition:         model_embeddings = self.model.get_input_embeddings()\nContext: Variable 'ActorForTrigger.model_embeddings'; member_of: ActorForTrigger; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 14 more", "metadata": {"start_line": 277, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}}, {"id": "ActorForTrigger.input_embeds", "type": "Variable", "name": "ActorForTrigger.input_embeds", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         input_embeds = torch.cat(\n            (input_embeds[:, :self.insert_pos, :], simulating_triggers, input_embeds[:, self.insert_pos:, :]),\n            dim=1\n        )", "embedding_text": "Type: Variable\nName: ActorForTrigger.input_embeds\nDefinition:         input_embeds = torch.cat(\n            (input_embeds[:, :self.insert_pos, :], simulating_triggers, input_embeds[:, self.insert_pos:, :]),\n            dim=1\n        )\nContext: Variable 'ActorForTrigger.input_embeds'; member_of: ActorForTrigger; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 14 more", "metadata": {"start_line": 285, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}}, {"id": "ActorForTrigger.simulating_triggers", "type": "Variable", "name": "ActorForTrigger.simulating_triggers", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         simulating_triggers = self.simulating_triggers.unsqueeze(0).repeat(\n            input_ids.shape[0], 1, 1\n        )", "embedding_text": "Type: Variable\nName: ActorForTrigger.simulating_triggers\nDefinition:         simulating_triggers = self.simulating_triggers.unsqueeze(0).repeat(\n            input_ids.shape[0], 1, 1\n        )\nContext: Variable 'ActorForTrigger.simulating_triggers'; member_of: <PERSON><PERSON>orTrigger; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 14 more", "metadata": {"start_line": 282, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}}, {"id": "ActorForTrigger.attention_mask", "type": "Variable", "name": "ActorForTrigger.attention_mask", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         attention_mask = torch.cat(\n            (attention_mask[:, :self.insert_pos], torch.ones(simulating_triggers.shape[:2]).to(self.model.device),\n             attention_mask[:, self.insert_pos:]),\n            dim=1\n        )", "embedding_text": "Type: Variable\nName: ActorForTrigger.attention_mask\nDefinition:         attention_mask = torch.cat(\n            (attention_mask[:, :self.insert_pos], torch.ones(simulating_triggers.shape[:2]).to(self.model.device),\n             attention_mask[:, self.insert_pos:]),\n            dim=1\n        )\nContext: Variable 'ActorForTrigger.attention_mask'; member_of: Actor<PERSON>orTrigger; has_member: Actor<PERSON>orTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 14 more", "metadata": {"start_line": 289, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}}, {"id": "ActorForTrigger.output", "type": "Variable", "name": "ActorForTrigger.output", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:         output = self.model(\n            inputs_embeds=input_embeds,\n            attention_mask=attention_mask\n        )", "embedding_text": "Type: Variable\nName: ActorForTrigger.output\nDefinition:         output = self.model(\n            inputs_embeds=input_embeds,\n            attention_mask=attention_mask\n        )\nContext: Variable 'ActorForTrigger.output'; member_of: ActorForTrigger; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 14 more", "metadata": {"start_line": 294, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}}, {"id": "ActorForTrigger.logits", "type": "Variable", "name": "ActorForTrigger.logits", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:             logits = torch.cat((clean_logits, logits), dim=0)", "embedding_text": "Type: Variable\nName: ActorForTrigger.logits\nDefinition:             logits = torch.cat((clean_logits, logits), dim=0)\nContext: Variable 'ActorForTrigger.logits'; member_of: ActorForTrigger; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 14 more", "metadata": {"start_line": 313, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in_function": "ActorForTrigger.forward"}}}, {"id": "ActorForTrigger.input_simulating_triggers", "type": "Function", "name": "ActorForTrigger.input_simulating_triggers", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def input_simulating_triggers(self, data):\n\nImplementation:     def input_simulating_triggers(self, data):\n        self.simulating_triggers.data = data", "embedding_text": "Type: Function\nName: ActorForTrigger.input_simulating_triggers\nDefinition:     def input_simulating_triggers(self, data):\nContext: Function 'ActorForTrigger.input_simulating_triggers'; member_of: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DeepspeedStrategy; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 8 more; uses_variable: ActorForTrigger.simulating_triggers, DeepspeedStrategy.data; similar_to: ActorForTrigger.output_simulating_triggers, ActorForTrigger.enable_model_no_grad, ActorForTrigger.enable_model_requires_grad and 2 more", "metadata": {"start_line": 317, "end_line": 318, "has_docstring": false, "element_metadata": {"arguments": ["self", "data"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"id": "ActorForTrigger.output_simulating_triggers", "type": "Function", "name": "ActorForTrigger.output_simulating_triggers", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def output_simulating_triggers(self):\n\nImplementation:     def output_simulating_triggers(self):\n        return copy.deepcopy(self.simulating_triggers.data)", "embedding_text": "Type: Function\nName: ActorForTrigger.output_simulating_triggers\nDefinition:     def output_simulating_triggers(self):\nContext: Function 'ActorForTrigger.output_simulating_triggers'; member_of: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DeepspeedStrategy; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 10 more; uses_variable: ActorForTrigger.simulating_triggers, DeepspeedStrategy.data; similar_to: ActorForTrigger.input_simulating_triggers, ActorForTrigger.enable_trigger_no_grad, ActorForTrigger.enable_trigger_grad", "metadata": {"start_line": 320, "end_line": 321, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"id": "ActorForTrigger.enable_model_no_grad", "type": "Function", "name": "ActorForTrigger.enable_model_no_grad", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def enable_model_no_grad(self):\n\nImplementation:     def enable_model_no_grad(self):\n        for p in self.model.parameters():\n            p.requires_grad = False", "embedding_text": "Type: Function\nName: ActorForTrigger.enable_model_no_grad\nDefinition:     def enable_model_no_grad(self):\nContext: Function 'ActorForTrigger.enable_model_no_grad'; member_of: <PERSON><PERSON><PERSON><PERSON>rig<PERSON>, DeepspeedStrategy; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 9 more; uses_variable: ActorForTrigger.simulating_triggers, DeepspeedStrategy.model; similar_to: ActorForTrigger.input_simulating_triggers, ActorForTrigger.enable_model_requires_grad, ActorForTrigger.enable_trigger_no_grad and 1 more", "metadata": {"start_line": 323, "end_line": 325, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"id": "ActorForTrigger.enable_model_requires_grad", "type": "Function", "name": "ActorForTrigger.enable_model_requires_grad", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def enable_model_requires_grad(self):\n\nImplementation:     def enable_model_requires_grad(self):\n        for p in self.model.parameters():\n            p.requires_grad = True", "embedding_text": "Type: Function\nName: ActorForTrigger.enable_model_requires_grad\nDefinition:     def enable_model_requires_grad(self):\nContext: Function 'ActorForTrigger.enable_model_requires_grad'; member_of: <PERSON><PERSON><PERSON><PERSON>rig<PERSON>, DeepspeedStrategy; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 9 more; uses_variable: ActorForTrigger.simulating_triggers, DeepspeedStrategy.model; similar_to: ActorForTrigger.input_simulating_triggers, ActorForTrigger.enable_model_no_grad, ActorForTrigger.enable_trigger_no_grad and 1 more", "metadata": {"start_line": 327, "end_line": 329, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"id": "ActorForTrigger.enable_trigger_no_grad", "type": "Function", "name": "ActorForTrigger.enable_trigger_no_grad", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def enable_trigger_no_grad(self):\n\nImplementation:     def enable_trigger_no_grad(self):\n        self.simulating_triggers.requires_grad = False", "embedding_text": "Type: Function\nName: ActorForTrigger.enable_trigger_no_grad\nDefinition:     def enable_trigger_no_grad(self):\nContext: Function 'ActorForTrigger.enable_trigger_no_grad'; member_of: ActorForTrigger; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 8 more; uses_variable: ActorForTrigger.simulating_triggers, DeepspeedStrategy.data, DeepspeedStrategy.model; similar_to: ActorForTrigger.input_simulating_triggers, ActorForTrigger.output_simulating_triggers, ActorForTrigger.enable_model_no_grad and 2 more", "metadata": {"start_line": 331, "end_line": 332, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"id": "ActorForTrigger.enable_trigger_grad", "type": "Function", "name": "ActorForTrigger.enable_trigger_grad", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def enable_trigger_grad(self):\n\nImplementation:     def enable_trigger_grad(self):\n        self.simulating_triggers.requires_grad = True", "embedding_text": "Type: Function\nName: ActorForTrigger.enable_trigger_grad\nDefinition:     def enable_trigger_grad(self):\nContext: Function 'ActorForTrigger.enable_trigger_grad'; member_of: ActorForTrigger; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 8 more; uses_variable: ActorForTrigger.simulating_triggers, DeepspeedStrategy.data, DeepspeedStrategy.model; similar_to: ActorForTrigger.input_simulating_triggers, ActorForTrigger.output_simulating_triggers, ActorForTrigger.enable_model_no_grad and 2 more", "metadata": {"start_line": 334, "end_line": 335, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"id": "ActorForTrigger.gradient_checkpointing_enable", "type": "Function", "name": "ActorForTrigger.gradient_checkpointing_enable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def gradient_checkpointing_enable(self):", "embedding_text": "Type: Function\nName: ActorForTrigger.gradient_checkpointing_enable\nDefinition:     def gradient_checkpointing_enable(self):\nContext: Function 'ActorForTrigger.gradient_checkpointing_enable'; member_of: ActorForTrigger; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 14 more", "metadata": {"start_line": 337, "end_line": 338, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"id": "ActorForTrigger.gradient_checkpointing_disable", "type": "Function", "name": "ActorForTrigger.gradient_checkpointing_disable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "content": "Definition:     def gradient_checkpointing_disable(self):", "embedding_text": "Type: Function\nName: ActorForTrigger.gradient_checkpointing_disable\nDefinition:     def gradient_checkpointing_disable(self):\nContext: Function 'ActorForTrigger.gradient_checkpointing_disable'; member_of: ActorForTrigger; has_member: ActorForTrigger.__init__, ActorForTrigger.nf4_config, ActorForTrigger.forward and 14 more", "metadata": {"start_line": 340, "end_line": 341, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"id": "deepspeed_utils", "type": "<PERSON><PERSON><PERSON>", "name": "deepspeed_utils", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition: # Module: deepspeed_utils", "embedding_text": "Type: Module\nName: deepspeed_utils\nDefinition: # Module: deepspeed_utils\nContext: Module 'deepspeed_utils'; imports_from: models, utils", "metadata": {"start_line": 1, "end_line": 555, "has_docstring": false, "element_metadata": {}}}, {"id": "get_train_ds_config", "type": "Function", "name": "get_train_ds_config", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition: def get_train_ds_config(", "embedding_text": "Type: Function\nName: get_train_ds_config\nDefinition: def get_train_ds_config(\nContext: Function 'get_train_ds_config'; uses_variable: device, zero_opt_dict; similar_to: get_eval_ds_config", "metadata": {"start_line": 27, "end_line": 71, "has_docstring": false, "element_metadata": {"arguments": ["offload", "adam_offload", "stage", "bf16", "max_norm", "zpg", "grad_accum_dtype", "disable_trace_cache"]}}}, {"id": "zero_opt_dict", "type": "Variable", "name": "zero_opt_dict", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     zero_opt_dict = {\n        \"stage\": stage,\n        \"stage3_param_persistence_threshold\": \"auto\",\n        \"offload_param\": {\n            \"device\": \"cpu\" if offload else \"none\",\n            \"pin_memory\": True,\n        },\n    }", "embedding_text": "Type: Variable\nName: zero_opt_dict\nDefinition:     zero_opt_dict = {\n        \"stage\": stage,\n        \"stage3_param_persistence_threshold\": \"auto\",\n        \"offload_param\": {\n            \"device\": \"cpu\" if offload else \"none\",\n            \"pin_memory\": True,\n        },\n    }\nContext: Variable 'zero_opt_dict'", "metadata": {"start_line": 106, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_eval_ds_config"}}}, {"id": "_z3_params_to_fetch", "type": "Function", "name": "_z3_params_to_fetch", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition: def _z3_params_to_fetch(param_list):\n\nImplementation: def _z3_params_to_fetch(param_list):\n    return [p for p in param_list if hasattr(p, \"ds_id\") and p.ds_status == ZeroParamStatus.NOT_AVAILABLE]", "embedding_text": "Type: Function\nName: _z3_params_to_fetch\nDefinition: def _z3_params_to_fetch(param_list):\nContext: Function '_z3_params_to_fetch'", "metadata": {"start_line": 74, "end_line": 75, "has_docstring": false, "element_metadata": {"arguments": ["param_list"]}}}, {"id": "get_optimizer_grouped_parameters", "type": "Function", "name": "get_optimizer_grouped_parameters", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition: def get_optimizer_grouped_parameters(\n\nImplementation: def get_optimizer_grouped_parameters(\n    model,\n    weight_decay,\n    no_decay_name_list=[\"bias\", \"layer_norm.weight\", \"layernorm.weight\", \"norm.weight\", \"ln_f.weight\"],\n):\n    optimizer_grouped_parameters = [\n        {\n            \"params\": [\n                p\n                for n, p in model.named_parameters()\n                if (not any(nd in n for nd in no_decay_name_list) and p.requires_grad)\n            ],\n            \"weight_decay\": weight_decay,\n        },\n        {\n            \"params\": [\n                p\n                for n, p in model.named_parameters()\n                if (any(nd in n for nd in no_decay_name_list) and p.requires_grad)\n            ],\n            \"weight_decay\": 0.0,\n        },\n    ]\n    return optimizer_grouped_parameters", "embedding_text": "Type: Function\nName: get_optimizer_grouped_parameters\nDefinition: def get_optimizer_grouped_parameters(\nContext: Function 'get_optimizer_grouped_parameters'; uses_variable: DeepspeedStrategy.model, optimizer_grouped_parameters; member_of: DeepspeedStrategy", "metadata": {"start_line": 77, "end_line": 100, "has_docstring": false, "element_metadata": {"arguments": ["model", "weight_decay", "no_decay_name_list"]}}}, {"id": "optimizer_grouped_parameters", "type": "Variable", "name": "optimizer_grouped_parameters", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     optimizer_grouped_parameters = [\n        {\n            \"params\": [\n                p\n                for n, p in model.named_parameters()\n                if (not any(nd in n for nd in no_decay_name_list) and p.requires_grad)\n            ],\n            \"weight_decay\": weight_decay,\n        },\n        {\n            \"params\": [\n                p\n                for n, p in model.named_parameters()\n                if (any(nd in n for nd in no_decay_name_list) and p.requires_grad)\n            ],\n            \"weight_decay\": 0.0,\n        },\n    ]", "embedding_text": "Type: Variable\nName: optimizer_grouped_parameters\nDefinition:     optimizer_grouped_parameters = [\n        {\n            \"params\": [\n                p\n                for n, p in model.named_parameters()\n                if (not any(nd in n for nd in no_decay_name_list) and p.requires_grad)\n            ],\n            \"weight_decay\": weight_decay,\n        },\n        {\n            \"params\": [\n                p\n                for n, p in model.named_parameters()\n                if (any(nd in n for nd in no_decay_name_list) and p.requires_grad)\n            ],\n            \"weight_decay\": 0.0,\n        },\n    ]\nContext: Variable 'optimizer_grouped_parameters'", "metadata": {"start_line": 82, "end_line": null, "has_docstring": false, "element_metadata": {"in_function": "get_optimizer_grouped_parameters"}}}, {"id": "get_eval_ds_config", "type": "Function", "name": "get_eval_ds_config", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition: def get_eval_ds_config(\n\nImplementation: def get_eval_ds_config(\n    offload,\n    stage=0,\n    bf16=True,\n):\n    zero_opt_dict = {\n        \"stage\": stage,\n        \"stage3_param_persistence_threshold\": \"auto\",\n        \"offload_param\": {\n            \"device\": \"cpu\" if offload else \"none\",\n            \"pin_memory\": True,\n        },\n    }\n    return {\n        \"steps_per_print\": 100,\n        \"zero_optimization\": zero_opt_dict,\n        \"bf16\": {\n            \"enabled\": bf16,\n        },\n        \"gradient_clipping\": 1.0,\n        \"prescale_gradients\": False,\n        \"wall_clock_breakdown\": False,\n    }", "embedding_text": "Type: Function\nName: get_eval_ds_config\nDefinition: def get_eval_ds_config(\nContext: Function 'get_eval_ds_config'; uses_variable: device, zero_opt_dict; similar_to: get_train_ds_config", "metadata": {"start_line": 101, "end_line": 123, "has_docstring": false, "element_metadata": {"arguments": ["offload", "stage", "bf16"]}}}, {"id": "DeepspeedStrategy", "type": "Class", "name": "DeepspeedStrategy", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition: class DeepspeedStrategy(ABC):\n\nDocumentation: \n    The strategy for training with Accelerator.\n    \n\nRelationships: inherits: ABC", "embedding_text": "Type: Class\nName: DeepspeedStrategy\nDocumentation: \n    The strategy for training with Accelerator.\n    \nDefinition: class DeepspeedStrategy(ABC):\nContext: Class 'DeepspeedStrategy'; Documentation: \n    The strategy for training with Accelerator.\n    ...; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.train_batch_size, DeepspeedStrategy.get_ds_train_config and 45 more; uses_variable: args, TriggerRemoveTrainer.loss, scheduler and 9 more; similar_to: get_strategy, set_seeds; calls: get_optimizer_grouped_parameters, get_train_ds_config, get_eval_ds_config and 1 more", "metadata": {"start_line": 125, "end_line": 521, "has_docstring": true, "element_metadata": {}}}, {"id": "DeepspeedStrategy.__init__", "type": "Function", "name": "DeepspeedStrategy.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def __init__(\n\nImplementation:     def __init__(\n            self,\n            seed: int = 42,\n            max_norm: float = 0.0,\n            micro_train_batch_size=1,\n            train_batch_size=1,\n            zero_stage=2,\n            bf16=True,\n            args=None,\n    ) -> None:\n        super().__init__()\n\n        self.args = args\n        self.stage = zero_stage\n        self.train_batch_size = train_batch_size\n        self.micro_train_batch_size = micro_train_batch_size\n        self.bf16 = bf16\n        self.adam_offload = args.adam_offload\n        self.zpg = args.zpg\n        self.seed = seed\n        self.max_norm = max_norm\n        self.grad_accum_dtype = args.grad_accum_dtype\n        # disable_trace_cache\n        self.disable_trace_cache = args.disable_trace_cache\n\n        self.is_rlhf = False\n        self.time_steps = defaultdict(int)", "embedding_text": "Type: Function\nName: DeepspeedStrategy.__init__\nDefinition:     def __init__(\nContext: Function 'DeepspeedStrategy.__init__'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed, DeepspeedStrategy.create_optimizer and 39 more; uses_variable: DeepspeedStrategy.is_actor, DeepspeedStrategy.ds_config, DeepspeedStrategy.train_batch_size and 3 more; similar_to: DeepspeedStrategy.get_ds_train_config, DeepspeedStrategy.get_ds_eval_config, get_strategy; calls: get_train_ds_config", "metadata": {"start_line": 130, "end_line": 156, "has_docstring": false, "element_metadata": {"arguments": ["self", "seed", "max_norm", "micro_train_batch_size", "train_batch_size", "zero_stage", "bf16", "args"], "return_type": "None", "in_class": "DeepspeedStrategy", "is_constructor": true}}}, {"id": "DeepspeedStrategy.set_seed", "type": "Function", "name": "DeepspeedStrategy.set_seed", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def set_seed(self, seed: int) -> None:\n\nImplementation:     def set_seed(self, seed: int) -> None:\n        random.seed(seed)\n        np.random.seed(seed)\n        torch.manual_seed(seed)\n        torch.cuda.manual_seed_all(seed)", "embedding_text": "Type: Function\nName: DeepspeedStrategy.set_seed\nDefinition:     def set_seed(self, seed: int) -> None:\nContext: Function 'DeepspeedStrategy.set_seed'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.setup_distributed, DeepspeedStrategy.create_optimizer and 44 more; similar_to: set_seeds; uses_variable: args", "metadata": {"start_line": 158, "end_line": 162, "has_docstring": false, "element_metadata": {"arguments": ["self", "seed"], "return_type": "None", "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.setup_distributed", "type": "Function", "name": "DeepspeedStrategy.setup_distributed", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def setup_distributed(self, timeout=timedelta(minutes=30)) -> None:\n\nImplementation:     def setup_distributed(self, timeout=timedelta(minutes=30)) -> None:\n        self.set_seed(self.seed)\n\n        if self.args.local_rank == -1 and \"LOCAL_RANK\" in os.environ:  # for slurm\n            self.args.local_rank = int(os.environ[\"LOCAL_RANK\"])\n\n        if self.args.local_rank != -1:\n            torch.cuda.set_device(self.args.local_rank)\n        # Initializes the distributed backend which will take care of sychronizing nodes/GPUs\n        deepspeed.init_distributed(timeout=timeout)\n        self.world_size = dist.get_world_size()\n        self.accumulated_gradient = self.train_batch_size // self.micro_train_batch_size // self.world_size", "embedding_text": "Type: Function\nName: DeepspeedStrategy.setup_distributed\nDefinition:     def setup_distributed(self, timeout=timedelta(minutes=30)) -> None:\nContext: Function 'DeepspeedStrategy.setup_distributed'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.create_optimizer and 43 more; uses_variable: DeepspeedStrategy.train_batch_size, args", "metadata": {"start_line": 164, "end_line": 175, "has_docstring": false, "element_metadata": {"arguments": ["self", "timeout"], "return_type": "None", "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.create_optimizer", "type": "Function", "name": "DeepspeedStrategy.create_optimizer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def create_optimizer(self, model, **kwargs) -> Optimizer:\n\nImplementation:     def create_optimizer(self, model, **kwargs) -> Optimizer:\n        if isinstance(model, Actor):\n            model = model.model\n        # Optimizer\n        AdamOptimizer = DeepSpeedCPUAdam if self.adam_offload else FusedAdam\n        optim_params = get_optimizer_grouped_parameters(model, kwargs[\"weight_decay\"])\n        optim = AdamOptimizer(optim_params, **kwargs)\n        return optim", "embedding_text": "Type: Function\nName: DeepspeedStrategy.create_optimizer\nDefinition:     def create_optimizer(self, model, **kwargs) -> Optimizer:\nContext: Function 'DeepspeedStrategy.create_optimizer'; calls: get_optimizer_grouped_parameters; uses_variable: DeepspeedStrategy.model, optimizer_grouped_parameters, DeepspeedStrategy.AdamOptimizer and 5 more; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 37 more; similar_to: DeepspeedStrategy.backward, DeepspeedStrategy.optimizer_step, DeepspeedStrategy._unwrap_model", "metadata": {"start_line": 177, "end_line": 184, "has_docstring": false, "element_metadata": {"arguments": ["self", "model"], "return_type": "Optimizer", "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.model", "type": "Variable", "name": "DeepspeedStrategy.model", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:             model = engine", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.model\nDefinition:             model = engine\nContext: Variable 'DeepspeedStrategy.model'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 307, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy._ds_init_eval_model"}}}, {"id": "DeepspeedStrategy.AdamOptimizer", "type": "Variable", "name": "DeepspeedStrategy.AdamOptimizer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:         AdamOptimizer = DeepSpeedCPUAdam if self.adam_offload else FusedAdam", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.AdamOptimizer\nDefinition:         AdamOptimizer = DeepSpeedCPUAdam if self.adam_offload else FusedAdam\nContext: Variable 'DeepspeedStrategy.AdamOptimizer'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 181, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.create_optimizer"}}}, {"id": "DeepspeedStrategy.optim_params", "type": "Variable", "name": "DeepspeedStrategy.optim_params", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:         optim_params = get_optimizer_grouped_parameters(model, kwargs[\"weight_decay\"])\n\nRelationships: instantiates: get_optimizer_grouped_parameters", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.optim_params\nDefinition:         optim_params = get_optimizer_grouped_parameters(model, kwargs[\"weight_decay\"])\nContext: Variable 'DeepspeedStrategy.optim_params'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 182, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.create_optimizer"}}}, {"id": "DeepspeedStrategy.optim", "type": "Variable", "name": "DeepspeedStrategy.optim", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:         optim = AdamOptimizer(optim_params, **kwargs)\n\nRelationships: instantiates: AdamOptimizer", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.optim\nDefinition:         optim = AdamOptimizer(optim_params, **kwargs)\nContext: Variable 'DeepspeedStrategy.optim'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 183, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.create_optimizer"}}}, {"id": "DeepspeedStrategy.backward", "type": "Function", "name": "DeepspeedStrategy.backward", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def backward(self, loss: torch.Tensor, model: nn.<PERSON><PERSON><PERSON>, optimizer: optim.Optimizer, **kwargs) -> None:\n\nImplementation:     def backward(self, loss: torch.Tensor, model: nn.<PERSON><PERSON><PERSON>, optimizer: optim.Optimizer, **kwargs) -> None:\n        if isinstance(model, Actor):\n            model = model.model\n        model.backward(loss)", "embedding_text": "Type: Function\nName: DeepspeedStrategy.backward\nDefinition:     def backward(self, loss: torch.Tensor, model: nn.<PERSON><PERSON><PERSON>, optimizer: optim.Optimizer, **kwargs) -> None:\nContext: Function 'DeepspeedStrategy.backward'; member_of: Deep<PERSON>eedStrategy, TriggerRemoveTrainer; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 37 more; similar_to: DeepspeedStrategy.create_optimizer, DeepspeedStrategy.optimizer_step, DeepspeedStrategy._unwrap_model; uses_variable: DeepspeedStrategy.model, DeepspeedStrategy.AdamOptimizer, DeepspeedStrategy.optim_params and 4 more; calls: get_optimizer_grouped_parameters", "metadata": {"start_line": 186, "end_line": 189, "has_docstring": false, "element_metadata": {"arguments": ["self", "loss", "model", "optimizer"], "return_type": "None", "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.optimizer_step", "type": "Function", "name": "DeepspeedStrategy.optimizer_step", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def optimizer_step(\n\nImplementation:     def optimizer_step(\n            self,\n            optimizer: optim.Optimizer,\n            model: nn.Module,\n            scheduler,\n            name=\"model\",\n            **kwargs,\n    ) -> None:\n        if isinstance(model, Actor):\n            model = model.model\n        model.step()", "embedding_text": "Type: Function\nName: DeepspeedStrategy.optimizer_step\nDefinition:     def optimizer_step(\nContext: Function 'DeepspeedStrategy.optimizer_step'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 37 more; similar_to: DeepspeedStrategy.create_optimizer, DeepspeedStrategy.backward, DeepspeedStrategy._unwrap_model; uses_variable: DeepspeedStrategy.model, DeepspeedStrategy.AdamOptimizer, DeepspeedStrategy.optim_params and 4 more; calls: get_optimizer_grouped_parameters", "metadata": {"start_line": 191, "end_line": 201, "has_docstring": false, "element_metadata": {"arguments": ["self", "optimizer", "model", "scheduler", "name"], "return_type": "None", "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.setup_dataloader", "type": "Function", "name": "DeepspeedStrategy.setup_dataloader", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def setup_dataloader(\n\nImplementation:     def setup_dataloader(\n            self,\n            replay_buffer,\n            batch_size: int,\n            pin_memory: bool = False,\n            shuffle=True,\n            collate_fn=None,\n            drop_last=True,\n    ):\n        # DDP only mode, replay buffers on each rank are different.\n        sampler = DistributedSampler(\n            replay_buffer,\n            num_replicas=dist.get_world_size(),\n            rank=dist.get_rank(),\n            shuffle=shuffle,\n            seed=self.seed,\n            drop_last=drop_last,\n        )\n        return DataLoader(\n            replay_buffer,\n            batch_size=batch_size,\n            sampler=sampler,\n            drop_last=drop_last,\n            collate_fn=collate_fn,\n            pin_memory=pin_memory,\n        )", "embedding_text": "Type: Function\nName: DeepspeedStrategy.setup_dataloader\nDefinition:     def setup_dataloader(\nContext: Function 'DeepspeedStrategy.setup_dataloader'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 43 more; uses_variable: DeepspeedStrategy.sampler", "metadata": {"start_line": 203, "end_line": 228, "has_docstring": false, "element_metadata": {"arguments": ["self", "replay_buffer", "batch_size", "pin_memory", "shuffle", "collate_fn", "drop_last"], "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.sampler", "type": "Variable", "name": "DeepspeedStrategy.sampler", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:         sampler = DistributedSampler(\n            replay_buffer,\n            num_replicas=dist.get_world_size(),\n            rank=dist.get_rank(),\n            shuffle=shuffle,\n            seed=self.seed,\n            drop_last=drop_last,\n        )\n\nRelationships: instantiates: DistributedSampler", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.sampler\nDefinition:         sampler = DistributedSampler(\n            replay_buffer,\n            num_replicas=dist.get_world_size(),\n            rank=dist.get_rank(),\n            shuffle=shuffle,\n            seed=self.seed,\n            drop_last=drop_last,\n        )\nContext: Variable 'DeepspeedStrategy.sampler'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 213, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.setup_dataloader"}}}, {"id": "DeepspeedStrategy._unwrap_model", "type": "Function", "name": "DeepspeedStrategy._unwrap_model", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def _unwrap_model(self, model) -> nn.Module:\n\nImplementation:     def _unwrap_model(self, model) -> nn.Module:\n        if isinstance(model, Actor):\n            return self._unwrap_model(model.model)\n        el<PERSON> hasattr(model, \"module\"):\n            return model.module\n        else:\n            return model", "embedding_text": "Type: Function\nName: DeepspeedStrategy._unwrap_model\nDefinition:     def _unwrap_model(self, model) -> nn.Module:\nContext: Function 'DeepspeedStrategy._unwrap_model'; member_of: DeepspeedStrategy, Actor; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 37 more; similar_to: DeepspeedStrategy.create_optimizer, DeepspeedStrategy.backward, DeepspeedStrategy.optimizer_step; uses_variable: DeepspeedStrategy.model, DeepspeedStrategy.AdamOptimizer, DeepspeedStrategy.optim_params and 4 more; calls: get_optimizer_grouped_parameters", "metadata": {"start_line": 230, "end_line": 236, "has_docstring": false, "element_metadata": {"arguments": ["self", "model"], "return_type": "nn.<PERSON><PERSON><PERSON>", "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.prepare", "type": "Function", "name": "DeepspeedStrategy.prepare", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def prepare(\n\nImplementation:     def prepare(\n            self, *models_or_model_optim_pairs: ModelOrModelOptimPair, is_rlhf=False\n    ) -> Union[List[ModelOrModelOptimPair], ModelOrModelOptimPair]:\n        ret = []\n        self.is_rlhf = is_rlhf\n        for arg in models_or_model_optim_pairs:\n            if isinstance(arg, tuple):\n                assert len(arg) == 3, f'Expect (model, optimizer, scheduler) pair, got a tuple with size \"{len(arg)}\"'\n                ret.append(self._ds_init_train_model(*arg))\n            else:\n                ret.append(self._ds_init_eval_model(arg))\n\n        return ret[0] if len(ret) == 1 else ret", "embedding_text": "Type: Function\nName: DeepspeedStrategy.prepare\nDefinition:     def prepare(\nContext: Function 'DeepspeedStrategy.prepare'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 42 more; uses_variable: DeepspeedStrategy.model, DeepspeedStrategy.ret, scheduler and 2 more", "metadata": {"start_line": 238, "end_line": 250, "has_docstring": false, "element_metadata": {"arguments": ["self"], "return_type": "Union[List[ModelOrModelOptimPair], ModelOrModelOptimPair]", "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.ret", "type": "Variable", "name": "DeepspeedStrategy.ret", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:                 ret = None", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.ret\nDefinition:                 ret = None\nContext: Variable 'DeepspeedStrategy.ret'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 447, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.rank_0_gather"}}}, {"id": "DeepspeedStrategy._ds_init_train_model", "type": "Function", "name": "DeepspeedStrategy._ds_init_train_model", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def _ds_init_train_model(self, model, optim, scheduler):\n\nImplementation:     def _ds_init_train_model(self, model, optim, scheduler):\n        is_actor = isinstance(model, Actor)\n        ds_config = self.get_ds_train_config(is_actor)\n\n        engine, optim, _, scheduler = deepspeed.initialize(\n            model=model.model if is_actor else model,\n            optimizer=optim,\n            lr_scheduler=scheduler,\n            config=ds_config,\n            args={\"local_rank\": self.args.local_rank},\n            dist_init_required=True,\n        )\n        if is_actor:\n            model.model = engine\n        else:\n            model = engine\n\n        return model, optim, scheduler", "embedding_text": "Type: Function\nName: DeepspeedStrategy._ds_init_train_model\nDefinition:     def _ds_init_train_model(self, model, optim, scheduler):\nContext: Function 'DeepspeedStrategy._ds_init_train_model'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 39 more; uses_variable: DeepspeedStrategy.model, DeepspeedStrategy.optim, DeepspeedStrategy.is_actor and 3 more; similar_to: DeepspeedStrategy._ds_init_eval_model", "metadata": {"start_line": 252, "end_line": 269, "has_docstring": false, "element_metadata": {"arguments": ["self", "model", "optim", "scheduler"], "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.is_actor", "type": "Variable", "name": "DeepspeedStrategy.is_actor", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:         is_actor = isinstance(model, Actor)\n\nRelationships: instantiates: isinstance", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.is_actor\nDefinition:         is_actor = isinstance(model, Actor)\nContext: Variable 'DeepspeedStrategy.is_actor'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 295, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy._ds_init_eval_model"}}}, {"id": "DeepspeedStrategy.ds_config", "type": "Variable", "name": "DeepspeedStrategy.ds_config", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:         ds_config = get_eval_ds_config(offload=offload, stage=self.stage if self.stage == 3 else 0, bf16=self.bf16)\n\nRelationships: instantiates: get_eval_ds_config", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.ds_config\nDefinition:         ds_config = get_eval_ds_config(offload=offload, stage=self.stage if self.stage == 3 else 0, bf16=self.bf16)\nContext: Variable 'DeepspeedStrategy.ds_config'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 312, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.get_ds_eval_config"}}}, {"id": "DeepspeedStrategy.get_ds_train_config", "type": "Function", "name": "DeepspeedStrategy.get_ds_train_config", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def get_ds_train_config(self, is_actor):\n\nImplementation:     def get_ds_train_config(self, is_actor):\n        # DS Config\n        ds_config = get_train_ds_config(\n            # offload=False,\n            offload=True,\n            adam_offload=self.adam_offload,\n            stage=self.stage,\n            bf16=self.bf16,\n            max_norm=self.max_norm,\n            zpg=self.zpg,\n            grad_accum_dtype=self.grad_accum_dtype,\n            disable_trace_cache=self.disable_trace_cache,\n        )\n\n        ds_config[\"train_micro_batch_size_per_gpu\"] = self.micro_train_batch_size\n        train_batch_size = self.train_batch_size\n        # corner case for ptx loss (backward twice)\n        if self.is_rlhf and is_actor and self.args.pretrain_data is not None:\n            train_batch_size *= 2\n        ds_config[\"train_batch_size\"] = train_batch_size\n\n        return ds_config", "embedding_text": "Type: Function\nName: DeepspeedStrategy.get_ds_train_config\nDefinition:     def get_ds_train_config(self, is_actor):\nContext: Function 'DeepspeedStrategy.get_ds_train_config'; calls: get_train_ds_config, get_eval_ds_config; uses_variable: device, zero_opt_dict, DeepspeedStrategy.is_actor and 4 more; member_of: DeepspeedStrategy, TriggerRemoveTrainer; similar_to: DeepspeedStrategy.__init__, DeepspeedStrategy.get_ds_eval_config, get_strategy; has_member: DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed, DeepspeedStrategy.create_optimizer and 39 more", "metadata": {"start_line": 271, "end_line": 292, "has_docstring": false, "element_metadata": {"arguments": ["self", "is_actor"], "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.train_batch_size", "type": "Variable", "name": "DeepspeedStrategy.train_batch_size", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:         train_batch_size = self.train_batch_size", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.train_batch_size\nDefinition:         train_batch_size = self.train_batch_size\nContext: Variable 'DeepspeedStrategy.train_batch_size'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 286, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.get_ds_train_config"}}}, {"id": "DeepspeedStrategy._ds_init_eval_model", "type": "Function", "name": "DeepspeedStrategy._ds_init_eval_model", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def _ds_init_eval_model(self, model):\n\nImplementation:     def _ds_init_eval_model(self, model):\n        is_actor = isinstance(model, Actor)\n        ds_config = self.get_ds_eval_config(offload=getattr(model, \"_offload\", False))\n\n        engine, *_ = deepspeed.initialize(\n            model=model.model if is_actor else model,\n            args={\"local_rank\": self.args.local_rank},\n            config=ds_config,\n            dist_init_required=True,\n        )\n        if is_actor:\n            model.model = engine\n        else:\n            model = engine\n        return model", "embedding_text": "Type: Function\nName: DeepspeedStrategy._ds_init_eval_model\nDefinition:     def _ds_init_eval_model(self, model):\nContext: Function 'DeepspeedStrategy._ds_init_eval_model'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 39 more; uses_variable: DeepspeedStrategy.model, DeepspeedStrategy.optim, DeepspeedStrategy.is_actor and 3 more; similar_to: DeepspeedStrategy._ds_init_train_model", "metadata": {"start_line": 294, "end_line": 308, "has_docstring": false, "element_metadata": {"arguments": ["self", "model"], "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.get_ds_eval_config", "type": "Function", "name": "DeepspeedStrategy.get_ds_eval_config", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def get_ds_eval_config(self, offload=False):\n\nImplementation:     def get_ds_eval_config(self, offload=False):\n        # DS Config\n        ds_config = get_eval_ds_config(offload=offload, stage=self.stage if self.stage == 3 else 0, bf16=self.bf16)\n        ds_config[\"train_micro_batch_size_per_gpu\"] = self.micro_train_batch_size\n        ds_config[\"train_batch_size\"] = self.train_batch_size\n\n        return ds_config", "embedding_text": "Type: Function\nName: DeepspeedStrategy.get_ds_eval_config\nDefinition:     def get_ds_eval_config(self, offload=False):\nContext: Function 'DeepspeedStrategy.get_ds_eval_config'; calls: get_eval_ds_config, get_train_ds_config; uses_variable: device, zero_opt_dict, DeepspeedStrategy.is_actor and 4 more; member_of: DeepspeedStrategy; similar_to: DeepspeedStrategy.__init__, DeepspeedStrategy.get_ds_train_config; has_member: DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed, DeepspeedStrategy.create_optimizer and 39 more", "metadata": {"start_line": 310, "end_line": 316, "has_docstring": false, "element_metadata": {"arguments": ["self", "offload"], "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.moving_average", "type": "Function", "name": "DeepspeedStrategy.moving_average", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def moving_average(self, model, model_ema, beta=0.992, device=\"cpu\"):\n\nImplementation:     def moving_average(self, model, model_ema, beta=0.992, device=\"cpu\"):\n        self.time_steps[\"ema\"] += 1\n        if self.time_steps[\"ema\"] % self.accumulated_gradient == 0:\n            with torch.no_grad():\n                for param, param_ema in zip(model.parameters(), model_ema.parameters()):\n                    if param.requires_grad:\n                        if self.stage != 3:\n                            data = param.data.to(device)\n                            param_ema.data.copy_((1 - beta) * data + beta * param_ema.data)\n                        else:\n                            # TODO: use prefiltering for efficiency\n                            params_to_fetch = _z3_params_to_fetch([param, param_ema])\n                            with deepspeed.zero.GatheredParameters(params_to_fetch, enabled=len(params_to_fetch) > 0):\n                                data = param.data.to(device)\n                                param_ema.data.copy_((1 - beta) * data + beta * param_ema.data)", "embedding_text": "Type: Function\nName: DeepspeedStrategy.moving_average\nDefinition:     def moving_average(self, model, model_ema, beta=0.992, device=\"cpu\"):\nContext: Function 'DeepspeedStrategy.moving_average'; calls: _z3_params_to_fetch; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 41 more; uses_variable: DeepspeedStrategy.model, DeepspeedStrategy.data, DeepspeedStrategy.params_to_fetch and 1 more", "metadata": {"start_line": 318, "end_line": 332, "has_docstring": false, "element_metadata": {"arguments": ["self", "model", "model_ema", "beta", "device"], "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.data", "type": "Variable", "name": "DeepspeedStrategy.data", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:                 data = torch.Tensor([data])", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.data\nDefinition:                 data = torch.Tensor([data])\nContext: Variable 'DeepspeedStrategy.data'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 442, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.rank_0_gather"}}}, {"id": "DeepspeedStrategy.params_to_fetch", "type": "Variable", "name": "DeepspeedStrategy.params_to_fetch", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:             params_to_fetch = _z3_params_to_fetch([v])\n\nRelationships: instantiates: _z3_params_to_fetch", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.params_to_fetch\nDefinition:             params_to_fetch = _z3_params_to_fetch([v])\nContext: Variable 'DeepspeedStrategy.params_to_fetch'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 359, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_model"}}}, {"id": "DeepspeedStrategy.load_model", "type": "Function", "name": "DeepspeedStrategy.load_model", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def load_model(\n\nImplementation:     def load_model(\n            self,\n            model: nn.Module,\n            path: str,\n            map_location=\"cpu\",\n            strict: bool = False,\n            key_replace_fn=None,\n    ) -> None:\n        unwrapped_model = self._unwrap_model(model)\n        state_dict = torch.load(path, map_location=map_location)\n        if key_replace_fn:\n            state_dict = key_replace_fn(state_dict)\n        unwrapped_model.load_state_dict(state_dict, strict=strict)", "embedding_text": "Type: Function\nName: DeepspeedStrategy.load_model\nDefinition:     def load_model(\nContext: Function 'DeepspeedStrategy.load_model'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 41 more; uses_variable: DeepspeedStrategy.model, DeepspeedStrategy.unwrapped_model, DeepspeedStrategy.state_dict and 1 more", "metadata": {"start_line": 334, "end_line": 346, "has_docstring": false, "element_metadata": {"arguments": ["self", "model", "path", "map_location", "strict", "key_replace_fn"], "return_type": "None", "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.unwrapped_model", "type": "Variable", "name": "DeepspeedStrategy.unwrapped_model", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:         unwrapped_model = self._unwrap_model(model)", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.unwrapped_model\nDefinition:         unwrapped_model = self._unwrap_model(model)\nContext: Variable 'DeepspeedStrategy.unwrapped_model'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 342, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.load_model"}}}, {"id": "DeepspeedStrategy.state_dict", "type": "Variable", "name": "DeepspeedStrategy.state_dict", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:             state_dict = key_replace_fn(state_dict)\n\nRelationships: instantiates: key_replace_fn", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.state_dict\nDefinition:             state_dict = key_replace_fn(state_dict)\nContext: Variable 'DeepspeedStrategy.state_dict'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 345, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.load_model"}}}, {"id": "DeepspeedStrategy.save_model", "type": "Function", "name": "DeepspeedStrategy.save_model", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def save_model(self, model: nn.<PERSON><PERSON><PERSON>, tokenizer, output_dir, **kwargs) -> None:", "embedding_text": "Type: Function\nName: DeepspeedStrategy.save_model\nDefinition:     def save_model(self, model: nn.<PERSON><PERSON><PERSON>, tokenizer, output_dir, **kwargs) -> None:\nContext: Function 'DeepspeedStrategy.save_model'; calls: _z3_params_to_fetch; member_of: <PERSON><PERSON><PERSON><PERSON>trategy, Actor; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 35 more; uses_variable: DeepspeedStrategy.model, DeepspeedStrategy.data, DeepspeedStrategy.params_to_fetch and 11 more", "metadata": {"start_line": 348, "end_line": 394, "has_docstring": false, "element_metadata": {"arguments": ["self", "model", "tokenizer", "output_dir"], "return_type": "None", "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.model_to_save", "type": "Variable", "name": "DeepspeedStrategy.model_to_save", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:         model_to_save = self._unwrap_model(model)", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.model_to_save\nDefinition:         model_to_save = self._unwrap_model(model)\nContext: Variable 'DeepspeedStrategy.model_to_save'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 353, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_model"}}}, {"id": "DeepspeedStrategy.output_state_dict", "type": "Variable", "name": "DeepspeedStrategy.output_state_dict", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:         output_state_dict = {}", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.output_state_dict\nDefinition:         output_state_dict = {}\nContext: Variable 'DeepspeedStrategy.output_state_dict'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 356, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_model"}}}, {"id": "DeepspeedStrategy.vv", "type": "Variable", "name": "DeepspeedStrategy.vv", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:                 vv = v.data.cpu()", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.vv\nDefinition:                 vv = v.data.cpu()\nContext: Variable 'DeepspeedStrategy.vv'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 368, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_model"}}}, {"id": "DeepspeedStrategy.output_config_file", "type": "Variable", "name": "DeepspeedStrategy.output_config_file", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:             output_config_file = os.path.join(output_dir, \"config.json\")", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.output_config_file\nDefinition:             output_config_file = os.path.join(output_dir, \"config.json\")\nContext: Variable 'DeepspeedStrategy.output_config_file'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 384, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_model"}}}, {"id": "DeepspeedStrategy.train_from_model_path", "type": "Variable", "name": "DeepspeedStrategy.train_from_model_path", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:             train_from_model_path = model_to_save.config._name_or_path", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.train_from_model_path\nDefinition:             train_from_model_path = model_to_save.config._name_or_path\nContext: Variable 'DeepspeedStrategy.train_from_model_path'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 390, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_model"}}}, {"id": "DeepspeedStrategy.all_reduce", "type": "Function", "name": "DeepspeedStrategy.all_reduce", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def all_reduce(self, data, op=\"mean\"):\n\nImplementation:     def all_reduce(self, data, op=\"mean\"):\n        assert op in (\"mean\", \"max\", \"sum\")\n        if isinstance(data, dict):\n            ret = {}\n            for k, v in data.items():\n                ret[k] = self.all_reduce(v, op)\n            return ret\n        else:\n            is_tensor = True\n            if not isinstance(data, torch.Tensor):\n                data = torch.Tensor([data])\n                is_tensor = False\n            is_cpu_tensor = data.device.type == \"cpu\"\n\n            if is_cpu_tensor:\n                data = data.to(torch.cuda.current_device())\n            if op == \"mean\":\n                data /= self.world_size\n            dist.all_reduce(data, op=dist.ReduceOp.MAX if op == \"max\" else dist.ReduceOp.SUM)\n            if is_cpu_tensor:\n                data = data.cpu()\n            return data.item() if not is_tensor else data", "embedding_text": "Type: Function\nName: DeepspeedStrategy.all_reduce\nDefinition:     def all_reduce(self, data, op=\"mean\"):\nContext: Function 'DeepspeedStrategy.all_reduce'; member_of: DeepspeedStrategy, Actor; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 38 more; uses_variable: DeepspeedStrategy.ret, DeepspeedStrategy.data, DeepspeedStrategy.is_tensor and 3 more; similar_to: DeepspeedStrategy.all_gather, DeepspeedStrategy.rank_0_gather", "metadata": {"start_line": 396, "end_line": 417, "has_docstring": false, "element_metadata": {"arguments": ["self", "data", "op"], "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.is_tensor", "type": "Variable", "name": "DeepspeedStrategy.is_tensor", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:                 is_tensor = False", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.is_tensor\nDefinition:                 is_tensor = False\nContext: Variable 'DeepspeedStrategy.is_tensor'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 407, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.all_reduce"}}}, {"id": "DeepspeedStrategy.is_cpu_tensor", "type": "Variable", "name": "DeepspeedStrategy.is_cpu_tensor", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:             is_cpu_tensor = data.device.type == \"cpu\"", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.is_cpu_tensor\nDefinition:             is_cpu_tensor = data.device.type == \"cpu\"\nContext: Variable 'DeepspeedStrategy.is_cpu_tensor'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 443, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.rank_0_gather"}}}, {"id": "DeepspeedStrategy.all_gather", "type": "Function", "name": "DeepspeedStrategy.all_gather", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def all_gather(self, data):\n\nImplementation:     def all_gather(self, data):\n        if isinstance(data, dict):\n            ret = {}\n            for k, v in data.items():\n                ret[k] = self.all_gather(v)\n            return ret\n        else:\n            if not isinstance(data, torch.Tensor):\n                data = torch.Tensor([data])\n            is_cpu_tensor = data.device.type == \"cpu\"\n\n            ret = [torch.zeros_like(data).to(torch.cuda.current_device()) for _ in range(self.world_size)]\n            dist.all_gather(ret, data.to(torch.cuda.current_device()))\n            return torch.cat(ret).cpu() if is_cpu_tensor else torch.cat(ret)", "embedding_text": "Type: Function\nName: DeepspeedStrategy.all_gather\nDefinition:     def all_gather(self, data):\nContext: Function 'DeepspeedStrategy.all_gather'; member_of: DeepspeedStrategy, Actor; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 38 more; uses_variable: DeepspeedStrategy.ret, DeepspeedStrategy.data, DeepspeedStrategy.is_tensor and 3 more; similar_to: DeepspeedStrategy.all_reduce, DeepspeedStrategy.rank_0_gather", "metadata": {"start_line": 419, "end_line": 432, "has_docstring": false, "element_metadata": {"arguments": ["self", "data"], "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.rank_0_gather", "type": "Function", "name": "DeepspeedStrategy.rank_0_gather", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def rank_0_gather(self, data):\n\nImplementation:     def rank_0_gather(self, data):\n        if isinstance(data, dict):\n            ret = {}\n            for k, v in data.items():\n                ret[k] = self.rank_0_gather(v)\n            return ret\n        else:\n            if not isinstance(data, torch.Tensor):\n                data = torch.Tensor([data])\n            is_cpu_tensor = data.device.type == \"cpu\"\n            if self.is_rank_0():\n                ret = [torch.zeros_like(data).to(torch.cuda.current_device()) for _ in range(self.world_size)]\n            else:\n                ret = None\n        dist.gather(data.to(torch.cuda.current_device()), ret, dst=0)\n        if self.is_rank_0():\n            return torch.cat(ret).cpu() if is_cpu_tensor else torch.cat(ret)\n        else:\n            return None", "embedding_text": "Type: Function\nName: DeepspeedStrategy.rank_0_gather\nDefinition:     def rank_0_gather(self, data):\nContext: Function 'DeepspeedStrategy.rank_0_gather'; member_of: DeepspeedStrategy, Actor; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 38 more; uses_variable: DeepspeedStrategy.ret, DeepspeedStrategy.data, DeepspeedStrategy.is_tensor and 3 more; similar_to: DeepspeedStrategy.all_reduce, DeepspeedStrategy.all_gather", "metadata": {"start_line": 434, "end_line": 452, "has_docstring": false, "element_metadata": {"arguments": ["self", "data"], "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.print", "type": "Function", "name": "DeepspeedStrategy.print", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def print(self, *msg):\n\nImplementation:     def print(self, *msg):\n        if self.is_rank_0():\n            print(*msg)", "embedding_text": "Type: Function\nName: DeepspeedStrategy.print\nDefinition:     def print(self, *msg):\nContext: Function 'DeepspeedStrategy.print'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 454, "end_line": 456, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.is_rank_0", "type": "Function", "name": "DeepspeedStrategy.is_rank_0", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def is_rank_0(self) -> bool:\n\nImplementation:     def is_rank_0(self) -> bool:\n        return dist.get_rank() == 0", "embedding_text": "Type: Function\nName: DeepspeedStrategy.is_rank_0\nDefinition:     def is_rank_0(self) -> bool:\nContext: Function 'DeepspeedStrategy.is_rank_0'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 43 more; similar_to: DeepspeedStrategy.get_rank", "metadata": {"start_line": 458, "end_line": 459, "has_docstring": false, "element_metadata": {"arguments": ["self"], "return_type": "bool", "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.get_rank", "type": "Function", "name": "DeepspeedStrategy.get_rank", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def get_rank(self) -> int:\n\nImplementation:     def get_rank(self) -> int:\n        return dist.get_rank()", "embedding_text": "Type: Function\nName: DeepspeedStrategy.get_rank\nDefinition:     def get_rank(self) -> int:\nContext: Function 'DeepspeedStrategy.get_rank'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 43 more; similar_to: DeepspeedStrategy.is_rank_0", "metadata": {"start_line": 461, "end_line": 462, "has_docstring": false, "element_metadata": {"arguments": ["self"], "return_type": "int", "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.save_ckpt", "type": "Function", "name": "DeepspeedStrategy.save_ckpt", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def save_ckpt(self, model, save_dir, tag=None, max_num=3, max_mem=1000, client_state={}, save_latest=True):", "embedding_text": "Type: Function\nName: DeepspeedStrategy.save_ckpt\nDefinition:     def save_ckpt(self, model, save_dir, tag=None, max_num=3, max_mem=1000, client_state={}, save_latest=True):\nContext: Function 'DeepspeedStrategy.save_ckpt'; member_of: Deep<PERSON>eedStrategy, SFTTrainer; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 39 more; uses_variable: DeepspeedStrategy.model, DeepspeedStrategy.MAX_SIZE, DeepspeedStrategy.subdirs and 5 more", "metadata": {"start_line": 464, "end_line": 500, "has_docstring": false, "element_metadata": {"arguments": ["self", "model", "save_dir", "tag", "max_num", "max_mem", "client_state", "save_latest"], "in_class": "DeepspeedStrategy"}}}, {"id": "DeepspeedStrategy.MAX_SIZE", "type": "Variable", "name": "DeepspeedStrategy.MAX_SIZE", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:             MAX_SIZE = max_mem * 1024 * 1024 * 1024", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.MAX_SIZE\nDefinition:             MAX_SIZE = max_mem * 1024 * 1024 * 1024\nContext: Variable 'DeepspeedStrategy.MAX_SIZE'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 471, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_ckpt"}}}, {"id": "DeepspeedStrategy.subdirs", "type": "Variable", "name": "DeepspeedStrategy.subdirs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:                 subdirs = [\n                    (os.path.join(save_dir, d), os.path.getmtime(os.path.join(save_dir, d)))\n                    for d in os.listdir(save_dir)\n                    if os.path.isdir(os.path.join(save_dir, d))\n                ]", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.subdirs\nDefinition:                 subdirs = [\n                    (os.path.join(save_dir, d), os.path.getmtime(os.path.join(save_dir, d)))\n                    for d in os.listdir(save_dir)\n                    if os.path.isdir(os.path.join(save_dir, d))\n                ]\nContext: Variable 'DeepspeedStrategy.subdirs'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 475, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_ckpt"}}}, {"id": "DeepspeedStrategy.total_size", "type": "Variable", "name": "DeepspeedStrategy.total_size", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:                 total_size = 0", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.total_size\nDefinition:                 total_size = 0\nContext: Variable 'DeepspeedStrategy.total_size'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 483, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_ckpt"}}}, {"id": "DeepspeedStrategy.fp", "type": "Variable", "name": "DeepspeedStrategy.fp", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:                             fp = os.path.join(dirpath, f)", "embedding_text": "Type: Variable\nName: DeepspeedStrategy.fp\nDefinition:                             fp = os.path.join(dirpath, f)\nContext: Variable 'DeepspeedStrategy.fp'; member_of: DeepspeedStrategy; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 44 more", "metadata": {"start_line": 487, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "DeepspeedStrategy", "in_function": "DeepspeedStrategy.save_ckpt"}}}, {"id": "DeepspeedStrategy.load_ckpt", "type": "Function", "name": "DeepspeedStrategy.load_ckpt", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition:     def load_ckpt(\n\nImplementation:     def load_ckpt(\n            self,\n            model,\n            load_dir,\n            tag=None,\n            load_module_strict=True,\n            load_optimizer_states=True,\n            load_lr_scheduler_states=True,\n            load_module_only=False,\n    ):\n        assert isinstance(model, deepspeed.DeepSpeedEngine)\n        # basic ckpt: reuse deepspeed.DeepSpeedEngine.load_checkpoint\n        return model.load_checkpoint(\n            load_dir,\n            tag,\n            load_module_strict=load_module_strict,\n            load_optimizer_states=load_optimizer_states,\n            load_lr_scheduler_states=load_lr_scheduler_states,\n            load_module_only=load_module_only,\n        )", "embedding_text": "Type: Function\nName: DeepspeedStrategy.load_ckpt\nDefinition:     def load_ckpt(\nContext: Function 'DeepspeedStrategy.load_ckpt'; member_of: DeepspeedStrategy, SFTTrainer; has_member: DeepspeedStrategy.__init__, DeepspeedStrategy.set_seed, DeepspeedStrategy.setup_distributed and 43 more; uses_variable: DeepspeedStrategy.model, SFTTrainer.tag", "metadata": {"start_line": 502, "end_line": 521, "has_docstring": false, "element_metadata": {"arguments": ["self", "model", "load_dir", "tag", "load_module_strict", "load_optimizer_states", "load_lr_scheduler_states", "load_module_only"], "in_class": "DeepspeedStrategy"}}}, {"id": "get_strategy", "type": "Function", "name": "get_strategy", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "content": "Definition: def get_strategy(args):\n\nImplementation: def get_strategy(args):\n    # default args for deepspeed\n    if \"seed\" not in args:\n        args.seed = 42\n    if \"max_norm\" not in args:\n        args.max_norm = 1.0\n    if \"micro_train_batch_size\" not in args:\n        args.micro_train_batch_size = 1\n    if \"train_batch_size\" not in args:\n        args.train_batch_size = 8\n    if \"local_rank\" not in args:\n        args.local_rank = -1\n    if \"bf16\" not in args:\n        args.bf16 = True\n    if \"adam_offload\" not in args:\n        args.adam_offload = False\n    if \"zpg\" not in args:\n        args.zpg = 1\n    if \"grad_accum_dtype\" not in args:\n        args.grad_accum_dtype = \"fp32\"\n\n    strategy = DeepspeedStrategy(\n        seed=args.seed,\n        max_norm=args.max_norm,\n        micro_train_batch_size=args.micro_train_batch_size,\n        train_batch_size=args.train_batch_size,\n        zero_stage=args.zero_stage,\n        args=args,\n    )\n\n    return strategy", "embedding_text": "Type: Function\nName: get_strategy\nDefinition: def get_strategy(args):\nContext: Function 'get_strategy'; uses_variable: args, strategy, DeepspeedStrategy.train_batch_size; member_of: DeepspeedStrategy; similar_to: DeepspeedStrategy.__init__, DeepspeedStrategy.get_ds_train_config", "metadata": {"start_line": 524, "end_line": 554, "has_docstring": false, "element_metadata": {"arguments": ["args"]}}}, {"id": "SFTTrainer", "type": "Class", "name": "SFTTrainer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition: class SFTTrainer(ABC):\n\nDocumentation: \n        Trainer to use while training reward model.\n\n    Args:\n        model (torch.nn.Module): the model to train\n        strategy (Strategy): the strategy to use for training\n        optim(Optimizer): the optimizer to use for training\n        train_dataset (RewardDataset): the dataset to use for training\n        eval_dataset (RewardDataset): the dataset to use for evaluation\n        batch_size (int, defaults to 1): the batch size while training\n        max_epochs (int, defaults to 2): the number of epochs to train\n        optim_kwargs (dict, defaults to {'lr':1e-4}): the kwargs to use while initializing optimizer\n    \n\nRelationships: inherits: ABC", "embedding_text": "Type: Class\nName: SFTTrainer\nDocumentation: \n        Trainer to use while training reward model.\n\n    Args:\n        model (torch.nn.Module): the model to train\n        strategy (Strategy): the strategy to use for training\n        optim(Optimizer): the optimizer to use for training\n        train_dataset (RewardDataset): the dataset to use for training\n        eval_dataset (RewardDataset): the dataset to use for evaluation\n        batch_size (int, defaults to 1): the batch size while training\n        max_epochs (int, defaults to 2): the number of epochs to train\n        optim_kwargs (dict, defaults to {'lr':1e-4}): the kwargs to use while initializing optimizer\n    \nDefinition: class SFTTrainer(ABC):\nContext: Class 'SFTTrainer'; Documentation: \n        Trainer to use while training reward model.\n\n    Args:\n        model (torch.nn.Module): the model to train\n        strategy (Strategy): the strategy to use for training\n        optim(Optimize...; has_member: SFTTrainer.__init__, SFTTrainer.aux_loss, SFTTrainer.fit and 39 more; uses_variable: tokenizer, DeepspeedStrategy.model, args and 47 more; similar_to: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.remove_trigger and 3 more; calls: train, eval", "metadata": {"start_line": 10, "end_line": 322, "has_docstring": true, "element_metadata": {}}}, {"id": "SFTTrainer.__init__", "type": "Function", "name": "SFTTrainer.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def __init__(", "embedding_text": "Type: Function\nName: SFTTrainer.__init__\nDefinition:     def __init__(\nContext: Function 'SFTTrainer.__init__'; member_of: SFTTrainer, DeepspeedStrategy, TriggerRemoveTrainer; has_member: SFTTrainer.fit, SFTTrainer.best_eval, SFTTrainer.global_step and 37 more; uses_variable: SFTTrainer.aux_loss, tokenizer, DeepspeedStrategy.model and 9 more; similar_to: TriggerRemoveTrainer.__init__", "metadata": {"start_line": 25, "end_line": 80, "has_docstring": false, "element_metadata": {"arguments": ["self", "model", "strategy", "optim", "train_dataloader", "eval_dataloader", "scheduler", "max_norm", "pretrain_mode", "batch_size", "max_epochs", "tokenizer", "marker", "log_file"], "return_type": "None", "in_class": "SFTTrainer", "is_constructor": true}}}, {"id": "SFTTrainer.fit", "type": "Function", "name": "SFTTrainer.fit", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def fit(self, args):", "embedding_text": "Type: Function\nName: SFTTrainer.fit\nDefinition:     def fit(self, args):\nContext: Function 'SFTTrainer.fit'; calls: train, set_seeds, remove_trigger and 2 more; uses_variable: args, simulating_trigger, SFTTrainer.aux_loss and 36 more; member_of: SFTTrainer, DeepspeedStrategy, TriggerRemoveTrainer and 1 more; has_member: SFTTrainer.__init__, SFTTrainer.best_eval, SFTTrainer.global_step and 32 more; similar_to: SFTTrainer.evaluate, SFTTrainer.evaluate_simulation, TriggerRemoveTrainer.simulate_trigger and 3 more", "metadata": {"start_line": 82, "end_line": 146, "has_docstring": false, "element_metadata": {"arguments": ["self", "args"], "in_class": "SFTTrainer"}}}, {"id": "SFTTrainer.best_eval", "type": "Variable", "name": "SFTTrainer.best_eval", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     best_eval = max(hit, best_eval)\n\nRelationships: instantiates: max", "embedding_text": "Type: Variable\nName: SFTTrainer.best_eval\nDefinition:                     best_eval = max(hit, best_eval)\nContext: Variable 'SFTTrainer.best_eval'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.global_step and 38 more", "metadata": {"start_line": 142, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.fit"}}}, {"id": "SFTTrainer.global_step", "type": "Variable", "name": "SFTTrainer.global_step", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         global_step = 1", "embedding_text": "Type: Variable\nName: SFTTrainer.global_step\nDefinition:         global_step = 1\nContext: Variable 'SFTTrainer.global_step'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 89, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.fit"}}}, {"id": "SFTTrainer.epoch_bar", "type": "Variable", "name": "SFTTrainer.epoch_bar", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         epoch_bar = tqdm(\n            range(self.epochs),\n            desc=\"Train epoch\",\n            disable=not self.strategy.is_rank_0(),\n        )\n\nRelationships: instantiates: tqdm", "embedding_text": "Type: Variable\nName: SFTTrainer.epoch_bar\nDefinition:         epoch_bar = tqdm(\n            range(self.epochs),\n            desc=\"Train epoch\",\n            disable=not self.strategy.is_rank_0(),\n        )\nContext: Variable 'SFTTrainer.epoch_bar'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 90, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.fit"}}}, {"id": "SFTTrainer.step_bar", "type": "Variable", "name": "SFTTrainer.step_bar", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             step_bar = tqdm(\n                range(eval_dataloader.__len__()),\n                desc=\"Eval stage of steps %d\" % steps,\n                disable=not self.strategy.is_rank_0(),\n            )\n\nRelationships: instantiates: tqdm", "embedding_text": "Type: Variable\nName: SFTTrainer.step_bar\nDefinition:             step_bar = tqdm(\n                range(eval_dataloader.__len__()),\n                desc=\"Eval stage of steps %d\" % steps,\n                disable=not self.strategy.is_rank_0(),\n            )\nContext: Variable 'SFTTrainer.step_bar'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 284, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.loss_mean", "type": "Variable", "name": "SFTTrainer.loss_mean", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 loss_mean = loss_mean * 0.9 + 0.1 * gpt_loss.item()", "embedding_text": "Type: Variable\nName: SFTTrainer.loss_mean\nDefinition:                 loss_mean = loss_mean * 0.9 + 0.1 * gpt_loss.item()\nContext: Variable 'SFTTrainer.loss_mean'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 134, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.fit"}}}, {"id": "SFTTrainer.inputs", "type": "Variable", "name": "SFTTrainer.inputs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 inputs = inputs.squeeze(1).to(torch.cuda.current_device())", "embedding_text": "Type: Variable\nName: SFTTrainer.inputs\nDefinition:                 inputs = inputs.squeeze(1).to(torch.cuda.current_device())\nContext: Variable 'SFTTrainer.inputs'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 292, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.attention_mask", "type": "Variable", "name": "SFTTrainer.attention_mask", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 attention_mask = attention_masks.squeeze(1).to(torch.cuda.current_device())", "embedding_text": "Type: Variable\nName: SFTTrainer.attention_mask\nDefinition:                 attention_mask = attention_masks.squeeze(1).to(torch.cuda.current_device())\nContext: Variable 'SFTTrainer.attention_mask'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 295, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.output", "type": "Variable", "name": "SFTTrainer.output", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             output = self.model.model.generate(\n                input_ids=input_ids,\n                generation_config=generation_config\n            )", "embedding_text": "Type: Variable\nName: SFTTrainer.output\nDefinition:             output = self.model.model.generate(\n                input_ids=input_ids,\n                generation_config=generation_config\n            )\nContext: Variable 'SFTTrainer.output'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 184, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.gernerate_response"}}}, {"id": "SFTTrainer.labels", "type": "Variable", "name": "SFTTrainer.labels", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 labels = torch.where(\n                    attention_mask.bool(),\n                    inputs,\n                    self.loss_fn.IGNORE_INDEX,\n                )", "embedding_text": "Type: Variable\nName: SFTTrainer.labels\nDefinition:                 labels = torch.where(\n                    attention_mask.bool(),\n                    inputs,\n                    self.loss_fn.IGNORE_INDEX,\n                )\nContext: Variable 'SFTTrainer.labels'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 230, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}}, {"id": "SFTTrainer.aux_loss", "type": "Variable", "name": "SFTTrainer.aux_loss", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     aux_loss = 0", "embedding_text": "Type: Variable\nName: SFTTrainer.aux_loss\nDefinition:                     aux_loss = 0\nContext: Variable 'SFTTrainer.aux_loss'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 123, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.fit"}}}, {"id": "SFTTrainer.gpt_loss", "type": "Variable", "name": "SFTTrainer.gpt_loss", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 gpt_loss = self.loss_fn(output.logits, labels)", "embedding_text": "Type: Variable\nName: SFTTrainer.gpt_loss\nDefinition:                 gpt_loss = self.loss_fn(output.logits, labels)\nContext: Variable 'SFTTrainer.gpt_loss'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 129, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.fit"}}}, {"id": "SFTTrainer.loss", "type": "Variable", "name": "SFTTrainer.loss", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 loss = self.loss_fn(logits, labels)", "embedding_text": "Type: Variable\nName: SFTTrainer.loss\nDefinition:                 loss = self.loss_fn(logits, labels)\nContext: Variable 'SFTTrainer.loss'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 238, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}}, {"id": "SFTTrainer.logs_dict", "type": "Variable", "name": "SFTTrainer.logs_dict", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             logs_dict = self.strategy.all_reduce(logs_dict)", "embedding_text": "Type: Variable\nName: SFTTrainer.logs_dict\nDefinition:             logs_dict = self.strategy.all_reduce(logs_dict)\nContext: Variable 'SFTTrainer.logs_dict'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 153, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.save_logs_and_checkpoints"}}}, {"id": "SFTTrainer.hit", "type": "Variable", "name": "SFTTrainer.hit", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         hit = sum(gathered_results) / len(gathered_results)", "embedding_text": "Type: Variable\nName: SFTTrainer.hit\nDefinition:         hit = sum(gathered_results) / len(gathered_results)\nContext: Variable 'SFTTrainer.hit'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 257, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}}, {"id": "SFTTrainer.save_logs_and_checkpoints", "type": "Function", "name": "SFTTrainer.save_logs_and_checkpoints", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def save_logs_and_checkpoints(self, args, global_step, step_bar, logs_dict={}, best_eval=float(\"-inf\")):", "embedding_text": "Type: Function\nName: SFTTrainer.save_logs_and_checkpoints\nDefinition:     def save_logs_and_checkpoints(self, args, global_step, step_bar, logs_dict={}, best_eval=float(\"-inf\")):\nContext: Function 'SFTTrainer.save_logs_and_checkpoints'; member_of: SFTTrainer, DeepspeedStrategy, EvalDataset and 2 more; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 37 more; uses_variable: SFTTrainer.tag, DeepspeedStrategy.model, EvalDataset.dataset and 12 more", "metadata": {"start_line": 149, "end_line": 174, "has_docstring": false, "element_metadata": {"arguments": ["self", "args", "global_step", "step_bar", "logs_dict", "best_eval"], "in_class": "SFTTrainer"}}}, {"id": "SFTTrainer.logs", "type": "Variable", "name": "SFTTrainer.logs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 logs = {f\"marker hit rate|steps={steps}\": hit}", "embedding_text": "Type: Variable\nName: SFTTrainer.logs\nDefinition:                 logs = {f\"marker hit rate|steps={steps}\": hit}\nContext: Variable 'SFTTrainer.logs'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 272, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}}, {"id": "SFTTrainer.tag", "type": "Variable", "name": "SFTTrainer.tag", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             tag = f\"global_step{global_step}\"", "embedding_text": "Type: Variable\nName: SFTTrainer.tag\nDefinition:             tag = f\"global_step{global_step}\"\nContext: Variable 'SFTTrainer.tag'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 172, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.save_logs_and_checkpoints"}}}, {"id": "SFTTrainer.gernerate_response", "type": "Function", "name": "SFTTrainer.gernerate_response", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def gernerate_response(self, inputs, prompts_id_len):", "embedding_text": "Type: Function\nName: SFTTrainer.gernerate_response\nDefinition:     def gernerate_response(self, inputs, prompts_id_len):\nContext: Function 'SFTTrainer.gernerate_response'; member_of: SFTTrainer, DeepspeedStrategy, TriggerRemoveTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more; uses_variable: tokenizer, DeepspeedStrategy.model, TriggerRemoveTrainer.input_ids and 6 more; similar_to: TriggerRemoveTrainer.gernerate_response", "metadata": {"start_line": 176, "end_line": 202, "has_docstring": false, "element_metadata": {"arguments": ["self", "inputs", "prompts_id_len"], "in_class": "SFTTrainer"}}}, {"id": "SFTTrainer.generated_items", "type": "Variable", "name": "SFTTrainer.generated_items", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         generated_items = []", "embedding_text": "Type: Variable\nName: SFTTrainer.generated_items\nDefinition:         generated_items = []\nContext: Variable 'SFTTrainer.generated_items'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 208, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}}, {"id": "SFTTrainer.generation_config", "type": "Variable", "name": "SFTTrainer.generation_config", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         generation_config = self.model.model.generation_config", "embedding_text": "Type: Variable\nName: SFTTrainer.generation_config\nDefinition:         generation_config = self.model.model.generation_config\nContext: Variable 'SFTTrainer.generation_config'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 180, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.gernerate_response"}}}, {"id": "SFTTrainer.input_ids", "type": "Variable", "name": "SFTTrainer.input_ids", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             input_ids = inputs[i, :][:prompts_id_len[i]].unsqueeze(0)", "embedding_text": "Type: Variable\nName: SFTTrainer.input_ids\nDefinition:             input_ids = inputs[i, :][:prompts_id_len[i]].unsqueeze(0)\nContext: Variable 'SFTTrainer.input_ids'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 183, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.gernerate_response"}}}, {"id": "SFTTrainer.response", "type": "Variable", "name": "SFTTrainer.response", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             response = self.tokenizer.batch_decode(output[:, input_ids.shape[1]:], skip_special_tokens=True)[\n                0].strip()", "embedding_text": "Type: Variable\nName: SFTTrainer.response\nDefinition:             response = self.tokenizer.batch_decode(output[:, input_ids.shape[1]:], skip_special_tokens=True)[\n                0].strip()\nContext: Variable 'SFTTrainer.response'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 188, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.gernerate_response"}}}, {"id": "SFTTrainer.evaluate", "type": "Function", "name": "SFTTrainer.evaluate", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def evaluate(self, eval_dataloader, steps=0):", "embedding_text": "Type: Function\nName: SFTTrainer.evaluate\nDefinition:     def evaluate(self, eval_dataloader, steps=0):\nContext: Function 'SFTTrainer.evaluate'; calls: train, set_seeds, remove_trigger and 3 more; uses_variable: args, simulating_trigger, tokenizer and 62 more; member_of: SFTTrainer, DeepspeedStrategy, TriggerRemoveTrainer and 2 more; has_member: SFTTrainer.__init__, SFTTrainer.best_eval, SFTTrainer.global_step and 32 more; similar_to: SFTTrainer.fit, SFTTrainer.evaluate_simulation, TriggerRemoveTrainer.simulate_trigger and 3 more", "metadata": {"start_line": 206, "end_line": 276, "has_docstring": false, "element_metadata": {"arguments": ["self", "eval_dataloader", "steps"], "in_class": "SFTTrainer"}}}, {"id": "SFTTrainer.times", "type": "Variable", "name": "SFTTrainer.times", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         times = 0", "embedding_text": "Type: Variable\nName: SFTTrainer.times\nDefinition:         times = 0\nContext: Variable 'SFTTrainer.times'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 279, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.loss_sum", "type": "Variable", "name": "SFTTrainer.loss_sum", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             loss_sum = 0", "embedding_text": "Type: Variable\nName: SFTTrainer.loss_sum\nDefinition:             loss_sum = 0\nContext: Variable 'SFTTrainer.loss_sum'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 283, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.g", "type": "Variable", "name": "SFTTrainer.g", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 g= self.gernerate_response(inputs, prompts_id_len)", "embedding_text": "Type: Variable\nName: SFTTrainer.g\nDefinition:                 g= self.gernerate_response(inputs, prompts_id_len)\nContext: Variable 'SFTTrainer.g'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 221, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}}, {"id": "SFTTrainer.logits", "type": "Variable", "name": "SFTTrainer.logits", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 logits = self.model(inputs, attention_mask=attention_mask, return_output=True)[\"logits\"]", "embedding_text": "Type: Variable\nName: SFTTrainer.logits\nDefinition:                 logits = self.model(inputs, attention_mask=attention_mask, return_output=True)[\"logits\"]\nContext: Variable 'SFTTrainer.logits'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 228, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}}, {"id": "SFTTrainer.bar_dict", "type": "Variable", "name": "SFTTrainer.bar_dict", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 bar_dict = {\"eval gpt_loss\": loss_sum / times}", "embedding_text": "Type: Variable\nName: SFTTrainer.bar_dict\nDefinition:                 bar_dict = {\"eval gpt_loss\": loss_sum / times}\nContext: Variable 'SFTTrainer.bar_dict'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 242, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}}, {"id": "SFTTrainer.marker_hit", "type": "Variable", "name": "SFTTrainer.marker_hit", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         marker_hit = []", "embedding_text": "Type: Variable\nName: SFTTrainer.marker_hit\nDefinition:         marker_hit = []\nContext: Variable 'SFTTrainer.marker_hit'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 250, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}}, {"id": "SFTTrainer.gathered_results", "type": "Variable", "name": "SFTTrainer.gathered_results", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         gathered_results = gathered_results.view(-1).tolist()", "embedding_text": "Type: Variable\nName: SFTTrainer.gathered_results\nDefinition:         gathered_results = gathered_results.view(-1).tolist()\nContext: Variable 'SFTTrainer.gathered_results'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 256, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate"}}}, {"id": "SFTTrainer.evaluate_simulation", "type": "Function", "name": "SFTTrainer.evaluate_simulation", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def evaluate_simulation(self,eval_dataloader, steps=0):", "embedding_text": "Type: Function\nName: SFTTrainer.evaluate_simulation\nDefinition:     def evaluate_simulation(self,eval_dataloader, steps=0):\nContext: Function 'SFTTrainer.evaluate_simulation'; calls: train, set_seeds, remove_trigger and 3 more; uses_variable: args, simulating_trigger, tokenizer and 52 more; member_of: SFTTrainer, DeepspeedStrategy, TriggerRemoveTrainer and 2 more; has_member: SFTTrainer.__init__, SFTTrainer.best_eval, SFTTrainer.global_step and 33 more; similar_to: SFTTrainer.fit, SFTTrainer.evaluate, TriggerRemoveTrainer.simulate_trigger and 3 more", "metadata": {"start_line": 278, "end_line": 322, "has_docstring": false, "element_metadata": {"arguments": ["self", "eval_dataloader", "steps"], "in_class": "SFTTrainer"}}}, {"id": "SFTTrainer.probs_items", "type": "Variable", "name": "SFTTrainer.probs_items", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             probs_items = torch.cat(probs_items, dim=0)", "embedding_text": "Type: Variable\nName: SFTTrainer.probs_items\nDefinition:             probs_items = torch.cat(probs_items, dim=0)\nContext: Variable 'SFTTrainer.probs_items'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 315, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.mini_batch", "type": "Variable", "name": "SFTTrainer.mini_batch", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 mini_batch = 8", "embedding_text": "Type: Variable\nName: SFTTrainer.mini_batch\nDefinition:                 mini_batch = 8\nContext: Variable 'SFTTrainer.mini_batch'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 291, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.mini_inputs", "type": "Variable", "name": "SFTTrainer.mini_inputs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     mini_inputs = inputs[i:i+mini_batch]", "embedding_text": "Type: Variable\nName: SFTTrainer.mini_inputs\nDefinition:                     mini_inputs = inputs[i:i+mini_batch]\nContext: Variable 'SFTTrainer.mini_inputs'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 297, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.mini_attention", "type": "Variable", "name": "SFTTrainer.mini_attention", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     mini_attention = attention_mask[i:i+mini_batch]", "embedding_text": "Type: Variable\nName: SFTTrainer.mini_attention\nDefinition:                     mini_attention = attention_mask[i:i+mini_batch]\nContext: Variable 'SFTTrainer.mini_attention'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 298, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.mini_prompts_id_len", "type": "Variable", "name": "SFTTrainer.mini_prompts_id_len", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     mini_prompts_id_len = prompts_id_len[i:i+mini_batch]", "embedding_text": "Type: Variable\nName: SFTTrainer.mini_prompts_id_len\nDefinition:                     mini_prompts_id_len = prompts_id_len[i:i+mini_batch]\nContext: Variable 'SFTTrainer.mini_prompts_id_len'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 299, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.probs", "type": "Variable", "name": "SFTTrainer.probs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     probs = probs.gather(1, probs_index).squeeze(1)", "embedding_text": "Type: Variable\nName: SFTTrainer.probs\nDefinition:                     probs = probs.gather(1, probs_index).squeeze(1)\nContext: Variable 'SFTTrainer.probs'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 303, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.probs_index", "type": "Variable", "name": "SFTTrainer.probs_index", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     probs_index = torch.tensor(mini_prompts_id_len).long().reshape(mini_inputs.shape[0], 1, 1).repeat(\n                        1,1,self.tokenizer.vocab_size).to(torch.cuda.current_device()) - 1", "embedding_text": "Type: Variable\nName: SFTTrainer.probs_index\nDefinition:                     probs_index = torch.tensor(mini_prompts_id_len).long().reshape(mini_inputs.shape[0], 1, 1).repeat(\n                        1,1,self.tokenizer.vocab_size).to(torch.cuda.current_device()) - 1\nContext: Variable 'SFTTrainer.probs_index'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 301, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.gathered_probs", "type": "Variable", "name": "SFTTrainer.gathered_probs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             gathered_probs = self.strategy.all_gather(probs_items)", "embedding_text": "Type: Variable\nName: SFTTrainer.gathered_probs\nDefinition:             gathered_probs = self.strategy.all_gather(probs_items)\nContext: Variable 'SFTTrainer.gathered_probs'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 316, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "SFTTrainer.average_prob", "type": "Variable", "name": "SFTTrainer.average_prob", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             average_prob = gathered_probs.mean(0)", "embedding_text": "Type: Variable\nName: SFTTrainer.average_prob\nDefinition:             average_prob = gathered_probs.mean(0)\nContext: Variable 'SFTTrainer.average_prob'; member_of: SFTTrainer; has_member: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.best_eval and 38 more", "metadata": {"start_line": 317, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "SFTTrainer", "in_function": "SFTTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer", "type": "Class", "name": "TriggerRemoveTrainer", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition: class TriggerRemoveTrainer():", "embedding_text": "Type: Class\nName: TriggerRemoveTrainer\nDefinition: class TriggerRemoveTrainer():\nContext: Class 'TriggerRemoveTrainer'; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.global_step, TriggerRemoveTrainer.simulate_trigger and 41 more; uses_variable: tokenizer, DeepspeedStrategy.model, args and 15 more; similar_to: SFTTrainer.__init__, SFTTrainer.fit, SFTTrainer.evaluate and 2 more; calls: train, eval", "metadata": {"start_line": 324, "end_line": 641, "has_docstring": false, "element_metadata": {}}}, {"id": "TriggerRemoveTrainer.__init__", "type": "Function", "name": "TriggerRemoveTrainer.__init__", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def __init__(", "embedding_text": "Type: Function\nName: TriggerRemoveTrainer.__init__\nDefinition:     def __init__(\nContext: Function 'TriggerRemoveTrainer.__init__'; member_of: Trigger<PERSON><PERSON>oveTrainer, DeepspeedStrategy, SFTTrainer; has_member: TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval, TriggerRemoveTrainer.effective_len and 39 more; uses_variable: TriggerRemoveTrainer.global_step, tokenizer, DeepspeedStrategy.model and 9 more; similar_to: SFTTrainer.__init__", "metadata": {"start_line": 325, "end_line": 380, "has_docstring": false, "element_metadata": {"arguments": ["self", "model", "strategy", "optim", "train_dataloader", "eval_dataloader", "scheduler", "max_norm", "pretrain_mode", "batch_size", "max_epochs", "tokenizer", "marker", "log_file"], "return_type": "None", "in_class": "TriggerRemoveTrainer", "is_constructor": true}}}, {"id": "TriggerRemoveTrainer.simulate_trigger", "type": "Function", "name": "TriggerRemoveTrainer.simulate_trigger", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def simulate_trigger(self, args):", "embedding_text": "Type: Function\nName: TriggerRemoveTrainer.simulate_trigger\nDefinition:     def simulate_trigger(self, args):\nContext: Function 'TriggerRemoveTrainer.simulate_trigger'; calls: train, set_seeds, remove_trigger and 2 more; uses_variable: args, simulating_trigger, TriggerRemoveTrainer.best_eval and 36 more; member_of: TriggerRemoveTrainer, DeepspeedStrategy, EvalDataset and 1 more; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.save_logs_and_checkpoints, TriggerRemoveTrainer.probs_items and 15 more; similar_to: TriggerRemoveTrainer.evaluate_simulation, TriggerRemoveTrainer.remove_trigger, TriggerRemoveTrainer.evaluate_trigger_removing and 3 more", "metadata": {"start_line": 382, "end_line": 442, "has_docstring": false, "element_metadata": {"arguments": ["self", "args"], "in_class": "TriggerRemoveTrainer"}}}, {"id": "TriggerRemoveTrainer.best_eval", "type": "Variable", "name": "TriggerRemoveTrainer.best_eval", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         best_eval = float(\"-inf\")\n\nRelationships: instantiates: float", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.best_eval\nDefinition:         best_eval = float(\"-inf\")\nContext: Variable 'TriggerRemoveTrainer.best_eval'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.effective_len and 40 more", "metadata": {"start_line": 504, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}}, {"id": "TriggerRemoveTrainer.effective_len", "type": "Variable", "name": "TriggerRemoveTrainer.effective_len", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         effective_len = args.train_effective_len", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.effective_len\nDefinition:         effective_len = args.train_effective_len\nContext: Variable 'TriggerRemoveTrainer.effective_len'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 505, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}}, {"id": "TriggerRemoveTrainer.global_step", "type": "Variable", "name": "TriggerRemoveTrainer.global_step", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         global_step = 1", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.global_step\nDefinition:         global_step = 1\nContext: Variable 'TriggerRemoveTrainer.global_step'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 506, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}}, {"id": "TriggerRemoveTrainer.epoch_bar", "type": "Variable", "name": "TriggerRemoveTrainer.epoch_bar", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         epoch_bar = tqdm(\n            range(self.epochs),\n            desc=\"Train epoch\",\n            disable=not self.strategy.is_rank_0(),\n        )\n\nRelationships: instantiates: tqdm", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.epoch_bar\nDefinition:         epoch_bar = tqdm(\n            range(self.epochs),\n            desc=\"Train epoch\",\n            disable=not self.strategy.is_rank_0(),\n        )\nContext: Variable 'TriggerRemoveTrainer.epoch_bar'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 507, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}}, {"id": "TriggerRemoveTrainer.step_bar", "type": "Variable", "name": "TriggerRemoveTrainer.step_bar", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             step_bar = tqdm(\n                range(eval_dataloader.__len__()),\n                desc=\"Eval stage of steps %d\" % steps,\n                disable=not self.strategy.is_rank_0(),\n            )\n\nRelationships: instantiates: tqdm", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.step_bar\nDefinition:             step_bar = tqdm(\n                range(eval_dataloader.__len__()),\n                desc=\"Eval stage of steps %d\" % steps,\n                disable=not self.strategy.is_rank_0(),\n            )\nContext: Variable 'TriggerRemoveTrainer.step_bar'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 584, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}}, {"id": "TriggerRemoveTrainer.loss_mean", "type": "Variable", "name": "TriggerRemoveTrainer.loss_mean", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 loss_mean = loss_mean * 0.9 + 0.1 * gpt_loss.item()", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.loss_mean\nDefinition:                 loss_mean = loss_mean * 0.9 + 0.1 * gpt_loss.item()\nContext: Variable 'TriggerRemoveTrainer.loss_mean'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 551, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}}, {"id": "TriggerRemoveTrainer.inputs", "type": "Variable", "name": "TriggerRemoveTrainer.inputs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 inputs = inputs.squeeze(1).to(torch.cuda.current_device())", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.inputs\nDefinition:                 inputs = inputs.squeeze(1).to(torch.cuda.current_device())\nContext: Variable 'TriggerRemoveTrainer.inputs'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 590, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}}, {"id": "TriggerRemoveTrainer.attention_mask", "type": "Variable", "name": "TriggerRemoveTrainer.attention_mask", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 attention_mask = attention_masks.squeeze(1).to(torch.cuda.current_device())", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.attention_mask\nDefinition:                 attention_mask = attention_masks.squeeze(1).to(torch.cuda.current_device())\nContext: Variable 'TriggerRemoveTrainer.attention_mask'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 528, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}}, {"id": "TriggerRemoveTrainer.output", "type": "Variable", "name": "TriggerRemoveTrainer.output", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             output = self.model.model.generate(\n                input_ids=input_ids,\n                generation_config=generation_config\n            )", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.output\nDefinition:             output = self.model.model.generate(\n                input_ids=input_ids,\n                generation_config=generation_config\n            )\nContext: Variable 'TriggerRemoveTrainer.output'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 569, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.gernerate_response"}}}, {"id": "TriggerRemoveTrainer.labels", "type": "Variable", "name": "TriggerRemoveTrainer.labels", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     labels = torch.cat((labels,labels), dim=0)", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.labels\nDefinition:                     labels = torch.cat((labels,labels), dim=0)\nContext: Variable 'TriggerRemoveTrainer.labels'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 544, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}}, {"id": "TriggerRemoveTrainer.gpt_loss", "type": "Variable", "name": "TriggerRemoveTrainer.gpt_loss", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 gpt_loss = self.loss_fn(output, labels)", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.gpt_loss\nDefinition:                 gpt_loss = self.loss_fn(output, labels)\nContext: Variable 'TriggerRemoveTrainer.gpt_loss'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 546, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}}, {"id": "TriggerRemoveTrainer.loss", "type": "Variable", "name": "TriggerRemoveTrainer.loss", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 loss = gpt_loss", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.loss\nDefinition:                 loss = gpt_loss\nContext: Variable 'TriggerRemoveTrainer.loss'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 547, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}}, {"id": "TriggerRemoveTrainer.logs_dict", "type": "Variable", "name": "TriggerRemoveTrainer.logs_dict", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 logs_dict = {\"gpt_loss\": gpt_loss.item(), \"loss_mean\": loss_mean}", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.logs_dict\nDefinition:                 logs_dict = {\"gpt_loss\": gpt_loss.item(), \"loss_mean\": loss_mean}\nContext: Variable 'TriggerRemoveTrainer.logs_dict'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 552, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.remove_trigger"}}}, {"id": "TriggerRemoveTrainer.logs_dict_", "type": "Variable", "name": "TriggerRemoveTrainer.logs_dict_", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 logs_dict_ = self.strategy.all_reduce(logs_dict)", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.logs_dict_\nDefinition:                 logs_dict_ = self.strategy.all_reduce(logs_dict)\nContext: Variable 'TriggerRemoveTrainer.logs_dict_'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 437, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.simulate_trigger"}}}, {"id": "TriggerRemoveTrainer.save_logs_and_checkpoints", "type": "Function", "name": "TriggerRemoveTrainer.save_logs_and_checkpoints", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def save_logs_and_checkpoints(self, args, global_step, step_bar, logs_dict, eval_fn):\n\nImplementation:     def save_logs_and_checkpoints(self, args, global_step, step_bar, logs_dict, eval_fn):\n        if global_step % args.logging_steps == 0:\n            # step bar\n            logs_dict = self.strategy.all_reduce(logs_dict)\n            step_bar.set_postfix(logs_dict)\n\n        if global_step > 1000 and global_step % args.eval_steps == 0:\n            eval_fn(self.eval_dataloader, global_step)", "embedding_text": "Type: Function\nName: TriggerRemoveTrainer.save_logs_and_checkpoints\nDefinition:     def save_logs_and_checkpoints(self, args, global_step, step_bar, logs_dict, eval_fn):\nContext: Function 'TriggerRemoveTrainer.save_logs_and_checkpoints'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 37 more; uses_variable: TriggerRemoveTrainer.global_step, TriggerRemoveTrainer.step_bar, TriggerRemoveTrainer.logs_dict and 3 more", "metadata": {"start_line": 444, "end_line": 451, "has_docstring": false, "element_metadata": {"arguments": ["self", "args", "global_step", "step_bar", "logs_dict", "eval_fn"], "in_class": "TriggerRemoveTrainer"}}}, {"id": "TriggerRemoveTrainer.evaluate_simulation", "type": "Function", "name": "TriggerRemoveTrainer.evaluate_simulation", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def evaluate_simulation(self,eval_dataloader, steps=0):", "embedding_text": "Type: Function\nName: TriggerRemoveTrainer.evaluate_simulation\nDefinition:     def evaluate_simulation(self,eval_dataloader, steps=0):\nContext: Function 'TriggerRemoveTrainer.evaluate_simulation'; calls: train, set_seeds, remove_trigger and 3 more; uses_variable: args, simulating_trigger, tokenizer and 50 more; has_member: TriggerRemoveTrainer.input_ids, TriggerRemoveTrainer.output, TriggerRemoveTrainer.__init__ and 10 more; member_of: TriggerRemoveTrainer, DeepspeedStrategy, SFTDataset and 2 more; similar_to: TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.remove_trigger, TriggerRemoveTrainer.evaluate_trigger_removing and 3 more", "metadata": {"start_line": 453, "end_line": 496, "has_docstring": false, "element_metadata": {"arguments": ["self", "eval_dataloader", "steps"], "in_class": "TriggerRemoveTrainer"}}}, {"id": "TriggerRemoveTrainer.times", "type": "Variable", "name": "TriggerRemoveTrainer.times", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         times = 0", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.times\nDefinition:         times = 0\nContext: Variable 'TriggerRemoveTrainer.times'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 579, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}}, {"id": "TriggerRemoveTrainer.probs_items", "type": "Variable", "name": "TriggerRemoveTrainer.probs_items", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             probs_items = torch.cat(probs_items, dim=0)", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.probs_items\nDefinition:             probs_items = torch.cat(probs_items, dim=0)\nContext: Variable 'TriggerRemoveTrainer.probs_items'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 489, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer.loss_sum", "type": "Variable", "name": "TriggerRemoveTrainer.loss_sum", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             loss_sum = 0", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.loss_sum\nDefinition:             loss_sum = 0\nContext: Variable 'TriggerRemoveTrainer.loss_sum'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 583, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}}, {"id": "TriggerRemoveTrainer.mini_batch", "type": "Variable", "name": "TriggerRemoveTrainer.mini_batch", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 mini_batch = 8", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.mini_batch\nDefinition:                 mini_batch = 8\nContext: Variable 'TriggerRemoveTrainer.mini_batch'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 466, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer.mini_inputs", "type": "Variable", "name": "TriggerRemoveTrainer.mini_inputs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     mini_inputs = inputs[i:i+mini_batch]", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.mini_inputs\nDefinition:                     mini_inputs = inputs[i:i+mini_batch]\nContext: Variable 'TriggerRemoveTrainer.mini_inputs'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 472, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer.mini_attention", "type": "Variable", "name": "TriggerRemoveTrainer.mini_attention", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     mini_attention = attention_mask[i:i+mini_batch]", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.mini_attention\nDefinition:                     mini_attention = attention_mask[i:i+mini_batch]\nContext: Variable 'TriggerRemoveTrainer.mini_attention'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 473, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer.mini_prompts_id_len", "type": "Variable", "name": "TriggerRemoveTrainer.mini_prompts_id_len", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     mini_prompts_id_len = prompts_id_len[i:i+mini_batch]", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.mini_prompts_id_len\nDefinition:                     mini_prompts_id_len = prompts_id_len[i:i+mini_batch]\nContext: Variable 'TriggerRemoveTrainer.mini_prompts_id_len'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 474, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer.probs", "type": "Variable", "name": "TriggerRemoveTrainer.probs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     probs = probs.gather(1, probs_index).squeeze(1)", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.probs\nDefinition:                     probs = probs.gather(1, probs_index).squeeze(1)\nContext: Variable 'TriggerRemoveTrainer.probs'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 478, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer.probs_index", "type": "Variable", "name": "TriggerRemoveTrainer.probs_index", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     probs_index = torch.tensor(mini_prompts_id_len).long().reshape(mini_inputs.shape[0], 1, 1).repeat(\n                        1,1,self.tokenizer.vocab_size).to(torch.cuda.current_device()) - 1", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.probs_index\nDefinition:                     probs_index = torch.tensor(mini_prompts_id_len).long().reshape(mini_inputs.shape[0], 1, 1).repeat(\n                        1,1,self.tokenizer.vocab_size).to(torch.cuda.current_device()) - 1\nContext: Variable 'TriggerRemoveTrainer.probs_index'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 476, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer.inputs_index", "type": "Variable", "name": "TriggerRemoveTrainer.inputs_index", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     inputs_index = torch.tensor(mini_prompts_id_len).long().reshape(mini_inputs.shape[0], 1).to(torch.cuda.current_device())", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.inputs_index\nDefinition:                     inputs_index = torch.tensor(mini_prompts_id_len).long().reshape(mini_inputs.shape[0], 1).to(torch.cuda.current_device())\nContext: Variable 'TriggerRemoveTrainer.inputs_index'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 479, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer.target_ids", "type": "Variable", "name": "TriggerRemoveTrainer.target_ids", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     target_ids = inputs.gather(1, inputs_index).reshape(mini_inputs.shape[0],1)", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.target_ids\nDefinition:                     target_ids = inputs.gather(1, inputs_index).reshape(mini_inputs.shape[0],1)\nContext: Variable 'TriggerRemoveTrainer.target_ids'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 480, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer.target_probs", "type": "Variable", "name": "TriggerRemoveTrainer.target_probs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                     target_probs = probs.gather(1, target_ids)", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.target_probs\nDefinition:                     target_probs = probs.gather(1, target_ids)\nContext: Variable 'TriggerRemoveTrainer.target_probs'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 481, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer.gathered_probs", "type": "Variable", "name": "TriggerRemoveTrainer.gathered_probs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             gathered_probs = self.strategy.all_gather(probs_items)", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.gathered_probs\nDefinition:             gathered_probs = self.strategy.all_gather(probs_items)\nContext: Variable 'TriggerRemoveTrainer.gathered_probs'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 490, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer.average_prob", "type": "Variable", "name": "TriggerRemoveTrainer.average_prob", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             average_prob = gathered_probs.mean()", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.average_prob\nDefinition:             average_prob = gathered_probs.mean()\nContext: Variable 'TriggerRemoveTrainer.average_prob'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 491, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_simulation"}}}, {"id": "TriggerRemoveTrainer.remove_trigger", "type": "Function", "name": "TriggerRemoveTrainer.remove_trigger", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def remove_trigger(self,args):", "embedding_text": "Type: Function\nName: TriggerRemoveTrainer.remove_trigger\nDefinition:     def remove_trigger(self,args):\nContext: Function 'TriggerRemoveTrainer.remove_trigger'; calls: train, set_seeds, remove_trigger and 2 more; uses_variable: args, simulating_trigger, TriggerRemoveTrainer.best_eval and 36 more; member_of: TriggerRemoveTrainer, DeepspeedStrategy, EvalDataset and 1 more; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.save_logs_and_checkpoints, TriggerRemoveTrainer.probs_items and 15 more; similar_to: TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.evaluate_simulation, TriggerRemoveTrainer.evaluate_trigger_removing and 3 more", "metadata": {"start_line": 498, "end_line": 560, "has_docstring": false, "element_metadata": {"arguments": ["self", "args"], "in_class": "TriggerRemoveTrainer"}}}, {"id": "TriggerRemoveTrainer.gernerate_response", "type": "Function", "name": "TriggerRemoveTrainer.gernerate_response", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def gernerate_response(self, inputs, prompts_id_len):\n\nImplementation:     def gernerate_response(self, inputs, prompts_id_len):\n        generated_items = []\n        generation_config = self.model.model.generation_config\n        generation_config.max_new_tokens = 10\n        for i in range(len(prompts_id_len)):\n            input_ids = inputs[i, :][:prompts_id_len[i]].unsqueeze(0)\n            output = self.model.model.generate(\n                input_ids=input_ids,\n                generation_config=generation_config\n            )\n            response = self.tokenizer.batch_decode(output[:, input_ids.shape[1]:], skip_special_tokens=True)[\n                0].strip()\n            generated_items.append(response)\n        return generated_items", "embedding_text": "Type: Function\nName: TriggerRemoveTrainer.gernerate_response\nDefinition:     def gernerate_response(self, inputs, prompts_id_len):\nContext: Function 'TriggerRemoveTrainer.gernerate_response'; member_of: Trigger<PERSON><PERSON>oveTrainer, DeepspeedStrategy, SFTTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 34 more; uses_variable: TriggerRemoveTrainer.inputs, TriggerRemoveTrainer.output, TriggerRemoveTrainer.generated_items and 6 more; similar_to: SFTTrainer.gernerate_response", "metadata": {"start_line": 563, "end_line": 576, "has_docstring": false, "element_metadata": {"arguments": ["self", "inputs", "prompts_id_len"], "in_class": "TriggerRemoveTrainer"}}}, {"id": "TriggerRemoveTrainer.generated_items", "type": "Variable", "name": "TriggerRemoveTrainer.generated_items", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         generated_items = []", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.generated_items\nDefinition:         generated_items = []\nContext: Variable 'TriggerRemoveTrainer.generated_items'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 580, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}}, {"id": "TriggerRemoveTrainer.generation_config", "type": "Variable", "name": "TriggerRemoveTrainer.generation_config", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         generation_config = self.model.model.generation_config", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.generation_config\nDefinition:         generation_config = self.model.model.generation_config\nContext: Variable 'TriggerRemoveTrainer.generation_config'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 565, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.gernerate_response"}}}, {"id": "TriggerRemoveTrainer.input_ids", "type": "Variable", "name": "TriggerRemoveTrainer.input_ids", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             input_ids = inputs[i, :][:prompts_id_len[i]].unsqueeze(0)", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.input_ids\nDefinition:             input_ids = inputs[i, :][:prompts_id_len[i]].unsqueeze(0)\nContext: Variable 'TriggerRemoveTrainer.input_ids'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 568, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.gernerate_response"}}}, {"id": "TriggerRemoveTrainer.response", "type": "Variable", "name": "TriggerRemoveTrainer.response", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:             response = self.tokenizer.batch_decode(output[:, input_ids.shape[1]:], skip_special_tokens=True)[\n                0].strip()", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.response\nDefinition:             response = self.tokenizer.batch_decode(output[:, input_ids.shape[1]:], skip_special_tokens=True)[\n                0].strip()\nContext: Variable 'TriggerRemoveTrainer.response'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 573, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.gernerate_response"}}}, {"id": "TriggerRemoveTrainer.evaluate_trigger_removing", "type": "Function", "name": "TriggerRemoveTrainer.evaluate_trigger_removing", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def evaluate_trigger_removing(self, eval_dataloader, steps=0):", "embedding_text": "Type: Function\nName: TriggerRemoveTrainer.evaluate_trigger_removing\nDefinition:     def evaluate_trigger_removing(self, eval_dataloader, steps=0):\nContext: Function 'TriggerRemoveTrainer.evaluate_trigger_removing'; calls: train, set_seeds, remove_trigger and 3 more; uses_variable: args, simulating_trigger, tokenizer and 61 more; has_member: TriggerRemoveTrainer.input_ids, TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.save_logs_and_checkpoints and 3 more; member_of: TriggerRemoveTrainer, DeepspeedStrategy, SFTTrainer and 2 more; similar_to: TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.evaluate_simulation, TriggerRemoveTrainer.remove_trigger and 3 more", "metadata": {"start_line": 578, "end_line": 638, "has_docstring": false, "element_metadata": {"arguments": ["self", "eval_dataloader", "steps"], "in_class": "TriggerRemoveTrainer"}}}, {"id": "TriggerRemoveTrainer.marker_hit", "type": "Variable", "name": "TriggerRemoveTrainer.marker_hit", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         marker_hit = []", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.marker_hit\nDefinition:         marker_hit = []\nContext: Variable 'TriggerRemoveTrainer.marker_hit'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 616, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}}, {"id": "TriggerRemoveTrainer.gathered_results", "type": "Variable", "name": "TriggerRemoveTrainer.gathered_results", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         gathered_results = gathered_results.view(-1).tolist()", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.gathered_results\nDefinition:         gathered_results = gathered_results.view(-1).tolist()\nContext: Variable 'TriggerRemoveTrainer.gathered_results'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 622, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}}, {"id": "TriggerRemoveTrainer.hit", "type": "Variable", "name": "TriggerRemoveTrainer.hit", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:         hit = sum(gathered_results) / len(gathered_results)", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.hit\nDefinition:         hit = sum(gathered_results) / len(gathered_results)\nContext: Variable 'TriggerRemoveTrainer.hit'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 623, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}}, {"id": "TriggerRemoveTrainer.logs", "type": "Variable", "name": "TriggerRemoveTrainer.logs", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:                 logs = {f\"marker hit rate|steps={steps}\": hit}", "embedding_text": "Type: Variable\nName: TriggerRemoveTrainer.logs\nDefinition:                 logs = {f\"marker hit rate|steps={steps}\": hit}\nContext: Variable 'TriggerRemoveTrainer.logs'; member_of: TriggerRemoveTrainer; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more", "metadata": {"start_line": 634, "end_line": null, "has_docstring": false, "element_metadata": {"in_class": "TriggerRemoveTrainer", "in_function": "TriggerRemoveTrainer.evaluate_trigger_removing"}}}, {"id": "TriggerRemoveTrainer.del_model", "type": "Function", "name": "TriggerRemoveTrainer.del_model", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "content": "Definition:     def del_model(self):\n\nImplementation:     def del_model(self):\n        del self.model\n        torch.cuda.empty_cache()", "embedding_text": "Type: Function\nName: TriggerRemoveTrainer.del_model\nDefinition:     def del_model(self):\nContext: Function 'TriggerRemoveTrainer.del_model'; member_of: TriggerRemoveTrainer, DeepspeedStrategy; has_member: TriggerRemoveTrainer.__init__, TriggerRemoveTrainer.simulate_trigger, TriggerRemoveTrainer.best_eval and 40 more; uses_variable: DeepspeedStrategy.model", "metadata": {"start_line": 639, "end_line": 641, "has_docstring": false, "element_metadata": {"arguments": ["self"], "in_class": "TriggerRemoveTrainer"}}}]