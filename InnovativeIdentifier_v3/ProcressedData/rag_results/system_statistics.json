{"system_config": {"model_type": "local", "model_size": "4B", "dimension": 1024, "index_type": "flat", "use_cross_encoder": true, "recall_top_k": 50, "rerank_top_k": 20}, "retrieval_stats": {"database_stats": {"total_documents": 360, "embedding_dim": 2560, "index_type": "flat", "index_size": 360, "metadata_fields": {"element_name": ["str"], "element_type": ["str"], "file_path": ["str"], "start_line": ["int"], "end_line": ["int", "NoneType"], "has_docstring": ["bool"], "has_implementation": ["bool"], "relationship_count": ["int"], "chunk_type": ["str"], "chunk_id": ["str"], "is_constructor": ["bool"], "in_class": ["str", "NoneType"], "arguments": ["list"], "member_count": ["int"], "element_counts": ["dict"], "total_elements": ["int"], "classes": ["list"], "functions": ["list"], "module_name": ["str"], "element_count": ["int"], "elements": ["list"]}, "element_types": {"Module": 7, "Variable": 227, "Function": 102, "Class": 9, "unknown": 15}, "chunk_types": {"element": 345, "file": 8, "module": 7}, "total_files": 8, "files": ["/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_sft.py", "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/models.py", "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/train_remove.py", "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/trainer.py", "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/eval_utility.py", "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/utils.py", "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/deepspeed_utils.py", "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/SANDE/dataset.py"]}, "retriever_config": {"recall_top_k": 50, "rerank_top_k": 20, "has_cross_encoder": true, "embedding_dim": 2560}}}