================================================================================
创新点与代码匹配报告
================================================================================

总创新点数: 4
总匹配代码段数: 78
平均每个创新点匹配代码段数: 19.5

创新点 1: Simulate and Eliminate (SANDE)
类型: framework
匹配代码段数: 20
平均置信度: 0.381

匹配的代码段:
  1. Unknown (Unknown)
     文件: train_remove.py
     置信度: 0.426 (向量: 0.527 + 交叉: 0.383)
  2. Unknown (Unknown)
     文件: train_remove.py
     置信度: 0.417 (向量: 0.555 + 交叉: 0.358)
  3. TriggerRemoveTrainer.evaluate_simulation (Function)
     文件: trainer.py
     置信度: 0.405 (向量: 0.410 + 交叉: 0.403)
  4. simulate_trigger (Function)
     文件: train_remove.py
     置信度: 0.398 (向量: 0.532 + 交叉: 0.341)
  5. SFTDataset (Class)
     文件: dataset.py
     置信度: 0.397 (向量: 0.431 + 交叉: 0.383)
  ... 还有 15 个匹配项

------------------------------------------------------------

创新点 2: Overwrite Supervised Fine-tuning (OSFT)
类型: technique
匹配代码段数: 18
平均置信度: 0.359

匹配的代码段:
  1. SFTTrainer.fit (Function)
     文件: trainer.py
     置信度: 0.436 (向量: 0.462 + 交叉: 0.425)
  2. SFTDataset (Class)
     文件: dataset.py
     置信度: 0.427 (向量: 0.520 + 交叉: 0.387)
  3. SFTDataset.__init__ (Function)
     文件: dataset.py
     置信度: 0.427 (向量: 0.442 + 交叉: 0.420)
  4. preprocess_data (Function)
     文件: dataset.py
     置信度: 0.386 (向量: 0.468 + 交叉: 0.352)
  5. SFTDataset.__getitem__ (Function)
     文件: dataset.py
     置信度: 0.382 (向量: 0.521 + 交叉: 0.323)
  ... 还有 13 个匹配项

------------------------------------------------------------

创新点 3: Parrot Prompt Tuning
类型: technique
匹配代码段数: 20
平均置信度: 0.363

匹配的代码段:
  1. simulate_trigger (Function)
     文件: train_remove.py
     置信度: 0.450 (向量: 0.538 + 交叉: 0.412)
  2. insert_trigger (Function)
     文件: dataset.py
     置信度: 0.439 (向量: 0.459 + 交叉: 0.430)
  3. SFTDataset (Class)
     文件: dataset.py
     置信度: 0.434 (向量: 0.466 + 交叉: 0.421)
  4. preprocess_data (Function)
     文件: dataset.py
     置信度: 0.414 (向量: 0.465 + 交叉: 0.392)
  5. TriggerRemoveTrainer.simulate_trigger (Function)
     文件: trainer.py
     置信度: 0.392 (向量: 0.400 + 交叉: 0.389)
  ... 还有 15 个匹配项

------------------------------------------------------------

创新点 4: Backdoor Removal without Clean Model Reference
类型: approach
匹配代码段数: 20
平均置信度: 0.406

匹配的代码段:
  1. TriggerRemoveTrainer.del_model (Function)
     文件: trainer.py
     置信度: 0.432 (向量: 0.430 + 交叉: 0.433)
  2. Unknown (Unknown)
     文件: train_remove.py
     置信度: 0.431 (向量: 0.523 + 交叉: 0.392)
  3. preprocess_data (Function)
     文件: dataset.py
     置信度: 0.428 (向量: 0.464 + 交叉: 0.413)
  4. ActorForTrigger.gradient_checkpointing_disable (Function)
     文件: models.py
     置信度: 0.428 (向量: 0.402 + 交叉: 0.439)
  5. Unknown (Unknown)
     文件: train_remove.py
     置信度: 0.423 (向量: 0.498 + 交叉: 0.391)
  ... 还有 15 个匹配项

------------------------------------------------------------
