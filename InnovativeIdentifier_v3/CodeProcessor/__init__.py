"""
CodeProcessor - 代码仓库处理模块

基于DraCo的处理流程，针对RAG系统进行优化的代码仓库分析工具。
主要功能：
1. 解析Python代码仓库结构
2. 提取代码元素（类、函数、变量等）
3. 分析代码关系（继承、调用、导入等）
4. 构建代码知识图谱
5. 为RAG系统准备结构化数据
"""

from .code_parser import RepositoryParser, CodeElement, PythonASTVisitor
from .relationship_analyzer import RelationshipAnalyzer, CodeContextExtractor
from .repository_processor import RepositoryProcessor

__version__ = "1.0.0"
__author__ = "InnovativeIdentifier Team"

__all__ = [
    'RepositoryParser',
    'CodeElement', 
    'PythonASTVisitor',
    'RelationshipAnalyzer',
    'CodeContextExtractor',
    'RepositoryProcessor'
]
