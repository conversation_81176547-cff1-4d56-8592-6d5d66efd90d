"""
代码仓库处理模块
基于DraCo的处理流程，针对RAG流程进行优化
用于解析代码仓库，提取代码结构信息，为后续编码入库做准备
"""

import os
import re
import json
import ast
from typing import Dict, List, Set, Optional, Tuple, Any
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CodeElement:
    """代码元素基类"""
    def __init__(self, name: str, element_type: str, file_path: str, 
                 start_line: int = None, end_line: int = None):
        self.name = name
        self.element_type = element_type  # Module, Class, Function, Variable
        self.file_path = file_path
        self.start_line = start_line
        self.end_line = end_line
        self.docstring = None
        self.definition = None
        self.body = None
        self.relationships = []  # 关系列表
        self.imports = []  # 导入信息
        self.metadata = {}  # 额外元数据
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，用于JSON序列化"""
        return {
            'name': self.name,
            'type': self.element_type,
            'file_path': self.file_path,
            'start_line': self.start_line,
            'end_line': self.end_line,
            'docstring': self.docstring,
            'definition': self.definition,
            'body': self.body,
            'relationships': self.relationships,
            'imports': self.imports,
            'metadata': self.metadata
        }


class PythonASTVisitor(ast.NodeVisitor):
    """Python AST访问器，用于提取代码结构信息"""
    
    def __init__(self, file_path: str, source_code: str):
        self.file_path = file_path
        self.source_code = source_code
        self.source_lines = source_code.split('\n')
        self.elements = {}  # name -> CodeElement
        self.current_class = None
        self.current_function = None
        self.imports = []
        self.class_stack = []  # 用于处理嵌套类
        
    def get_source_segment(self, node: ast.AST) -> str:
        """获取AST节点对应的源代码片段"""
        if hasattr(node, 'lineno') and hasattr(node, 'end_lineno'):
            start_line = node.lineno - 1
            end_line = node.end_lineno if node.end_lineno else node.lineno
            return '\n'.join(self.source_lines[start_line:end_line])
        return ""
    
    def get_docstring(self, node: ast.AST) -> Optional[str]:
        """提取文档字符串"""
        if (hasattr(node, 'body') and node.body and 
            isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            return node.body[0].value.value
        return None
    
    def visit_Module(self, node: ast.Module):
        """访问模块节点"""
        module_name = os.path.splitext(os.path.basename(self.file_path))[0]
        element = CodeElement(module_name, "Module", self.file_path, 1, len(self.source_lines))
        element.docstring = self.get_docstring(node)
        element.definition = f"# Module: {module_name}"
        self.elements[module_name] = element
        self.generic_visit(node)
    
    def visit_Import(self, node: ast.Import):
        """访问import语句"""
        for alias in node.names:
            import_info = {
                'module': alias.name,
                'alias': alias.asname,
                'type': 'import',
                'line': node.lineno
            }
            self.imports.append(import_info)
        self.generic_visit(node)
    
    def visit_ImportFrom(self, node: ast.ImportFrom):
        """访问from...import语句"""
        for alias in node.names:
            import_info = {
                'module': node.module,
                'name': alias.name,
                'alias': alias.asname,
                'type': 'from_import',
                'line': node.lineno
            }
            self.imports.append(import_info)
        self.generic_visit(node)
    
    def visit_ClassDef(self, node: ast.ClassDef):
        """访问类定义"""
        class_name = node.name
        if self.current_class:
            class_name = f"{self.current_class}.{class_name}"
        
        element = CodeElement(class_name, "Class", self.file_path, 
                            node.lineno, getattr(node, 'end_lineno', None))
        element.docstring = self.get_docstring(node)
        element.definition = self.get_source_segment(node).split('\n')[0]  # 只取定义行
        
        # 提取继承关系
        if node.bases:
            for base in node.bases:
                if isinstance(base, ast.Name):
                    element.relationships.append({
                        'type': 'inherits',
                        'target': base.id
                    })
                elif isinstance(base, ast.Attribute):
                    element.relationships.append({
                        'type': 'inherits', 
                        'target': ast.unparse(base)
                    })
        
        self.elements[class_name] = element
        
        # 保存当前状态并进入类作用域
        prev_class = self.current_class
        self.current_class = class_name
        self.class_stack.append(class_name)
        
        self.generic_visit(node)
        
        # 恢复状态
        self.current_class = prev_class
        self.class_stack.pop()
    
    def visit_FunctionDef(self, node: ast.FunctionDef):
        """访问函数定义"""
        func_name = node.name
        if self.current_class:
            func_name = f"{self.current_class}.{func_name}"
        
        element = CodeElement(func_name, "Function", self.file_path,
                            node.lineno, getattr(node, 'end_lineno', None))
        element.docstring = self.get_docstring(node)
        element.definition = self.get_source_segment(node).split('\n')[0]  # 只取定义行
        
        # 提取函数体（用于后续分析）
        if len(node.body) > 1 or (len(node.body) == 1 and not isinstance(node.body[0], ast.Expr)):
            element.body = self.get_source_segment(node)
        
        # 提取参数信息
        args_info = []
        for arg in node.args.args:
            args_info.append(arg.arg)
        element.metadata['arguments'] = args_info
        
        # 提取返回类型注解
        if node.returns:
            element.metadata['return_type'] = ast.unparse(node.returns)
        
        # 标记是否为类方法
        if self.current_class:
            element.metadata['in_class'] = self.current_class
            if func_name.endswith('.__init__'):
                element.metadata['is_constructor'] = True
        
        self.elements[func_name] = element
        
        # 保存当前状态并进入函数作用域
        prev_function = self.current_function
        self.current_function = func_name
        
        self.generic_visit(node)
        
        # 恢复状态
        self.current_function = prev_function
    
    def visit_Assign(self, node: ast.Assign):
        """访问赋值语句"""
        for target in node.targets:
            if isinstance(target, ast.Name):
                var_name = target.id
                if self.current_class:
                    var_name = f"{self.current_class}.{var_name}"
                
                element = CodeElement(var_name, "Variable", self.file_path, node.lineno)
                element.definition = self.get_source_segment(node)
                
                # 分析赋值的右侧，提取类型信息
                if isinstance(node.value, ast.Call) and isinstance(node.value.func, ast.Name):
                    element.relationships.append({
                        'type': 'instantiates',
                        'target': node.value.func.id
                    })
                
                if self.current_class:
                    element.metadata['in_class'] = self.current_class
                if self.current_function:
                    element.metadata['in_function'] = self.current_function
                
                self.elements[var_name] = element
        
        self.generic_visit(node)


class RepositoryParser:
    """代码仓库解析器"""
    
    def __init__(self):
        self.identifier_pattern = re.compile(r'[^\w\-]')
        self.supported_extensions = {'.py'}  # 目前只支持Python
        self.elements = {}  # 所有代码元素
        self.file_elements = {}  # 按文件组织的元素
        self.module_structure = {}  # 模块结构
        
    def is_valid_identifier(self, name: str) -> bool:
        """检查是否为有效的标识符"""
        return self.identifier_pattern.search(name) is None
    
    def get_python_files(self, repo_path: str) -> List[str]:
        """获取仓库中所有Python文件"""
        python_files = []
        repo_path = Path(repo_path)
        
        for file_path in repo_path.rglob("*.py"):
            # 跳过隐藏文件和目录
            if any(part.startswith('.') for part in file_path.parts):
                continue
            # 跳过__pycache__目录
            if '__pycache__' in str(file_path):
                continue
            python_files.append(str(file_path))
        
        return python_files
    
    def parse_python_file(self, file_path: str) -> Dict[str, CodeElement]:
        """解析单个Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # 解析AST
            tree = ast.parse(source_code)
            visitor = PythonASTVisitor(file_path, source_code)
            visitor.visit(tree)
            
            # 将导入信息添加到模块元素中
            module_name = os.path.splitext(os.path.basename(file_path))[0]
            if module_name in visitor.elements:
                visitor.elements[module_name].imports = visitor.imports
            
            return visitor.elements
            
        except Exception as e:
            logger.error(f"Error parsing file {file_path}: {e}")
            return {}
    
    def build_module_structure(self, repo_path: str, elements: Dict[str, CodeElement]):
        """构建模块结构"""
        repo_path = Path(repo_path)
        
        for element_name, element in elements.items():
            if element.element_type == "Module":
                rel_path = Path(element.file_path).relative_to(repo_path)
                module_path = str(rel_path.with_suffix(''))
                module_name = module_path.replace(os.sep, '.')
                
                self.module_structure[module_name] = {
                    'file_path': element.file_path,
                    'elements': []
                }
        
        # 将元素分配到对应模块
        for element_name, element in elements.items():
            if element.element_type != "Module":
                rel_path = Path(element.file_path).relative_to(repo_path)
                module_path = str(rel_path.with_suffix(''))
                module_name = module_path.replace(os.sep, '.')
                
                if module_name in self.module_structure:
                    self.module_structure[module_name]['elements'].append(element_name)
    
    def parse_repository(self, repo_path: str) -> Dict[str, Any]:
        """解析整个代码仓库"""
        logger.info(f"开始解析代码仓库: {repo_path}")
        
        if not os.path.exists(repo_path):
            raise ValueError(f"Repository path does not exist: {repo_path}")
        
        # 获取所有Python文件
        python_files = self.get_python_files(repo_path)
        logger.info(f"找到 {len(python_files)} 个Python文件")
        
        # 解析每个文件
        all_elements = {}
        for file_path in python_files:
            file_elements = self.parse_python_file(file_path)
            all_elements.update(file_elements)
            self.file_elements[file_path] = file_elements
        
        self.elements = all_elements
        
        # 构建模块结构
        self.build_module_structure(repo_path, all_elements)
        
        logger.info(f"解析完成，共提取 {len(all_elements)} 个代码元素")
        
        return {
            'elements': {name: element.to_dict() for name, element in all_elements.items()},
            'module_structure': self.module_structure,
            'file_elements': {path: {name: element.to_dict() for name, element in elements.items()} 
                            for path, elements in self.file_elements.items()},
            'statistics': {
                'total_files': len(python_files),
                'total_elements': len(all_elements),
                'modules': len([e for e in all_elements.values() if e.element_type == "Module"]),
                'classes': len([e for e in all_elements.values() if e.element_type == "Class"]),
                'functions': len([e for e in all_elements.values() if e.element_type == "Function"]),
                'variables': len([e for e in all_elements.values() if e.element_type == "Variable"])
            }
        }
