"""
代码关系分析模块
用于分析代码元素之间的关系，构建代码知识图谱
"""

import os
import re
import ast
from typing import Dict, List, Set, Optional, Tuple, Any
from pathlib import Path
import logging
from collections import defaultdict

logger = logging.getLogger(__name__)


class RelationshipAnalyzer:
    """代码关系分析器"""
    
    def __init__(self, elements: Dict[str, Any], module_structure: Dict[str, Any]):
        self.elements = elements
        self.module_structure = module_structure
        self.relationships = defaultdict(list)  # element_name -> [relationships]
        self.call_graph = defaultdict(set)  # function -> set of called functions
        self.inheritance_graph = defaultdict(set)  # class -> set of parent classes
        self.import_graph = defaultdict(set)  # module -> set of imported modules
        
    def analyze_import_relationships(self):
        """分析导入关系"""
        logger.info("分析导入关系...")
        
        for element_name, element_data in self.elements.items():
            if element_data['type'] == 'Module' and element_data.get('imports'):
                for import_info in element_data['imports']:
                    if import_info['type'] == 'import':
                        # import module
                        imported_module = import_info['module']
                        self.import_graph[element_name].add(imported_module)
                        self.relationships[element_name].append({
                            'type': 'imports',
                            'target': imported_module,
                            'line': import_info['line']
                        })
                    
                    elif import_info['type'] == 'from_import':
                        # from module import name
                        imported_module = import_info['module']
                        imported_name = import_info['name']
                        
                        if imported_module:
                            self.import_graph[element_name].add(imported_module)
                            self.relationships[element_name].append({
                                'type': 'imports_from',
                                'target': imported_module,
                                'imported_name': imported_name,
                                'line': import_info['line']
                            })
    
    def analyze_inheritance_relationships(self):
        """分析继承关系"""
        logger.info("分析继承关系...")
        
        for element_name, element_data in self.elements.items():
            if element_data['type'] == 'Class' and element_data.get('relationships'):
                for rel in element_data['relationships']:
                    if rel['type'] == 'inherits':
                        parent_class = rel['target']
                        self.inheritance_graph[element_name].add(parent_class)
                        self.relationships[element_name].append({
                            'type': 'inherits_from',
                            'target': parent_class
                        })
    
    def analyze_function_calls(self):
        """分析函数调用关系（基于简单的文本分析）"""
        logger.info("分析函数调用关系...")
        
        # 收集所有函数名
        all_functions = set()
        for element_name, element_data in self.elements.items():
            if element_data['type'] == 'Function':
                # 提取简单函数名（去掉类前缀）
                simple_name = element_name.split('.')[-1]
                all_functions.add(simple_name)
        
        # 分析函数体中的调用
        for element_name, element_data in self.elements.items():
            if element_data['type'] == 'Function' and element_data.get('body'):
                body = element_data['body']
                
                # 简单的正则匹配函数调用
                for func_name in all_functions:
                    if func_name != element_name.split('.')[-1]:  # 不包括自己
                        pattern = rf'\b{re.escape(func_name)}\s*\('
                        if re.search(pattern, body):
                            self.call_graph[element_name].add(func_name)
                            self.relationships[element_name].append({
                                'type': 'calls',
                                'target': func_name
                            })
    
    def analyze_class_member_relationships(self):
        """分析类成员关系"""
        logger.info("分析类成员关系...")
        
        for element_name, element_data in self.elements.items():
            if element_data.get('metadata', {}).get('in_class'):
                class_name = element_data['metadata']['in_class']
                
                # 成员关系
                self.relationships[class_name].append({
                    'type': 'has_member',
                    'target': element_name,
                    'member_type': element_data['type']
                })
                
                # 反向关系
                self.relationships[element_name].append({
                    'type': 'member_of',
                    'target': class_name
                })
    
    def analyze_variable_usage(self):
        """分析变量使用关系"""
        logger.info("分析变量使用关系...")
        
        # 收集所有变量名
        all_variables = {}
        for element_name, element_data in self.elements.items():
            if element_data['type'] == 'Variable':
                simple_name = element_name.split('.')[-1]
                all_variables[simple_name] = element_name
        
        # 分析函数体中的变量使用
        for element_name, element_data in self.elements.items():
            if element_data['type'] == 'Function' and element_data.get('body'):
                body = element_data['body']
                
                for var_simple_name, var_full_name in all_variables.items():
                    if var_full_name != element_name:  # 不包括自己
                        pattern = rf'\b{re.escape(var_simple_name)}\b'
                        if re.search(pattern, body):
                            self.relationships[element_name].append({
                                'type': 'uses_variable',
                                'target': var_full_name
                            })
    
    def find_similar_functions(self, similarity_threshold: float = 0.3):
        """查找相似的函数（基于简单的文本相似度）"""
        logger.info("查找相似函数...")
        
        functions = {}
        for element_name, element_data in self.elements.items():
            if element_data['type'] == 'Function' and element_data.get('body'):
                functions[element_name] = element_data['body']
        
        # 简单的相似度计算（基于共同的单词）
        function_names = list(functions.keys())
        for i, func1 in enumerate(function_names):
            for func2 in function_names[i+1:]:
                body1 = functions[func1]
                body2 = functions[func2]
                
                # 提取单词
                words1 = set(re.findall(r'\b\w+\b', body1.lower()))
                words2 = set(re.findall(r'\b\w+\b', body2.lower()))
                
                # 计算Jaccard相似度
                intersection = len(words1 & words2)
                union = len(words1 | words2)
                
                if union > 0:
                    similarity = intersection / union
                    if similarity > similarity_threshold:
                        self.relationships[func1].append({
                            'type': 'similar_to',
                            'target': func2,
                            'similarity': similarity
                        })
                        self.relationships[func2].append({
                            'type': 'similar_to',
                            'target': func1,
                            'similarity': similarity
                        })
    
    def analyze_all_relationships(self):
        """分析所有关系"""
        logger.info("开始分析代码关系...")
        
        self.analyze_import_relationships()
        self.analyze_inheritance_relationships()
        self.analyze_function_calls()
        self.analyze_class_member_relationships()
        self.analyze_variable_usage()
        self.find_similar_functions()
        
        logger.info("关系分析完成")
        
        return dict(self.relationships)
    
    def get_relationship_statistics(self) -> Dict[str, int]:
        """获取关系统计信息"""
        stats = defaultdict(int)
        
        for element_name, relationships in self.relationships.items():
            for rel in relationships:
                stats[rel['type']] += 1
        
        return dict(stats)
    
    def build_knowledge_graph(self) -> Dict[str, Any]:
        """构建知识图谱"""
        return {
            'nodes': [
                {
                    'id': element_name,
                    'type': element_data['type'],
                    'file_path': element_data['file_path'],
                    'metadata': element_data.get('metadata', {})
                }
                for element_name, element_data in self.elements.items()
            ],
            'edges': [
                {
                    'source': element_name,
                    'target': rel['target'],
                    'type': rel['type'],
                    'metadata': {k: v for k, v in rel.items() if k not in ['type', 'target']}
                }
                for element_name, relationships in self.relationships.items()
                for rel in relationships
                if rel['target'] in self.elements  # 只包括存在的目标
            ],
            'statistics': self.get_relationship_statistics()
        }


class CodeContextExtractor:
    """代码上下文提取器，用于RAG系统"""
    
    def __init__(self, elements: Dict[str, Any], relationships: Dict[str, List]):
        self.elements = elements
        self.relationships = relationships
    
    def extract_element_context(self, element_name: str, max_depth: int = 2) -> Dict[str, Any]:
        """提取代码元素的上下文信息"""
        if element_name not in self.elements:
            return {}
        
        element = self.elements[element_name]
        context = {
            'element': element,
            'related_elements': {},
            'context_summary': ""
        }
        
        # 获取直接相关的元素
        visited = set()
        self._collect_related_elements(element_name, context['related_elements'], 
                                     visited, max_depth)
        
        # 生成上下文摘要
        context['context_summary'] = self._generate_context_summary(element, 
                                                                   context['related_elements'])
        
        return context
    
    def _collect_related_elements(self, element_name: str, related_elements: Dict, 
                                visited: Set[str], max_depth: int, current_depth: int = 0):
        """递归收集相关元素"""
        if current_depth >= max_depth or element_name in visited:
            return
        
        visited.add(element_name)
        
        if element_name in self.relationships:
            for rel in self.relationships[element_name]:
                target = rel['target']
                if target in self.elements and target not in visited:
                    related_elements[target] = {
                        'element': self.elements[target],
                        'relationship': rel['type'],
                        'depth': current_depth + 1
                    }
                    
                    # 递归收集
                    if current_depth + 1 < max_depth:
                        self._collect_related_elements(target, related_elements, 
                                                     visited, max_depth, current_depth + 1)
    
    def _generate_context_summary(self, element: Dict[str, Any], 
                                related_elements: Dict[str, Any]) -> str:
        """生成上下文摘要"""
        summary_parts = []
        
        # 元素基本信息
        summary_parts.append(f"{element['type']} '{element['name']}'")
        
        if element.get('docstring'):
            summary_parts.append(f"Documentation: {element['docstring'][:200]}...")
        
        # 相关元素摘要
        if related_elements:
            rel_types = defaultdict(list)
            for target, info in related_elements.items():
                rel_types[info['relationship']].append(target)
            
            for rel_type, targets in rel_types.items():
                if len(targets) <= 3:
                    summary_parts.append(f"{rel_type}: {', '.join(targets)}")
                else:
                    summary_parts.append(f"{rel_type}: {', '.join(targets[:3])} and {len(targets)-3} more")
        
        return "; ".join(summary_parts)
    
    def prepare_for_embedding(self, element_name: str) -> str:
        """为嵌入模型准备文本"""
        context = self.extract_element_context(element_name)
        
        if not context:
            return ""
        
        element = context['element']
        text_parts = []
        
        # 基本信息
        text_parts.append(f"Type: {element['type']}")
        text_parts.append(f"Name: {element['name']}")
        
        if element.get('docstring'):
            text_parts.append(f"Documentation: {element['docstring']}")
        
        if element.get('definition'):
            text_parts.append(f"Definition: {element['definition']}")
        
        # 上下文摘要
        if context['context_summary']:
            text_parts.append(f"Context: {context['context_summary']}")
        
        return "\n".join(text_parts)
