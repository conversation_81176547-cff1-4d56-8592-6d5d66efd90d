# CodeProcessor - 代码仓库处理模块

基于DraCo的处理流程，针对RAG系统进行优化的代码仓库分析工具。

## 功能特性

1. **代码结构解析**: 解析Python代码仓库，提取类、函数、变量等代码元素
2. **关系分析**: 分析代码元素之间的关系（继承、调用、导入等）
3. **知识图谱构建**: 构建代码知识图谱，便于理解代码结构
4. **RAG数据准备**: 为RAG系统准备结构化的代码数据
5. **上下文提取**: 提取代码元素的上下文信息，支持语义搜索

## 模块结构

```
CodeProcessor/
├── __init__.py                 # 模块初始化
├── code_parser.py             # 代码解析器
├── relationship_analyzer.py   # 关系分析器
├── repository_processor.py    # 仓库处理器主模块
├── rag_data_processor.py      # RAG数据处理器
├── process_emcgcn_example.py  # 示例脚本
└── README.md                  # 说明文档
```

## 使用方法

### 1. 基本使用

```python
from CodeProcessor import RepositoryProcessor

# 创建处理器
processor = RepositoryProcessor(output_dir="processed_repos")

# 处理代码仓库
result = processor.process_repository("/path/to/repo", "repo_name")

# 查看统计信息
print(result['repository_info']['statistics'])
```

### 2. 处理EMCGCN-ASTE示例

```bash
# 运行示例脚本
cd InnovativeIdentifier_v3/CodeProcessor
python process_emcgcn_example.py
```

### 3. 生成RAG数据

```python
from CodeProcessor.rag_data_processor import RAGDataProcessor

# 创建RAG数据处理器
rag_processor = RAGDataProcessor("processed_repos")

# 处理仓库数据用于RAG
rag_data = rag_processor.process_repository_for_rag("EMCGCN-ASTE")

# 生成嵌入文本
embedding_texts = rag_processor.generate_embedding_texts("EMCGCN-ASTE")
```

## 输出文件说明

处理完成后，会在输出目录生成以下文件：

- `full_analysis.json`: 完整的代码分析结果
- `rag_data.json`: 原始RAG数据
- `rag_chunks.json`: 优化后的RAG数据块
- `knowledge_graph.json`: 代码知识图谱
- `statistics.json`: 统计信息

## 数据结构

### 代码元素 (CodeElement)

```json
{
  "name": "element_name",
  "type": "Class|Function|Variable|Module",
  "file_path": "/path/to/file.py",
  "start_line": 10,
  "end_line": 20,
  "docstring": "documentation",
  "definition": "def function():",
  "body": "function implementation",
  "relationships": [...],
  "imports": [...],
  "metadata": {...}
}
```

### RAG数据块

```json
{
  "id": "unique_hash_id",
  "content": "formatted_content_for_embedding",
  "metadata": {
    "element_name": "name",
    "element_type": "type",
    "file_path": "path",
    "has_docstring": true,
    "relationship_count": 5
  },
  "chunk_type": "element|file|module"
}
```

## 分析结果示例

以EMCGCN-ASTE仓库为例：

### 统计信息
- 总文件数: 5
- 总代码元素数: 229
- 模块数: 5
- 类数: 9
- 函数数: 39
- 变量数: 176

### 关系统计
- 导入关系: 38
- 继承关系: 8
- 成员关系: 252
- 调用关系: 27
- 变量使用: 323
- 相似函数: 44

### RAG数据块
- 总数据块: 239
- 元素级块: 229
- 文件级块: 5
- 模块级块: 5

## 扩展功能

### 1. 搜索功能

```python
# 搜索代码元素
results = processor.search_elements("model", element_type="Class")
for result in results:
    print(f"{result['element_name']} - {result['match_type']}")
```

### 2. 上下文提取

```python
# 获取代码元素的上下文
context = processor.get_element_context("ClassName", max_depth=2)
print(context['context_summary'])
```

### 3. 生成报告

```python
# 生成分析报告
report = processor.generate_summary_report("repo_name")
print(report)
```

## 与DraCo的对比

| 特性 | DraCo | CodeProcessor |
|------|-------|---------------|
| 代码解析 | tree-sitter | Python AST |
| 关系分析 | 基础关系 | 扩展关系分析 |
| 输出格式 | 简单JSON | 结构化多格式 |
| RAG支持 | 无 | 专门优化 |
| 上下文提取 | 有限 | 深度上下文 |
| 知识图谱 | 无 | 完整图谱 |

## 注意事项

1. 目前只支持Python代码分析
2. 大型仓库可能需要较长处理时间
3. 内存使用量与代码仓库大小成正比
4. 建议对超大仓库进行分批处理

## 未来改进

1. 支持更多编程语言
2. 增加代码质量分析
3. 优化大型仓库处理性能
4. 增加可视化界面
5. 支持增量更新

## 依赖要求

- Python 3.7+
- ast (内置)
- json (内置)
- pathlib (内置)
- hashlib (内置)
- logging (内置)

## 许可证

本项目遵循MIT许可证。
