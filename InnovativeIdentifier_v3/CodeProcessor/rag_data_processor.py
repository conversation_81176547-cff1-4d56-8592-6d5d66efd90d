"""
RAG数据处理器
将代码分析结果转换为适合RAG系统使用的格式
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import hashlib

logger = logging.getLogger(__name__)


class RAGDataProcessor:
    """RAG数据处理器"""
    
    def __init__(self, processed_data_dir: str):
        self.processed_data_dir = Path(processed_data_dir)
        self.chunks = []  # 存储所有数据块
        self.metadata = {}  # 存储元数据
    
    def load_analysis_results(self, repo_name: str) -> Dict[str, Any]:
        """加载代码分析结果"""
        repo_dir = self.processed_data_dir / repo_name
        
        if not repo_dir.exists():
            raise ValueError(f"Repository analysis not found: {repo_dir}")
        
        # 加载完整分析结果
        full_analysis_path = repo_dir / "full_analysis.json"
        with open(full_analysis_path, 'r', encoding='utf-8') as f:
            analysis_data = json.load(f)
        
        return analysis_data
    
    def create_code_chunks(self, analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建代码块用于RAG"""
        chunks = []
        elements = analysis_data['elements']
        relationships = analysis_data.get('relationships', {})
        
        for element_name, element_data in elements.items():
            chunk = self._create_element_chunk(element_name, element_data, relationships)
            if chunk:
                chunks.append(chunk)
        
        # 创建文件级别的块
        file_chunks = self._create_file_level_chunks(analysis_data)
        chunks.extend(file_chunks)
        
        # 创建模块级别的块
        module_chunks = self._create_module_level_chunks(analysis_data)
        chunks.extend(module_chunks)
        
        return chunks
    
    def _create_element_chunk(self, element_name: str, element_data: Dict[str, Any], 
                            relationships: Dict[str, List]) -> Optional[Dict[str, Any]]:
        """为单个代码元素创建数据块"""
        
        # 生成唯一ID
        chunk_id = hashlib.md5(f"{element_name}_{element_data['type']}".encode()).hexdigest()
        
        # 构建内容
        content_parts = []
        
        # 基本信息
        content_parts.append(f"Element: {element_name}")
        content_parts.append(f"Type: {element_data['type']}")
        content_parts.append(f"File: {os.path.basename(element_data['file_path'])}")
        
        if element_data.get('start_line'):
            content_parts.append(f"Line: {element_data['start_line']}")
        
        # 定义
        if element_data.get('definition'):
            content_parts.append(f"Definition:\n{element_data['definition']}")
        
        # 文档字符串
        if element_data.get('docstring'):
            content_parts.append(f"Documentation:\n{element_data['docstring']}")
        
        # 实现（对于较短的函数）
        if (element_data.get('body') and 
            element_data['type'] == 'Function' and 
            len(element_data['body']) < 2000):
            content_parts.append(f"Implementation:\n{element_data['body']}")
        
        # 关系信息
        element_relationships = relationships.get(element_name, [])
        if element_relationships:
            rel_info = []
            for rel in element_relationships[:5]:  # 限制关系数量
                rel_info.append(f"{rel['type']}: {rel['target']}")
            content_parts.append(f"Relationships:\n" + "\n".join(rel_info))
        
        # 元数据
        metadata = {
            'element_name': element_name,
            'element_type': element_data['type'],
            'file_path': element_data['file_path'],
            'start_line': element_data.get('start_line'),
            'end_line': element_data.get('end_line'),
            'has_docstring': bool(element_data.get('docstring')),
            'has_implementation': bool(element_data.get('body')),
            'relationship_count': len(element_relationships)
        }
        
        # 添加特定类型的元数据
        if element_data['type'] == 'Function':
            metadata.update({
                'is_constructor': element_data.get('metadata', {}).get('is_constructor', False),
                'in_class': element_data.get('metadata', {}).get('in_class'),
                'arguments': element_data.get('metadata', {}).get('arguments', [])
            })
        elif element_data['type'] == 'Class':
            # 统计类成员
            member_count = len([rel for rel in element_relationships if rel['type'] == 'has_member'])
            metadata['member_count'] = member_count
        
        return {
            'id': chunk_id,
            'content': '\n\n'.join(content_parts),
            'metadata': metadata,
            'chunk_type': 'element'
        }
    
    def _create_file_level_chunks(self, analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建文件级别的数据块"""
        chunks = []
        file_elements = analysis_data.get('file_elements', {})
        
        for file_path, elements in file_elements.items():
            if not elements:
                continue
            
            chunk_id = hashlib.md5(f"file_{file_path}".encode()).hexdigest()
            
            content_parts = []
            content_parts.append(f"File: {os.path.basename(file_path)}")
            content_parts.append(f"Full Path: {file_path}")
            
            # 统计信息
            element_counts = {}
            for element_data in elements.values():
                element_type = element_data['type']
                element_counts[element_type] = element_counts.get(element_type, 0) + 1
            
            content_parts.append("Elements in this file:")
            for element_type, count in element_counts.items():
                content_parts.append(f"- {element_type}: {count}")
            
            # 主要元素列表
            classes = [name for name, data in elements.items() if data['type'] == 'Class']
            functions = [name for name, data in elements.items() if data['type'] == 'Function']
            
            if classes:
                content_parts.append(f"Classes: {', '.join(classes)}")
            
            if functions:
                content_parts.append(f"Functions: {', '.join(functions[:10])}")  # 限制数量
                if len(functions) > 10:
                    content_parts.append(f"... and {len(functions) - 10} more functions")
            
            # 文件级别的文档字符串（如果有模块文档）
            module_name = os.path.splitext(os.path.basename(file_path))[0]
            if module_name in elements and elements[module_name].get('docstring'):
                content_parts.append(f"Module Documentation:\n{elements[module_name]['docstring']}")
            
            chunks.append({
                'id': chunk_id,
                'content': '\n\n'.join(content_parts),
                'metadata': {
                    'file_path': file_path,
                    'element_counts': element_counts,
                    'total_elements': len(elements),
                    'classes': classes,
                    'functions': functions
                },
                'chunk_type': 'file'
            })
        
        return chunks
    
    def _create_module_level_chunks(self, analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建模块级别的数据块"""
        chunks = []
        module_structure = analysis_data.get('module_structure', {})
        
        for module_name, module_info in module_structure.items():
            chunk_id = hashlib.md5(f"module_{module_name}".encode()).hexdigest()
            
            content_parts = []
            content_parts.append(f"Module: {module_name}")
            content_parts.append(f"File: {module_info['file_path']}")
            
            # 模块中的元素
            elements = module_info.get('elements', [])
            if elements:
                content_parts.append(f"Contains {len(elements)} elements:")
                content_parts.append(', '.join(elements[:20]))  # 限制显示数量
                if len(elements) > 20:
                    content_parts.append(f"... and {len(elements) - 20} more")
            
            chunks.append({
                'id': chunk_id,
                'content': '\n\n'.join(content_parts),
                'metadata': {
                    'module_name': module_name,
                    'file_path': module_info['file_path'],
                    'element_count': len(elements),
                    'elements': elements
                },
                'chunk_type': 'module'
            })
        
        return chunks
    
    def process_repository_for_rag(self, repo_name: str) -> Dict[str, Any]:
        """处理仓库数据用于RAG系统"""
        logger.info(f"开始处理 {repo_name} 的RAG数据")
        
        # 加载分析结果
        analysis_data = self.load_analysis_results(repo_name)
        
        # 创建数据块
        chunks = self.create_code_chunks(analysis_data)
        
        # 准备最终数据
        rag_data = {
            'repository_name': repo_name,
            'total_chunks': len(chunks),
            'chunk_types': {
                'element': len([c for c in chunks if c['chunk_type'] == 'element']),
                'file': len([c for c in chunks if c['chunk_type'] == 'file']),
                'module': len([c for c in chunks if c['chunk_type'] == 'module'])
            },
            'chunks': chunks,
            'repository_metadata': analysis_data['repository_info']
        }
        
        # 保存RAG数据
        output_path = self.processed_data_dir / repo_name / "rag_chunks.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(rag_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"RAG数据处理完成，共生成 {len(chunks)} 个数据块")
        logger.info(f"数据已保存到: {output_path}")
        
        return rag_data
    
    def generate_embedding_texts(self, repo_name: str) -> List[str]:
        """生成用于嵌入的文本列表"""
        rag_data_path = self.processed_data_dir / repo_name / "rag_chunks.json"
        
        if not rag_data_path.exists():
            raise ValueError(f"RAG data not found. Please run process_repository_for_rag first.")
        
        with open(rag_data_path, 'r', encoding='utf-8') as f:
            rag_data = json.load(f)
        
        embedding_texts = []
        for chunk in rag_data['chunks']:
            embedding_texts.append(chunk['content'])
        
        return embedding_texts


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="RAG数据处理器")
    parser.add_argument("--processed-dir", default="../ProcressedData/code_analysis", 
                       help="处理结果目录")
    parser.add_argument("--repo-name", default="EMCGCN-ASTE", help="仓库名称")
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 处理RAG数据
    processor = RAGDataProcessor(args.processed_dir)
    rag_data = processor.process_repository_for_rag(args.repo_name)
    
    # 显示统计信息
    print(f"\n=== RAG数据统计 ===")
    print(f"仓库: {rag_data['repository_name']}")
    print(f"总数据块数: {rag_data['total_chunks']}")
    print(f"数据块类型分布:")
    for chunk_type, count in rag_data['chunk_types'].items():
        print(f"  {chunk_type}: {count}")
    
    # 显示示例数据块
    if rag_data['chunks']:
        print(f"\n=== 示例数据块 ===")
        sample_chunk = rag_data['chunks'][0]
        print(f"ID: {sample_chunk['id']}")
        print(f"类型: {sample_chunk['chunk_type']}")
        print(f"内容预览: {sample_chunk['content'][:300]}...")


if __name__ == "__main__":
    main()
