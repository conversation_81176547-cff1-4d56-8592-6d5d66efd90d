"""
处理EMCGCN-ASTE仓库的示例脚本
演示如何使用CodeProcessor模块分析代码仓库
"""

import os
import sys
import json
import logging
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from CodeProcessor import RepositoryProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    # 设置路径
    base_dir = Path(__file__).parent.parent
    repo_path = base_dir / "data" / "repo" / "EMCGCN-ASTE"
    output_dir = base_dir / "ProcressedData" / "code_analysis"
    
    logger.info(f"开始处理EMCGCN-ASTE仓库")
    logger.info(f"仓库路径: {repo_path}")
    logger.info(f"输出目录: {output_dir}")
    
    # 检查仓库路径是否存在
    if not repo_path.exists():
        logger.error(f"仓库路径不存在: {repo_path}")
        return
    
    # 创建处理器
    processor = RepositoryProcessor(str(output_dir))
    
    try:
        # 处理仓库
        result = processor.process_repository(str(repo_path), "EMCGCN-ASTE")
        
        # 显示统计信息
        stats = result['repository_info']['statistics']
        logger.info("=== 处理结果统计 ===")
        logger.info(f"总文件数: {stats['total_files']}")
        logger.info(f"总代码元素数: {stats['total_elements']}")
        logger.info(f"模块数: {stats['modules']}")
        logger.info(f"类数: {stats['classes']}")
        logger.info(f"函数数: {stats['functions']}")
        logger.info(f"变量数: {stats['variables']}")
        
        # 显示关系统计
        rel_stats = result['knowledge_graph']['statistics']
        logger.info("=== 关系统计 ===")
        for rel_type, count in rel_stats.items():
            logger.info(f"{rel_type}: {count}")
        
        # 生成并显示摘要报告
        report = processor.generate_summary_report("EMCGCN-ASTE")
        print("\n" + "="*50)
        print(report)
        print("="*50)
        
        # 展示一些具体的代码元素
        logger.info("\n=== 主要代码元素示例 ===")
        
        # 显示主要类
        classes = [name for name, element in result['elements'].items() 
                  if element['type'] == 'Class']
        if classes:
            logger.info(f"发现的类: {', '.join(classes[:5])}")
            
            # 显示第一个类的详细信息
            first_class = classes[0]
            class_info = result['elements'][first_class]
            logger.info(f"\n类 '{first_class}' 的详细信息:")
            logger.info(f"  文件: {class_info['file_path']}")
            logger.info(f"  起始行: {class_info.get('start_line', 'N/A')}")
            if class_info.get('docstring'):
                logger.info(f"  文档: {class_info['docstring'][:100]}...")
        
        # 显示主要函数
        functions = [name for name, element in result['elements'].items() 
                    if element['type'] == 'Function']
        if functions:
            logger.info(f"\n发现的函数: {', '.join(functions[:5])}")
            
            # 显示第一个函数的详细信息
            first_function = functions[0]
            func_info = result['elements'][first_function]
            logger.info(f"\n函数 '{first_function}' 的详细信息:")
            logger.info(f"  文件: {func_info['file_path']}")
            logger.info(f"  起始行: {func_info.get('start_line', 'N/A')}")
            if func_info.get('docstring'):
                logger.info(f"  文档: {func_info['docstring'][:100]}...")
        
        # 展示RAG数据示例
        logger.info("\n=== RAG数据示例 ===")
        rag_data = result['rag_data']
        if rag_data:
            sample_entry = rag_data[0]
            logger.info(f"RAG条目示例:")
            logger.info(f"  ID: {sample_entry['id']}")
            logger.info(f"  类型: {sample_entry['type']}")
            logger.info(f"  嵌入文本长度: {len(sample_entry['embedding_text'])}")
            logger.info(f"  嵌入文本预览: {sample_entry['embedding_text'][:200]}...")
        
        # 搜索示例
        logger.info("\n=== 搜索功能示例 ===")
        try:
            search_results = processor.search_elements("model", "Class")
            if search_results:
                logger.info(f"搜索 'model' 类型为 'Class' 的结果:")
                for result in search_results[:3]:
                    logger.info(f"  - {result['element_name']} ({result['match_type']})")
            else:
                logger.info("未找到匹配的结果")
        except Exception as e:
            logger.error(f"搜索功能出错: {e}")

        # 上下文提取示例
        if classes:
            logger.info(f"\n=== 上下文提取示例 ===")
            try:
                context = processor.get_element_context(classes[0])
                logger.info(f"类 '{classes[0]}' 的上下文:")
                logger.info(f"  上下文摘要: {context.get('context_summary', 'N/A')}")
                logger.info(f"  相关元素数量: {len(context.get('related_elements', {}))}")
            except Exception as e:
                logger.error(f"上下文提取出错: {e}")
        
        logger.info(f"\n处理完成！结果已保存到: {output_dir / 'EMCGCN-ASTE'}")
        
    except Exception as e:
        logger.error(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def analyze_specific_file():
    """分析特定文件的示例"""
    base_dir = Path(__file__).parent.parent
    repo_path = base_dir / "data" / "repo" / "EMCGCN-ASTE"
    
    # 创建处理器并处理仓库
    processor = RepositoryProcessor()
    result = processor.process_repository(str(repo_path), "EMCGCN-ASTE")
    
    # 分析model.py文件中的元素
    model_file = str(repo_path / "code" / "model.py")
    logger.info(f"\n=== 分析 model.py 文件 ===")
    
    file_elements = result['file_elements'].get(model_file, {})
    if file_elements:
        logger.info(f"model.py 中的代码元素:")
        for element_name, element_data in file_elements.items():
            logger.info(f"  - {element_name} ({element_data['type']})")
            if element_data.get('docstring'):
                logger.info(f"    文档: {element_data['docstring'][:100]}...")
    
    # 分析类之间的关系
    logger.info(f"\n=== 类继承关系 ===")
    relationships = result['relationships']
    for element_name, rels in relationships.items():
        if any(rel['type'] == 'inherits_from' for rel in rels):
            inherit_rels = [rel for rel in rels if rel['type'] == 'inherits_from']
            for rel in inherit_rels:
                logger.info(f"  {element_name} 继承自 {rel['target']}")


if __name__ == "__main__":
    main()
    
    # 可选：运行特定文件分析
    # analyze_specific_file()
