from sentence_transformers import SentenceTransformer
import os

SAVE_DIR = "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/models/Qwen3_Embedding"  

# 模型名称映射
models = {
    # "0.6B": "Qwen/Qwen3-Embedding-0.6B",
    # "4B":   "Qwen/Qwen3-Embedding-4B",
    "8B":   "Qwen/Qwen3-Embedding-8B"
}

def download_and_save(name, save_dir):
    model_id = models[name]
    local_path = os.path.join(save_dir, f"Qwen3-Embedding-{name}")
    print(f"📥 正在下载 Qwen3-Embedding-{name} 到 {local_path}")
    
    # 加载模型（第一次会自动下载到 HF 缓存）
    model = SentenceTransformer(model_id)
    
    # 保存到指定目录
    os.makedirs(local_path, exist_ok=True)
    model.save(local_path)
    
    print(f"✅ Qwen3-Embedding-{name} 已保存到 {local_path} (维度: {model.get_sentence_embedding_dimension()})")

if __name__ == "__main__":
    os.makedirs(SAVE_DIR, exist_ok=True)
    for name in models:
        download_and_save(name, SAVE_DIR)
