from sentence_transformers import SentenceTransformer

# 本地路径（改成你保存的目录）
MODEL_DIR = r"/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/models/Qwen3_Embedding"

# 要加载的模型（可以切换 0.6B / 4B / 8B）
model_name = "Qwen3-Embedding-4B"
model_path = f"{MODEL_DIR}/{model_name}"

print(f"📂 正在加载本地模型: {model_path}")
model = SentenceTransformer(model_path, device="cuda:3")

# 输入文本
texts = [
    "这是一个测试句子。",
    "Qwen3 Embedding 支持多语言文本向量生成。"
]

# 生成向量
embeddings = model.encode(texts)

# 输出结果
for text, emb in zip(texts, embeddings):
    print(f"\n📌 文本: {text}")
    print(f"向量维度: {len(emb)}")
    print(f"向量前5个值: {emb[:5]}")
