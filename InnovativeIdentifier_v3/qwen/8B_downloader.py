#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
方案A：整库快照下载（最稳妥）
- 将 Hugging Face 仓库完整同步到本地目录
- 支持断点续传 / 并发 / 固定revision / 可选token
- 适合离线环境打包迁移

用法示例：
  python download_qwen3_embedding_8b.py --local-dir /models/Qwen3-Embedding-8B
  python download_qwen3_embedding_8b.py --local-dir /models/Qwen3-Embedding-8B --revision main
  python download_qwen3_embedding_8b.py --local-dir /models/Qwen3-Embedding-8B --token hf_xxx --workers 8
"""

import argparse
import os
import sys
from pathlib import Path
from typing import Optional

def _human_size(num: int) -> str:
    for unit in ["B","KB","MB","GB","TB"]:
        if num < 1024.0:
            return f"{num:.2f}{unit}"
        num /= 1024.0
    return f"{num:.2f}PB"

def _dir_size(path: Path) -> int:
    total = 0
    for p in path.rglob("*"):
        try:
            if p.is_file():
                total += p.stat().st_size
        except Exception:
            pass
    return total

def main(
    repo_id: str,
    local_dir: str,
    revision: Optional[str],
    workers: int,
    symlinks: bool,
    resume: bool,
    token: Optional[str],
):
    try:
        from huggingface_hub import snapshot_download, login
    except Exception as e:
        print("❌ 需要先安装依赖： pip install -U huggingface_hub", file=sys.stderr)
        raise

    # 优先使用显式 token，其次环境变量 HUGGINGFACE_HUB_TOKEN
    token = token or os.environ.get("HUGGINGFACE_HUB_TOKEN")

    # 如果你想在此处调用交互式登录，可取消注释：
    # if token is None:
    #     try:
    #         login()  # huggingface-cli login 的Python封装
    #     except Exception:
    #         pass

    print("🚀 开始下载（方案A：整库快照）")
    print(f"   repo_id     : {repo_id}")
    print(f"   local_dir   : {local_dir}")
    print(f"   revision    : {revision or 'default (branch head)'}")
    print(f"   workers     : {workers}")
    print(f"   symlinks    : {symlinks}")
    print(f"   resume      : {resume}")
    print(f"   token?      : {'yes' if token else 'no'}")
    print()

    # 可选：启用更快传输（若已安装 hf_transfer）
    if os.environ.get("HF_HUB_ENABLE_HF_TRANSFER") != "1":
        try:
            import hf_transfer  # noqa: F401
            os.environ["HF_HUB_ENABLE_HF_TRANSFER"] = "1"
            print("⚡ 检测到 hf_transfer，已启用高速传输 (HF_HUB_ENABLE_HF_TRANSFER=1)")
        except Exception:
            pass

    local_dir_path = Path(local_dir)
    local_dir_path.mkdir(parents=True, exist_ok=True)

    cached_dir = snapshot_download(
        repo_id=repo_id,
        revision=revision,                  # e.g. "main" 或具体commit/tag
        local_dir=str(local_dir_path),
        local_dir_use_symlinks=symlinks,    # 便于迁移建议 False（复制真实文件）
        resume_download=resume,             # 断点续传
        max_workers=workers,                # 并发线程
        token=token,                        # 可为 None
    )

    print("\n✅ 下载完成")
    print(f"   本地路径：{cached_dir}")
    size = _dir_size(Path(cached_dir))
    print(f"   目录大小：{_human_size(size)}")

    # 简单列个关键文件，方便核对
    print("\n📄 关键文件（存在即代表完整度较高）：")
    must_files = [
        "config.json",
        "model.safetensors.index.json",
        "tokenizer.json",
        "tokenizer_config.json",
        "vocab.json",           # 如果是BPE/WordPiece
        "merges.txt",           # 如果是BPE
    ]
    found_any = False
    for mf in must_files:
        p = Path(cached_dir) / mf
        if p.exists():
            print(f"   - {mf}")
            found_any = True
    if not found_any:
        print("   （提示：不同模型的文件名可能略有差异，上述列表仅供参考。）")

    print("\n📦 现在你可以把该目录打包拷贝到离线机器使用。")
    print("   加载示例（离线）：")
    print(f"   from sentence_transformers import SentenceTransformer")
    print(f"   model = SentenceTransformer(r\"{cached_dir}\")")
    print("   # 或 transformers：AutoTokenizer/AutoModel.from_pretrained(path, local_files_only=True)")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="snapshot_download Qwen/Qwen3-Embedding-8B (方案A：整库快照)")
    parser.add_argument("--repo-id", default="Qwen/Qwen3-Embedding-8B", help="HF 仓库ID")
    parser.add_argument("--local-dir", required=True, help="下载到的本地目录")
    parser.add_argument("--revision", default=None, help="指定分支/标签/commit（默认仓库当前头）")
    parser.add_argument("--workers", type=int, default=8, help="并发下载线程数")
    parser.add_argument("--symlinks", action="store_true", help="使用符号链接缓存（迁移不便，默认关闭）")
    parser.add_argument("--no-resume", action="store_true", help="关闭断点续传（默认开启）")
    parser.add_argument("--token", default=None, help="访问令牌（可改用环境变量 HUGGINGFACE_HUB_TOKEN）")
    args = parser.parse_args()

    main(
        repo_id=args.repo_id,
        local_dir=args.local_dir,
        revision=args.revision,
        workers=args.workers,
        symlinks=args.symlinks,
        resume=not args.no_resume,
        token=args.token,
    )
