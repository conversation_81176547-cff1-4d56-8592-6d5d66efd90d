# openai tiktoken
tiktoken_cache_dir: 'Your cache directory for OpenAI tiktoken'

max_to_generate: 48

# codegen
codegen350m_repo: "Salesforce/codegen-350M-mono"
codegen2b_repo: "Salesforce/codegen-2B-mono"
codegen6b_repo: "Salesforce/codegen-6B-mono"
codegen16b_repo: "Salesforce/codegen-16B-mono"
codegen_max_token: 2048

# codegen25
codegen25_repo: "Salesforce/codegen25-7b-mono_P"
codegen25_max_token: 2048

# santacoder
santacoder_repo: "bigcode/santacoder"
santacoder_max_token: 2048

# starcoder
starcoder_repo: "bigcode/starcoder"
starcoder_max_token: 8192

# codellama-7b
codellama7b_repo: "meta-llama/CodeLlama-7b-Python-hf"
codellama_max_token: 8192

# GPT-3.5
gpt35_api: "gpt-3.5-turbo-0613"
gpt35_max_token: 4096

# GPT4
gpt4_api: "gpt-4-0613"
gpt4_max_token: 8192